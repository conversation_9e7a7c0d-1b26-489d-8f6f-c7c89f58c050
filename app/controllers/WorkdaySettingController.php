<?php

namespace App\Controllers;

//轮休配置 每周休息天数
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\BllService;
use App\Services\WorkdaySettingService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class WorkdaySettingController extends BaseController
{


    public function initialize()
    {
        parent::initialize();
    }

    /**
     * @api workday-setting/list
     * @Permission(action='free_workday_view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $param = $this->request->get();
        Validation::validate($param, [
            'store_id'      => 'Arr',//网点
            'state'         => 'Arr',//在职状态
            'audit_state'   => 'Arr',//审批状态
            'staff_info_id' => 'Str',//用户
        ]);
        $server        = new WorkdaySettingService();
        $server->param = $param;
        $data          = $server->getCount()->getList();
        $count         = $server->count;
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['list' => $data, 'count' => $count]);
    }


    /**
     * 导入
     * @Permission(action='free_workday_import')
     */
    public function importAction()
    {
        $params              = $this->request->get();
        $params['file_type'] = AsyncImportTaskModel::WORKDAY_SETTING;
        Validation::validate($params, [
            'file_url' => 'Required|Str',
        ]);
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->import($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    //导入模板

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function templateAction()
    {
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->getTmp($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }


    /**
     * @Permission(action='free_workday_import')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deletePartAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'ids' => 'Required|Arr',
        ]);
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $data                = $server->deleteData($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    //删除 所有

    /**
     * @Permission(action='free_workday_import')
     * @return Response|ResponseInterface
     */
    public function deleteAllAction()
    {
        $server                  = new WorkdaySettingService();
        $params['user_info']     = $this->user;
        $params['is_delete_all'] = true;
        $data                    = $server->deleteData($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);
    }

    //导出

    /**
     * @Permission(action='free_workday_export')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'store_id'      => 'Arr',//网点
            'state'         => 'Arr',//在职状态
            'audit_state'   => 'Arr',//审批状态
            'staff_info_id' => 'Str',//用户
        ]);
        //获取文件名
        $file_name             = 'free_workday_setting';
        $action_name           = 'workday' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'workdaySettingExport';
        $params['lang']        = $this->locale;
        $params['current_uid'] = $this->user['id'];
        [$msg, $code] = (new BllService())->exportToDownloadCenter($this->user['id'], $file_name, $action_name, $params);
        if ($code == ErrCode::SUCCESS) {
            return $this->returnJson(ErrCode::SUCCESS, $msg, []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'export failed', []);
    }

    /**
     * 删除全部 获取所有记录条目数
     * @Permission(action='free_workday_delete')
     */
    public function deleteInfoAction(){
        $server              = new WorkdaySettingService();
        $params['user_info'] = $this->user;
        $num                 = $server->deleteNum();
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), ['count' => $num]);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function remindAction()
    {
        $params     = $this->request->get();
        $validation = [
           // 'month' => 'Required|Str|>>>:month error',
        ];
        Validation::validate($params, $validation);
        $server               = new WorkdaySettingService();
        $params['operate_id'] = $this->user['id'];
        $result               = $server->getPageRemindData($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }


}