<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/8/4
 * Time: 2:43 PM
 */

namespace App\Controllers;

use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Services\AsyncImportTaskService;
use App\Services\BllService;
use App\Services\DefaultRestDayService;
use App\Models\backyard\HrStaffInfoModel;
use App\Services\ExcelService;
use App\Services\HrJobTitleService;
use App\Services\HrShiftService;
use App\Services\SettingEnvService;
use App\Services\StaffPublicHolidayService;
use App\Services\SysService;
use App\Services\SysStoreService;
use App\Services\WorkDayImportService;
use App\Services\WorkdayService;
use App\Library\ErrCode;
use App\Models\backyard\SysStoreModel;
use App\Services\StaffService;
use App\Services\SysDepartmentService;
use App\Library\ApiClient;
use App\Services\WorkShiftService;

class WorkdayController extends BaseController{


    public $paramIn;
    public $userinfo;
    public $staff_service;
    public function initialize()
    {
        parent::initialize();

        $this->paramIn = $this->request->get();
        $this->paramIn = filter_param($this->paramIn);
        $this->staff_service = new StaffService();
        $this->userinfo = $this->staff_service->get_fbi_user_info($this->user['id']);

        //判断权限写死 参数
        $this->paramIn['sub'] = 1;//默认取直属下级
        $user_position = $this->userinfo['position_category'] = empty($this->userinfo['position_category']) ? array() : explode(',',$this->userinfo['position_category']);
        if(!empty($this->userinfo['position_category'])){
            //超管或人事  新增 系统管理员和人事专员角色
            if($this->get_permission($user_position)){
                $this->paramIn['is_sub'] = 0;
            }else if(in_array(21,$user_position)){//区域经理
                $this->paramIn['is_manager'] = 'manager';
            }else if(in_array(3,$user_position) || in_array(18,$user_position)){//网点
                $this->paramIn['pre_store_id'] = $this->userinfo['organization_id'];
            }
        }

    }

    /**
     * @Token
     * 轮休页 tab
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function tab_configAction()
    {
        $return = [
            ['label'=>$this->t->_('working_day_rest_type_61'),'value'=>'6_1'],
            ['label'=>$this->t->_('working_day_rest_type_51'),'value'=>'5_1'],
            ['label'=>$this->t->_('working_day_rest_type_62'),'value'=>'6_2'],
            ['label'=>$this->t->_('working_day_rest_type_52'),'value'=>'5_2'],
            ['label'=>$this->t->_('working_day_rest_type_91'),'value'=>'9_1'],
        ];
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $return);
    }



    /**
     * @Token
     * 单个人 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_workdaysAction(){
        $params['staff_info_id'] =  $this->paramIn['staff_info_id'];
        $params['date'] =  $this->paramIn['date'];
        $validation = [
            'staff_info_id'=> 'Required|int|>>>:staff_info_id error',
            'date' => 'Required|Date|>>>:date error',
        ];
        Validation::validate($params, $validation);

        try {
            $workdayService = new WorkdayService();
            $suggestConfirm = $this->paramIn['is_submit'] ?? 0;//轮休配置验证建议班次 二次确认参数
            $workdayService->setSuggestConfirm($suggestConfirm);
            $workdayService->handle([$params['staff_info_id']], [$params['date']], $this->user['id']);
            return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
        } catch (BusinessException $b) {
            //排班建议 二次确认弹窗
            if ($b->getCode() == 10086) {
                return $this->returnJson(1, '', ['code' => -1,'message' => $b->getMessage(),'data' => null]);//小于6天
            }
            throw $b;
        }
    }


    /**
     * @Token
     * 批量 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_batch_workdaysAction(){
        $params['staff_info_id'] =  $this->paramIn['staff_info_id'];
        $params['date'] =  $this->paramIn['date'];
        $validation = [
            'staff_info_id'=> 'Required|Arr|>>>:staff_info_id error',
            'date' => 'Required|Arr|>>>:date error',
        ];
        Validation::validate($params, $validation);

        try{
            $workdayService = new WorkdayService();
            $suggestConfirm = $this->paramIn['is_submit'] ?? 0;//轮休配置验证建议班次 二次确认参数
            $workdayService->setSuggestConfirm($suggestConfirm);
            $workdayService->setSrc(2);
            $workdayService->handle($params['staff_info_id'],$params['date'],$this->user['id']);
            return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
        }catch (BusinessException $b) {
            //排班建议 二次确认弹窗
            if ($b->getCode() == 10086) {
                return $this->returnJson(1, '', ['code' => -1,'message' => $b->getMessage(),'data' => null]);//小于6天
            }
            throw $b;
        }
    }


    /**
     * @Token
     * @param $user_position
     * @return bool
     */
    protected function get_permission($user_position)
    {
        //超管 运营经理 人事经理 人事专员  系统管理员  有全部权限
        if(in_array(17,$user_position)
            || in_array(99,$user_position)
//            || in_array(8,$user_position)//运营经理 去掉全部权限 根据所在部门 获取部门以及子部门的员工
            || in_array(14,$user_position)
            || in_array(16,$user_position)
            || in_array(41,$user_position)
        )
            return true;
        return false;
    }


    /**
     * @Token
     */
    public function menu_listAction()
    {
        $bll           = new WorkdayService();
        $return        = $bll::$permission;
        $user_position = $this->userinfo['position_category'];
        $manager_store = (new SysStoreService())->searchStaffManageStoreList($this->user);
        if (count($manager_store) <= 1) {
            $return[0]['value'] = $return[1]['value'] = false;
        }
        //总部的人 才显示部门筛选 网点的 不限时 修改为 只有部门负责人和超管才显示
        if (!$this->is_manager($this->userinfo['id']) && !$this->get_permission($user_position)) {
            $return[4]['value'] = false;
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $return);
    }

    /**
     * @Token
     * @Permission(action='work_day_view')
     */
    public function staff_workdays_listAction()
    {
        $param['store_id']      = empty($this->paramIn['store_id']) ? [] : $this->paramIn['store_id'];
        $param['state']         = empty($this->paramIn['state']) ? [] : $this->paramIn['state'];
        $param['area']          = empty($this->paramIn['area']) ? '' : $this->paramIn['area'];
        $param['job_title']     = empty($this->paramIn['job_title']) ? '' : $this->paramIn['job_title'];
        $param['staff_info_id'] = empty($this->paramIn['staff_info_id']) ? '' : $this->paramIn['staff_info_id'];
        //部门页面搜索
        $param['search_department'] = empty($this->paramIn['search_department']) ? '' : $this->paramIn['search_department'];
        //分页
        $param['start']  = empty($this->paramIn['start']) ? 0 : $this->paramIn['start'];
        $param['length'] = empty($this->paramIn['length']) ? 30 : $this->paramIn['length'];

        $work_type                 = empty($this->paramIn['work_type']) ? '0_0' : $this->paramIn['work_type'];
        $param['week_working_day'] = explode('_', $work_type)[0];
        $param['rest_type']        = explode('_', $work_type)[1];
        $param['hire_type']        = $this->paramIn['hire_type']??[];

        //员工信息
        $staff_bll         = new WorkdayService();
        $staff_id          = $this->userinfo['id'];
        $param['operator'] = $staff_id;

        //获符合筛选条件和登录用户可视范围数据权限的员工
        $result = $staff_bll->search_staff_workdays($param, $this->userinfo['id']);

        if (!empty($result['data'])) {
            $param['month'] = $this->paramIn['month'] ?? '';
            $result['data'] = $staff_bll->formatList($param, $result['data']);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * @Token
     */
    public function area_listAction()
    {
        $parcel_model = SysStoreModel::$sorting_no_map;
        $return = array();
        foreach ($parcel_model as $k => $v) {
            $row['code'] = $k;
            $row['value'] = $v;
            $return[] = $row;
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $return);
    }

    /**
     * @Token
     */
    public function store_listAction(){
        $params = $this->request->get();
        $condition = $bind = [];
        if(isset($params['search_name'])){
            $condition = ['(name LIKE :name: or id like :id:) and state =1'];
            $bind = [
                'name'=>'%'.$params['search_name'].'%',
                'id'=>'%'.$params['search_name'].'%',
            ];
        }
        $data = (new SysStoreService())->searchAllStoreList(['id','name'],$condition,$bind);
        if (isset($params['search_name']) && strstr("head office", strtolower($params['search_name']))) {
            $row['id'] = '-1';
            $row['name'] = GlobalEnums::HEAD_OFFICE;
            array_unshift($data,$row);
        }

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * @Token
     */
    public function job_title_listAction()
    {
        $params = $this->request->get();
        $condition = $bind = [];
        if(isset($params['search_name'])){
            $condition = ['status = 1 and job_name LIKE :job_name:'];
            $bind = [
                'job_name'=>'%'.$params['search_name'].'%',
            ];
        }
        $result = (new HrJobTitleService())->searchJobTitleData(['id','job_name'],$condition,$bind);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }



    /**
     * @Token
     * @Permission(action='work_day_setting')
     */
    public function cancel_shiftAction(){
        $staff_id = $this->paramIn['staff_info_id'];
        $date =$this->paramIn['date'];
        $operator = $this->paramIn['operator'];

        if(empty($staff_id) || empty($date)){
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'wrong param', []);
        }

        //能不能修改自己的轮休
        $workday_bll = new WorkdayService();
        $workday_bll->checkSelfEdit($operator, $staff_id);
        $flag = $workday_bll->checkTruckUpdatePermission([$staff_id], $operator);
        if(!$flag){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR,$this->t->_('workday_truck_permission'), []);
        }

        //验证员工
        $staffInfo = (new StaffService())->getHrStaffInfo($staff_id);
        if(empty($staffInfo)){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_LEAVE_ERROR,$this->t->_('staff_id_not_found'), []);
        }
        //只能取消当天之后的
        $today = date('Y-m-d');
        $sysService = new SysService;
        $isWorkDayRootId =$sysService->setCurrentStaffId($this->user['id'])->isWorkDayRootId();
        $canModifyTodayRest = $sysService->setCurrentStaffId($this->user['id'])->canModifyTodayRest()
            && $staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_SIX &&  $staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_1;
        if (!$isWorkDayRootId) {
            if ($canModifyTodayRest && $date < $today || !$canModifyTodayRest && $date <= $today) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'can not cancel,wrong date chosen', []);
            }
        }

        //当天有审批通过或者待审批的加班申请，请撤销加班申请后再调整休息日
        $check_overtime = $workday_bll->check_overtime($staff_id,$date);
        if(!empty($check_overtime)){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR,$this->t->_('workdays_check_overtime_alert_v2',['staff_info_id'=>$staff_id,'name'=>$staffInfo['name']]), []);
        }
        //当天有请假申请，限制不能取消休息日
        $check_leave_all =  $workday_bll->check_leave_all($staff_id,$date);
        if(!empty($check_leave_all)){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_LEAVE_ERROR,$this->t->_('workdays_check_leave_alert_v2',['staff_info_id'=>$staff_id,'name'=>$staffInfo['name']]), []);
        }

        $workday_bll->cancel($staff_id,$date, $operator);

        //日志
        $this->logger->info("staff_workdays_cancel {$staff_id} {$date} {$operator}");
        //发送消息 取消
        $workday_bll->staff_set_work_day_send_message($staff_id, $date, 1);//轮休发送消息取消
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }


    /**
     * 列表导出
     * @Token
     * @Permission(action='work_day_view')
     * @throws \Exception
     */
    public function exportAction()
    {
        $month                  = empty($this->paramIn['month']) ? date('Y-m', time()) : $this->paramIn['month'];
        $param['store_id']      = empty($this->paramIn['store_id']) ? [] : $this->paramIn['store_id'];
        $param['state']      = empty($this->paramIn['state']) ? [] : $this->paramIn['state'];
        $param['area']          = empty($this->paramIn['area']) ? '' : $this->paramIn['area'];
        $param['area']          = empty($this->paramIn['area']) ? '' : $this->paramIn['area'];
        $param['job_title']     = empty($this->paramIn['job_title']) ? '' : $this->paramIn['job_title'];
        $param['staff_info_id'] = empty($this->paramIn['staff_info_id']) ? '' : $this->paramIn['staff_info_id'];
        //部门页面搜索
        $param['search_department'] = empty($this->paramIn['search_department']) ? '' : $this->paramIn['search_department'];
        $param['hire_type']         = $this->paramIn['hire_type'] ?? [];
        $work_type                  = empty($this->paramIn['work_type']) ? '6_1' : $this->paramIn['work_type'];
        $param['week_working_day']  = explode('_', $work_type)[0];
        $param['rest_type']         = explode('_', $work_type)[1];
        $param['lang']              = $this->locale;
        $param['month']             = $month;
        $param['operator']          = $this->userinfo['id'];


        $file_name = uniqid('workday_' . date('Y-m-d')) . '.xlsx';
        $return    = (new ExcelService())->insertTask($param['operator'], \WorkdayTask::$listExportAction, $param, 1,
            $file_name);
        return $this->returnJson($return['code'], $return['message'], $return['data'] ?? []);
    }



    //获取 是否是 部门负责人
    protected function is_manager($staff_id){
        $model = new SysDepartmentService();
        $res = $model->get_dep_manager($staff_id);
        if(empty($res))
            return false;

        $dep_ids = array_column($res,'id');//负责的所有部门
        return $dep_ids;
    }


    //一级部门列表 下拉菜单接口
    public function get_department_listAction(){
        $p_id = empty($this->paramIn['p_id']) ? 0 : $this->paramIn['p_id'];
        $p_id = intval($p_id);
        $bll = new SysDepartmentService();
        $list = $bll->department_list($p_id);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }


    /**
     * @Token
     * @Permission(action='work_day_view')
     */
    public function suggestAction()
    {
        $param['store_id']         = $this->paramIn['store_id'];
        $param['month']            = $this->paramIn['month'];
        $work_type                 = empty($this->paramIn['work_type']) ? '0_0' : $this->paramIn['work_type'];
        $param['week_working_day'] = explode('_', $work_type)[0];
        $param['rest_type']        = explode('_', $work_type)[1];
        $validation                = [
            'month'    => 'Required|Str|>>>:month error',
        ];
        Validation::validate($param, $validation);

        //网点主管和网点经理 直接展示排班建议 又改为 如果是网点主管 管辖网点超过2个 也不显示
        if(!empty($this->paramIn['pre_store_id'])){
            //网点主管和网点经理所属网点
            $param['pre_store_id'] = $this->paramIn['pre_store_id'];
        }
        $param['operate_id'] = $this->userinfo['id'];

        //员工信息
        $staff_bll = new WorkdayService();
        $result    = $staff_bll->suggestRow($param);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }


    /**
     * 修改班次页面展示排版建议 和 班次可选下拉
     * @Token
     * @Permission(action='work_day_view')
     */
    public function shiftChangeInfoAction()
    {
        $params['staff_info_id']      = $this->paramIn['staff_info_id'];
        $params['date_at']            = $this->paramIn['date_at'];
        $params['job_title']          = $this->paramIn['job_title'];
        $params['store_id']          = $this->paramIn['store_id'];
        $params['node_department_id'] = $this->paramIn['node_department_id'];
        $validation                   = [
            'staff_info_id'      => 'Required|int|>>>:staff_info_id error',
            'job_title'          => 'Required|int|>>>:job_title error',
            'date_at'            => 'Required|Date|>>>:date error',
            'store_id'           => 'Required|Str',
            'node_department_id' => 'Required|int|>>>:node_department_id error',
        ];
        Validation::validate($params, $validation);

        $server = new HrShiftService();
        $res    = $server->changeInfo($params, $this->userinfo['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $res);
    }


    /**
     * 权限 班次菜单权限
     * 六天班轮休修改班次
     * @Token
     * @Permission(action='work_day_setting')
     */
    public function shiftChangeAction()
    {
        $params['staff_info_id'] = $this->paramIn['staff_info_id'];
        $params['date_at']       = $this->paramIn['date_at'];
        $params['shift_id']      = $this->paramIn['shift_id'];
        $params['job_title']     = $this->paramIn['job_title'];
        $params['store_id']      = $this->paramIn['store_id'];
        $params['start']         = $this->paramIn['start'];
        $params['is_submit']     = $this->paramIn['is_submit'] ?? 0;
        $params['update_all']    = $this->paramIn['update_all'] ?? 0;

        //新增参数 1 当天和之后日期有效 2 仅当天有效
        $params['change_type'] = $this->paramIn['change_type'];
        $validation            = [
            'staff_info_id' => 'Required|int|>>>:staff_info_id error',
            'job_title'     => 'Required|int|>>>:job_title error',
            'shift_id'      => 'Required|int|>>>:shift_id error',
            'date_at'       => 'Required|Date|>>>:date error',
            'store_id'      => 'Required|Str',
            'start'         => 'Required|Str',
            'change_type'   => 'Required|int|>>>:change_type error',
        ];
        Validation::validate($params, $validation);
        $params['operator_id'] = $this->userinfo['id'];
        $params['operator_job_title'] = $this->user['job_title'];
        $params['operator_name'] = $this->userinfo['name'];

        try {
            $server = new HrShiftService();
            $res    = $server->changeShiftUseLock($params);
        } catch (ValidationException $b) {
            if ($b->getCode() == 10086) {
                $data = ['code' => -1, 'message' => $b->getMessage()];
                return $this->returnJson(1, $b->getMessage(), $data);
            }
            if ($b->getCode() == 10087) {
                $data = ['code' => -2, 'message' => $b->getMessage()];
                return $this->returnJson(1, $b->getMessage(), $data);
            }
            throw $b;
        }
        return $this->returnJson($res['code'] ?? ErrCode::SUCCESS, $res['msg'] ?? '', $res['data'] ?? null);
    }


    /**
     * 五天班&六天班轮休 设置默认休息日
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @Permission(action='work_day_setting')
     */
    public function editDefaultRestDayDateAction(){
        $staffId = $this->paramIn['staff_info_id'];
        $defaultRestDayDate = $this->paramIn['default_rest_day_date'];
        if (empty($defaultRestDayDate) || !is_array($defaultRestDayDate) || count($defaultRestDayDate)> 2) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        (new DefaultRestDayService())->editDefaultRestDayDate($staffId,$defaultRestDayDate, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }


    /**
     * 导入 班次和默认休息日
     * @Token
     * @Permission(action='work_day_setting')
     */
    public function importRoleAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            'file_url' => 'Required|Str',
        ]);
        $server = new WorkDayImportService();
        $params['user_info'] = $this->user;
        $data   = $server->import($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);

    }

    /**
     * 下载模板
     * @Token
     * @Permission(action='work_day_setting')
     */
    public function downTempAction(){
        $setting = (new SettingEnvService())->getSetVal('workday_import_temp');
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $setting);
    }


    /**
     * 操作日志 轮休 班次 默认休 都在这里面
     * @Token
     * @Permission(action='work_day_operate_log')
     */
    public function operateLogListAction(){
        $params = $this->request->get();
        Validation::validate($params, [
            'page' => 'Required|Int',
            'size' => 'Required|Int',
        ]);
        $server = new WorkShiftService();
        $params['user_info'] = $this->user;
        $params['current_uid'] = $this->user['id'];
        $data   = $server->logList($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), $data);

    }


    /**
     * 操作日志导出
     * @Token
     * @Permission(action='work_day_operate_log')
     */
    public function operateLogExportAction()
    {
        $param              = $this->request->get();
        $param['user_info'] = $this->user;
        //公用验证时间跨度
        $param['lang']        = $this->locale;
        $param['current_uid'] = $this->user['id'];
        $res                  = (new BllService())->exportToDownloadCenter(
            $this->user['id'],
            "workday_shift_log_data",
            "workday" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'exportOperateLog',
            $param
        );
        if (empty($res)) {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'server error', []);
        }
        [$msg, $code] = $res;
        return $this->returnJson($code, $msg, []);
    }


}