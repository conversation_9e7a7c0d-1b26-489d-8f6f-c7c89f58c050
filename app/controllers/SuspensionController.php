<?php
/**
 * Author: Bruce
 * Date  : 2023-06-08 11:42
 * Description:
 */

namespace App\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\SuspensionService;

class SuspensionController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @Token
     */
    public function getSysInfoAction()
    {
        $result = (new SuspensionService())->getSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-停职员工列表
     * @Token
     * @Permission(action='suspension_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'page_num'  => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ];
        Validation::validate($params, $validate_rule);

        $result = (new SuspensionService())->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-停职员工导出
     * @Token
     * @Permission(action='suspension_export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportListAction()
    {
        $params                = $this->request->get();
        $params['is_download'] = 1;
        $params['file_name']   = uniqid('staff_suspension_export_' . $this->locale . '_') . '.xlsx';
        $params['lang']        = $this->locale;
        $params['staff_id']    = $this->user['id'];
        // 加锁处理
        (new SuspensionService())->handleExportUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], []);
    }

    /**
     * 停职管理-查看停职文件
     * @Token
     * @Permission(action='suspension_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function checkFileListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'suspension_id' => 'Required|IntGt:0',
            'staff_info_id' => 'Required|IntGt:0',
        ];
        Validation::validate($params, $validate_rule);

        $result = (new SuspensionService())->checkFileListInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-再次发送邮件
     * @Token
     * @Permission(action='suspension_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function sendEmailAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'file_id' => 'Required|IntGt:0',
            'email'   => 'Required|Email|>>>:' . $this->t->_('email_is_error'),
        ];
        Validation::validate($params, $validate_rule);

        $result = (new SuspensionService())->send($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-停职记录
     * @Token
     * @Permission(action='suspension_log_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     */
    public function getLogListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'page_num'  => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ];
        Validation::validate($params, $validate_rule);

        $result = (new SuspensionService())->getLogList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-停职历史记录-详情
     * @Token
     * @Permission(action='suspension_log_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @throws \Exception
     */
    public function getLogHistoryListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'staff_info_id' => 'Required|IntGt:0', // 员工工号
        ];
        Validation::validate($params, $validate_rule);
        $suspensionService = reBuildCountryInstance(new SuspensionService());

        $result = $suspensionService->getLogHistoryList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职管理-停职记录-审批详情
     * 两个tab页 共用 接口
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     * @throws \App\Library\Validation\ValidationException
     */
    public function auditDetailAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'suspension_log_id' => 'Required|IntGt:0', //停止记录id
        ];
        Validation::validate($params, $validate_rule);

        $result = (new SuspensionService())->auditDetailInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 工号搜索，获取员工信息
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getStaffListAction()
    {
        $param['staff_info_id'] = $this->request->get('staff_info_id', 'int');
        $result                 = (new SuspensionService())->getStaffListInfo($param);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取停职签字配置信息
     * @Token
     * @Permission(action='suspension_sign_config_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function signConfigListAction()
    {
        $params = $this->request->get();
        $result = (new SuspensionService())->signConfigList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取停职签字配置-详情
     * @Token
     * @Permission(action='suspension_sign_config.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function signConfigDetailAction()
    {
        $params     = $this->request->get();
        $validation = [
            "sign_id" => "Required|Int|IntGt:0",
        ];
        Validation::validate($params, $validation);

        $result = (new SuspensionService())->signConfigDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 添加停职签字配置
     * @Token
     * @Permission(action='suspension_sign_config.add')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function signConfigAddAction()
    {
        $params     = $this->request->get();
        $validation = [
            "staff_info_id" => "Required|Int|IntGt:0",
            "sign_url"      => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);

        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new SuspensionService())->signConfigAddUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 编辑停职签字配置
     * @Token
     * @Permission(action='suspension_sign_config.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function signConfigEditAction()
    {
        $params     = $this->request->get();
        $validation = [
            "sign_id"  => "Required|Int|IntGt:0",
            "sign_url" => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);

        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new SuspensionService())->signConfigEditUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }


    /**
     * 删除警告书签字配置
     * @Token
     * @Permission(action='suspension_sign_config.delete')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function signConfigDeleteAction()
    {
        $params     = $this->request->get();
        $validation = [
            "sign_id" => "Required|Int|IntGt:0",
        ];
        Validation::validate($params, $validation);

        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];
        $result                  = (new SuspensionService())->signConfigDeleteUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * PH 发送文件预览
     * @Token
     * @Permission(action='suspension_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addFilePreviewAction()
    {
        $params     = $this->request->get();
        $validation = [
            "suspension_manage_log_id" => "Required|Int|IntGt:0",
            "type"                     => "Required|Int|IntGt:0",
            "email"                    => "Required|StrLenGe:1",
            "sign_staff_id"            => "Required|Int|IntGt:0",

        ];
        Validation::validate($params, $validation);

        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new SuspensionService())->addFilePreviewUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * PH 发送文件预览
     * @Token
     * @Permission(action='suspension_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addFileSendAction()
    {
        $params     = $this->request->get();
        $validation = [
            "suspension_manage_log_id" => "Required|Int|IntGt:0",
            "type"                     => "Required|Int|IntGt:0",
            "email"                    => "Required|StrLenGe:1",
            "sign_staff_id"            => "Required|Int|IntGt:0",
            "file_url"                 => "Required|StrLenGe:1",
            'cc_email'                 => 'Arr|>>>:' . $this->t->_('email_is_error'),
            'cc_email[*]'              => 'Email|>>>:' . $this->t->_('email_is_error'),
        ];
        Validation::validate($params, $validation);

        $params['operator_id']   = $this->user['id'];
        $params['operator_name'] = $this->user['name'];

        $result = (new SuspensionService())->addFileSendUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职记录-导出停职明细
     * @Token
     * @Permission(action='suspension_export_detail')
     */
    public function exportDetailAction()
    {
        $params              = $this->request->get();
        $params['file_name'] = uniqid('staff_suspension_detail_export_' . $this->locale . '_') . '.xlsx';
        $params['lang']      = $this->locale;
        $params['staff_id']  = $this->user['id'];
        // 加锁处理
        (new SuspensionService())->handleDetailExportUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], []);
    }

    /**
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function auditSysInfoAction()
    {
        $result = (new SuspensionService())->auditSysInfo();
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职申请-列表
     * @Token
     * @Permission(action='suspension_audit_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function auditListAction()
    {
        $params        = $this->request->get();
        $validate_rule = [
            'page_num'  => 'IntGt:0',
            'page_size' => 'IntGt:0',
        ];
        Validation::validate($params, $validate_rule);
        $params['staff_id'] = $this->user['id'];

        $result = (new SuspensionService())->auditList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 停职申请-停职员工导出
     * @Token
     * @Permission(action='suspension_audit_export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function auditExportAction()
    {
        $params              = $this->request->get();
        $params['file_name'] = uniqid('request_nte_' . date('YmdHis') . $this->locale . '_') . '.xlsx';
        $params['lang']      = $this->locale;
        $params['staff_id']  = $this->user['id'];
        // 加锁处理
        (new SuspensionService())->auditExportUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t['file_download_tip'], []);
    }


    /**
     * 停职申请-停职员工导入
     * @Token
     * @Permission(action='suspension_audit_import')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function auditImportAction()
    {
        $params     = $this->request->get();
        $validation = [
            "file_name" => "Required|StrLenGe:1",
            "file_url"  => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $params['lang']     = $this->request->getBestLanguage();
        $params['staff_id'] = $this->user['id'];

        (new SuspensionService())->auditImportUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, $this->t['async_upload_tip'], []);
    }

    /**
     * 停职管理-撤销
     * @Token
     * @Permission(action='suspension_audit_cancel')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function auditCancelAction()
    {
        $params     = $this->request->get();
        $validation = [
            "id" => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $params['staff_id'] = $this->user['id'];

        (new SuspensionService())->auditCancelUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', []);
    }

    /**
     * 停职申请-查看
     * @Token
     * @Permission(action='suspension_audit_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function auditCheckAction()
    {
        $params     = $this->request->get();
        $validation = [
            "id" => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);
        $params['staff_id'] = $this->user['id'];

        $data = (new SuspensionService())->auditCheck($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * 停职申请-审批
     * @Token
     * @Permission(action='suspension_audit_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function auditSubmitAction()
    {
        $params     = $this->request->get();
        $validation = [
            "audit_id" => "Required|StrLenGe:1",
            "status"   => "Required|IntIn:2,3",
        ];

        Validation::validate($params, $validation);
        $params['staff_id']   = $this->user['id'];
        $params['staff_name'] = $this->user['name'];

        (new SuspensionService())->auditSubmitUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', []);
    }

    /**
     * 停职管理-获取下载模板
     * @Token
     * @Permission(action='suspension_audit_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function importTemplateAction()
    {
        $data = (new SuspensionService())->importTemplate();

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $data);
    }

    /**
     * 停职申请-停职工号是否有 有效停职的申请
     * @Token
     * @Permission(action='suspension_audit_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function checkOtherInfoAction()
    {
        $params     = $this->request->get();
        $validation = [
            "audit_id" => "Required|StrLenGe:1",
            "status"   => "Required|IntIn:2,3",
        ];

        Validation::validate($params, $validation);
        $params['staff_id']   = $this->user['id'];
        $params['staff_name'] = $this->user['name'];

        $res = (new SuspensionService())->checkOtherInfoUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $res);
    }


}