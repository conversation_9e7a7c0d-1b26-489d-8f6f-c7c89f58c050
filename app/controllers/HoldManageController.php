<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\ExcelService;
use App\Services\HoldManageService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class HoldManageController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 筛选条件
     * @Token
     * @return Response|ResponseInterface
     */
    public function screeningAction()
    {
        $data = (new HoldManageService())->getScreeningList();
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * hold list
     * @Token
     * @Permission(action='hold_manage.hold_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function holdListAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $validations = $hold_manage_service->HoldListParamsValidations();
        $validations = array_merge($validations, ['page_num' => 'Required|IntGe:1', 'page_size' => 'Required|IntGe:10']);
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);

        Validation::validate($params, $validations);

        $list = $hold_manage_service->getHoldList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $list);
    }

    /**
     * hold 详情
     * @Token
     * @Permission(action='hold_manage.hold_info')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function holdInfoAction()
    {
        $params = $this->request->get();
        $validations = [
            'id' => 'Required|Int',
        ];
        Validation::validate($params, $validations);

        $hold_info = (new HoldManageService())->getHoldInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $hold_info);
    }

    /**
     * 添加 hold
     * @Token
     * @Permission(action='hold_manage.add_hold')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addHoldAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $v_handle_people    = implode(',', array_values(HoldManageService::$handle_people));
        $v_hold_reason      = implode(',', array_values(HoldManageService::$hold_reasons));
        $v_hold_reason_id   = implode(',', array_keys(HoldManageService::$hold_reasons));
        $validations = [
            'staff_info_id' => 'Required|Int',          //员工id
            'type'          => 'Required|IntIn:1,2,3',  //hold 类型
            'hold_reason'   => 'Required|StrIn:'.$v_hold_reason,    //原因
            'handle_people' => 'Required|StrIn:'.$v_handle_people,  //处理人
            'reason_id'     => 'Required|IntIn:'.$v_hold_reason_id, //原因id
            'hold_remark'   => 'Str',       //备注
            'hold_attachments' => 'Arr',    //附件
            //'month_begin'   => 'Date',
            //'month_end'     => 'Date'
        ];
        Validation::validate($params, $validations);

        $params['staff_submit_id'] = $this->user['id']; //提交人
        $params['handle_progress'] = 1;                 //新增默认1 处理状态(1:未处理,2:处理中,3:已处理)
        $params['hold_source'] = 1;                     //hold来源(1.hold 新增,2.hirs)
        $params['version'] = HoldManageService::$version;

        $add_hold_params = $hold_manage_service->combinationAddHoldParams($params);
        $add_hold_result = $hold_manage_service->addHold($add_hold_params);
        if($add_hold_result) {
            //同步员工管理
            $api_params = [
                'type' => 1,
                'staff_info_id' => $params['staff_info_id'],
                'payment_markup' => $params['reason_id'],
                'operator_id' => $params['staff_submit_id'],
                'stop_payment_type' => $params['type'] == 3 ? '1,2' : $params['type'],
            ];
            $sync_hold_result = $hold_manage_service->syncHrisStaffHoldSvc($api_params);
            $this->logger->info(['function' => 'addHoldAction', 'service' => 'syncHrisStaffHoldSvc', 'params' => $api_params, 'result' => $sync_hold_result]);
        }
        return $this->returnJson(ErrCode::SUCCESS, '', []);
    }

    /**
     * hold 操作
     * @Token
     * @Permission(action='hold_manage.handle_hold')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function handleHoldAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $validations = [
            'id'                => 'Required|Int',          //hold id
            'staff_info_id'     => 'Required|Int',          //工号
            'hold_remark'       => 'Required|Str',          //处理备注
            'hold_attachments'  => 'Required|Arr',       //附件
            'hold_attachments_up' => 'Arr',                //附件
            'handle_progress'   => 'Required|IntIn:1,2,3',  //处理状态
            'handle_result'     =>  'Required|Arr',         //处理意见
            //'month_begin'       => 'Date',
            //'month_end'         => 'Date'
        ];
        Validation::validate($params, $validations);
        $params['staff_submit_id'] = $this->user['id'];

        $result = $hold_manage_service->uploadHold($params);
        if($result) {
            return $this->returnJson(ErrCode::SUCCESS, '', []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
    }

    /**
     * hold 导出
     * @Token
     * @Permission(action='hold_manage.export_hold')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportHoldAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $validations = $hold_manage_service->HoldListParamsValidations();

        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);

        Validation::validate($params, $validations);

        $data = $hold_manage_service->exportHoldList($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * 工资/提成释放列表
     * @Token
     * @Permission(action='hold_manage.release_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function releaseListAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $validations = $hold_manage_service->releaseListParamsValidations();
        $validations = array_merge($validations, ['page_num' => 'Required|IntGe:1', 'page_size' => 'Required|IntGe:10']);
        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);

        Validation::validate($params, $validations);

        $list = (new HoldManageService())->getReleaseList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $list);
    }

    /**
     * 工资/提成释放下载
     * @Token
     * @Permission(action='hold_manage.release_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportReleaseAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();
        $validations = $hold_manage_service->releaseListParamsValidations();

        $params = array_only($params, array_keys($validations));
        $params = array_filter($params);

        Validation::validate($params, array_merge($validations));

        $data = (new HoldManageService())->exportReleaseList($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * 更新释放
     * @Token
     * @Permission(action='hold_manage.update_release')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateReleaseAction()
    {
        $params = $this->request->get();
        $hold_manage_service = new HoldManageService();

        $validations = [
            'staff_info_id'  => 'Required|Int',        //工号
            'release_state'  => 'Required|IntIn:1,2',  //1阻止 2释放
            'type'           => 'Required|IntIn:1,2',  //1工资 2提成
            'payment_markup' => 'Required|Str',        //备注
        ];
        Validation::validate($params, $validations);
        $release_type = 2;

        $update_release_params = [
            'staff_info_id' => $params['staff_info_id'],
            'release_state' => $params['release_state'],
            'type' => $params['type'],
            'release_submit_id' => $this->user['id'],
        ];
        $update_release_result = $hold_manage_service->updateRelease($update_release_params);
        if($update_release_result) {
            $payment_markup = $params['payment_markup']; //工资发放备注
            if($params['release_state'] == 1) {
                $release_type = 1;
                //同步员工管理
                $payment_markup = implode(',', $hold_manage_service->getStaffReasonList($params['staff_info_id']));
            }

            $api_params = [
                'type' => $release_type,
                'staff_info_id' => $params['staff_info_id'],
                'payment_markup' => $payment_markup,
                'operator_id' => $this->user['id'],
                'stop_payment_type' => $params['type'],
            ];
            $sync_hold_result = $hold_manage_service->syncHrisStaffHoldSvc($api_params);
            $this->logger->info(['function' => 'updateReleaseAction', 'service' => 'syncHrisStaffHoldSvc', 'params' => $api_params, 'result' => $sync_hold_result]);
            return $this->returnJson(ErrCode::SUCCESS, '', []);
        } else {
            return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
        }
    }

    /**
     * 获取员工基本信息
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function staffInfoAction()
    {
        $params = $this->request->get();
        $validations = [
            'staff_info_id' => 'Required|Int',
        ];
        Validation::validate($params, $validations);

        $staff_info_id = $params['staff_info_id'];
        $staff_info = (new HoldManageService())->getStaffInfo($staff_info_id);
        return $this->returnJson(ErrCode::SUCCESS, '', $staff_info);
    }

    /**
     * 批量释放
     * @Permission(action='hold_manage.batch_release')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function batchReleaseAction()
    {
        $params = $this->request->get();
        $excel_file = $this->request->getUploadedFiles();

        $validations = [
            'type' => 'Required|IntIn:1,2',  //释放类型
        ];
        Validation::validate($params, $validations);

        if(empty($excel_file)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'file extension error');
        }

        $file = $excel_file[0];
        $hold_manage_service = new HoldManageService();

        $extension = $file->getExtension();
        if($extension !== "xlsx"){
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'file extension error');
        }

        // 读取文件
        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);
        $excel_data = $excel->openFile($file->getTempName())
                            ->openSheet()
                            ->setSkipRows(1)
                            ->getSheetData();

        if (count($excel_data) < 1) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'Excel error');
        }

        $data = $hold_manage_service->addHoldManageBatchRelease([
            'operator_id' => $this->user['id'],
            'release_type' => $params['type'],
            'file_name' => $file->getName(),
            'excel_data' => $excel_data
        ]);
        //$data = $hold_manage_service->batchRelease(['excel_data' => $excel_data, 'operator_id' => $this->user['id'], 'type' => $params['type']]);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 批量释放历史列表
     * @Permission(action='hold_manage.batch_release')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchReleaseHistoryAction()
    {
        $params = $this->request->get();
        $validations = ['release_type' => 'Required|IntIn:1,2', 'page_num' => 'Required|IntGe:1', 'page_size' => 'Required|IntGe:1'];
        Validation::validate($params, $validations);
        $params['user'] = $this->user;
        $hold_manage_service = new HoldManageService();
        $date = $hold_manage_service->getReleaseHistoryList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $date);
    }

    /**
     * 删除批量上传释放历史数据
     * @Permission(action='hold_manage.batch_release')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delReleaseAction()
    {
        $params = $this->request->get();
        $validations =  [
            'release_type' => 'Required|IntIn:1,2',
            'del_ids' => 'Required|Arr',
            'del_ids[*]' => 'Required|Int',
        ];
        Validation::validate($params, $validations);
        $hold_manage_service = new HoldManageService();
        $params['user'] = $this->user;
        $result = $hold_manage_service->delReleaseHistory($params);
        if($result) {
            return $this->returnJson(ErrCode::SUCCESS, '', []);
        }
        return $this->returnJson(ErrCode::SYSTEM_ERROR, 'error', []);
    }

    /**
     * @Permission(action='resign_early')
     * @return Response|ResponseInterface
     */
    public function resignEarlyAction()
    {
        $params = $this->request->get();
        $data   = reBuildCountryInstance(new HoldManageService())->getResignEarly($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }


    /**
     * @Permission(action='resign_early')
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function resignEarlyExportAction()
    {
        $params = $this->request->get();
        $params['staff_id']  = $this->user['id'];
        $params['lang']      = $this->locale;
        if (isCountry('TH')) {
            $file_name = uniqid('Apply_for_resignation_less_than_30days' . date('YmdHis')) . '.xlsx';
        } else {
            $file_name = uniqid('Apply_for_resignation_less_than_rules' . date('YmdHis')) . '.xlsx';
        }
        $action_name         = 'hold'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'resignEarlyExport';

        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $file_name);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('file_download_tip'), []);

    }


}