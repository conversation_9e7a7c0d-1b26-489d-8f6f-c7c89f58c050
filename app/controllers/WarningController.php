<?php
/**
 * 电子警告书-从FBI迁移到HCM系统
 * date：2022-03-03
 * author：donghao
 */

namespace app\controllers;

use App\Library\Enums\MessWarningEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\ReportService;
use App\Services\StaffService;
use App\Services\WarningService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class WarningController extends BaseController
{
    public $paramIn;
    public $userinfo;
    public $staff_service;
    public $warning_service;
    function initialize()
    {
        parent::initialize();
        //获取bi-userinfo
        if(!empty($this->user['id'])){
            $this->userinfo = (new StaffService())->get_fbi_user_info($this->user['id']);
        }
    }


    /**
     * 获取枚举数据
     * @return Response|ResponseInterface
     * @Token
     */
    function getMenuListAction()
    {
        $params = $this->request->get();
        $data = (new WarningService())->getMenuList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 获取警告书发送列表
     * @Token
     * @return Response|ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning.warning_list')
     */
    function getWarningListAction()
    {
        $params = $this->request->get();
        $params = Validation::ignoreParams($params, ['staff_id','store_category','department','manager_area','type_code','warning_type','sign_state','start_day','end_day', 'staff_state']);
        $validation = [
            "page" => 'Required|Int',
            "pagesize" => 'Required|Int',
            "store_category" => 'IntIn:' . implode(",", array_keys(MessWarningEnums::$store_type_map)),
            "department" => 'IntGe:0',
            "manager_area" => 'IntIn:' . implode(",", array_keys(GlobalEnums::$areas)),
            "start_day" => "Date",
            "end_day" => "Date",
            "sign_state" => "IntIn:" . implode(",", array_keys(MessWarningEnums::$sign_state_desc)),
            "type_code" => "Arr",
            "warning_type" => "Arr",
            "staff_state" => "Arr",
            'is_sub_department' => 'Required|IntIn:0,1|>>>:is_sub_department param error' // 是否包含子部门，0 不包含 1 包含
        ];
        Validation::validate($params, $validation);

        $data = (new WarningService())->getWarningList($params, $this->userinfo);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 导出excel文件
     * @Token
     * @return Response|ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning.warning_list')
     */
    function exportWarningListAction()
    {
        $params = $this->request->get();
        $params = Validation::ignoreParams($params, ['staff_id','store_category','department','manager_area','type_code','warning_type','sign_state','start_day','end_day', 'staff_state']);
        if(isset($params['staff_id']) && empty($params['staff_id'])){
            unset($params['staff_id']);
        }
        $validation = [
            "store_category" => 'IntIn:' . implode(",", array_keys(MessWarningEnums::$store_type_map)),
            "department" => 'IntGe:0',
            "manager_area" => 'IntIn:' . implode(",", array_keys(GlobalEnums::$areas)),
            "start_day" => "Date",
            "end_day" => "Date",
            "staff_id" => "IntGe:0",
            'is_sub_department' => 'Required|IntIn:0,1|>>>:is_sub_department param error', // 是否包含子部门，0 不包含 1 包含
            "staff_state" => "Arr",
        ];
        Validation::validate($params, $validation);

        $result = (new WarningService())->exportWarningList($params, $this->userinfo);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 获取警告记录
     * @Token
     * @return Response|ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning.add_warning')
     */
    function getWarningListByStaffAction()
    {
        $params = $this->request->get();
        $validation = [
            'staff_id' => 'Required|Int',
            'page' => 'Required|Int',
            'page_size' => 'Required|Int',
        ];
        Validation::validate($params, $validation);

        $data = (new WarningService())->getWarningListByStaff($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 获取员工信息和警告次数
     * @Token
     * @return Response|ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning.add_warning')
     */
    function getStaffInfoAction(){
        $params = $this->request->get();
        $params = Validation::ignoreParams($params,['report_id']);
        $validation = [
            'staff_id' => 'Required|Int',
            'report_id' => 'Int',
        ];
        Validation::validate($params, $validation);

        $WarningService           = new WarningService();
        $WarningService->userinfo = $this->userinfo;
        $data = $WarningService->getStaffInfo($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * pdf预览接口
     * @Token
     * @return Response|ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning.add_warning')
     */
    function previewAction(){
        $params = $this->request->get();
        $violation_reason_type = (new WarningService())->get_violation_reason_type();
        $violation_reason_type = array_column($violation_reason_type,'t_key');
        $validation = [
            "staff_id" => "Required|Int",
            "warning_type" => "Required|IntIn:1,2,3",
            "type_code" => "Required|StrIn:" . implode(",", $violation_reason_type),
            "date_ats" => "Required|Arr",
            "hold_type" => "Required|IntIn:" . implode(",", array_keys(MessWarningEnums::$hold_type)),
            "task"           => "Required|StrLenGeLe:1,10000|>>>:Improvement has to be between 1 and 10000",
            "content"        => "Required|StrLenGeLe:1,10000|>>>:content has to be between 1 and 10000",
            "regulations"    => "Required|StrLenGeLe:1,10000|>>>:regulations has to be between 1 and 10000",
            "warning_result" => "Required|StrLenGeLe:1,10000|>>>:warning_result has to be between 1 and 10000",
        ];
        Validation::validate((array)$params, $validation);

        $urlData = [
            'staff_id'=>$params['staff_id'],
            'user_id'=>$this->user['id'],
            's'=>md5('flash-'.$params['staff_id']),
        ];

        $_url = http_build_query($urlData);
        
        $key = "warning-preview-{$params['staff_id']}-{$urlData['user_id']}";
        (new WarningService())->setCache($key, json_encode($params), 60*5);

        $url = rtrim(env("base_url"),'/') . "/Datatmp/warning_message?" .$_url ;
        return $this->returnJson(ErrCode::SUCCESS, 'success', ['data' => $url]);
    }

    /**
     * 发送电子警告 书
     * @Token
     * @return Response|ResponseInterface
     * @throws \App\Library\Validation\ValidationException
     * @Permission(action='warning.add_warning')
     */
    function addWarningAction()
    {
        $params     = $this->request->get();
        $violation_reason_type = (new WarningService())->get_violation_reason_type([],false);
        $violation_reason_type = array_column($violation_reason_type,'t_key');
        $validation = [
            "staff_id"       => "Required|Int",
            "warning_type"   => "Required|IntIn:1,2,3",
            "type_code"      => "Required|StrIn:".implode(",", $violation_reason_type),
            "date_ats"       => "Required|Arr",
            "hold_type"      => "Required|IntIn:".implode(",", array_keys(MessWarningEnums::$hold_type)),
            "task"           => "Required|StrLenGeLe:1,10000",
            "content"        => "Required|StrLenGeLe:1,10000",
            "regulations"    => "Required|StrLenGeLe:1,10000",
            "warning_result" => "Required|StrLenGeLe:1,10000",
        ];
        Validation::validate($params, $validation);

        //如果是提成 并且有限制月份的时候
        if ($params['hold_type'] == MessWarningEnums::HOLD_TYPE_INCENTIVE && $params['month_begin'] && $params['month_end']) {
            $params['month_begin'] = date("Y-m-01", strtotime($params['month_begin']));
            $params['month_end']   = date("Y-m-t", strtotime($params['month_end']));
        } else {
            $params['month_begin'] = $params['month_end'] = '';
        }
        $params['task']           = nl2br($params['task']);   //任务
        $params['content']        = nl2br($params['content']);   //问题描述
        $params['regulations']    = nl2br($params['regulations']);   //制度
        $params['warning_result'] = nl2br($params['warning_result']);   //警告结果
        $operatorId               = $this->userinfo['id'];

        $lock_key = 'add_warning_message'.$params['staff_id'];
        $result   = $this->atomicLock(function () use ($params, $operatorId) {
            $WarningService           = new WarningService();
            $WarningService->userinfo = $this->userinfo;
            $insertId = $WarningService->addMessageWarning($params, $operatorId);
            return true;
        }, $lock_key, 15);
        if ($result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 工号搜索，获取员工信息
     * @Token
     * @return Response|ResponseInterface
     */
    public function getStaffListAction()
    {
        $param['staff_info_id'] = $this->request->get('staff_info_id', 'int');
        $result                 = (new WarningService())->getStaffListInfo($param);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取警告书签字配置信息
     * @Token
     * @Permission(action='warning.sign_config.list')
     * @return Response|ResponseInterface
     */
    public function signConfigListAction()
    {
        $params = $this->request->get();
        $result = (new WarningService())->signConfigList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 获取警告书签字配置-详情
     * @Token
     * @Permission(action='warning.template.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function signConfigDetailAction()
    {
        $params     = $this->request->get();
        $validation = [
            "sign_id"        => "Required|Int|IntGt:0",
        ];
        Validation::validate($params, $validation);

        $result = (new WarningService())->signConfigDetail($params);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * 添加警告书签字配置
     * @Token
     * @Permission(action='warning.sign_config.add')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function signConfigAddAction()
    {
        $params     = $this->request->get();
        $validation = [
            "signature_node"        => "Required|Int|IntGt:0",
            "staff_info_id"         => "Required|Int|IntGt:0",
            "sign_url"              => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);

        $params['operator_id']    = $this->user['id'];
        $params['operator_name']  = $this->user['name'];

        $lock_key = 'sign_config_add_'.$this->user['id'];
        $get_result   = $this->atomicLock(function () use ($params) {
            return (new WarningService())->signConfigAdd($params);
        }, $lock_key, 15);
        if($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($get_result['code'], $get_result['message'], $get_result['data']);
    }

    /**
     * 编辑警告书签字配置
     * @Token
     * @Permission(action='warning.sign_config.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function signConfigEditAction()
    {
        $params     = $this->request->get();
        $validation = [
            "sign_id"               => "Required|Int|IntGt:0",
            "signature_node"        => "Required|Int|IntGt:0",
            "sign_url"              => "Required|StrLenGe:1",
        ];
        Validation::validate($params, $validation);

        $params['operator_id']    = $this->user['id'];
        $params['operator_name']  = $this->user['name'];

        $lock_key = 'sign_config_edit_'.$this->user['id'];
        $get_result   = $this->atomicLock(function () use ($params) {
            return (new WarningService())->signConfigEdit($params);
        }, $lock_key, 15);
        if($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($get_result['code'], $get_result['message'], $get_result['data']);
    }

    /**
     * 删除警告书签字配置
     * @Token
     * @Permission(action='warning.sign_config.delete')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function signConfigDeleteAction()
    {
        $params     = $this->request->get();
        $validation = [
            "sign_id"               => "Required|Int|IntGt:0",
        ];
        Validation::validate($params, $validation);

        $lock_key = 'sign_config_delete_'.$this->user['id'];
        $get_result   = $this->atomicLock(function () use ($params) {
            return (new WarningService())->signConfigDelete($params);
        }, $lock_key, 15);
        if($get_result === false) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, $this->t->_('please_try_again'), []);
        }
        return $this->returnJson($get_result['code'], $get_result['message'], $get_result['data']);
    }

    /**
     * 移除警告信
     * @Token
     * @Permission(action='warning.remove')
     * @throws ValidationException
     */
    public function removeAction()
    {
        $params['ids']       = $this->request->get('ids');
        $params['staffInfo'] = $this->user;
        (new WarningService())->removeUseLock($params);
        return $this->returnJson(ErrCode::SUCCESS, $this->t->_('success'), []);
    }
    
    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function get_violation_reason_typeAction()
    {
        $params     = $this->request->get();
        $data = (new WarningService())->get_violation_reason_type($params['type_ids'],$params['is_deleted']);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

}