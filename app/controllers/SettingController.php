<?php

namespace App\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\BikeCompensationCoefficientService;
use App\Services\CarCompensationCoefficientService;
use App\Services\PdService;
use App\Services\SettingEnvService;
use App\Services\StaffExperienceAllowanceV2Service;
use App\Services\XxljobTaskServer;

class SettingController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
    }


    /**
     * @Permission(action='admin.settingenv.list')
     */
    public function setting_listAction()
    {
        $params = $this->request->get();
        $data = (reBuildCountryInstance(new SettingEnvService()))->getSettingListV2($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * 保存
     * @Permission(action='admin.settingenv.save')
     * @throws \App\Library\Exception\BusinessException
     */
    public function setting_saveAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params' => $params, 'action_staff' => $this->user['id']]);
        Validation::validate((array)$params, SettingEnvService::$s_v_param);

        if ($params['set_val'] && !preg_match('/^[1-9]+[0-9,]*[0-9]*$/', $params['set_val'])) {
            //throw new ValidationException('set_val数据格式错误，设置内容只能包含数字或英文逗号且只能数字开头和结尾');
        }
        //env 配置
        $params['staff_id'] = $this->user['id'];
        // 校验一些存在互斥关系的配置
        $this->filter($params);
        $data = (new SettingEnvService)->saveSettingByCodeUseLock($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 校验一些存在互斥关系的配置
     * @param $params
     * @throws \App\Library\Exception\BusinessException
     */
    public function filter($params)
    {
        if (empty($params['set_val'])) {
            return;
        }
        $service = new SettingEnvService();
        if (in_array($params['code'],
            array_keys(BikeCompensationCoefficientService::$bccDifficultMap)
        )) {
            $service->specialFormatFilterIntersectV1($params['code'], $params['set_val'],
                array_keys(BikeCompensationCoefficientService::$bccDifficultMap));
        }
        if (in_array($params['code'],
            array_keys(BikeCompensationCoefficientService::$bccTopMap)
        )) {
            $service->specialFormatFilterIntersectV1($params['code'], $params['set_val'],
                array_keys(BikeCompensationCoefficientService::$bccTopMap));
        }
        $bccTopMapAndSuratAndSukAndUnstable = array_merge(
            array_keys(BikeCompensationCoefficientService::$bccTopMap),
            array_keys(BikeCompensationCoefficientService::$bccSuratAndSuk),
            array_keys(BikeCompensationCoefficientService::$bccUnstableStore),
            array_keys(BikeCompensationCoefficientService::$attendanceOffsetStore),
            array_keys(BikeCompensationCoefficientService::$bccOverloadStore));
        if (in_array($params['code'],
            $bccTopMapAndSuratAndSukAndUnstable
        )) {
            $service->specialFormatFilterIntersectV1($params['code'], $params['set_val'],
                $bccTopMapAndSuratAndSukAndUnstable);
        }
        if (in_array($params['code'],
            array_keys(PdService::$pdTopMap)
        )) {
            $service->specialFormatFilterIntersectV1($params['code'], $params['set_val'],
                array_keys(PdService::$pdTopMap));
        }
        if (in_array($params['code'], CarCompensationCoefficientService::$envConfigMap)) {
            $service->specialFormatFilterIntersectV1($params['code'], $params['set_val'],
                CarCompensationCoefficientService::$envConfigMap);
        }
        if (in_array($params['code'],
            StaffExperienceAllowanceV2Service::$settingConfig
        )) {
            $service->specialFormatFilterIntersectV1($params['code'], $params['set_val'],
                StaffExperienceAllowanceV2Service::$settingConfig, false);
        }
        if (in_array($params['code'], [
            'remote_tourism_store',
            'ccc_bsp_store',
            'bcc_unstable_store',
            'DC_from_BDC_store',
        ])) {
            $service->specialFormatSelfRepeatV1($params['set_val']);
        }
        $service->checkIsNumeric($params['code'], $params['set_val']);
    }
    /**
     * @Permission(action='admin.settingenv.versionlist')
     */
    public function version_listAction()
    {
        $params = $this->request->get();
        $data = (new SettingEnvService())->getVersionList($params);
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
    }

    /**
     * 保存
     * @Permission(action='admin.settingenv.versionsave')
     */
    public function version_saveAction()
    {
        $params = $this->request->get();
        Validation::validate((array)$params, SettingEnvService::$s_v_param);
        $this->logger->info(['params'=>$params,'action_staff'=>$this->user['id']]);
        $params['staff_id'] =  $this->user['id'];
        $data = (new SettingEnvService)->saveSettingByCode($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * 任务列表
     * @Token
     */
    public function xxljoblistAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params'=>$params]);
        $data = (new XxljobTaskServer())->JobTaskList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', []);
    }

    /**
     * 任务保存
     * @Token
     */
    public function xxljobsaveAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params'=>$params]);
        $validation = [
            'regtask'=> 'Required|StrLenGeLe:1,100',
            'drive' => 'Required|StrLenGeLe:1,200',
            'task_path' => 'Required|StrLenGeLe:1,200',
            'remark' => 'Required|StrLenGeLe:1,200',
        ];
        Validation::validate((array)$params, $validation);
        $data = (new XxljobTaskServer())->JobTaskSave($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


    /**
     * @Permission(action='admin.settingenv.covid')
     * 针对 hr 配置越南新冠假期申请的 env 的名单
     */
    public function covidListAction()
    {
        $server = new SettingEnvService();
        $params['code'] = array('leave_25_list_vn','leave_26_list_vn');
        $list = $server->listByParam($params);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    /**
     * @Permission(action='admin.settingenv.covid')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function covidSettingAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params'=>$params,'action_staff'=>$this->user['id']]);
        Validation::validate((array)$params, SettingEnvService::$s_v_param);

        if($params['set_val'] && !preg_match('/^[1-9]+[0-9,]*[0-9]*$/',$params['set_val'])){
            //throw new ValidationException('set_val数据格式错误，设置内容只能包含数字或英文逗号且只能数字开头和结尾');
        }
        //env 配置
        $params['staff_id'] =  $this->user['id'];
        $data = (new SettingEnvService)->saveSettingByCode($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }

    /**
     * @Permission(action='admin.settingenv.prove-download-setting')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function proveDownloadSettingAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params'=>$params,'action_staff'=>$this->user['id']]);
        Validation::validate((array)$params, []);

        //env 配置
        $params['staff_id'] =  $this->user['id'];
        $data = (new SettingEnvService)->getSetVal('prove-download-setting');
        $res = $data ? json_decode($data,true):['name'=>'','job_title'=>'','url'=>''];
        return $this->returnJson(ErrCode::SUCCESS, 'success', $res);
    }

    /**
     * @Permission(action='admin.settingenv.prove-download-setting-save')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function proveDownloadSettingSaveAction()
    {
        $params = $this->request->get();
        $this->logger->info(['params'=>$params,'action_staff'=>$this->user['id']]);
        Validation::validate((array)$params, ['name' => 'Required|StrLenGe:1', 'job_title' => 'Required|StrLenGe:1','url'=>'Required|StrLenGe:1']);

        $params['staff_id'] =  $this->user['id'];
        $params['code']     = 'prove-download-setting';
        $params['set_val']  = json_encode(['name'=>$params['name'],'job_title'=>$params['job_title'],'url'=>$params['url']]);

        $data = (new SettingEnvService)->saveSettingByCode($params);

        return $this->returnJson(ErrCode::SUCCESS, 'success', $data);
    }


}
