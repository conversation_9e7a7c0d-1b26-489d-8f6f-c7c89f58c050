<!doctype HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <style>
        body {
            font: 3.2mm/1.2 Tahoma, sans-serif;
            color: #000;
            -webkit-print-color-adjust: exact;
        }

        body, ul, p {
            margin: 0;
            padding: 0;
        }

        ul, li {
            list-style: none;
        }

        .clearfix::after {
            display: block;
            content: "";
            clear: both;
        }

        .break-after {
            page-break-after: always;
            page-break-inside: avoid;
        }

        .tal {
            text-align: left !important;
        }

        .table {
            width: 100%;
            border-left: .5mm solid #666;
            border-right: .5mm solid #666;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .table th, .table td {
            border: .3mm solid #999;
            padding: 2mm;
            text-align: center;
            word-break: break-all;
        }

        .table .bt {
            border-top: .5mm solid #666;
        }

        .table .bbn {
            border-bottom: none;
        }

        .table .cell-title {
            font-weight: 600;
            text-align: left;
        }

        .borrow-wrap {
            width: 210mm;
            height: 297mm;
            padding: 4mm;
            margin-bottom: 4mm;
            box-sizing: border-box;
        }

        .checkbox-item {
            display: inline-block;
            width: 2.5mm;
            height: 2.5mm;
            margin: 0 3mm;
            border: .3mm solid #999;
            vertical-align: bottom;
        }

        .checkbox-item.selected {
            background-color: #000;
            border-color: #000;
        }

        .signature {
            margin-top: 5mm;
            padding-left: 60%;
        }
    </style>
</head>

<body>
<div class="borrow-wrap">
    <table class="table">
        <tr style="font-size:3.4mm; font-weight:600;">
            <td colspan="2" class="tal bt">Employee loan application form</td>
            <td class="bt">Loan number</td>
            <td class="bt"><?php echo $lno ?></td>
        </tr>
        <tr>
            <td class="bt">Title</td>
            <td colspan="3" class="bt"><?php echo $lname ?></td>
        </tr>
        <tr>
            <td style="width:20%;">Applicant's name</td>
            <td style="width:30%;"><?php echo $create_name ?></td>
            <td style="width:20%;">Applicant's Employee ID</td>
            <td style="width:30%;"><?php echo $create_id ?></td>
        </tr>
        <tr>
            <td>Applicant's number</td>
            <td><?php echo $create_phone ?></td>
            <td>Applicant's mailbox</td>
            <td><?php echo $create_email ?></td>
        </tr>
        <tr>
            <td>Applicant's company</td>
            <td><?php echo $create_company_name ?></td>
            <td>The starting time</td>
            <td><?php echo $create_date ?></td>
        </tr>
        <tr>
            <td>Applicant's department</td>
            <td><?php echo $create_display_department_name ?></td>
            <td>Applicant's position</td>
            <td><?php echo $create_job_title_name ?></td>
        </tr>
        <tr>
            <td>Cost center</td>
            <td colspan="3" class="bt"><?php echo $cost_center_name ?></td>
        </tr>
        <tr>
            <td>Reasons for borrowing</td>
            <td colspan="3" class="bt">
                <?php
                $typeArr = ["","Travel expenses","Purchase","Long-term abroad","Official activities","Other"];
                echo $typeArr[$type];
                ?>
            </td>
        </tr>
        <tr>
            <td>付款方式</td>
            <td>
                <?php
                $payTypeArr =["","Cash","Bank Transfer"];
                echo $payTypeArr[$pay_type];
                ?>
            </td>
            <td>币种</td>
            <td>
                <?php
                $currencyArr=["","THB","USD"];
                echo $currencyArr[$currency];
                ?>
            </td>
        </tr>
    </table>

    <table class="table">
        <?php
            if($pay_type==2){
                echo '
                    <tr>
                        <td colspan="6" class="cell-title bt">Bank information</td>
                    </tr>
                    <tr>
                        <td style="width:15%;">Beneficiary account name</td>
                        <td style="width:20%;">'.$bank["name"].'</td>
                        <td style="width:15%;">Beneficiary account</td>
                        <td style="width:20%;">'.$bank["account"].'</td>
                        <td style="width:15%;">Beneficiary Bank</td>
                        <td style="width:15%;">'.$bank["bank_type"].'</td>
                    </tr>';
            }
            ?>
        <tr>
            <td colspan="6" class="cell-title bt">Item description</td>
        </tr>
        <tr>
            <td>Item name</td>
            <td colspan="2"><?php echo $event_name ?></td>
            <td>Amount</td>
            <td colspan="2"><?php echo $amount ?></td>
        </tr>
        <tr>
            <td>Expected completion time</td>
            <td colspan="2"><?php echo $finished_at ?></td>
            <td>Estimated repayment time</td>
            <td colspan="2"><?php echo $back_at ?></td>
        </tr>
        <tr>
            <td>Item usage instructions and estimation process</td>
            <td colspan="5"><?php echo $event_info ?></td>
        </tr>
        <?php
            if(!empty($travel)){
                echo
                '<tr>
                    <td>《Travel Application Form》</td>
                    <td colspan="5">'.$travel['serial_no'].'</td>
                </tr>';
            }
        ?>
        <?php

        if(!empty($pay))
        {

            $payTypeArr = ["","Cash","TMB","SCB"];

            $signTypeArr= ["","Yes","No"];


        echo '
        <tr>
            <td colspan="6" class="cell-title bt">Payment Information</td>
        </tr>
        <tr>
            <td>Payment Method</td>
            <td colspan="5" class="tal">
                '.$payTypeArr[$pay['pay_type']].'
            </td>
        </tr>
        <tr>
            <td>Payment Bank</td>
            <td>'.$pay['pay_bank_name'].'</td>
            <td>Payment Account</td>
            <td>'.$pay['pay_bank_account'].'</td>
            <td>Payment Date</td>
            <td>'.$pay['pay_date'].'</td>
        </tr>
        <tr>
            <td>Whether the borrower signs</td>
            <td>
                '.$signTypeArr[$pay['is_sign']].'
            </td>
            <td>Signatory</td>
            <td>'.$pay['sign_name'].'</td>
            <td>Sign Date</td>
            <td>'.$pay['sign_date'].'</td>
        </tr>
        <tr>
            <td class="bbn">Remarks</td>
            <td colspan="5" class="bbn">'.$pay['mark'].'</td>
        </tr>';
        }?>
    </table>

    <table class="table" style="border-bottom:.5mm solid #666;">
        <tr>
            <td colspan="6" class="cell-title bt">Approval Process</td>
        </tr>
        <tr>
            <td style="width:15%;">Step Name</td>
            <td style="width:20%;">Complete Time</td>
            <td style="width:15%;">Process Labor Employee ID</td>
            <td style="width:20%;">Process Labor name</td>
            <td style="width:15%;">Process Result</td>
            <td style="width:15%;">Handling Opinions</td>
        </tr>
        <?php
        if(!empty($auth_logs)){

            $auth_logs = array_reverse($auth_logs);
            foreach ($auth_logs as $k=>$item){
                echo '
                         <tr>
                            <td>Approver'.($k+1).'</td>
                            <td>'.$item['audit_at_datetime'].'</td>
                            <td>'.$item['staff_id'].'</td>
                            <td>'.$item['staff_name'].'</td>
                            <td>'.$item['action_name'].'</td>
                            <td>'.$item['info'].'</td>
                        </tr>
                    ';
            }
        }
        ?>
        <tr>
            <td>Whether to sign a guarantee</td>
            <td colspan="5" class="tal">
                Yes
            </td>
        </tr>
        <tr>
            <td colspan="6" class="cell-title bt" style="text-align:center;">Guarantee</td>
        </tr>
        <tr>
            <td colspan="6" class="tal" style="line-height:1.6;">
                <p>1.借款人员同意公司在其本人不能按时偿还借款时，有权从其个人工资、福利或奖金收入中扣还，若申请人本人工资、福利或奖金收入金额不够抵扣，将继续扣除申请人上级领导人工资、福利或者奖金收入，依此类推；</p>
                <p> 1.หากผู้กู้ไม่สามารถชำระคืนเงินกู้ตรงเวลา บริษัท มีสิทธิ์หักจากค่าจ้าง เงินเดือน
                    รายได้หรือโบนัสหากผู้เบิกเงินมีรายได้ไม่พอหัก หัวหน้าผู้บังคับบัญชาจะต้องถูกหักเงินค่าจ้าง เงินเดือน
                    รายได้หรือโบนัสแทนผู้เบิกเงิน</p>
                <p>2.同意按照借款总额0.05%的日利息支付因拖欠还款导致的公司利息损失；</p>
                <p>2.พนักงานยินยอมจะชำระดอกเบี้ยหากเลยกำหนดเวลา</p>
                <p> 3.保证不将公司借款用于私人目的；</p>
                <p> 3.ต้องรับรองว่าเงินสำรองที่เบิกไปนั้นไม่ได้ไปใช้ในกิจธุระส่วนตัว</p>
                <p>4. 保证随时接受公司财务部门的核查；</p>
                <p>4. ต้องยินยอมให้แผนกบัญชีตรวจสอบเงินสำรองที่เบิกไปทุกเวลาที่ต้องการตรวจสอบ</p>
                <p>5.保证不论由于何种原因岗位调整或离职，均承诺在规定时间内向公司还清借款。</p>
                <p> 5. เพื่อรับรองว่าการลาออกไม่ว่าเหตุผลใดๆก็ตาม จะคืนเงินบริษัทภายในกำหนด</p>
                <div class="signature">
                    <span>Guarantor's signature:</span>
                    <span><?php echo $create_name ?></span>
                </div>
            </td>
        </tr>
    </table>
</div>
</body>
</html>