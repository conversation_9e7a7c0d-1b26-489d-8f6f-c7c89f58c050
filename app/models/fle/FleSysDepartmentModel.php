<?php


namespace App\Models\fle;


class FleSysDepartmentModel extends FleBaseModel
{
    protected $table_name = 'sys_department';
    public function getSource()
    {
        return $this->table_name;
    }

    /**
     * 获取network及其子部门
     */
    public function getNetworkDepartmentIds(): array
    {
        return self::getDepartmentIdsList("999/222/1/4");
    }

    /**
     * 获取shop及其子部门
     */
    public function getShopDepartmentIds(): array
    {
        return self::getDepartmentIdsList("999/222/1/13");
    }

    /**
     * 获取hubb及其子部门
     */
    public function getHubDepartmentIds(): array
    {
        return self::getDepartmentIdsList("999/222/1/25");
    }

    /**
     * 根据部门链获取部门及其子部门
     * @param $chain
     * @return array
     */
    private function getDepartmentIdsList($chain): array
    {
        $departmentIdsArr = $this->find([
            'conditions' => 'ancestry_v3 like :chain: or ancestry_v3 = :chain_id:',
            'bind'       => [
                'chain'     => $chain . '/%',
                'chain_id'  => $chain
            ],
            'columns'    => 'id'
        ])->toArray();
        return array_column($departmentIdsArr, 'id') ?? [];
    }
}