<?php
/**
 * Author: Bruce
 * Date  : 2025-04-28 12:35
 * Description:
 */

namespace App\Models\backyard;


class VehicleInspectionModel extends BackyardBaseModel
{
    protected $table_name = 'vehicle_inspection';

    //车辆视频审核状态
    const VIDEO_STATUS_PENDING = 1;//1待审核
    const VIDEO_STATUS_PASS = 2;//2清晰，通过
    const VIDEO_STATUS_REJECT = 3;//3不清晰，驳回
    public static $video_status_text = [
        self::VIDEO_STATUS_PENDING => 'approve_state_2',
        self::VIDEO_STATUS_PASS => 'video_status_pass',
        self::VIDEO_STATUS_REJECT => 'video_status_reject',
    ];

    //车牌审核结果
    const PLATE_STATUS_SAME = 1;//1与系统记录一致
    const PLATE_STATUS_NOT_SAME = 2;//2与系统记录不一致
    public static $plate_status_text = [
        self::PLATE_STATUS_SAME => 'plate_status_same',
        self::PLATE_STATUS_NOT_SAME => 'plate_status_not_same',
    ];

    //里程表审核结果
    const MILEAGE_STATUS_1 = 1;//1与上次记录不超过500
    const MILEAGE_STATUS_2 = 2;//2与上次记录超过500
    public static $mileage_status_text = [
        self::MILEAGE_STATUS_1 => 'mileage_status_not_beyond',//与上次记录不超过500
        self::MILEAGE_STATUS_2 => 'mileage_status_beyond',//与上次记录超过500
    ];

    //车厢审核
    const CARRIAGE_TYPE_1 = 1;//有车厢
    const CARRIAGE_TYPE_2 = 2;//无车厢
    public static $carriage_type_text = [
        self::CARRIAGE_TYPE_1 => 'have_container',
        self::CARRIAGE_TYPE_2 => 'have_no_container',
    ];


}