<?php
/**
 * Author: Bruce
 * Date  : 2024-12-26 20:30
 * Description:
 */

namespace App\Models\backyard;


class SuspensionAuditModel extends BackyardBaseModel
{
    protected $table_name = 'suspension_audit';

    //处理状态
    const STATUS_PENDING  = 1;//待审批
    const STATUS_AGREE    = 2;//已同意
    const STATUS_REJECT   = 3;//已驳回
    const STATUS_CANCEL   = 4;//已撤销
    const STATUS_APPROVAL = 5;//审批中
    const STATUS_TIME_OUT = 6;//超时关闭

    //处理状态 翻译
    public static $fix_status_text = [
        self::STATUS_PENDING  => 'audit_status.1',
        self::STATUS_APPROVAL => 'suspension_audit_pending',
        self::STATUS_AGREE    => 'audit_status.2',
        self::STATUS_REJECT   => 'audit_status.3',
        self::STATUS_CANCEL   => 'audit_status.4',
        self::STATUS_TIME_OUT => 'audit_status.5',
    ];

    const NO_NEED_AUDIT = 0;

    const SEND_STATUS_0 = 0;//未发送
    const SEND_STATUS_1 = 1;//已发送
    const SEND_STATUS_2 = 2;//发送失败，邮箱为空
    const SEND_STATUS_3 = 3;//发送失败，邮箱异常
    const SEND_STATUS_4 = 4;//发送失败，未找到签字人信息
    const SEND_STATUS_5 = 5;//停职离职 无需发送
    const SEND_STATUS_6 = 6;//pdf 生成失败，提示 系统异常
}