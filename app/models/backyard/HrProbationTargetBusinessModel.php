<?php

namespace App\Models\backyard;

/**
 * 转正评估-目标业务签字流程
 */
class HrProbationTargetBusinessModel extends BackyardBaseModel
{
    protected $table_name = 'hr_probation_target_business';

    const STATE_NORMAL = 1;
    const STATE_CANCEL = 2;

    const STAFF_SIGN_STATE_SUCCESS = 1;
    const STAFF_SIGN_STATE_WAIT = 2;
    const STAFF_SIGN_STATE_REJECT = 3;

    public static $staffSignStateList = [
        self::STAFF_SIGN_STATE_SUCCESS => 'probation_target_business_sign_state_success',
        self::STAFF_SIGN_STATE_WAIT    => 'probation_target_business_sign_state_wait',
        self::STAFF_SIGN_STATE_REJECT  => 'probation_target_business_sign_state_reject',
    ];


    const MANAGER_SIGN_STATE_SUCCESS = 1;
    const MANAGER_SIGN_STATE_WAIT = 2;
    const MANAGER_SIGN_STATE_REJECT = 3;

    public static $managerSignStateList = [
        self::MANAGER_SIGN_STATE_SUCCESS => 'probation_target_business_sign_state_success',
        self::MANAGER_SIGN_STATE_WAIT    => 'probation_target_business_sign_state_wait',
        self::MANAGER_SIGN_STATE_REJECT  => 'probation_target_business_sign_state_reject',
    ];
}