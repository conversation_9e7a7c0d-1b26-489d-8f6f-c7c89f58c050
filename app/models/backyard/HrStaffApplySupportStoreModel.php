<?php

namespace App\Models\backyard;


class HrStaffApplySupportStoreModel extends BackyardBaseModel
{
    const STATUS_STAY = 1;//待审核
    const STATUS_PASS = 2;//审核通过

    // 交通方式
    const TRANSPORTATION_MODE_1 = 1;// 运兵车
    const TRANSPORTATION_MODE_2 = 2;// 小黄车
    const TRANSPORTATION_MODE_3 = 3;// 骑摩托车
    const TRANSPORTATION_MODE_4 = 4;// 公共交通
    const TRANSPORTATION_MODE_5 = 5;// Grab
    const TRANSPORTATION_MODE_6 = 6;// 其他

    public static $transportation_mode_item = [
        HrStaffApplySupportStoreModel::TRANSPORTATION_MODE_1,
        HrStaffApplySupportStoreModel::TRANSPORTATION_MODE_2,
        HrStaffApplySupportStoreModel::TRANSPORTATION_MODE_3,
        HrStaffApplySupportStoreModel::TRANSPORTATION_MODE_4,
        HrStaffApplySupportStoreModel::TRANSPORTATION_MODE_5,
        HrStaffApplySupportStoreModel::TRANSPORTATION_MODE_6,
    ];

    // 是否住宿
    const IS_STAY_NO = 'No';
    const IS_STAY_YES = 'Yes';

    const IS_STAY_NO_ENUMS = 0;
    const IS_STAY_YES_ENUMS = 1;

    //数据来源
    const DATA_SOURCE_1 = 1;// BY申请支援
    const DATA_SOURCE_2 = 2;// HCM批量导入支援'
    protected $table_name = 'hr_staff_apply_support_store';
}