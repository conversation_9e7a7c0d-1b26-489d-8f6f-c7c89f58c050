<?php
/**
 * Author: Bruce
 * Date  : 2023-02-20 17:43
 * Description:
 */

namespace App\Models\backyard;


class RolesModel extends BackyardBaseModel
{
    protected $table_name = 'roles';

    const ROLES_STORE_ASSIGNER = 0;//网点分配员
    const ROLES_COURIER = 1;//收派员 快递员
    const ROLES_STORE_KEEPER = 2;//仓管员
    const ROLES_STORE_MANAGER = 3;// 网点经理
    const ROLES_STORE_CASHIER = 4;//网点出纳
    const ROLES_CUSTOMER_SERVICE_AGENT = 5;//客服
    const ROLES_CUSTOMER_SERVICE_MANAGER = 6;//客服经理
    const ROLES_OPERATION_OFFICER = 7;//运营
    const ROLES_OPERATION_MANAGER = 8;//运营经理
    const ROLES_HQ_CASHIER = 9;//总部出纳
    const ROLES_HQ_ACCOUNTANT = 10;//总部会计
    const ROLES_CFO = 11;//财务总监
    const ROLES_SALESMAN = 12;//业务员
    const ROLES_SALES_MANAGER = 13;//销售经理
    const ROLES_SYSTEM_ADMIN = 14;//系统管理员
    const ROLES_CEO = 15;//总经理
    const ROLES_PERSONNEL_COMMISSIONER = 16;//人事专员
    const ROLES_PERSONNEL_MANAGER = 17;//人事经理
    const ROLES_STORE_SUPERVISOR = 18;// 网点主管
    const ROLES_SALES_DIRECTOR = 19;//销售总监
    const ROLES_FRANCHISE_MANAGER = 20;// 加盟商管理员
    const ROLES_REGIONAL_MANAGER = 21;// 区域经理
    const ROLES_DATA_ANALYST = 22;// 数据分析员
    const ROLES_AREA_STORE_MANAGER = 23;//区域网点经理 虚拟账号专属角色
    const ROLES_CENTRAL_CONTROL_DISPATCHING_OFFICER = 26;//汽运中控专员; 原值：LINE_HAUL_OFFICER；LH Officer汽运专员
    const ROLES_CENTRAL_CONTROL_DISPATCHING_MANAGER = 27;//汽运中控经理; 原值：LINE_HAUL_MANAGER；LH Manager 汽运经理
    const ROLES_CUSTOMER_SERVICE_DIRECTOR = 28;//CS Director  客服总监
    const ROLES_CUSTOMER_SERVICE_SUPERVISOR = 29;//CS  Manager 客服主管
    const ROLES_SECURITY_MANAGER = 30;//安全监督员
    const ROLES_TRANSPORTATION_PLANNING_OFFICER = 31;//汽运规划专员; 原值：LH_CONTROLLER；中控审批人员
    const ROLES_TRANSPORTATION_PLANNING_MANAGER = 32;//汽运规划经理; 原值：LH_PLANNING_MANAGER；线路规划管理员
    const ROLES_TRANSPORTATION_ACCOUNTING_OFFICER = 33;//汽运结算专员; 原值：LH_PRICE_MANAGER；汽运价格管理员
    const ROLES_INTERNATIONAL_FINANCE_MANAGER = 34;//国际财务经历
    const ROLES_INTERNATIONAL_HR_MANAGER = 35;//国际人力经理
    const ROLES_INTERNATIONAL_OPERATIONS_MANAGER = 36;//国际运营经理
    const ROLES_INTERNATIONAL_BUSINESS_DEVELOPMENT_MANAGER = 37;//国际业务发展经理
    const ROLES_INTERNATIONAL_ASSISTANT = 38;//国际助理
    const ROLES_CS_OFFICER_A = 39;//客服专员 -α
    const ROLES_TEMPORARY_VEHICLE_APPLICANT = 40;//加班车申请员
    const ROLES_HRIS_MANAGER = 41;//HRIS管理员
    const ROLES_PAYROLL_MANAGER = 42;//薪酬管理员
    const ROLES_HR_OFFICER = 43;//人力资源
    const ROLES_KA_MANAGER = 45;//ka 经理
    const ROLES_KA_SPECIALIST = 46;//ka 主管
    const ROLES_KA_OFFICER = 47;//ka 专员
    const ROLES_INFORMATION_MAINTENANCE = 48;//信息管理员
    const ROLES_HRIS_SALESMAN = 51;//HRIS业务员
    const ROLES_QAQC_SPECIALIST = 52;//QAQC专员
    const ROLES_TRANSPORTATION_ACCOUNTING_MANAGER = 54;//汽运结算经理
    const ROLES_NETWORK_DIRECTOR = 55;//网络总监
    const ROLES_AREA_MANAGER = 56;//大区经理
    const ROLES_DISTRICT_MANAGER = 57;//片区经理
    const ROLES_DISTRIBUTION_OFFICER = 58;//分拨操作员
    const ROLES_DISTRIBUTION_SPECIALIST = 59;//分拨管理员
    const ROLES_DISTRIBUTION_MANAGER = 60;//分拨经理
    const ROLES_HUB_CS = 61;//HUB CS
    const ROLES_OPERATION_DIRECTOR = 62;//运营总监
    const ROLES_OPERATION_SPECIALIST = 63;//运营主管
    const ROLES_TRANSPORTTATION_PROCUREMENT_OFFICER = 64;//汽运采购专员
    const ROLES_TRANSPORTATION_PROCUREMENT_MANAGER = 65;//汽运采购经理
    const ROLES_IT_SECURITY = 66;//网络安全工程师
    const ROLES_IT_SUPPORT = 67;//运维工程师
    const ROLES_HR_BP = 68;//HRBP
    const ROLES_KA_SALESMAN = 69;//
    const ROLES_REGIONAL_MANAGER_A = 70;// 区域经理 -α
    const ROLES_ONSITE_MANAGER = 71;//Onsite Manager
    const ROLES_ONSITE_SUPERVISOR = 72;//Onsite supervisor
    const ROLES_FRANCHISEE_DEFAULT = 73;//加盟商默认角色
    const ROLES_FRANCHISEE_CS_MANAGER = 74;//加盟商客服经理
    const ROLES_FRANCHISEE_OPERATION_OFFICER = 75;//加盟商客服经理
    const ROLES_FRANCHISEE_OPERATION_MANAGER = 76;//加盟商运营经理
    const ROLES_HR_SERVICE = 77;//加盟商运营经理
    const ROLES_EXPAT_CENTER = 78;//EXPAT_CENTER
    const ROLES_HRD = 79;//HRD
    const ROLES_ER = 80;//ER
    const ROLES_TALENT_ACQUISITION = 81;//TALENT_ACQUISITION
    const ROLES_KA_DIRECTOR = 82;//KA 总监
    const ROLES_SHARED_SERVICES = 83;//公共支持
    const ROLES_HUB_AREA_ADMINISTRATOR = 84;//分拨区域管理员
    const ROLES_HEAD_OF_CASHIER = 85;//总部出纳主管
    const ROLES_HEAD_OF_ACCOUNTING = 86;//总部会计主管
    const ROLES_FRANCHISEE_QUALITY_CONTROL = 87;//加盟商质控
    const ROLES_HUB_OPERATION_OFFICER = 88;//分拨运营专员
    const ROLES_HUB_OPERATION_SPECIALIST = 89;//分拨运营主管
    const ROLES_HUB_OPERATION_MANAGER = 90;//分拨运营经理
    const ROLES_NETWORK_QC = 91;//网络QC
    const ROLES_USER_OPERATIONS = 92;//网络客户
    const ROLES_NETWORK_OPERATIONS = 93;//网络运营
    const ROLES_NETWORK_PLANNING = 94;//网络规划
    const ROLES_NETWORK_SUPPORT = 95;//网络支持
    const ROLES_NETWORK_TRAINING = 96;//网络培训
    const ROLES_DATA_AUDIT = 97;//数据审查
    const ROLES_SUPER_ADMIN = 99;//超级管理员
    const ROLES_HUB_DIRECTOR = 100;//Hub总监
    const ROLES_HUB_PLANNING = 101;//Hub规划
    const ROLES_HUB_TRAINING = 102;//Hub培训
    const ROLES_HUB_SUPPORT = 103;//Hub支持
    const ROLES_HUB_SUPERVISOR = 104;//分拨主管
    const ROLES_HUB_INDUSTRIAL_ENGINEERING = 105;//分拨IE
    const ROLES_HUB_QAQC = 106;//分拨QC
    const ROLES_HUB_OPERATIONS_STANDARDIZATION = 107;//分拨操作标准化
    const ROLES_FUEL_CARD_ADMIN = 108;//油卡管理员
    const ROLES_FLEXIBLE_COURIER = 109;//机动快递员
    const ROLES_NETWORK_ACCOUNT_MANAGER = 110;//网络客户经理
    const ROLES_NETWORK_ACCOUNT_SUPERVISOR = 111;//网络客户主管
    const ROLES_CUSTOMER_SERVICE_FOR_PERFECTING_RESUMES = 112;//完善简历客服
    const ROLES_CUSTOMER_SERVICE_FOR_REVIEWING_RESUMES = 113;//审核简历客服
    const ROLES_PICK_UP_KEEPER = 114;//接件员
    const ROLES_HR_SYSTEM_ADMIN = 115;//HR系统管理员
    const ROLES_FEEDER_A = 116;//
    const ROLES_FEEDER_B = 117;//
    const ROLES_FRANCHISEE_INFORMATION_MANAGER = 118;//加盟商信息管理员
    const ROLES_FRANCHISEE_SALES = 119;//加盟商销售
    const ROLES_FRANCHISEE_FINANCE = 120;//加盟商财务
    const ROLES_TALENT_ACQUISITION_NW = 121;//网络TA
    const ROLES_LOSS_PREVENTION_INSPECTOR = 122;//防损稽查员
    const ROLES_UNAUTHORIZED_ROLE = 123;//无权限角色
    const ROLES_BU_RECRUITER = 124;// BU Recruiter
    const ROLES_HR_GENERALIST = 125;//HR Generalist
    const ROLES_HUB_AREA_MANAGER = 126;//HUB Area Manager
    const ROLES_CALLBACK_OFFICER = 127;//回访专员
    const ROLES_HUB_ATTENDANCE_OFFICER = 128;//hub考勤员


    //员工班次设置超管
    // 超级管理员、人事管理员、人事专员、人力资源、薪酬管理员、HRIS管理员、系统管理员、QAQC专员、安全监督员（所有员工）
    public static $staffShiftSuperPermissions = [
        self::ROLES_SUPER_ADMIN,
        self::ROLES_PERSONNEL_MANAGER,
        self::ROLES_PERSONNEL_COMMISSIONER,
        self::ROLES_HR_OFFICER,
        self::ROLES_PAYROLL_MANAGER,
        self::ROLES_HRIS_MANAGER,
        self::ROLES_SYSTEM_ADMIN,
        self::ROLES_QAQC_SPECIALIST,
        self::ROLES_SECURITY_MANAGER,
    ];

    //员工班次设置HR hrbp  hr server
    public static $staffShiftHr = [
        self::ROLES_HR_BP,
        self::ROLES_HR_SERVICE,
        self::ROLES_STORE_SUPERVISOR,
        self::ROLES_CENTRAL_CONTROL_DISPATCHING_OFFICER,
        self::ROLES_CENTRAL_CONTROL_DISPATCHING_MANAGER,
        self::ROLES_REGIONAL_MANAGER,
        self::ROLES_AREA_MANAGER,
        self::ROLES_DISTRICT_MANAGER,
    ];

    //员工班次设置 manager 区域经理 片区经理 大区经理
    public static $staffShiftManager = [
        self::ROLES_REGIONAL_MANAGER,
        self::ROLES_AREA_MANAGER,
        self::ROLES_DISTRICT_MANAGER,
    ];

    //员工班次设置 store 网点经理 网点主管
    public static $staffShiftStoreManager = [
        self::ROLES_STORE_MANAGER,
        self::ROLES_STORE_SUPERVISOR,
    ];

    // FLASH BOX 投诉跟进 超管角色
    // 超级管理员、人事经理(HR Management),HRIS管理员、HR系统管理员
    public static $staffCeoSuperPermissions = [
        self::ROLES_SUPER_ADMIN,
        self::ROLES_PERSONNEL_MANAGER,
        self::ROLES_HRIS_MANAGER,
        self::ROLES_HR_SYSTEM_ADMIN,
    ];

    //FLASH BOX 有数据权限的角色
    //超级管理员、人事经理(HR Management),HRIS管理员、HR系统管理员 HRBP
    public static $staffCeoDataPermissions = [
        self::ROLES_SUPER_ADMIN,
        self::ROLES_PERSONNEL_MANAGER,
        self::ROLES_HRIS_MANAGER,
        self::ROLES_HR_SYSTEM_ADMIN,
        self::ROLES_HR_BP,
    ];

    //需要走 管辖范围的 权限
    public static $staffCeoDataLimitPermissions = [
        self::ROLES_HR_BP,
    ];




    const ROLE_99 = 99;
    const ROLE_17 = 17;
    const ROLE_16 = 16;
    const ROLE_43 = 43;
    const ROLE_42 = 42;
    const ROLE_41 = 41;
    const ROLE_14 = 14;
    const ROLE_52 = 52;
    const ROLE_30 = 30;
    const ROLE_8 = 8;
    const ROLE_63 = 63;//运营主管

    const ROLES_OUTLET_SUPERVISOR_ID = 18;//网点主管

    const SUPPER_ADMIN_ROLE = 99;//隐藏的角色
    const UNAUTHORIZED_ROLE = 123;//不给权限的角色
    const FEEDER_A = 116;//Feeder A 角色ID
    const FEEDER_B = 117;//Feeder B 角色ID
    const ROLE_DEFAULT = 98;//默认角色, 没有任何权限
}