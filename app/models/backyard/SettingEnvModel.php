<?php

namespace App\Models\backyard;

class SettingEnvModel extends BackyardBaseModel
{
    protected $table_name = 'setting_env';

    const CATEGORY_WIN_HR = 1;
    const CATEGORY_Backyard = 2;
    const CATEGORY_HRIS = 3;
    const CATEGORY_CRM = 4;
    const CATEGORY_FBI = 5;
    const CATEGORY_SYS = 6;
    const CATEGORY_OA = 7;

    public static $category_list = [
        self::CATEGORY_WIN_HR => 'WinHR',
        self::CATEGORY_Backyard => 'Backyard',
        self::CATEGORY_HRIS => 'HRIS',
        self::CATEGORY_CRM => 'CRM',
        self::CATEGORY_FBI => 'FBI',
        self::CATEGORY_SYS => 'SYS',
        self::CATEGORY_OA => 'OA',
    ];


    /**
     * 获取单个配置项
     * @param $code
     * @return string
     */
    public static function get_val($code)
    {
        $info = self::findFirst("code = '{$code}'");
        if (empty($info)) {
            return '';
        }
        return $info->set_val;
    }

    /**
     * 获取多个配置项
     * @param array $codes
     * @return array
     */
    public static function getMultiEnvByCode(array $codes = []): array
    {
        if (empty($codes) || !is_array($codes)) {
            return [];
        }

        $result = self::find([
            'conditions' => "code in ({codes:array})",
            'bind'       => ['codes' => $codes],
            'columns'    => ['set_val', 'code'],
        ])->toArray();
        return array_column($result, 'set_val', 'code');
    }
}

