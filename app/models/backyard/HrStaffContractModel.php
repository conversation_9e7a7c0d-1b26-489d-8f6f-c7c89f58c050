<?php
/**
 * Created by PhpStorm.
 * User: z<PERSON><PERSON>
 * Date: 2021/3/15
 * Time: 19:06
 */

namespace App\Models\backyard;


class HrStaffContractModel  extends BackyardBaseModel{

    protected $table_name = 'hr_staff_contract';

    //合同类型
    const CONTRACT_TYPE_LABOR_CONTRACT = 1;//劳动合同

    const IS_NOT_DELETE = 0;//未删除
    const CONTRACT_STATUS_WAIT = 10;//待添加
    const CONTRACT_STATUS_WAIT_SEND = 20;//待发送
    const CONTRACT_STATUS_WAIT_SIGN = 30;//待员工签署
    const CONTRACT_STATUS_UN_SIGN = 100;//未签字
    const CONTRACT_STATUS_FEEDBACK = 110;//反馈
    const CONTRACT_STATUS_REFUSE   = 120; //拒绝

    const CONTRACT_CHILD_TYPE_51 = 51;
    const CONTRACT_CHILD_TYPE_52 = 52;
    const CONTRACT_CHILD_LDHT_INDE = 131;//个人代理合同类型
    const CONTRACT_CHILD_LDHT_INDE_TH = 135;//个人代理合同类型

    const CONTRACT_STATUS_TO_BE_RENEWED = 80;//待续签合同
    const CONTRACT_STATUS_RDNEWED = 81;//续签合同
    const CONTRACT_STATUS_TAKE    = 85;//合同生效中
    const CONTRACT_STATUS_EXPIRED = 90;//已到期
    const CONTRACT_STATUS_ARCHIVED = 60;//已归档（生效中）

    //是否长期合同
    const CONTRACT_DATE_IS_LONG_YES = 1; //是
    const CONTRACT_DATE_IS_LONG_NO = 2; //否

    //合同是否删除
    const CONTRACT_DELETED_YES = 1;
    const CONTRACT_DELETED_NO = 0;

    //是否需要
    const IS_NEED_YES = 1;
    const IS_NEED_NO  = 2;

    const VERSION_LDHT_INDE_2 = 101;//合同版本：个人代理合同 2
}