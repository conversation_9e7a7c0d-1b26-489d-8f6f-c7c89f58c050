<?php


namespace App\Models\backyard;


/**
 * 异步导入
 * Class AsyncImportTaskModel
 * @package App\Models\backyard
 */
class AsyncImportTaskModel extends BackyardBaseModel
{
    protected $table_name = 'async_import_task';

    /**
     * 1 待执行 2 执行完成
     */
    const STATE_WAIT_EXECUTE = 1;
    const STATE_EXECUTED = 2;
    const IS_DELETED = 1;
    const IS_NOT_DELETED = 0;
    const SCHEDULING_SUGGESTION = 1;         //排班建议
    const BATCH_IMPORT_LEAVE = 2;            //批量导入调休
    const BATCH_IMPORT_COMPENSATORY_OFF = 3; //批量导入补休
    const BATCH_IMPORT_WORKDAY_ROLE = 4;     //轮休页面 导入 默认休息日和班次规则
    const BATCH_IMPORT_JOB_GRADE = 5;        //职级管理-批量调整职级
    const BATCH_IMPORT_INSTRUCTOR_REWARD = 10;        //快捷工具-辅导员管理

    const ATTENDANCE_IMPORT_LEAVE = 6;//考勤工具 导入请假
    const ATTENDANCE_IMPORT_OT = 7;//考勤工具 导入加班
    const ATTENDANCE_IMPORT_REISSUE = 8;//考勤工具 导入补卡
    const ATTENDANCE_IMPORT_FULL_PRISE = 9;//考勤工具 导入全勤奖配置
    const STAFF_FACE_BLACKLIST = 13;//人脸黑名单
    const SUSPENSION_AUDIT = 14;//PH 停职申请导入
    const STAFF_INFO_BATCH_UPDATE = 15;//员工信息批量更新
    const PAYROLL_FILE_IMPORT_2316 = 16;//2316文件导入
    const OUT_SOURCING_BLACKLIST_IMPORT = 17;//外协黑名单导入
    const CERTIFICATE_IMPORT_HOUR = 18;//21369【TH丨HCM丨薪酬】小时工仓管发送50tawi

    const SALARY_BASE_DATA = 11;//薪酬基础信息异步导入（不包含MY）
    const OUTSOURCING_ORDER = 12;//外协员工工作订单
    const WORKDAY_SETTING = 19;//轮休配置规则 导入
    const STAFF_INFO_BATCH_UPDATE_JOB = 20;//员工信息批量更新岗位
    const COURIER_VEHICLE_INSPECTION = 21;//快递员车辆稽查 导入

    public static $importTypeMap = [
        self::SCHEDULING_SUGGESTION,
        self::BATCH_IMPORT_LEAVE,
        self::BATCH_IMPORT_COMPENSATORY_OFF,
        self::BATCH_IMPORT_WORKDAY_ROLE,
        self::BATCH_IMPORT_JOB_GRADE,
        self::BATCH_IMPORT_INSTRUCTOR_REWARD, //快捷工具-辅导员管理
        self::ATTENDANCE_IMPORT_LEAVE,
        self::ATTENDANCE_IMPORT_OT,
        self::ATTENDANCE_IMPORT_REISSUE,
        self::SALARY_BASE_DATA,
        self::ATTENDANCE_IMPORT_FULL_PRISE,
        self::OUTSOURCING_ORDER,
        self::STAFF_FACE_BLACKLIST,
        self::SUSPENSION_AUDIT,
        self::STAFF_INFO_BATCH_UPDATE,
        self::PAYROLL_FILE_IMPORT_2316,
        self::OUT_SOURCING_BLACKLIST_IMPORT,
        self::CERTIFICATE_IMPORT_HOUR,
        self::WORKDAY_SETTING,
        self::STAFF_INFO_BATCH_UPDATE_JOB,
        self::COURIER_VEHICLE_INSPECTION,
    ];

    const STATUS_WAIT = 1;

    const BUSINESS_STATUS_WAIT = 1;//执行中
    const BUSINESS_STATUS_COMPLETE = 2;//执行完成
}