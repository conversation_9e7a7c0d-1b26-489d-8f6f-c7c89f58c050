<?php

namespace App\Library\Enums;


    final class StaffEnums
    {

        const STATUS_PROBATION = 1;//试用期
        const STATUS_PASS = 2;//已通过
        const STATUS_NOT_PASS = 3; //未通过
        const STATUS_FORMAL = 4;//已转正

        const JOB_TITLE_LEVEL_STAFF = 1;
        const JOB_TITLE_LEVEL_SUPER = 2;
        const JOB_TITLE_LEVEL_MANAGER = 3;
        const JOB_TITLE_LEVEL_EXECUTIVE = 4;
        /**
         * 职位等级
         */
        const JOB_TITLE_LEVEL_DESC = [
            self::JOB_TITLE_LEVEL_STAFF => 'Staff',
            self::JOB_TITLE_LEVEL_SUPER => 'Supervisor',
            self::JOB_TITLE_LEVEL_MANAGER => 'Manager',
            self::JOB_TITLE_LEVEL_EXECUTIVE => 'Executive',
        ];
        // 工作制天数
        const WEEK_WORKING_DAY_FIVE = 5;
        const WEEK_WORKING_DAY_SIX = 6;
        public static $week_working_days = [
            self::WEEK_WORKING_DAY_FIVE,
            self::WEEK_WORKING_DAY_SIX,
        ];

        //队列redis key: 员工职位部门变更，发送邮件
        const STAFF_CHANGE_SEND_EMAIL_LIST = 'staff_change_send_email';

        //队列redis key: 全局重置密码
        const STAFF_RESET_PASSWORD_ALL_LIST = 'staff_rest_password_all_list';

        //MY 停职 连续旷工发邮件
        const STAFF_SUSPENSION_SEND_EMAIL_LIST = 'staff_suspension_send_email_list';


        //组织类别 1 store 2 department
        const STAFF_INFO_ORGANIZATION_TYPE_STORE = 1;
        const STAFF_INFO_ORGANIZATION_TYPE_DEPARTMENT = 2;

        //驳回状态
        const STAFF_AUDIT_REJECT_STATUS = 3;

        const IS_NOT_DELETE = 0;//未删除
        const CONTRACT_STATUS_AUDIT = 40; //待复核
        const CONTRACT_STATUS_ARCHIVE = 50; //待归档
        const CONTRACT_STATUS_ARCHIVED = 60; //已归档
        const CONTRACT_STATUS_RESCIND = 70; //已解除
        const CONTRACT_STATUS_RDNEWAL = 80; //待续约
        const CONTRACT_STATUS_RDNEWED = 81; //已续约
        const CONTRACT_STATUS_TAKE = 85; //合同生效中，马来独有状态

        const CONTRACT_CHILD_TYPE_51 = 51;
        const CONTRACT_CHILD_TYPE_52 = 52;
        const ASSETS_HANDOVER_PASSED_STATUS =4; //已通过

        //停职原因
        const STOP_DUTY_REASON_UNDER_INVESTIGATION    = 4;//调查中
        const STOP_DUTY_REASON_ABSENTEEISM    = 6;//连续旷工
        const STOP_DUTY_REASON_NOT_COLLECTION = 7;//未回公款
        const STOP_DUTY_REASON_SUSPECTED_CHEATING = 9;//疑似作弊
        const STOP_DUTY_REASON_VIOLATION_CONTRACT = 10;//停职原因 违反合同

        /**
         * 获取停职原因code与翻译key映射
         */
        const SUSPEND_TYPE_REASON_MAP = [
            1 => 'stop_duty_reason_1',
            2 => 'stop_duty_reason_2',
            3 => 'stop_duty_reason_3',
            4 => 'stop_duty_reason_4',
            5 => 'stop_duty_reason_5',
            6 => 'stay_away_from_work',
            7 => 'stop_duty_reason_7',
            8 => 'stop_duty_reason_8',
            9 => 'stop_duty_reason_9',
            10 => 'stop_duty_reason_10',
        ];

        public static $jobTitle = [
            'internship' => 1355,//泰国 实习生职位
        ];

        const STAFF_TARGET_PDF_OPERATE_STAFF = 1;
        const STAFF_TARGET_PDF_OPERATE_MANAGER = 2;
    }
