<?php

namespace App\Library\Enums;


    final class MsAccessPermissionEnums
    {
        const CHECK_STATUS_ERROR    = 1;
        const CHECK_STATUS_SUCCESS  = 2;
        const CHECK_STATUS_PASS     = 3;
        //不验证权限的接口  就是你写在controller接口上的那个@Permission()
        const NOT_CHECK_PERMISSION_ROUTER = [
            'branch_attendance_statistics_list',
        ];

        //FBI不验证权限的接口
        const NOT_CHECK_FBI_PERMISSION_ROUTER = [
            //网点员工出勤统计
            'branch_attendance_statistics_list',
            'branch_attendance_statistics_export',
            //总部员工出勤统计
            'headeroffice_attendance_statistics_list',
            'headeroffice_attendance_statistics_export',
            //by员工申请查询
            'hrstaff.get-bll.list',
            'hrstaff.get-ot.list',
            'hrstaff.get-trip.list',
            'hrstaff.get-bll.export',
            'hrstaff.get-ot.export',
            'hrstaff.get-trip.export',
            'hrstaff.get-addattendance.list',
            'hrstaff.get-addattendance.export',
            //by员工请假数据查询
            'hrstaff.get.list',
            'hrstaff.export.leave.export',

            //警告系统
            'warning_staff.getInputList',//警告通知录入
            'warning_staff.getFixList',//警告通知处理
            'warning_staff.getReviewList',//警告通知复核
            'warning_staff_dismiss.getList',//开除处理


            //电子警告书
            'warning.add_warning',

            //问卷管理
            'questionnaire.getQuesList',

            //外协员工出勤统计
            'outsourcing_attendance_statistics_export',
            //导入员工支援网点
            'import.staff.support',
            //考勤统计表
            'attendance_statistics_report',

            //离职管理
            'leave-assets-manager-v2.list',
            'leave-assets-manager-v2.log.list',

            //网点支援管理->支援员工管理
            'import.staff.support.manage',
            //网点支援管理->支援需求管理
            'import.staff.support.need',
            //快递员车辆稽查
            'express_vehicle_inspection',

        ];

        //FBI需要展示：by员工申请查询 标签页及按钮，是由前端控制
        const NEED_MERGE_PERMISSION_ROUTER = [
            //by员工申请查询
            'action.hrstaff.get-bll.list',
            'action.hrstaff.get-ot.list',
            'action.hrstaff.get-trip.list',
            'action.hrstaff.get-bll.export',
            'action.hrstaff.get-ot.export',
            'action.hrstaff.get-trip.export',
            'action.hrstaff.get-addattendance.list',
            'action.hrstaff.get-addattendance.export',
            //网点员工出勤统计--导出
            'action.branch_attendance_statistics_export',
            //总部员工出勤统计--导出
            'action.headeroffice_attendance_statistics_export',
            //by员工请假数据查询--导出
            'action.hrstaff.export.leave.export',

            //电子警告书
            'action.warning.add_warning',

             //外协员工出勤统计 导出按钮
            'action.outsourcing_attendance_statistics_export',
        ];
        
    }
