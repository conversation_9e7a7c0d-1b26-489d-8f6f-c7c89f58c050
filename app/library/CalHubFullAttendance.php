<?php

namespace App\Library;

use App\library\DateHelper;
use Phalcon\Mvc\User\Component;

/**
 * CalHubFullAttendance - 计算员工全勤奖励
 *
 * 该类负责计算员工的全勤奖励，基于出勤数据和相关规则
 */
class CalHubFullAttendance extends Component
{
    /**
     * 全勤奖连续月份奖励金额（单位：元）
     */
    const FULL_ATTENDANCE_CONSECUTIVE_1_MONTH = 700;
    const FULL_ATTENDANCE_CONSECUTIVE_2_MONTH = 1000;
    const FULL_ATTENDANCE_CONSECUTIVE_3_MONTH = 1500;
    const FULL_ATTENDANCE_CONSECUTIVE_4_MONTH = 2000;
    /** @var array 配置的网点列表 */
    private $config_store;

    /** @var array 员工信息列表 */
    private $staff_info_list;

    /** @var array 员工休息日数据 */
    private $staff_off_day;

    /** @var array 考勤数据 */
    private $attendance_data;

    /** @var array 请假数据 */
    private $leave_data;

    /** @var string 统计月份 */
    private $stat_month;

    /** @var string 开始日期 */
    private $start_date;

    /** @var string 结束日期 */
    private $end_date;

    /** @var array 日期范围 */
    private $date_range;

    /** @var array 上月数据 */
    private $last_month_data;
    /**
     * 职位
     * @var array
     */
    private $config_job_title;


    /**
     * 设置开始日期
     *
     * @param string $start_date 开始日期
     * @return void
     */
    public function setStartDate(string $start_date): void
    {
        $this->start_date = $start_date;
    }

    /**
     * 设置结束日期
     *
     * @param string $end_date 结束日期
     * @return void
     */
    public function setEndDate(string $end_date): void
    {
        $this->end_date = $end_date;
    }

    /**
     * 设置配置门店列表
     *
     * @param array $config_store 配置门店列表
     * @return void
     */
    public function setConfigStore(array $config_store): void
    {
        $this->config_store = $config_store;
    }

    /**
     * 设置员工信息列表
     *
     * @param array $staff_info_list 员工信息列表
     * @return void
     */
    public function setStaffInfoList(array $staff_info_list): void
    {
        $this->staff_info_list = $staff_info_list;
    }

    /**
     * 设置员工休息日
     *
     * @param array $staff_off_day 员工休息日数据
     * @return void
     */
    public function setOffDays(array $staff_off_day): void
    {
        $this->staff_off_day = $staff_off_day;
    }

    /**
     * 设置考勤数据
     *
     * @param array $attendance_data 考勤数据
     * @return void
     */
    public function setAttendanceData(array $attendance_data): void
    {
        $this->attendance_data = $attendance_data;
    }


    /**
     * 设置请假数据
     * @param $leave_data
     * @return void
     */
    public function setLeaveData($leave_data)
    {
        $this->leave_data = $leave_data;
    }

    /**
     * 设置上月数据
     *
     * @param array $last_month_data 上月数据
     * @return void
     */
    public function setLastMonthData(array $last_month_data): void
    {
        $this->last_month_data = $last_month_data;
    }

    /**
     * 设置统计月份
     *
     * @param string $stat_month 统计月份
     * @return void
     */
    public function setStatMonth(string $stat_month): void
    {
        $this->stat_month = $stat_month;
    }

    /**
     * 设置日期范围
     *
     * @param array $date_range 日期范围
     * @return void
     */
    protected function setDateRange(array $date_range): void
    {
        $this->date_range = $date_range;
    }

    /**
     * 执行全勤奖计算
     *
     * @return array 计算结果数据
     */
    public function fire(): array
    {
        $date_range = DateHelper::DateRange(strtotime($this->start_date), strtotime($this->end_date));
        $this->setDateRange($date_range);
        $insert_data = [];

        foreach ($this->staff_info_list as $staff) {
            $full_data                 = $this->initializeFullData($staff);
            $is_cancel_full_attendance = $this->checkInitialCancelConditions($staff);


            if ($this->stat_month == HubFullAttendance::NEW_RULE_MONTH) {
                $this->dealOneStaffHubAttendance2504Logic(
                    $staff['staff_info_id'],
                    $is_cancel_full_attendance,
                    $full_data
                );
            } else {
                $this->dealOneStaffHubAttendanceLogic(
                    $staff['staff_info_id'],
                    $is_cancel_full_attendance,
                    $full_data
                );
            }

            $insert_data[] = $full_data;
        }

        return $insert_data;
    }

    /**
     * 初始化员工全勤数据
     *
     * @param array $staff 员工信息
     * @return array 初始化的全勤数据
     */
    private function initializeFullData(array $staff): array
    {
        return [
            'staff_info_id'      => $staff['staff_info_id'],
            'stat_month'         => $this->stat_month,
            'job_title'          => $staff['job_title'],
            'sys_store_id'       => $staff['sys_store_id'],
            'is_full_attendance' => 0,
            'reward_money'       => 0,
            'late_date'          => '',
            'early_date'         => '',
            'AB_date'            => '',
            'valid_on'           => 0,
            'annuel_leave_days'  => 0,
            'should_working_day' => 0,
            'leave_date'         => '',
            'OFF_date'           => '',
            'valid_on_date'      => '',
            'on'                 => 0,
            'AB'                 => 0,
            'OFF'                => 0,
            'length'             => 0,
            'cancel_remark'      => '',
        ];
    }

    /**
     * 检查初始取消全勤条件
     *
     * @param array $staff 员工信息
     * @return bool 是否取消全勤
     */
    private function checkInitialCancelConditions(array $staff): bool
    {
        // 当月入职或离职的员工不享受全勤奖
        return strtotime($staff['hire_date']) >= strtotime($this->start_date) ||
            (!empty($staff['leave_date']) && date('Y-m-d', strtotime($staff['leave_date'])) <= date('Y-m-d',
                    strtotime($this->end_date)));
    }


    /**
     * 处理员工全勤数据的通用方法
     *
     * @param int $staff_info_id 员工ID
     * @param bool $is_cancel_full_attendance 是否取消全勤标志
     * @param array $full_data 全勤数据
     * @param bool $is_special_2504 是否使用2025年4月特殊逻辑
     * @return void
     */
    protected function processStaffAttendance(
        int $staff_info_id,
        bool $is_cancel_full_attendance,
        array &$full_data,
        bool $is_special_2504 = false
    ): void {
        $valid_on_flag            = 0;
        $valid_on_annuel_off_flag = 0;
        $ab_early_late_leave_flag = false;
        $total_days               = count($this->date_range);
        $cancel_remark            = [];
        if ($is_cancel_full_attendance) {
            $cancel_remark[] = '当月入离职';
        }

        foreach ($this->date_range as $date) {
            $unique_key = $staff_info_id . '-' . $date;
            // 跳过没有考勤数据的日期
            if (empty($this->attendance_data[$unique_key])) {
                continue;
            }
            $attendance_datum = $this->attendance_data[$unique_key];
            $leave_info         = $this->leave_data[$unique_key]??[];
            $this->processAttendanceDatum(
                $attendance_datum,
                $leave_info,
                $is_cancel_full_attendance,
                $full_data,
                $ab_early_late_leave_flag,
                $valid_on_flag,
                $valid_on_annuel_off_flag,
                $is_special_2504
            );
        }
        if (empty($cancel_remark) && $is_cancel_full_attendance) {
            $cancel_remark[] = '网点or职位不在全勤奖配置中';
        }

        // 处理全勤取消条件
        if ($ab_early_late_leave_flag) {
            $cancel_remark[]           = '旷工迟到早退请非年假';
            $is_cancel_full_attendance = true;
        }

        // 2025-04月特殊逻辑：4月14日前必须有至少12天有效出勤
        if ($is_special_2504 && $valid_on_flag < 12) {
            $cancel_remark[]           = '4月14日前必须有至少12天有效出勤';
            $is_cancel_full_attendance = true;
        }
        if ($is_special_2504 && $valid_on_annuel_off_flag < 16) {
            $cancel_remark[]           = '检查有效出勤天数不满足要求';
            $is_cancel_full_attendance = true;
        }

        if (!$is_special_2504 && ($full_data['valid_on'] + $full_data['annuel_leave_days'] + $full_data['OFF'] / 10) < $total_days) {
            $cancel_remark[]           = '检查有效出勤天数不满足要求';
            $is_cancel_full_attendance = true;
        }
        // 清理日期字符串末尾的逗号和空格
        $this->trimDateString($full_data);
        $full_data['should_working_day'] = $total_days - $full_data['OFF'] / 10;
        $full_data['cancel_remark']      = implode(',', $cancel_remark);
        if ($is_cancel_full_attendance) {
            return;
        }

        // 设置全勤奖励
        $full_data['is_full_attendance'] = 1;
        $full_data['length']             = ($this->last_month_data[$staff_info_id]['length'] ?? 0) + 1;
        $full_data['reward_money']       = $this->getRewardMoneyByLength($full_data['length']);
    }

    /**
     * 迟到早退逻辑
     * @param $leave_info
     * @param $attendance_datum
     * @return array []
     */
    protected function dealLateEarly($leave_info,$attendance_datum): array
    {
        $attendance_all = ($attendance_datum['attendance_time'] + $attendance_datum['BT']);
        $is_early = false;
        $is_late  = false;
        $check_early_late = empty($this->staff_off_day[$attendance_datum['unique_key']]) && (empty($leave_info) || !in_array($leave_info['type'],[0,3]));

        if ($attendance_datum['stat_date'] < (HubFullAttendance::NEW_RULE_MONTH . '-15')) {

            if ($check_early_late && (empty($leave_info)  || !($attendance_all == 10 && $leave_info['type'] == 1)) &&  !empty($attendance_datum['attendance_started_at'])) {
                $check_start = date('Y-m-d H:i', strtotime($attendance_datum['attendance_started_at']));//上班打卡时间
                $shift_start = "{$attendance_datum['stat_date']} {$attendance_datum['shift_start']}";
                $shift_start = date('Y-m-d H:i', strtotime($shift_start));//班次开始时间
                if ($attendance_datum['leave_time_type'] == AttendanceEnums::BEFORE_LEAVE_TIME_TYPE) {
                    $shift_start = date('Y-m-d H:i', (strtotime($shift_start) + 5 * 3600));
                }
                $is_late = $check_start > $shift_start;
            }
            if ($check_early_late && (empty($leave_info)  || !($attendance_all == 10 && $leave_info['type'] == 2))  && !empty($attendance_datum['attendance_end_at'])) {
                $check_end = date('Y-m-d H:i', strtotime($attendance_datum['attendance_end_at']));//下班打卡时间
                $shift_end = "{$attendance_datum['stat_date']} {$attendance_datum['shift_end']}";
                $shift_end = date('Y-m-d H:i', strtotime($shift_end));
                if ($attendance_datum['shift_start'] > $attendance_datum['shift_end']) {
                    $shift_end = date('Y-m-d H:i', strtotime("{$shift_end} +1 day"));
                }
                if ($attendance_datum['leave_time_type'] == AttendanceEnums::AFTER_LEAVE_TIME_TYPE) {
                    $shift_end = date('Y-m-d H:i', (strtotime($shift_end) - 5 * 3600));
                }
                $is_early = $check_end < $shift_end;
            }

        }else{
            if ($check_early_late && !empty($attendance_datum['attendance_started_at'])) {
                $check_start = date('Y-m-d H:i', strtotime($attendance_datum['attendance_started_at']));//上班打卡时间
                $shift_start = "{$attendance_datum['stat_date']} {$attendance_datum['shift_start']}";
                $shift_start = date('Y-m-d H:i', strtotime($shift_start));//班次开始时间
                if (!empty($leave_info) &&  $leave_info['type'] == AttendanceEnums::BEFORE_LEAVE_TIME_TYPE) {
                    $shift_start = date('Y-m-d H:i', (strtotime($shift_start) + 5 * 3600));
                }
                $is_late = $check_start > $shift_start;
            }
            if ($check_early_late && !empty($attendance_datum['attendance_end_at'])) {
                $check_end = date('Y-m-d H:i', strtotime($attendance_datum['attendance_end_at']));//下班打卡时间
                $shift_end = "{$attendance_datum['stat_date']} {$attendance_datum['shift_end']}";
                $shift_end = date('Y-m-d H:i', strtotime($shift_end));
                if ($attendance_datum['shift_start'] > $attendance_datum['shift_end']) {
                    $shift_end = date('Y-m-d H:i', strtotime("{$shift_end} +1 day"));
                }
                if (!empty($leave_info) &&  $leave_info['type'] == AttendanceEnums::AFTER_LEAVE_TIME_TYPE) {
                    $shift_end = date('Y-m-d H:i', (strtotime($shift_end) - 5 * 3600));
                }
                $is_early = $check_end < $shift_end;
            }
        }
        return [$is_late,$is_early];
    }


    /**
     * 处理单条考勤数据
     *
     * @param array $attendance_datum 考勤数据
     * @param array $leave_info 是否请假
     * @param array $full_data 全勤数据
     * @param bool $is_cancel_full_attendance 是否取消全勤标志
     * @param bool $ab_early_late_leave_flag 迟到早退请假标志
     * @param int $valid_on_flag 有效出勤计数（2025-04特殊逻辑使用）
     * @param int $valid_on_annuel_off_flag 有效出勤天数valid_on+年假天数annuel leave days≥ 16 - 期间内轮休表OFF天数（2025-04特殊逻辑使用）
     * @param bool $is_special_2504 是否使用2025年4月特殊逻辑
     * @return void
     */
    private function processAttendanceDatum(
        array $attendance_datum,
        array $leave_info,
        bool &$is_cancel_full_attendance,
        array &$full_data,
        bool &$ab_early_late_leave_flag,
        int &$valid_on_flag,
        int &$valid_on_annuel_off_flag,
        bool $is_special_2504
    ): void {
        // 检查是否早退或迟到

        [$is_late,$is_early] = $this->dealLateEarly($leave_info,$attendance_datum);
        $attendance_all = ($attendance_datum['attendance_time'] + $attendance_datum['BT']);
        // 检查网点是否在全勤奖配置中
        if (!$is_cancel_full_attendance) {
            $is_cancel_full_attendance = !in_array($attendance_datum['sys_store_id'], $this->config_store) || !in_array($attendance_datum['job_title'], $this->config_job_title);
        }

        // 累计出勤时间
        $full_data['on'] += $attendance_datum['attendance_time'] ?? 0;

        // 处理休息日
        if (!empty($this->staff_off_day[$attendance_datum['unique_key']])) {
            $full_data['OFF']      += 10;
            $full_data['OFF_date'] .= $attendance_datum['stat_date'] . ', ';
            if ($is_special_2504 && $attendance_datum['stat_date'] >= (HubFullAttendance::NEW_RULE_MONTH . '-15')) {
                $valid_on_annuel_off_flag += 1;
            }
        }

        // 处理缺勤
        if (!empty($attendance_datum['AB'])) {
            $full_data['AB']      += $attendance_datum['AB'];
            $full_data['AB_date'] .= $attendance_datum['stat_date'] . ', ';
        }

        // 记录迟到日期
        if (!empty($is_late)) {
            $full_data['late_date'] .= $attendance_datum['stat_date'] . ', ';
        }

        // 记录早退日期
        if (!empty($is_early)) {
            $full_data['early_date'] .= $attendance_datum['stat_date'] . ', ';
        }

        $validate_check = $attendance_all >= 5 && empty($this->staff_off_day[$attendance_datum['unique_key']]);
        if ($attendance_datum['stat_date'] < (HubFullAttendance::NEW_RULE_MONTH . '-15')) {
            $validate_check = $attendance_all >= 10;
        }

        // 处理有效出勤
        if (empty($is_early) && empty($is_late) && $validate_check) {
            $full_data['valid_on']      += $attendance_all / 10;
            $full_data['valid_on_date'] .= $attendance_datum['stat_date'] . ', ';

            // 2025-04特殊逻辑：统计4月14日前的有效出勤
            if ($attendance_datum['stat_date'] < (HubFullAttendance::NEW_RULE_MONTH . '-15')) {
                $valid_on_flag += $attendance_all / 10;
            } else {
                $valid_on_annuel_off_flag += $attendance_all / 10;
            }
        }


        // 处理迟到早退请假标记
        $has_leave = !empty($leave_info) && !in_array(1,explode(',', $leave_info['leave_type_str']));

        if ($is_special_2504) {
            // 2025-04特殊逻辑：4月15日后的迟到早退请假才影响全勤
            if ($attendance_datum['stat_date'] >= (HubFullAttendance::NEW_RULE_MONTH . '-15') && (!empty($attendance_datum['AB']) || $is_early || $is_late || $has_leave)) {
                $ab_early_late_leave_flag = true;
            }
        } else {
            // 常规逻辑：任何时间的迟到早退请假都影响全勤
            if (!empty($attendance_datum['AB']) || $is_early || $is_late || $has_leave) {
                $ab_early_late_leave_flag = true;
            }
        }
        // 处理年假
        if (!empty($attendance_datum['AL'])) {
            $full_data['annuel_leave_days'] += $attendance_datum['AL'] / 10;
            if ($is_special_2504 && $attendance_datum['stat_date'] >= (HubFullAttendance::NEW_RULE_MONTH . '-15')) {
                $valid_on_annuel_off_flag += $attendance_datum['AL'] / 10;
            }
        }

        // 记录请假日期
        if ($has_leave && $attendance_datum['stat_date'] >= (HubFullAttendance::NEW_RULE_MONTH . '-15')) {
            // 2025-04特殊逻辑：只记录4月15日后的请假
            $full_data['leave_date'] .= $attendance_datum['stat_date'] . ', ';
        }
    }

    /**
     * 处理2025年4月特殊逻辑的员工全勤
     *
     * @param int $staff_info_id 员工ID
     * @param bool $is_cancel_full_attendance 是否取消全勤标志
     * @param array $full_data 全勤数据
     * @return void
     */
    protected function dealOneStaffHubAttendance2504Logic(
        int $staff_info_id,
        bool $is_cancel_full_attendance,
        array &$full_data
    ): void {
        $this->processStaffAttendance(
            $staff_info_id,
            $is_cancel_full_attendance,
            $full_data,
            true // 使用2025-04特殊逻辑
        );
    }

    /**
     * 处理常规逻辑的员工全勤
     *
     * @param int $staff_info_id 员工ID
     * @param bool $is_cancel_full_attendance 是否取消全勤标志
     * @param array $full_data 全勤数据
     * @return void
     */
    protected function dealOneStaffHubAttendanceLogic(
        int $staff_info_id,
        bool $is_cancel_full_attendance,
        array &$full_data
    ): void {
        $this->processStaffAttendance(
            $staff_info_id,
            $is_cancel_full_attendance,
            $full_data,
            false // 使用常规逻辑
        );

        // 清理日期字符串末尾的逗号和空格
        $this->trimDateString($full_data);
    }

    /**
     * 根据连续全勤月数获取奖励金额
     *
     * @param int $length 连续全勤月数
     * @return int 奖励金额
     */
    protected function getRewardMoneyByLength(int $length): int
    {
        if ($length == 0) {
            return 0;
        }

        switch ($length) {
            case 1: // 第一个月
                $reward_money = self::FULL_ATTENDANCE_CONSECUTIVE_1_MONTH;
                break;
            case 2: // 第二个月
                $reward_money = self::FULL_ATTENDANCE_CONSECUTIVE_2_MONTH;
                break;
            case 3: // 第三个月
                $reward_money = self::FULL_ATTENDANCE_CONSECUTIVE_3_MONTH;
                break;
            default: // 第四个月及之后
                $reward_money = self::FULL_ATTENDANCE_CONSECUTIVE_4_MONTH;
        }

        return $reward_money;
    }

    /**
     * 清理日期字符串末尾的逗号和空格
     * 自动处理所有以_date结尾的字段
     *
     * @param array $full_data 全勤数据
     * @return void
     */
    private function trimDateString(array &$full_data): void
    {
        foreach ($full_data as $field => $value) {
            // 处理所有以_date结尾的字段
            if (is_string($value) && !empty($value) && strpos($field, '_date') !== false) {
                $full_data[$field] = rtrim($value, ', ');
            }
        }
    }

    public function setJobTitle(array $config_job_title)
    {
        $this->config_job_title = $config_job_title;
    }


}