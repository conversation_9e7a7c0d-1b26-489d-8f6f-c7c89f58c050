<?php
namespace App\Library;

use App\Library\Enums\GlobalEnums;

/**
 * 常用枚举数据
 * Class enums
 */
final class Enums
{
    const DATA_TYPE_INT = 1; //整型
    const DATA_TYPE_STRING = 2; //字符类型
    //网点车补分类
    //车补 该分类下自动填充补助金额 1 人效高易招人 2 人效高 中招人 3 人效高难招人 4 人效低易招人 5 人效低中招人 6 人效低难招人
    const STORE_CATEGORY_ONE = 1;
    const STORE_CATEGORY_TWO = 2;
    const STORE_CATEGORY_THREE = 3;
    const STORE_CATEGORY_FOURE = 4;
    const STORE_CATEGORY_FIVE = 5;
    const STORE_CATEGORY_SIX = 6;

    public static $store_category = [
        self::STORE_CATEGORY_ONE => 'classification_1',
        self::STORE_CATEGORY_TWO => 'classification_2',
        self::STORE_CATEGORY_THREE => 'classification_3',
        self::STORE_CATEGORY_FOURE => 'classification_4',
        self::STORE_CATEGORY_FIVE => 'classification_5',
        self::STORE_CATEGORY_SIX => 'classification_6'
    ];
//'sp' => 1,
//'dc' => 2,
//'branch_agent' => 3,
//'shop_pickup_only' => 4,
//'shop_pickup_delivery' => 5,
//'fh' => 6,
//'shop_ushop' => 7,
//'hub' => 8,
//'os' => 9,
//'bdc'=> 10,
//'bhub'=> 12,
//'cdc'=> 13,
    //网点类型
    const STORE_TYPE_SP = 1;
    const STORE_TYPE_DC = 2;
    const STORE_TYPE_HUB = 8;
    const STORE_TYPE_OS = 9;

    const STORE_TYPE_BDC = 10;
    const STORE_TYPE_CDC = 13;
    const STORE_TYPE_PDC = 14;

    /**
     * 区域列表
     */
    public static $manage_areas = [
        1 => 'B', //BKK
        2 => 'CE', //Cen+East
        3 => 'CW', //Cen+West
        4 => 'N', //North
        5 => 'NE', //North East
        6 => 'S', //South
        7 => 'C', //Cen
    ];


    const COMPANY_EXPRESS = 1;
    const COMPANY_FULFILLMENT = 2;
    const COMPANY_MONEY = 3;
    const COMPANY_COMMERCE = 5;
    const COMPANY_PAY = 6;
    const COMPANY_HOME = 9;

    public static $salary_company_list = [
        1 => 'Flash Express',
        2 => 'Flash Fulfillment',
        3 => 'Flash Money',
        5 => 'F Commerce',
        6 => 'Flash Pay',
        9 => 'Flash Home',

    ];

    public static $salary_company_th_list = [
        1 => 'บริษัท แฟลช เอ็กซ์เพรส จำกัด',
        2 => 'บริษัท แฟลช ฟูลฟิลล์เม้นท์ จำกัด',
        6 => 'บริษัท แฟลช เพย์ จำกัด',
        3 => 'บริษัท แฟลช มันนี่ จำกัด',
        5 => 'บริษัท เอฟ คอมเมิร์ซ จำกัด',
        9 => 'บริษัท แฟลช โฮม โอเปอร์เรชั่น จำกัด',

    ];

    //每个公司对应的税号
    public static $id_no_company = [
        1 => '0105560159254',//'Flash Express',
        2 => '0105561194487',//'Flash Fulfillment',
        6 => '0105561194509',//'Flash Pay',
        3 => '0105561194495',//'Flash Money',
        5 => '0105563150070',//'F Commerce'
        9 => '0115566013154',//home
    ];

    /**************************** 薪资发放审批 end ******************************************/



    /*组织架构-职位体系 start*/

    //职级
    public static $job_level = [
        ['id'=>'0' ,'name'=>'F0' ],
        ['id'=>'12','name'=>'F12'],
        ['id'=>'13','name'=>'F13'],
        ['id'=>'14','name'=>'F14'],
        ['id'=>'15','name'=>'F15'],
        ['id'=>'16','name'=>'F16'],
        ['id'=>'17','name'=>'F17'],
        ['id'=>'18','name'=>'F18'],
        ['id'=>'19','name'=>'F19'],
        ['id'=>'20','name'=>'F20'],
        ['id'=>'21','name'=>'F21'],
        ['id'=>'22','name'=>'F22'],
        ['id'=>'23','name'=>'F23'],
        ['id'=>'24','name'=>'F24'],
    ];

    /*组织架构-职位体系 end*/

    const HRIS_NATIONALITY_1 = 1;
    const HRIS_NATIONALITY_2 = 2;
    const HRIS_NATIONALITY_3 = 3;
    const HRIS_NATIONALITY_4 = 4;
    const HRIS_NATIONALITY_5 = 5;
    const HRIS_NATIONALITY_6 = 6;
    const HRIS_NATIONALITY_7 = 7;
    const HRIS_NATIONALITY_8 = 8;
    const HRIS_NATIONALITY_9 = 9;
    const HRIS_NATIONALITY_10 = 10;
    const HRIS_NATIONALITY_11 = 11;
    const HRIS_NATIONALITY_99 = 99;
    public static $hris_nationality = [
        self::HRIS_NATIONALITY_1 => 'hris_nationality_1',
        self::HRIS_NATIONALITY_2 => 'hris_nationality_2',
        self::HRIS_NATIONALITY_3 => 'hris_nationality_3',
        self::HRIS_NATIONALITY_4 => 'hris_nationality_4',
        self::HRIS_NATIONALITY_5 => 'hris_nationality_5',
        self::HRIS_NATIONALITY_6 => 'hris_nationality_6',
        self::HRIS_NATIONALITY_7 => 'hris_nationality_7',
        self::HRIS_NATIONALITY_8 => 'hris_nationality_8',
        self::HRIS_NATIONALITY_9 => 'hris_nationality_9',
        self::HRIS_NATIONALITY_10 => 'hris_nationality_10',
        self::HRIS_NATIONALITY_11 => 'hris_nationality_11',
        self::HRIS_NATIONALITY_99 => 'hris_nationality_99',
    ];

    const HRIS_WORKING_COUNTRY_1 = 1;
    const HRIS_WORKING_COUNTRY_2 = 2;
    const HRIS_WORKING_COUNTRY_3 = 3;
    const HRIS_WORKING_COUNTRY_4 = 4;
    const HRIS_WORKING_COUNTRY_5 = 5;
    const HRIS_WORKING_COUNTRY_6 = 6;
    const HRIS_WORKING_COUNTRY_7 = 7;
    const HRIS_WORKING_COUNTRY_8 = 8;
    const HRIS_WORKING_COUNTRY_99 = 99;
    public static $hris_working_country = [
        self::HRIS_WORKING_COUNTRY_1 => 'hris_working_country_1',
        self::HRIS_WORKING_COUNTRY_2 => 'hris_working_country_2',
        self::HRIS_WORKING_COUNTRY_3 => 'hris_working_country_3',
        self::HRIS_WORKING_COUNTRY_4 => 'hris_working_country_4',
        self::HRIS_WORKING_COUNTRY_5 => 'hris_working_country_5',
        self::HRIS_WORKING_COUNTRY_6 => 'hris_working_country_6',
        self::HRIS_WORKING_COUNTRY_7 => 'hris_working_country_7',
        self::HRIS_WORKING_COUNTRY_8 => 'hris_working_country_8',
        self::HRIS_WORKING_COUNTRY_99 => 'hris_working_country_99',
    ];

    const SYNC_COUNTRY_1 = 1;//TH
    const SYNC_COUNTRY_2 = 2;//PH
    const SYNC_COUNTRY_3 = 3;//MY
    const SYNC_COUNTRY_4 = 4;//LA
    const SYNC_COUNTRY_5 = 5;//VN
    const SYNC_COUNTRY_6 = 6;//ID
    //同步国家
    public static $sync_country_list  = [
        self::SYNC_COUNTRY_2 => 'sync_country_2',
        self::SYNC_COUNTRY_3 => 'sync_country_3',
        self::SYNC_COUNTRY_4 => 'sync_country_4',
        self::SYNC_COUNTRY_5 => 'sync_country_5',
        self::SYNC_COUNTRY_6 => 'sync_country_6',
    ];

    //同步fbi 处罚相关的考勤信息
    const SYNC_TYPE_OFF = 1;//休息日同步


    /*OT类型 end*/

    const OT_TYPE_1 = 1;
    const OT_TYPE_2 = 2;
    const OT_TYPE_4 = 4;
    public static $ot_type = [
        self::OT_TYPE_1 => 'working_overtime_1_5',
        self::OT_TYPE_2 => 'workining_holiday_3',
        self::OT_TYPE_4 => 'workining_holiday_1',
    ];
    //员工雇佣类型
    const HRIS_HIRE_TYPE_1 = 1;
    const HRIS_HIRE_TYPE_2 = 2;
    const HRIS_HIRE_TYPE_3 = 3;
    const HRIS_HIRE_TYPE_4 = 4;
    const HRIS_HIRE_TYPE_5 = 5;
    public static $hris_hire_type = [
        self::HRIS_HIRE_TYPE_1 => 'hris_hire_type_1',
        self::HRIS_HIRE_TYPE_2 => 'hris_hire_type_2',
        self::HRIS_HIRE_TYPE_3 => 'hris_hire_type_3',
        self::HRIS_HIRE_TYPE_4 => 'hris_hire_type_4',
        self::HRIS_HIRE_TYPE_5 => 'hris_hire_type_5',
    ];

    const HRIS_STAFF_TYPE_1 = 1;//正式员工
    const HRIS_STAFF_TYPE_2 = 2;//长期外协员工
    const HRIS_STAFF_OS = 2; //外协员工

    // 员工班次设置 员工类型
    public static $hris_staff_type_my = [
        self::HRIS_STAFF_TYPE_1 => 'hris_staff_type_1',
        self::HRIS_STAFF_TYPE_2 => 'hris_staff_type_2',
    ];

    public static $hris_staff_type_common = [
        self::HRIS_STAFF_TYPE_1 => 'hris_staff_type_1',
        self::HRIS_STAFF_OS => 'hris_staff_type_os',
    ];

    //员工在职状态
    const HRIS_WORKING_STATE_1 = 1;//在职
    const HRIS_WORKING_STATE_2 = 2;//离职
    const HRIS_WORKING_STATE_3 = 3;//停职
    const HRIS_WORKING_STATE_4 = 999;//待离职
    public static $hris_working_state = [
        self::HRIS_WORKING_STATE_1 => 'hris_working_state_1',
        self::HRIS_WORKING_STATE_2 => 'hris_working_state_2',
        self::HRIS_WORKING_STATE_3 => 'hris_working_state_3',
        self::HRIS_WORKING_STATE_4 => 'hris_working_state_4',
    ];

    //待离职 是在职 子状态 别混一起写了
    public static $staff_state = [
        self::HRIS_WORKING_STATE_1 => 'hris_working_state_1',
        self::HRIS_WORKING_STATE_2 => 'hris_working_state_2',
        self::HRIS_WORKING_STATE_3 => 'hris_working_state_3',
    ];

    //全部员工在职状态
    public static $hris_all_working_state=[
        self::HRIS_WORKING_STATE_1 => 'hris_working_state_1',
        self::HRIS_WORKING_STATE_2 => 'hris_working_state_2',
        self::HRIS_WORKING_STATE_3 => 'hris_working_state_3',
        self::HRIS_WORKING_STATE_4 => 'hris_working_state_4',
    ];

    //工作天数&轮休规则
    const WORKING_DAY_REST_TYPE_51 = 51;
    const WORKING_DAY_REST_TYPE_52 = 52;
    const WORKING_DAY_REST_TYPE_61 = 61;
    const WORKING_DAY_REST_TYPE_62 = 62;
    public static $working_day_rest_type = [
        self::WORKING_DAY_REST_TYPE_51 => 'working_day_rest_type_51',
        self::WORKING_DAY_REST_TYPE_52 => 'working_day_rest_type_52',
        self::WORKING_DAY_REST_TYPE_61 => 'working_day_rest_type_61',
        self::WORKING_DAY_REST_TYPE_62 => 'working_day_rest_type_62',
    ];

    //员工身份证审核状态
    const HRIS_IDENTITY_AUDIT_STATE_0 = 0;
    const HRIS_IDENTITY_AUDIT_STATE_1 = 1;
    const HRIS_IDENTITY_AUDIT_STATE_2 = 2;
    const HRIS_IDENTITY_AUDIT_STATE_999 = 999;
    public static $hris_identity_state = [
        self::HRIS_IDENTITY_AUDIT_STATE_0 => 'staff_identity_audit_state_0',
        self::HRIS_IDENTITY_AUDIT_STATE_1 => 'staff_identity_audit_state_1',
        self::HRIS_IDENTITY_AUDIT_STATE_2 => 'staff_identity_audit_state_2',
        self::HRIS_IDENTITY_AUDIT_STATE_999 => 'staff_identity_audit_state_999',
    ];

    const BANK_CARD_AUDIT_STATE_0 = 0;
    const BANK_CARD_AUDIT_STATE_1 = 1;
    const BANK_CARD_AUDIT_STATE_2 = 2;
    const BANK_CARD_AUDIT_STATE_999 = 999;
    public static $hris_bank_state = [
        self::BANK_CARD_AUDIT_STATE_0 => "bank_card_audit_state_0",
        self::BANK_CARD_AUDIT_STATE_1 => "bank_card_audit_state_1",
        self::BANK_CARD_AUDIT_STATE_2 => "bank_card_audit_state_2",
        self::BANK_CARD_AUDIT_STATE_999 => "bank_card_audit_state_999",
    ];

	const ROLES_SYSTEM_MANAGER_ID = 14;// 系统管理员
	const ROLES_SUPER_MANAGER_ID = 99;// 超级管理员
	const ROLES_OUTLET_SUPERVISOR_ID = 18;//网点主管
	const ROLES_REGIONAL_MANAGER_ID = 21;//区域经理
	
	//职位
	//数据来源bi-hr_job_title表
	public static $job_title = [
		'area_manager' => 11,
		'bike_courier' => 13,
		'branch_supervisor' => 16,
		'dc_officer' => 37,
		'hub_manager' => 50,
		'regional_manager' => 79,
		'sales_manager' => 83,
		'shop_cashier' => 97,
		'shop_officer' => 98,
		'shop_supervisor' => 101,
		'store_officer' => 106,
		'van_courier'   => 110,
		'warehouse_staff_sorter' => 111,
		'shop_bike' => 155,
		'mini_cs_officer' => 220,
		'district_manager' => 269,
		'hub_staff' => 271,
		'hub_supervisor' => 272,
		'store_supervisor' => 296,
		'warehouse_staff' => 300,
		'employee_relation_manager' => 441,
		'assistant_branch_supervisor' => 451,
		'boat_courier' => 452,
		'onsite_officer' => 812,
		'onsite_staff' => 473,
		'fleet_driver' => 474,
		'freight_hub_staff' => 545,
		'employee_relation_specialist' => 176,
		'er_officer' => 177,
		'shop_operations_manager' => 291,
		'freight_hub_manager' => 539,
		'freight_hub_outbound_supervisor' => 540,
		'freight_hub_inbound_supervisor' => 541,
		'freight_hub_QAQC_supervisor' => 542,
		'hub_admin_officer' => 568,
		'onsite_supervisor' => 675,
		'onsite_admin_officer' => 676,
		'tricycle_courier' => 1000,
		'hub_admin_manager' => 563,
		'hub_admin_supervisor' => 574,
		'freight_hub_admin_officer' => 544,
		'shop_support_officer' => 719,
		'shop_support_supervisor' => 718,
		'shop_support_section_manager' => 717,
		'network_support_officer' => 556,
		'network_support_supervisor' => 555,
		'network_support_manager' => 554,
		'warehouse_operations_director' => 914,
		'operations_director' => 929,
		'senior_asset_data_officer'=>624,
		'asset_data_officer'=>625,
		'senior_shop_officer'=>1884,
		'car_courier'=>1199,
        'truck_driver_night' => 1675,
        'van_courier_project' => 1015,
        'ev_courier' => 1930,
        'assistant_dc_supervisor' => 1553,
        'pickup_driver' => 1844,//泰国新职位
	];
	

    /*时长end*/
    const select_time_2 = 2;
    const select_time_3 = 3;
    const select_time_4 = 4;
    const select_time_5 = 5;
    const select_time_6= 6;
    const select_time_7 = 7;
    const select_time_8 = 8;
    public static $select_time = [
        self::select_time_2 => 2,
        self::select_time_3 => 3,
        self::select_time_4 => 4,
        self::select_time_5 => 5,
        self::select_time_6 => 6,
        self::select_time_7 => 7,
        self::select_time_8 => 8,
    ];

    //非基层员工加班时长
    public static $new_duration = [
        self::select_time_4 => 4,
        self::select_time_8 => 8,
    ];

    /*打卡类型end*/
    const clock_type_1 = 1;
    const clock_type_2 = 2;

    public static $clock_type = [
        self::clock_type_1 => 'attendance_started_at',
        self::clock_type_2 => 'attendance_ended_at',
    ];


    /*请假 时间区间 时间*/
    const time_interval_1 = 1;
    const time_interval_2 = 2;

    //上午下午 其他国家用
    public static $time_interval = [
        self::time_interval_1 => 'morning',
        self::time_interval_2 => 'afternoon',
    ];

    //前后半天
    public static $time_half = [
        self::time_interval_1 => 'half_am',
        self::time_interval_2 => 'half_pm',
    ];

    const operation_type_ot = 1;//新增OT
    const operation_type_make_up = 2;//补卡
    const operation_type_add_leave = 3;//请假
    const operation_type_leave_cancel = 4;//请假撤销
    const operation_type_attendance_edit = 5;//修改打卡记录
    const operation_type_full_press = 6;//全勤奖不考核日期
    const operation_type_bt = 7;//出差申请
    public static $operation_type = [
        self::operation_type_ot => 'OT',
        self::operation_type_make_up => 'make_up',
        self::operation_type_add_leave => '6004',
        self::operation_type_leave_cancel => 'leave_cancel',
        self::operation_type_attendance_edit => 'attendance_edit',
        self::operation_type_full_press => 'full_prise_ignore',
        self::operation_type_bt => 'business_trip_add',

    ];

    //审批状态 1 申请中 2 审核通过 3 驳回 4 撤销 5超时
    const audit_status_1 = 1;
    const audit_status_2 = 2;
    const audit_status_3 = 3;
    const audit_status_4 = 4;
    const audit_status_5 = 5;
    public static $audit_status = [
        self::audit_status_1 => 'by_state_1',
        self::audit_status_2 => 'by_state_2',
        self::audit_status_3 => 'by_state_3',
        self::audit_status_4 => 'by_state_4',
        self::audit_status_5 => 'by_state_5',
    ];

    //oss file type 枚举
    const OSS_FILE_TYPE_1 = 1;
    const OSS_FILE_TYPE_2 = 2;
    const OSS_FILE_TYPE_3 = 3;
    const OSS_FILE_TYPE_4 = 4;
    const OSS_FILE_TYPE_5 = 5;//油费报销 发票
    const OSS_FILE_TYPE_6 = 6;//油费报销 签名
    const OSS_FILE_TYPE_7 = 7;//油费报销 申请单
    const OSS_FILE_TYPE_8 = 8;//油费报销 过路费发票
    const OSS_FILE_TYPE_9 = 9;//油费报销 详情pdf
    const OSS_FILE_TYPE_10 = 10;//油费报销 用车记录 图片pdf
    const OSS_FILE_TYPE_11 = 11;//扣税证明 pdf


    public static $thai_month = array(
        //获取泰文月份
        1 => 'มกราคม',
        2 => 'กุมภาพันธ์',
        3 => 'มีนาคม',
        4 => 'เมษายน',
        5 => 'พฤษภาคม',
        6 => 'มิถุนายน',
        7 => 'กรกฎาคม',
        8 => 'สิงหาคม',
        9 => 'กันยายน',
        10 => 'ตุลาคม',
        11 => 'พฤศจิกายน',
        12 => 'ธันวาคม',
    );

    //待离职状态
    const WAIT_LEAVE_STATE = 1; //待离职

    /**
     * 处罚项目对应的每日最大罚款金额
     * @var int[]
     */
    public static $punish_money_day_max = [
        12 => 200,   //迟到罚款 每日最大200泰铢
        26 => 300,   //早退罚款
    ];

    /**
     * 处罚项目对应的每月最小违规时间
     * @var int[]
     */
    public static $punish_time_month_min = [
        12 => 20,   //迟到罚款，每月迟到时间总和低于当前值不处罚。
    ];

    //发送chu处罚原因 最大值38
    public static $punish = [
        27 => 'fleet_depart_late',                      //班车发车晚点
        18 => 'speed_not_prior',                        //仓管未交接SPEED/优先包裹给快递员
        13 => 'not_transfer_gt_one_day',                //揽收或中转包裹未及时发出
        2 => 'package_not_update_bg_one',               //5天以内未妥投，且超24小时未更新 [原来叫法:包裹超过1天没有更新]
        3 => 'undelivered_bg_five',                     //5天以上未妥投/未中转，且超24小时未更新 [原来叫法:5天未妥投/未中转，且超一天未更新]
        14 => 'work_work_not_deal_with',                //工单处理不及时 [原来叫法:仓管对工单处理不及时]
        4 => 'package_resolve_not_in_time',             //对问题件解决不及时 [原来叫法:miniCS对问题件解决不及时]
        15 => 'problem_package_not_deal_with',          //仓管未及时处理问题件包裹
        7 => 'package_loss',                            //包裹丢失
        8 => 'breakage',                                //包裹破损
        25 => 'parcel_embargo',                         //揽收禁运包裹
        10 => 'abnormal_balance',                       //揽件时称量包裹不准确 [原来叫法:复秤异常]
        19 => 'speed_not_prior_not_del',                //PRI或者speed包裹未妥投
        20 => 'false_finished_del',                     //虚假妥投
        1 => 'problem_package',                         //虚假问题件/虚假留仓件
        24 => 'customer_complaints_problem_package',    //客户投诉-虚假问题件/虚假留仓件
        21 => 'customer_complaints',                    //客户投诉
        22 => 'courier_not_remittance',                 //快递员公款超时未上缴
        //11 => 'cashier_un_back_money',                //出纳回款不及时
        12 => 'later_cut',                              //迟到罚款
        26 => 'early_cut',                              //早退罚款
        17 => 'do_not_answer_the_phone',                //故意不接公司电话
        9 => 'other',                                   //其他
        5 => 'undelivered_bg_three',                    //包裹配送时间超三天
        6 => 'undelivered_at_revision_time',            //未在客户要求的改约时间之前派送包裹
        //16 => 'complaint_cut',                        //客户投诉罚款 已废弃
        33 => 'pickup_late',                            //揽件不及时处罚
        23 => 'minics_work_not_deal_with',              //miniCS工单处理不及时
        28 => 'sham_work_order',                        //虚假工单上报
        29 => 'delivering_not_mark',                    //未妥投包裹没有标记
        30 => 'delivering_not_detain',                  //未妥投包裹没有入仓
        31 => 'sp_delivering_not_phone_timely',         //speed pri 交接扫描后未及时联系客户
        32 => 'sp_not_scan_timely',                     //仓管未及时交接SPEED/PRI优先包裹
        34 => 'parcel_not_inventoried',                 //网点应盘点包裹未清零
        35 => 'missed_collection',                      //漏揽收
        36 => 'unqualified_outer_packing_pkg',          //包裹外包装不合格
        37 => 'super_big_pkg',                          //超大件
        38 => 'multi_pno_pkg',                          //多面单
        39 => 'missed_weight',                          //漏称重
        40 => 'upload_sham_img',                        //上传虚假照片
        41 => 'arrive_lose_scan',                       //网点到件漏扫描
        42 => 'sham_cancel_pkg',                        //虚假撤销
        43 => 'sham_cancel_pickup_task',                //虚假揽件标记
        44 => 'parcel_handover_count_low',              //外协员工日交接不满50件包裹
        45 => 'pack_large_punish',                      //超大集包处罚
        46 => 'non_pack_punish',                        //不集包
        47 => 'pickup_claims_ticket_punish',            //理赔处理不及时
        48 => 'parcel_paste_not_standard',              //面单粘贴不规范
        49 => 'parcel_not_replacement',                 //未换单
        50 => 'pack_label_not_standard',                //集包标签不规范
        51 => 'pickup_ticket_unclosed',                 //未完成任务处罚
        52 => 'sham_violation_pkg_report',              //虚假上报（虚假违规件上报）
        53 => 'false_misclassification',                 //虚假错分
        54 => 'fruit_pieces',                           //物品类型错误（水果件）
        55 => 'false_report_mileage',                   //虚假上报车辆里程
        57 => 'courier_absenteeism',                    //旷工罚款
        58 => 'false_cancel_solicitation',              //虚假取消揽件任务
    ];

    //处罚项目对应的金额,为0的表示金额不确定或者由录入人员自己定义
    public static $punish_money = [
        1 => [
            'speed_pri' => 400,
            'other' => 200
        ],          //虚假问题件/虚假留仓件
        2 => 100,   //5天以内未妥投，且超24小时未更新
        3 => 150,   //5天以上未妥投/未中转，且超24小时未更新
        4 => 50,    //对问题件解决不及时
        5 => 0,     //包裹配送时间超三天
        6 => 150,   //未在客户要求的改约时间之前派送包裹
        7 => 0,     //包裹丢失
        8 => 0,     //包裹破损
        9 => 0,     //其他
        10 => 0,    //揽件时称量包裹不准确
        11 => 0,    //出纳回款不及时
        12 => 10,    //迟到罚款 每分钟 10 泰铢
        13 => 100,  //揽收或中转包裹未及时发出
        14 => 50,   //仓管对工单处理不及时
        15 => 100,  //仓管未及时处理问题件包裹
        //16 => 0,  //客户投诉罚款 已废弃
        17 => 0,    //故意不接公司电话 自定义
        18 => 150,  //仓管未交接SPEED/优先包裹给快递员
        19 => 50,   //PRI或者speed包裹未妥投
        20 => 0,    //虚假妥投
        21 => 200,  //客户投诉
        22 => 500,  //快递员公款超时未上缴
        23 => 50,   //miniCS工单处理不及时
        24 => 0,    //客户投诉-虚假问题件/虚假留仓件
        25 => 500,  //揽收禁运包裹
        26 => 10,   //早退罚款
        27 => 100,  //班车晚点
        28 => 50,   //虚假工单上报
        29 => 50,   //未妥投包裹没有标记
        30 => 50,   //未妥投包裹没有入仓
        31 => 50,   //speed pri 交接扫描后未及时联系客户
        32 => 50,   //仓管未及时交接SPEED/PRI优先包裹
        33 => 100,  //揽收不及时
        34 => 5,    //网点应盘点包裹未清零
        35 => 0,    //漏揽收
        36 => 0,    //包裹外包装不合格
        37 => 0,    //超大件
        38 => 0,    //多面单
        40 => 100,  //上传虚假照片
        39 => 50,    //漏称重
        41 => 1,    //网点到件漏扫描
        42 => 0,    //虚假撤销
        43 => 0,    //虚假揽件标记
        44 => 100,  //外协员工日交接不满50件包裹
        45 => 500,  //超大集包处罚
        46 => 100,  //不集包
        47 => 50,   //理赔处理不及时
        48 => 0,    //面单粘贴不规范
        49 => 0,    //未换单
        50 => 0,    //集包标签不规范
        51 => 100,  //未完成任务处罚
        52 => 0,    //虚假上报（虚假违规件上报）
        53 => 100,  //虚假错分
        54 => 0,    //物品类型错误（水果件）
        55 =>       //虚假上报车辆里程
            [
                'false_mileage' => 300, //虚假
                'vague' => 200, //模糊
            ],
        57 => 500, //旷工罚款
        58 => 200, //虚假取消揽件任务
    ];

    const AVG_PARCEL = 30;//应派件 平均值 计算pd 用
    const NEW_AVG_PARCEL = 50;//应派件 平均值 计算pd 用
    const NEW_AVG_PARCEL_V5 = 80;//应派件 平均值 计算pd 用 车补第五版中变更

    const ALL_STORE_PERMISSION = '-2';//有全部网点权限可视范围

    const SALARY_SOURCE_TASK = 0;//工资条任务 调用来源 也是返回pdf oss 地址 跟 2 一样
    const SALARY_SOURCE_DATA = 1;//by 获取工资数据 来源
    const SALARY_SOURCE_URL = 2;//by 页面操作 获取 工资条pdf 地址 来源
    const SALARY_SOURCE_UNFIX_URL = 3;//by 获取 非固定薪资pdf 来源 印尼定制
    const SALARY_SOURCE_DOWNLOAD = 4;//hcm 页面下载调用

    //结算类型
    public static $pay_type = [
        1 => 'BY_DAY',
        2 => 'BY_MONTH',
    ];

    const FRONT_LINE_POSITION = [101,98,97,16,451,37,110,1015,13,452,272,572,271,661,220,675,753,473,745,992,540,664,545,546,924,158,917,300,918,1000,801];//一线职位

    // 即时发送
    const INSTANT_SEND = 1;
    // 定时发送
    const SCHEDULED_SENDING = 2;

    public static $timer = [
        '00:00',
        '00:30',
        '01:00',
        '01:30',
        '02:00',
        '02:30',
        '03:00',
        '03:30',
        '04:00',
        '04:30',
        '05:00',
        '05:30',
        '06:00',
        '06:30',
        '07:00',
        '07:30',
        '08:00',
        '08:30',
        '09:00',
        '09:30',
        '10:00',
        '10:30',
        '11:00',
        '11:30',
        '12:00',
        '12:30',
        '13:00',
        '13:30',
        '14:00',
        '14:30',
        '15:00',
        '15:30',
        '16:00',
        '16:30',
        '17:00',
        '17:30',
        '18:00',
        '18:30',
        '19:00',
        '19:30',
        '20:00',
        '20:30',
        '21:00',
        '21:30',
        '22:00',
        '22:30',
        '23:00',
        '23:30',
        '23:59',
    ];

    public static $defaultTime = '1970-01-01';

    //提交状态
    const SUBMIT_STATUS_PENDING = 1;//待提交
    const SUBMIT_STATUS_COMPLETE = 2;//已提交
    public static $submit_status = [
        self::SUBMIT_STATUS_PENDING => 'not_commit',
        self::SUBMIT_STATUS_COMPLETE => 'commit'
    ];
}
