<?php

namespace App\Library;

use App\Models\backyard\AttendanceDataV2Model;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffAuditModel;
use App\Services\BackyardAttendanceService;
use App\Services\LeaveBllService;
use App\Services\SettingEnvService;
use Phalcon\Mvc\User\Component;


class HubFullAttendance extends Component
{

    const NEW_RULE_MONTH = '2025-04';


    /**
     * 计算员工全勤奖励
     * @param $start_date
     * @param $end_date
     * @param $staff_info_ids
     * @return array
     */
    public function calculate($start_date, $end_date, $staff_info_ids): array
    {
        $config_store = (new SettingEnvService())->getSetVal('hub_full_attendance_store', ',');
        $config_job_title = (new SettingEnvService())->getSetVal('hub_full_attendance_position', ',');

        $this->logger->info(['start_date' => $start_date, 'end_date' => $end_date, 'staff_info_ids' => $staff_info_ids,'config_store'=>$config_store,'config_job_title'=>$config_job_title]);
        $staff_list   = $this->getHubFullAttendanceStaffData($config_job_title,$start_date, $end_date, $staff_info_ids);
        $staff_chuck  = array_chunk($staff_list, 500);
        $stat_month   = substr($start_date, 0, 7);
        $insert_data  = [];
        foreach ($staff_chuck as $staff_item) {
            [
                $staff_off_day,
                $staff_info_list,
                $attendance_data,
                $leave_data,
                $last_month_data,
            ] = $this->bindData($staff_item,
                $start_date, $end_date);
            $calModel = new CalHubFullAttendance();
            $calModel->setConfigStore($config_store);
            $calModel->setJobTitle($config_job_title);
            $calModel->setStatMonth($stat_month);
            $calModel->setOffDays($staff_off_day);
            $calModel->setStartDate($start_date);
            $calModel->setEndDate($end_date);
            $calModel->setStaffInfoList($staff_info_list);
            $calModel->setAttendanceData($attendance_data);
            $calModel->setLeaveData($leave_data);
            $calModel->setLastMonthData($last_month_data);
            $insert_data = array_merge($insert_data, $calModel->fire());
        }
        return $insert_data;
    }

    /**
     * 绑定数据
     * @param $staff_item
     * @param $start_date
     * @param $end_date
     * @return array
     */
    protected function bindData($staff_item, $start_date, $end_date): array
    {
        //过滤历史离职的员工
        $staff_info_list = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,job_title,sys_store_id,hire_date,leave_date,state',
            'conditions' => 'staff_info_id in ({staff_ids:array}) and (state in (1,3) or (state = 2 and leave_date > :start_date:))',
            'bind'       => [
                'staff_ids'  => array_column($staff_item, 'staff_info_id'),
                'start_date' => $start_date,
            ],
        ])->toArray();
        $staff_ids       = array_column($staff_info_list, 'staff_info_id');

        $staff_off_day  = HrStaffWorkDaysModel::find([
            'columns'    => "staff_info_id,date_at,concat(staff_info_id, '-', date_at) as unique_key",
            'conditions' => 'date_at between :start_date: and :end_date: and staff_info_id in ({staff_ids:array})',
            'bind'       => [
                'start_date' => $start_date,
                'end_date'   => $end_date,
                'staff_ids'  => $staff_ids,
            ],
        ])->toArray();
        $staff_off_day  = array_column($staff_off_day, null, 'unique_key');
        $staff_ids_str      = implode(',', $staff_ids);
        $attendance_sql = "select
              concat(staff_info_id, '-', stat_date) as unique_key
              ,staff_info_id,stat_date,job_title,sys_store_id
              ,attendance_time,OFF,AB,BT,CT
              ,leave_time_type,AL            
              ,attendance_started_at,shift_start
              ,attendance_end_at,shift_end
            from attendance_data_v2  
            where staff_info_id in ({$staff_ids_str})
            and stat_date between '{$start_date}' and '{$end_date}'";
        //生成数据后直接读，所以此次选择主库
        $attendance_data = $this->getDI()->get('db_backyard')->query($attendance_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $attendance_data = array_column($attendance_data, null, 'unique_key');
        //上个月全勤奖数据
        $last_month_data = array_column((new BackyardAttendanceService())->hubFullAttendance(date('Y-m',
            strtotime("{$start_date} -1 month"))), null, 'staff_info_id');
        //获取 请假
        $date_list   = DateHelper::DateRange(strtotime($start_date), strtotime($end_date));
        $leave_data  = $this->getLeaveByDate($staff_ids, $date_list);
        $leave_data  = array_column($leave_data, null, 'unique_key');
        return [
            $staff_off_day,
            $staff_info_list,
            $attendance_data,
            $leave_data,
            $last_month_data,
        ];
    }


    public function getLeaveByDate($staffIds,$dateList){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.date_at,s.staff_info_id,sum(s.type) as type,group_concat(a.leave_type) as leave_type_str
        ,concat(s.staff_info_id, '-', s.date_at) as unique_key");
        $builder->from(['s' => StaffAuditLeaveSplitModel::class]);
        $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');
        $builder->inWhere("s.staff_info_id", $staffIds);
        $builder->inWhere('s.date_at ', $dateList);
        $builder->andWhere('a.audit_type = 2');
        $builder->inWhere('a.status', [Enums::audit_status_1,Enums::audit_status_2]);
        $builder->andWhere('a.leave_type not in (15,38)');
        $builder->groupBy("s.staff_info_id,s.date_at");
        return $builder->getQuery()->execute()->toArray();
    }



    /**
     * 获取hub全勤奖员工
     * @param $start_date
     * @param $end_date
     * @param $staff_info_ids
     * @return array
     */
    protected function getHubFullAttendanceStaffData($config_job_title,$start_date, $end_date, $staff_info_ids): array
    {

        $conditions = 'stat_date between :start_date: and :end_date: and job_title in ({job_title:array})';
        $bind       = [
            'start_date' => $start_date,
            'end_date'   => $end_date,
            'job_title'  => $config_job_title,
        ];

        if (!empty($staff_info_ids)) {
            $conditions             .= ' and staff_info_id in ({staff_info_ids:array})';
            $bind['staff_info_ids'] = $staff_info_ids;
        }

        return AttendanceDataV2Model::find([
            'columns'    => 'staff_info_id',
            'conditions' => $conditions,
            'bind'       => $bind,
            'group'      => 'staff_info_id',
        ])->toArray();
    }

}
