<?php

namespace App\Library;

use App\Services\SysService;
use Exception;
use Phalcon\Mvc\User\Component;
use PHPMailer\PHPMailer\PHPMailer;

/**
 * FlashMailer - 邮件发送工具类（单例模式）
 */
class FlashMailer extends Component
{
    /**
     * @var FlashMailer 单例实例
     */
    private static $instance = null;

    /**
     * @var array 邮件配置
     */
    protected $config = [];

    /**
     * 私有构造函数，防止外部创建实例
     *
     * @param $config
     */
    private function __construct($config)
    {
        $this->config = $config;
    }

    /**
     * 私有克隆方法，防止外部克隆
     */
    private function __clone() {}

    /**
     * 私有反序列化方法，防止反序列化创建对象
     */
    private function __wakeup() {}

    /**
     * 获取单例实例
     *
     * @param  $config
     * @return FlashMailer 返回单例实例
     */
    public static function getInstance($config): self
    {
        if (self::$instance === null) {
            self::$instance = new self($config);
        }

        return self::$instance;
    }


    /**
     * 发送邮件
     *
     * @param array|string $to 收件人，可以是单个邮箱地址或包含多个邮箱地址的数组
     *                         数组元素可以是字符串邮箱地址或包含'email'和'name'键的关联数组
     * @param string $title 邮件标题
     * @param string $content 邮件内容
     * @param array $attachments 附件数组
     * @param array $cc 抄送人数组，格式同$to
     * @param array $bcc 密送人数组，格式同$to
     * @param bool $isHtml 是否为HTML格式邮件
     * @return bool 发送结果
     * @throws Exception 发送失败时抛出异常
     */
    public function send(
        $to,
        string $title,
        string $content,
        array $attachments = [],
        array $cc = [],
        array $bcc = [],
        bool $isHtml = true
    ): bool {
        $mail = new PHPMailer(true);

        //Server settings
        $mail->SMTPAuth = true;                                                                                                                                                                                    //Enable SMTP authentication
        $mail->isSMTP();                                                                                                                                                                                                    //Send using SMTP
        $mail->Host     = $this->config['smtp_host'];                                                                                                                                                                           //Set the SMTP server to send through
        $mail->Username = $this->config['username'];                                                                                                                                                                        //SMTP username
        $mail->Password = $this->config['password'];                                                                                                                                                                        //SMTP password

        $mail->SMTPSecure  = PHPMailer::ENCRYPTION_SMTPS;            //Enable implicit TLS encryption
        $mail->CharSet     = 'UTF-8';
        $mail->ContentType = 'text/plain; charset=UTF-8';
        $mail->Encoding    = PHPMailer::ENCODING_BASE64;
        $mail->Port        = 465;                                    //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`


        //Recipients
        $mail->setFrom($this->config['username'], $this->config['show_name']);

        // 添加多个收件人
        if (is_array($to)) {
            foreach ($to as $recipient) {
                if (is_array($recipient) && isset($recipient['email']) && isset($recipient['name'])) {
                    $mail->addAddress($recipient['email'], $recipient['name']);
                } else if (is_string($recipient) && filter_var($recipient, FILTER_VALIDATE_EMAIL)) {
                    $mail->addAddress($recipient);
                }
            }
        } else if (is_string($to) && filter_var($to, FILTER_VALIDATE_EMAIL)) {
            $mail->addAddress($to);
        }

        // 添加多个抄送人
        if (!empty($cc)) {
            foreach ($cc as $ccRecipient) {
                if (is_array($ccRecipient) && isset($ccRecipient['email']) && isset($ccRecipient['name'])) {
                    $mail->addCC($ccRecipient['email'], $ccRecipient['name']);
                } else if (is_string($ccRecipient) && filter_var($ccRecipient, FILTER_VALIDATE_EMAIL)) {
                    $mail->addCC($ccRecipient);
                }
            }
        }

        // 添加多个密送人
        if (!empty($bcc)) {
            foreach ($bcc as $bccRecipient) {
                if (is_array($bccRecipient) && isset($bccRecipient['email']) && isset($bccRecipient['name'])) {
                    $mail->addBCC($bccRecipient['email'], $bccRecipient['name']);
                } else if (is_string($bccRecipient) && filter_var($bccRecipient, FILTER_VALIDATE_EMAIL)) {
                    $mail->addBCC($bccRecipient);
                }
            }
        }

        // 添加附件
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (is_array($attachment) && isset($attachment['path'])) {
                    $filename = $attachment['name'] ?? basename($attachment['path']);
                    $encoding = $attachment['encoding'] ?? PHPMailer::ENCODING_BASE64;
                    $type = $attachment['type'] ?? '';

                    $mail->addAttachment($attachment['path'], $filename, $encoding, $type);
                } else if (is_string($attachment) && file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }

        $mail->isHTML($isHtml);
        $content = (new SysService())->getEmailSignature($content,$this->config['company_type']);
        //Set email format to HTML
        $mail->Subject = $title;
        $mail->Body    = $content;
        $this->logger->info(['to'=>$to,'title'=>$title,'content'=>$content,'attachments'=>$attachments,'cc'=>$cc,'bcc'=>$bcc,'isHtml'=>$isHtml]);
        return $mail->send();
    }

}