<?php

namespace App\Library;


final class ErrCode {
    /**
     * @Message ('成功')
     */
    CONST SUCCESS = 1;
    
    const FAIL = -3;//失败

    /**
     * @Message ('验证错误，可对外抛出的错误')
     */
    CONST VALIDATE_ERROR = 2;

    //系统故障系列
    /**
     * @Message ('系统内部错误')
     */
    CONST SYSTEM_ERROR = 3;

    //MySQL级别错误
    /**
     * @Message ('MySQL内部错误')
     */
    CONST MYSQL_ERROR = 4;

    /**
     * @var int
     */
    CONST BUSINESS_ERROR = 5;

    /**     1000以下预留，业务错误请从1000开始使用     **/


	/********************* 轮休管理 错误码区分  11000 ~ 12000 end **********************/
	/**
	 * @Message ('当天有审批通过或者待审批的加班申请，请撤销加班申请后再调整休息日')
	 */
	CONST WORK_DAY_CHECK_OVERTIME_ERROR = 11001;
	/**
	 * @Message ('当天有审批通过或者待审批的请假申请，请撤销加班申请后再调整休息日')
	 */
	CONST WORK_DAY_CHECK_LEAVE_ERROR = 11002;
    /**
     * @Message ('轮休配置错误')
     */
	CONST WORK_DAY_CHECK_SETTING_ERROR = 11003;

	/********************* 轮休管理 错误码区分  11000 ~ 12000 end **********************/



    /********************* 创建外协众包工号 begin **********************/
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20001 = 20001;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20002 = 20002;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20003 = 20003;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20004 = 20004;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20005 = 20005;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20006 = 20006;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20007 = 20007;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20008 = 20008;
    const SOURCING_OUTSOURCING_STAFF_CREATE_ERROR_20009 = 20009;


    /********************* 创建外协众包工号 end **********************/
    //非在职员工不能转为个人代理
    const HIRE_TYPE_NOT_ON_JOB = 21001;

    //未完成的揽件任务，如有则报错：该员工有未完成的揽件任务,不能变更所属网点，请联系网点经理
    const HIRE_TYPE_TICKET_PICKUP = 21002;

    //未完成的派件任务，如有则报错：该员工有未完成的派件任务不能变更所属网点，请联系网点经理
    const HIRE_TYPE_TICKET_DELIVERY = 21003;

    //未回款的快递员公款，如有则报错：该员工有未结清的公款，不能变更所属网点，请联系网点经理
    const HIRE_TYPE_RECEIVABLE_BILL_DETAIL = 21004;

    //若员工绑定了客户，则提示：该员工被签约客户指定揽件，不能变更所属网点，请联系管理员
    const HIRE_TYPE_KA_PROFILE = 21005;

    //该员工已绑定派送码，不能变更所属网点，请通知网点主管重新设置派送码设置及快递员绑定
    const HIRE_TYPE_DELIVERY_BARANGAY_STAFF_INFO = 21006;

    //该员工是片区负责人，不能变更所属网点，请通知网点经理重新设置片区负责人
    const HIRE_TYPE_SYS_DISTRICT = 21007;

    //当前员工有未向总部汇款项，不能变更所属网点
    const HIRE_TYPE_REMITTANCE_BILL = 21008;

    //验证是否有未归还备用金
    const HIRE_TYPE_RESERVE_FUND_APPLY = 21009;
    /********************* 考勤薪酬 **********************/
    const MANAGE_STAFF_EMPTY = 22001;


    const JOB_GRADE_BE_SAME = 31001; //职级与原职级一致
    const JOB_GRADE_WRONG_JOB_GRADE_CHANGE_REASON = 31002; //校验职级调整原因
    const JOB_GRADE_NO_PERMISSION = 31003; //校验编辑权限
    const JOB_GRADE_CAN_NOT_OPERATE_HIGHER_GRADE = 31004; //校验编辑权限
    const JOB_GRADE_JOB_GRADE_EFFECTIVE_DATE_ERR = 31005; //校验生效日期需要大于上一次调整的生效日期或入职日期
    const JOB_GRADE_EXIST_PENDING = 31006; //校验是否存在未到生效日期的变更记录
    const JOB_GRADE_JOB_DEPARTMENT_RELATION_NOT_EXIST = 31007; //校验是否存部门职位关联关系
    const JOB_GRADE_STAFF_INFO_NOT_EXIST = 31008; //校验工号是否正确
    const JOB_GRADE_STAFF_INFO_FORMAL = 31009; //校验工号是否编制工号
    const JOB_GRADE_INVALID_EFFECTIVE_DATE = 31010; //校验生效日期
    const JOB_GRADE_INVALID_JOB_GRADE = 31011; //校验职级选项
    const JOB_GRADE_INVALID_JOB_TITLE_NAME = 31012; //校验职位名
    const JOB_GRADE_JOB_TITLE_ID_NAME_NOT_SAME = 31013; //校验职位名与ID是否一致
    const JOB_GRADE_INVALID_DEPARTMENT_NAME = 31014; //校验部门名
    const JOB_GRADE_DEPARTMENT_ID_NAME_NOT_SAME = 31015; //校验部门名与ID是否一致
    const JOB_GRADE_INVALID_JOB_TITLE_ID = 31016; //校验职位ID
    const JOB_GRADE_INVALID_DEPARTMENT_ID = 31017; //校验部门ID
}
