<?php

namespace App\Library;

use AliyunMNS\Client;
use AliyunMNS\Requests\PublishMessageRequest;
use App\Library\Enums\GlobalEnums;
use App\Models\backyard\HrStaffInfoModel;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class StaffUtils
{
    /**
     * 获取外协工号
     * @param array $params
     * @param string $language
     * @return array|bool|mixed|null
     * @throws GuzzleException
     * @throws Exception
     */
    public static function getNewStaffId(array $params, string $language)
    {
        // 获取工号
        $request_params = [
            'type'   => ($params['store_id'] == GlobalEnums::HEAD_OFFICE_ID) ? 2 : 1,
            'formal' => !empty($params['formal']) ? intval($params['formal']) : HrStaffInfoModel::FORMAL_0,
        ];
        $api            = new RestClient('nws');
        $res            = $api->execute(RestClient::METHOD_POST, '/svc/staff/generate/staffId',
            $request_params, ['Accept-Language' => $language]);
        if (isset($res['code']) && $res['code'] == 1) {
            return $res['data'];
        }
        $t = BaseService::getTranslation($language);
        throw new Exception($res['message'] ?? $t->_('server_error'));
    }

    /**
     * 向fle同步工号
     * @param $param
     * @return mixed
     */
    public static function syncStaffToFle($param)
    {
        $param['positions'] = is_array($param['positions']) ? array_map('intval', $param['positions']) : [];
        $messageBody        = [
            'id'                   => intval($param['staff_info_id']),
            'company_name'         => strval($param['company_name_ef'] ?? ''),
            'name'                 => $param['name'],
            'mobile'               => strval($param['mobile']),
            'email'                => strval($param['email'] ?? ''),
            'personal_email'       => strval($param['personal_email'] ?? ''),
            'positions'            => $param['positions'],
            'vehicle'              => intval($param['vehicle'] ?? null),
            'formal'               => intval($param['formal'] ?? 1),
            'state'                => intval($param['state'] ?? 1),  // 默认在职
            'wait_leave_state'     => intval($param['wait_leave_state'] ?? 0),  //待离职状态
            'leave_date'           => $param['leave_date'] ? date('Y-m-d', strtotime($param['leave_date'])) : null,
            'operator_id'          => intval($param['operator_id'] ?? -1),
            'event'                => intval($param['event'] ?? 1),//更新还是新增 1 新增 2 update 默认为新增
            'hire_date'            => $param['hire_date'] ? date('Y-m-d', strtotime($param['hire_date'])) : null,
            'organization_id'      => strval($param['organization_id'] ?? ''),
            'organization_type'    => intval($param['organization_type']),
            'department_id'        => strval($param['department_id']??''),
            'outsourcing_pay_type' => isset($param['pay_type']) ? strval($param['pay_type']) : null,
            'store_id_list'        => $param['store_id_list'] ?? [],
            'area_list'            => $param['area_list'] ?? [],
            'profile_photo_path'   => $param['profile_photo_path'] ?? '',
            'is_sub_staff'         => intval($param['is_sub_staff'] ?? 0),
            'master_staff'         => isset($param['master_staff']) ? intval($param['master_staff']) : null,
            'stop_duties_date'     => $param['stop_duties_date'] ? date('Y-m-d', strtotime($param['stop_duties_date'])) : null,
            'job_title'            => intval($param['job_title']??0),
            'reset_password'       => boolval($param['reset_password'] ?? false),
            'outsourcing_category' => isset($param['outsourcing_type']) ? intval($param['outsourcing_type']) : null,
            'mobile_company'       => !empty($param['mobile_company']) ? strval($param['mobile_company']) : null,
            'instructor_id'        => isset($param['instructor_id']) ? intval($param['instructor_id']) : null,
            'superior_id'          => isset($param['manager_id']) ? intval($param['manager_id']) : null,
            'bank_no'              => isset($param['bank_no']) ? strval($param['bank_no']) : null,
            'bank_type'            => isset($param['bank_type']) ? intval($param['bank_type']) : null,
            'bank_no_name'         => $param['bank_no_name'] ?? null,
            'hire_type'            => intval($param['hire_type'] ?? 0),
            'staff_source_category' => intval($param['staff_source_category'] ?? 0),
        ];
        $mq                           = new RocketMQ('sync-staff-to-java');
        $sendData['jsonCondition']    = json_encode($messageBody);
        $sendData['handleType']       = RocketMQ::TAG_HR_STAFF_SAVE;
        $sendData['shardingOrderKey'] = $param['staff_info_id'];
        $mq->setShardingKey($param['staff_info_id']);
        $result                       = $mq->sendOrderlyMsg($sendData);
        return !empty($result);
    }
}