<?php
/**
 * Author: Bruce
 * Date  : 2023-09-26 10:59
 * Description:
 */

namespace App\Repository;


use App\Library\BaseRepository;
use App\Models\backyard\AuditApplyModel;
use App\Models\backyard\AuditApprovalModel;

class AuditApprovalRepository extends BaseRepository
{
    /**
     * 获取审批人审批信息
     * @param array $auditIds
     * @param int $auditType
     * @param int $auditState
     * @param array $columns
     * @return array
     */
    public static function getAuditApprovalList($auditIds = [], $auditType = 0, $auditState = 0, $columns = ['*'])
    {
        if (empty($auditIds) || empty($auditType)) {
            return [];
        }
        $conditions        = 'biz_type = :biz_type: and biz_value in ({biz_value:array}) and deleted = 0';
        $bind['biz_value'] = $auditIds;
        $bind['biz_type']  = $auditType;

        if (!empty($auditState)) {
            $conditions    .= ' and state = :state: ';
            $bind['state'] = $auditState;
        }

        return AuditApprovalModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ])->toArray();
    }

    /**
     * 获取审批人审批信息
     * @param array $params
     * @param array $columns
     * @return array
     */
    public static function getApprovalList($params, $columns = ['*'])
    {
        if (empty($params['audit_ids']) || empty($params['audit_type'])) {
            return [];
        }
        $conditions        = 'biz_type = :biz_type: and biz_value in ({biz_value:array}) and deleted = 0';
        $bind['biz_value'] = $params['audit_ids'];
        $bind['biz_type']  = $params['audit_type'];

        if (!empty($params['audit_state'])) {
            $conditions    .= ' and state = :state: ';
            $bind['state'] = $params['audit_state'];
        }

        //审批人
        if (!empty($params['approval_id'])) {
            $conditions          .= ' and approval_id = :approval_id: ';
            $bind['approval_id'] = $params['approval_id'];
        }

        return AuditApprovalModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ])->toArray();
    }

}