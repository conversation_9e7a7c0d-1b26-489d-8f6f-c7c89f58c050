<?php
/**
 * Author: Bruce
 * Date  : 2025-01-02 20:08
 * Description:
 */

namespace App\Repository;


use App\Library\BaseRepository;
use App\Models\backyard\SuspensionAuditModel;
use App\Models\backyard\SuspensionSignConfigModel;

class SuspensionRepository extends BaseRepository
{
    /**
     * 查询停职申请
     * @param $params
     * @param $columns
     * @return array
     */
    public static function getAudit($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['id'])) {
            $conditions .= ' and  id = :id:';
            $bind['id'] = $params['id'];
        }

        $data = SuspensionAuditModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ]);

        return !empty($data) ? $data->toArray() : [];
    }

    /**
     * 获取 停职申请列表
     * @param $params
     * @param array $columns
     * @return array
     */
    public function getAuditList($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['id'])) {
            $conditions .= ' and  id = :id:';
            $bind['id'] = $params['id'];
        }

        if (!empty($params['audit_states']) && is_array($params['audit_states'])) {
            $conditions           .= ' and audit_state in ({audit_states:array})';
            $bind['audit_states'] = $params['audit_states'];
        }

        if (!empty($params['is_not_status'])) {
            $conditions     .= ' and status != :status:';
            $bind['status'] = $params['is_not_status'];
        }

        if (!empty($params['stop_duties_date'])) {
            $conditions               .= ' and stop_duties_date <= :stop_duties_date:';
            $bind['stop_duties_date'] = $params['stop_duties_date'];
        }


        if (!empty($params['send_status']) && is_array($params['send_status'])) {
            $conditions          .= ' and send_status in ({send_status:array})';
            $bind['send_status'] = $params['send_status'];
        }

        return SuspensionAuditModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
            'order'      => 'id desc',
            'limit'      => 1000,
        ])->toArray();
    }

    public static function getSignConfigList($params = [], $columns = ['*'])
    {
        $conditions         = 'is_deleted = :is_deleted:';
        $bind['is_deleted'] = SuspensionSignConfigModel::IS_DELETED_NO;

        return SuspensionSignConfigModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ])->toArray();
    }

    /**
     * 获取 停职签字配置信息
     * @param $staffId
     * @param string $columns
     * @return array
     */
    public static function getSignConfigInfo($staffId, $columns = '*')
    {
        $data = SuspensionSignConfigModel::findFirst(
            [
                'columns'    => $columns,
                'conditions' => "staff_info_id = :staff_info_id: and is_deleted = :is_deleted:",
                'bind'       => [
                    'staff_info_id' => $staffId,
                    'is_deleted'    => SuspensionSignConfigModel::IS_DELETED_NO,
                ],
            ]
        );

        return empty($data) ? [] : $data->toArray();
    }
}