<?php
/**
 * Author: Bruce
 * Date  : 2025-05-21 21:23
 * Description:
 */

namespace App\Repository;


use App\Library\BaseRepository;
use App\Models\backyard\VanContainerModel;

class VanContainerRepository extends BaseRepository
{
    public static function getOne($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['id'])) {
            $conditions .= ' and  id = :id:';
            $bind['id'] = $params['id'];
        }

        $data = VanContainerModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ]);

        return !empty($data) ? $data->toArray() : [];
    }

    public static function getList($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['ids'])) {
            $conditions .= ' and  id in ({ids:array})';
            $bind['ids'] = $params['ids'];
        }

        return VanContainerModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ])->toArray();
    }
}