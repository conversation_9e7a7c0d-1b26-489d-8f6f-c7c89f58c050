<?php
/**
 * Author: Bruce
 * Date  : 2023-08-10 20:43
 * Description:
 */

namespace App\Repository;


use App\Library\BaseRepository;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use app\models\backyard\ToolStaffInfoPositionModel;

class HrStaffInfoPositionRepository extends BaseRepository
{
    /**
     * 获取指定员工角色
     * @param $staffId
     * @return array
     */
    public static function getHrStaffRoles($staffId)
    {
        $position = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $staffId,
            ],
        ])->toArray();

        return empty($position) ? [] : array_column($position, 'position_category');
    }

    /**
     * 获取多个员工 角色
     * @param $staffIds
     * @return array
     */
    public static function getStaffsBatch($staffIds)
    {
        $positions = HrStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id in ({staff_ids:array})',
            'bind'       => [
                'staff_ids' => $staffIds,
            ],
            'columns'    => 'staff_info_id, group_concat(position_category) as position_category',
            'group'      => 'staff_info_id',
        ])->toArray();

        return empty($positions) ? [] : array_column($positions, 'position_category', 'staff_info_id');
    }

    /**
     * 工具号角色
     * @param $staffIds
     * @return array
     */
    public static function getToolStaffPositionInfo($staffIds)
    {
        $positions = ToolStaffInfoPositionModel::find([
            'conditions' => 'staff_info_id in ({staff_ids:array})',
            'bind'       => [
                'staff_ids' => $staffIds,
            ],
            'columns'    => 'staff_info_id, group_concat(position_category) as position_category',
            'group'      => 'staff_info_id',
        ])->toArray();

        return empty($positions) ? [] : array_column($positions, 'position_category', 'staff_info_id');
    }


    /**
     * 获取员工信息
     * @param array $params
     * @param array $columns
     * @return mixed
     */
    public function getStaffList($params = [], $columns = ['*'])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['hsip' => HrStaffInfoPositionModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = hsip.staff_info_id', 'hsi');
        $builder = $this->getBuilderWhere($builder, $params);

        return $builder->getQuery()->execute()->toArray();
    }

    public function getBuilderWhere($builder, $params)
    {
        $builder->where('hsi.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO]);

        if (!empty($params['formal'])) {
            $builder->andWhere('hsi.formal in ({formal:array})', ['formal' => $params['formal']]);
        }

        //非离职状态
        if (!empty($params['not_leave'])) {
            $builder->andWhere('hsi.state != :not_leave:', ['not_leave' => $params['not_leave']]);
        }

        //roles
        if (!empty($params['roles'])) {
            $builder->andWhere('hsip.position_category in ({roles:array})', ['roles' => $params['roles']]);
        }

        //staff_ids
        if (!empty($params['staff_ids'])) {
            $builder->andWhere('hsip.staff_info_id in ({staff_ids:array})', ['staff_ids' => $params['staff_ids']]);
        }

        //在职，待离职
        if (!empty($params['on_job_wait'])) {
            $builder->andWhere('hsi.state = :on_job_wait:', ['on_job_wait' => $params['on_job_wait']]);
        }

        return $builder;
    }

}