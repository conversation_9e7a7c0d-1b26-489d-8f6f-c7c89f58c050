<?php
/**
 * Created by PhpStorm.
 * User: guo<PERSON><PERSON>o
 * Date: 2020/12/23
 * Time: 10:52 AM
 */
namespace App\Services;

class GongziTax2022Service extends GongziTax2021Service{
    /**
     * 计算 'Tax(Compensation/Retirement)'=>'tax_compensation' 配置
     * TAX_CONFIG_COMPENSATION_DISABILITY  TAX_CONFIG_COMPENSATION_DISABILITY_NOT   FIXED_INCOME_TAX_DISABILITY  FIXED_INCOME_TAX_DISABILITY_NOT
     * DISABILITY 残疾   DISABILITY_NOT 不残疾
     */
    const TAX_CONFIG_COMPENSATION_DISABILITY = [
        [5000000,0.35,487000],
        [2000000,0.3,237000],
        [1000000,0.25,137000],
        [750000,0.2,87000],
        [500000,0.15,49500],
        [300000,0.1,24500],
        [190000,0.05,9500],
    ];
    /**
     * 若员工不是残疾人
     */
    const TAX_CONFIG_COMPENSATION_DISABILITY_NOT = [
        [5000000,0.35,485000],
        [2000000,0.3,235000],
        [1000000,0.25,135000],
        [750000,0.2,85000],
        [500000,0.15,47500],
        [300000,0.1,22500],
        [150000,0.05,7500],
    ];
    const TAX_CONFIG_RETIREMENT_OVER5Y = [
        [300000, 0.05],
        [500000, 0.1],
        [750000, 0.15],
        [1000000, 0.2],
        [2000000, 0.25],
        [5000000, 0.3],
        [999999999999, 0.35],
    ];

    /*
     * 新的算法和transfer 有关,  和 是否离职有关
     */
    public function cal_fix_year_income(&$realRowInfo)
    {
        //年固定收入 = 历史收入 + 本月实际收入 + 本月预估*剩余月份
        if(empty($realRowInfo['transfer_day'])){
            $income_fixed_year_total = $realRowInfo['fixed_salary_before'] +
                ($realRowInfo['salary'] + $realRowInfo['position'] + $realRowInfo['experience'] + $realRowInfo['notebook'] + $realRowInfo['house'] + $realRowInfo['firestarer_award'] + $realRowInfo['island'] + $realRowInfo['gasoline'] + $realRowInfo['disability'] + $realRowInfo['other_income_fix1'] + $realRowInfo['other_income_fix2']+ $realRowInfo['other_income_fix3']+ $realRowInfo['other_income_fix4'] - $realRowInfo['other_deduct_beforetax1'] - $realRowInfo['other_deduct_beforetax2'] ) +
                $realRowInfo['tax_month'] * ( $realRowInfo['salary_base'] + $realRowInfo['position_base'] + $realRowInfo['experience_base'] + $realRowInfo['notebook_base'] + $realRowInfo['house_base'] + $realRowInfo['island_base'] + $realRowInfo['gasoline_base'] + $realRowInfo['disability_base'] + $realRowInfo['firestarer_award_base']);
        }else{
            $income_fixed_year_total = $realRowInfo['fixed_salary_before'] +
                ($realRowInfo['salary'] + $realRowInfo['position'] + $realRowInfo['experience'] + $realRowInfo['notebook'] + $realRowInfo['house'] + $realRowInfo['firestarer_award'] + $realRowInfo['island'] + $realRowInfo['gasoline'] + $realRowInfo['disability'] + $realRowInfo['other_income_fix1'] + $realRowInfo['other_income_fix2']+ $realRowInfo['other_income_fix3']+ $realRowInfo['other_income_fix4'] - $realRowInfo['other_deduct_beforetax1'] - $realRowInfo['other_deduct_beforetax2'] ) +
                $realRowInfo['tax_month'] * ( $realRowInfo['salary_adj'] + $realRowInfo['position_adj'] + $realRowInfo['experience_adj'] + $realRowInfo['notebook_base'] + $realRowInfo['house_base'] + $realRowInfo['island_base'] + $realRowInfo['gasoline_base'] + $realRowInfo['disability_base'] + $realRowInfo['firestarer_award_base']);
        }
        return $income_fixed_year_total;
    }
    /*
     * 区别:  如果离职 , 社保不乘剩余月份
     */
    public function cal_tax_all(&$realRowInfo)
    {
        //之前社保和 + 本月社保 + 预估以后社保和(如果已离职 并且小于等于24号,tax month=0)
        $realRowInfo['total_shebao'] = $realRowInfo['social_before_this_month'] + $realRowInfo['social'] + $realRowInfo['social'] * $realRowInfo['tax_month'];

        //年固定收入
        $income_fixed_year_total = $this->cal_fix_year_income($realRowInfo);
        $income_fixed_year_total = round($income_fixed_year_total,2);
        $realRowInfo['fixed year income'] = $income_fixed_year_total;

        /*
         * 3.1.4	租房费		（不是每人都有）
3.1.5	电脑补贴		（不是每人都有）
3.2	不固定薪资是除了固定薪资以外的收入，当月计算当月应纳税所得额
3.2.1	餐补              		Food Allowance ok
3.2.2	租车费         		Car Rental ok
3.2.3	危险费         		Risk Allowance  ok
3.2.4	出车费        		Trip payment ok
3.2.5	LH 费            		LH ok 移除 （special_lh 是2020年才有的）
3.2.6	Special LH ok
3.2.7	加班费 		OT ok
3.2.8	提成			Incentive ok
3.2.9	介绍费		Recommended fee ok
3.2.10	其他收入		Other Income ?
3.2.11	每一项的补发	XXXXXX Backward
        food = 15*45 = 675
        31205.95

         */
        $income_not_fixed =  $this->cal_unfixed_tax_base($realRowInfo);

        //年固定收入 + 本月非固定收入 + 历史非固定收入
        $income_all_year_total = $income_fixed_year_total + $income_not_fixed + $realRowInfo['unfixed_salary_before'];
//        $tax_exempt_consumption = bcdiv($income_all_year_total,2,2);
        //消费抵扣,年收入的一半,最大100000
        $tax_exempt_consumption = round($income_all_year_total/2,2);
        if($tax_exempt_consumption<100000){
            $realRowInfo['tax_exempt_consumption'] = ($tax_exempt_consumption<0)?0:$tax_exempt_consumption;
        }else{
            $realRowInfo['tax_exempt_consumption'] = 100000;
        }

        $realRowInfo['tax_exempt_total'] = $realRowInfo['tax_exempt_consumption'] + 60000 + $realRowInfo['total_shebao'] +  $realRowInfo['tax_deduct'];


        //算固定年收入的年度税
        [$realRowInfo['fixed_tax_base'], $tax_fixed_year_tatal,$realRowInfo['fixed_tax_rate']] = $this->cal_tax_byyear($income_fixed_year_total, $realRowInfo['tax_exempt_total'],$realRowInfo['disability_status']);

        //算固定年收入的月税 和 余数
        $tax_month_two = 0;
        $tax_month_yushu = 0;
        if ($tax_fixed_year_tatal > 0) {
            $tax_month_three = bcdiv($tax_fixed_year_tatal - $realRowInfo['fixed_tax_before'], $realRowInfo['tax_month']+1, 3);
            $tax_month_two = bcdiv($tax_fixed_year_tatal - $realRowInfo['fixed_tax_before'], $realRowInfo['tax_month']+1, 2);
            $tax_month_yushu = bcsub($tax_month_three, $tax_month_two, 3);
            if ($tax_month_yushu == '0.000') {
                $tax_month_yushu = 0;
            }
        }
        if($tax_month_two<0){
            $tax_month_two = 0;
        }
        $realRowInfo['fixed_tax'] = $tax_month_two;

        //计算本月非固定收入的税
        if($realRowInfo['unfixed_income'] <= 0){
            $tax_notfix = 0;
            $realRowInfo['unfixed_tax_rate'] = 0;
            //unfixed_tax_base 的含义其实是 Fixed Tax Base + Unfixed Tax Base(unfixed_income) 210830 hy 加上unfixed_income
            $realRowInfo['unfixed_tax_base'] = round($realRowInfo['fixed_tax_base'] + $realRowInfo['unfixed_salary_before'] + $realRowInfo['unfixed_income'],2);
        }else{
            //算总的年度税
            [$realRowInfo['unfixed_tax_base'], $tax_all_year_tatal,$realRowInfo['unfixed_tax_rate']] = $this->cal_tax_byyear($income_all_year_total, $realRowInfo['tax_exempt_total'],$realRowInfo['disability_status']);
            //计算非固定收入的月税
            //如果离职或是12月  非固定税 = 全年税 - 本月固定 - 历史固定 - 历史非固定
            if($realRowInfo['tax_month'] == 0){
                $tax_notfix = round($tax_all_year_tatal - $realRowInfo['fixed_tax'] - $realRowInfo['fixed_tax_before'] - $realRowInfo['unfixed_tax_before'], 2);
            }else{
                //未离职:  非固定税 = 全年税 - 年固定税 - 历史非固定
                $tax_notfix = round($tax_all_year_tatal - $tax_fixed_year_tatal - $realRowInfo['unfixed_tax_before'], 2);
            }

            if($tax_notfix<0){
                $tax_notfix = 0;
            }
        }

        $realRowInfo['unfixed_tax'] = $tax_notfix;
        $realRowInfo['tax'] = $tax_month_two + $tax_notfix;
        if($realRowInfo['tax']<0){
            $realRowInfo['tax'] = 0;
        }
        $realRowInfo['tax_yushu'] = $tax_month_yushu;
        if($realRowInfo['tax_yushu']<0){
            $realRowInfo['tax_yushu'] = 0;
        }

//        echo 'last tax:' . $realRowInfo['tax'] . ' yushu:' . $realRowInfo['tax yushu'] . "\n";

        if($realRowInfo['compensation']>0){
            $realRowInfo['tax_compensation'] = $this->cal_compensation_tax($realRowInfo);
            $realRowInfo['tax'] = $realRowInfo['tax'] - $realRowInfo['tax_compensation'];
        }
        if($realRowInfo['retirement_over5y']>0){
            $this->cal_retirement_over5y_tax($realRowInfo);
        }
    }

    /**
     * 工资表 Tax(Compensation/Retirement)
     * tax_compensation = unfixed_tax - ($tax1 -$tax2 - unfixed_tax_before)
     * $tax1 不含赔偿金的tax合计   $tax2  固定收入税  unfixed_tax_before 往月非固定个税累计
     * @param $realRowInfo
     * @return float
     */
    private function cal_compensation_tax($realRowInfo): float
    {
        $tax1 =  $tax2 = 0;
        $config = $realRowInfo['disability_status'] == 1 ?  self::TAX_CONFIG_COMPENSATION_DISABILITY_NOT :self::TAX_CONFIG_COMPENSATION_DISABILITY ;
        foreach ($config as $taxInfo){
            if (($realRowInfo['unfixed_tax_base'] - $realRowInfo['compensation']) > $taxInfo[0]) {
                $tax1 =  round(($realRowInfo['unfixed_tax_base'] - $realRowInfo['compensation']) * $taxInfo[1] - $taxInfo[2],2);
                break;
            }
        }
        foreach ($config as $taxInfo){
            if ($realRowInfo['fixed_tax_base'] > $taxInfo[0]) {
                $tax2 =  round($realRowInfo['fixed_tax_base'] * $taxInfo[1] - $taxInfo[2],2);
                break;
            }
        }
        $this->logger->info('cal_compensation_tax' . json_encode($realRowInfo));
        return max(round($realRowInfo['unfixed_tax'] - max(($tax1 - $tax2 - $realRowInfo['unfixed_tax_before']),0),2),0);
    }

    private function cal_retirement_over5y_tax(&$realRowInfo)
    {
        //1是残疾人 2不是残疾人
        if ($realRowInfo['disability_status'] == 1) {
            $retirement_over5y_base = ($realRowInfo['retirement_over5y'] - $realRowInfo['working_years'] * 7000) * 0.5;
        } else {
            $retirement_over5y_base = ($realRowInfo['retirement_over5y'] - 190000 - $realRowInfo['working_years'] * 7000) * 0.5;
        }
        if ($retirement_over5y_base < 0) {
            $retirement_over5y_base = 0;
        }
        $realRowInfo['tax_retirement_over5y_base'] = $retirement_over5y_base;
        $realRowInfo['tax_retirement_over5y'] = 0;
        if ($retirement_over5y_base > 0) {
            foreach (self::TAX_CONFIG_RETIREMENT_OVER5Y as $taxInfo) {
                if ($retirement_over5y_base <= $taxInfo[0]) {
                    $realRowInfo['tax_retirement_over5y'] = round($retirement_over5y_base * $taxInfo[1], 2);
                    break;
                }
            }
        }
    }

    //cal tax base,这个和算税没关系,独立项,用于报税用
    public function cal_tax_base_field(&$realRowInfo){
        $realRowInfo['tax_base'] = round($realRowInfo['total_income'] -  $realRowInfo['performance_deduct'] - $realRowInfo['other_deduct_beforetax1'] -$realRowInfo['other_deduct_beforetax2'] - $realRowInfo['other_deduct_beforetax3'] -  $realRowInfo['compensation'] - $realRowInfo['retirement_over5y'],2);
        if($realRowInfo['tax_base']<0){
            $realRowInfo['tax_base'] = 0;
        }
    }

    //如果计算明年1月份工资,历史收入为0, 这里查出来为空
    public function getFixSalaryData($gongzi_month)
    {
        $year = substr($gongzi_month, 0, 4);
//        $max_month= $year.'-12';
        $min_month = $year . '-01';

        $sql = "select staff_info_id as staff_info_id,
       min(excel_month) as first_month,
       sum(salary)+ sum(`position`)+ sum(experience)+ sum(notebook)+ sum(house)+ sum(island)+ sum(gasoline)+ sum(disability)+ 
  sum(other_income_fix1)+ sum(other_income_fix2) +sum(other_income_fix3)+sum(other_income_fix4) + IFNULL(sum(firestarer_award),0)
    - sum(other_deduct_beforetax1) - sum(other_deduct_beforetax2)
 as fixed_salary_before 
  from `salary_gongzi`
 where 
   excel_month>= '" . $min_month . "'
   and excel_month< '" . $gongzi_month . "'
   and (salary + `position` +experience + notebook + house + island + gasoline + disability +other_income_fix1 + other_income_fix2 + other_income_fix3 + other_income_fix4 + IFNULL(firestarer_award,0)  - other_deduct_beforetax1 - other_deduct_beforetax2)>0 
 GROUP BY staff_info_id";

        $result = $this->db_rby->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        return array_column($result, null, 'staff_info_id');
    }

    public function getUnfixSalaryData($gongzi_month)
    {
        $year = substr($gongzi_month, 0, 4);
        $min_month = $year . '-01';

            $sql = "select staff_info_id,
           sum(food)+ sum(car)+ sum(`risk`)+ sum(lh)
           +sum(night_shift) + sum(ot)+ sum(incentive) 
           +sum(other_income) +sum(compensation_incentive) + sum(bonus) + sum(compensation)
       +sum(other_income_unfix1) +sum(other_income_unfix2) + sum(other_income_unfix3)  + sum(site_allowance) - sum(performance_deduct) 
       - sum(other_deduct_beforetax3)
     as unfixed_salary_before 
      from `salary_gongzi`
     where 
       excel_month>= '" . $min_month . "'
       and excel_month< '" . $gongzi_month . "'
     GROUP BY staff_info_id";

        $result = $this->db_rby->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        return array_column($result, null, 'staff_info_id');
    }
    /**
     * 计算 非固定薪酬纳税基数
     * @param $realRowInfo
     * @return float
     */
    public function cal_unfixed_tax_base(&$realRowInfo): float
    {
        $income_not_fixed =  $realRowInfo['food'] + $realRowInfo['risk'] + $realRowInfo['night_shift'] + $realRowInfo['special_lh'] + $realRowInfo['ot'] + $realRowInfo['incentive'] + $realRowInfo['recommended'] + $realRowInfo['site_allowance'] + $realRowInfo['car']+ $realRowInfo['compensation_incentive']+ $realRowInfo['bonus']+ $realRowInfo['compensation']+ $realRowInfo['other_income_unfix1']+ $realRowInfo['other_income_unfix2']+ $realRowInfo['other_income_unfix3'] - $realRowInfo['performance_deduct'] - $realRowInfo['other_deduct_beforetax3'];
        $income_not_fixed = round($income_not_fixed,2);
        $realRowInfo['unfixed_income'] = $income_not_fixed;
        return $income_not_fixed;
    }
}
