<?php


namespace App\Services;

use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums\ConditionsRulesEnums;
use App\Library\Enums\SchedulingSuggestionEnums;
use App\Library\Exception\BusinessException;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\SchedulingSuggestionDaysModel;
use App\Models\backyard\SchedulingSuggestionModel;
use App\Models\backyard\SchedulingSuggestionRuleModel;
use App\Models\backyard\StaffDefaultRestDayModel;
use App\Repository\HrStaffShiftMiddleDateRepository;

/**
 * 5&6天班轮休 设置默认休息日
 * Class DefaultRestDayService
 * @package App\Services
 */
class DefaultRestDayService extends BaseService
{
    /**
     * 获取轮休 默认休息日 list
     * @param array $staffIds
     * @return array
     */
    public function getAll($staffIds = []): array
    {
        $data = [];
        if (!$staffIds) {
            return $data;
        }
        $find = StaffDefaultRestDayModel::find([
            'columns'    => 'staff_info_id,rest_day',
            'conditions' => 'staff_info_id in ({ids:array}) and is_deleted=0',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        foreach (static::yieldData()($find) as $item) {
            $data[$item['staff_info_id']] = str_split($item['rest_day']);
        }
        return $data;
    }

    /**
     * 设置轮休 默认休息日
     * @throws BusinessException
     * @throws \Exception
     */
    public function editDefaultRestDayDate($staffId, $defaultRestDayDate, $operateId = 10000, $extend = []): bool
    {
        $first = HrStaffInfoModel::findFirst([
            'columns'    => 'week_working_day,rest_type,sys_store_id,job_title',
            'conditions' => 'staff_info_id=:staff_id:',
            'bind'       => ['staff_id' => $staffId],
        ]);
        if (!$first) {
            throw new BusinessException('Staff ID Error');
        }
        //验证 是否有修改truck 的权限
        $workdayServer = new WorkdayService();
        $workdayServer = reBuildCountryInstance($workdayServer);
        $flag = $workdayServer->checkTruckUpdatePermission([$staffId], $operateId);
        if(!$flag){
            $key = empty($extend['is_import']) ? 'workday_truck_permission' : 'work_shift_truck_permission';
            throw new BusinessException(self::$t->_($key));
        }

        if($operateId != 10000 && $staffId == $operateId){
            //新增规则配置校验 能否修改自己的休息日
            $ruleParam['staff_info_id'] = $operateId;
            $ruleParam['rule_key']      = 'Allow_set_selfOff';
            $rules                      = $this->getConditionRule(self::$language, $ruleParam);
            //- N or 空：不允许修改自己，给出如下提示
            if (empty($rules['data']) || $rules['data']['response_type'] != ConditionsRulesEnums::RESPONSE_TYPE_VALUE || !$rules['data']['response_data']) {
                throw new BusinessException(self::$t->_('can_not_edit_self_default_off'));
            }
        }

        if (!in_array($first->week_working_day,
                [HrStaffInfoModel::WEEK_WORKING_DAY_FIVE, HrStaffInfoModel::WEEK_WORKING_DAY_SIX,HrStaffInfoModel::WEEK_WORKING_DAY_FREE])
            || $first->rest_type != HrStaffInfoModel::REST_TYPE_1) {
            throw new BusinessException('Staff must be ' . static::$t->_('working_day_rest_type_51') . ' or ' . static::$t->_('working_day_rest_type_61') . ' or ' . static::$t->_('working_day_rest_type_91'));
        }
        if ($first->week_working_day == HrStaffInfoModel::WEEK_WORKING_DAY_FIVE && count($defaultRestDayDate) != 2) {
            throw new BusinessException(static::$t->_('choose_default_rest_day_2'));
        }
        if ($first->week_working_day == HrStaffInfoModel::WEEK_WORKING_DAY_SIX && count($defaultRestDayDate) != 1) {
            throw new BusinessException(static::$t->_('choose_default_rest_day_1'));
        }
        $SDRDFirst = StaffDefaultRestDayModel::findFirst([
            'conditions' => 'staff_info_id =:id: and is_deleted=0',
            'bind'       => ['id' => $staffId],
        ]);
        //检查是否与排班建议冲突
        $restDayAndSchedulingSuggest = false;
        $message                     = '';
        $startDeleteTime             = date('Y-m-d', strtotime(' +1 day'));
        //六天班轮休
        if ($first->week_working_day == HrStaffInfoModel::WEEK_WORKING_DAY_SIX
            && $first->rest_type == HrStaffInfoModel::REST_TYPE_1
        ) {
            $N = current($defaultRestDayDate);
            [$restDayAndSchedulingSuggest, $message] = $this->checkRestDayAndSchedulingSuggest(
                $first->sys_store_id,
                $first->job_title, $N);
            $this->logger->info([
                $staffId,
                $defaultRestDayDate,
                $first->sys_store_id,
                $first->job_title,
                $restDayAndSchedulingSuggest,
                $message,
            ]);
            //设置的许休息日已经过去，不删除当周的休息日，从下周一开始删除
            if ($N <= date('N')) {
                $offsetDay       = 7 - date('N') + 1;
                $startDeleteTime = date('Y-m-d', strtotime("+{$offsetDay} days"));
            }
        }
        $rest_day = implode('', $defaultRestDayDate);
        $rest_day = empty($rest_day) ? 0 : $rest_day;
        $old = '';
        if ($SDRDFirst) {
            $old                 = $SDRDFirst->rest_day;
            $SDRDFirst->rest_day = $rest_day;
            $SDRDFirst->update();
        }
        if (!$SDRDFirst) {
            $model                = new StaffDefaultRestDayModel;
            $model->rest_day      = $rest_day;
            $model->staff_info_id = $staffId;
            $model->save();
        }
        $logServer = new WorkShiftService();
        if (!$restDayAndSchedulingSuggest) {
            //不能删除的备注类型
            $disable_del_remark = HrStaffWorkDaysModel::SET_DEFAULT_REST_DAY_DISABLE_DEL_REMARK;
            $workData = HrStaffWorkDaysModel::find([
                'conditions' => 'staff_info_id=:staff_id: and date_at>=:time: and remark not in ({remark:array})  ',
                'bind'       => ['staff_id' => $staffId, 'time' => $startDeleteTime, 'remark' => $disable_del_remark],
            ]);
            //先加日志 然后删
            $logData = $workData->toArray();
            if(!empty($logData)){
                $this->logger->info(['editDefaultRestDayDate' => $logData]);
                $logData = $logServer->formatWorkdayModel($logData, $operateId);
                $logServer->addShiftLogBatch(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF,$logData, ['t_key' => 'workday_default_rest']);
            }
            $workData->delete();
            $message = $this->setALl([$staffId]);
            $this->logger->info($message);
        }
        if ($restDayAndSchedulingSuggest) {
            throw new BusinessException($message);
        }
        $defaultLog['staff_info_id'] = $staffId;
        $defaultLog['date_at']       = date('Y-m-d');
        $defaultLog['operate_id']    = $operateId;
        $extend['before']            = empty($old) ? '' : $old;
        $extend['after']             = empty($rest_day) ? '' : $rest_day;
        $extend['t_key']             = 'work_shift_log_type_default_rest';
        $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_DEFAULT_REST, $defaultLog, $extend);
        return true;
    }

    /**
     * 检查是否与排班建议人数冲突
     * @param $storeId
     * @param $jobTitle
     * @param $defaultRestDayDate
     * @return array
     */
    public function checkRestDayAndSchedulingSuggest($storeId, $jobTitle, $defaultRestDayDate): array
    {
        [$jobTitles, $positionType] = (new SchedulingSuggestionService())->getSchedulingSuggestJobTitle($jobTitle);
        if (empty($jobTitles)) {
            return [false, ''];
        }
        $staffInfo = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'state=:state: and formal in ({formal:array}) and is_sub_staff=:is_sub_staff:  and sys_store_id=:store_id: and job_title in ({job_title:array})  and week_working_day=:week_working_day: and rest_type=:rest_type:',
            'bind'       => [
                'state'            => HrStaffInfoModel::STATE_ON_JOB,
                'formal'           => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_NO,
                'store_id'         => $storeId,
                'job_title'        => $jobTitles,
                'week_working_day' => HrStaffInfoModel::WEEK_WORKING_DAY_SIX,
                'rest_type'        => HrStaffInfoModel::REST_TYPE_1,
            ],
        ])->toArray();
        //计算是本周日期还是下周日期
        $staffIds = array_column($staffInfo, 'staff_info_id');
        if (empty($staffIds)) {
            return [false, ''];
        }
        $weekN  = date('N');
        $offset = $defaultRestDayDate - 1;
        if ($weekN >= $defaultRestDayDate) {
            $monday = date('Y-m-d', strtotime('Monday next week'));
        } else {
            $monday = date('Y-m-d', strtotime('Monday this week'));
        }
        $judgeDate      = date('Y-m-d', strtotime("$offset days", strtotime($monday)));
        $suggestDaysObj = SchedulingSuggestionDaysModel::findFirst([
            'conditions' => 'is_deleted=0 and store_id=:store_id:  and position_type=:position_type: and stat_date=:stat_date:',
            'bind'       => [
                'store_id'      => $storeId,
                'position_type' => $positionType,
                'stat_date'     => $judgeDate,
            ],
        ]);
        $message        = '';
        if (!$suggestDaysObj) {
            return [false, $message];
        }
        $onJob            = count($staffIds);
        $suggestionNumber = $suggestDaysObj->suggest_number;
        $offCounts        = HrStaffWorkDaysModel::count([
            'conditions' => 'staff_info_id in ({ids:array}) and date_at=:date_at:',
            'bind'       => ['ids' => $staffIds, 'date_at' => $judgeDate],
        ]);
        $this->logger->info([
            $storeId,
            $jobTitle,
            $defaultRestDayDate,
            $onJob,
            $judgeDate,
            $suggestionNumber,
            $offCounts + 1,
        ]);
        //能休息的天数
        $realOffNumber = max(count($staffIds) - $suggestionNumber, 0);
        if (($offCounts + 1) > $realOffNumber) {
            $weekDay       = static::$t->_('default_rest_day_' . $defaultRestDayDate);
            $MessageParams = ['week_day' => $weekDay, 'date' => $judgeDate, 'n' => $realOffNumber];
            $message       = static::$t->_('default_rest_scheduling_suggest', $MessageParams);
            return [true, $message];
        }
        return [false, $message];
    }

    /**
     * 检查是否与排班建议冲突
     * @param $storeId
     * @param $jobTitle
     * @param $offDayModel 要轮休的数据表 数组
     * @return array
     */
    public function checkOffDayAndSchedulingSuggest($storeId, $jobTitle, $offDayModel): array
    {
        if (empty($offDayModel)) {
            return [];
        }
        $dateList = array_column($offDayModel, 'date_at');

        [$jobTitles, $positionType] = (new SchedulingSuggestionService())->getSchedulingSuggestJobTitle($jobTitle);
        if (empty($jobTitles)) {
            return $offDayModel;
        }
        $staffInfo = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'state=:state: and formal in ({formal:array}) and is_sub_staff = :is_sub_staff:  and sys_store_id = :store_id: and job_title in ({job_title:array})  and week_working_day=:week_working_day: and rest_type=:rest_type:',
            'bind'       => [
                'state'            => HrStaffInfoModel::STATE_ON_JOB,
                'formal'           => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'is_sub_staff'     => HrStaffInfoModel::IS_SUB_STAFF_NO,
                'store_id'         => $storeId,
                'job_title'        => $jobTitles,
                'week_working_day' => HrStaffInfoModel::WEEK_WORKING_DAY_SIX,
                'rest_type'        => HrStaffInfoModel::REST_TYPE_1,
            ],
        ])->toArray();
        //计算是本周日期还是下周日期
        $staffIds = array_column($staffInfo, 'staff_info_id');
        if (empty($staffIds)) {
            return $offDayModel;
        }
        //在职人数
        $onJob = count($staffIds);

        //off人数
        $offCounts = HrStaffWorkDaysModel::find([
            'columns'    => 'count(*) as count,date_at',
            'conditions' => 'staff_info_id in ({ids:array}) and date_at in ({dates:array})',
            'group'      => 'date_at',
            'bind'       => ['ids' => $staffIds, 'dates' => $dateList],
        ])->toArray();

        if (!empty($offCounts)) {
            $offCounts = array_column($offCounts, 'count', 'date_at');
        }
        //建议数据
        $suggestData = SchedulingSuggestionDaysModel::find([
            'conditions' => 'is_deleted = 0 and store_id= :store_id:  and position_type = :position_type: and stat_date in ({dates:array})',
            'bind'       => [
                'store_id'      => $storeId,
                'position_type' => $positionType,
                'dates'         => $dateList,
            ],
        ])->toArray();
        //没配置
        if (empty($suggestData)) {
            return $offDayModel;
        }
        $suggestDays = array_column($suggestData, 'suggest_number', 'stat_date');
        foreach ($offDayModel as $k => $item) {
            $date   = $item['date_at'];
            $offNum = $offCounts[$date] ?? 0;
            //实际出勤人数
            $actNum = $onJob - $offNum;

            //出勤人数 -1(当前员工算一个要休息) 小于建议出勤人数 不能操作off
            if (!empty($suggestDays[$date]) && ($actNum - 1) < $suggestDays[$date]) {
                unset($offDayModel[$k]);
            }
        }
        return array_values($offDayModel);
    }


    /**
     * 因删除或者调休不应在设置休息日的周
     * @param $startTime
     * @param $endTime
     * @param $staffInfoIds
     * @return array
     */
    public function getShouldNotSetOffWeeks($startTime, $endTime, $staffInfoIds): array
    {
        $returnStaffWeek = [];
        $conditions      = 'date_at >=:start: and date_at<=:end:';
        if ($staffInfoIds) {
            $conditions  = 'staff_info_id in ({ids:array}) and date_at >=:start: and date_at<=:end:';
            $bind['ids'] = $staffInfoIds;
        }
        $bind['start'] = $startTime;
        $bind['end']   = $endTime;
        $find          = HrStaffWorkDaysChangeModel::find([
            'columns'    => 'staff_info_id,date_at',
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
        foreach (static::yieldData()($find) as $item) {
            $W                                           = date('W', strtotime($item['date_at']));
            $returnStaffWeek[$item['staff_info_id']][$W] = true;
        }
        return $returnStaffWeek;
    }

    /**
     * 设置 5&6天轮休 自由轮休 默认休息日
     * @param array $staffInfoIds
     * @param string $startTime
     * @param string $endTime
     * @return string
     * @throws \Exception
     */
    public function setALl($staffInfoIds = [], $startTime = '', $endTime = ''): string
    {
        $operate_id = 10000;
        $startTime  = empty($startTime) ? DateHelper::weekStart(date('Y-m-d')) : DateHelper::weekStart($startTime);
        $endTime    = empty($endTime)
            ? DateHelper::weekEnd(date('Y-m-d', strtotime(date('Y-m-01') . ' last day of +1 months')))
            : DateHelper::weekEnd($endTime);
        $bind       = [];
        $conditions = 'is_deleted=0';
        if ($staffInfoIds) {
            $conditions = 'staff_info_id in ({ids:array}) and is_deleted=0';
            $bind       = ['ids' => $staffInfoIds];
        }
        $schedulingSuggestionService = new SchedulingSuggestionService();
        $suggestJobTitle             = $schedulingSuggestionService->getJobTitle();
        $future14Day                 = date('Y-m-d', strtotime('+14 days'));

        // 获取 因 删除 或者调休不应在设置休息日的周
        $shouldNotSetOffWeeks = $this->getShouldNotSetOffWeeks($startTime, $endTime, $staffInfoIds);
        //轮休默认休息日
        $find       = StaffDefaultRestDayModel::find([
            'columns'    => 'staff_info_id,rest_day',
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
        $typeError  = [];
        $dayError   = [];
        $chunk      = array_chunk($find, 1000);
        $filterData = [];
        $daysMap    = [];
        //应该验证排班建议的员工
        $shouldValidateSchedulingSuggestionStaffIds = [];
        foreach ($chunk as $chunkItem) {
            $staffIds = array_column($chunkItem, 'staff_info_id');
            // type  51 五天班轮休 61 六天班轮休 91 自由轮休
            $staffFind = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,concat(week_working_day,rest_type) as type,date(leave_date) as leave_date,state,sys_store_id,job_title',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $staffIds],
            ])->toArray();
            $staffMap  = array_column($staffFind, null, 'staff_info_id');
            foreach ($chunkItem as $staffRestDay) {
                if (!isset($staffMap[$staffRestDay['staff_info_id']])) {
                    continue;
                }
                if ($staffMap[$staffRestDay['staff_info_id']]['state'] == HrStaffInfoModel::STATE_RESIGN
                    && strtotime($staffMap[$staffRestDay['staff_info_id']]['leave_date']) <= strtotime($startTime)) {
                    continue;
                }
                if (!in_array($staffMap[$staffRestDay['staff_info_id']]['type'], ['51', '61','91'])) {
                    $typeError[] = $staffRestDay['staff_info_id'];
                    continue;
                }
                $days = str_split($staffRestDay['rest_day']);
                if (
                    ($staffMap[$staffRestDay['staff_info_id']]['type'] == '51' && count($days) != 2)
                    || ($staffMap[$staffRestDay['staff_info_id']]['type'] == '61' && count($days) != 1)
                ) {
                    $dayError[] = $staffRestDay['staff_info_id'];
                    continue;
                }
                if ($staffMap[$staffRestDay['staff_info_id']]['type'] == '61') {
                    $shouldValidateSchedulingSuggestionStaffIds[] = $staffRestDay['staff_info_id'];
                }
                sort($days);
                $daysMap[]    = implode('', $days);
                $filterData[] = [
                    's'         => $staffRestDay['staff_info_id'],
                    'r'         => $days,
                    'type'      => $staffMap[$staffRestDay['staff_info_id']]['type'],//'51', '61','91'
                    'store_id'  => $staffMap[$staffRestDay['staff_info_id']]['sys_store_id'],
                    'job_title' => $staffMap[$staffRestDay['staff_info_id']]['job_title'],
                ];
            }
        }
        //$needCheckSchedulingSuggestOffDays 应该去校验排班建议的休息日
        $storeIds = $schedulingSuggestionJobTitle = $needCheckSchedulingSuggestOffDays = [];
        if ($shouldValidateSchedulingSuggestionStaffIds) {
            $storeIds                          = (new SchedulingSuggestionService())->getSuggestStoreFutureTime($shouldValidateSchedulingSuggestionStaffIds,
                date('Y-m-d', strtotime('+1 days')), $future14Day);
            $schedulingSuggestionJobTitle      = array_merge(...$suggestJobTitle);
        }
        //新增逻辑 如果配置轮休的员工对应日期 存在请假记录（任何类型请假都算）跳过对应日期的休息日 不配置
        $dateList      = DateHelper::DateRange(strtotime($startTime), strtotime($endTime));
        $leaveStaffIds = array_column($filterData, 's');
        $leaveBll      = new LeaveBllService();
        $leaveData     = $leaveBll->getLeaveByDate($leaveStaffIds, $dateList);
        $leaveData     = empty($leaveData) ? [] : array_column($leaveData, null, 'u_key');
        //星期几
        $daysMap        = array_unique($daysMap);
        $dateMap        = $this->getFixedDate($daysMap, $startTime, $endTime);
        $filterChunk    = array_chunk($filterData, 1000);
        $insertNumber   = 0;
        $handleCount    = count($filterData);
        $insertStoreIds = $batchInsert = [];

        $workdaySettingService = new WorkdaySettingService();


        //剔除不符合的
        foreach ($filterChunk as $filterItem) {
            //已经存在休息日的周
            $staffWorkDays = $this->getStaffWorkDays($filterItem, $startTime, $endTime);
            foreach ($filterItem as $staffItem) {
                $keyDay = implode('', $staffItem['r']);
                if (!isset($dateMap[$keyDay])) {
                    continue;
                }
                foreach ($dateMap[$keyDay] as $key => $restDay) {
                    $W = date('W', strtotime($restDay));
                    //排除已经存在休息日的周
                    if (isset($staffWorkDays[$staffItem['s']][$W])) {
                        continue;
                    }
                    //仅设置未来的
                    if ($restDay <= date('Y-m-d')) {
                        continue;
                    }
                    //因 删除 或者调休不应在设置休息日的周
                    if (isset($shouldNotSetOffWeeks[$staffItem['s']][$W])) {
                        continue;
                    }
                    //排除请假的数据
                    $leaveKey = "{$restDay}_{$staffItem['s']}";
                    if (!empty($leaveData[$leaveKey])) {
                        continue;
                    }
                    if (isCountry('MY')) {
                        if (in_array($staffItem['type'], ['61','91'])) {
                            $insert['type'] = HrStaffWorkDaysModel::TYPE_2;
                        } else {
                            // 偶数是设置的每周第一天
                            if ($key % 2 == 0) {
                                $insert['type'] = HrStaffWorkDaysModel::TYPE_1;
                            } else {
                                $insert['type'] = HrStaffWorkDaysModel::TYPE_2;
                            }
                        }
                    }
                    //菲律宾自由轮休 轮休配置 验证
                    if (isCountry('PH') && $staffItem['type'] == '91') {
                        //看看本周的
                        $flag = $workdaySettingService->checkDefaultOffDay(strlen($keyDay), $staffItem['store_id'],
                            $staffItem['job_title'], $restDay);
                        if (empty($flag)) {
                            continue;
                        }
                    }

                    $insert['staff_info_id'] = $staffItem['s'];
                    $insert['month']         = substr($restDay, 0, 7);
                    $insert['date_at']       = $restDay;
                    $insert['remark']        = 'DRD';// default rest day
                    //需要既要排班建议
                    if ($staffItem['type'] ==  '61'
                        && in_array($staffItem['store_id'], $storeIds)
                        && in_array($staffItem['job_title'], $schedulingSuggestionJobTitle)
                        && $restDay <= $future14Day
                    ) {
                        $unique                                       = $staffItem['store_id'] . '_' . (in_array($staffItem['job_title'],
                                $suggestJobTitle[SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER]) ? SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER : SchedulingSuggestionEnums::POSITION_TYPE_COURIER);
                        $needCheckSchedulingSuggestOffDays[$unique][] = $insert;
                        $insertStoreIds[]                             = $staffItem['store_id'];
                        continue;
                    }
                    //剩下的直接操作入库
                    $batchInsert[] = $insert;
                }
            }
        }
        //不能设置未来休息日的员工
        $notSetOffStaff = [];
        $logData        = [];
        $logServer = new WorkShiftService();
        //检查与排班建议是否冲突
        if ($storeIds && $needCheckSchedulingSuggestOffDays) {
            $storeIds      = array_unique(array_intersect($insertStoreIds, $storeIds));
            $storeIdsChunk = array_chunk($storeIds, 100);
            $model         = new HrStaffWorkDaysModel();
            foreach ($storeIdsChunk as $storeIdsItem) {
                $needCheckOffDays = $schedulingSuggestionService->getNeedCheckOffDays($storeIdsItem,
                    $needCheckSchedulingSuggestOffDays);
                if ($needCheckOffDays) {
                    //检查后没问题才能入库
                    [
                        $canSetOffData,
                        $notSetOffIds,
                    ] = $schedulingSuggestionService->defaultOffTaskBeforeCheck($storeIdsItem, $needCheckOffDays);
                    $notSetOffStaff = array_merge($notSetOffStaff, $notSetOffIds);
                    if ($canSetOffData) {
                        $insertNumber += count($canSetOffData);
                        $model->batch_insert($canSetOffData,BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME,true);
                        //轮休添加日志 整理格式
                        $arr = $logServer->formatWorkdayModel($canSetOffData, $operate_id);
                        $logData = $logData + $arr;
                    }
                }
            }
        }
        //直接入库
        $batchInsertChunk = array_chunk($batchInsert, 1000);
        foreach ($batchInsertChunk as $batchInsertItem) {
            $canSetData = [];
            foreach ($batchInsertItem as $batchInsertItemValue) {
                if (!in_array($batchInsertItemValue['staff_info_id'], $notSetOffStaff)) {
                    $canSetData[] = $batchInsertItemValue;
                }
            }
            $model = new HrStaffWorkDaysModel();
            $model->batch_insert($canSetData,BackyardBaseModel::WRITE_DB_PHALCON_DI_NAME,true);
            $insertNumber += count($canSetData);
            //轮休添加日志 整理格式
            $arr = $logServer->formatWorkdayModel($canSetData, $operate_id);
            $logData = $logData + $arr;
        }

        $logServer->addShiftLogBatch(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF,$logData, ['t_key' => 'workday_default_rest']);

        return " startTime {$startTime} endTime {$endTime} handleTotal:{$handleCount},insert number:{$insertNumber},not 51 or 61  or 91:" . implode(',',
                $typeError) . ",day error:" . implode(',', $dayError);
    }

    /**
     * 获取一段范围内固定星期几
     * @param $daysMap
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getFixedDate($daysMap, $startTime, $endTime): array
    {
        $startTime = strtotime($startTime);
        $endTime   = strtotime($endTime);
        $dateMap   = [];
        foreach ($daysMap as $dayStr) {
            $dayArr       = str_split($dayStr);
            $startTimeTmp = $startTime;
            while ($startTimeTmp <= $endTime) {
                if (in_array(date('N', $startTimeTmp), $dayArr)) {
                    $dateMap[$dayStr][] = date('Y-m-d', $startTimeTmp);
                }
                $startTimeTmp = strtotime('+1 day', $startTimeTmp);
            }
        }
        return $dateMap;
    }

    /**
     * 获取已经存在的休息日
     * @param $staffInfos
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getStaffWorkDays($staffInfos, $startTime, $endTime): array
    {
        $staffWeekMap    = []; //员工第几周已经存在
        $findWorkDays    = HrStaffWorkDaysModel::find([
            'columns'    => 'staff_info_id,GROUP_CONCAT(date_at) as date_at',
            'conditions' => 'staff_info_id in ({ids:array}) and date_at>=:start: and date_at<=:end:',
            'bind'       => ['ids' => array_column($staffInfos, 's'), 'start' => $startTime, 'end' => $endTime],
            'group'      => 'staff_info_id',
        ])->toArray();
        $findWorkDaysCol = array_column($findWorkDays, 'date_at', 'staff_info_id');
        foreach ($findWorkDaysCol as $staffId => $colItem) {
            $colItem = explode(',', $colItem);
            foreach ($colItem as $date) {
                $staffWeekMap[$staffId][date('W', strtotime($date))] = true;
            }
        }
        return $staffWeekMap;
    }

    /**
     * 删除 轮休默认休息日
     * @param $staffInfoId
     * @return mixed
     */
    public function delete($staffInfoId)
    {
        $model = StaffDefaultRestDayModel::class;
        return $this->modelsManager->executeQuery("update {$model} set is_deleted=1 where staff_info_id=:id:",
            ['id' => $staffInfoId])->success();
    }
}