<?php
/**
 * Author: Bruce
 * Date  : 2022-12-07 22:03
 * Description:
 */

namespace App\Services;


use App\Library\ApiClient;
use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums\ApprovalEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\HubOutSourcingStaffEnums;
use App\Library\ErrCode;
use App\Library\FlashOss;
use App\Library\HikvisionClient;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AttendanceHikDataModel;
use App\Models\backyard\AttendanceHikRecordLogModel;
use App\Models\backyard\AttendanceHikRecordModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrOutsourcingOrderModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HubOutsourcingOvertimeDetailModel;
use App\Models\backyard\HubOutsourcingOvertimeModel;
use App\Models\backyard\OutsourcingCompanyDeviceTokenModel;
use App\Models\backyard\OutsourcingCompanyModel;
use App\Models\backyard\StaffHikvisionModel;
use App\Models\backyard\OutsourcingHubStaffAttendanceCorrectionModel;
use app\models\backyard\OutsourcingHubStaffAttendanceModel;
use App\Models\backyard\SysStoreModel;
use App\Repository\HrOutsourcingOrderRepository;
use App\Repository\HrStaffInfoRepository;
use App\Repository\HubOutsourcingOvertimeRepository;
use App\Repository\StaffHikvisionRepository;
use App\Repository\SysStoreRepository;
use DateTime;
use Exception;

class HubOsAttendanceService extends BaseService
{
    const BY_ATTENDANCE_DATE = 1;//通过考勤日期
    const BY_ATTENDANCE_TIME = 2;//通过打卡时间
    const BY_RECONCILIATION_DATE = 3;//对账日

    const DOOR_IN = 1;//进门
    const DOOR_OUT = 2;//出门

    //限制下载数量
    const LIMIT_DOWNLOAD_NUM = 300000;

    const IS_PDC_NO = 1;
    const IS_PDC_YES = 2;

    public $shift_info = [];//班次信息
    public $hubStaffList = [];//员工信息
    public $staffJobTitleList = [];//员工职位
    public $holidays = [];//公共假期
    public $staffOrder = [];//订单详情
    public $staffOrderIds = [];//订单详情里的工号
    public $noInOrderStaffIds = [];//不在订单详情里的工号
    public $allStaffOrder = [];//所有订单
    const OFFSET_TIME = 60 * 60;//1 小时
    public $otOrderInfo = [];//存在加班的订单信息
    public $addOtOrderDetailInfo = [];//存入加班详情的数据（工号）
    public $shift_start_list = ['11:00', '11:30', '12:00', '12:30'];

    /**
     * 获取筛选信息
     * @return mixed
     */
    public function getSelectInfo()
    {
        $storeList = (new SysStoreService())->getStoreInfoByCategoryFromCache([
            SysStoreModel::CATEGORY_HUB,
            SysStoreModel::CATEGORY_B_HUB,
        ]);

        if(isCountry('TH')) {
            $storeList = array_merge($storeList, (new SysStoreRepository())->getPdcHikStore());
        }

        //外协公司
        $outsourcing_company = (new OutsourcingCompanyModel())->getList();
        $company             = [];
        foreach ($outsourcing_company as $key => $val) {
            $company[] = [
                'code' => (int)$val['id'],
                'name' => $val['company_name'],
            ];
        }

        $os_late_list        = OutsourcingHubStaffAttendanceModel::$late;
        $os_leave_early_list = OutsourcingHubStaffAttendanceModel::$leave_early;
        $is_absence_list     = OutsourcingHubStaffAttendanceModel::$is_absence_list;
        $is_not_order        = OutsourcingHubStaffAttendanceModel::$is_not_order;
        $is_ot        = OutsourcingHubStaffAttendanceModel::$is_ot_list;

        $all                 = array_merge_recursive(
            ['os_late_list'         => $os_late_list],
            ['os_leave_early_list'  => $os_leave_early_list]
        );
        $view_all_data = [];
        foreach ($all as $key => $values) {
            foreach ($values as $code => $val) {
                $view_all_data[$key][] = [
                    'code' => $code,
                    'name' => self::$t->_($val),
                ];
            }
        }

        $absence_list = [];
        foreach ($is_absence_list as $code => $val) {
            $absence_list[] = [
                'value' => $code,
                'label' => self::$t->_($val),
            ];
        }

        $is_not_order_list = [];
        foreach ($is_not_order as $code => $val) {
            $is_not_order_list[] = [
                'value' => $code,
                'label' => self::$t->_($val),
            ];
        }

        $is_ot_list = [];
        foreach ($is_ot as $code => $val) {
            $is_ot_list[] = [
                'value' => $code,
                'label' => self::$t->_($val),
            ];
        }

        //职位
        $getJobTitleMapByIds =  explode(',',(new SettingEnvService())->getSetVal('hub_os_select_roles'));
        $jobTitleMap = (new HrJobTitleService())->getJobTitleMapByIds($getJobTitleMapByIds);
        $result['os_job_list'] = [];
        foreach($jobTitleMap as $key=>$val ){
            $result['os_job_list'][] = [
                'code' => $key,
                'name' => $val,
            ];
        }

        array_unshift($result['os_job_list'],['code' => 99,'name' => self::$t->_('outsourcing_job_list_99')]);
        // 是否迟到
        $result['os_late_list'] = $view_all_data['os_late_list'];
        // 是否早退
        $result['os_leave_early_list'] = $view_all_data['os_leave_early_list'];

        $result['store_list']       = $storeList;
        $result['out_company_list'] = $company;
        $result['is_absence_list']  = $absence_list;
        $result['is_not_order_list']= $is_not_order_list;
        $result['is_ot']            = $is_ot_list;
        return $result;
    }

    /**
     * 获取HUB外协考勤数据
     * @param $params
     * @return mixed
     */
    public function getStaffList($params)
    {
        $params['page']      = empty($params['page']) ? 1 : $params['page'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];

        if (empty($params['is_download'])) {
            $total         = $this->getStaffListQuery($params, true);
            $data['total'] = !empty($total) ? intval($total['count']) : 0;
        }

        $list = $this->getStaffListQuery($params);
        if ($list) {
            $list = $this->formatList($list);
        }

        $data['list']  = $list;

        return $data;
    }

    /**
     * 获取列表数据
     * @param $params
     * @return mixed
     */
    public function getList($params)
    {
        $data = $this->getStaffList($params);

        $params['is_order_config'] = 1;
        $order_config_num          = $this->getStaffListQuery($params, true);

        unset($params['is_order_config']);
        $params['is_absence'] = OutsourcingHubStaffAttendanceModel::IS_ABSENCE_NO;
        $is_absence_not_num   = $this->getStaffListQuery($params, true);

        $data['is_absence_not_num'] = !empty($is_absence_not_num) ? intval($is_absence_not_num['count']) : 0;//出勤人数
        $data['order_config_num']   = !empty($order_config_num) ? intval($order_config_num['count']) : 0;//配置人数

        return $data;
    }

    /**
     * 查询 query
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getStaffListQuery($params, $isCount = false)
    {
        $columns = "hsa.id,
        hsa.hub_attendance_date, 
        ocsh.staff_info_id, 
        hsi.name, 
        hsa.attendance_store_id, 
        hsi.job_title, 
        hsi.sex,
        ocsh.outsourcing_company_id, 
        hsa.shift_id, 
        hsa.shift_start,
        hsa.shift_end, 
        started_at, 
        started_extra,
        end_at,
        end_extra,
        hsa.is_late,
        hsa.late_times,
        hsa.is_leave_early,
        hsa.leave_early_times,
        hsa.working_hours,
        hsa.is_holiday_vacations,
        hsa.settlement_coefficient,
        hsa.clocking_time,
        hsa.is_not_order,
        hsa.order_serial_no,
        hsa.is_ot,
        hsa.ot_id
        ";
        if ($isCount) {
            $columns = 'count(*) as count';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['ocsh' => StaffHikvisionModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = ocsh.staff_info_id and hsi.formal = 0', 'hsi');
        $builder->leftJoin(OutsourcingHubStaffAttendanceModel::class, 'hsa.staff_info_id = ocsh.staff_info_id', 'hsa');
        $builder = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        $builder->orderBy('hsa.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 数据格式化
     * @param $list
     * @return array
     */
    public function formatList($list)
    {
        //班次信息
        $shiftInfo     = (new SysService())->shiftTimeInfo();
        $shiftInfoToId = [];
        if ($shiftInfo) {
            $shiftInfoToId = array_column($shiftInfo, 'label', 'value');
        }
        $storeList     = (new SysStoreService())->getStoreInfoByCategoryFromCache([
            SysStoreModel::CATEGORY_HUB,
            SysStoreModel::CATEGORY_B_HUB,
        ]);

        if(isCountry('TH')) {
            $storeList = array_merge($storeList, (new SysStoreRepository())->getPdcHikStore());
        }

        $storeListToId = array_column($storeList, 'name', 'id');

        $companyInfo     = $this->getOsCompanyList();
        $companyInfoToId = array_column($companyInfo, 'company_name', 'id');

        $getJobTitleMapByIds =  array_values(array_unique(array_filter(array_column($list, 'job_title'))));
        //职位列表
        $jobTitleMap = (new HrJobTitleService())->getJobTitleMapByIds($getJobTitleMapByIds);

        $otIds = array_values(array_unique(array_column($list,'ot_id')));

        $params       = [
            'is_all'          => 1,
            'ids'             => $otIds,
            'apply_state'     => 2,
        ];
        $field        = [
            'hoom.id',
            'hoom.duration',
            'hoom.ot_date',
            'hoom.attendance_date',
        ];
        $os_ot_result = (new HubOutsourcingOvertimeService())->getHubOutsourcingOvertimeByQuery($params, $field);
        $os_ot_list = !empty($os_ot_result) ? array_column($os_ot_result, NULL,'id') : [];

        $allData = [];
        foreach ($list as $oneData) {
            $data['id']                     = $oneData['id'];
            $data['attendance_date']        = $oneData['hub_attendance_date'];
            $data['store_name']             = $storeListToId[$oneData['attendance_store_id']] ?? '';
            $is_not_order = !empty($oneData['is_not_order']) ? ' (no order)' : '';
            $data['staff_info_id']          = $oneData['staff_info_id'] . $is_not_order;
            $data['name']                   = $oneData['name'];
            $data['job_title_name']         = $jobTitleMap[$oneData['job_title']] ?? '';
            $data['out_company_name']       = $companyInfoToId[$oneData['outsourcing_company_id']] ?? '';

            $shiftName = $shiftInfoToId[$oneData['shift_id']] ?? '';
            if(empty($shiftName) && !empty($oneData['shift_start'] && !empty($oneData['shift_end']))) {
                $shiftName = $oneData['shift_start'] . '-' . $oneData['shift_end'];
            }
            $data['shift_name']             = $shiftName;

            $data['started_at']             = $oneData['started_at'] ? DateHelper::utcToLocal($oneData['started_at']) : '';
            $data['started_extra']          = json_decode($oneData['started_extra'], true);
            $data['is_late']                = $oneData['is_late'] ? self::$t->_('yes') : self::$t->_('no');
            $data['late_times']             = $oneData['late_times'];
            $data['end_at']                 = $oneData['end_at'] ? DateHelper::utcToLocal($oneData['end_at']) : '';
            $data['end_extra']              = json_decode($oneData['end_extra'], true);
            $data['is_leave_early']         = $oneData['is_leave_early'] ? self::$t->_('yes') : self::$t->_('no');
            $data['leave_early_times']      = $oneData['leave_early_times'];
            $data['working_hours']          = $oneData['working_hours'] / 10;
            $data['is_holiday_vacations']   = $oneData['is_holiday_vacations'] ? self::$t->_('yes') : self::$t->_('no');
            $data['settlement_coefficient'] = $oneData['settlement_coefficient']  / 100;
            $data['os_ot_id'] = 0;
            $data['is_work_overtime'] = HubOutSourcingStaffEnums::OUTSOURCING_NOT_OVERTIME;                                                                 // 是否加班 0 - 未加班 1 - 加班
            $data['is_work_overtime_text'] = self::$t->_(HubOutSourcingStaffEnums::$outsourcing_ot[HubOutSourcingStaffEnums::OUTSOURCING_NOT_OVERTIME]);    // 根据is_work_overtime 字段取值 格式化文案用于展示
            $data['duration'] = '0';                                                                                                                        // 加班时长
            if ($oneData['is_ot'] == OutsourcingHubStaffAttendanceModel::IS_OT_YES) {
                $data['os_ot_id']              = $oneData['ot_id'];
                $data['is_work_overtime']      = HubOutSourcingStaffEnums::OUTSOURCING_WORK_OVERTIME;
                $data['is_work_overtime_text'] = self::$t->_(HubOutSourcingStaffEnums::$outsourcing_ot[HubOutSourcingStaffEnums::OUTSOURCING_WORK_OVERTIME]);
                $data['duration']              = !empty($os_ot_list[$oneData['ot_id']]['duration']) ? floor($os_ot_list[$oneData['ot_id']]['duration'] * 10) / 10 : 0;
            }
            $data['is_absence_text']        = $oneData['clocking_time'] == '0000-00-00 00:00:00' ? self::$t->_('is_absence_yes') : self::$t->_('is_absence_no');//是否缺
            $data['order_serial_no']        = $oneData['order_serial_no'];

            $allData[] = $data;
        }

        return $allData;
    }

    /**
     * 获取外协加班信息
     * @param array $paramIn
     * @param array $userInfo
     * @return array|mixed
     */
    public function getHubAttendanceOTDetail (array $paramIn, array $userInfo)
    {
        $data         = [];
        $params       = [
            'is_all'      => 1,
            'id'          => $paramIn['os_ot_id'],
            'apply_state' => 2,
        ];
        $field        = [
            'hoom.id',
            'hoom.source',
            'hoom.apply_staff_id',
            'hoom.img',
            'hoom.ot_date',
            'hoom.attendance_date',
            'hsi.name AS apply_staff_name',
        ];
        $os_ot_result = (new HubOutsourcingOvertimeService())->getHubOutsourcingOvertimeByQuery($params, $field);
        if (empty($os_ot_result)) {
            return $os_ot_result;
        }
        $os_ot_info               = $os_ot_result[0];
        $data['apply_source']     = self::$t->_(HubOutSourcingStaffEnums::$outsourcing_ot_apply_source[$os_ot_info['source']]);
        $data['apply_staff_id']   = $os_ot_info['apply_staff_id'];
        $data['apply_staff_name'] = $os_ot_info['apply_staff_name'];
        $data['img']              = json_decode($os_ot_info['img'], true);
        $data['approval_log']     = [];

        $post_data = [
            'audit_id'   => $params['id'],
            'audit_type' => 51,     // 外协加班审批类型
            'staff_id'   => $userInfo['id'],
        ];
        $client    = new ApiClient('by', '', 'get_hc_workflow', self::$language);
        $client->setParams([$post_data]);
        $rpc_res = $client->execute();
        $this->logger->write_log("调用bi_rpc: get_hc_workflow - rpc_res: ".json_encode([
                'post_data' => $post_data,
                'result'    => $rpc_res,
            ], JSON_UNESCAPED_UNICODE), 'info');

        if (!empty($rpc_res) && !isset($rpc_res['code'])) {
            array_shift($rpc_res);
            $data['approval_log'] = $rpc_res;
        }
        return $data;
    }

    /**
     * @param $start
     * @param $end
     * @return false|float
     */
    public function calcWorkingHours($start,$end)
    {
        return floor(((strtotime($end) - strtotime($start)) / 3600) * 10);
    }

    /**
     * 获取结算系数
     * @param $working_hours
     * @param $isHoliday
     * @return float|int
     */
    public function getSettlementCoefficient($working_hours, $isHoliday)
    {
        $working_hours = $working_hours / 10;//$working_hours 工时 是乘 10后的，所以要除以10。
        $settlement_coefficient = 0;
        if ($working_hours < 4) {
            $settlement_coefficient = 0;
        } elseif ($working_hours >= 4 && $working_hours < 6) {
            $settlement_coefficient = 0.5;
        } elseif ($working_hours >= 6 && $working_hours < 9) {
            $settlement_coefficient = 0.75;
        } elseif ($working_hours >= 9) {
            $settlement_coefficient = 1;
        }
        if ($isHoliday) {
            $settlement_coefficient = $settlement_coefficient * 2;
        }

        return $settlement_coefficient;
    }

    /**
     * 查询条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        $builder->where('hsa.id is not null');
        //非正式
        $builder->andWhere('ocsh.formal = :hik_formal:', ['hik_formal' => StaffHikvisionModel::FORMAL_0]);
        //按考勤日期查询-考勤周期
        if (!empty($params['attendance_type']) && !empty($params['start_date']) && !empty($params['end_date']) && $params['attendance_type'] == self::BY_ATTENDANCE_DATE) {
            $builder->andWhere('hsa.hub_attendance_date >= :start_time: and hsa.hub_attendance_date <= :end_time: ',
                ['start_time' => $params['start_date'], 'end_time' => $params['end_date']]);
        }

        //按日期 区间
        if (!empty($params['attendance_type']) && !empty($params['start_date']) && !empty($params['end_date']) && $params['attendance_type'] == self::BY_ATTENDANCE_TIME) {
            $start_day = gmdate('Y-m-d H:i:s', strtotime($params['start_date'].' 00:00:00'));
            $end_day   = gmdate('Y-m-d H:i:s', strtotime($params['end_date'].' 23:59:59'));
            $builder->andWhere('hsa.clocking_time >= :start_day: and hsa.clocking_time <= :end_day: ',
                ['start_day' => $start_day, 'end_day' => $end_day]);
        }

        //缺勤
        if (!empty($params['is_absence']) && $params['is_absence'] == OutsourcingHubStaffAttendanceModel::IS_ABSENCE_YES) {
            $builder->andWhere('hsa.clocking_time = :is_absence_time: ', ['is_absence_time' => '0000-00-00 00:00:00']);
        }

        //非缺勤
        if (!empty($params['is_absence']) && $params['is_absence'] == OutsourcingHubStaffAttendanceModel::IS_ABSENCE_NO) {
            $builder->andWhere('hsa.clocking_time != :is_absence_time: ', ['is_absence_time' => '0000-00-00 00:00:00']);
        }

        //是否不在订单
        if (isset($params['is_not_order_status']) && $params['is_not_order_status'] !== '' && in_array($params['is_not_order_status'], [OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO, OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES])) {
            $builder->andWhere('hsa.is_not_order = :is_not_order: ', ['is_not_order' => $params['is_not_order_status']]);
        }

        // 按照对账日期查询
        if (!empty($params['attendance_type']) && !empty($params['start_date']) && !empty($params['end_date']) && $params['attendance_type'] == self::BY_RECONCILIATION_DATE) {
            $start_day = $params['start_date'];
            $end_day   = $params['end_date'];
            $builder->andWhere('hsa.reconciliation_date >= :reconciliation_start_day: and hsa.reconciliation_date <= :reconciliation_end_day: ',
                ['reconciliation_start_day' => $start_day, 'reconciliation_end_day' => $end_day]);
        }

        //按网点
        if (!empty($params['sys_store_id'])) {
            $builder->andWhere('hsa.attendance_store_id = :store_id: ', ['store_id' => $params['sys_store_id']]);
        }

        //按公司
        if (!empty($params['out_company_id'])) {
            $builder->andWhere('ocsh.outsourcing_company_id = :out_company_id: ',
                ['out_company_id' => $params['out_company_id']]);
        }

        //按 工号 和 姓名
        if (!empty($params['staff_keyword'])) {
            $builder->andWhere('(hsi.staff_info_id LIKE :staff_keyword: OR hsi.name LIKE :staff_keyword:)',
                ['staff_keyword' => '%'.$params['staff_keyword'].'%']);
        }

        // 职位 99 表示全部
        if (isset($params['job_title']) && !empty($params['job_title']) && OutsourcingHubStaffAttendanceModel::OUTSOURCING_JOB_ALL != $params['job_title']) {
            $builder->andWhere('hsi.job_title = :job_title: ', ['job_title' => $params['job_title']]);
        }

        // 是否迟到 0->否 1->是 99->全部 如果查询全部时 不拼接该条件
        if (isset($params['is_late']) && is_numeric($params['is_late']) && OutsourcingHubStaffAttendanceModel::LATE_ALL != $params['is_late']) {
            $builder->andWhere('hsa.is_late = :is_late: ', ['is_late' => $params['is_late']]);
        }

        // 是否加班 1->否 2->是 99->全部 如果查询全部时 不拼接该条件
        if (!empty($params['is_ot'])  && OutsourcingHubStaffAttendanceModel::IS_OT_ALL != $params['is_ot']) {
            $builder->andWhere('hsa.is_ot = :is_ot: ', ['is_ot' => $params['is_ot']]);
        }

        // 是否早退 0->否 1->是 99->全部 如果查询全部时 不拼接该条件
        if (isset($params['is_leave_early']) && is_numeric($params['is_leave_early']) && OutsourcingHubStaffAttendanceModel::LEAVE_EARLY_ALL != $params['is_leave_early']) {
            $builder->andWhere('hsa.is_leave_early = :is_leave_early: ', ['is_leave_early' => $params['is_leave_early']]);
        }

        //不为空 则为订单中配置了的员工(在订单里配置的)
        //【18084】【TH | BY HCM】HUB外协考勤与保安订单优化 不在订单里，也要有班次id。
        if (!empty($params['is_order_config'])) {
            $builder->andWhere('hsa.shift_id != 0 and hsa.is_not_order = 0');
        }

        //工单编号
        if (!empty($params['order_serial_no'])) {
            $builder->andWhere('hsa.order_serial_no = :order_serial_no: ',
                ['order_serial_no' => $params['order_serial_no']]);
        }


        return $builder;
    }

    /**
     * 外协公司信息
     * @return mixed
     */
    public function getOsCompanyList()
    {
        return OutsourcingCompanyModel::find()->toArray();
    }

    /**
     * 保存到导出表
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function handleExportHubAttendance(array $params)
    {
        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "hub_attendance_statistics_export".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR."main";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir.'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        return $hcmExcelTaskId;
    }

    /**
     * 生成外协账单
     * @param array $params
     * @return mixed
     * @throws ValidationException
     */
    public function createBill(array $params)
    {
        $action_name = "hub_attendance_statistics_bill_export".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR."main";
        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: AND status = 0  AND action_name = :action_name: AND is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir.'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
        return $excelTask->id;
    }


    /**
     * @return array
     */
    public function caclAttData($started_at_time,$end_at_time, $oneStaffWorkAttendance)
    {
        $hub_attendance_date   = '';
        $is_late           = OutsourcingHubStaffAttendanceModel::IS_NOT_LATE;
        $late_times        = 0;
        $is_leave_early    = OutsourcingHubStaffAttendanceModel::IS_NOT_LEAVE_EARLY;
        $leave_early_times = 0;
        $working_hours     = 0;
        if (!empty($end_at_time)) {
            $date            = date('Y-m-d', strtotime($end_at_time));
            $hub_attendance_date = $date;
            if (($date.' 00:00:00' <= $end_at_time) && ($end_at_time <= $date.' 21:00:00')) {
                $hub_attendance_date = date('Y-m-d', strtotime($date.'-1 days'));
            }
        }

        if (!empty($started_at_time)) {
            $date            = date('Y-m-d', strtotime($started_at_time));
            $hub_attendance_date = $date;
            if ((strtotime($date.' 00:00:00') <= strtotime($started_at_time)) && (strtotime($started_at_time) < strtotime($date.' 12:00:00'))) {
                $hub_attendance_date = date('Y-m-d', strtotime($date.'-1 days'));
            }
        }

        if (!empty($oneStaffWorkAttendance['clocking_date']) && !empty($oneStaffWorkAttendance['shift_start']) && !empty($oneStaffWorkAttendance['shift_end'])) {
            $startTime = $oneStaffWorkAttendance['clocking_date'].' '.$oneStaffWorkAttendance['shift_start'].':00';//班次开始时间
            $endTime   = $oneStaffWorkAttendance['clocking_date'].' '.$oneStaffWorkAttendance['shift_end'].':00';//班次结束时间
            if ($startTime > $endTime) {//跨天
                $endTime = date('Y-m-d',
                                strtotime($oneStaffWorkAttendance['clocking_date'].'+1 days')).' '.$oneStaffWorkAttendance['shift_end'].":00";
            }
            //考勤日期，hik 的考勤周期 当日11:30 到次日11:29:59;
            $hub_attendance_date = $oneStaffWorkAttendance['clocking_date'];
            if (strtotime($startTime) < strtotime($oneStaffWorkAttendance['clocking_date'].' '.'12:00:00')) {//小于11点30分的上班时间 为前一天的考勤。
                $hub_attendance_date = date('Y-m-d', strtotime($oneStaffWorkAttendance['clocking_date']."-1 days"));
            }
            //是否迟到
            if ($started_at_time > $startTime) {
                $late_times = floor((strtotime($started_at_time) - strtotime($startTime)) / 60);
                $is_late    = OutsourcingHubStaffAttendanceModel::IS_LATE;
                $started_at = $started_at_time;
            } else {
                $started_at = $startTime;
            }

            //是否早退
            if (!empty($end_at_time) && $end_at_time < $endTime) {
                $leave_early_times = floor((strtotime($endTime) - strtotime($end_at_time)) / 60);
                $is_leave_early    = OutsourcingHubStaffAttendanceModel::IS_LEAVE_EARLY;
                $end_at            = $end_at_time;
            } else {
                $end_at = $endTime;
            }
            //工作时长
            if (!empty($started_at_time) && !empty($end_at_time) && !empty($end_at) && !empty($started_at) && $started_at < $end_at) {
                $working_hours = $this->calcWorkingHours($started_at,$end_at);//（入库，乘10操作，没有小数点）
            }
        } else {
            if (!empty($started_at_time) && !empty($end_at_time) && $started_at_time < $end_at_time) {
                $working_hours = $this->calcWorkingHours($started_at_time,$end_at_time);//（入库，乘10操作，没有小数点）
            }
        }

        return [$hub_attendance_date,$is_late,$late_times,$is_leave_early,$leave_early_times,$working_hours];
    }

    /**
     * 生成补卡申请数据
     * @param $data
     * @return void
     */
    public function genCorrection($date,$staffs)
    {
        $conditions = "hub_attendance_date=:date:";
        $bind = ['date'=>$date];
        if ($staffs){
            $conditions .= " and staff_info_id in ({ids:array})";
            $bind['ids'] = array_column($staffs,'staff_info_id');
        }
        $attData = OutsourcingHubStaffAttendanceModel::find(
            [
                'conditions' => $conditions,
                'bind'=> $bind,
            ]
        )->toArray();

        foreach ($attData as $data){
            if (empty($data['started_at'])){
                $this->genCorrectionType($data,OutsourcingHubStaffAttendanceCorrectionModel::ATT_TYPE_1);
            }
            if (empty($data['end_at'])){
                $this->genCorrectionType($data,OutsourcingHubStaffAttendanceCorrectionModel::ATT_TYPE_2);
            }
        }

    }

    public function genCorrectionType($data,$attType)
    {
        if (!$this->hasCorrection($data['staff_info_id'], $data['clocking_date'],$attType)){
            $staffInfo = (new HubOutSourcingStaffService())->getOneOutSourcingStaff(['staff_info_id'=>$data['staff_info_id']]);
            $staffInfo = $staffInfo['data'];
            $model = new OutsourcingHubStaffAttendanceCorrectionModel();
            $model->serial_no = " ";
            $model->staff_info_id = $data['staff_info_id'];
            $model->store_id = $data['attendance_store_id'];
            $model->company_id = $staffInfo['outsourcing_company_id'];
            $model->attendance_date = $data['clocking_date'];
            $model->shift = $data['shift_start'] . '-' . $data['shift_end'];
            $model->attendance_type = $attType;
            $model->reason = '';
            $model->status = OutsourcingHubStaffAttendanceCorrectionModel::STATUS_1;
            $model->created_at = DateHelper::localToUtc();
            $model->updated_at = DateHelper::localToUtc();

            $model->create();
        }
    }

    /**
     * @param $staffId
     * @param $date
     * @param $attType
     * @return bool
     */
    protected function hasCorrection($staffId,$date,$attType)
    {
        $count = OutsourcingHubStaffAttendanceCorrectionModel::count(
            [
                'conditions' => "staff_info_id=:staff_id: and attendance_date = :date: and attendance_type=:type:",
                'bind' => ['staff_id'=>$staffId,'date'=>$date,'type'=>$attType],
            ]
        );
        return $count > 0;
    }


    /**
     * @return void
     */
    public function sendAttendanceCorrectionNotification($date)
    {
        //查询当天有员工缺卡的公司
        $companies = OutsourcingHubStaffAttendanceCorrectionModel::find(
            [
                'columns'=>'distinct(company_id) as company_id',
                'conditions' => 'attendance_date=:date:',
                'bind' =>['date'=> $date],
            ]
        )->toArray();
        foreach ($companies as $company){
            // 向公司登录设备发送push
            $devices = OutsourcingCompanyDeviceTokenModel::find(
                [
                    'conditions' => ' company_id = :company_id:',
                    'bind'       => ['company_id' => $company['company_id']],
                    'columns'    => 'accept_language,device_token,device_type,os',
                ]
            )->toArray();
            $pushDataArr = [];
            foreach ($devices as $device) {
                $t = $this->getTranslation($device['accept_language']);
                $pushData['message_title']   = $t->_('push_osm_outsourcing_at_title');
                $pushData['message_content'] = $t->_('push_osm_outsourcing_at_content');
                $pushData['device_token']    = $device['device_token'];
                $pushData['device_type']     = $device['device_type'];
                $pushData['os']              = $device['os'];
                $pushData['src']             = 'osm';
                $pushData['message_scheme']  = 'osm://fe/page?path=checkInAgainRoot';
                $pushDataArr[]              = $pushData;
            }
            (new PushService())->sendPushToOsmCompany(['list' => $pushDataArr]);
        }
    }

    /**
     * 针对 雇佣天数 大于1 的 需要 变更 雇佣日期 为 考勤周期内的 雇佣日期。
     * 将 不在 考勤日的订单 去除
     * @param $order
     * @param $date
     * @param $shift_info
     * @param $params
     * @return mixed
     */
    public function changeEmploymentDate($order, $date, $shift_info, $params)
    {
        //考勤日 订单 的时间区间
        $oderStartTime = $params['start_date'] . HrOutsourcingOrderRepository::START_TIME;//最早12点班次，11点30开始
        $oderEndTime = $params['end_date'] . HrOutsourcingOrderRepository::END_TIME;//最晚次日11点半班次，11点开始
        if(empty($order)) {
            return [];
        }
        $allOrder = [];
        foreach ($order as $oneOrder) {
            //找不到 班次信息的跳过
            if(!isset($shift_info[$oneOrder['shift_id']])) {
                continue;
            }
            $days = $oneOrder['employment_days'] - 1;//雇佣天数-1
            //最后一天的雇佣日期
            $end_employment_date = date('Y-m-d', strtotime("{$oneOrder['employment_date']} +{$days}days"));
            $start_employment_date = $oneOrder['employment_date'];

            //雇佣日期为1天。并且 订单 生效时间 在 考勤日订单时间区间内。去除多查出来的订单信息。
            if($days == 0 && (strtotime($oneOrder['effective_date']) >= strtotime($oderStartTime) && strtotime($oneOrder['effective_date']) <= strtotime($oderEndTime))) {
                $allOrder[] = $oneOrder;
                continue;
            }

            //考勤日 + 订单班次开始时间，小于 考勤日 12点 班次的时间，则雇佣日期 = 考勤日 + 1， 大于则 雇佣日期 = 考勤日;
            if(strtotime($date . ' ' . $shift_info[$oneOrder['shift_id']]['start']) < strtotime($date . ' 12:00')) {
                $oneOrder['employment_date'] = date('Y-m-d', strtotime($date . "+1 day"));
            } else {
                $oneOrder['employment_date'] = $date;
            }
            //雇佣日期。
            if(strtotime($oneOrder['employment_date']) > strtotime($end_employment_date) || strtotime($oneOrder['employment_date']) < strtotime($start_employment_date)) {
                continue;
            }
            //订单上班时间
            $time = $oneOrder['employment_date'] . ' ' . $shift_info[$oneOrder['shift_id']]['start'];
            //考勤日的 订单生效时间 = 订单上班时间 - 30分钟
            $current_effective_date = date('Y-m-d H:i:s', strtotime($time) - 30 * 60);
            //不在考勤周期 订单生效时间范围内的 信息 跳过
            if(strtotime($current_effective_date) < strtotime($oderStartTime) || strtotime($current_effective_date) > strtotime($oderEndTime)){
                continue;
            }

            $allOrder[] = $oneOrder;
        }

        return $allOrder;
    }

    /**
     * 给订单，按 雇佣日期+班次开始时间 排个序
     * @param $order
     * @return array
     */
    public function getOrderAscData($order)
    {
        if(empty($order)) {
            return [];
        }
        foreach ($order as &$oneOrder) {
            $oneOrder['employment_time'] = strtotime($oneOrder['employment_date'] . ' ' . $oneOrder['shift_begin_time_str']);
        }

        $sortArray = array_column($order, 'employment_time');

        array_multisort($sortArray, SORT_ASC, $order);

        return $order;
    }

    /**
     * OT订单筛选 枚举
     * @return mixed
     */
    public function getOtSelectInfo()
    {
        $storeList = (new SysStoreService())->getStoreInfoByCategoryFromCache([
            SysStoreModel::CATEGORY_HUB,
            SysStoreModel::CATEGORY_B_HUB,
        ]);
        if(isCountry('TH')) {
            $storeList = array_merge($storeList, (new SysStoreRepository())->getPdcHikStore());
        }

        $approveState = (new SysService())->approveState();

        $result['store_list']    = $storeList;
        $result['approve_state'] = $approveState;

        return $result;
    }

    /**
     * 列表数据
     * @param $params
     * @return array
     */
    public function getOtList($params)
    {
        $hubOutsourcingOvertimeRepository = new HubOutsourcingOvertimeRepository();

        $data = $this->getOtListData($params);

        $params['is_download'] = 1;
        $total          = $hubOutsourcingOvertimeRepository->getOtListQuery($params, ['sum(demand_num) as demand_num_total']);
        $data['demand_num_total'] = !empty($total) ? intval($total[0]['demand_num_total']) : 0;

        return $data;
    }

    /**
     * 获取 加班列表数据
     * @param $params
     * @return array
     */
    public function getOtListData($params)
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $hubOutsourcingOvertimeRepository = new HubOutsourcingOvertimeRepository();
        $total = $hubOutsourcingOvertimeRepository->getOtListQuery($params, [], true);

        $data['total'] = !empty($total) ? intval($total['count']) : 0;
        if($data['total'] == 0) {
            return ['total' => 0, 'list' => []];
        }

        if(isset($params['is_check_data_num']) && $params['is_check_data_num'] == true) {
            return $data;
        }

        $columns = ['hoo.id as ot_id', 'hoo.serial_no', 'hoo.store_id', 'hoo.store_id', 'hoo.ot_date', 'hoo.shift_id', 'hoo.demand_num', 'hoo.duration','hoo.apply_staff_id', 'hoo.img', 'hoo.apply_state', "DATE_FORMAT(CONVERT_TZ(hoo.created_at, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as created_at"];
        $list  = $hubOutsourcingOvertimeRepository->getOtListQuery($params, $columns);
        if ($list) {
            $list = $this->formatOtList($list);
        }

        $data['list']  = $list;

        return $data;
    }

    /**
     * 格式化数据
     * @param $list
     * @return mixed
     */
    public function formatOtList($list)
    {
        $storeListToId = [];
        $sysStoreId    = array_values(array_unique(array_column($list, 'store_id')));
        $storeList     = (new SysStoreService())->getStoreListByIds($sysStoreId);

        $storeList[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $staffInfoIds        = array_column($list, 'apply_staff_id');
        $staffInfoList       = HrStaffInfoRepository::getHrStaffByIds($staffInfoIds,
            ['staff_info_id', 'name', 'node_department_id']);
        $staffInfoListToId   = array_column($staffInfoList, 'node_department_id', 'staff_info_id');
        $staffInfoListToName = array_column($staffInfoList, 'name', 'staff_info_id');

        $sysService         = new SysService();
        $departmentInfoToId = array_column($sysService->getDepartmentListFromCache(), 'name', 'id');

        $shiftInfo     = (new SysService())->shiftTimeInfo();
        $shiftInfoToId = [];
        if ($shiftInfo) {
            $shiftInfoToId = array_column($shiftInfo, 'label', 'value');
        }

        foreach ($list as &$oneData) {
            $shiftName = $shiftInfoToId[$oneData['shift_id']] ?? '';
            $oneData['shift_name'] = !empty($shiftName) ? $oneData['ot_date']. ' ' . $shiftName : '';

            $oneData['store_name']      = isset($storeListToId[$oneData['store_id']]) ? $storeListToId[$oneData['store_id']]['store_name'] : '';
            $oneData['department_name'] = isset($staffInfoListToId[$oneData['apply_staff_id']]) && isset($departmentInfoToId[$staffInfoListToId[$oneData['apply_staff_id']]]) ? $departmentInfoToId[$staffInfoListToId[$oneData['apply_staff_id']]] : '';

            $staff_name                  = $staffInfoListToName[$oneData['apply_staff_id']] ?? '';
            $oneData['staff_name']       = $staff_name . '(' . $oneData['apply_staff_id'] . ')';
            $oneData['apply_state_text'] = empty($oneData['apply_state']) ? '' : self::$t->_('by_state_' . $oneData['apply_state']);
            $oneData['img']              = json_decode($oneData['img'], true);
            $oneData['duration']         = number_format($oneData['duration'], 1);
        }
        return $list;
    }

    /**
     * HUB 外协加班 导出
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getOtListExport($params)
    {
        $params['is_check_data_num'] = true;
        $result = $this->getOtListData($params);
        if($result['total'] > self::LIMIT_DOWNLOAD_NUM) {
            throw new ValidationException(self::$t->_('file_download_limit',['num' => self::LIMIT_DOWNLOAD_NUM]));
        }
        unset($params['is_check_data_num']);

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "hub_overtime_export".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR."main";

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir.'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['user_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => $hcmExcelTaskId];
    }

    /**
     * 获取审批详情
     * @param $params
     * @return array
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function getOtDetail($params)
    {
        $hubOtInfo = HubOutsourcingOvertimeRepository::getHubOtInfo($params['ot_id']);
        if(empty($hubOtInfo)) {
            throw new ValidationException(self::$t->_('data_error'));

        }

        $audit_params['id_union']     = 'id_' . $params['ot_id'];
        $audit_params['staff_id']     = $hubOtInfo['apply_staff_id'];
        $audit_params['type']         = ApprovalEnums::APPROVAL_TYPE_HUB_OS_OT;
        $audit_params['locale']       = self::$language;
        $audit_params['date_created'] = $hubOtInfo['created_at'] ?? '';//待确认
        //获取审批列表
        $detail = (new WorkflowService())->getAuditDetailV2($audit_params,
            $params['user_info']) ?: [];

        return $detail;
    }

    public function getExportHeader(): array
    {
        $t = self::$t;

        return [
            $t['attendance_date'],       //考勤日期
            $t['attendance_store'],      //考勤网点
            $t['os_staff_id'],           //外协工号
            $t['os_name'],               //员工姓名
            $t['staff_job_title'],       //职位
            $t['out_os_company_name'],   //外协公司
            $t['shift'],                 //班次名称
            $t['is_absence'],            //是否缺勤
            $t['clock_in'],              //上班打卡时间
            $t['is_late'],               //迟到时长
            $t['hub_late_times'],        //迟到时长
            $t['clock_out'],             //下班打卡时间
            $t['is_leave_early'],        //是否早退
            $t['leave_early_times'],     //早退分钟数
            $t['working_hours'],         //工作时长（小时）
            $t['work_overtime'],         //是否加班
            $t['os_ot_duration'],        //加班时长
            $t['is_holiday_vacations'],  //是否节假日
            $t['settlement_coefficient'],//结算系数
            $t['hub_outsoucing_order'],  //外协订单
        ];
    }


    /**
     * @param $params
     * @param $file_name
     */
    public function handleExport($params,$file_name)
    {
        $header   = $this->getExportHeader();

        $new_data = [];
        $params['page'] = 1;
        $params['page_size'] = 1000;

        while (true){
            $data = $this->getStaffList($params);

            $list = !empty($data['list']) ? $data['list'] : [];

            if(empty($list)){
                break;
            }
            $params['page'] ++;
            foreach ($list as $key=>$value){
                $new_data[] = [
                    $value['attendance_date'],
                    $value['store_name'],
                    $value['staff_info_id'],
                    $value['name'],
                    $value['job_title_name'],
                    $value['out_company_name'],
                    $value['shift_name'],
                    $value['is_absence_text'],
                    $value['started_at'],
                    $value['is_late'],
                    $value['late_times'],
                    $value['end_at'],
                    $value['is_leave_early'],
                    $value['leave_early_times'],
                    $value['working_hours'],
                    $value['is_work_overtime_text'],    // 是否加班
                    $value['duration'],                 // 加班时长
                    $value['is_holiday_vacations'],
                    $value['settlement_coefficient'],
                    $value['order_serial_no'],
                ];
            }

        }
        $file_data =  $this->exportExcel($header, $new_data, $file_name);
        $flashOss  = new FlashOss();
        $ossObject = 'hub_attendance_at/'.date('Ymd').'/'.$file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);
        return $ossObject;

    }

    /**
     * 获取公共假期
     * @return array
     */
    public function getHoliday()
    {
        $holiday         = (new HolidayService())->getHoliday();
        $holiday_arr_wd5 = $holiday[HrStaffInfoModel::WEEK_WORKING_DAY_FIVE];
        $holiday_arr_wd6 = $holiday[HrStaffInfoModel::WEEK_WORKING_DAY_SIX];
        $holidays        = array_values(array_unique(array_merge($holiday_arr_wd5, $holiday_arr_wd6)));

        return $holidays;
    }

    /**
     * 判断是否是 节假日
     * @param $started_at_time
     * @param $clocking_date
     * @param $holidays
     * @param string $store_id
     * @return int
     */
    public function getIsHoliday($started_at_time, $clocking_date,$holidays, $store_id = '')
    {
        /**
         * - 班次起始时间在1月30日0点到23:59:59这个区间则判断为节假日；
         * - 如班次为空，则上班打卡时间为1月29日23点到1月30日23点则判断为节假日；
         */
        $isHoliday       = OutsourcingHubStaffAttendanceModel::IS_NOT_HOLIDAY_VACATIONS;
        if (!empty($started_at_time)) {
            $started_date = date('Y-m-d', strtotime($started_at_time));
            if($started_at_time >= $started_date . " 23:00:00") {
                $started_date = date('Y-m-d', strtotime($started_date. "+1 day"));
            }
            if(in_array($started_date, $holidays)) {
                $isHoliday       = OutsourcingHubStaffAttendanceModel::IS_HOLIDAY_VACATIONS;
            }
        }

        if(!empty($clocking_date) && in_array($clocking_date, $holidays)) {
            $isHoliday       = OutsourcingHubStaffAttendanceModel::IS_HOLIDAY_VACATIONS;
        }

        return $isHoliday;
    }

    //查询 hik 打卡数据
    public function getHikAttendanceData($hik_client, $params)
    {
        $postData['startTime']            = $params['startTime'];//事件开始时间
        $postData['endTime']              = $params['endTime'];//事件结束时间
        $postData['pageSize']             = $params['pageSize'];
        $postData['pageNo']               = $params['pageNo'];
        $postData['doorRegionIndexCodes'] = $params['doorRegionIndexCodes'];//通过，门禁点所在区域集合，查询区域列表v2接口获取返回参数indexCode，最大支持500个区域

        return $hik_client->execute($postData, $hik_client->hik_attendance_data);
    }

    /**
     * 获取指定区域下的子区域-PDC-Operations_AC
     * @param $hik_client
     * @param $regionIndexCodes
     * @return mixed
     */
    public function getRegion($hik_client, $regionIndexCodes)
    {
//        $data['regionName'] = 'PDC-Operations_AC';
        $data['parentIndexCodes'] = $regionIndexCodes;
        $data['isSubRegion'] = true;
        $data['pageSize']    = 1000;
        $data['pageNo'] = 1;
        $data['resourceType'] = 'region';
        return $hik_client->execute($data, $hik_client->hik_region_node);
    }

    /**
     * 从hik 获取 pdc 区域的打卡数据
     * @param $params
     * @return array
     * @throws Exception
     */
    public function getAttendanceData($params)
    {
        $hik_client          = new HikvisionClient();
        //pdc ope 区域唯一码
        $hikRegionIndexCode = (new SettingEnvService)->getSetVal('hik_region_index_code', ',');
        if(empty($hikRegionIndexCode)) {
            $this->logger->info(['hik-getAttendanceData' => '获取PDC operation 区域唯一码 失败，请检查！']);

            $res['result'] = false;
            $res['message'] = '获取PDC operation 区域唯一码 失败，请检查！';
            return $res;
        }

        //查询PDC-Operations_AC，子区域
        $region = $this->getRegion($hik_client, $hikRegionIndexCode);
        $regionList = [];
        if(!empty($region) && $region['code'] == '0') {
            if(empty($region['data']['list'])) {
                $this->logger->info(['hik-getAttendanceData-getRegion' => ['params'=>$hikRegionIndexCode, 'res'=> $region, 'remark' =>'获取PDC operation 区域 子区域唯一码失败，请检查！【需要重新跑任务--1】']]);
                $res['result'] = false;
                $res['message'] = '获取PDC operation 区域 子区域唯一码失败，请检查！【需要重新跑任务--1】';
                return $res;
            }
            $regionList = $region['data']['list'];
        }

        if(empty($regionList)) {
            $this->logger->info(['hik-getAttendanceData-getRegion' => ['params'=>$hikRegionIndexCode, 'res'=> $region, 'remark' =>'获取PDC operation 区域 子区域唯一码失败，请检查！【需要重新跑任务--2】']]);
            $res['result'] = false;
            $res['message'] = '获取PDC operation 区域 子区域唯一码失败，请检查！【需要重新跑任务--2】';
            return $res;
        }

        $indexCodes = [];
        foreach ($regionList as $one) {
            $indexCodes[] = $one['indexCode'];
        }

        $storeInfo = (new SysStoreRepository())->getPdcHikStore();

        $storeList = [];
        foreach ($storeInfo as $da) {
            $hikName          = strtoupper($da['hik_store_name']);
            $flashName        = strtoupper($da['id']);
            $storeList[$hikName] = $flashName;
        }

        $where['pageSize'] = 1000;
        $where['pageNo'] = 1;
        $where['doorRegionIndexCodes'] = $indexCodes;
        $where['startTime'] = str_replace(' ', 'T', $params['start_time']).get_sys_timezone();//事件开始时间;
        $where['endTime'] = str_replace(' ', 'T', $params['end_time']).get_sys_timezone();//事件结束时间;
        $baseModel = new BaseModel();
        $this->logger->info(['hik-getHikAttendanceData' => ['params'=>$where]]);
        while(true) {
            $data = $this->getHikAttendanceData($hik_client, $where);
            if(!empty($data) && $data['code'] == '0') {
                $res['total']     = $data['data']['total'];//总条数
                $res['pageSize']  = $data['data']['pageSize'];//每页行数
                $res['totalPage'] = $data['data']['totalPage'];//总页数
                $res['pageNo']    = $data['data']['pageNo'];//第几页
                $this->logger->info(['hik-getHikAttendanceData-res' => $res]);

                if(empty($data['data']['list'])) {
                    $this->logger->info(['hik-getHikAttendanceData-end' => $data]);
                    break;
                }
                $dataList = $data['data']['list'];
            } else {
                //异常数据删除，当前这波数据。
                $this->delRecordData($params['end_time']);
                $result = ['hik-getHikAttendanceData-error' => ['params' => $where, 'res' => $data]];
                $this->logger->info($result);
                $res['result'] = false;
                $res['message'] = '获取打卡数据异常：' . json_encode($result, JSON_UNESCAPED_UNICODE);
                return $res;
            }

            $staffInfo = $staffBatchInfo =  [];
            foreach ($dataList as $one) {
                if(empty($one['jobNo'])) {
                    continue;
                }

                $staffInfo['staff_info_id'] = $one['jobNo'];
                $staffInfo['dev_name']       = $one['devName'];
                $staffInfo['door_name']       = $one['doorName'];
                //2021-06-27T02:03:30+07:00 时间需要格式化
                $time_arr             = empty($one['eventTime']) ? [] : explode('T', $one['eventTime']);
                //打卡时间
                $staffInfo['event_time'] = null;
                $date = '';
                if (!empty($time_arr)) {
                    $date                 = $time_arr[0];
                    $time                 = explode('+', $time_arr[1])[0];
                    $staffInfo['event_time'] = date('Y-m-d H:i:s', strtotime("{$date} {$time}"));
                }
                //打卡日期
                $staffInfo['date_at']       = empty($date) ? date('Y-m-d') : $date;

                $time_arr             = empty($one['receiveTime']) ? [] : explode('T', $one['receiveTime']);
                //入库时间
                $staffInfo['receive_time'] = null;
                if (!empty($time_arr)) {
                    $date                 = $time_arr[0];
                    $time                 = explode('+', $time_arr[1])[0];
                    $staffInfo['receive_time'] = date('Y-m-d H:i:s', strtotime("{$date} {$time}"));
                }

                $name                       = empty($one['devName']) ? '' : explode('_', str_replace('-','_',$one['devName']));//海康机器保存的网点名称
                $hikName                    = is_array($name) ? str_replace('ฺ','',strtoupper($name[0])) : '';//取第一个下划线前面的值 发现特殊字符 需要替换下
                $staffInfo['hik_store_name']   = $hikName;
                $staffInfo['flash_store_name'] = $storeList[$hikName] ?? '';//如果没匹配上 先把hik 的存进去
                $direction                  = 0;//默认未知
                if (strstr(strtoupper($one['doorName']), 'IN')) {
                    $direction = self::DOOR_IN;
                }
                if (strstr(strtoupper($one['doorName']), 'OUT')) {
                    $direction = self::DOOR_OUT;
                }
                $staffInfo['direction'] = $direction;//海康机器对应的 出门还是进门 用in out 区分
                //执行任务时间
                $staffInfo['execute_time']       = $params['end_time'];

                $staffBatchInfo[] = $staffInfo;

            }
            //入库
            if(!empty($staffBatchInfo)) {
                $baseModel->table_batch_insert($staffBatchInfo, 'db_backyard', 'attendance_hik_record');
            }

            //如果是最后一页 保存以后 断掉
            if (!empty($data['data']['totalPage']) && $where['pageNo'] >= $data['data']['totalPage']) {
                break;
            }
            $where['pageNo']++;
        }
        $res['result'] = true;
        $res['message'] = 'success!';
        return $res;
    }

    /**
     * 记录执行失败的数据
     * @param $params
     * @param array $columns
     * @return array|bool
     */
    public function addRecordExecuteLog($params, $columns = ['*'])
    {
        if (empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];

        if (!empty($params['execute_time'])) {
            $conditions         .= ' and execute_time = :execute_time:';
            $bind['execute_time'] = $params['execute_time'];
        }

        $model = AttendanceHikRecordLogModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $columns,
        ]);

        if(empty($model) && $params['result'] === false) {
            $model = new AttendanceHikRecordLogModel();
            $model->execute_time = $params['execute_time'];
            $model->status = AttendanceHikRecordLogModel::STATUS_FAIL;
            $model->msg    = $params['message'];
            $model->num = 1;
            $model->save();
            return true;
        }

        if(!empty($model) && $model->status == AttendanceHikRecordLogModel::STATUS_FAIL && $params['result'] === false) {
            $model->num = $model->num + 1;
            $model->msg = $params['message'];
            $model->save();
            if($model->num > 3) {
                $this->logger->info(['hik-getAttendanceData-addRecordExecuteLog' => ['params'=>$params, 'res'=> $params['message'], 'remark' =>'pdc拉取hik 打卡数据 失败3次以上，请检查！']]);
            }
            return true;
        }

        if(!empty($model) && $model->status == AttendanceHikRecordLogModel::STATUS_FAIL && $params['result'] === true) {
            $model->status = AttendanceHikRecordLogModel::STATUS_SUCCESS;
            $model->save();
            return true;
        }

        return true;
    }

    public function getAttendanceHikRecordLog()
    {
        return AttendanceHikRecordLogModel::find([
            'conditions' => 'status = :status:',
            'bind'       => ['status' => AttendanceHikRecordLogModel::STATUS_FAIL],
        ])->toArray();
    }

    //删除执行中断的数据
    public function delRecordData($execute_time){

        $sql = " delete from attendance_hik_record where execute_time = '{$execute_time}'";
        return $this->getDI()->get('db_backyard')->execute($sql);

    }

    /**
     * 格式化时间
     * @param $input
     * @return string
     * @throws Exception
     */
    public function formatTime($input) {
        $date = new DateTime($input);
        $minutes = (int)$date->format('i'); // 获取分钟数

        if ($minutes < 30) {
            $date->setTime($date->format('H'), 0, 0); // 设置为 02:00
        } else {
            $date->setTime($date->format('H'), 30, 0); // 设置为 32:00
        }

        return $date->format('Y-m-d H:i:s');
    }

    public function statistics($params)
    {
        if(empty($params['current_time'])) {
            return false;
        }

        $storeInfo = (new SysStoreRepository())->getPdcHikStore();
        $pdc_store_ids = empty($storeInfo) ? [] : array_column($storeInfo, 'id');

        //班次
        $shift = (new HrShiftService())->getList(['is_all' => true]);
        //班次信息
        $this->shift_info = array_column($shift, null, 'id');
        $this->holidays = $this->getHoliday();


        $params['pdc_store_ids'] = $pdc_store_ids;
        $params['is_pdc'] = HubOsAttendanceService::IS_PDC_YES;

        $this->hubStaffList = (new StaffHikvisionRepository())->getHubStaffInfo($params);
        $this->staffJobTitleList = array_column($this->hubStaffList, 'job_title', 'staff_info_id');

        //目的：获取 班次开始和结束时间 = 当前时间 的订单
        //当前时间 半小时前 生效的订单
        $orderWhere['begin'] = date('Y-m-d H:i:s', strtotime("{$params['current_time']}-30 minutes"));
        //当前时间 5.5小时后 失效的订单
        $orderWhere['end'] = date('Y-m-d H:i:s', strtotime("{$params['current_time']}+330 minutes"));

        $orderWhere['pdc_store_ids'] = $pdc_store_ids;

        $hrOutsourcingOrderRepository = new HrOutsourcingOrderRepository();

        $orderAll      = $hrOutsourcingOrderRepository->getOrderInfoByShiftInfo($orderWhere);
        $orderStaffAll = $hrOutsourcingOrderRepository->getStaffOrderInfoByShiftInfo($orderWhere);


        //计算上班
        if(!empty($orderAll['on_order'])) {
            $onParams['on_order'] = $orderAll['on_order'];//上班订单
            $onParams['on_order_staff'] = $orderStaffAll['on_order_staff'];//上班订单的人
            $onParams['record_start_time'] = date('Y-m-d H:i:s', strtotime("{$orderWhere['begin']}-1 hours"));
            $onParams['record_end_time'] = date('Y-m-d H:i:s', strtotime("{$orderWhere['begin']}+1 hours"));
            $this->getOnAttendanceInfo($onParams);
        }

        if(!empty($orderAll['off_order'])) {
            $offParams['off_order'] = $orderAll['off_order'];//下班订单
            $offParams['off_order_staff'] = $orderStaffAll['off_order_staff'];//下班订单的人

            //找出下班订单 班次最早时间。（订单中可能会有 半天班次）
            $offParams['record_start_time'] = '';
            foreach ($offParams['off_order'] as $oneOrder) {
                $shiftInfo = $this->shift_info[$oneOrder['shift_id']];
                $record_start_time = $oneOrder['employment_date'].' '.$shiftInfo['start'].':00';
                if(empty($offParams['record_start_time'])) {
                    $offParams['record_start_time'] = $record_start_time;
                    continue;
                }

                if(strtotime($record_start_time) > strtotime($offParams['record_start_time'])) {
                    continue;
                }
                $offParams['record_start_time'] = $record_start_time;
            }
            $offParams['record_start_time'] = date('Y-m-d H:i:s', strtotime("{$offParams['record_start_time']}-1 hours"));//订单中最早班次开始时间 -1 小时；
            $offParams['record_end_time'] = date('Y-m-d H:i:s', strtotime("{$params['current_time']}+1 hours"));//当前时间下班 + 1 小时；
            $this->getOffAttendanceInfo($offParams);
        }
    }

    /**
     * 简单计算 上班卡
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function getOnAttendanceInfo($params)
    {
        $on_order_staff = $params['on_order_staff'];

        $staffOrderIds = !empty($on_order_staff) ? array_column($on_order_staff, 'staff_info_id') : [];
        $on_order_staff = array_column($on_order_staff, NULL, 'staff_info_id');


        $recordWhere['start_time'] = $params['record_start_time'];
        $recordWhere['end_time'] = $params['record_end_time'];
        $recordList = $this->getHikAttendanceRecord($recordWhere);

        /**
         * 只计算在订单里的。
         */
        $staffInAttendance = [];
        foreach ($recordList as $oneRecord) {
            //如果工号不存在在订单里--跳过
            if (!in_array($oneRecord['staff_info_id'], $staffOrderIds)) {
                continue;
            }
            // 只是处理存在订单中的数据
            $staffInAttendance[$oneRecord['staff_info_id']]['staff_info_id']       = $oneRecord['staff_info_id'];
            $staffInAttendance[$oneRecord['staff_info_id']]['attendance_store_id'] = $oneRecord['flash_store_name'];
            $staffInAttendance[$oneRecord['staff_info_id']]['direction']           = AttendanceHikDataModel::DIRECTION_IN;

            // 海康打卡时间
            $oneInHikAttendance_event_time = $oneRecord['event_time'];
            // 取首次打卡时间，如果存在则不在更新该时间
            if (isset($staffInAttendance[$oneRecord['staff_info_id']]['started_at'])) {
                if ($staffInAttendance[$oneRecord['staff_info_id']]['started_at'] > $oneInHikAttendance_event_time) {
                    $staffInAttendance[$oneRecord['staff_info_id']]['started_at'] = $oneInHikAttendance_event_time;
                }
            } else {
                $staffInAttendance[$oneRecord['staff_info_id']]['started_at'] = $oneInHikAttendance_event_time;
            }
        }

        $db = $this->getDI()->get("db_backyard");

        $allAttendance = [];
        foreach ($on_order_staff as $oneStaffInfo) {
            $attendance_date = $oneStaffInfo['employment_date'] ?? NULL;
            $order_serial_no = $oneStaffInfo['order_serial_no'] ?? null;
            $shift_id = $oneStaffInfo['shift_id']?? null;
            $store_id = isset($staffInAttendance[$oneStaffInfo['staff_info_id']]) ? $staffInAttendance[$oneStaffInfo['staff_info_id']]['attendance_store_id'] : null;

            $oneStaffWorkAttendance['staff_info_id']       = $oneStaffInfo['staff_info_id'];
            $oneStaffWorkAttendance['job_title']           = $this->staffJobTitleList[$oneStaffInfo['staff_info_id']] ?? 0;
            $oneStaffWorkAttendance['clocking_date']       = $attendance_date;//实际雇拥日期：这样才能结合 班次，进行判断迟到早退。
            $oneStaffWorkAttendance['order_serial_no']     = $order_serial_no;//匹配外协订单编号
            $oneStaffWorkAttendance['shift_id']            = $shift_id;
            $oneStaffWorkAttendance['shift_start']         = isset($this->shift_info[$shift_id]) ? $this->shift_info[$shift_id]['start'] : null;
            $oneStaffWorkAttendance['shift_end']           = isset($this->shift_info[$shift_id]) ? $this->shift_info[$shift_id]['end'] : null;
            $oneStaffWorkAttendance['started_at']          = !empty($staffInAttendance[$oneStaffInfo['staff_info_id']]['started_at']) ? gmdate('Y-m-d H:i:s', strtotime($staffInAttendance[$oneStaffInfo['staff_info_id']]['started_at'])) : null;
            $oneStaffWorkAttendance['end_at']              = null;
            $oneStaffWorkAttendance['attendance_store_id'] = $store_id;
            $oneStaffWorkAttendance['clocking_time']       = !empty($oneStaffWorkAttendance['started_at']) ? $oneStaffWorkAttendance['started_at'] : NULL;

            $started_at_time = !empty($staffInAttendance[$oneStaffInfo['staff_info_id']]['started_at']) ? $staffInAttendance[$oneStaffInfo['staff_info_id']]['started_at'] : null;
            $end_at_time     = !empty($staffOutAttendance[$oneStaffInfo['staff_info_id']]['end_at']) ? $staffOutAttendance[$oneStaffInfo]['end_at'] : null;

            [$hub_attendance_date,$is_late,$late_times,$is_leave_early,$leave_early_times,$working_hours] = $this->caclAttData($started_at_time,$end_at_time,$oneStaffWorkAttendance);

            $isHoliday = $this->getIsHoliday($started_at_time, $oneStaffWorkAttendance['clocking_date'], $this->holidays, $store_id);

            $oneStaffWorkAttendance['is_late']                = $is_late;//是否迟到
            $oneStaffWorkAttendance['late_times']             = $late_times;//迟到分钟数
            $oneStaffWorkAttendance['is_leave_early']         = $is_leave_early;//是否早退
            $oneStaffWorkAttendance['leave_early_times']      = $leave_early_times;//早退分钟数
            $oneStaffWorkAttendance['working_hours']          = $working_hours;//工作时长 （入库，是乘10后的结果，没有小数点）
            $oneStaffWorkAttendance['is_holiday_vacations']   = $isHoliday;//是否节假日
            $oneStaffWorkAttendance['hub_attendance_date']    = $hub_attendance_date;//hub 考勤规则的考勤日期
            $oneStaffWorkAttendance['settlement_coefficient'] = $this->getSettlementCoefficient($working_hours,
                    $isHoliday) * 100;//入库乘100操作，没有小数点。
            $oneStaffWorkAttendance['is_not_order']           = OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO;//在订单中

            $attendanceIdInfo = $this->getStaffAttendanceInfo($oneStaffWorkAttendance);
            if (!empty($attendanceIdInfo)) {
                $this->getDi()->get('logger')->info('[HubAttendanceStatisticsTask-pdc-update-data]' . json_encode($attendanceIdInfo, JSON_UNESCAPED_UNICODE));
                $db->updateAsDict('outsourcing_hub_staff_attendance', $oneStaffWorkAttendance, ["conditions" => "id = ?", 'bind' => $attendanceIdInfo['id']]);
                continue;
            }

            $allAttendance[] = $oneStaffWorkAttendance;
        }

        if(empty($allAttendance)) {
            return true;
        }

        $baseModel = new BaseModel();
        $baseModel->table_batch_insert($allAttendance, 'db_backyard', 'outsourcing_hub_staff_attendance');
        return true;
    }

    public function getOffAttendanceInfo($params)
    {
        $this->noInOrderStaffIds = [];

        $this->staffOrder    = empty($params['off_order_staff']) ? [] : array_column($params['off_order_staff'], NULL, 'staff_info_id');
        $this->staffOrderIds = empty($params['off_order_staff']) ? [] : array_column($params['off_order_staff'], 'staff_info_id');
        $this->allStaffOrder           = $params['off_order'];

        //获取下班订单，班次开始+1小时 - 结束-1小时 内的所有卡。重新计算。上班卡下班卡。
        $recordWhere['start_time'] = $params['record_start_time'];
        $recordWhere['end_time']   = $params['record_end_time'];
        $recordList = $this->getHikAttendanceRecord($recordWhere);

        [$staffInAttendance, $staffInAllAttendance] = $this->endStatisticsOn($recordList);

        [$staffOutAttendance, $staffOutAllAttendance] = $this->endStatisticsOff($recordList, $staffInAttendance);
        //获取 订单外员工上 下 班卡数据。
        [$staffInAttendanceNoOrder, $staffOutAttendanceNoOrder] = $this->endStatisticsNoOrder($staffInAllAttendance, $staffOutAllAttendance);

        if(!empty($staffInAttendanceNoOrder)) {
            foreach ($staffInAttendanceNoOrder as $oneInAttendanceNoOrder) {
                $staffInAttendance[$oneInAttendanceNoOrder['staff_info_id']] = $oneInAttendanceNoOrder;
            }
        }

        if(!empty($staffOutAttendanceNoOrder)) {
            foreach ($staffOutAttendanceNoOrder as $oneOutAttendanceNoOrder) {
                $staffOutAttendance[$oneOutAttendanceNoOrder['staff_info_id']] = $oneOutAttendanceNoOrder;
            }
        }

        $this->formatWorkAttendance($this->staffOrder, $staffInAttendance, $staffOutAttendance, $this->shift_info);

        return true;
    }

    /**
     * 插入，更新 考勤正式表
     * @param $staffOrder --外协订单信息
     * @param $staffInAttendance --上班卡
     * @param $staffOutAttendance --下班卡
     * @param $shift_info --班次信息
     * @param $staffInfos --员工信息
     * @param $holidays   --节假日
     * @throws Exception
     */
    public function formatWorkAttendance($staffOrder, $staffInAttendance, $staffOutAttendance, $shift_info)
    {
        $db         = $this->getDI()->get("db_backyard");
        $baseServer = new BaseModel();

        $inStaffId  = array_keys($staffInAttendance);
        $outStaffId = array_keys($staffOutAttendance);
        //可能只有上班卡，或者只有下班卡。
        $allStaffIds     = array_unique(array_merge($inStaffId, $outStaffId));

        $allStaffWorkAttendance = $allAbsenceData = [];
        foreach ($allStaffIds as $oneStaffId) {
            //下班打卡网点
            $store_id = isset($staffOutAttendance[$oneStaffId]) ? $staffOutAttendance[$oneStaffId]['attendance_store_id'] : null;
            //上班打卡网点
            $store_id = isset($staffInAttendance[$oneStaffId]) ? $staffInAttendance[$oneStaffId]['attendance_store_id'] : $store_id;

            $shift_id        = isset($staffOrder[$oneStaffId]) ? $staffOrder[$oneStaffId]['shift_id'] : 0;
            $attendance_date = isset($staffOrder[$oneStaffId]) ? $staffOrder[$oneStaffId]['employment_date'] : null;

            $order_serial_no = isset($staffOrder[$oneStaffId]) ? $staffOrder[$oneStaffId]['order_serial_no'] : null;

            $is_not_order = isset($staffOrder[$oneStaffId]) ? (empty($staffOrder[$oneStaffId]['is_not_order']) ? OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO : OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES) : OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO;

            if(empty($staffInAttendance[$oneStaffId]['started_at']) && empty($staffOutAttendance[$oneStaffId]['end_at'])) {
                continue;
            }

            $oneStaffWorkAttendance['staff_info_id']       = $oneStaffId;
            $oneStaffWorkAttendance['job_title']           = $this->staffJobTitleList[$oneStaffId] ?? 0;
            $oneStaffWorkAttendance['clocking_date']       = $attendance_date;//实际雇拥日期：这样才能结合 班次，进行判断迟到早退。
            $oneStaffWorkAttendance['order_serial_no']     = $order_serial_no;//匹配外协订单编号
            $oneStaffWorkAttendance['shift_id']            = $shift_id;
            $oneStaffWorkAttendance['shift_start']         = isset($shift_info[$shift_id]) ? $shift_info[$shift_id]['start'] : null;
            $oneStaffWorkAttendance['shift_end']           = isset($shift_info[$shift_id]) ? $shift_info[$shift_id]['end'] : null;
            $oneStaffWorkAttendance['started_at']          = !empty($staffInAttendance[$oneStaffId]['started_at']) ? gmdate('Y-m-d H:i:s', strtotime($staffInAttendance[$oneStaffId]['started_at'])) : null;
            $oneStaffWorkAttendance['end_at']              = !empty($staffOutAttendance[$oneStaffId]['end_at']) ? gmdate('Y-m-d H:i:s', strtotime($staffOutAttendance[$oneStaffId]['end_at'])) : null;
            $oneStaffWorkAttendance['attendance_store_id'] = $store_id;
            $oneStaffWorkAttendance['clocking_time']       = !empty($oneStaffWorkAttendance['started_at']) ? $oneStaffWorkAttendance['started_at'] : $oneStaffWorkAttendance['end_at'];

            $started_at_time = !empty($staffInAttendance[$oneStaffId]['started_at']) ? $staffInAttendance[$oneStaffId]['started_at'] : null;
            $end_at_time     = !empty($staffOutAttendance[$oneStaffId]['end_at']) ? $staffOutAttendance[$oneStaffId]['end_at'] : null;

            [$hub_attendance_date,$is_late,$late_times,$is_leave_early,$leave_early_times,$working_hours] =
                $this->caclAttData($started_at_time,$end_at_time,$oneStaffWorkAttendance);

            $isHoliday = $this->getIsHoliday($started_at_time, $oneStaffWorkAttendance['clocking_date'], $this->holidays , $store_id);

            $oneStaffWorkAttendance['is_late']                = $is_late;//是否迟到
            $oneStaffWorkAttendance['late_times']             = $late_times;//迟到分钟数
            $oneStaffWorkAttendance['is_leave_early']         = $is_leave_early;//是否早退
            $oneStaffWorkAttendance['leave_early_times']      = $leave_early_times;//早退分钟数
            $oneStaffWorkAttendance['working_hours']          = $working_hours;//工作时长 （入库，是乘10后的结果，没有小数点）
            $oneStaffWorkAttendance['is_holiday_vacations']   = $isHoliday;//是否节假日
            $oneStaffWorkAttendance['hub_attendance_date']    = $hub_attendance_date;//hub 考勤规则的考勤日期
            $oneStaffWorkAttendance['settlement_coefficient'] = $this->getSettlementCoefficient($working_hours,
                    $isHoliday) * 100;//入库乘100操作，没有小数点。
            $oneStaffWorkAttendance['is_not_order']           = $is_not_order;//是否在 外协订单中。
            $oneStaffWorkAttendance['is_ot']                  = isset($this->addOtOrderDetailInfo[$oneStaffId]) ? OutsourcingHubStaffAttendanceModel::IS_OT_YES : OutsourcingHubStaffAttendanceModel::IS_OT_NO;//是否在 加班中
            $oneStaffWorkAttendance['ot_id']                  = isset($this->addOtOrderDetailInfo[$oneStaffId]) ? $this->addOtOrderDetailInfo[$oneStaffId]['hub_outsourcing_overtime_id'] : 0;//是否在 加班中

            //存在考勤数据
            $allAbsenceData[] = $oneStaffWorkAttendance;
            $attendanceIdInfo = $this->getStaffAttendanceInfo($oneStaffWorkAttendance);
            if (!empty($attendanceIdInfo)) {
                //如果已经存在上班卡下班卡都有了，且当前新计算的卡是 no order 考勤，则是错误的跳过。错误原因，是上一次统计的订单信息，没拿到上一次的订单。
                if (!empty($attendanceIdInfo['end_at']) && !empty($attendanceIdInfo['started_at']) && $oneStaffWorkAttendance['is_not_order'] == OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES) {
                    continue;
                }
                //已经存在一个卡并且是订单里的， 当前新计算考勤是无订单的不更新原来的数据。
                if ((!empty($attendanceIdInfo['end_at']) || !empty($attendanceIdInfo['started_at'])) && $attendanceIdInfo['is_not_order'] == OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO && $oneStaffWorkAttendance['is_not_order'] == OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES) {
                    continue;
                }
                //已经存在一个卡并且是订单里的， 当前新计算考勤是无订单的不更新原来的数据。
                if ($attendanceIdInfo['is_not_order'] == OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO && $oneStaffWorkAttendance['is_not_order'] == OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES) {
                    continue;
                }
                $this->getDi()->get('logger')->info('[HubAttendanceStatisticsTask-pdc-update-data]' . json_encode($attendanceIdInfo, JSON_UNESCAPED_UNICODE));
                $db->updateAsDict('outsourcing_hub_staff_attendance', $oneStaffWorkAttendance, ["conditions" => "id = ?", 'bind' => $attendanceIdInfo['id']]);
                continue;
            }

            //如果计算出来是没有订单的，查看一下 前一个考勤周期 是否是 '11:00', '11:30', '12:00', '12:30' 这些 班次开始的订单，如果是则 不计入 no order 考勤。
            if ($oneStaffWorkAttendance['is_not_order'] == OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES && in_array($oneStaffWorkAttendance['shift_start'], $this->shift_start_list)) {
                $hub_attendance_date = date('Y-m-d', strtotime("{$oneStaffWorkAttendance['hub_attendance_date']}-1 days"));
                $attendanceIdInfoAgain = $this->getStaffAttendanceInfo(['staff_info_id' => $oneStaffId, 'hub_attendance_date' => $hub_attendance_date]);
                if(!empty($attendanceIdInfoAgain) && in_array($attendanceIdInfoAgain['shift_start'], $this->shift_start_list)) {
                    continue;
                }
                //如果计算的 考勤日期 小于 当前 雇佣日期，则按雇佣日期，查询考勤日期，是否有考勤，如果有，并且 班次开始在 shift_start_list 中 则跳过。。。
                if (strtotime($oneStaffWorkAttendance['hub_attendance_date']) < strtotime($attendance_date)) {
                    $attendanceIdInfoAgainEm = $this->getStaffAttendanceInfo(['staff_info_id' => $oneStaffId, 'hub_attendance_date' => $attendance_date]);
                    if(!empty($attendanceIdInfoAgainEm) && in_array($attendanceIdInfoAgainEm['shift_start'], $this->shift_start_list)) {
                        continue;
                    }
                }

            }
            $allStaffWorkAttendance[] = $oneStaffWorkAttendance;
        }

        $absenceData = $this->getAbsenceData($staffOrder, $allAbsenceData);
        if(!empty($absenceData)) {
            $allStaffWorkAttendance = array_merge($allStaffWorkAttendance, $absenceData);
        }

        if (!empty($allStaffWorkAttendance)) {
            $baseServer->table_batch_insert($allStaffWorkAttendance, 'db_backyard', 'outsourcing_hub_staff_attendance');
        }

        //写入 加班详情表，才能关联上订单，记为加班
        if(!empty($this->addOtOrderDetailInfo)) {
            $addOtOrderDetailInfo = array_values($this->addOtOrderDetailInfo);
            $baseServer = new BaseModel();
            $baseServer->table_batch_insert($addOtOrderDetailInfo, 'db_backyard', 'hub_outsourcing_overtime_detail');
        }
    }

    /**
     * 获取员工 考勤日 打卡数据。
     * @param $params
     * @return array
     */
    public function getStaffAttendanceInfo($params)
    {
        $staffAttendance = OutsourcingHubStaffAttendanceModel::findFirst([
            'conditions' => '  staff_info_id = :staff_id: and hub_attendance_date = :hub_attendance_date: ',
            'bind'       => [
                'staff_id'        => $params['staff_info_id'],
                'hub_attendance_date' => $params['hub_attendance_date'],
            ],
            'columns'    => 'id, started_at, end_at, shift_start, is_not_order',
        ]);

        return !empty($staffAttendance) ? $staffAttendance->toArray() : [];
    }

    /**
     * 获取缺勤数据
     * @param $staffOrder
     * @param $allStaffWorkAttendance
     * @param $staffInfos
     * @param $shift_info
     * @return array
     */
    public function getAbsenceData($staffOrder, $allStaffWorkAttendance)
    {
        $allStaffWorkAttendanceToStaff = array_column($allStaffWorkAttendance, NULL,'staff_info_id');
        $db         = $this->getDI()->get("db_backyard");
        $allStaffAbsenceData = [];
        foreach ($staffOrder as $oneOrder) {
            if(isset($allStaffWorkAttendanceToStaff[$oneOrder['staff_info_id']])) {
                continue;
            }
            if(empty($oneOrder['staff_info_id'])) {
                continue;
            }

            $start = isset($this->shift_info[$oneOrder['shift_id']]) ? $this->shift_info[$oneOrder['shift_id']]['start'] : null;
            $end   = isset($this->shift_info[$oneOrder['shift_id']]) ? $this->shift_info[$oneOrder['shift_id']]['end'] : null;

            $attendance_date = !empty($oneOrder['employment_date']) ? $oneOrder['employment_date'] : null;
            $order_serial_no = !empty($oneOrder['order_serial_no']) ? $oneOrder['order_serial_no'] : null;

            $hub_attendance_date = $attendance_date;

            if(strtotime($attendance_date . ' 00:00:00') <= strtotime($attendance_date . ' ' . $start . ':00') && strtotime($attendance_date . ' ' . $start . ':00') <= strtotime($attendance_date . ' 11:30:00')) {
                $hub_attendance_date = date('Y-m-d', strtotime($attendance_date . '-1 days'));
            }

            $oneStaffWorkAttendance['staff_info_id']       = $oneOrder['staff_info_id'];
            $oneStaffWorkAttendance['job_title']           = $this->staffJobTitleList[$oneOrder['staff_info_id']] ?? 0;;
            $oneStaffWorkAttendance['clocking_date']       = $attendance_date;//实际雇拥日期
            $oneStaffWorkAttendance['order_serial_no']     = $order_serial_no;//匹配外协订单编号
            $oneStaffWorkAttendance['shift_id']            = $oneOrder['shift_id'];

            $oneStaffWorkAttendance['shift_start']         = $start;
            $oneStaffWorkAttendance['shift_end']           = $end;

            $oneStaffWorkAttendance['started_at']          = null;
            $oneStaffWorkAttendance['end_at']              = null;
            $oneStaffWorkAttendance['attendance_store_id'] = $oneOrder['store_id'];
            $oneStaffWorkAttendance['clocking_time']       = '0000-00-00 00:00:00';

            $oneStaffWorkAttendance['is_late']    = 0;//是否迟到
            $oneStaffWorkAttendance['late_times'] = 0;//迟到分钟数

            $oneStaffWorkAttendance['is_leave_early']    = 0;//是否早退
            $oneStaffWorkAttendance['leave_early_times'] = 0;//早退分钟数

            $oneStaffWorkAttendance['working_hours']          = 0;//工作时长 （入库，是乘10后的结果，没有小数点）
            $oneStaffWorkAttendance['is_holiday_vacations']   = 0;//是否节假日
            $oneStaffWorkAttendance['hub_attendance_date']    = $hub_attendance_date;//hub 考勤规则的考勤日期
            $oneStaffWorkAttendance['settlement_coefficient'] = 0;
            $oneStaffWorkAttendance['is_not_order']           = OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO;//是否在 外协订单中。


            $attendanceIdInfo = $this->getStaffAttendanceInfo($oneStaffWorkAttendance);
            if (!empty($attendanceIdInfo)) {
                $this->getDi()->get('logger')->info('[HubAttendanceStatisticsTask-update-data]' . json_encode($attendanceIdInfo, JSON_UNESCAPED_UNICODE));
                $db->updateAsDict('outsourcing_hub_staff_attendance', $oneStaffWorkAttendance, ["conditions" => "id = ?", 'bind' => $attendanceIdInfo['id']]);
                continue;
            }

            $allStaffAbsenceData[] = $oneStaffWorkAttendance;
        }

        return $allStaffAbsenceData;
    }

    /**
     * 用 上班卡 匹配 订单。
     * @param $clock_time
     * @param $storeId
     * @return array|mixed
     */
    public function getMateOrderIn($clock_time, $storeId)
    {
        $allOrder = [];
        foreach ($this->allStaffOrder as $oneOrder) {
            $shiftInfo = $this->shift_info[$oneOrder['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];

            $employment_date = $oneOrder['employment_date'];
            $begin_time      = strtotime($employment_date . ' ' . $shift_start_time . ':00');
            $startTime  = $begin_time - self::OFFSET_TIME;
            $endTime    = $begin_time + self::OFFSET_TIME;
            if (strtotime($clock_time) >= $startTime && strtotime($clock_time) <= $endTime && $oneOrder['store_id'] == $storeId) {
                $allOrder[] = $oneOrder;
            }
        }

        return $allOrder;
    }

    /**
     * 用 下班卡 匹配 订单。
     * @param $clock_time
     * @param $storeId
     * @return array|mixed
     */
    public function getMateOrderOut($clock_time, $storeId)
    {
        $allOrder = [];
        foreach ($this->allStaffOrder as $oneOrder) {
            $shiftInfo = $this->shift_info[$oneOrder['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];
            // 当前order对应的班次结束时间
            $shift_end_time = $shiftInfo['end'];

            $employment_date = $oneOrder['employment_date'];
            $start_time      = $employment_date . ' ' . $shift_start_time . ':00';
            $end_time        = $employment_date . ' ' . $shift_end_time . ':00';
            if (strtotime($end_time) < strtotime($start_time)) {
                // 存在跨天 结束时间需要 + 1天
                $end_time = date('Y-m-d H:i:s', strtotime("{$end_time} +1 day"));
            }

            $startTime = strtotime($end_time) - self::OFFSET_TIME;
            $endTime   = strtotime($end_time) + self::OFFSET_TIME;
            if (strtotime($clock_time) >= $startTime && strtotime($clock_time) <= $endTime && $oneOrder['store_id'] == $storeId) {
                $allOrder[] = $oneOrder;
            }
        }

        return $allOrder;
    }

    public function endStatisticsNoOrder($staffInAllAttendance, $staffOutAllAttendance)
    {
        $staffInAttendance = $staffOutAttendance = [];
        $this->noInOrderStaffIds = array_values(array_unique($this->noInOrderStaffIds));
        foreach ($this->noInOrderStaffIds as $oneStaffId) {
            //没找到 员工上班卡记录，跳过
            if(!isset($staffInAllAttendance[$oneStaffId]) || !isset($staffOutAllAttendance[$oneStaffId])) {
                continue;
            }
            //找出上班卡，匹配的订单
            $inOneOrder = [];
            foreach ($staffInAllAttendance[$oneStaffId] as $oneStaffInAttendance) {
                $orders = $this->getMateOrderIn($oneStaffInAttendance['event_time'], $oneStaffInAttendance['store_id']);
                if(empty($orders)) {
                    continue;
                }
                //由于 打卡时间 正序，所以 多个打卡时间 取最早。同一个订单 就是要取最早打卡时间
                foreach ($orders as $order) {
                    if(isset($inOneOrder[$order['id']])) {
                        if(strtotime($inOneOrder[$order['id']]) > strtotime($oneStaffInAttendance['event_time'])) {
                            $inOneOrder[$order['id']] = $oneStaffInAttendance['event_time'];
                        }
                    } else {
                        $inOneOrder[$order['id']] = $oneStaffInAttendance['event_time'];
                    }
                }
            }

            //找出下班卡，匹配的订单
            $outOneOrder = [];
            foreach ($staffOutAllAttendance[$oneStaffId] as $oneStaffOutAttendance) {

                $orders = $this->getMateOrderOut($oneStaffOutAttendance['event_time'], $oneStaffOutAttendance['store_id']);
                if(empty($orders)) {
                    continue;
                }
                //由于 打卡时间 正序，所以 多个打卡时间 取最早。同一个订单 多个打卡，取最晚的一个卡。
                foreach ($orders as $order) {
                    if(isset($outOneOrder[$order['id']])) {
                        if(strtotime($outOneOrder[$order['id']]) < strtotime($oneStaffOutAttendance['event_time'])) {
                            $outOneOrder[$order['id']] = $oneStaffOutAttendance['event_time'];
                        }
                    } else {
                        $outOneOrder[$order['id']] = $oneStaffOutAttendance['event_time'];
                    }
                }
            }

            //上班卡匹配的订单，下班卡匹配的订单有一个为空，则为 缺卡
            if(empty($inOneOrder) || empty($outOneOrder)) {
                continue;
            }

            $inOneOrderIds  = array_keys($inOneOrder);
            $outOneOrderIds = array_keys($outOneOrder);
            $orderIds       = array_values(array_unique(array_intersect($inOneOrderIds, $outOneOrderIds)));
            //没有交集，则为 不是同一个订单。跳过。
            if(empty($orderIds)) {
                continue;
            }

            //取最早班次的一个订单。
            $mateOrder = [];
            foreach ($this->allStaffOrder as $oneOrder) {
                if(in_array($oneOrder['id'], $orderIds)) {
                    $mateOrder = $oneOrder;
                    break;
                }
            }

            //原订单，存在这个工号，跳过。这里是 处理未在订单内的。
            //未找到 匹配订单的 跳过。
            if(isset($this->staffOrder[$oneStaffId]) || empty($mateOrder)) {
                continue;
            }

            //将 没在订单配置的员工，匹配进到订单里。（实际没在订单里）
            $mateOrder['staff_info_id']    = $oneStaffId;
            $mateOrder['is_not_order']     = OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES;
            $this->staffOrder[$oneStaffId] = $mateOrder;

            //员工上班卡
            $staffInAttendance[$oneStaffId]['staff_info_id']       = $oneStaffId;
            $staffInAttendance[$oneStaffId]['attendance_store_id'] = $mateOrder['store_id'];
            $staffInAttendance[$oneStaffId]['direction']           = AttendanceHikDataModel::DIRECTION_IN;
            $staffInAttendance[$oneStaffId]['started_at']          = $inOneOrder[$mateOrder['id']];

            //员工下班卡
            $staffOutAttendance[$oneStaffId]['staff_info_id']       = $oneStaffId;
            $staffOutAttendance[$oneStaffId]['attendance_store_id'] = $mateOrder['store_id'];
            $staffOutAttendance[$oneStaffId]['direction']           = AttendanceHikDataModel::DIRECTION_OUT;
            $staffOutAttendance[$oneStaffId]['end_at']              = $outOneOrder[$mateOrder['id']];
        }

        return [$staffInAttendance, $staffOutAttendance];
    }


    public function endStatisticsOff($allHikAttendanceData, $staffInAllAttendance = [])
    {
        $staffHikvisionRepository = new StaffHikvisionRepository();

        //去除 pdc 网点的员工。
        $storeInfo = (new SysStoreRepository())->getPdcHikStore();
        $params['pdc_store_ids'] = empty($storeInfo) ? [] : array_column($storeInfo, 'id');
        $params['is_pdc'] = HubOsAttendanceService::IS_PDC_YES;

        //取员工最早的下班卡，排除休息的的下班卡。
        $staffOutAttendance = $staffOutAllAttendance = [];
        foreach ($allHikAttendanceData as $oneOutHikAttendance) {
            //获取员工所有下班卡记录
            $hikAttendanceInfo['event_time'] = $oneOutHikAttendance['event_time'];
            $hikAttendanceInfo['store_id']   = $oneOutHikAttendance['flash_store_name'];
            $staffOutAllAttendance[$oneOutHikAttendance['staff_info_id']][] = $hikAttendanceInfo;

            //如果当前下班卡时间 小于 员工上班卡时间 则跳过。
            if (!empty($staffInAttendance[$oneOutHikAttendance['staff_info_id']]['started_at']) && $staffInAttendance[$oneOutHikAttendance['staff_info_id']]['started_at'] >= $oneOutHikAttendance['event_time']) {
                continue;
            }

            //判断 下班卡是否为，休息下班卡，如果是则跳过
            if (isset($staffInAllAttendance[$oneOutHikAttendance['staff_info_id']]) && $this->isRestOut($oneOutHikAttendance['event_time'],
                    $staffInAllAttendance[$oneOutHikAttendance['staff_info_id']])) {
                continue;
            }

            //如果工号不存在订单里 下班时间需要单独处理
            if (!in_array($oneOutHikAttendance['staff_info_id'], $this->staffOrderIds)) {
                $this->noInOrderStaffIds[] = $oneOutHikAttendance['staff_info_id'];
                continue;
            }

            /**
             * 2. 下班打卡时间：取订单班次开始时间向后的最早一条非休息下班打卡记录。
             * 例：班次起始时间15点，则取15点以后最早的一条非休息（下班打卡时间后90分钟内无上班打卡记录，泰国吃饭时间需要打上下班卡）下班打卡记录。
             * 2. 下班打卡区间：班次开始时间< x <=班次结束时间+1小时
             */
            $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['staff_info_id']       = $oneOutHikAttendance['staff_info_id'];
            $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['attendance_store_id'] = $oneOutHikAttendance['flash_store_name'];
            $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['direction']           = AttendanceHikDataModel::DIRECTION_OUT;

            //只是处理在订单中的数据
            // 班次信息
            $shiftInfo = $this->shift_info[$this->staffOrder[$oneOutHikAttendance['staff_info_id']]['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];
            // 当前order对应的班次结束时间
            $shift_end_time = $shiftInfo['end'];
            // 当前order对应结束班次 加上1小时
            $forward_time = date('H:i', strtotime('+1 hour', strtotime($shift_end_time)));

            $employment_date = $this->staffOrder[$oneOutHikAttendance['staff_info_id']]['employment_date'];
            $start_time      = $employment_date.' '.$shift_start_time.':00';
            $end_time        = $employment_date.' '.$forward_time.':00';
            if ($end_time < $start_time) {
                // 存在跨天 结束时间需要 + 1天
                $end_time = date('Y-m-d H:i:s', strtotime("{$end_time} +1 day"));
                //$start_time     = $yesterday_date.' '.$shift_start_time.':00';
            }

//            // 数据按照时间从小排序，取最晚的一条
//            if (strtotime($start_time) < strtotime($oneOutHikAttendance['event_time']) && strtotime($oneOutHikAttendance['event_time']) <= strtotime($end_time)) {
//                if (isset($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'])) {
//                    if ($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] < $oneOutHikAttendance['event_time']) {
//                        $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] = $oneOutHikAttendance['event_time'];
//                    }
//                } else {
//                    $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] = $oneOutHikAttendance['event_time'];
//                }
//            }

            $serial_no = $this->staffOrder[$oneOutHikAttendance['staff_info_id']]['order_serial_no'];
            $otInfo = $this->getOvertimeDuration([
                'serial_no' => $serial_no,
                'staff_id'  => $oneOutHikAttendance['staff_info_id'],
                'pdc_store_ids'  => $params['pdc_store_ids'],
                'is_pdc'  => $params['is_pdc'],
            ], $oneOutHikAttendance['staff_info_id'], $end_time);

            //如何知道这个人加班。$otInfo['is_staff']
            //结束时间 晚于 外协订单结束时间。并且是非 加班订单里的人。匹配
            $outEndTime = strtotime($otInfo['end_time']) > strtotime($end_time) ? $otInfo['end_time'] : $end_time;
            if(!$otInfo['is_staff'] && !isset($this->otOrderInfo[$otInfo['ot_serial_no']])) {
                $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_serial_no'] = $otInfo['ot_serial_no'];//加班订单编号
                $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_demand_num'] = $otInfo['ot_demand_num'];//申请加班人数
                $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_residue_demand_num'] = $otInfo['ot_demand_num'];//申请加班剩余人数
            }

            // 数据按照时间从小排序，取最晚的一条
            if (strtotime($start_time) < strtotime($oneOutHikAttendance['event_time']) && strtotime($oneOutHikAttendance['event_time']) <= strtotime($outEndTime)) {
                if (isset($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'])) {
                    if ($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] < $oneOutHikAttendance['event_time']) {
                        $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] = $oneOutHikAttendance['event_time'];
                    }
                } else {
                    $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] = $oneOutHikAttendance['event_time'];
                }

                //如果 下班卡超出 外协订单的 下班卡 打卡区间， 且 当前人所在订单为加班订单  则 为加班
                if($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] > $end_time && !$otInfo['is_staff']) {
                    //加班人数 -1 操作。
                    if (!isset($this->addOtOrderDetailInfo[$oneOutHikAttendance['staff_info_id']])) {
                        if ($this->otOrderInfo[$otInfo['ot_serial_no']]['ot_residue_demand_num'] > 0) {
                            $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_residue_demand_num']--;
                        }

                        $hubStaffList = $staffHikvisionRepository->getHubStaffInfo(['staff_info_ids' => [$oneOutHikAttendance['staff_info_id']]]);
                        $company_id = empty($hubStaffList) ? 0 : $hubStaffList[0]['outsourcing_company_id'];

                        $addOtOrderDetailInfo['staff_id']                                  = $oneOutHikAttendance['staff_info_id'];
                        $addOtOrderDetailInfo['hub_outsourcing_overtime_id']               = $otInfo['ot_id'];
                        $addOtOrderDetailInfo['serial_no']                                 = $otInfo['ot_serial_no'];
                        $addOtOrderDetailInfo['company_id']                                = $company_id;
                        $addOtOrderDetailInfo['outsourcing_order_serial_no']               = $serial_no;
                        $this->addOtOrderDetailInfo[$oneOutHikAttendance['staff_info_id']] = $addOtOrderDetailInfo;
                    }
                }
            }
        }

        return [$staffOutAttendance, $staffOutAllAttendance];
    }

    public function endStatisticsOn($allHikAttendanceData)
    {
        //取员工最早的上班卡
        $staffInAttendance = $staffInAllAttendance = [];
        foreach ($allHikAttendanceData as $oneInHikAttendance) {
            //获取员工所有上班卡记录
            $hikAttendanceInfo['event_time'] = $oneInHikAttendance['event_time'];
            $hikAttendanceInfo['store_id'] = $oneInHikAttendance['flash_store_name'];
            $staffInAllAttendance[$oneInHikAttendance['staff_info_id']][] = $hikAttendanceInfo;

            //如果工号不存在在订单里
            if (!in_array($oneInHikAttendance['staff_info_id'], $this->staffOrderIds)) {
                $this->noInOrderStaffIds[] = $oneInHikAttendance['staff_info_id'];
                continue;
            }

            // 只是处理存在订单中的数据
            $staffInAttendance[$oneInHikAttendance['staff_info_id']]['staff_info_id']       = $oneInHikAttendance['staff_info_id'];
            $staffInAttendance[$oneInHikAttendance['staff_info_id']]['attendance_store_id'] = $oneInHikAttendance['flash_store_name'];
            $staffInAttendance[$oneInHikAttendance['staff_info_id']]['direction']           = AttendanceHikDataModel::DIRECTION_IN;

            /**
             * 存在外协订单的一定存在班次，
             * 此需求只是处理有班次的情况
             * 原规则：1. 上班打卡时间：取订单班次起始时间向前一小时内最近的上班打卡记录，如未取到则查找班次起始时间向后至班次结束时间内最近的一条上班打卡记录。
             * 例：班次起始时间为15点-0点，则上班时间取14点至15点间的上班打卡记录，如果没有，则取15点以后至班次结束时间（0点）内最早的一条上班打卡记录，如也没有，则为空。
             * 新规则：上班打卡区间：班次开始时间-1小时 <= x < 班次结束时间, 如果存在多条是则获取第一条时间最早的那条
             */

            // th正式环境数据存在shift_begin_time 或者 shift_end_time 为空的的数据，如果为空 则使用班次获取时间
            $shiftInfo = $this->shift_info[$this->staffOrder[$oneInHikAttendance['staff_info_id']]['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];
            // 当前order对应的班次结束时间
            $shift_end_time = $shiftInfo['end'];

            $employment_date = $this->staffOrder[$oneInHikAttendance['staff_info_id']]['employment_date'];
            $start_time      = $employment_date.' '.$shift_start_time.':00';
            // 当前order对应班次起始时间减去一个小时
            $start_time      = date('Y-m-d H:i:s', strtotime('-1 hour', strtotime($start_time)));

            $end_time        = $employment_date.' '.$shift_end_time.':00';
            if ($shift_end_time < $shift_start_time) {
                // 跨天
                $end_time = date('Y-m-d H:i:s', strtotime("{$end_time} +1 day"));
            }
            // 海康打卡时间
            $oneInHikAttendance_event_time = $oneInHikAttendance['event_time'];
            if (strtotime($start_time) <= strtotime($oneInHikAttendance_event_time) && strtotime($oneInHikAttendance_event_time) < strtotime($end_time)) {
                // 取首次打卡时间，如果存在则不在更新该时间
                if (isset($staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'])) {
                    if ($staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'] > $oneInHikAttendance_event_time) {
                        $staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'] = $oneInHikAttendance_event_time;
                    }
                } else {
                    $staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'] = $oneInHikAttendance_event_time;
                }
            }
        }

        return [$staffInAttendance, $staffInAllAttendance];
    }

    /**
     * 获取打卡数据
     * @param $params
     * @return mixed
     */
    public function getHikAttendanceRecord($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['staff_info_id', 'date_at', 'event_time', 'direction', 'flash_store_name']);
        $builder->from(AttendanceHikRecordModel::class);
        if(!empty($params['staff_info_ids'])) {
            $builder->andWhere('staff_info_id in ({staff_info_ids:array})',
                ['staff_info_ids' => $params['staff_info_ids']]);
        }

        $builder->andWhere('event_time >= :start_time: and event_time < :end_time:',
            ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
        $builder->orderBy('staff_info_id ASC, event_time ASC');


        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 判断 下班卡是否为休息时打的。
     * @param $event_time
     * @param $staffInAllAttendance
     * @return bool
     */
    public function isRestOut($event_time, $staffInAllAttendance)
    {
        foreach ($staffInAllAttendance as $oneAttendance) {
            if(empty($oneAttendance['event_time'])) {
                continue;
            }
            $startEventTime = strtotime($event_time);
            $endEventTime = strtotime($event_time) + 90 * 60;
            $inAttendance = strtotime($oneAttendance['event_time']);
            if($startEventTime < $inAttendance && $inAttendance < $endEventTime) {
                return true;
            }
        }

        return false;
    }

    public function statisticsOt($date)
    {
        $currentTime = !empty($date) ? $date . ' 23:59:59' : date('Y-m-d H:i:s');
        //去除 pdc 网点的员工。
        $storeInfo = (new SysStoreRepository())->getPdcHikStore();
        $params['pdc_store_ids'] = empty($storeInfo) ? [] : array_column($storeInfo, 'id');
        $params['is_pdc'] = HubOsAttendanceService::IS_PDC_YES;
        $params['attendance_date_is_null'] = 1;//获取，审批通过且，没有统计 加班的 加班申请
        $otList = (new HubOutsourcingOvertimeRepository())->getHubOtOrder($params);

        if(empty($otList)) {
            // todo 加日志
            $this->logger->info(['statisticsOt' => '当前没有外协加班信息']);
            return false;
        }

        $this->hubStaffList = (new StaffHikvisionRepository())->getHubStaffInfo($params);
        $this->staffJobTitleList = array_column($this->hubStaffList, 'job_title', 'staff_info_id');

        //班次
        $shift = (new HrShiftService())->getList(['is_all' => true]);
        //班次信息
        $this->shift_info = array_column($shift, null, 'id');
        $this->holidays = $this->getHoliday();

        //按加班申请 分组
        $allOtList = $allOtDetailList = $allOtInfoMinutesList = [];
        foreach ($otList as $oneData) {
            $allOtList[$oneData['serial_no']][] = $oneData['outsourcing_order_serial_no'];
            if(!isset($allOtInfoMinutesList[$oneData['serial_no']])) {
                $duration  = $this->getMinuteByDuration($oneData['duration']);
                $oneInfo['minutes'] = $duration;
                $oneInfo['ot_date'] = $oneData['ot_date'];
                $oneInfo['shift_id'] = $oneData['shift_id'];
                $allOtInfoMinutesList[$oneData['serial_no']] = $oneInfo;
            }
        }
        $db_by  = $this->getDI()->get('db_backyard');

        $hrOutsourcingOrderRepository = new HrOutsourcingOrderRepository();
        foreach ($allOtList as $serial_no => $orderNos) {
            $orderList = $hrOutsourcingOrderRepository->getOrderListByNo(['serial_nos' => $orderNos]);
            $orderDetailList = $hrOutsourcingOrderRepository->getOrderDetailListByNo(['serial_nos' => $orderNos]);
            //没有符合条件的订单，跳过
            if(empty($orderList)) {
                continue;
            }

            $this->otOrderInfo = [];//存在加班的订单信息
            $this->addOtOrderDetailInfo = [];//存入加班详情的数据（工号）

            $offParams['off_order'] = $orderList;//下班订单
            $offParams['off_order_staff'] = $orderDetailList;//下班订单的人

            //找出下班订单 班次最早时间。（订单中可能会有 半天班次）
            $oneOrder = $orderList[0];
            $shiftInfo = $this->shift_info[$oneOrder['shift_id']];

            $start_time      = $oneOrder['employment_date'].' '.$shiftInfo['start'].':00';
            $end_time        = $oneOrder['employment_date'].' '.$shiftInfo['end'].':00';
            if ($end_time < $start_time) {
                // 存在跨天 结束时间需要 + 1天
                $end_time = date('Y-m-d H:i:s', strtotime("{$end_time} +1 day"));
                //$start_time     = $yesterday_date.' '.$shift_start_time.':00';
            }

            $minutes = isset($allOtInfoMinutesList[$serial_no]) ? $allOtInfoMinutesList[$serial_no]['minutes'] : 0;
            $end_time     = date('Y-m-d H:i:s', strtotime("+{$minutes} minutes", strtotime($end_time)));

            $offParams['record_start_time'] = date('Y-m-d H:i:s', strtotime("{$start_time}-1 hours"));//订单中最早班次开始时间 -1 小时；
            $offParams['record_end_time'] = date('Y-m-d H:i:s', strtotime("{$end_time}+1 hours"));//当前时间下班 + 1 小时；

            //当前时间 小于 加班后结束时间，跳过。
            if(strtotime($offParams['record_end_time']) > strtotime($currentTime)) {
                continue;
            }

            $result = $this->getOffAttendanceInfo($offParams);

            // 当日12点
            $cur_date_time = date('Y-m-d 12:00:00', strtotime($oneOrder['employment_date']));

            // 加班所属班次的开始时间 >= 当日的12点 考勤日属于第二天
            if (strtotime($start_time) >= strtotime($cur_date_time)) {
                $hubAttendanceDate = date('Y-m-d', strtotime($oneOrder['employment_date']));
            } else {
                $hubAttendanceDate = date('Y-m-d', strtotime('-1 day', strtotime($oneOrder['employment_date'])));
            }
            //修改 加班订单 考勤日期
            if($result) {
                $db_by->updateAsDict('hub_outsourcing_overtime', ['attendance_date' => $hubAttendanceDate], 'serial_no = ' . $serial_no);

            }
        }
    }

    /**
     * 如果员工存在加班，班次结束时间 + 上加班时长
     * @param array $params
     * @param string $staff_ifo_id
     * @param string $end_time
     * @return array
     */
    public function getOvertimeDuration(array $params, string $staff_ifo_id, string $end_time)
    {
        $is_detail_staff = false;//兼容老数据，在加班详情中的，则不再写入。（如果重新跑，则相当于已经给配置到详情里了。）
        // 查询员工有没有加班数据
        $ot_list = $this->getHubOutsourcingOvertime($params);
        if (isset($ot_list[$staff_ifo_id]) && !empty($ot_list[$staff_ifo_id]['duration'])) {
            $duration = $ot_list[$staff_ifo_id]['duration'];
            $minutes  = $this->getMinuteByDuration($duration);
            $end_time = date('Y-m-d H:i:s', strtotime("+{$minutes} minutes", strtotime($end_time)));
            $is_detail_staff = true;
        }
        $ot_serial_no = '';//加班订单号。
        $ot_id = 0;//加班订单号。
        $demand_num = 0;//18484 新版，只有加班人数，没有指定工号。这里只处理，没有指定工号的加班订单。
        if(empty($ot_list)) {
            $otOrderInfo = (new HubOutsourcingOvertimeRepository())->getHubOutsourcingOvertimeOrder($params);
            if (!empty($otOrderInfo)) {
                //重复执行，detail 里会有 工号信息，相当于，已经配置过。
                $detailInfo = HubOutsourcingOvertimeRepository::getHubOtDetailInfo($otOrderInfo['id']);
                $useStaffNum = !empty($detailInfo) ? count($detailInfo) : 0;
                $demand_num = $otOrderInfo['demand_num'] - $useStaffNum;
                // 存在加班情况：申请加班通过后，下班打卡区间为班次开始时间<x<=加班开始时间+加班时长(小时)+1小时
                $minutes      = $this->getMinuteByDuration($otOrderInfo['duration']) + 60; //+ 60分钟 1小时
                $end_time     = date('Y-m-d H:i:s', strtotime("+{$minutes} minutes", strtotime($otOrderInfo['start_time'])));
                $demand_num   = $demand_num >= 0 ? $demand_num : 0;
                $ot_serial_no = $otOrderInfo['serial_no'];
                $ot_id        = $otOrderInfo['id'];
            }
        }

        return ['end_time' => $end_time, 'is_staff' => $is_detail_staff, 'ot_demand_num' => $demand_num, 'ot_id' => $ot_id,'ot_serial_no' => $ot_serial_no];
    }


    /**
     * 根据加班时长获取对应的分钟数
     * @param $index
     * @return int
     */
    public function getMinuteByDuration($index): int
    {
        $minute = 0;
        if (empty($index)) {
            return $minute;
        }
        $mapping = [
            '0.50' => 30,
            '1.00' => 60,
            '1.50' => 90,
            '2.00' => 120,
            '2.50' => 150,
            '3.00' => 180,
            '3.50' => 210,
            '4.00' => 240,
            '4.50' => 270,
            '5.00' => 300,
            '5.50' => 330,
            '6.00' => 360,
            '6.50' => 390,
            '7.00' => 420,
            '7.50' => 450,
            '8.00' => 480,
        ];
        if (!empty($mapping[$index])) {
            return $mapping[$index];
        }
        return $minute;
    }

    /**
     * 查询考勤日的 所有非取消订单员工
     * @param array $params
     * @return array
     */
    public function getHubOutsourcingOvertime(array $params = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(["o.duration", "d.staff_id"]);
        $builder->from(['o' => HubOutsourcingOvertimeModel::class]);
        $builder->leftJoin(HubOutsourcingOvertimeDetailModel::class, 'd.hub_outsourcing_overtime_id = o.id', 'd');
        $builder->andWhere('o.apply_state = :apply_state:', ['apply_state' => 2]);
        $builder->andwhere('d.outsourcing_order_serial_no = :outsourcing_order_serial_no:',
            ['outsourcing_order_serial_no' => $params['serial_no']]);
        $builder->andWhere('d.staff_id = :staff_id:', ['staff_id' => $params['staff_id']]);

        //兼容是否是pdc
        $sql = '';
        if(!empty($params['is_pdc']) && $params['is_pdc'] == HubOsAttendanceService::IS_PDC_NO) {
            $sql = 'o.store_id not in ({pdc_store_ids:array})';
        }

        if(!empty($params['is_pdc']) && $params['is_pdc'] == HubOsAttendanceService::IS_PDC_YES) {
            $sql = 'o.store_id in ({pdc_store_ids:array})';
        }

        if(!empty($sql) && !empty($params['pdc_store_ids'])) {
            $builder->andWhere($sql, ['pdc_store_ids' => $params['pdc_store_ids']]);
        }

        $list = $builder->getQuery()->execute()->toArray();
        return array_column($list, null, 'staff_id');
    }

    /**
     * pdc 补卡数据
     * @param $currentDay
     */
    public function cardReplacementStatistics($currentDay)
    {
        $storeInfo = (new SysStoreRepository())->getPdcHikStore();
        $pdc_store_ids = empty($storeInfo) ? [] : array_column($storeInfo, 'id');

        $params['pdc_store_ids'] = $pdc_store_ids;
        $params['is_pdc'] = HubOsAttendanceService::IS_PDC_YES;

        $staffList = (new StaffHikvisionRepository())->getHubStaffInfo($params);
        $this->genCorrection($currentDay, $staffList);
    }



}