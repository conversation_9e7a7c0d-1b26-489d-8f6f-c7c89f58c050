<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\Enums\EnumSingleton;
use App\Library\Exception\BusinessException;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SettingEnvModel;
use App\Library\Enums;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\VehicleInfoModel;
use App\Models\coupon\MessageCourierModel;
use App\Repository\StaffInfoRepository;
use App\Services\MessagesService;
use App\Services\SettingEnvService;
use App\Services\StaffService;
use mysql_xdevapi\Exception;

class StaffNoticeService extends BaseService
{
    protected $need_vehicle_message_job_title = [];

    public function setNeedVehicleMessageJobTitle($job_title)
    {
        $this->need_vehicle_message_job_title = $job_title;
    }


    public function fillBankAccount($params): bool
    {
        $city    = (new SettingEnvService())->getSetVal('s_f_d_nationality');
        $builder = $this->modelsManager->createBuilder();
        $colum   = ' h.staff_info_id,h.working_country,h.state,h.formal,h.is_sub_staff,h.hire_type,h.bank_no';
        $builder->columns($colum);
        $builder->from(['h' => HrStaffInfoModel::class]);
        $builder->where('h.state in (1,3) AND h.formal = 1 AND h.is_sub_staff = 0 AND h.working_country = :working_country:',
            ['working_country' => $city]);
//        if (isCountry('TH')){
//            $builder->leftJoin(HrStaffAnnexInfoModel::class,
//                'h.staff_info_id = a.staff_info_id and a.type=' . HrStaffAnnexInfoModel::TYPE_BANK_CARD, 'a');
//            $builder->andWhere("a.audit_state is null");
//        }else{
//            $builder->andWhere(" h.bank_no IS NULL OR h.bank_no = '' ");
//        }
        if (isset($params[0])) {
            $builder->andWhere("h.staff_info_id = :staff_info_id:", ['staff_info_id' => intval($params[0])]);
        }
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return true;
        }

        $staffsLang     = (new StaffService())->getStaffEquipmentLanguage(array_column($list,
            'staff_info_id'));
        $messageService = new MessagesService();

        // 获取附件信息
        $staff_annex_info = HrStaffAnnexInfoModel::find([
            'conditions' => 'staff_info_id in ({staff_info_id:array}) and type = :type:',
            'bind' => ['staff_info_id' => array_column($list,
                'staff_info_id'), 'type'=>HrStaffAnnexInfoModel::TYPE_BANK_CARD]
        ])->toArray();
        $staff_annex_info = array_column($staff_annex_info,null,'staff_info_id');
        foreach ($list as $item) {
            if (isCountry('TH') && !empty($staff_annex_info[$item['staff_info_id']]) && !is_null($staff_annex_info[$item['staff_info_id']]['audit_state'])){
                // th 只看附件是否为待上传
                continue;
            }
            if (isCountry('PH')){
                if (in_array($item['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
                    // ph 个人代理的话 只看附件是否为待上传
                    if (!empty($staff_annex_info[$item['staff_info_id']]) && !is_null($staff_annex_info[$item['staff_info_id']]['audit_state'])){
                        continue;
                    }
                }else{
                    // ph 非个人代理的话 只看卡号是否为空
                    if (!empty($item['bank_no'])){
                        continue;
                    }
                }
            }

            if (!isCountry(['TH','PH','MY'])){
                // 除th ph 外国家 只看卡号是否为空 之前逻辑
                if (!empty($item['bank_no'])){
                    continue;
                }
            }
            $staffLang    = $staffsLang[$item['staff_info_id']] ?? getCountryDefaultLang();
            $t            = BaseService::getTranslation($staffLang);
            $message_category      = Enums\MessageEnums::MESSAGE_CATEGORY_FILL_BANK_CARD;
            $message_category_code = 0;
            $title = $t->_('notice_no_bank_account_title');
            $content = '';

            //马来卡号和附件为空
            if (isCountry('MY')) {
                if (in_array($item['hire_type'],HrStaffInfoModel::$agentTypeTogether)){
                    // my 已经有这个脚本发过了 send_bank_card_annex_reminder 产品定的不需要再发一遍
                    continue;
                }
                if (!empty($item['bank_no']) && !empty($staff_annex_info[$item['staff_info_id']]['annex_path_front'])){
                    $this->logger->info('bank_no and annex_path_front existence ' . $item['staff_info_id']);
                    continue;
                }
            }

            if (isCountry('PH')) {
                if (in_array($item['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
                    $title = $t->_('notice_no_bank_account_title_independent_contractor');
                    $content = 'independent_contractor';
                } else {
                    $message_category      = Enums\MessageEnums::CATEGORY_SIGN;
                    $message_category_code = Enums\MessageEnums::CATEGORY_SIGN_CODE_FILL_BANK_ACCOUNT_PH;
                    $title                 = $t->_('notice_no_bank_account_title_ph');
                }
            }

            $send_message = [
                'staff_users'        => [$item['staff_info_id']],
                'message_title'      => $title,
                'message_content'    => $content,
                'staff_info_ids_str' => $item['staff_info_id'],
                'category'           => $message_category,
                'category_code'      => $message_category_code,
                'push_state'         => 1,
                'id'                 => time() . $item['staff_info_id'] . rand(1000000, 9999999),
            ];
            $res          = $messageService->add_kit_message($send_message);
            $this->logger->info(['send_message' => $send_message, 'result' => $res]);
        }
        return true;
    }

    /**
     * 完善车辆信息
     * 获取待发送员工
     * @param int $staff_info_id
     * @param int $max_id
     * @return mixed
     */
    protected function getVehicleWaitNoticeStaff(int $staff_info_id = 0, int $max_id = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $colum   = 'h.staff_info_id,h.hire_type';
        $builder->columns($colum);
        $builder->from(['v' => VehicleInfoModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'v.uid = h.staff_info_id', 'h');
        $builder->where('h.state = 1 AND h.formal in (1,4) AND h.is_sub_staff = 0 and h.job_title in ({job_title:array})',
            ['job_title' => $this->need_vehicle_message_job_title]);
        $builder->andWhere("v.deleted = :deleted: and v.approval_status IN ({approval_status:array})", [
            'deleted'         => 0,
            'approval_status' => [
                VehicleInfoModel::APPROVAL_STATUS_PENDING,
                VehicleInfoModel::APPROVAL_STATUS_TURN_DOWN,
            ],
        ]);

        $builder->andWhere("h.staff_info_id > :query_max_id:", ['query_max_id' => $max_id]);
        if ($staff_info_id) {
            $builder->andWhere("h.staff_info_id = :staff_info_id:", ['staff_info_id' => $staff_info_id]);
        }
        $builder->orderBy('h.staff_info_id');
        $limit = 500;
        if (RUNTIME == 'dev') {
            // $limit = 1;
        }
        $builder->limit($limit);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 完善车辆信息
     * 获取消息标题和内容的翻译key
     * @param $hire_type
     * @return string[]
     */
    private function getVehicleTitleContent($hire_type): array
    {
        $result = ['vehicle_information_filling_title', 'vehicle_information_filling_content_v21270'];
        switch (strtoupper(env('country_code'))) {
            case 'PH':
                $result = ['vehicle_information_filling_title_ph', 'vehicle_information_filling_content_ph_v21270'];
                break;
            case 'TH':
                if ($hire_type == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                    $result = [
                        'agent_vehicle_information_filling_title_default',
                        'agent_vehicle_information_filling_content_default_v21270',
                    ];
                }
            default:
        }
        return $result;
    }


    /**
     * 完善车辆信息
     * @param $params
     * @return bool
     */
    public function fillVehicleMessage($params): bool
    {
        $staff_info_id = $params[0] ?? 0;
        $max_id        = 0;

        $setting   = new SettingEnvService();
        $job_title = $setting->getSetVal('job_title_vehicle_type', ',');
        if (empty($job_title)) {
            throw new Exception('完善车辆信息配置职位为空');
        }
        //设置需要发送完善消息的职位
        $this->setNeedVehicleMessageJobTitle($job_title);
        $staffService   = new StaffService();
        $messageService = new MessagesService();
        $i              = 0;

        // 前端v2版本
        $vehicle_info_path = env('backyard_ui_url') . '/#/VehicleInfo.pages';

        while (true) {
            $staff_list = $this->getVehicleWaitNoticeStaff(intval($staff_info_id), $max_id);
            if (empty($staff_list)) {
                break;
            }
            $i++;
            $str = '第' . $i . '批 max_staff_id ' . $max_id . PHP_EOL;
            echo $str;
            $this->logger->info($str);
            $staff_info_ids = array_column($staff_list, 'staff_info_id');
            $staffsLang     = $staffService->getStaffEquipmentLanguage($staff_info_ids);
            $max_id         = max($staff_info_ids);
            foreach ($staff_list as $item) {
                $staffLang = $staffsLang[$item['staff_info_id']] ?? getCountryDefaultLang();
                $t         = BaseService::getTranslation($staffLang);
                [
                    $vehicle_information_filling_title,
                    $vehicle_information_filling_content,
                ] = $this->getVehicleTitleContent($item['hire_type']);
                $send_message = [
                    'staff_users'        => [$item['staff_info_id']],
                    'message_title'      => $t->_($vehicle_information_filling_title),
                    'message_content'    => addslashes("<div style='font-size: 35px'>" . $t->_($vehicle_information_filling_content, ['vehicle_info_path' => $vehicle_info_path]) . "</div>"),
                    'staff_info_ids_str' => $item['staff_info_id'],
                    'category'           => -1,
                    'id'                 => time() . $item['staff_info_id'] . rand(1000000, 9999999),
                ];
                $res          = $messageService->add_kit_message($send_message);
                $this->logger->info(['send_message' => $send_message, 'result' => $res]);
            }
        }
        return true;
    }

    /**
     * 发送银行卡附件填写消息提醒
     * @return array
     */
    public function send_bank_card_annex_reminder()
    {
        $return_data = ['count_num' => 0, 'success_staff_ids' => [], 'fail_staff_ids' => []];
        $builder     = $this->modelsManager->createBuilder();
        $builder->columns('s.staff_info_id');
        $builder->from(['s' => HrStaffInfoModel::class]);
        $builder->leftJoin(HrStaffAnnexInfoModel::class,
            's.staff_info_id = a.staff_info_id and a.type=' . HrStaffAnnexInfoModel::TYPE_BANK_CARD, 'a');
        $builder->where(
            's.working_country = :working_country: and s.state != :state: and s.hire_type in ({hire_type:array}) and s.formal = :formal: and s.is_sub_staff = :is_sub_staff: and a.audit_state is null',
            [
                'state'        => HrStaffInfoModel::STATE_RESIGN,
                'hire_type'    => HrStaffInfoModel::$agentTypeTogether,
                'formal'       => HrStaffInfoModel::FORMAL_1,
                'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO,
                'working_country' => Enums::HRIS_WORKING_COUNTRY_3,
            ]
        );
        $staff_list            = $builder->getQuery()->execute()->toArray();
        $staff_list            = $staff_list ? array_values(array_column($staff_list, 'staff_info_id',
            'staff_info_id')) : [];
        $message_category      = Enums\EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_FILL_BANK_CARD_ANNEX');
        $message_category_code = 0;
        $return_data['count_num'] = count($staff_list);
        foreach ($staff_list as $staff_id) {
            $accept_language            = (new StaffService())->getAcceptLanguage($staff_id);
            $t                          = self::getTranslation($accept_language);
            $bank_card_reminder_title   = $t->_('bank_card_reminder_title');
            $bank_card_reminder_content = 'independent_contractor_bank_card_annex';

            //发送消息
            $send_message = [
                'staff_users'        => [$staff_id],
                'message_title'      => $bank_card_reminder_title,
                'message_content'    => $bank_card_reminder_content,
                'staff_info_ids_str' => $staff_id,
                'category'           => $message_category,
                'category_code'      => $message_category_code,
                'push_state'         => 1,
                'id'                 => time() . $staff_id . rand(1000000, 9999999),
            ];

            $res = (new MessagesService())->add_kit_message($send_message);
            $this->logger->info(['send_message' => $send_message, 'result' => $res]);
            $messageId = $res[2][0] ?? '';
            if (!$messageId) {
                $return_data['fail_staff_ids'][] = $staff_id;
                $this->logger->error('send_bank_card_annex_reminder add_kit_message error 参数:'.json_encode($send_message, JSON_UNESCAPED_UNICODE));
            }else{
                $return_data['success_staff_ids'][] = $staff_id;
            }
        }
        return $return_data;
    }

    /**
     * 发送简历完善通知 消息
     * @param $staff_info_id
     * @param $sub_staff_info_id
     * @param $begin_date-支援日期
     * @return bool
     */
    public function sendResumeMessage($staff_info_id, $sub_staff_info_id, $begin_date)
    {
        if(empty($staff_info_id) || empty($sub_staff_info_id)) {
            return false;
        }
        $masterStaffInfo = StaffInfoRepository::getInfoByStaffInfoId($staff_info_id, true);
        $hireData = date('Y-m-d 00:00:00', strtotime($masterStaffInfo['hire_date']));

        //入职日期+15天， 五天之后不再看是否 未读。
        $limitData = date('Y-m-d 00:00:00', strtotime("{$hireData}+15 day"));

        $begin_date = date('Y-m-d 00:00:00', strtotime($begin_date));

        if(strtotime($limitData) < strtotime($begin_date)) {
            return false;
        }

        $category = EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_COMPLETE_RESUME');
        //主账号肯定发过，已读不发
        $messageParams = ['staff_info_id' => $staff_info_id, 'category' => $category,'read_state' => MessageCourierModel::READ_STATE_NO];
        $messageInfo = (new MessageCourierService())->getMessageCourierInfo($messageParams);
        if(empty($messageInfo)) {
            return false;
        }

        //消息标题（泰国泰文，其他国家英文）
        $resume_msg_title = 'Completion of Personal Information Notice';
        if (isCountry()) {

            //泰国用泰文
            $resume_msg_title = 'แจ้งกรอกข้อมูลพนักงาน';

            if (!empty($masterStaffInfo['hire_type']) && $masterStaffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                $resume_msg_title = 'แจ้งการกรอกข้อมูลของผู้รับจ้างขนส่งพัสดุรายชิ้น';
            }
        }
        //签字消息
        $message_param = [
            'id'              => time() . $sub_staff_info_id . rand(1000000, 9999999),
            'staff_info_ids_str' => $sub_staff_info_id,
            'staff_users'        => [$sub_staff_info_id],
            'message_title'      => $resume_msg_title,
            'message_content'    => $messageInfo['id'],
            'category'           => $category,
        ];

        if (
            isCountry('TH')
            &&
            in_array(
                $masterStaffInfo['job_title'],
                [
                    enums::$job_title["van_courier"],
                    enums::$job_title["bike_courier"],
                    enums::$job_title["pickup_driver"],
                ]
            )
        ) {
            //完善简历消息 最晚 完善时间:入职日期+2天，如果在这期间未完善，则发送给子账号。
            $delayDate = date("Y-m-d 00:00:00", strtotime("{$hireData}+2 day"));
            if(strtotime($delayDate) >= strtotime($begin_date)) {
                $message_param['related_id'] = strtotime($delayDate);
            }
        }

        $res = (new MessagesService())->add_kit_message($message_param);
        $this->logger->info([
            'function' => 'StaffSupportStoreServer-ImportStaffSupport-sendResumeMessage',
            'message' => '消息发送成功',
            'params' => $message_param,
            'result' => $res
        ]);

        return true;
    }


}
