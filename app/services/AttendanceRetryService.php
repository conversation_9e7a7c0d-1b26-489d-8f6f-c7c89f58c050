<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\CacheListKeyEnums;
use App\Library\ErrCode;
use App\Models\backyard\AsyncTaskModel;
use App\Models\bi\SettingEnvModel;



class AttendanceRetryService extends BaseService
{
    /**
     * 2025年周期开始使用
     * 带有Daily为日薪 反之为月薪
     * @var string[]
     */
    public $headerV2 = [
        'Department',  //部门
        'Branch', //网点
        'staff ID',  //staff_info_id
        'staff name', //staff_info_name
        'position',  //职位
        'hire date', //入职时期
        'leave date', //离职日期
        'Last Work Day',
        'stop duties date',//停职日期
        'Hire Type',//新增 雇佣类型
        'transfer day',//转岗日期
        'Hire Type(Before transfer)',//新增  转岗前雇佣类型
        'Hire Type(After transfer)',//新增  转岗后雇佣类型
        'oil card is open', //油卡是否开通
        'oil card money', //油卡押金
        'state', //员工状态
        'period', // 周期  4.16-5.15
        'Total days', //总的统计日期 30 天
        'On', //修改仅月薪
        'On(Daily)', //新增 日薪制出勤天数
        'Total On(Daily)', //新增 日薪薪资天数 成为On(Daily)+PH(Daily)+AL(Daily)+CT(Daily)+SL(Daily)+PL(Daily)+BT(Daily)+BTY(Daily)
        'On backyard', //按backyard统计的考勤信息
        'On operator', //按operatorer 计算的考勤信息
        'On SUM',
        'On transfer before',
        'On transfer after',
        'On(Daily) transfer before',//新增 日薪转岗后
        'On(Daily) transfer after',//新增 日薪转岗前
        'Actual On(Daily)',
        'Actual On(Daily) transfer before',
        'Actual On(Daily) transfer after',
        'Total On(Daily) transfer before',//新增
        'Total On(Daily) transfer after',//新增
        'On Feeder B',
        'On Feeder B transfer before',
        'On Feeder B transfer after',
        'LW',//不带薪假期
        'AB',
        'USL', //无薪病假
        'other_UL',//其他不带薪假期
        'LW(Daily)',
        'AB(Daily)',
        'USL(Daily)',
        'other_UL(Daily)',
        'AL',//年假
        'AL(Daily)',
        'SL',//病假
        'SL(Daily)',
        'PL',//带薪假
        'PL(Daily)',
        'PH', //法定节假日
        'PH(Daily)',
        'OFF', //休息日，调休
        'OFF tmp',//晚班
        'CT',//公司培训假期
        'CT transfer before',
        'CT transfer after',
        'CT(Daily)',
        'CT(Daily) transfer before',
        'CT(Daily) transfer after',
        'BT',//出差
        'BT transfer before',
        'BT transfer after',
        'BT(Daily)',
        'BT(Daily) transfer before',
        'BT(Daily) transfer after',
        'BT_Y',//黄牌出差
        'BT_Y transfer before',
        'BT_Y transfer after',
        'BT_Y(Daily)',
        'BT_Y(Daily) transfer before',
        'BT_Y(Daily) transfer after',

        'weekend Day 1 time before', //节假日正常上班 overtime_type = 4 如果无transfer，赋值before
        'PH 1 time before',//新增 日薪制节假日1倍 OT overtime_type = 6
        'Total 1 time before',//1倍OT时长总和 weekend Day 1 time before+PH 1 time before

        'Weekday 1.5 times before', //工作日加班 1.5倍 overtime_type = 1
        'Off Day 2 times before',//新增 日薪制休息日2倍 OT overtime_type = 5
        'Weekend OT 3 times before', //节假日加班 3倍 overtime_type = 2

        'weekend Day 1 time after',
        'PH 1 time  after',//新增 转岗后日薪制节假日1OT
        'Total 1 time after',//新增 转岗后一倍总和

        'Weekday 1.5 times after',
        'Off Day 2 times after',
        'Weekend OT 3 times after',


        'OT times', //ot总的加班小时数 weekend Day 1 time+PH 1 time+Weekday 1.5 +Off Day 2 +Weekend OT 3 + Off Day 2 times + PH 1 time before; after和 before
        'LH', //LH
        'Bike compensation coefficient', //车辆补偿系数
        'Bike compensation coefficient-transfer before',
        'Bike compensation coefficient-transfer after',
        'operator blank count', //业务操作量
        'Night shift',//晚班
        'BT International',//境外出差
    ];
	//导出的 header
	public $header = [
		'Department',  //部门
		'Branch', //网点
		'staff ID',  //staff_info_id
		'staff name', //staff_info_name
		'position',  //职位
		'hire date', //入职时期
		'leave date', //离职日期
		'Last Work Day',
		'stop duties date',//停职日期
		'transfer day',//转岗日期
		'oil card is open', //油卡是否开通
		'oil card money', //油卡押金
		'state', //员工状态
		'period', // 周期  4.16-5.15
		'Total days', //总的统计日期 30 天
		'On', // 出勤时长0.5(4<= T <= 8); 1 (T>=8)
		'On backyard', //按backyard统计的考勤信息
		'On operator', //按operatorer 计算的考勤信息
		'On SUM',
		'On transfer before',
		'On transfer after',
        'On Feeder B',
        'On Feeder B transfer before',
        'On Feeder B transfer after',
		'AL trial', //试用期病假
		'SL trial', //试用期年假
		'PL trial', //试用期带薪事假+产假+陪产假+国家军训假+家人去世假+ leave for sterillization + 婚嫁 + 出家假
		'AL formal', //病假
		'SL formal', //年假
		'PL formal', //带薪事假+产假+陪产假+国家军训假+家人去世假+ leave for sterillization + 婚嫁 + 出家假
		'PH', //法定节假日
		'USL', //无薪病假
		'other_UL',
		'Paid SL (Covid-19)',//带薪病假 新冠治疗
		'Paid SL (Covid-19) transfer before',//带薪病假 新冠治疗
		'Paid SL (Covid-19) transfer after',//带薪病假 新冠治疗
		'Isolation leave',//隔离假
		'Isolation leave transfer before',//隔离假
		'Isolation leave transfer after',//隔离假
		'OFF', //休息日，调休
		'OFF tmp',//晚班
		'OFF-mend',
		'LW', //不带薪事假+受训假+Other
		'CT',
		'CT transfer before',
		'CT transfer after',
		'BT',
		'BT transfer before',
		'BT transfer after',
		'BT_Y',
		'BT_Y transfer before',
		'BT_Y transfer after',
		'AB',
		'AB NEW', //出勤时长 AB + OFF-mend(小于0则不加)
		'weekend Day 1 time', //休息日加假期正常上班小时数 overtime_type = 4
		'times1_PH',
		'times1_other',
		'times1_ALL',
		'Weekday 1.5 times', //工作日加班小时数 overtime_type = 1
		'Weekend OT 3 times', //休息日加假期加班小时数 overtime_type = 2
		'OT times', //ot总的加班小时数
		'LH', //LH
        'Bike compensation coefficient', //车辆补偿系数
        'Bike compensation coefficient-transfer before',
        'Bike compensation coefficient-transfer after',
		'operator blank count', //业务操作量
		'Night shift',//晚班
		'BT International',//境外出差
	];
	
	private function get_logistic_staff(){
		return [31425];
	}
	
	private function get_fulfillment_staff($gongzi_month){
		//获取fullfillment 员工工号
		$year = substr($gongzi_month,0,4);
		if($year<='2020'){
			$month = date('Y-m', strtotime($gongzi_month . " -1 month"));
		}else{
			$month = $gongzi_month;
		}
		//薪资白名单
        $staff_id_ffm =array_column((new GongziBaseService)->get_special_staff($month , GongziBaseService::SPECIAL_STAFF_TYPE_FFM),'staff_info_id') ; //ffm白名单
		if (empty($staff_id_ffm)) {
			return [];
		}
        $this->getDi()->get('logger')->write_log(" get_fulfillment_staff [$gongzi_month]  staff_id_ffm:".json_encode($staff_id_ffm,JSON_UNESCAPED_UNICODE),'info');

        return $staff_id_ffm;
	}
	
	/*
     *  'flash_money'=>30001,
        'flash_pay'=>60001,
        'f_commerce'=>50001,
     */
	private function get_company_staff($gongzi_month,$company_id){
		//type=2部门,type=1公司
		$sys_department = \App\Models\backyard\SysDepartmentModel::class;
		$sql = "SELECT id FROM {$sys_department} where company_id in ({company_id:array}) and type=2 and deleted=0";
		$company_id = is_array($company_id)?$company_id : explode(",", $company_id);
		$data = $this->modelsManager->executeQuery($sql,['company_id'=>array_values($company_id)])->toArray();
		$id_all = array_column($data,'id');
		if(empty($id_all)){
			return [];
		}
		
		$sql_where = " and hsi.sys_department_id in ({hsi_sys_department_id:array}) ";
		$sql_where_params['hsi_sys_department_id'] = $id_all;
		
		[$start_date,$end_date] = $this->get_start_end_by_month($gongzi_month);
		
		$data = (new BackyardAttendanceService())->getStatAttendUser($start_date, $end_date,$sql_where,$sql_where_params);
		if(empty($data)){
			return [];
		}
		$money_staffs = array_column($data,'staff_info_id');
		
		//工资始终在快递部门发的员工
		$gongzi_in_express_staffs = (new  SettingEnvModel())::getSettingEnv('gongzi_in_express_staffs');
		$gongzi_in_express_staff_arr = explode(',',$gongzi_in_express_staffs);
		
		//60641在money工作,但是发工资再express,需要去除
		foreach ($money_staffs as $key=>$staff_id){
			if(in_array($staff_id,$gongzi_in_express_staff_arr)){
				unset($money_staffs[$key]);
			}
		}
		return $money_staffs;
	}
	
	
	//计算日期月份
	public function get_start_end_by_month($gongzi_month){
		$gongzi_prev_month_lastday = date("Y-m-d", strtotime($gongzi_month . '-01') - 3600 * 15);
		$gongzi_prev_month = substr($gongzi_prev_month_lastday, 0, 7);
		
		if(substr($gongzi_month,0,4)>='2021'){
			if($gongzi_month == '2021-01'){
				$start_date = $gongzi_prev_month . '-16';
			}else{
				$start_date = $gongzi_prev_month . '-24';
			}
			$end_date = $gongzi_month . '-23';
			
		}else{
			$start_date = $gongzi_prev_month . '-16';
			$end_date = $gongzi_month . '-15';
		}
		return [$start_date,$end_date];
	}
	
	
	
	//考勤重算
	public function re_cal_attend_ajax($params=[]) {
		
		try {
			$month = $params['month']?? date('Y-m');
			$re_cal_type =$params['re_cal_type']?? 0;
			$staff_id = $params['staff_id']?? '';
			$edit_staff_info_id = $params['edit_staff_info_id'];
			$salary_end = $params['salary_end'];
			$salary_start = $params['salary_start'];
			
			$this->logger->info("重新计算考勤相关数据 操作人: {$edit_staff_info_id} ;生产数据类型为: {$re_cal_type}; 生成数据月份为{$month}; 需要重新计算的员工ID为{$staff_id}; 计算时间为:" .  date('Y-m-d H:i:s').'开始写入异步任务 参数==>'.json_encode($params));
			
			$staff_info_ids = '';
			if (!empty($staff_id)) {
				$staff_info_ids = implode(',', array_map(function ($v) {
					return (int)$v;
				}, explode(',', $staff_id)));
			}
            if (isCountry('PH')) {
               $rhDays = (new \App\Modules\Ph\Services\HolidayService())->getRangeTimeRh($salary_start,$salary_end);
               if ($rhDays) {
                   $haveRhDays = true;
               }
            }
			switch ((int)$re_cal_type) {
				case 0: //Salary Attendance Strict
					$run_param = [
						[
							"task" => "Backyardattendance",
							"action" => "attendance_v2",
							'params' =>  [
								1
								,0
								,$salary_start
								,$salary_end
								,"{$staff_info_ids}",
                                'recal_solid'
							],
						],
                        [
                            "task" => "Backyardattendance",
                            "action" => "cal_bike_compensation_coefficient",
                            'params' =>  [
                                $month
                                ,"{$staff_info_ids}"
                            ],
                        ],
                        [
                            "task" => "Backyardattendance",
                            "action" => "cal_car_compensation_coefficient",
                            'params' =>  [
                                $month
                                ,"{$staff_info_ids}"
                            ],
                        ],
                        [
                            "task"   => "Backyardattendance",
                            "action" => "cal_pd_days",
                            'params' => [
                                $salary_start,
                                $salary_end,
                                "{$staff_info_ids}",
                            ],
                        ],
						[
							"task" => "Backyardattendance",
							"action" => "attendance_date_month",
							'params' =>  [
								$salary_start
								,$salary_end
								,"{$staff_info_ids}"
							],
						],[
							"task" => "Backyardattendance",
							"action" => "calculate_entry_allowance",
							'params' =>  [
                                  $month
                                ,"{$staff_info_ids}"
							],
						],
                        [
                            "task" => "overtime",
                            "action" => "calculateOvertimeSalaryState",
                            'params' =>  [
                                $salary_start
                                ,$salary_end
                                ,"{$staff_info_ids}"
                            ],
                        ],
//						[
//							"task" => "Backyardattendancesolidify",
//							"action" => "main",
//							'params' =>  [
//								2,
//								$salary_start,
//								$salary_end
//							],
//						],
					];
                    if (isCountry('PH') && isset($haveRhDays)) {
                        array_shift($run_param);
                        $run_param = array_merge([
                            [
                                "task" => "Backyardattendance",
                                "action" => "attendance_v2",
                                'params' =>  [
                                    1
                                    ,0
                                    ,$salary_start
                                    ,$salary_end
                                    ,"{$staff_info_ids}",
                                    'recal_solid',
                                    'have_rh',
                                ],
                            ],
                            [
                                "task" => "Backyardattendance",
                                "action" => "attendance_v2",
                                'params' =>  [
                                    1
                                    ,0
                                    ,$salary_start
                                    ,$salary_end
                                    ,"{$staff_info_ids}",
                                    'recal_solid',
                                    'have_rh',
                                ],
                            ],
                        ],$run_param);
                    }
					break;
				case 1: //Salary Attendance Kind
					$run_param = [
						[
							"task" => "Backyardattendance",
							"action" => "attendance_v2",
							'params' =>  [
								1
								,1
								,$salary_start
								,$salary_end
								,"{$staff_info_ids}"
							],
						]
					];
					break;
				case 2: //Incentive Attendance
					$start = date('Y-m-01', strtotime($month));
					$end = date('Y-m-d', strtotime("{$start} +1 month -1 day"));
					$run_param = [
						[
							"task" => "Backyardattendance",
							"action" => "attendance_v2",
							'params' =>  [
								1
								,1
								,$start
								,$end
								,"{$staff_info_ids}"
							],
						],
						[
							"task" => "incentive_cut",
							"action" => "SyncIncentiveCut",
							'params' =>  [
								$start
								,$end
							],
						],
					];
					break;
				case 3: //Attendance Data Month
					$run_param = [
						[
							"task" => "Backyardattendance",
							"action" => "attendance_date_month",
							'params' =>  [
								$salary_start
								,$salary_end
								,"{$staff_info_ids}"
							],
						]
					];
					break;
                case 4: //Hub Full Attendance
                    $start_date = $month.'-01';
                    $end_date = date('Y-m-d', strtotime("{$start_date} +1 month -1 day"));
                    $run_param = [
                        [
                            "task"   => "Backyardattendance",
                            "action" => "attendance_v2",
                            'params' => [
                                1,
                                0,
                                $start_date,
                                $end_date,
                                "{$staff_info_ids}",
                            ],
                        ],
                        [
                            "task"   => "Backyardattendance",
                            "action" => "hub_full_attendance_recalculate",
                            'params' => [
                                $month,
                                "{$staff_info_ids}",
                            ],
                        ],
                    ];
                    break;
                default:
                    break;
			}
			
			if (!empty($run_param)) {
				$type = empty($staff_info_ids) ? 1 : 2;
				if (!empty($this->getAsyncTask('0', $re_cal_type, 1))) {
					$result =  ['code' => ErrCode::VALIDATE_ERROR, 'message' => '任务正在运行，请在列表查看!', []];
				}else{
					$ret = $this->createAsyncTask($edit_staff_info_id,$run_param, $type, $re_cal_type);
					if ($ret) {
						$this->logger->info("重新计算考勤相关数据 操作人: {$edit_staff_info_id} ;生产数据类型为: {$re_cal_type}; 生成数据月份为{$month}; 需要重新计算的员工ID为{$staff_id}; 计算时间为:" .  date('Y-m-d H:i:s')."写入异步任务结束");
						$result =  ['code' => ErrCode::SUCCESS, 'message' => 'success!', []];
					} else {
						$this->logger->info("重新计算考勤相关数据 操作人: {$edit_staff_info_id} ;生产数据类型为: {$re_cal_type}; 生成数据月份为{$month}; 需要重新计算的员工ID为{$staff_id}; 计算时间为:" . date('Y-m-d H:i:s')."写入异步任务结束  失败");
						$result =  ['code' => ErrCode::VALIDATE_ERROR, 'message' => '请稍后重试!', []];
					}
				}
				
			} else {
				$result =  ['code' => ErrCode::VALIDATE_ERROR, 'message' => '计算类型错误!', []];
			}
			
		} catch (\Exception $e) {
			$this->logger->error("重新计算考勤数据异常，原因可能是: ".$e->getMessage());
			$result =  ['code' => ErrCode::SYSTEM_ERROR, 'message' => '请稍后重试!', []];
		}
		return $result;
		
	}
	
	/**
     * 获取异步总数
     * @param $state
     * @param $category
     * @param null $createTime
     * @return mixed
     */
    public function getAsyncTaskTotal($state, $category, $createTime = null)
    {
        $where      = "state IN ({state:array}) AND created_at >= :created_at: ";
        $where_bind = [
            'state'      => is_array($state) ? $state : explode(',', $state),
            'created_at' => $createTime,
        ];
        if (!is_null($category)) {
            $where                  .= " AND category IN ({category:array}) ";
            $where_bind['category'] = is_array($category) ? $category : explode(',', $category);
        }
        return AsyncTaskModel::find([
            "conditions" => $where,
            'bind'       => $where_bind,
        ])->count();
    }
	//获取异步任务
	public function getAsyncTask($state = '0', $category = null, $type = null,$page=1,$size=100,$order='',$validTime = null) {
		
		
		$page = (int)$page;
		$size =  (int)$size;
		$offset = $size * ($page - 1);
		if (empty($validTime)) {
            $validTime = date('Y-m-d H:i:s', strtotime(' 3 days ago'));
        }
		$where = "state IN ({state:array})
                AND created_at >= :created_at: ";
		$where_bind = [
			'state'=> is_array($state) ? $state : explode(',',$state),
			'created_at'=>$validTime,
		];
		if (!is_null($category)) {
			$where .= " AND category IN ({category:array}) ";
			$where_bind['category']=is_array($category) ? $category : explode(',',$category);
		}
		
		if (!is_null($type)) {
			$where .= " AND type IN ({type:array}) ";
			$where_bind['type']=is_array($type) ? $type : explode(',',$type);
		}
		$query = [
            "conditions" => $where,
            'bind'       => $where_bind,
            'columns'    => "id,
                    staff_info_id,
                    run_param,
                    category,
                    type,
                    state,CONVERT_TZ( created_at, '+00:00', '{$this->timeZone}' ) as created_at,
                    CONVERT_TZ( updated_at, '+00:00', '{$this->timeZone}' ) as updated_at  ",
            'limit'      => $size,
            'offset'     => $offset
        ];
		if($order){
		    $query['order'] = $order;
        }
		return AsyncTaskModel::find($query)->toArray();
	}
	
	//创建异步任务
	public function createAsyncTask($staff_info_id, $run_param, $type, $category) {
		//进行任务的插入
		$insert_item = [
			'staff_info_id' => $staff_info_id,
			'run_param' => json_encode($run_param),
			'type' => $type,
			'category' => $category,
		];
		$result      = $this->getDI()->get('db_backyard')->insertAsDict((new AsyncTaskModel())->getSource(), $insert_item);
		if ($result) {
			return $this->getDI()->get('db_backyard')->lastInsertId();
		} else {
			return false;
		}
	}
	
	//更新异步任务
	public function updateAsyncTask($id,int $state,$remark='') {
		
		return $this->getDI()->get('db_backyard')->updateAsDict(
			(new AsyncTaskModel())->getSource(),
			[
				'state' => $state,
				'remark'=>$remark,
			],
			[
				'conditions' => 'id = ?',
				'bind'       => [$id],
				'bindTypes'  => [\PDO::PARAM_INT], // Optional parameter
			]
		);
	}
	
	//一些规则
	public function parcelBase($start_date) {
		
		switch (substr($start_date,0,7)) {
			case '2019-05':
				$parcel_base_all = 0;
				$parcel_base_except = 14;
				$where = '';
				break;
			case '2019-06':
				$parcel_base_all = 10;
				$parcel_base_except = 10;
				$where = " and hsi.staff_info_id not in (31671,31592,31593,31594,31595,31596,31597,31598,31599,31600,31601,31602,31603,31604,31605,31606,31607,31608,31609,31591,31610,31611,31612,31613,31614,31615,31616,31617,31618,31619,31620,31621,31623,31624,31625,31626,31627,31628,31629,31630,31631,31632,31633,31634,31635,31636,31637,31640,31641,31642,31643,31644,31645,31646,31647,31648,31649,31650,31651,31652,31653,31654,31655,31656,31657,31658,31659,31660,31661,31662,31663,31677,31678,31679,31680,31681,31682,31683,31684,31685,31686,31687,31688,31689,31690,31691,31692,31693,31694,31695) ";
				break;
			case '2019-07':
				$parcel_base_all = 10;
				$parcel_base_except = 10;
				$where = '';
				break;
			default:
				$parcel_base_all = 10;
				$parcel_base_except = 10;
				$where = '';
		}
		
		$_data = [
			'parcel_base_all' => $parcel_base_all,
			'parcel_base_except' => $parcel_base_except,
			'where' => $where,
		];
		
		return $_data;
	}

    /**
     * 去除快递公司之外公司的员工
     * @param $gongzi_month
     * @return array
     */
	public function get_staff_id_exclude_express($gongzi_month): array
    {
		$a1 = $this->get_logistic_staff();
		$a2 = $this->get_fulfillment_staff($gongzi_month);
		$a3 = $this->get_company_staff($gongzi_month,"30001,50001,60001,1222,80014");
        $excludeStaffConfig =  (new SettingEnvService())->listByParam(['code' => ['special_ffm_to_flashexpress','special_money_to_flashexpress','special_pay_to_flashexpress','special_fcommerce_to_flashexpress','special_flash_home_to_flashexpress','special_bulk_bulk_to_flashexpress']]);
        $excludeStaffConfig = array_column($excludeStaffConfig,'set_val');
        $excludeStaffIds  = [];
        foreach ($excludeStaffConfig as $item) {
            $excludeStaffIds = array_merge(explode(',',$item),$excludeStaffIds);
        }
		return array_diff(array_merge($a1,$a2,$a3),$excludeStaffIds);
	}
	
	/**
	 * 通过公司获取员工
	 * @param $department
	 * @param $gongzi_month
	 * @return array|int[]
	 */
	public function get_staff_id_by_department_v2($department,$gongzi_month): array
    {
		switch ($department) {
			case 'money':
				return $this->get_company_staff_v2(30001);
			case 'pay':
				return $this->get_company_staff_v2(60001);
			case 'commerce':
				return $this->get_company_staff_v2(50001);
            case 'flash_home':
				return $this->get_company_staff_v2(1222);
            case 'bulk_bulk':
				return $this->get_company_staff_v2(80014);
			case 'logistic':
				return $this->get_logistic_staff();
			case  'fulfillment':
				return array_diff($this->get_fulfillment_staff($gongzi_month), explode(',',(new SettingEnvService())->getSetVal('special_ffm_to_flashexpress')));
			default:
				return [];
		}
	}
	
	
	//为了获取staff_info_id 和 company的对应关系
	private function get_company_staff_v2($company_id){
		//type=2部门,type=1公司
		$sys_department = \App\Models\backyard\SysDepartmentModel::class;
		$sql = "SELECT id FROM {$sys_department} where company_id in ({company_id:array}) and type=2 and deleted=0";
		$company_id_data = is_array($company_id)?$company_id : explode(",", $company_id);
		$data = $this->modelsManager->executeQuery($sql,['company_id'=>array_values($company_id_data)])->toArray();
		$id_all = array_column($data,'id');
		if(empty($id_all)){
			return [];
		}
		
		$id_all_str = '';
		foreach ($id_all as $id){
			$id_all_str .= $id.",";
		}
		$id_all_str = trim($id_all_str,',');
		
		$staff_id_arr = $this->get_staff_by_department($id_all_str);
		
		//工资始终在快递部门发的员工
		$gongzi_in_express_staffs = (new  SettingEnvModel())::getSettingEnv('gongzi_in_express_staffs');
		$gongzi_in_express_staff_arr = explode(',',$gongzi_in_express_staffs);
		$excludeStaff = [];
        $settingEnv = (new SettingEnvService());
        if (30001 == $company_id) {
            $excludeStaff =  explode(',',$settingEnv->getSetVal('special_money_to_flashexpress'));
        }
        if (60001 == $company_id) {
            $excludeStaff =  explode(',',$settingEnv->getSetVal('special_pay_to_flashexpress'));
        }
        if (50001 == $company_id) {
            $excludeStaff =  explode(',',$settingEnv->getSetVal('special_fcommerce_to_flashexpress'));
        }
        if (1222 == $company_id) {
            $excludeStaff =  explode(',',$settingEnv->getSetVal('special_flash_home_to_flashexpress'));
        }
        if (80014 == $company_id) {
            $excludeStaff =  explode(',',$settingEnv->getSetVal('special_bulk_bulk_to_flashexpress'));
        }
        $excludeStaff = array_merge($excludeStaff,$gongzi_in_express_staff_arr);
		//60641在money工作,但是发工资再express,需要去除
		foreach ($staff_id_arr as $key=>$staff_id){
			if(in_array($staff_id,$excludeStaff)){
				unset($staff_id_arr[$key]);
			}
		}
		return $staff_id_arr;
	}
	
	
	/*
     * $str_staff_info_ids 可以是字符串,也可以是数组
     */
	private function get_staff_by_department($department_ids='',$gongzi_month=''){
		try {
			$hr_staff_info =  \App\Models\backyard\HrStaffInfoModel::class;
			$sql = "select hsi.staff_info_id from {$hr_staff_info} hsi
where  hsi.is_sub_staff=0 and hsi.formal=1 and hsi.sys_department_id in ({sys_department_id:array})  ";
			$where['sys_department_id']= is_array($department_ids) ? $department_ids : explode(',',$department_ids);
			
			if(!empty($gongzi_month)){
				$min_month = date("Y-m-d",strtotime($gongzi_month.'-01'.' -1 year'));
				$sql .= " and ( hsi.state !=2
              or (hsi.state=2 and hsi.leave_date > :min_month: ))";
				$where['min_month']= $min_month;
			}
			
			$data = $this->modelsManager->executeQuery($sql,$where)->toArray();

			
			return array_column($data, 'staff_info_id');
		}catch (\Exception $e) {
			$this->logger->error('get_hr_info exception' . $sql.$e->getMessage()." where=>".json_encode($where));
			return [];
		}
	}
}