<?php

namespace App\Services;

use App\Library\BaseService;
use App\Repository\HrStaffInfoRepository;

class ContractCompanyService extends BaseService{
    /**
     * 更新员工 合同公司
     * @param $staffIdsStr
     * @param $operatorStaffId
     * @return bool
     */
    public function updateStaffContractCompanyId($staffIdsStr, $operatorStaffId): bool
    {
        if (empty($staffIdsStr)) {
            return false;
        }
        $staffIds = explode(',', $staffIdsStr);

        $staffList = HrStaffInfoRepository::getHrStaffByIds($staffIds, ['staff_info_id', 'name']);
        if (empty($staffList)) {
            return false;
        }

        foreach ($staffList as $oneStaff) {
            $asyncParams = [
                'staff_info_id'   => $oneStaff['staff_info_id'],
                'name'            => $oneStaff['name'],
                'is_edit_company' => true,
                'operator'        => $operatorStaffId,
            ];
            $this->logger->info($asyncParams);
            (new StaffService())->asyncUpdateStaffInfoToHRIS($oneStaff['staff_info_id'], $asyncParams);
        }
        return true;
    }
}