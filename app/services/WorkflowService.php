<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums\ApprovalEnums;
use App\Library\Exception\BusinessException;
use App\Models\backyard\AuditApplyModel;
use App\Models\backyard\AuditApprovalModel;

class WorkflowService extends BaseService
{
    /**
     * @var WorkflowService
     */
    private static $instance;

    /**
     * @return WorkflowService
     */
    public static function getInstance(): WorkflowService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取下拉列表
     * @throws BusinessException
     */
    public function getValidWorkflowList(): array
    {
        $ac = new ApiClient('by', '', 'getValidWorkflowList');
        $ac->setParams([[]]);
        $return = $ac->execute();

        if (!$return){
            throw new BusinessException('Invalid return');
        }

        return $return['data'] ?? [];
    }

    public function getVisualizationWorkList(): array {
        $ac = new ApiClient('by', '', 'visualization',self::$language);
        $ac->setParams([[]]);
        $return = $ac->execute();

        if (!$return){
            throw new BusinessException('Invalid return');
        }

        return $return['data'] ?? [];
    }

    /**
     * @param $staff_id  员工 staff_info_id
     * @param $relate_type 审批流的 relate_type
     * @return array
     * @throws BusinessException
     */
    public function getPreviewList ($staff_id,$relate_type) : array {
        $ac = new ApiClient('by', '', 'getPreview',self::$language);
        $ac->setParams([[
            'staff_id' => $staff_id,
            'relate_type' => $relate_type
        ]]);
        $return = $ac->execute();
        if (!$return){
            throw new BusinessException('Invalid return');
        }
        return  $return;
    }

    /**
     * 获取下拉列表
     * @throws BusinessException
     */
    public function search($params, $user): array
    {
        $id = $params['id'] ?? 0;

        $ac = new ApiClient('by', '', 'getWorkflowForHcm');
        $ac->setParams(
            [[
                "flow_id" => $id
            ]]
        );
        $return = $ac->execute();
        if (!$return){
            throw new BusinessException('Invalid return');
        }

        return $return['data'] ?? [];
    }

    /**
     * 获取下拉列表
     * @throws BusinessException
     */
    public function edit($params, $user)
    {
        $flowId = $params['id'] ?? 0;
        $id = $params['code_id'] ?? 0;
        $staffIds = $params['staff_ids'] ?? '';

        $this->logger->info(sprintf("editWorkflowSpecifyStaffInfoId == user %s, edit workflow params: %s", $user, json_encode($params)));

        $ac = new ApiClient('by', '', 'editWorkflowSpecifyStaffInfoId');
        $ac->setParams(
            [[
                'flow_id'      => $flowId,
                'id'          => $id,
                'staff_ids'   => $staffIds,
                'operator_id' => $user,
            ]]
        );
        $return = $ac->execute();
        $this->logger->info(sprintf("editWorkflowSpecifyStaffInfoId == result: %s", json_encode($return)));

        if (!$return){
            throw new BusinessException('Invalid return');
        }

        return $return['data'] ?? "";
    }

    /**
     * 审批详情
     * @throws BusinessException
     */
    public function getAuditDetailV2($params, $user)
    {
        $flowId          = $params['id'] ?? 0;
        $id              = $params['id_union'] ?? 0;
        $staff_id        = $params['staff_id'] ?? '';
        $type            = $params['type'] ?? 0;
        $date_created    = $params['date_created'] ?? ''; //创建时间
        $audit_show_type = $params['audit_show_type'] ?? ApprovalEnums::AUDIT_SHOW_TYPE_APPLY; //默认：1我申请的，2我审批的

        $local = $params['locale'] ?? 'zh-CN';
        $ac    = new ApiClient('by', '', 'getDetailForHcm', $local);
        $ac->setParams(
            [[
                    'flow_id'      => $flowId,
                    'id'           => $id,
                    'staff_id'     => $staff_id,
                    'operator_id'  => $user,
                    'type'         => $type,
                    'isCommit'     => $audit_show_type,
                    'date_created' => $date_created,// 创建时间
                    'version'      => AuditApprovalModel::VERSION_4,//hcm 更新 版本4
            ],]
        );
        $return = $ac->execute();
        $this->logger->info(sprintf("get_audit_detail == result: %s", json_encode($return)));
        if (!$return){
            throw new BusinessException('Invalid return');
        }
        return $return['data'] ?? "";
    }

    /**
     * 查看审批流日志
     * @param $params
     * @param $id
     * @return void
     */
    public function getAuditLog($params, $staff_ino_id)
    {
        $ac    = new ApiClient('by', '', 'get_audit_log', static::$language);
        $ac->setParams(
            [[
                'audit_id'    => $params['audit_id'],
                'audit_type'  => $params['audit_type'],
                'staff_id'    => $staff_ino_id ?? 10000,
                'log_version' => 4,
            ],]
        );
        $return = $ac->execute();
        return $return ?? [];
    }
}