<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrBlacklistModel;
use App\Models\backyard\HrOutSourcingBlacklistModel;
use App\Models\backyard\HrStaffInfoModel;

class BlackListService extends BaseService
{
    /**
     * svc 接口 原因枚举转换
     * @param $code
     * @return string
     * @throws ValidationException
     */
    protected function getReasonCode($code): string
    {
        if (!in_array($code, [1, 2])) {
            throw new ValidationException('未定义的 reason_code');
        }

        if (isCountry('TH')) {
            $map = [
                1 => '10000',
                2 => '10012',
            ];
        } elseif (isCountry('PH')) {
            $map = [
                1 => '10000',
                2 => '10013',
            ];
        } elseif (isCountry('MY')) {
            $map = [
                1 => '10000',
                2 => '10014',
            ];
        }
        return $map[$code] ?? '';
    }

    /**
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    protected function svcValidation($params)
    {
        if (empty($params['staff_info_id'])) {
            throw new ValidationException(self::$t->_('need staff_info_id'));
        }
        if (empty($params['reason_code'])) {
            throw new ValidationException(self::$t->_('need reason_code'));
        }
        if (!empty($params['remark'])) {
            $params['remark'] = mb_substr($params['remark'], 0, 1000);
        }
        $params['reason_code'] = $this->getReasonCode($params['reason_code']);
        $params['type'] = $params['type'] ?? HrOutSourcingBlacklistModel::TYPE_FACE_DIFF;
        return $params;
    }

    /**
     * svc 接口 加入黑名单
     * @param $locale
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function addOsBlacklistSVC($locale, $params): array
    {
        $params                    = $this->svcValidation($params);
        $staffInfo                 = (new StaffService())->getStaffInfoSimplify($params);
        $params['name']            = $staffInfo['name'];
        $params['mobile']          = $staffInfo['mobile'];
        $params['identity']        = $staffInfo['identity'];
        $params['staffInfo']['id'] = $params['operator'] ?? 10000;
        $this->add($params);
        return [
            'code'    => ErrCode::SUCCESS,
            'msg'     => 'ok',
            'message' => 'ok',
            'data'    => [],
        ];
    }


    /**
     * 新增验证
     * @param $params
     * @return void
     * @throws ValidationException
     */
    protected function addValidation($params)
    {
        $os_black_reason = (new DictionaryService())->getTotalDictionaryLabelByDictCode('os_black_reason');
        $os_black_reason = array_column($os_black_reason, 'label', 'value');
        if (!isset($os_black_reason[$params['reason_code']])) {
            throw new ValidationException(self::$t->_('reason_code_err'));
        }

        $remark_tip = $this->checkRemarkTip($params['remark'] ?? '');
        if ($params['type'] == HrOutSourcingBlacklistModel::TYPE_DIRECT_INPUT && !empty($remark_tip)) {
            throw new ValidationException($remark_tip);
        }
        if (!empty($params['name'])) {
            $name_tip = $this->checkNameTip(trim($params['name']));
            if (!empty($name_tip)) {
                throw new ValidationException($name_tip);
            }
        }

        if (!empty($params['mobile'])) {
            $mobile_tip = $this->checkMobileTip(trim($params['mobile']));
            if (!empty($mobile_tip)) {
                throw new ValidationException($mobile_tip);
            }
        }

        $identity_tip = $this->checkIdentityTip($params['identity'] ?? '');
        if (!empty($identity_tip)) {
            throw new ValidationException($identity_tip);
        }
    }

    /**
     * 构建参数
     * @param $staff_info_id
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    protected function buildParams($staff_info_id, &$params): bool
    {
        if (!empty($staff_info_id)) {
            $staff_info = (new StaffService())->getStaffInfoSimplify([
                'staff_info_id' => $staff_info_id,
            ]);
            if (HrStaffInfoModel::FORMAL_0 != $staff_info['formal']) {
                throw new ValidationException(self::$t->_('staff_info_id_err_2'));
            }
            if (!empty($params['source']) && $params['source'] == 'outSourcingBlackListImport') {
                if (empty($params['mobile']) && !empty($staff_info['mobile'])) {
                    $params['mobile'] = $staff_info['mobile'];
                }
                if (empty($params['name']) && !empty($staff_info['name'])) {
                    $params['name'] = $staff_info['name'];
                }
            }
        }
        return true;
    }


    /**
     * 添加黑名单-单个
     * @param $params
     * @return bool
     * @throws ValidationException
     * @throws \Exception
     */
    public function add($params): bool
    {
        $this->addValidation($params);

        $staff_info_id = trim($params['staff_info_id'] ?? '');

        $this->buildParams($staff_info_id, $params);

        $outSourcingBlacklist                     = new HrOutSourcingBlacklistModel();
        $outSourcingBlacklist->identity           = trim($params['identity']);
        $outSourcingBlacklist->remark             = trim($params['remark']);
        $outSourcingBlacklist->reason_code        = trim($params['reason_code']);
        $outSourcingBlacklist->name               = trim($params['name'] ?? '');
        $outSourcingBlacklist->mobile             = trim($params['mobile'] ?? '');
        $outSourcingBlacklist->staff_info_id      = $staff_info_id;
        $outSourcingBlacklist->submitter_staff_id = $params['staffInfo']['id'];
        $outSourcingBlacklist->status             = HrOutSourcingBlacklistModel::STATUS_TAKE_EFFECT;
        $outSourcingBlacklist->type               = $params['type']?? HrOutSourcingBlacklistModel::TYPE_DIRECT_INPUT;
        $outSourcingBlacklist->save();

        $this->afterSave($staff_info_id, $params);
        return true;
    }

    protected function afterSave($staff_info_id, $params)
    {
        $blackface_config = (new SettingEnvService())->getSetVal('blackface_os_reason',',');
        if (in_array($params['reason_code'],$blackface_config)) {
            $add_face_blacklist_data = [
                'business_type'      => 'add_face_blacklist',
                'staff_info_id'      => $staff_info_id,
                'identity'           => trim($params['identity']),
                'mobile'             => trim($params['mobile'] ?? ''),
                'submitter_staff_id' => $params['staffInfo']['id'],
                'type'               => 'out_sourcing_blacklist',
                'remark'             => trim($params['remark']??''),
                'reason_code'        => trim($params['reason_code']),
            ];
            $rmq                     = new RocketMQ('face-blacklist');
            $rmq->sendOrderlyMsg($add_face_blacklist_data, 3);
        }
    }


    /**
     * 添加黑名单-批量
     * @param $params
     * @return bool
     * @throws ValidationException
     * @throws \Exception
     */
    public function addBatch($params): bool
    {
        if (empty($params['identity'])) {
            throw new ValidationException(self::$t->_('identity_empty'));
        }
        $os_black_reason = (new DictionaryService())->getTotalDictionaryLabelByDictCode('os_black_reason');
        $os_black_reason = array_column($os_black_reason, 'label', 'value');
        if (!isset($os_black_reason[$params['reason_code']])) {
            throw new ValidationException(self::$t->_('reason_code_err'));
        }

        $remark_tip = $this->checkRemarkTip($params['remark'] ?? '');
        if (!empty($remark_tip)) {
            throw new ValidationException($remark_tip);
        }
        $identity_clean = trim(preg_replace("/(\s+)/", " ", trim($params['identity'])));
        $identity_clean = explode(' ', $identity_clean);
        if (empty($identity_clean)) {
            throw new ValidationException(self::$t->_('identity_empty'));
        }
        if (count($identity_clean) > 1000) {
            throw new ValidationException(self::$t->_('identity_num_limitation', ['bit_limit' => '1000']));
        }
        $identity_err        = [];
        $return_identity_tip = '';
        foreach ($identity_clean as $v) {
            $identity_tip = $this->checkIdentityTip($v);
            if (!empty($identity_tip)) {
                $return_identity_tip = $identity_tip;
                $identity_err[]      = $v;
            }
        }
        if (!empty($return_identity_tip) && !empty($identity_err)) {
            throw new ValidationException($return_identity_tip . ':' . implode(',', $identity_err));
        }

        $blackface_config = (new SettingEnvService())->getSetVal('blackface_os_reason',',');

        $model = new HrOutSourcingBlacklistModel();
        $db    = $model::beginTransaction($this);
        $data  = [];
        foreach ($identity_clean as $item) {
            $data[] = [
                'identity'           => $item,
                'status'             => HrOutSourcingBlacklistModel::STATUS_TAKE_EFFECT,
                'submitter_staff_id' => $params['staffInfo']['id'],
                'remark'             => trim($params['remark']),
                'reason_code'        => $params['reason_code'],
                'type'               => HrOutSourcingBlacklistModel::TYPE_DIRECT_INPUT,
            ];
            if (in_array($params['reason_code'],$blackface_config)) {
                $add_face_blacklist_data = [
                    'business_type'      => 'add_face_blacklist',
                    'staff_info_id'      => '',
                    'identity'           => $item,
                    'mobile'             => '',
                    'submitter_staff_id' => $params['staffInfo']['id'],
                    'type'               => 'out_sourcing_blacklist',
                    'remark'             => trim($params['remark']),
                    'reason_code'        => $params['reason_code'],
                ];
                $rmq                    = new RocketMQ('face-blacklist');
                $rmq->sendOrderlyMsg($add_face_blacklist_data, 3);
            }
        }
        $model->batch_insert($data);
        $db->commit();
        $this->logger->info($params);
        return true;
    }

    public function remove($params)
    {
        if (empty($params['id']) || empty($params['remove_remark'])) {
            throw new ValidationException(self::$t->_('param_error'));
        }
        $outSourcingBlacklist = HrOutSourcingBlacklistModel::findFirst([
            'conditions' => 'id = ?1',
            'bind'       => [
                1 => $params['id'],
            ],
        ]);
        if (empty($outSourcingBlacklist)) {
            throw new ValidationException(self::$t->_('data_error'));
        }
        if ($outSourcingBlacklist->status != HrOutSourcingBlacklistModel::STATUS_TAKE_EFFECT) {
            throw new ValidationException(self::$t->_('status_changed'));
        }
        if (isCountry(['TH','PH','MY'])) {
            $remove_face_blacklist_data = [
                'business_type'      => 'remove_face_blacklist',
                'staff_info_id'      => $outSourcingBlacklist->staff_info_id ?? 0,
                'identity'           => $outSourcingBlacklist->identity ?? '',
                'mobile'             => $outSourcingBlacklist->mobile ?? '',
                'submitter_staff_id' => $params['staffInfo']['id'],
                'type'               => 'out_sourcing_blacklist',
                'remove_remark'      => trim($params['remove_remark']),
            ];
            $rmq                        = new RocketMQ('face-blacklist');
            $rmq->sendOrderlyMsg($remove_face_blacklist_data, 3);
        }

        $outSourcingBlacklist->remove_staff_id = $params['staffInfo']['id'];
        $outSourcingBlacklist->status          = HrOutSourcingBlacklistModel::STATUS_INVALID;
        $outSourcingBlacklist->remove_date     = gmdate('Y-m-d H:i:s');
        $outSourcingBlacklist->remove_remark   = trim($params['remove_remark']);
        $outSourcingBlacklist->save();
        return true;
    }

    /**
     * 移除黑名单
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function removeBatch($params): bool
    {
        if (empty($params['identity']) || empty($params['remove_remark'])) {
            throw new ValidationException(self::$t->_('param_error'));
        }
        $identity       = $params['identity'];
        $identity_clean = trim(preg_replace("/(\s+)/", " ", $identity));
        $identity_clean = explode(' ', $identity_clean);

        if (empty($identity_clean)) {
            throw new ValidationException(self::$t->_('identity_empty'));
        }
        if (count($identity_clean) > 1000) {
            throw new ValidationException(self::$t->_('identity_num_limitation', ['bit_limit' => '1000']));
        }
        $identity_err        = [];
        $return_identity_tip = '';
        foreach ($identity_clean as $v) {
            $identity_tip = $this->checkIdentityTip($v, true);
            if (!empty($identity_tip)) {
                $return_identity_tip = $identity_tip;
                $identity_err[]      = $v;
            }
        }
        if (!empty($return_identity_tip) && !empty($identity_err)) {
            throw new ValidationException($return_identity_tip . ':' . implode(',', $identity_err));
        }

        $model = new HrOutSourcingBlacklistModel();
        $db    = $model::beginTransaction($this);
        $info  = $model->find([
            'conditions' => 'identity in ({identity:array})  and status = 1',
            'bind'       => ['identity' => array_values($identity_clean)],
        ]);

        if ($info->count() == 0) {
            $db->rollback();
            throw new ValidationException(self::$t->_('not_find_data'));
        }

        foreach ($info as $item) {
            $item->status          = HrOutSourcingBlacklistModel::STATUS_INVALID;
            $item->remove_remark   = trim($params['remove_remark']);
            $item->remove_staff_id = $params['staffInfo']['id'];
            $item->remove_date     = gmdate('Y-m-d H:i:s');
            $item->save();
            if (isCountry(['TH','PH','MY'])) {
                $remove_face_blacklist_data = [
                    'business_type'      => 'remove_face_blacklist',
                    'staff_info_id'      => $item->staff_info_id ?? 0,
                    'identity'           => $item->identity ?? '',
                    'mobile'             => $item->mobile ?? '',
                    'type'               => 'out_sourcing_blacklist',
                    'submitter_staff_id' => $params['staffInfo']['id'],
                    'remove_remark'      => trim($params['remove_remark']),
                ];
                $rmq                        = new RocketMQ('face-blacklist');
                $rmq->sendOrderlyMsg($remove_face_blacklist_data, 3);
            }
        }
        $db->commit();
        return true;
    }

    /**
     * winhr-黑名单数据查询
     * @return void
     */
    public function getHrBlacklistByParams(array $params, array $field = [])
    {
        if (empty($params)) {
            return [];
        }
        $colum   = empty($field) ? "*" : implode(', ', $field);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($colum);
        $builder->from(HrBlacklistModel::class);
        if (!empty($params['identity']) && is_array($params['identity'])) {
            // 身份号
            $builder->inWhere('identity', $params['identity']);
        }

        if (!empty($params['mobile'])) {
            // 手机号
            $builder->andWhere('mobile = :mobile:', ['mobile' => trim($params['mobile'])]);
        }

        // 状态
        if (!empty($params['status'])) {
            $builder->andWhere('status = :status:', ['status' => trim($params['status'])]);
        }

        if (!empty($params['type']) && is_array($params['type'])) {
            $builder->inWhere('type', $params['type']);
        }

        $result = $builder->getQuery()->execute()->getFirst();
        if (!empty($result)) {
            return $result->toArray();
        }
        return [];
    }

    /**
     * 外协黑名单的验证
     * @return void
     */
    public function getHrOutSourcingBlacklistByParams(array $params, array $field = [])
    {
        if (empty($params)) {
            return [];
        }

        $colum   = empty($field) ? "*" : implode(', ', $field);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($colum);
        $builder->from(HrOutSourcingBlacklistModel::class);
        if (!empty($params['identity']) && is_array($params['identity'])) {
            // 身份号
            $builder->inWhere('identity', $params['identity']);
        }

        if (!empty($params['mobile'])) {
            // 手机号
            $builder->andWhere('mobile = :mobile:', ['mobile' => trim($params['mobile'])]);
        }

        // 状态
        if (!empty($params['status'])) {
            $builder->andWhere('status = :status:', ['status' => trim($params['status'])]);
        }

        if (!empty($params['type']) && is_array($params['type'])) {
            $builder->inWhere('type', $params['type']);
        }

        $result = $builder->getQuery()->execute()->getFirst();
        if (!empty($result)) {
            return $result->toArray();
        }
        return [];
    }

    /**
     * 列表
     * @param $params
     * @return array
     */
    public function list($params): array
    {
        $page     = $params['page_num'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        $offset   = $pageSize * ($page - 1);

        $builder       = $this->getListQueryBuilder($params);
        $builder_count = $this->getListQueryBuilder($params);
        $builder->limit($pageSize, $offset);
        $list               = $builder->getQuery()->execute()->toArray();
        $new_list           = !empty($list) ? $this->combinationList($list) : [];
        $result['list']     = $new_list;
        $totalCount         = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();
        $result['paginate'] = [
            'total_count' => !empty($totalCount) ? (int)$totalCount->count : 0,
            'page_num'    => $page,
            'page_size'   => $pageSize,
        ];
        return $result;
    }

    /**
     * @param array $params
     * @return mixed
     */
    public function getListQueryBuilder(array $params)
    {
        $builder = $this->modelsManager->createBuilder();
//        $builder->columns('
//        b.id
//        ');
        $builder->orderBy('b.id desc');
        $builder->from(['b' => HrOutSourcingBlacklistModel::class]);
        // 证件号
        if (!empty($params['identity'])) {
            $builder->andWhere("b.identity = :identity:",
                ['identity' => trim($params['identity'])]);
        }
        // 手机号
        if (!empty($params['mobile'])) {
            $builder->andWhere("b.mobile = :mobile:",
                ['mobile' => trim($params['mobile'])]);
        }
        // 姓名
        if (!empty($params['name'])) {
            $builder->andWhere('b.name LIKE :name:', ['name' => "%{$params['name']}%"]);
        }
        // 工号
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere("b.staff_info_id = :staff_info_id:",
                ['staff_info_id' => trim($params['staff_info_id'])]);
        }
        // 信息来源
        if (!empty($params['type']) && is_array($params['type'])) {
            $builder->andWhere("b.type in ({type:array})",
                ['type' => $params['type']]);
        }
        // 原因
        if (!empty($params['reason_code']) && is_array($params['reason_code'])) {
            $builder->andWhere("b.reason_code in ({reason_code:array})",
                ['reason_code' => $params['reason_code']]);
        }
        // 状态
        if (!empty($params['status'])) {
            $builder->andWhere("b.status = :status:",
                ['status' => trim($params['status'])]);
        }
        // 新增时间 - 开始
        if (!empty($params['created_at_start'])) {
            $builder->andWhere('b.created_at >= :created_at_start:',
                ['created_at_start' => zero_time_zone($params['created_at_start'] . ' 00:00:00')]);
        }
        // 新增时间 - 结束
        if (!empty($params['created_at_end'])) {
            $builder->andWhere('b.created_at <= :created_at_end:',
                ['created_at_end' => zero_time_zone($params['created_at_end'] . ' 23:59:59')]);
        }
        // 黑名单id
        if (!empty($params['id'])) {
            $builder->andWhere("b.id = :id:",
                ['id' => trim($params['id'])]);
        }

        return $builder;
    }

    /**
     * 格式化业务列表
     * @param $data
     * @return array
     */
    public function combinationList($data): array
    {
        $add_hour = $this->config->application->add_hour;
        // 原因
        $os_black_reason = (new DictionaryService())->getTotalDictionaryLabelByDictCode('os_black_reason');
        $os_black_reason = array_column($os_black_reason, 'label', 'value');

        $staffIds   = array_values(array_unique(array_merge(array_filter(array_column($data, 'submitter_staff_id')),
            array_filter(array_column($data, 'remove_staff_id')))));
        $staff_data = [];
        if (!empty($staffIds)) {
            $staff_data = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,name',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $staffIds],
            ])->toArray();
            $staff_data = array_column($staff_data, 'name', 'staff_info_id');
        }
        foreach ($data as $k => $v) {
            // 创建时间
            $data[$k]['created_at'] = !empty($v['created_at']) ? date('Y-m-d H:i:s',
                (strtotime($v['created_at']) + $add_hour * 3600)) : '';
            // 更新时间
            $data[$k]['updated_at'] = !empty($v['updated_at']) ? date('Y-m-d H:i:s',
                (strtotime($v['updated_at']) + $add_hour * 3600)) : '';
            // 移除时间
            $data[$k]['remove_date'] = !empty($v['remove_date']) ? date('Y-m-d H:i:s',
                (strtotime($v['remove_date']) + $add_hour * 3600)) : '';
            // 工号
            $data[$k]['staff_info_id'] = empty($v['staff_info_id']) ? '' : $v['staff_info_id'];
            // 信息来源
            $data[$k]['type_text'] = !empty($v['type']) ? static::$t->_('out_sourcing_blacklist_type_' . $v['type']) : '';
            // 原因
            $data[$k]['reason_code_text'] = !empty($v['reason_code']) ? $os_black_reason[$v['reason_code']] : '';
            // 新增操作人
            $data[$k]['submitter_staff_id_name'] = !empty($v['submitter_staff_id']) ? '(' . $v['submitter_staff_id'] . ')' . $staff_data[$v['submitter_staff_id']] : '';
            $data[$k]['submitter_staff_name']    = !empty($v['submitter_staff_id']) ? $staff_data[$v['submitter_staff_id']] : '';
            // 状态
            $data[$k]['status_text'] = !empty($v['status']) ? static::$t->_('out_sourcing_blacklist_status_' . $v['status']) : '';
            // 移除操作人
            $data[$k]['remove_staff_id_name'] = !empty($v['remove_staff_id']) ? '(' . $v['remove_staff_id'] . ')' . $staff_data[$v['remove_staff_id']] : '';
            $data[$k]['remove_staff_name']    = !empty($v['remove_staff_id']) ? $staff_data[$v['remove_staff_id']] : '';
        }
        return $data;
    }

    /**
     * @return array[]
     */
    public function typeList()
    {
        return [
            [
                'value' => HrOutSourcingBlacklistModel::TYPE_DIRECT_INPUT,
                'label' => static::$t->_('out_sourcing_blacklist_type_1'),
            ],
            [
                'value' => HrOutSourcingBlacklistModel::TYPE_FACE_DIFF,
                'label' => static::$t->_('out_sourcing_blacklist_type_2'),
            ],
            [
                'value' => HrOutSourcingBlacklistModel::TYPE_NOT_REFUND_TASK,
                'label' => static::$t->_('out_sourcing_blacklist_type_3'),
            ],
        ];
    }

    /**
     * @return array[]
     */
    public function statusList()
    {
        return [
            [
                'value' => HrOutSourcingBlacklistModel::STATUS_TAKE_EFFECT,
                'label' => static::$t->_('out_sourcing_blacklist_status_1'),
            ],
            [
                'value' => HrOutSourcingBlacklistModel::STATUS_INVALID,
                'label' => static::$t->_('out_sourcing_blacklist_status_2'),
            ],
        ];
    }

    /**
     * 检测是否存在外协黑名单给提示
     * @param $params
     * @return array
     */
    public function checkExists($params): array
    {
        $return_data = [
            'mobile_tip'   => null,
            'identity_tip' => null,
        ];
        $identity    = $params['identity'] ?? '';
        $mobile      = $params['mobile'] ?? '';
        if (empty($identity) && empty($mobile)) {
            return $return_data;
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrOutSourcingBlacklistModel::class);
        $builder->where('status = ' . HrOutSourcingBlacklistModel::STATUS_TAKE_EFFECT);
        if (!empty($identity)) {
            $builder->andWhere('identity = :identity:', ['identity' => trim($identity)]);
        }
        if (!empty($mobile)) {
            $builder->andWhere('mobile = :mobile:', ['mobile' => trim($mobile)]);
        }
        $data = $builder->getQuery()->execute()->toArray();
        if (empty($data)) {
            return $return_data;
        }
        $identity_data               = array_column($data, 'id', 'identity');
        $mobile_data                 = array_column($data, 'id', 'mobile');
        $return_data['mobile_tip']   = $mobile && !empty($mobile_data[$mobile]) ? static::$t->_('mobile_tip_1') : null;
        $return_data['identity_tip'] = $identity && !empty($identity_data[$identity]) ? static::$t->_('identity_tip_1') : null;
        return $return_data;
    }

    /**
     * @param $identity
     * @param bool $all_check
     * @return string
     */
    public function checkIdentityTip($identity, bool $all_check = false): string
    {
        if (!$all_check) {
            if (isCountry(['TH', 'VN', 'MY',])) {
                if (!preg_match("/^[A-Za-z0-9]{8,13}$/", $identity)) {
                    return self::$t->_('identity_err_bit_limit', ['bit_limit' => '8~13']);
                }
            } elseif (isCountry(['PH'])) {
                if (!preg_match("/^[A-Za-z0-9]{8,30}$/", $identity)) {
                    return self::$t->_('identity_err_bit_limit', ['bit_limit' => '8~30']);
                }
            } elseif (isCountry('LA')) {
                if (!preg_match("/^[A-Za-z0-9]{8,15}$/", $identity)) {
                    return self::$t->_('identity_err_bit_limit', ['bit_limit' => '8~15']);
                }
            } elseif (isCountry('ID')) {
                if (!preg_match("/^[A-Za-z0-9]{8,16}$/", $identity)) {
                    return self::$t->_('identity_err_bit_limit', ['bit_limit' => '8~16']);
                }
            }
        } else {
            if (!preg_match("/^[A-Za-z0-9]{1,32}$/", $identity)) {
                return self::$t->_('identity_err_bit_limit', ['bit_limit' => '1~32']);
            }
        }
        return '';
    }

    /**
     * @param $mobile
     * @return string
     */
    public function checkMobileTip($mobile): string
    {
        if (isCountry(['TH', 'VN'])) {
            if (!preg_match("/^[0-9]{10}$/", $mobile)) {
                return self::$t->_('mobile_err_bit_limit', ['bit_limit' => '10']);
            }
        } elseif (isCountry(['MY', 'LA'])) {
            if (!preg_match("/^[0-9]{10,11}$/", $mobile)) {
                return self::$t->_('mobile_err_bit_limit', ['bit_limit' => '10~11']);
            }
        } elseif (isCountry('PH')) {
            if (!preg_match("/^[0-9]{11}$/", $mobile)) {
                return self::$t->_('mobile_err_bit_limit', ['bit_limit' => '11']);
            }
        } elseif (isCountry('ID')) {
            if (!preg_match("/^[0-9]{8,13}$/", $mobile)) {
                return self::$t->_('mobile_err_bit_limit', ['bit_limit' => '8~13']);
            }
        }
        return '';
    }

    /**
     * @param $remark
     * @return string
     */
    public function checkRemarkTip($remark): string
    {
        if (empty($remark) || mb_strlen($remark) > 1000) {
            return self::$t->_('remark_err_bit_limit', ['bit_limit' => '1000']);
        }
        return '';
    }

    /**
     * @param $name
     * @return string
     */
    public function checkNameTip($name): string
    {
        if (empty($name) || mb_strlen($name) > 100) {
            return self::$t->_('name_err_bit_limit', ['bit_limit' => '100']);
        }
        return '';
    }

    /**
     * @description 上传校验
     * @param $importPath
     * @return void
     * @throws BusinessException
     */
    public function uploadCheck($importPath)
    {
        $tmpDir      = sys_get_temp_dir();                // 获取系统的临时目录路径
        $fileName    = basename($importPath);             // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;         // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($importPath))) {
            throw new BusinessException('System error');
        }
        $config       = ['path' => dirname($tmpFilePath)];
        $fileRealName = basename($tmpFilePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet();
        $excelData = $excel->getSheetData();
        if (!isset($excelData[1])) {
            throw new BusinessException('Please set the date header of the second row');
        }
        if ($this->checkIsImportDataBeyondMaxLineCount($excelData, 5000, 1)) {
            throw new BusinessException(static::$t->_('schedule_suggest_error_number'));
        }
        @unlink($tmpFilePath);
        if (
            !isset($excelData[0]) ||
            !isset($excelData[0][0]) ||
            $excelData[0][0] != 'ID No.*' ||
            !isset($excelData[0][1]) ||
            $excelData[0][1] != 'Phone no.' ||
            !isset($excelData[0][2]) ||
            $excelData[0][2] != 'Name' ||
            !isset($excelData[0][3]) ||
            $excelData[0][3] != 'Staff ID' ||
            !isset($excelData[0][4]) ||
            $excelData[0][4] != 'Reason*' ||
            !isset($excelData[0][5]) ||
            $excelData[0][5] != 'Add remark*'
        ) {
            throw new BusinessException(static::$t->_('import_batch_header_err'));
        }
    }

    /**
     * @description 上传
     * @param $filePath
     * @param $operatorId
     * @param $fileName
     * @return array
     */
    public function dealImportData($filePath, $operatorId, $fileName): array
    {
        //获取Excel数据
        $excelData = $this->getExcelData($filePath,
            [
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,
                1 => \Vtiful\Kernel\Excel::TYPE_STRING,
                2 => \Vtiful\Kernel\Excel::TYPE_STRING,
                3 => \Vtiful\Kernel\Excel::TYPE_STRING,
                4 => \Vtiful\Kernel\Excel::TYPE_STRING,
                5 => \Vtiful\Kernel\Excel::TYPE_STRING,
            ]);
        //去重 保留行号大的数据
        $data = $fileData = [];
        foreach (static::yieldData()($excelData) as $key => $datum) {
            //表头
            if ($key == 0) {
                $header = $datum;
                continue;
            }
            //去除多余空行
            if (empty($datum[0]) && empty($datum[1])) {
                continue;
            }
            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);
        if (!empty($data)) {
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError] = $this->dealItem($operatorId, $item);
                $fileData[] = $item;
                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }
        $header[]   = 'result';
        $excel_file = $this->exportExcel($header, $fileData, $fileName);
        $flashOss   = new FlashOss();
        $oss_path   = 'out_sourcing_blacklist' . '/' . $fileName;
        $flashOss->uploadFile($oss_path, $excel_file['data']);
        return ['url' => $oss_path, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }

    public function dealItem($operatorId, $item)
    {
        try {
            $params                    = [
                'staff_info_id' => $item[3],
                'identity'      => $item[0],
                'mobile'        => $item[1],
                'remark'        => $item[5],
                'reason_code'   => !empty($item[4]) ? explode('|', $item[4])[0] : '',
                'name'          => $item[2],
            ];
            $params['staffInfo']['id'] = $operatorId;
            $params['source'] = 'outSourcingBlackListImport';
            $params['type'] = HrOutSourcingBlacklistModel::TYPE_DIRECT_INPUT;
            $this->add($params);
            $item[6]   = 'success';
            $haveError = false;
        } catch (ValidationException $e) {
            $haveError = true;
            $item[6]   = 'fail ' . $e->getMessage();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log("outSourcingBlackListImportAction dealItem error paramIn: " . json_encode($item,
                    JSON_UNESCAPED_UNICODE) . ' error_message:' . $e->getMessage(), 'error');
            $haveError = true;
            $item[6]   = 'fail ' . self::$t->_('retry_later');
        }
        return [$item, $haveError];
    }

    /**
     * 详情
     * @param $id
     * @return array|mixed
     * @throws ValidationException
     */
    public function info($id)
    {
        if (empty($id)) {
            throw new ValidationException(self::$t->_('param_error'));
        }
        $info = HrOutSourcingBlacklistModel::findFirst([
            'conditions' => 'id = ?1',
            'bind'       => [
                1 => $id,
            ],
        ]);
        if (empty($info)) {
            throw new ValidationException(self::$t->_('data_error'));
        }
        $info = $info->toArray();
        $info = $this->combinationList([$info]);
        return $info[0] ?? [];
    }


}