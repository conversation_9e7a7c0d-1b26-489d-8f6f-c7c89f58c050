<?php
/**
 * Author: Bruce
 * Date  : 2023-06-08 11:43
 * Description:
 */

namespace App\Services;


use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums;
use App\Library\Enums\ApprovalEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MessWarningEnums;
use App\Library\Enums\StaffEnums;
use app\library\Enums\SuspensionEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\FlashMailer;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\AuditApprovalModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffNotRefundedSuspensionTaskModel;
use App\Models\backyard\ReinstatementRequestModel;
use app\models\backyard\SuspendWorkModel;
use App\Models\backyard\SuspensionAuditModel;
use App\Models\backyard\SuspensionFileModel;
use App\Models\backyard\SuspensionManageLogModel;
use App\Models\backyard\SuspensionSignConfigModel;
use App\Models\backyard\TripleToLeaveModel;
use app\modules\My\library\Enums\BocEnums;
use App\Repository\AuditApprovalRepository;
use App\Modules\My\library\Enums\CompanyEnums;
use App\Modules\My\library\Enums\SalaryEnums;
use App\Repository\HrStaffInfoRepository;
use App\Repository\HrStaffNotRefundedSuspensionTaskRepository;
use App\Repository\StaffInfoRepository;
use App\Repository\SuspensionFileRepository;
use App\Repository\SuspensionRepository;
use DateTime;
use Exception;

class SuspensionService extends BaseService
{
    const LIMIT_DOWNLOAD_NUM = 300000;
    const IMPORT_NUM = 999;
    const HOLD_LIMIT = 14;//// 1号-14号：hold限制月可选择上月及以后的月份

    public static $hireTypes = [
        HrStaffInfoModel::HIRE_TYPE_1,
        HrStaffInfoModel::HIRE_TYPE_2,
        HrStaffInfoModel::HIRE_TYPE_3,
        HrStaffInfoModel::HIRE_TYPE_4,
        HrStaffInfoModel::HIRE_TYPE_5,
    ];

    const HOLD_TYPE_WAGES = 1;//工资
    const HOLD_TYPE_PERCENTAGE = 2;//提成
    const HOLD_TYPE_WAGES_PERCENTAGE = 3;//工资+提成


    public $regionPiece;//大区片区名
    public $jobInfo;//职位名
    public $departmentInfo;//部门名

    protected $columns = [
        'hsi.staff_info_id',
        'hsi.name',
        'hsi.job_title',
        'hsi.node_department_id',
        'hsi.sys_store_id',
        'hsi.stop_duties_date',
        'hsi.stop_duty_reason',
        'hsi.state',
        'hsi.hire_type',
        'hsi.hire_date',
        'hsi.stop_duties_count',
        'hsi.wait_leave_state',
        'hsi.personal_email',
        'sml.id as suspension_log_id',
        'sml.created_at as sus_created_at',
    ];

    /**
     * 获取枚举数据
     * @return mixed
     */
    public function getSysInfo()
    {
        //停职原因
        $suspension_reason = [];
        foreach (Enums\StaffEnums::SUSPEND_TYPE_REASON_MAP as $key => $oneData) {
            $suspension_reason[] = ['value' => $key, 'label' => self::$t->_($oneData)];
        }

        //在职状态
        $staff_state = [];
        foreach (Enums::$hris_working_state as $key => $value) {
            $staff_state[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        $sign_config_list_data = [];
        $sign_config_list      = $this->signConfigList(['is_download' => 1]);
        foreach ($sign_config_list['list'] as $one) {
            $sign_config_list_data[] = [
                'value' => $one['staff_info_id'],
                'label' => $one['staff_info_id'] . ' ' . $one['name'],
            ];
        }

        $result['suspension_reason']      = $suspension_reason;
        $result['staff_state']            = $staff_state;
        $result['suspension_sign_config'] = $sign_config_list_data;

        return $result;
    }

    /**
     * 枚举
     * @return mixed
     */
    public function auditSysInfo()
    {
        //在职状态
        $staff_state = [];
        foreach (Enums::$hris_working_state as $key => $value) {
            $staff_state[] = ['value' => strval($key), 'label' => self::$t->_($value)];
        }

        //审批状态
        $audit_status_list = [];
        foreach (Enums::$audit_status as $key => $value) {
            if($key == Enums::audit_status_4) {
                continue;
            }
            $audit_status_list[] = ['value' => strval($key), 'label' => self::$t->_($value)];
        }
        $audit_status_list[] = ['value' => strval(SuspensionAuditModel::NO_NEED_AUDIT), 'label' => self::$t->_('no_need_audit')];

        //处理状态
        $fix_status_list = [];
        foreach (SuspensionAuditModel::$fix_status_text as $key => $value) {
            $fix_status_list[] = ['value' => strval($key), 'label' => self::$t->_($value)];
        }
        //hold 相关
        $holdInfo = (new SysService())->getHoldEnumsInfo();

        $data['state_list'] = $staff_state;
        $data['audit_status_list'] = $audit_status_list;
        $data['fix_status_list'] = $fix_status_list;
        $data['hold_type_list'] = $holdInfo['hold_type_list'];
        $data['hold_reason_list'] = $holdInfo['hold_reason_list'];

        return $data;
    }

    /**
     * 获取列表数据
     * @param $params
     * @return mixed
     */
    public function getList($params)
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];

        $total = $this->getListQuery($params, true);
        $list  = $this->getListQuery($params);
        if ($list) {
            $list = $this->formatList($list);
        }

        $data['total'] = !empty($total) ? intval($total['count']) : 0;
        $data['list']  = $list;

        return $data;
    }

    /**
     * 格式化数据
     * @param $data
     * @return array
     */
    public function formatList($data)
    {
        $storeListToId    = $jobTitleInfoToId = $list = [];
        $jobTitleId       = array_values(array_unique(array_column($data, 'job_title')));
        $sysStoreId       = array_values(array_unique(array_column($data, 'sys_store_id')));
        $department_ids   = array_values(array_unique(array_column($data, 'node_department_id')));
        $suspensionLogIds = array_values(array_unique(array_column($data, 'suspension_log_id')));

        $fileList     = SuspensionFileRepository::getSuspensionFile($suspensionLogIds);
        $fileListToId = array_column($fileList, 'suspension_manage_log_id');

        $storeList = (new SysStoreService())->getStoreListByIds($sysStoreId);

        $storeList[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $jobTitleInfo = (new DepartmentService())->getJobList('', $jobTitleId, true);

        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $department_list = (new DepartmentService())->getAllDepartment($department_ids);
        $reinstatement_request_list = $this->getReinstatementRequestList($suspensionLogIds);

        $staff_info_ids = array_column($data,'staff_info_id');
        // 暂停接单的停职记录
        $suspend_work_list = SuspendWorkModel::find([
            'conditions' => 'staff_info_id in ({staff_info_id:array}) and status = :status: and approval_status = :approval_status: and suspend_source = :suspend_source:',
            'bind'       => [
                'staff_info_id' => $staff_info_ids,
                'status' => SuspendWorkModel::STATUS_ALREADY_SUSPEND,
                'approval_status' => Enums::audit_status_2,
                'suspend_source' => SuspendWorkModel::SOURCE_SUSPEND_WORK_TASK,
            ],
        ])->toArray();
        $suspend_work_list_arr = [];
        foreach ($suspend_work_list as $v){
            $key = $v['staff_info_id'].'_'.$v['suspend_date'];
            $suspend_work_list_arr[$key] = $v;
        }
        
        foreach ($data as $value) {
            $oneData['staff_info_id']   = $value['staff_info_id'];
            $oneData['name']            = $value['name'];
            $oneData['job_title_name']  = $jobTitleInfoToId[$value['job_title']] ?? '';
            $oneData['department_name'] = $department_list[$value['node_department_id']] ?? '';
            $oneData['store_name']      = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['store_name'] : '';
            $oneData['region_name']     = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['region_name'] : '';
            $oneData['piece_name']      = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['piece_name'] : '';
            $oneData['suspension_date'] = date('Y-m-d', strtotime($value['stop_duties_date']));
            $stop_duty_reason           = $value['stop_duty_reason'] == 6 ? 'stay_away_from_work' : 'stop_duty_reason_' . $value['stop_duty_reason'];
            $oneData['reason']          = !empty($value['stop_duty_reason']) ? static::$t->_($stop_duty_reason) : '';
            $oneData['reason_id']       = $value['stop_duty_reason'];
            $state                      = $value['state'];
            if ($value['state'] == HrStaffInfoModel::STATE_ON_JOB && $value['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
            }
            $oneData['state_text'] = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';
            $audit = $reinstatement_request_list[$value['suspension_log_id']] ?? [];
            $oneData['audit_state_text'] = !empty($audit) ? static::$t->_('audit_status.' . $audit['state']) : '';
            $oneData['hire_date']         = date('Y-m-d', strtotime($value['hire_date']));
            $oneData['suspension_number'] = $value['stop_duties_count'];
            $oneData['suspension_log_id'] = !empty($value['suspension_log_id']) ? intval($value['suspension_log_id']) : 0;
            $oneData['personal_email']    = $value['personal_email'];
            //可以查看 停职文件，有停职记录，且发过停职文件，且 连续旷工 + 未回公款
            $oneData['is_check']          = in_array($value['stop_duty_reason'], [Enums\StaffEnums::STOP_DUTY_REASON_ABSENTEEISM,Enums\StaffEnums::STOP_DUTY_REASON_NOT_COLLECTION])//连续旷工&未回公款
                && in_array($value['suspension_log_id'], $fileListToId)//有停职文件
                && $oneData['suspension_log_id'] > 0;//有停职记录
            $oneData['hire_type_text']    = !empty($value['hire_type']) ? static::$t->_('hire_type_' . $value['hire_type']) : '';

            $oneData['suspend_work_approval_status_text']    = '';
            $oneData['suspend_work_id']    = '';
            $_key = $value['staff_info_id'].'_'.date('Y-m-d', strtotime($value['stop_duties_date']));
            if ($value['stop_duty_reason'] == Enums\StaffEnums::STOP_DUTY_REASON_VIOLATION_CONTRACT && !empty($suspend_work_list_arr[$_key])){
                $oneData['suspend_work_approval_status_text']    = static::$t->_('audit_status.'.$suspend_work_list_arr[$_key]['approval_status']);
                $oneData['suspend_work_id']    = $suspend_work_list_arr[$_key]['id'] ?? '';
            }
            $list[]                       = $oneData;
        }

        return $list;
    }

    /**
     * 列表查询sql
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getListQuery($params, $isCount = false)
    {
        $columns = $this->getColumns();
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(SuspensionManageLogModel::class, 'sml.staff_info_id = hsi.staff_info_id and sml.is_new = 1',
            'sml');

        $builder = $this->createModel($builder);

        $params['state'] = HrStaffInfoModel::STATE_SUSPEND;
        $builder         = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            $builder->columns('count(*) as count');
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->columns($columns);
        if (empty($params['is_download'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));
        }
        $builder->orderBy('sml.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 构建字段
     * @return array
     */
    public function getColumns()
    {
        return $this->columns;
    }

    /**
     * 构建model
     * @param $builder
     * @return mixed
     */
    public function createModel($builder)
    {
        return $builder;
    }

    /**
     * 构造查询条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        $builder->where('hsi.formal in ({formal:array})',
            ['formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]]);
        $builder->andWhere('hsi.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO]);

        //停职状态
        if (!empty($params['state']) && $params['state'] == HrStaffInfoModel::STATE_SUSPEND) {
            $builder->andWhere('hsi.state = :state:', ['state' => HrStaffInfoModel::STATE_SUSPEND]);
        }

        //雇佣类型
        if (!empty($params['hire_type'])) {
            $builder->inWhere("hsi.hire_type",
                is_array($params['hire_type']) ? $params['hire_type'] : [$params['hire_type']]);
        }
        
        //员工id or name
        if (!empty($params['staff_keyword'])) {
            $builder->andWhere('(hsi.staff_info_id LIKE :staff_keyword: OR hsi.name LIKE :staff_keyword:)',
                ['staff_keyword' => '%' . $params['staff_keyword'] . '%']);
        }

        //网点
        if (!empty($params['sys_store_id'])) {
            $builder->andWhere('hsi.sys_store_id = :sys_store_id:', ['sys_store_id' => $params['sys_store_id']]);
        }

        //职位
        if (!empty($params['job_title'])) {
            $builder->andWhere('hsi.job_title = :job_title:', ['job_title' => $params['job_title']]);
        }

        //部门
        if (!empty($params['department'])) {
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere('hsi.node_department_id', $deptIds);
            }
        }

        //停职原因
        if (!empty($params['reason'])) {
            $builder->andWhere('hsi.stop_duty_reason in ({reason:array})', ['reason' => $params['reason']]);
        }

//        //审批进度
//        if (!empty($params['audit_state'])) {
//            $builder->andWhere('rsr.state = :audit_state:', ['audit_state' => $params['audit_state']]);
//        }

        //停职日期
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $builder->betweenWhere('hsi.stop_duties_date', $params['start_date'] . ' 00:00:00',
                $params['end_date'] . ' 23:59:59');
        }

        //入职日期
        if (!empty($params['hire_start_date']) && !empty($params['hire_end_date'])) {
            $builder->betweenWhere('hsi.hire_date', $params['hire_start_date'] . ' 00:00:00',
                $params['hire_end_date'] . ' 23:59:59');
        }

        //明细表 停职记录 查询
        if(!empty($params['stop_start_date']) && !empty($params['stop_end_date'])){
            $builder->betweenWhere('sml.stop_duties_date', $params['stop_start_date'] . ' 00:00:00', $params['stop_end_date'] . ' 00:00:00');
        }


        //在职状态
        if (!empty($params['staff_state'])) {
            if (in_array(Enums::HRIS_WORKING_STATE_4, $params['staff_state'])) {
                $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state=:state: AND hsi.wait_leave_state=:wait_leave_state:)",
                    [
                        'states'           => $params['staff_state'],
                        'state'            => Enums::HRIS_WORKING_STATE_1,
                        'wait_leave_state' => Enums::WAIT_LEAVE_STATE,
                    ]);
            } elseif (!in_array(Enums::HRIS_WORKING_STATE_4,
                    $params['staff_state']) && in_array(Enums::HRIS_WORKING_STATE_1, $params['staff_state'])) {
                $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state != :wait_leave_state:",
                    ['states' => $params['staff_state'], 'wait_leave_state' => Enums::WAIT_LEAVE_STATE]);
            } else {
                $builder->andWhere("hsi.state IN ({states:array})", ['states' => $params['staff_state']]);
            }
        }

        //停职记录创建 时间查询
        if(!empty($params['sus_log_created_at_start']) && !empty($params['sus_log_created_at_end'])) {
            $startTime = gmdate('Y-m-d H:i:s',strtotime($params['sus_log_created_at_start'] . ' 00:00:00'));
            $endTime   = gmdate('Y-m-d H:i:s',strtotime($params['sus_log_created_at_end'] . ' 23:59:59'));
            $builder->betweenWhere('sml.created_at', $startTime, $endTime);
        }


        return $builder;
    }

    /**
     * 停职管理导出
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function handleExport($params)
    {
        $total = $this->getListQuery($params, true);
        $count = !empty($total) ? intval($total['count']) : 0;
        if ($count > self::LIMIT_DOWNLOAD_NUM) {
            throw new ValidationException(self::$t->_('file_download_limit', ['num' => self::LIMIT_DOWNLOAD_NUM]));
        }

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "staff_suspension_export" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "main";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = show_time_zone(gmdate('Y-m-d H:i:s', time()), 'Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        return $hcmExcelTaskId;
    }

    /**
     * 查看停职文件 
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function checkFileListInfo($params)
    {
        if (empty($params['suspension_id'])) {
            throw new ValidationException('data_error');
        }

        $suspensionInfo = $this->getSuspensionInfo($params['suspension_id']);
        if (empty($suspensionInfo)) {
            $this->logger->write_log('sendEmailSuspension 停职发送邮件 未找到记录信息 id:' . $params['suspension_id'], "notice");
            return false;
        }

        $data = SuspensionFileModel::find([
            'conditions' => 'suspension_manage_log_id = :suspension_id:',
            'bind'       => [
                'suspension_id' => $params['suspension_id'],
            ],
            'order' => 'id desc',
        ])->toArray();

        $operateIds = array_column($data, 'operator_staff_id');
        $operateInfos = [];
        if(!empty($operateIds)) {
            $staffInfos = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array})',
                'bind'       => [
                    'staff_ids' => $operateIds,
                ],
            ])->toArray();
            $operateInfos = array_column($staffInfos, 'name', 'staff_info_id');
        }


        $list = [];
        foreach ($data as $item) {
            $allData['file_id']       = $item['id'];
            $allData['file_type']     = $this->getFileTypeText($item['type']);
            $allData['staff_info_id'] = $item['staff_info_id'];
            $operator_name            = $operateInfos[$item['operator_staff_id']] ?? '';
            $allData['staff_info']    = $operator_name . '(' . $item['operator_staff_id'] . ')';
            $allData['file_url']      = $item['file_url'];
            $allData['created_at']    = date('Y-m-d H:i:s',
                strtotime($item['created_at']) + $this->timeOffset * 3600);

            $list[] = $allData;
        }

        $staffInfo = $this->getStaffInfo($params['staff_info_id']);
        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('no_staff_info'));
        }

        $suspension_file_type = [];
        $fileTypeList = [];
        //连续旷工 文件类型
        if($suspensionInfo['stop_duty_reason'] == Enums\StaffEnums::STOP_DUTY_REASON_ABSENTEEISM && isCountry('PH')) {
            $fileTypeList = SuspensionEnums::$suspension_file_type_list;

        }
        //未回公款 文件类型
        if($suspensionInfo['stop_duty_reason'] == Enums\StaffEnums::STOP_DUTY_REASON_NOT_COLLECTION && isCountry('PH')) {
            $fileTypeList = SuspensionEnums::$not_refunded_file_type_list;
        }

        foreach ($fileTypeList as $key => $value) {
            $suspension_file_type[] = ['value' => $key, 'label' => self::$t->_($value)];
        }

        $result['list']         = $list;
        $result['person_email'] = !empty($staffInfo['personal_email']) ? $staffInfo['personal_email'] : '';
        $result['cc_email'] = $this->getCCEmailList($staffInfo);
        $result['suspension_file_type']   = $suspension_file_type;

        return $result;
    }

    /**
     * 获取上级/上上级/hrbp 邮箱
     * @param $staff_info
     * @return array
     */
    public function getCCEmailList($staff_info): array
    {
        $settingEnvService = new SettingEnvService();
        $email_list = $settingEnvService->getSetVal('suspension_file_cc_email', ',');


        //员工直线上级的企业邮箱;
        $manager_info = $this->getStaffInfo($staff_info['manger']);
        if($manager_info['state'] == HrStaffInfoModel::STATE_ON_JOB && !empty($manager_info['email'])) {
            $email_list[] = $manager_info['email'];
        }

        //员工上上级的企业邮箱;
        $manager_manger_info = $this->getStaffInfo($manager_info['manger']);
        if($manager_manger_info['state'] == HrStaffInfoModel::STATE_ON_JOB && !empty($manager_manger_info['email'])) {
            $email_list[] = $manager_manger_info['email'];
        }

        //员工对应HRBP的企业邮箱（HRBP查找逻辑：按照HCM-基础设置-审批管辖范围查找）
        $staffInfoService = new StaffInfoService();
        $hrbp_data = $staffInfoService->findHRBPToBackyard(['department_id' => $staff_info['node_department_id'], 'store_id' => $staff_info['sys_store_id']]);
        if ($hrbp_data['code'] != ErrCode::SUCCESS || !isset($hrbp_data['data']['hrbp'])) {
            throw new BusinessException(self::$t->_('retry_later'));
        }
        $hrbp_ids = $hrbp_data['data']['hrbp'];
        if(!empty($hrbp_ids)) {
            $hrbp_ids = explode(',', $hrbp_ids);
            $hrbp_info = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({staff_ids:array})',
                'bind'       => [
                    'staff_ids' => $hrbp_ids,
                ],
                'columns' => 'email',
            ])->toArray();
            $hrbp_email = array_filter(array_column($hrbp_info, 'email'));
            $email_list = array_merge($email_list, $hrbp_email);
        }
        return !empty($email_list) ? array_values(array_unique($email_list)) : [];
    }

    public function getFileTypeText($type)
    {
        return self::$t->_(SuspensionFileModel::$suspension_types[$type]);
    }

    /**
     * 再次发送
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function send($params)
    {
        $fileInfo = SuspensionFileModel::findFirst([
            'conditions' => 'id = :file_id:',
            'bind'       => [
                'file_id' => $params['file_id'],
            ],
        ]);
        if (empty($fileInfo)) {
            throw new ValidationException('data_error');
        }
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $fileInfo->staff_info_id,
            ],
        ]);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('no_staff_info'));
        }
        if (empty($params['email'])){
            throw new ValidationException('email is empty');
        }
        if (isCountry('MY') && in_array($staffInfo->hire_type,HrStaffInfoModel::$agentTypeTogether)){
            $staff_data = $staffInfo->toArray();
            $this->sendEmailAgent($params['email'],$staff_data,$fileInfo->file_url,true);
            return true;
        }
        if (empty($staffInfo->personal_email)) {
            throw new ValidationException('email_not_empty');
        }

        $data['is_lnt'] = (new StaffService())->isLntStaff($staffInfo->staff_info_id);
        $staff_name = '_' . $staffInfo->name;
        if ($data['is_lnt']) {
            $staff_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $staff_name, -1);
        } else {
            $staff_name = str_replace('/', '', $staff_name);
        }



        $pdfFileName = $staffInfo->staff_info_id . $staff_name . '_BOC';
        //为了给文件命名
        $fileUrl            = $this->fileDownload($fileInfo->file_url, '', $pdfFileName . '.pdf');

        $data['staff_id']   = $staffInfo->staff_info_id;
        $data['staff_name'] = $staffInfo->name;
        $data['email']      = $params['email'] ?? '';
        $data['cc_email']   = (new SettingEnvService())->getSetVal('suspension_file_cc_email', ',');
        $data['file_url']   = $fileUrl;
        $date               = date("d-m-Y",
            strtotime('+3 day', strtotime($fileInfo->created_at) + $this->timeOffset * 3600));
        $data['date']       = $this->getDate($date);
        $data['pdfFileName']       = $pdfFileName;

        if (!$this->sendEmail($data)) {
            throw new ValidationException('send_state_fail');
        }

        return true;
    }

    /**
     * 发送日期
     * @param $date
     * @return string
     */
    public function getDate($date)
    {
        $dateArray    = explode('-', $date);
        $dateArray[1] = GlobalEnums::MONTHS_MAPS[intval($dateArray[1])];

        return implode(' ', $dateArray);
    }

    /**
     * MY 日期转换 马来语
     * @param $date
     * @return mixed
     */
    public function getMonthDate($date)
    {
        $dateArray     = explode('-', $date);
        $data['month'] = GlobalEnums::MY_MONTHS_MAPS[intval($dateArray[1])] . ' ' . $dateArray[2];// MY全称 月 年

        $dateArray[1] = GlobalEnums::MY_MONTHS_MAPS_ABBR[intval($dateArray[1])];//MY 缩写 月
        $data['date'] = implode(' ', $dateArray);

        return $data;
    }


    /**
     * 发送邮箱
     * @param $params
     * @return bool
     */
    public function sendEmail($params)
    {
        try {
            $staffsEmail = $params['email'];
            //发送邮件
            $title   = self::$t->_('suspension_email_title',
                ['staff_id' => $params['staff_id'], 'staff_name' => $params['staff_name']]);
            $content = self::$t->_('suspension_email_content',
                ['staff_name' => $params['staff_name'], 'date' => $params['date']]);
            if (empty($staffsEmail)) {
                $this->logger->notice('suspension_send 邮件发送失败,邮箱不能为空，staff_id' . $params['staff_id']);

                return false;
            }
            if ($params['is_lnt']) {
                $this->logger->info(['lnt_sendEmail' => $params]);
                $sendResult = FlashMailer::getInstance($this->getDI()->getConfig()->mail_lnt)->send($staffsEmail, $title, $content, [$params['file_url']],[],$params['cc_email']);
            } else {
                $sendResult = \App\Library\BiMail::send($staffsEmail, $title, $content, [$params['file_url']],
                    $params['cc_email']);
            }

            if (!$sendResult) {
                $json = json_encode(['staff_info_id' => $params['staff_id'], 'email' => $staffsEmail,$params],
                    JSON_UNESCAPED_UNICODE);
                $this->logger->notice('suspension_send :邮件发送失败' . $json);
                return false;
            }
            return true;
        } catch (Exception $e) {
            $this->logger->notice(['param'=>$params,'exception'=>$e->getMessage(),'trace'=>$e->getTraceAsString()]);
            return false;
        }
    }

    /**
     * 推到 redis
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function sendEmailPushRedis($params)
    {
        if (empty($params)) {
            throw new ValidationException('params error');
        }
        $content = json_encode($params, JSON_UNESCAPED_UNICODE);

        $redis = $this->getDI()->get("redis");
        $res   = $redis->lpush(\App\Library\Enums\StaffEnums::STAFF_SUSPENSION_SEND_EMAIL_LIST, $content);

        if (!$res) {
            $this->logger->write_log('sendEmailPushRedis 停职发送邮件:' . $content, "notice");
            throw new ValidationException(self::$t->_('server_error') . ": redis lpush 失败");
        }

        return true;
    }

    
    /**
     * MY 生成pdf 发送邮件
     * @param $suspension_manage_log_id
     * @param $stopDutyReason
     * @param $date_of_issue
     * @return bool
     */
    public function creatPdf($suspension_manage_log_id, $stopDutyReason = Enums\StaffEnums::STOP_DUTY_REASON_ABSENTEEISM, $date_of_issue = '')
    {
        $suspensionInfo = $this->getSuspensionInfo($suspension_manage_log_id);
        if (empty($suspensionInfo)) {
            $this->logger->write_log('sendEmailSuspension 停职发送邮件 未找到记录信息 id:' . $suspension_manage_log_id, "notice");
            return false;
        }

        $staffInfo = $this->getStaffInfo($suspensionInfo['staff_info_id']);
        if (empty($staffInfo)) {
            $this->logger->write_log('sendEmailSuspension 停职发送邮件 未找到员工信息 id:' . $suspension_manage_log_id . ',staff_info_id:' . $suspensionInfo['staff_info_id'],
                "notice");
            return false;
        }

        if (in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether)) {
            $this->logger->info("BOC pdf unpaid " . $suspensionInfo['staff_info_id']);
            return true;
        }

        //连续旷工
        if ($stopDutyReason == Enums\StaffEnums::STOP_DUTY_REASON_ABSENTEEISM) {
            $tripleToLeaveInfo = $this->getTripleToLeaveInfo($suspensionInfo['staff_info_id']);
            if (empty($tripleToLeaveInfo)) {
                $this->logger->write_log('sendEmailSuspension 停职发送邮件 未找到员工旷工信息 id:' . $suspension_manage_log_id . ',staff_info_id:' . $suspensionInfo['staff_info_id'],
                    "notice");
                return false;
            }
            $this->logger->write_log('sendEmailSuspension 停职发送邮件 员工旷工信息 suspension_id:' . $suspension_manage_log_id . ',tripleToLeaveInfo:' . json_encode($tripleToLeaveInfo,
                    JSON_UNESCAPED_UNICODE),
                "info");
            //连续旷工的开始日期。
            $stop_begin_date = date("d-m-Y", strtotime($tripleToLeaveInfo['stop_begin_date']));

         //未回公款
        } else {
            $createdDate = show_time_zone($suspensionInfo['created_at'], 'Y-m-d');
            //未回公款--变为停职的日期（停职记录的创建日期）
            $stop_begin_date = date("d-m-Y", strtotime("+1 days", strtotime($createdDate)));
        }

        $sysService      = new SysService();
        $department_list = array_column($sysService->getDepartmentListFromCache(), 'name', 'id');

        $job_title_service = new HrJobTitleService();
        $job_title         = $job_title_service->getJobTitleListFromCache();
        $job_title_list    = array_column($job_title, 'job_name', 'id');

        $settingEnvService = new SettingEnvService();

        $tmpPath = BASE_PATH . '/app/views/suspension_pdf_temp/BOC.ftl';
        $boc_pdf_temp_url = $this->getPdfTemp($tmpPath);

        $staff_absent_days = $settingEnvService->getSetVal('staff_absent_days');//连续旷工N天数
        $staff_leave_days  = $settingEnvService->getSetVal('staff_leave_days');//再连续旷工N天数

        $monthDate       = $this->getMonthDate($stop_begin_date);//连续旷工起始日信息||未回公款 变为停职的日期 +1 天
        $date_of_issue = empty($date_of_issue) ? date('d-m-Y'): date('d-m-Y', strtotime($date_of_issue));
        $today           = $this->getMonthDate($date_of_issue);//签发日

        $companyConfigInfo = (new \App\Modules\My\Services\CertificateService())->getCompanyConfigInfo(['staff_info_id' => $suspensionInfo['staff_info_id']]);
        $is_lnt     = (new StaffService())->isLntStaff($staffInfo['staff_info_id']);

        $fileData['date']                   = $today['date'];//签发日期
        $fileData['month']                  = $monthDate['month'];//连续旷工起始月
        $fileData['name']                   = $staffInfo['name'];//姓名
        $fileData['staff_info_id']          = $staffInfo['staff_info_id'];//工号
        $fileData['job_title_name']         = $job_title_list[$staffInfo['job_title']] ?? '';//职位
        $fileData['department_name']        = $department_list[$staffInfo['node_department_id']] ?? '';//部门名称
        $fileData['sex']                    = GlobalEnums::MY_SEX_MAPS[$staffInfo['sex']] ?? '';//性别
        $fileData['absence_day_num']        = $staff_absent_days;//连续旷工天数
        $fileData['absence_hours_num']      = $staff_absent_days * 24;//连续旷工 小时
        $fileData['absence_day_start']      = $monthDate['date'];//旷工起始日
        $fileData['absence_leave_days']     = GlobalEnums::MY_NUMBER[$staff_leave_days] ?? '';//再连续旷工N天数
        $fileData['absence_leave_days_num'] = $staff_leave_days;//再连续旷工N天数
        $fileData['company_info']           = $companyConfigInfo['company_name'] ?? '';
        $fileData['boc_department_email']   = $is_lnt ? 'Director (<EMAIL>)' : ( $companyConfigInfo['id'] == SalaryEnums::FLASH_COMMERCE ? 'Jabatan F-Commerce PMO-MY (<EMAIL>)' : 'Jabatan People Management (<EMAIL>)');

        $staff_name = '_' . $staffInfo['name'];
        if ($is_lnt) {
            $staff_name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $staff_name, -1);
        } else {
            $staff_name = str_replace('/', '', $staff_name);
        }

        $pdfFileName = $staffInfo['staff_info_id'] . $staff_name . '_BOC';


        [$header, $footer] = $this->getBodPdfHeaderFooter($companyConfigInfo);
        $singPdfSetting = [
            'format'              => 'a4',
            'displayHeaderFooter' => true,
            'footerTemplate'      => $footer,
            'headerTemplate'      => $header,
        ];

        $pdfRes = (new FormPdfServer())->getInstance()->generatePdf($boc_pdf_temp_url, $fileData, [], $singPdfSetting,
            $pdfFileName, 'attchment');

        if (!isset($pdfRes['object_url'])) {
            $this->logger->write_log('sendEmailSuspension 停职发送邮件 pdf 生产失败 id:' . $suspension_manage_log_id . ',staff_info_id:' . $suspensionInfo['staff_info_id'],
                "notice");
            return false;
        }

        $insertData['suspension_manage_log_id'] = $suspension_manage_log_id;
        $insertData['staff_info_id']            = $staffInfo['staff_info_id'];
        $insertData['type']                     = 1;
        $insertData['file_url']                 = $pdfRes['object_url'];

        $db_by = $this->getDI()->get('db_backyard');
        $db_by->insertAsDict('suspension_file', $insertData);

        //为了给文件命名
        $fileUrl = $this->fileDownload($pdfRes['object_url'], '', $pdfFileName . '.pdf');

        $data['is_lnt']     = $is_lnt;
        $data['staff_id']   = $staffInfo['staff_info_id'];
        $data['staff_name'] = $staffInfo['name'] ?? '';
        $data['email']      = $staffInfo['personal_email'] ?? '';
        $data['cc_email']   = $settingEnvService->getSetVal('suspension_file_cc_email', ',');

        $data['file_url'] = $fileUrl;

        $date = date("d-m-Y", strtotime("+3 days", strtotime($date_of_issue)));
        $data['date'] = $this->getDate($date);

        if (!$this->sendEmail($data)) {
            return false;
        }
        return true;
    }

    /**
     * my 个人代理 停职发送pdf邮件
     * @param $suspensionInfo
     * @param $staffInfo
     * @param $stopDutyReason
     * @param $date_of_issue
     * @return true
     */
    public function agentCreatPdf($suspensionInfo, $staffInfo, $stopDutyReason, $date_of_issue): bool
    {
        $logger = $this->getDI()->get('logger');
        try {
            if (
                !isCountry('MY') ||
                empty($suspensionInfo) ||
                empty($staffInfo) ||
                empty($stopDutyReason) ||
                !in_array($stopDutyReason,
                    [
                        Enums\StaffEnums::STOP_DUTY_REASON_ABSENTEEISM,
                        Enums\StaffEnums::STOP_DUTY_REASON_NOT_COLLECTION,
                    ]) ||
                !in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether) ||
                empty($staffInfo['signing_date'])
            ) {
                throw new Exception('入口参数错误');
            }
            $logger->write_log("suspension agentCreatPdf suspensionInfo:" . json_encode($suspensionInfo) . " ,staffInfo:" . json_encode($staffInfo) . " ,stopDutyReason:" . $stopDutyReason . ",date_of_issue:" . $date_of_issue,
                "info");
            $tmpPath                   = BASE_PATH . '/app/views/suspension_pdf_temp/agent_suspension.ftl';
            $boc_pdf_temp_url          = $this->getPdfTemp($tmpPath);
            $companyConfigInfo         = (new \App\Modules\My\Services\CertificateService())->getCompanyConfigInfo(['staff_info_id' => $staffInfo['staff_info_id']]);
            $fileData['company_name']  = $companyConfigInfo['company_name'];//公司名
            $fileData['staff_name']    = $staffInfo['name'];         //姓名
            $fileData['staff_info_id'] = $staffInfo['staff_info_id'];//工号
            $fileData['signing_date']  = $staffInfo['signing_date']; //合同日期
            $fileData['generate_date'] = empty($date_of_issue) ? date('d-m-Y') : date('d-m-Y',
                strtotime($date_of_issue));                          // 信函日期
            if ($stopDutyReason == Enums\StaffEnums::STOP_DUTY_REASON_ABSENTEEISM) {
                // 连续旷工
                $triple_to_leave_data = TripleToLeaveModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and type = :type:',
                    'bind'       => [
                        'staff_id' => $staffInfo['staff_info_id'],
                        'type'     => TripleToLeaveModel::TYPE_CONTINUE_ABSENT,
                    ],
                    'order'      => 'id desc',
                ]);
                $triple_to_leave_data = !empty($triple_to_leave_data) ? $triple_to_leave_data->toArray() : [];
                if (empty($triple_to_leave_data) || empty($triple_to_leave_data['stop_date_detail'])) {
                    throw new Exception('triple_to_leave.stop_date_detail is null');
                }
                $stop_date_detail = json_decode($triple_to_leave_data['stop_date_detail'], true);
                sort($stop_date_detail);
            }
            if ($stopDutyReason == Enums\StaffEnums::STOP_DUTY_REASON_NOT_COLLECTION) {
                //未回公款
                $hr_staff_not_refunded_suspension_task_data = HrStaffNotRefundedSuspensionTaskModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: or main_staff_info_id = :main_staff_info_id:',
                    'bind'       => [
                        'staff_id' => $staffInfo['staff_info_id'],
                        'main_staff_info_id' => $staffInfo['staff_info_id'],
                    ],
                    'order'      => 'id desc',
                ]);
                if (empty($hr_staff_not_refunded_suspension_task_data) || empty($hr_staff_not_refunded_suspension_task_data->content_json)) {
                    throw new Exception('hr_staff_not_refunded_suspension_task.business_date is null');
                }
                $_content_json = json_decode($hr_staff_not_refunded_suspension_task_data->content_json, true);
                $_not_pay_details = array_column($_content_json['not_pay_details'] ?? [], 'date');
                if (empty($_not_pay_details)){
                    throw new Exception('hr_staff_not_refunded_suspension_task.business_date is null');
                }
                $fileData['content_business_date'] = implode(',',$_not_pay_details);// 未回公款日期
                $fileData['leave_day_num']         = (new SettingEnvService())->getSetVal('notpaid_leave_days') ?: 0;
                $fileData['leave_day']             = strtoupper(GlobalEnums::MY_NUMBER[$fileData['leave_day_num']] ?? '');
                if (empty($fileData['leave_day_num']) || empty($fileData['leave_day'])) {
                    // 没有对应值 直接返回不生成pdf了
                    throw new Exception('notpaid_leave_day_num or notpaid_leave_day is empty');
                }
                $fileData['content'] = '<p>
        Anda telah gagal dan/atau abai untuk memulangkan Cash On Delivery
        (“<strong>COD</strong>”) kepada Syarikat berdasarkan Tahap Perkhidmatan
        pada ' . $fileData['content_business_date'] . '.
      </p>
      <p>
        Penggantungan ini akan berkuat kuasa mulai tarikh surat ini. Sekiranya
        COD dan/atau bungkusan tersebut tidak dikembalikan kepada Syarikat dalam
        masa
        <span style="color: red;font-weight: bold">' . $fileData['leave_day'] . ' (' . $fileData['leave_day_num'] . ') HARI</span>
        (termasuk tarikh surat ini). Syarikat berhak untuk menamatkan Perjanjian
        dengan serta-merta dan menolak nilai COD dan bungkusan yang tertunggak
        daripada Fi Perkhidmatan anda.
      </p>';
            } elseif ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                // 连续旷工 - 个人代理
                if (empty($stop_date_detail[0]) || empty($stop_date_detail[count($stop_date_detail) - 1])) {
                    // 没有对应值 直接返回不生成pdf了
                    throw new Exception('triple_to_leave.stop_date_detail is null');
                }
                $fileData['stop_date_detail_date_1'] = date('d-m-Y',
                    strtotime($stop_date_detail[0]));
                $fileData['stop_date_detail_date_2'] = date('d-m-Y',
                    strtotime($stop_date_detail[count($stop_date_detail) - 1]));
                $fileData['leave_day_num_1']           = (new SettingEnvService())->getSetVal('IC_staff_absent_days') ?: 0;
                $fileData['leave_day_1']               = strtoupper(GlobalEnums::MY_NUMBER[$fileData['leave_day_num_1']] ?? '');
                $fileData['leave_day_num_2']           = (new SettingEnvService())->getSetVal('IC_staff_leave_days') ?: 0;
                $fileData['leave_day_2']               = strtoupper(GlobalEnums::MY_NUMBER[$fileData['leave_day_num_2']] ?? '');
                if (empty($fileData['leave_day_num_1']) || empty($fileData['leave_day_1']) || empty($fileData['leave_day_num_2']) || empty($fileData['leave_day_2'])) {
                    // 没有对应值 直接返回不生成pdf了
                    throw new Exception('notpaid_leave_day_num or notpaid_leave_day is empty');
                }
                $fileData['content'] = '<p>
        Anda telah gagal dan/atau abai untuk melaksanakan Perkhidmatan sebanyak
        <strong>'.$fileData['leave_day_1'].' (' . $fileData['leave_day_num_1'] . ') HARI</strong> secara berturut-turut selepas menerima Perkhidmatan yang terjadual dari ' . $fileData['stop_date_detail_date_1'] . ' hingga ' . $fileData['stop_date_detail_date_2'] . '.
      </p>
      <p>
        Penggantungan ini akan berkuat kuasa mulai tarikh surat ini. Sekiranya kami tidak menerima penjelasan yang memuaskan daripada anda dalam tempoh
        <span style="color: red;font-weight: bold">' . $fileData['leave_day_2'] . ' (' . $fileData['leave_day_num_2'] . ') HARI</span>
        (termasuk tarikh surat ini), Syarikat berhak untuk menamatkan Perjanjian dengan serta-merta dan menolak nilai COD dan bungkusan yang tertunggak daripada Fi Perkhidmatan anda. 
      </p>';
            } elseif ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT) {
                // 连续旷工 - 兼职个人代理
                if (empty($stop_date_detail[0])) {
                    // 没有对应值 直接返回不生成pdf了
                    throw new Exception('triple_to_leave.stop_date_detail is null');
                }
                $fileData['stop_date_detail_date_1'] = date('d-m-Y',
                    strtotime($stop_date_detail[0]));
                $fileData['leave_day_num']           = (new SettingEnvService())->getSetVal('part_time_IC_staff_leave_days') ?: 0;
                $fileData['leave_day']               = strtoupper(GlobalEnums::MY_NUMBER[$fileData['leave_day_num']] ?? '');
                if (empty($fileData['leave_day_num']) || empty($fileData['leave_day'])) {
                    // 没有对应值 直接返回不生成pdf了
                    throw new Exception('notpaid_leave_day_num or notpaid_leave_day is empty');
                }
                $fileData['content'] = '<p>Anda telah gagal dan/atau abai untuk melaksanakan Perkhidmatan selepas menerima Perkhidmatan yang terjadual pada ' . $fileData['stop_date_detail_date_1'] . '.
      </p>
      <p>
        Penggantungan ini akan berkuat kuasa mulai tarikh surat ini. Sekiranya kami tidak menerima penjelasan yang memuaskan daripada anda dalam <span style="color: red;font-weight: bold">' . $fileData['leave_day'] . ' (' . $fileData['leave_day_num'] . ') HARI</span> (termasuk tarikh surat ini), Syarikat berhak untuk menamatkan Perjanjian dengan serta-merta dan menolak nilai COD dan bungkusan yang tertunggak daripada Fi Perkhidmatan anda. 
      </p>';
            } else {
                throw new Exception('stopDutyReason error');
            }
//            $lang = (new StaffService())->getAcceptLanguage($staffInfo['staff_info_id']);
            $t = $this->getTranslation('en');

            $email_data['email_title']   = $t->_('agent_suspension_email_title',
                [
                    'staff_id'   => $staffInfo['staff_info_id'],
                    'staff_name' => $staffInfo['name'],
                    'date'       => date('d-m-Y', strtotime($staffInfo['stop_duties_date'])),
                ]);

            [$header, $footer] = $this->getBodPdfHeaderFooter($companyConfigInfo);
            $singPdfSetting = [
                'format'              => 'a4',
                'displayHeaderFooter' => true,
                'footerTemplate'      => $footer,
                'headerTemplate'      => $header,
            ];
            $pdfFileName    = str_replace('/', '', $email_data['email_title']);

            $pdfRes = (new FormPdfServer())->getInstance()->generatePdf($boc_pdf_temp_url, $fileData, [],
                $singPdfSetting,
                $pdfFileName, 'attchment');

            if (!isset($pdfRes['object_url'])) {
                throw new Exception('生成pdf失败');
            }
            $insertData['suspension_manage_log_id'] = $suspensionInfo['id'];
            $insertData['staff_info_id']            = $staffInfo['staff_info_id'];
            $insertData['type']                     = 1;
            $insertData['file_url']                 = $pdfRes['object_url'];
            $db_by                                  = $this->getDI()->get('db_backyard');
            $db_by->insertAsDict('suspension_file', $insertData);
            // 发送邮件
            if (empty($staffInfo['personal_email'])) {
                throw new Exception('个人邮箱为空，发送邮件失败');
            }
            $this->sendEmailAgent($staffInfo['personal_email'],$staffInfo,$pdfRes['object_url'],true);
            return true;
        } catch (Exception $e) {
            $logger->write_log("suspension agentCreatPdf suspensionInfo:" . json_encode($suspensionInfo) . " ,staffInfo:" . json_encode($staffInfo) . " ,stopDutyReason:" . $stopDutyReason . ",date_of_issue:" . $date_of_issue . ",message:" . $e->getMessage(),
                "error");
            return false;
        }
    }
    
    /**
     * @param $email
     * @param $staffInfo
     * @param $pdf_url
     * @param $is_cc
     * @return bool
     * @throws \PHPMailer\PHPMailer\Exception
     */
    public function sendEmailAgent($email,$staffInfo,$pdf_url,$is_cc = true)
    {
        if (!isCountry('MY')){
            return false;
        }
        $t = $this->getTranslation('en');
        $email = empty($email) ? $staffInfo['personal_email'] : $email;
        if (empty($email)){
            throw new Exception('邮件为空');
        }
        $email_data['email_title']   = $t->_('agent_suspension_email_title',
            [
                'staff_id'   => $staffInfo['staff_info_id'],
                'staff_name' => $staffInfo['name'],
                'date'       => date('d-m-Y', strtotime($staffInfo['stop_duties_date'])),
            ]);
        $email_data['email_content'] = $t->_('agent_suspension_email_content');
        $pdfFileName    = str_replace('/', '', $email_data['email_title']);
        $email_data['file_url'] = $this->fileDownload($pdf_url, '', $pdfFileName . '.pdf');
        if ($is_cc){
            // 获取抄送人
            $email_data['cc_email'] = (new SettingEnvService())->getSetVal('email_agent_suspension_resign',',') ?: '';
        }
        $sendResult             = \App\Library\BiMail::send($email,
            $email_data['email_title'],
            $email_data['email_content'], 
            [$email_data['file_url']],
            $email_data['cc_email'] ?? []);
        if (!$sendResult) {
            throw new Exception('发送邮件失败');
        }
        return true;
    }

    /**
     * 获取BOC pdf 文件页眉页脚
     * @param $companyConfigInfo
     * @return array
     */
    public function getBodPdfHeaderFooter($companyConfigInfo)
    {
        $header = '<div style="width: 100%; margin: 0 19mm; box-sizing: border-box">
  <table
    style="
      line-height: 6mm;
      width: 100%;
      font-weight: 700;
      font-size: 6mm;
      font-family: sans-serif;
      background-size: 38mm;
    "
  >
    <tr>
      <td style="text-align: right">
        <img
          style="height: 9.5mm; width: auto; object-fit: cover"
          src="' . $companyConfigInfo['company_logo_url_base64'] .'"
        />
      </td>
    </tr>
  </table>
</div>';

        $color_footer_right = CompanyEnums::$company_color_right[$companyConfigInfo['company_id']] ?? 'yellow';
        $color_footer_left  = CompanyEnums::$company_color_left[$companyConfigInfo['company_id']] ?? 'black';

        $footer = '<div style="width: 297mm; transform: translateY(4mm); box-sizing: border-box">
  <div style="width: 85%; text-align: right">
    <img
      style="width: auto; height: 17mm; object-fit: cover"
      src="' . $companyConfigInfo['company_card_url_base64'] .'"
      alt=""
    />
  </div>
  <div
    style="width: 100%; display: flex; flex-direction: row; flex-wrap: nowrap"
  >
    <div style="height: 0; width: 60mm; border: 4mm solid ' . $color_footer_left . '"></div>
    <div
      style="
        width: 0px;
        height: 0px;
        border-color: ' . $color_footer_left . ' ' . $color_footer_right .' ' . $color_footer_right . ' ' . $color_footer_left . ';
        border-width: 10px 10px 20px 20px;
        border-style: solid;
      "
    ></div>
    <div style="height: 0; width: 90mm; border: 4mm solid ' . $color_footer_right . '"></div>
  </div>
</div>';

        return [$header, $footer];
    }

    /**
     * 获取离职记录信息
     * @param $id
     * @return array
     */
    public function getSuspensionInfo($id)
    {
        $data = SuspensionManageLogModel::findFirst([
            'conditions' => 'id = :log_id:',
            'bind'       => [
                'log_id' => $id,
            ],
        ]);

        if (empty($data)) {
            return [];
        }

        return $data->toArray();
    }

    public function getStaffInfo($staffId)
    {
        $data = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $staffId,
            ],
        ]);

        if (empty($data)) {
            return [];
        }

        return $data->toArray();
    }

    /**
     * 获取旷工信息
     * @param $staffId
     * @return array
     */
    public function getTripleToLeaveInfo($staffId)
    {
        $data = TripleToLeaveModel::findFirst([
            'columns'    => 'id,staff_info_id,stop_begin_date',
            'conditions' => 'staff_info_id = :staff_id: and type = :type:',
            'bind'       => [
                'staff_id' => $staffId,
                'type'     => TripleToLeaveModel::TYPE_CONTINUE_ABSENT,
            ],
            'order'      => 'id desc',
        ]);

        if (empty($data)) {
            return [];
        }

        return $data->toArray();
    }

    /**
     * 获取员工 所属公司
     * @param $node_department_id
     * @return int
     */
    public function getCompanyId($node_department_id)
    {
        $department = (new SysDepartmentService())->getDepartmentDetail($node_department_id);
        if (empty($department)) {
            return BocEnums::COMPANY_OTHER;
        }
        $departmentArray = explode('/', $department['ancestry_v3']);
        if (empty($departmentArray[2])) {
            return BocEnums::COMPANY_OTHER;
        }
        if (in_array($departmentArray[2], [BocEnums::COMPANY_FFL, BocEnums::COMPANY_FCM, BocEnums::COMPANY_FP ,BocEnums::COMPANY_MONEY])) {
            return $departmentArray[2];
        }

        return BocEnums::COMPANY_OTHER;
    }

    /**
     * 停职管理-停职记录
     * @param $params
     * @return mixed
     */
    public function getLogList($params)
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $total = $this->getLogListQuery($params, true);
        $list  = $this->getLogListQuery($params);
        if ($list) {
            $list = $this->formatList($list);
        }

        $data['total'] = !empty($total) ? intval($total['count']) : 0;
        $data['list']  = $list;

        return $data;
    }

    /**
     * 停职记录 query
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getLogListQuery($params, $isCount = false)
    {
        $columns = [
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.job_title',
            'hsi.node_department_id',
            'hsi.sys_store_id',
            'sml.stop_duties_date',
            'sml.stop_duty_reason',
            'hsi.state',
            'hsi.hire_type',
            'hsi.hire_date',
            'hsi.stop_duties_count',
            'hsi.wait_leave_state',
            'hsi.personal_email',
            'sml.id as suspension_log_id',
        ];

        $builder = $this->modelsManager->createBuilder();

        $builder->from(['sml' => SuspensionManageLogModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'sml.staff_info_id = hsi.staff_info_id and sml.is_new = 1',
            'hsi');

        $builder = $this->getBuilderWhere($builder, $params);
        if ($isCount) {
            $builder->columns('count(*) as count');
            return $builder->getQuery()->getSingleResult()->toArray();
        }
        $builder->columns($columns);
        if (empty($params['is_download'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));
        }
        $builder->orderBy('sml.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取停职记录信息
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getLogHistoryList($params)
    {
        $staffInfo = $this->getStaffInfo($params['staff_info_id']);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $storeList = (new SysStoreService())->getStoreListByIds([$staffInfo['sys_store_id']]);

        $storeList[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $jobTitleInfo = (new DepartmentService())->getJobList('', [$staffInfo['job_title']], true);

        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }
        $storeListToId = [];
        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $department_list = (new DepartmentService())->getAllDepartment([$staffInfo['node_department_id']]);

        $staffData['staff_info_id']   = $staffInfo['staff_info_id'];
        $staffData['name']            = $staffInfo['name'];
        $staffData['department_name'] = $department_list[$staffInfo['node_department_id']] ?? '';
        $staffData['job_title_name']  = $jobTitleInfoToId[$staffInfo['job_title']] ?? '';
        $staffData['sys_store_name']  = isset($storeListToId[$staffInfo['sys_store_id']]) ? $storeListToId[$staffInfo['sys_store_id']]['store_name'] : '';
        $staffData['hire_date']       = date('Y-m-d', strtotime($staffInfo['hire_date']));
        $staffData['personal_email']  = $staffInfo['personal_email'];

        $state = $staffInfo['state'];
        if ($staffInfo['state'] == 1 && $staffInfo['wait_leave_state'] == 1) {//待离职
            $state = 999;
        }
        $staffData['state_text'] = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';

        $suspensionList = $this->getSuspensionLogAll($staffInfo['staff_info_id']);

        $suspensionListAll = $this->getSuspensionListFormatList($suspensionList);
        $data['staff_info'] = $staffData;
        $data['list']       = $suspensionListAll;

        return $data;
    }

    /**
     * suspensionList 格式化数据
     * @param $suspensionList
     * @return array
     */
    public function getSuspensionListFormatList($suspensionList): array
    {
        $operate_ids      = array_values(array_unique(array_column($suspensionList, 'operate_id')));
        $operateInfos     = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staff_ids:array})',
            'bind'       => [
                'staff_ids' => $operate_ids,
            ],
        ])->toArray();
        $suspensionLogIds = array_column($suspensionList, 'id');
        $fileList         = SuspensionFileRepository::getSuspensionFile($suspensionLogIds);
        $fileListToId     = array_column($fileList, 'suspension_manage_log_id');

        $operateInfosToName = array_column($operateInfos, 'name', 'staff_info_id');

        $suspensionListAll = [];
        foreach ($suspensionList as $one) {
            $oneData['suspension_log_id'] = $one['id'];
            $oneData['created_at']        = $one['created_at'];

            $operateName     = $operateInfosToName[$one['operate_id']] ?? '';
            $oneData['name'] = $one['operate_id'] == -1 ? 'System' : $operateName . '(' . $one['operate_id'] . ')';

            $oneData['suspension_date']  = date('Y-m-d', strtotime($one['stop_duties_date']));
            $stop_duty_reason            = $one['stop_duty_reason'] == 6 ? 'stay_away_from_work' : 'stop_duty_reason_' . $one['stop_duty_reason'];
            $oneData['reason']           = !empty($one['stop_duty_reason']) ? static::$t->_($stop_duty_reason) : '';
            $oneData['audit_state_text'] = !empty($one['audit_state']) ? static::$t->_('audit_status.' . $one['audit_state']) : '';
            $oneData['is_check']         = $this->isCheckFile($one['stop_duty_reason'], $one['id'], $fileListToId);
            $oneData['expected_date']    = $one['expected_date'] ?? '';
            $oneData['effective_date']   = $one['effective_date'] ?? '';
            $suspensionListAll[]         = $oneData;
        }

        return $suspensionListAll;
    }

    /**
     * 是否可以查看 文件
     * @param $stop_duty_reason
     * @param $susId
     * @param $susFileIds
     * @return bool
     */
    public function isCheckFile($stop_duty_reason, $susId = 0, $susFileIds = [])
    {
        return in_array($stop_duty_reason, [Enums\StaffEnums::STOP_DUTY_REASON_ABSENTEEISM, Enums\StaffEnums::STOP_DUTY_REASON_NOT_COLLECTION]) && in_array($susId, $susFileIds);
    }

    /**
     * 获取停职记录信息
     * @param $staffId
     * @return mixed
     */
    public function getSuspensionLogAll($staffId)
    {
        $columns = [
            'sml.id',
            'sml.staff_info_id',
            'sml.stop_duties_date',
            'sml.stop_duty_reason',
            'sml.operate_id',
            'rsr.state as audit_state',
            'rsr.expected_date',
            'rsr.effective_date',
            "DATE_FORMAT(CONVERT_TZ(sml.created_at, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as created_at",
        ];

        $builder = $this->modelsManager->createBuilder();

        $builder->from(['sml' => SuspensionManageLogModel::class]);
        $builder->leftJoin(ReinstatementRequestModel::class, 'sml.id = rsr.ref_id',
            'rsr');

        $builder->where('sml.staff_info_id = :staff_id:', ['staff_id' => $staffId]);
        $builder->columns($columns);
        $builder->orderBy('sml.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     *
     * @param $params
     * @return mixed
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function auditDetailInfo($params)
    {
        $suspension_info = $this->getSuspensionInfo($params['suspension_log_id']);
        if (empty($suspension_info)) {
            throw new ValidationException(self::$t->_('data_error'));
        }
        $reinstatementInfo = ReinstatementRequestModel::findFirst([
            'conditions' => 'ref_id = :ref_id:',
            'bind'       => [
                'ref_id' => $params['suspension_log_id'],
            ],
            'order' => 'id desc',
        ]);

        if (empty($reinstatementInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $audit_params['id_union']     = 'id_' . $reinstatementInfo->id;
        $audit_params['staff_id']     = $suspension_info['staff_info_id'];
        $audit_params['type']         = ApprovalEnums::APPROVAL_TYPE_HR_RESUME_EMPLOYMENT;
        $audit_params['locale']       = self::$language;
        $audit_params['date_created'] = $suspension_info['created_at'] ?? '';//待确认
        //获取审批列表
        $detail = (new WorkflowService())->getAuditDetailV2($audit_params,
            $params['user_info']) ?: [];

        return $detail;
    }

    /**
     * 获取停职签字配置信息
     * @param array $params
     * @return mixed
     */
    public function signConfigList($params = [])
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $total = $this->getSignConfigQuery($params, true);
        $list  = $this->getSignConfigQuery($params);
        if ($list) {
            $list = $this->formatSignList($list);
        }

        $data['total'] = !empty($total) ? intval($total['count']) : 0;
        $data['list']  = $list;

        return $data;
    }

    public function getSignConfigQuery($params, $isCount = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ssc' => SuspensionSignConfigModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'hsi.staff_info_id = ssc.staff_info_id', 'hsi');
        $builder->where('is_deleted = :is_deleted:', ['is_deleted' => SuspensionSignConfigModel::IS_DELETED_NO]);

        if ($isCount) {
            $builder->columns('count(*) as count');
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.node_department_id',
            'hsi.job_title',
            'sign_url',
            'ssc.id as sign_id',
            'ssc.operator_id',
            'ssc.operator_name',
            "DATE_FORMAT(CONVERT_TZ(ssc.updated_at, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as updated_at",
        ]);

        if (empty($params['is_download'])) {
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));
        }
        $builder->orderBy('ssc.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 格式化停职签字配置
     * @param $list
     * @return array
     */
    public function formatSignList($list)
    {
        $nodeDepartmentIds = array_values(array_unique(array_column($list, 'node_department_id')));
        $jobTitleId        = array_values(array_unique(array_column($list, 'job_title')));

        $jobTitleInfo       = (new DepartmentService())->getJobList('', $jobTitleId, true);
        $nodeDepartmentInfo = (new DepartmentService())->getDepartmentInfo($nodeDepartmentIds);

        $jobTitleInfoToId = [];
        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }
        $nodeDepartmentInfoToId = [];
        if ($nodeDepartmentInfo) {
            $nodeDepartmentInfoToId = array_column($nodeDepartmentInfo, 'name', 'id');
        }

        $result = [];
        foreach ($list as $oneData) {
            $staffInfo['staff_info_id']   = $oneData['staff_info_id'];
            $staffInfo['name']            = $oneData['name'];
            $staffInfo['department_name'] = $nodeDepartmentInfoToId[$oneData['node_department_id']] ?? '';
            $staffInfo['job_title']       = $jobTitleInfoToId[$oneData['job_title']] ?? '';
            $staffInfo['sign_url']        = $oneData['sign_url'];
            $staffInfo['operator_name']   = !empty($oneData['operator_id']) ? $oneData['operator_name'] . '(' . $oneData['operator_id'] . ')' : '';
            $staffInfo['updated_at']      = $oneData['updated_at'];
            $staffInfo['sign_id']         = $oneData['sign_id'];
            $result[]                     = $staffInfo;
        }
        return $result;
    }

    /**
     * 获取详情
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function signConfigDetail($params)
    {
        $signInfo = SuspensionSignConfigModel::findFirst([
            'columns'    => 'id as sign_id,staff_info_id,sign_url',
            'conditions' => ' id = :sign_id: ',
            'bind'       => [
                'sign_id' => $params['sign_id'],
            ],
        ]);
        if (empty($signInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }
        $signInfo = $signInfo->toArray();

        $staffInfo = (new WarningService())->getStaffInfoOne($signInfo['staff_info_id']);

        $signInfo['staff_info_id']   = $staffInfo['staff_info_id'] ?? '';
        $signInfo['name']            = $staffInfo['name'] ?? '';
        $signInfo['department_name'] = $staffInfo['department_name'] ?? '';
        $signInfo['job_title']       = $staffInfo['job_title_name'] ?? '';

        return $signInfo;
    }

    /**
     * 获取员工信息
     * @param $params
     * @return mixed
     */
    public function getStaffListInfo($params)
    {
        return (new WarningService())->getStaffListInfo($params);
    }

    /**
     * 签名配置-添加
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function signConfigAdd($params)
    {
        $signInfo = SuspensionSignConfigModel::findFirst([
            'columns'    => 'id',
            'conditions' => ' staff_info_id = :staff_info_id: and is_deleted = :is_deleted:',
            'bind'       => [
                'staff_info_id' => $params['staff_info_id'],
                'is_deleted' => SuspensionSignConfigModel::IS_DELETED_NO,
            ],
        ]);
        if (!empty($signInfo)) {
            throw new ValidationException(self::$t->_('isset_staff_id'));
        }

        $db = $this->getDI()->get('db_backyard');

        $data['staff_info_id'] = $params['staff_info_id'];
        $data['sign_url']      = $params['sign_url'];
        $data['operator_id']   = $params['operator_id'];
        $data['operator_name'] = $params['operator_name'];

        $res = $db->insertAsDict('suspension_sign_config', $data);
        if (!$res) {
            throw new ValidationException(self::$t->_('save_error'));
        }

        return [];
    }

    /**
     * 签名配置-编辑
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function signConfigEdit($params)
    {
        $signInfo = SuspensionSignConfigModel::findFirst([
            'columns'    => 'id',
            'conditions' => ' id = :sign_id: ',
            'bind'       => [
                'sign_id' => $params['sign_id'],
            ],
        ]);
        if (empty($signInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $db = $this->getDI()->get('db_backyard');

        $data['sign_url']      = $params['sign_url'];
        $data['operator_id']   = $params['operator_id'];
        $data['operator_name'] = $params['operator_name'];

        $res = $db->updateAsDict("suspension_sign_config", $data, ["conditions" => 'id = ' . intval($signInfo['id'])]);

        if (!$res) {
            throw new ValidationException(self::$t->_('save_error'));
        }

        return [];
    }

    /**
     * 签名配置 -- 删除
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function signConfigDelete($params)
    {
        $signInfo = SuspensionSignConfigModel::findFirst([
            'columns'    => 'id',
            'conditions' => ' id = :sign_id: ',
            'bind'       => [
                'sign_id' => $params['sign_id'],
            ],
        ]);
        if (empty($signInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $db = $this->getDI()->get('db_backyard');

        $data['is_deleted']    = SuspensionSignConfigModel::IS_DELETED_YES;
        $data['operator_id']   = $params['operator_id'];
        $data['operator_name'] = $params['operator_name'];
        $res                   = $db->updateAsDict("suspension_sign_config", $data,
            ["conditions" => 'id = ' . intval($signInfo['id'])]);

        if (!$res) {
            throw new ValidationException(self::$t->_('delete_error'));
        }

        return [];
    }

    /**
     * ph 预览文件
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function addFilePreview($params)
    {
        $suspensionInfo = $this->getSuspensionInfo($params['suspension_manage_log_id']);
        if (empty($suspensionInfo)) {
            throw new ValidationException('suspension info not found id:' . $params['suspension_manage_log_id']);
        }
        $suspensionStaffInfo = $this->getStaffInfo($suspensionInfo['staff_info_id']);
        if (empty($suspensionStaffInfo)) {
            throw new ValidationException('staff info not found staff_id:' . $suspensionInfo['staff_info_id']);
        }

        $tmpPath = $this->getFilePath($params['type']);

        if (empty($tmpPath)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $signStaffIds[] = $params['operator_id'];
        if (!empty($params['sign_staff_id'])) {
            $signStaffIds[] = $params['sign_staff_id'];
        }

        $configInfo          = $this->getSignConfigInfo([$params['sign_staff_id'], $params['operator_id']]);
        $configInfoToStaffId = array_column($configInfo, NULL, 'staff_info_id');
        if (empty($configInfoToStaffId[$params['operator_id']])) {
            throw new ValidationException(self::$t->_('operator_sign_not_found'));
        }

        if (empty($configInfoToStaffId[$params['sign_staff_id']])) {
            throw new ValidationException(self::$t->_('select_sign_not_found'));
        }

        $companyConfigInfo = (new \App\Modules\Ph\Services\CertificateService())->getCompanyConfigInfo(['staff_info_id' => $suspensionInfo['staff_info_id']]);

        [$header, $footer] = $this->getAlowHeaderFooter($companyConfigInfo);


        $pdfTempUrl = $this->getPdfTemp($tmpPath);

        $img_data = [
            ['name' => 'operator_sign_img', 'url' => $configInfoToStaffId[$params['operator_id']]['sign_url']],
            ['name' => 'select_sign_img',   'url' => $configInfoToStaffId[$params['sign_staff_id']]['sign_url']],
            ['name' => 'operation_guide_img_1',   'url' =>'https://tc-static-asset-internal.flashexpress.ph/workOrder/1727231163-cfe6650ea69544cbbd6d17945d1dcc33.png'],//操作指南图片 1
            ['name' => 'operation_guide_img_2',   'url' =>'https://tc-static-asset-internal.flashexpress.ph/workOrder/1727231210-18c19275d8c54e8e92803a47d22be325.png'],//操作指南图片 2
            ['name' => 'operation_guide_img_3',   'url' =>'https://tc-static-asset-internal.flashexpress.ph/workOrder/1727231232-d46c243ed5fa4074953f8041e05c52c2.png'],//操作指南图片 3
            ['name' => 'operation_guide_img_4',   'url' =>'https://tc-static-asset-internal.flashexpress.ph/workOrder/1727231258-617056fab7594655aee415f27a7370ae.png'],//操作指南图片 4
        ];

        $data = $suspensionInfo['stop_duty_reason'] == StaffEnums::STOP_DUTY_REASON_NOT_COLLECTION ? $this->getNotCollectionData($params) : $this->getAbsenteeismData($params);
        $data['company_name'] = $companyConfigInfo['company_name'];

        $pdfFileName = $suspensionStaffInfo['staff_info_id'] . '_' . $suspensionStaffInfo['name'];
        $pdfFileName = str_replace('/', '', $pdfFileName);

        $fileData = $this->getEmailContent($params['type'], $pdfFileName, '');

        //PH 离职文件 页眉
        $pdfHeaderFooterSetting = [
            'format'              => 'a4',
            'displayHeaderFooter' => true,
        ];
        $pdfHeaderFooterSetting['headerTemplate'] = $header;
        $pdfHeaderFooterSetting['footerTemplate'] = $footer;
        //未回公款页面
        if($suspensionInfo['stop_duty_reason'] == StaffEnums::STOP_DUTY_REASON_NOT_COLLECTION) {
            $pdfHeaderFooterSetting['headerTemplate'] = $this->getCollectionHeader($companyConfigInfo);
            $pdfHeaderFooterSetting['footerTemplate'] = '<div></div>';
            $pdfHeaderFooterSetting['printBackground'] = true;
        }

        $pdf_file_data = (new FormPdfServer())->getInstance()->generatePdf($pdfTempUrl, $data, $img_data, $pdfHeaderFooterSetting, $fileData['title'], 'attchment');

        //生产pdf失败
        if (empty($pdf_file_data['object_url'])) {
            $this->logger->write_log("suspension 生成pdf失败 suspension_manage_log_id：" . $params['suspension_manage_log_id'] . 'result:' . json_encode($pdf_file_data),
                'notice');
            throw new ValidationException(self::$t->_('create_file_fail'));
        }
        $file_url = $pdf_file_data['object_url'] ?? '';

        $params['file_url']  = $file_url;
        $all_list            = SuspensionEnums::$suspension_file_type_list + SuspensionEnums::$not_refunded_file_type_list;
        $params['file_type'] = self::$t->_($all_list[$params['type']]);

        return $params;
    }


    /**
     * 获取alow pdf 文件 页眉页脚
     * @param $companyConfigInfo
     * @return array
     */
    public function getAlowHeaderFooter($companyConfigInfo)
    {
        $header = '
    <div style="width: 100%;margin: 0 12mm;box-sizing: border-box;">
        <table
        style="line-height: 6mm;width: 100%;font-weight: 700;font-size: 4mm;font-family: sans-serif; background-size: 38mm;">
        <tr>
            <td>
            <div>' . $companyConfigInfo['company_name'] . '</div>
            </td>
            <td style="height: 7mm;text-align: right;">
            <img style="height: 7mm; width: auto;object-fit: cover;"
                src="' . $companyConfigInfo['company_logo_url_base64']. '" />
            </td>
        </tr>
        </table>
    </div>';

        $footer = '<div style="width: 210mm; font-weight: bold; font-size: 8px; text-align: center">
  <div>' . $companyConfigInfo['company_address'] . '</div>
  <div>Tel: ' . $companyConfigInfo['company_phone'] . ' Website: <a>' . $companyConfigInfo['company_web_url'] . '</a></div>
</div>';

        return [$header, $footer];
    }


    /**
     * 获取未回公款 pdf 文件 页眉页脚
     * @param $companyConfigInfo
     * @return string
     */
    public function getCollectionHeader($companyConfigInfo)
    {
        $header = '
    <div style="width: 100%;margin: 0 10mm;box-sizing: border-box;">
        <table
        style="line-height: 6mm;width: 100%;font-weight: 700;font-size: 4mm;font-family: sans-serif; background-size: 38mm;">
        <tr>
            <td>
            <div>' . $companyConfigInfo['company_name'] . '</div>
            </td>
            <td style="height: 8mm;text-align: right;">
            <img style="height: 8mm; width: auto;object-fit: cover;"
                src="' . $companyConfigInfo['company_logo_url_base64']. '" />
            </td>
        </tr>
        </table>
    </div>';

        return $header;
    }

    /**
     * 获取停职原因：连续旷工 文件数据
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getAbsenteeismData($params)
    {
        if ($params['type'] == SuspensionEnums::FILE_TYPE_RTWN) {
            $data = $this->getFirstFileData($params);
        } else {
            $data = $this->getOtherFileData($params);
        }

        return $data;
    }


    /**
     * 获取停职原因：未回公款 文件数据
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getNotCollectionData($params)
    {
        if (in_array($params['type'], [SuspensionEnums::FILE_TYPE_DL01, SuspensionEnums::FILE_TYPE_DL02])) {
            $data = $this->getDlFileData($params);
        } else {
            $data = $this->getOtherCodFileData($params);
        }

        return $data;
    }

    /**
     * 未回公款 FILE_TYPE_DL01  FILE_TYPE_DL02 类型 文件数据
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getDlFileData($params)
    {
        $suspensionInfo = $this->getSuspensionInfo($params['suspension_manage_log_id']);
        if (empty($suspensionInfo)) {
            throw new ValidationException('suspension info not found id:' . $params['suspension_manage_log_id']);
        }
        $staffInfo = $this->getStaffInfo($suspensionInfo['staff_info_id']);
        if (empty($staffInfo)) {
            throw new ValidationException('staff info not found staff_id:' . $suspensionInfo['staff_info_id']);
        }
        $where = ['staff_info_id' => $suspensionInfo['staff_info_id'], 'business_date' => date('Y-m-d', strtotime($suspensionInfo['stop_duties_date'])), 'business_type' => HrStaffNotRefundedSuspensionTaskModel::BUSINESS_TYPE_1];
        $notCollectionInfo = HrStaffNotRefundedSuspensionTaskRepository::getInfo($where);

        $not_collection_sum = '';
        $not_collection_data = [];
        //快递员仓管未回款
        if(!empty($notCollectionInfo) && $notCollectionInfo['business_type'] == HrStaffNotRefundedSuspensionTaskModel::BUSINESS_TYPE_1) {
            $content_json = !empty($notCollectionInfo['content_json']) ? json_decode($notCollectionInfo['content_json'], true) : [];
            $not_collection_sum = !empty($content_json) ? $content_json['not_pay'] : '';
            $not_collection_data = !empty($content_json['not_pay_details']) ? $content_json['not_pay_details'] : [];
        }


        if(empty($not_collection_sum)) {
            $this->logger->notice(['suspension_not_found_data' => ['suspension_manage_log_id' => $params['suspension_manage_log_id'], 'where' => $where]]);
        }

        $this->logger->info(['suspension_found_data' => ['suspension_manage_log_id' => $params['suspension_manage_log_id'], "not_collection_where" => $where, 'not_collection_data' => $notCollectionInfo]]);

        foreach ($not_collection_data as &$oneData) {
            $oneData['amount'] = !empty($oneData['amount']) ? number_format($oneData['amount'] / 100, 2) : '';//未回款金额
        }

        $not_collection_sum = !empty($not_collection_sum) ? number_format($not_collection_sum / 100, 2) : '';//未回款金额

        //兼容老数据，没有 未回公款详情。拿总金额 和 停职日期来处理
        if(empty($not_collection_data)) {
            $dataSum['amount'] = $not_collection_sum;
            $dataSum['date']   = $suspensionInfo['stop_duties_date'];
            $not_collection_data[] = $dataSum;
        }

        $operatorInfo = $this->getStaffInfo($params['operator_id']);

        $jobTitleInfo = (new DepartmentService())->getJobList('', [$staffInfo['job_title'],$operatorInfo['job_title']], true);

        $jobTitleInfoToId = [];
        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        $data['date']                = date('Y-m-d');//签发日期
        $data['name']                = $staffInfo['name'];//停职员工 姓名
        $data['address']             = $this->getStaffAddress($staffInfo['staff_info_id']);//停职员工 姓名
        $data['job_title']           = $jobTitleInfoToId[$staffInfo['job_title']] ?? "";//停职员工职位
        $data['not_collection_data'] = $not_collection_data;//未回款金额
        $data['operator_name']       = $operatorInfo['name'];//发送人 姓名
        $data['operator_job_title']  = $jobTitleInfoToId[$operatorInfo['job_title']] ?? '';//发送人 职位

        return $data;
    }

    /**
     * 未回公款：其他 文件类型数据
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getOtherCodFileData($params)
    {
        $suspensionInfo = $this->getSuspensionInfo($params['suspension_manage_log_id']);
        if (empty($suspensionInfo)) {
            throw new ValidationException('suspension info not found id:' . $params['suspension_manage_log_id']);
        }
        $staffsInfo = HrStaffInfoRepository::getHrStaffByIds([$suspensionInfo['staff_info_id'], $params['operator_id'], $params['sign_staff_id']]);
        $staffInfo = $staffsInfo[$suspensionInfo['staff_info_id']] ?? [];
        if (empty($staffInfo)) {
            throw new ValidationException('staff info not found staff_id:' . $suspensionInfo['operator_id']);
        }

        $where = ['staff_info_id' => $suspensionInfo['staff_info_id'], 'business_date' => date('Y-m-d', strtotime($suspensionInfo['stop_duties_date'])), 'business_type' => HrStaffNotRefundedSuspensionTaskModel::BUSINESS_TYPE_1];
        $notCollectionInfo = HrStaffNotRefundedSuspensionTaskRepository::getInfo($where);

        $not_collection_sum = '';
        $not_collection_data = [];
        //快递员仓管未回款
        if(!empty($notCollectionInfo) && $notCollectionInfo['business_type'] == HrStaffNotRefundedSuspensionTaskModel::BUSINESS_TYPE_1) {
            $content_json = !empty($notCollectionInfo['content_json']) ? json_decode($notCollectionInfo['content_json'], true) : [];
            $not_collection_sum = !empty($content_json) ? $content_json['not_pay'] : '';
            $not_collection_data = !empty($content_json['not_pay_details']) ? $content_json['not_pay_details'] : [];
        }

        if(empty($not_collection_sum)) {
            $this->logger->notice(['suspension_not_found_data' => ['suspension_manage_log_id' => $params['suspension_manage_log_id'], 'where' => $where]]);
        }

        $this->logger->info(['suspension_found_data' => ['suspension_manage_log_id' => $params['suspension_manage_log_id'], "not_collection_where" => $where, 'not_collection_data' => $notCollectionInfo]]);

        $not_collection_data_detail = [];
        foreach ($not_collection_data as $oneData) {
            $oneData['amount'] = !empty($oneData['amount']) ? number_format($oneData['amount'] / 100, 2) : '';//未回款金额
            $not_collection_data_detail[] =  $oneData['amount'] . ' on ' . $oneData['date'];
        }

        $not_collection_sum = !empty($not_collection_sum) ? number_format($not_collection_sum / 100, 2) : '';//未回款金额

        //兼容老数据，没有 未回公款详情。拿总金额 和 停职日期来处理
        if(empty($not_collection_data)) {
            $dataSum['amount'] = $not_collection_sum;
            $dataSum['date']   = $suspensionInfo['stop_duties_date'];
            $not_collection_data_detail[] = $dataSum['amount'] . ' on ' . $dataSum['date'];
        }

        $signStaffInfo = $staffsInfo[$params['sign_staff_id']] ?? [];

        $operatorInfo = $staffsInfo[$params['operator_id']] ?? [];

        $jobTitleInfo = (new DepartmentService())->getJobList('', [$staffInfo['job_title'],$operatorInfo['job_title'], $signStaffInfo['job_title']], true);

        $jobTitleInfoToId = [];
        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        $data['date']                = date('Y-m-d');//签发日期
        $data['name']                = $staffInfo['name'];//停职员工 姓名
        $data['job_title']           = $jobTitleInfoToId[$staffInfo['job_title']] ?? "";//停职员工职位
        $data['not_collection_data'] = !empty($not_collection_data_detail) ? implode(',', $not_collection_data_detail) : '';//未回款金额 数据
        $data['operator_name']       = $operatorInfo['name'] ?? '';//发送人 姓名
        $data['operator_job_title']  = $jobTitleInfoToId[$operatorInfo['job_title']] ?? '';//发送人 职位
        $data['sign_name']           = $signStaffInfo['name'] ?? '';//签字人 姓名

        return $data;
    }

    /**
     * 获取模板地址
     * @param $type
     * @return string
     */
    public function getFilePath($type)
    {
        switch ($type) {
            case SuspensionEnums::FILE_TYPE_RTWN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_RTWN_1.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NTE_AWOL :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NTE_AWOL_2.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NTE_AWOL_COD :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NTE_AWOL_COD_3.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_AWOL_EXPLAIN_4.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_NOT_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_AWOL_NOT_EXPLAIN_5.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_COD_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_AWOL_COD_EXPLAIN_6.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_COD_NOT_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_AWOL_COD_NOT_EXPLAIN_7.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_EXONERATED_MUSHROOM_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_EXONERATED_MUSHROOM_EXPLAIN_8.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_EXONERATED_MUSHROOM_NOT_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_EXONERATED_MUSHROOM_NOT_EXPLAIN_9.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_EXONERATED_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_AWOL_EXONERATED_EXPLAIN_10.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_EXONERATED_NOT_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NOD_AWOL_EXONERATED_NOT_EXPLAIN_11.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_DL01 :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_DL01_12.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_DL02 :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_DL02_13.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NTE_COD :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NTE_COD_14.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NTE_COD_NOT_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NTE_COD_NOT_EXPLAIN_15.ftl';
                break;
            case SuspensionEnums::FILE_TYPE_NTE_COD_EXPLAIN :
                $path = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NTE_COD_EXPLAIN_16.ftl';
                break;
            default:
                $path = '';
                break;
        }

        return $path;
    }

    /**
     * 获取签名数据
     * @param $signStaffIds
     * @return array
     */
    public function getSignConfigInfo($signStaffIds)
    {
        $signInfo = SuspensionSignConfigModel::find([
            'conditions' => 'staff_info_id in ({staff_info_ids:array}) and is_deleted = :is_deleted:',
            'bind'       => [
                'staff_info_ids' => $signStaffIds,
                'is_deleted'     => SuspensionSignConfigModel::IS_DELETED_NO,
            ],
            'columns'    => 'id, staff_info_id, sign_url',
        ]);

        if (empty($signInfo)) {
            return [];
        }

        return empty($signInfo) ? [] : $signInfo->toArray();
    }

    /**
     * 获取第一个文件 数据
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getFirstFileData($params)
    {
        $suspensionInfo = $this->getSuspensionInfo($params['suspension_manage_log_id']);
        if (empty($suspensionInfo)) {
            throw new ValidationException('suspension info not found id:' . $params['suspension_manage_log_id']);
        }
        $staffInfo = $this->getStaffInfo($suspensionInfo['staff_info_id']);
        if (empty($staffInfo)) {
            throw new ValidationException('staff info not found staff_id:' . $suspensionInfo['staff_info_id']);
        }

        $bind['staff_info_id'] = $suspensionInfo['staff_info_id'];
        $bind['created_date']  = show_time_zone($suspensionInfo['created_at'], 'Y-m-d');
        $tripleToLeave         = $this->getTripleToLeaveInfoByCreated($bind);
        if (empty($tripleToLeave)) {
            $bind['suspension_manage_log_id'] = $params['suspension_manage_log_id'];
            $this->logger->write_log(" suspension 未找到旷工信息 params：" . json_encode($bind),
                'notice');
            throw new ValidationException('not found triple to leave:' . self::$t->_('data_error'));
        }

        $stop_begin_date = '';
        if(!empty($tripleToLeave['stop_date_detail'])) {
            $stop_date_detail = json_decode($tripleToLeave['stop_date_detail'], true);
            $stop_begin_date  = $stop_date_detail[0];
        }

        if (empty($stop_begin_date)) {
            $bind['suspension_manage_log_id'] = $params['suspension_manage_log_id'];
            $this->logger->write_log(" suspension 未找到 旷工开始日期 stop_date_detail params：" . json_encode($bind),
                'notice');
            throw new ValidationException('not found triple to leave stop begin date:' . self::$t->_('data_error'));
        }

        $settingEnvService = new SettingEnvService();
        $staff_absent_days = $settingEnvService->getSetVal('staff_absent_days');//连续旷工N天数

        $operatorInfo = $this->getStaffInfo($params['operator_id']);


        $jobTitleInfo = (new DepartmentService())->getJobList('', [$staffInfo['job_title'],$operatorInfo['job_title']], true);

        $jobTitleInfoToId = [];
        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        $data['date']               = date('Y-m-d');//签发日期
        $data['name']               = $staffInfo['name'];//停职员工 姓名
        $data['address']            = $this->getStaffAddress($staffInfo['staff_info_id']);//停职员工 姓名
        $data['job_title']          = $jobTitleInfoToId[$staffInfo['job_title']] ?? "";//停职员工职位
        $data['staff_absent_days']  = GlobalEnums::NUMBER[$staff_absent_days] . '(' . $staff_absent_days . ')';//连续旷工N天
        $data['stop_begin_date']    = $stop_begin_date;//旷工开始日期
        $data['hire_date']          = date('Y-m-d', strtotime($staffInfo['hire_date']));//入职日期
        $data['date_plus']          = date('Y-m-d', strtotime("+1 day"));//签发日期+1天
        $data['operator_name']      = $operatorInfo['name'];//发送人 姓名
        $data['operator_job_title'] = $jobTitleInfoToId[$operatorInfo['job_title']] ?? '';//发送人 职位

        return $data;
    }

    /**
     * 获取其他类型 文件数据
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getOtherFileData($params)
    {
        $suspensionInfo = $this->getSuspensionInfo($params['suspension_manage_log_id']);
        if (empty($suspensionInfo)) {
            throw new ValidationException('suspension info not found id:' . $params['suspension_manage_log_id']);
        }
        $staffInfo = $this->getStaffInfo($suspensionInfo['staff_info_id']);
        if (empty($staffInfo)) {
            throw new ValidationException('staff info not found staff_id:' . $suspensionInfo['staff_info_id']);
        }

        $settingEnvService = new SettingEnvService();
        $staff_absent_days = $settingEnvService->getSetVal('staff_absent_days');//连续旷工N天数

        $operatorInfo  = $this->getStaffInfo($params['operator_id']);
        $signStaffInfo = $this->getStaffInfo($params['sign_staff_id']);


        $jobTitleInfo = (new DepartmentService())->getJobList('', [$operatorInfo['job_title'], $staffInfo['job_title']],
            true);

        $jobTitleInfoToId = [];
        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        $data['date']               = date('Y-m-d');//签发日期
        $data['name']               = $staffInfo['name'];//停职员工 姓名
        $data['job_title']          = $jobTitleInfoToId[$staffInfo['job_title']] ?? "";//停职员工职位
        $data['staff_absent_days']  = GlobalEnums::NUMBER[$staff_absent_days] . '(' . $staff_absent_days . ')';//连续旷工N天
        $data['operator_name']      = $operatorInfo['name'];//发送人 姓名
        $data['operator_job_title'] = $jobTitleInfoToId[$operatorInfo['job_title']] ?? '';//发送人 职位
        $data['sign_name']          = $signStaffInfo['name'];//签字人姓名

        return $data;
    }

    /**
     * 获取员工地址
     * @param $staffId
     * @return string
     */
    public function getStaffAddress($staffId)
    {
        $staffService = new StaffService();

        $staffItems = $staffService->getHrStaffItems($staffId);

        //{门牌号} {村号} {村庄} {巷} {街道} {乡} {市} {省} {邮编}
        $otherItems['REGISTER_HOUSE_NUM']   = $staffItems['REGISTER_HOUSE_NUM'] ?? '';//{门牌号}
        $otherItems['REGISTER_VILLAGE_NUM'] = $staffItems['REGISTER_VILLAGE_NUM'] ?? '';//{村号}
        $otherItems['REGISTER_VILLAGE']     = $staffItems['REGISTER_VILLAGE'] ?? '';//{村庄}
        $otherItems['REGISTER_ALLEY']       = $staffItems['REGISTER_ALLEY'] ?? '';//{巷}
        $otherItems['REGISTER_STREET']      = $staffItems['REGISTER_STREET'] ?? '';//{街道}

        return $staffService->getStaffAddress($staffItems['REGISTER_PROVINCE']??'', $staffItems['REGISTER_CITY']??'',
            $staffItems['REGISTER_DISTRICT']??'', $otherItems??[], $staffItems['REGISTER_POSTCODES']??'');
    }

    /**
     * 获取旷工信息
     * @param $staffId
     * @return array
     */
    public function getTripleToLeaveInfoByCreated($params)
    {
        $data = TripleToLeaveModel::findFirst([
            'columns'    => 'id,staff_info_id,stop_begin_date,stop_date_detail',
            'conditions' => 'staff_info_id = :staff_id: and type = :type: and created_at like :created_at:',
            'bind'       => [
                'staff_id'   => $params['staff_info_id'],
                'created_at' => $params['created_date'] . '%',
                'type'       => TripleToLeaveModel::TYPE_CONTINUE_ABSENT,
            ],
        ]);

        if (empty($data)) {
            return [];
        }

        return $data->toArray();
    }

    /**
     * 发送邮件
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function addFileSend($params)
    {
        $suspensionInfo = $this->getSuspensionInfo($params['suspension_manage_log_id']);
        if (empty($suspensionInfo)) {
            throw new ValidationException('suspension info not found id:' . $params['suspension_manage_log_id']);
        }
        $staffInfo = $this->getStaffInfo($suspensionInfo['staff_info_id']);
        if (empty($staffInfo)) {
            throw new ValidationException('staff info not found staff_id:' . $suspensionInfo['staff_info_id']);
        }

        $configInfo          = $this->getSignConfigInfo([$params['sign_staff_id'], $params['operator_id']]);
        $configInfoToStaffId = array_column($configInfo, NULL, 'staff_info_id');
        if (empty($configInfoToStaffId[$params['operator_id']])) {
            throw new ValidationException(self::$t->_('operator_sign_not_found'));
        }

        if (empty($configInfoToStaffId[$params['sign_staff_id']])) {
            throw new ValidationException(self::$t->_('select_sign_not_found'));
        }

        $pdfFileName = $staffInfo['staff_info_id'] . '_' . $staffInfo['name'];
        $pdfFileName = str_replace('/', '', $pdfFileName);

        $emailContent = $this->getEmailContent($params['type'], $pdfFileName, date('Y-m-d', strtotime("+1 day")));

        //为了给文件命名
        $fileUrl = $this->fileDownload($params['file_url'], '', $emailContent['title'] . '.pdf');

        $data['title']     = $emailContent['title'];
        $data['content']   = $emailContent['content'];
        $data['email']     = $params['email'] ?? '';
        $data['cc_email']  = $params['cc_email'] ?? [];

        $data['file_url'] = $fileUrl;

        if ($this->sendPdfEmail($data)) {
            $insertData['suspension_manage_log_id'] = $params['suspension_manage_log_id'];
            $insertData['staff_info_id']            = $staffInfo['staff_info_id'];
            $insertData['type']                     = $params['type'];
            $insertData['file_url']                 = $params['file_url'];
            $insertData['sign_staff_id']            = $configInfoToStaffId[$params['sign_staff_id']]['id'];//签名id
            $insertData['operate_id']               = $configInfoToStaffId[$params['operator_id']]['id'];//签名id
            $insertData['operator_staff_id']         = $params['operator_id'];//操作人工号
            $insertData['operator_name']             = $params['operator_name'];//操作人姓名

            $db_by = $this->getDI()->get('db_backyard');
            $db_by->insertAsDict('suspension_file', $insertData);

            return true;
        }

        throw new ValidationException(self::$t->_('send_email_fail'));
    }

    /**
     * 获取邮件内容
     * @param $type
     * @param $titleName
     * @param $sendDate
     * @return mixed
     */
    public function getEmailContent($type, $titleName, $sendDate)
    {
        $today = date('Y m d');
        switch ($type) {
            case SuspensionEnums::FILE_TYPE_RTWN :
                $title   = self::$t->_('awol_file_title_1_v2', ['name' => $titleName, 'date' => $today]);
                $content = self::$t->_('awol_file_content_1', ['date' => $sendDate]);
                break;

            case SuspensionEnums::FILE_TYPE_NTE_AWOL :
            case SuspensionEnums::FILE_TYPE_NTE_AWOL_COD :
                $title   = self::$t->_('awol_file_title_2_v2', ['name' => $titleName, 'date' => $today]);
                $content = self::$t->_('awol_file_content_2');
                break;

            case SuspensionEnums::FILE_TYPE_NOD_AWOL_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_NOT_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_COD_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_COD_NOT_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NOD_EXONERATED_MUSHROOM_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NOD_EXONERATED_MUSHROOM_NOT_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_EXONERATED_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NOD_AWOL_EXONERATED_NOT_EXPLAIN :
                $title   = self::$t->_('awol_file_title_3_v2', ['name' => $titleName, 'date' => $today]);
                $content = self::$t->_('awol_file_content_3');
                break;

            //未回公款停职类型
            case SuspensionEnums::FILE_TYPE_DL01 :
                $title   = self::$t->_('file_title_dl_1_v2', ['name' => $titleName, 'date' => $today]);
                $content = self::$t->_('file_content_dl_1_v2');
                break;
            case SuspensionEnums::FILE_TYPE_DL02 :
                $title   = self::$t->_('file_title_dl_2_v2', ['name' => $titleName, 'date' => $today]);
                $content = self::$t->_('file_content_dl_2_v2');
                break;

            case SuspensionEnums::FILE_TYPE_NTE_COD :
                $title   = self::$t->_('file_title_net_cod_v2', ['name' => $titleName, 'date' => $today]);
                $content = self::$t->_('file_content_net_cod');
                break;

            case SuspensionEnums::FILE_TYPE_NTE_COD_EXPLAIN :
            case SuspensionEnums::FILE_TYPE_NTE_COD_NOT_EXPLAIN :

                $title   = self::$t->_('file_title_net_cod_exp_v2', ['name' => $titleName, 'date' => $today]);
                $content = self::$t->_('file_content_net_cod_exp');
                break;
            default:
                $title   = '';
                $content = '';
                break;
        }
        $data['title']   = $title;
        $data['content'] = $content;

        return $data;
    }

    /**
     * PH发送邮箱
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function sendPdfEmail($params)
    {
        try {
            $staffsEmail = $params['email'];
            //发送邮件
            $title   = $params['title'];
            $content = $params['content'];
            if (empty($staffsEmail)) {
                throw new ValidationException(self::$t->_('send_email_not_empty'));
            }

            $sendResult = \App\Library\BiMail::send($staffsEmail, $title, $content, [$params['file_url']],
                $params['cc_email']);
            if (!$sendResult) {
                throw new ValidationException(self::$t->_('send_email_fail'));
            }
            return true;
        } catch (Exception $e) {
            $this->logger->notice("suspension_send 邮件发送失败suspension__id:" . $params['suspension_manage_log_id'] . '' . ",接收人邮箱异常email:" . $staffsEmail . ', message:' . $e->getMessage());
            throw new ValidationException(self::$t->_('server_error'));
        }
    }

    /**
     * 发送指定日期AWOL发送情况统计
     * @param $params
     * @return bool
     * @throws \PHPMailer\PHPMailer\Exception
     */
    public function awolStatisticsSendEmail($params): bool
    {
        $begin_date = $params['begin_date'];
        $end_date = $params['end_date'];
        [$department_ids, $email] = $this->getAWOLStatisticsSendEmailEnv();
        if(empty($department_ids) || empty($email)) {
            $this->logger->notice([
                'function' => 'awolStatisticsSendEmail',
                'params' => $params,
                'department_ids' => $department_ids,
                'email' => $email,
                'message' => '配置项异常',
            ]);
            return false;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            's_file.id',
            's_file.suspension_manage_log_id',
            's_file.staff_info_id',
            's_file.type',
            's_file.file_url',
            's_file.created_at',
            's_file.updated_at',
            'staff.name as staff_name',
            'staff.node_department_id',
            'staff.job_title',
            'staff.sys_store_id',
        ]);
        $builder->from(['s_file' => SuspensionFileModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 's_file.staff_info_id = staff.staff_info_id', 'staff');
        $builder->andWhere('s_file.created_at >= :begin_date:', ['begin_date' => $begin_date]);
        $builder->andWhere('s_file.created_at < :end_date:', ['end_date' => $end_date]);
        $builder->inWhere('staff.node_department_id', $department_ids);
        $list = $builder->getQuery()->execute()->toArray();

        if(empty($list)) {
            $this->logger->info([
                'function' => 'awolStatisticsSendEmail',
                'params' => $params,
                'department_ids' => $department_ids,
                'email' => $email,
                'message' => '未找到数据',
            ]);
            return false;
        }

        $excel_data = $this->awolStatisticsFormatList($list);

        $header = [
            self::$t->_('staff_info_id'),        //工号
            self::$t->_('name'),                 //姓名
            self::$t->_('job_title'),            //职位
            self::$t->_('node_department_name'), //所属部门
            self::$t->_('sys_store_name'),       //所属网点
            self::$t->_('regional'),             //所属大区
            self::$t->_('area'),                 //所属片区
            self::$t->_('suspension_file_type'), //文件类型
        ];
        $business_day = date('Y-m-d', strtotime('-1 days'));
        $file_name = get_country_code() . '_nw_awol_email_' . $business_day;
        $file_data = $this->exportExcel($header, $excel_data, $file_name);

        $email_title = get_country_code() . '-NW AWOL email summary ' . $business_day;
        $email_content = 'Dear All, Please see attached about AWOL email summary';
        $sendResult = \App\Library\BiMail::send($email, $email_title, $email_content, [$file_data['data']]);
        if (!$sendResult) {
            $this->logger->notice([
                'function' => 'awolStatisticsSendEmail',
                'params' => $params,
                'department_ids' => $department_ids,
                'email' => $email,
                'message' => '邮件发送失败',
                'send_result' => $sendResult,
            ]);
            return false;
        }
        return true;
    }

    /**
     * 格式化数据
     * @param $list
     * @return array
     */
    public function awolStatisticsFormatList($list): array
    {
        $job_title_ids  = [];
        $department_ids = [];
        $sys_store_ids  = [];
        foreach ($list as $value) {
            $job_title_ids[]  = $value['job_title'];
            $department_ids[] = $value['node_department_id'];
            $sys_store_ids[]  = $value['sys_store_id'];
        }

        $store_service = new SysStoreService();
        $job_title_list  = (new HrJobTitleService())->getJobTitleMapByIds($job_title_ids);
        $department_list = (new SysDepartmentService())->getListByIds($department_ids);
        $store_list      = $store_service->getStoreList($sys_store_ids);
        $store_list      = !empty($store_list) ? array_column($store_list, null, 'id') : [];

        $store_relation = $store_service->getStoreRelationListByStoreId($sys_store_ids);

        $region_ids  = array_column($store_relation, 'region_id');
        $piece_ids   = array_column($store_relation, 'piece_id');
        $region_list = (new SysManageRegionService())->getListByIds($region_ids);
        $piece_list  = (new SysManagePieceService())->getListByIds($piece_ids);

        $excel_data = [];
        foreach ($list as $key => $value) {
            $sys_store_id = $value['sys_store_id'];
            $job_title_name = $job_title_list[$value['job_title']] ?? '';
            $department_name = $department_list[$value['node_department_id']]['name'] ?? '';
            $store_name = $store_list[$sys_store_id]['name'] ?? '';
            $region_id = $store_relation[$sys_store_id]['region_id'] ?? '0';
            $piece_id = $store_relation[$sys_store_id]['piece_id'] ?? '0';

            $region_name = $region_list[$region_id]['name'] ?? '';
            $piece_name = $piece_list[$piece_id]['name'] ?? '';

            $awol_type = $this->getFileTypeText($value['type']);
            $excel_data[] = [
                $value['staff_info_id'],
                $value['staff_name'],
                $job_title_name,
                $department_name,
                $store_name,
                $region_name,
                $piece_name,
                $awol_type,
            ];
        }
        return $excel_data;
    }

    /**
     * 获取env配置
     * @return array
     */
    public function getAWOLStatisticsSendEmailEnv(): array
    {
        $setting_service = new SettingEnvService();

        $env_department_ids = $setting_service->getSetVal('awol_statistics_send_email_department_ids', ',');

        $department_ids = [];
        foreach ($env_department_ids as $value) {
            $ids            = (new SysDepartmentService())->getDepartmentAndSubDepartmentIds($value);
            $department_ids = array_merge($department_ids, $ids);
        }

        $email = $setting_service->getSetVal('awol_statistics_send_email_address', ',');

        return [$department_ids, $email];
    }


    //待处理恢复在职
    public function getWaitReinstatementList(): array
    {
        $list = ReinstatementRequestModel::find([
            'columns'=>'staff_info_id',
            'conditions'=>'state in (1,2) and handled = 0 ',
        ])->toArray();
        if(empty($list)){
            return [];
        }
        return array_column($list,'staff_info_id');
    }


    /**
     * 停职记录-导出明细
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function handleDetailExport($params)
    {
        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "staff_suspension_export" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "export_detail";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = show_time_zone(gmdate('Y-m-d H:i:s', time()), 'Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        return $hcmExcelTaskId;
    }

    //导出明细用 各国不同
    public function getDetailListHeader($param)
    {
        $columns = [
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.hire_type',
            'hsi.state',
            'hsi.node_department_id',
            'hsi.job_title',
            'hsi.sys_store_id',
            'hsi.hire_date',
            'hsi.wait_leave_state',
            'sml.stop_duties_date',
            'sml.stop_duty_reason',
            'sml.created_at as log_create_time',
            'sml.operate_id as log_operate_id',
            'ohr.name as log_operate_name',
            'sml.id as suspension_log_id',
        ];

        //员工信息 取实时 员工表 离职申请表 离职记录表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['sml' => SuspensionManageLogModel::class]);
        $builder->join(HrStaffInfoModel::class, 'sml.staff_info_id = hsi.staff_info_id', 'hsi');
        $builder->leftJoin(HrStaffInfoModel::class, 'sml.operate_id = ohr.staff_info_id', 'ohr');
        $builder = $this->getBuilderWhere($builder, $param);
        $builder->columns($columns);

        $list = $builder->getQuery()->execute()->toArray();

        $header = [
            self::$t->_('staff_info_id'), //工号
            self::$t->_('name'), //姓名
            self::$t->_('hire_type'), //雇佣类型
            self::$t->_('staff_state'), //在职状态
            self::$t->_('node_department_name'),  //所属部门
            self::$t->_('job_title'), //职位
            self::$t->_('sys_store_name'), //所属网点
            self::$t->_('regional'), //所属大区
            self::$t->_('area'), //所属片区
            self::$t->_('hire_date'), //入职时间
            self::$t->_('stop_duties_date'), //停职日期
            self::$t->_('stop_duty_reason'), //停职原因
            self::$t->_('stop_duty_create_time'), //停职记录创建时间
            self::$t->_('creator'), //创建人
        ];

        if (empty($list)) {
            return [[], $header];
        }
        $this->getListName($list);
        $addHour  = $this->config->application->add_hour;
        $data = [];
        foreach ($list as $li) {
            $row   = [];
            $state = $li['state'];
            if ($li['state'] == HrStaffInfoModel::STATE_ON_JOB && $li['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
            }
            $stop_duty_reason = $li['stop_duty_reason'] == 6 ? 'stay_away_from_work' : 'stop_duty_reason_' . $li['stop_duty_reason'];
            //离职记录操作人
            $logOperateName = empty($li['log_operate_name']) ? '' : "{$li['log_operate_name']}({$li['log_operate_id']})";

            $row[]  = $li['staff_info_id'];
            $row[]  = $li['name'];
            $row[]  = !empty($li['hire_type']) ? static::$t->_('hire_type_' . $li['hire_type']) : '';
            $row[]  = self::$t->_(Enums::$hris_working_state[$state]) ?? '';
            $row[]  = $this->departmentInfo[$li['node_department_id']] ?? '';
            $row[]  = $this->jobInfo[$li['job_title']] ?? '';
            $row[]  = $this->regionPiece[$li['sys_store_id']]['store_name'] ?? '';
            $row[]  = $this->regionPiece[$li['sys_store_id']]['region_name'] ?? '';
            $row[]  = $this->regionPiece[$li['sys_store_id']]['piece_name'] ?? '';
            $row[]  = date('Y-m-d', strtotime($li['hire_date']));
            $row[]  = date('Y-m-d', strtotime($li['stop_duties_date']));
            $row[]  = !empty($li['stop_duty_reason']) ? static::$t->_($stop_duty_reason) : '';
            $row[]  = date('Y-m-d H:i:s', strtotime($li['log_create_time']) + ($addHour * 3600));
            $row[]  = in_array($li['log_operate_id'],[-1,10000]) ? 'System' : $logOperateName;
            $data[] = $row;
        }

        return [$data, $header];
    }

    //列表回显用的名字
    public function getListName($list)
    {
        //大区片区 取关联 HrOrganizationDepartmentStoreRelationModel 总部为空
        $storeIds    = array_column($list, 'sys_store_id');
        $storeIds    = array_values(array_unique(array_diff($storeIds, ['-1'])));
        $regionPiece = [];
        if (!empty($storeIds)) {
            $regionPiece = (new SysStoreService())->getStorePieceRegion($storeIds);
        }
        $regionPiece[]     = [
            'store_id'    => '-1',
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];
        $this->regionPiece = array_column($regionPiece, null, 'store_id');

        $jobTitleInfo         = (new DepartmentService())->getJobList('', array_column($list, 'job_title'), true);
        $this->jobInfo        = empty($jobTitleInfo) ? [] : array_column($jobTitleInfo, 'name', 'id');
        $this->departmentInfo = (new DepartmentService())->getAllDepartment(array_column($list, 'node_department_id'));
    }

    //获取最新的文件 马来 菲律宾用
    public function getLastFile($logId){
        $info = SuspensionFileModel::findFirst([
            'conditions' => 'suspension_manage_log_id = :log_id:',
            'bind' => ['log_id' => $logId],
            'order'=> 'id desc',
        ]);
        if(empty($info)){
            return [];
        }

        return $info->toArray();
    }

    //获取最新的文件 马来 菲律宾用
    public function reinstatementInfo($refId){
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['rsr' => ReinstatementRequestModel::class]);
        $builder->join(HrStaffInfoModel::class, 'rsr.submitter_id = hsi.staff_info_id', 'hsi');
        $builder->columns("rsr.id,hsi.name as submit_name, rsr.state, rsr.submitter_id,
                            rsr.created_at, rsr.reason_explanation, rsr.reject_reason");
        $builder->andWhere("rsr.ref_id = :ref_id:",['ref_id' => $refId]);
        $info = $builder->getQuery()->getSingleResult();
        if(empty($info)){
            return [];
        }
        return $info->toArray();
    }

    /**
     * 查找最新一条申请记录
     * @param $ref_ids
     * @return array
     */
    public function getReinstatementRequestList($ref_ids): array
    {
        if(empty($ref_ids)) {
            return [];
        }
        $list = ReinstatementRequestModel::find([
            'conditions' => 'ref_id in ({ref_ids:array}) and is_new = :is_new:',
            'bind'       => [
                'ref_ids' => $ref_ids,
                'is_new' => ReinstatementRequestModel::IS_NEW_YES,
            ],
        ])->toArray();
        return array_column($list, null, 'ref_id');
    }

    /**
     * 停职申请-列表
     * @param $params
     * @return mixed
     */
    public function auditList($params)
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 100) ? 100 : $params['page_size'];

        $data['total'] = $this->getAuditCountQuery($params);
        $data['list']  = $this->getAuditListQuery($params);

        return $data;
    }

    /**
     * 获取条数
     * @param $params
     * @return int
     */
    public function getAuditCountQuery($params)
    {
        $builder = $this->modelsManager->createBuilder();

        $builder->from(['sa' => SuspensionAuditModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'sa.staff_info_id = hsi.staff_info_id',
            'hsi');

        $builder = $this->getAuditBuilderWhere($builder, $params);
        $builder->columns('count(*) as count');
        $total = $builder->getQuery()->getSingleResult()->toArray();

        return !empty($total) ? intval($total['count']) : 0;
    }

    /**
     * 获取列表数据
     * @param $params
     * @param $isFormat
     * @return array
     */
    public function getAuditListQuery($params, $isFormat = true)
    {
        $columns = [
            'sa.id',
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.job_title',
            'hsi.node_department_id',
            'hsi.sys_store_id',
            'hsi.state',
            'hsi.wait_leave_state',
            'sa.serial_no',
            'sa.audit_state',
            'sa.status',
            'sa.apply_staff_info_id',
            'sa.apply_name',
            'sa.stop_duties_date',
            'sa.created_at',
            'sa.last_audit_approval_id',
            'sa.last_audit_approval_name',
            'sa.last_audit_approval_name',
            'sa.audit_time',
            'sa.hold_type',
            'sa.hold_reason',
            'sa.month_begin',
            'sa.month_end',
            'sa.violation_content',
            'sa.violation_clause',
            'hsi.personal_email',
            'sa.send_status',
            'sa.sign_staff_id',
        ];

        $builder = $this->modelsManager->createBuilder();

        $builder->from(['sa' => SuspensionAuditModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'sa.staff_info_id = hsi.staff_info_id',
            'hsi');

        $builder = $this->getAuditBuilderWhere($builder, $params);

        $builder->columns($columns);

        $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));

        $builder->orderBy('sa.id desc');
        $list = $builder->getQuery()->execute()->toArray();
        if ($list && $isFormat) {
            $list = $this->formatAuditList($list, $params['staff_id']);
        }

        return $list;
    }

    /**
     * 构建查询
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getAuditBuilderWhere($builder, $params)
    {
        //员工id
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere('hsi.staff_info_id LIKE :staff_info_id:',
                ['staff_info_id' => '%' . $params['staff_info_id'] . '%']);
        }

        //员工id--精确
        if (!empty($params['stop_staff_id'])) {
            $builder->andWhere('hsi.staff_info_id = :stop_staff_id:',
                ['stop_staff_id' => $params['stop_staff_id']]);
        }

        //员工name
        if (!empty($params['name'])) {
            $builder->andWhere('hsi.name LIKE :name: ',
                ['name' => '%' . $params['name'] . '%']);
        }

        //网点
        if (!empty($params['sys_store_id'])) {
            $builder->andWhere('hsi.sys_store_id = :sys_store_id:', ['sys_store_id' => $params['sys_store_id']]);
        }

        //职位
        if (!empty($params['job_title_id'])) {
            $builder->andWhere('hsi.job_title = :job_title:', ['job_title' => $params['job_title_id']]);
        }

        //部门
        if (!empty($params['department'])) {
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere('hsi.node_department_id', $deptIds);
            }
        }

        //在职状态
        if (!empty($params['state']) && is_array($params['state'])) {
            if (in_array(Enums::HRIS_WORKING_STATE_4, $params['state'])) {
                $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state=:state: AND hsi.wait_leave_state=:wait_leave_state:)",
                    [
                        'states'           => $params['state'],
                        'state'            => Enums::HRIS_WORKING_STATE_1,
                        'wait_leave_state' => Enums::WAIT_LEAVE_STATE,
                    ]);
            } elseif (!in_array(Enums::HRIS_WORKING_STATE_4,
                    $params['state']) && in_array(Enums::HRIS_WORKING_STATE_1, $params['state'])) {
                $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state != :wait_leave_state:",
                    ['states' => $params['state'], 'wait_leave_state' => Enums::WAIT_LEAVE_STATE]);
            } else {
                $builder->andWhere("hsi.state IN ({states:array})", ['states' => $params['state']]);
            }
        }

        //停职日期
        if (!empty($params['stop_duties_start_date']) && !empty($params['stop_duties_end_date'])) {
            $builder->betweenWhere('sa.stop_duties_date', $params['stop_duties_start_date'],
                $params['stop_duties_end_date']);
        }

        //申请日期
        if (!empty($params['created_start_date']) && !empty($params['created_end_date'])) {
            $created_start_date = gmdate('Y-m-d H:s:i', strtotime($params['created_start_date'] . ' 00:00:00'));
            $created_end_date   = gmdate('Y-m-d H:s:i', strtotime($params['created_end_date'] . ' 23:59:59'));
            $builder->betweenWhere('sa.created_at', $created_start_date, $created_end_date);
        }

        //最后审批时间
        if (!empty($params['audit_start_time']) && !empty($params['audit_end_time'])) {
            $audit_start_time = $params['audit_start_time'] . ' 00:00:00';
            $audit_end_time   = $params['audit_end_time'] . ' 23:59:59';
            $builder->betweenWhere('sa.audit_time', $audit_start_time, $audit_end_time);
        }

        //处理状态
        if (!empty($params['fix_status'])) {
            //待审批：审批状态为待审批 且 审批节点审批人为当前登录人
            if ($params['fix_status'] == SuspensionAuditModel::STATUS_PENDING) {
                $builder->join(AuditApprovalModel::class,
                    'sa.id = aa.biz_value and aa.biz_type = ' . ApprovalEnums::APPROVAL_TYPE_SUSPENSION, 'aa');
                $builder->andWhere("sa.status = :approval_status:",
                    ['approval_status' => SuspensionAuditModel::STATUS_APPROVAL]);

                $builder->andWhere("aa.approval_id = :approval_id:", ['approval_id' => $params['staff_id']]);
                $builder->andWhere("aa.state = :audit_approval:", ['audit_approval' => Enums::audit_status_1]);
            } elseif ($params['fix_status'] == SuspensionAuditModel::STATUS_APPROVAL) {
                $builder->join(AuditApprovalModel::class,
                    'sa.id = aa.biz_value and aa.biz_type = ' . ApprovalEnums::APPROVAL_TYPE_SUSPENSION, 'aa');
                $builder->andWhere("sa.status = :approval_status:",
                    ['approval_status' => SuspensionAuditModel::STATUS_APPROVAL]);

                $builder->andWhere("aa.approval_id != :approval_id:", ['approval_id' => $params['staff_id']]);
                $builder->andWhere("aa.state = :audit_approval:", ['audit_approval' => Enums::audit_status_1]);
            } else {
                $builder->andWhere("sa.status = :status:", ['status' => $params['fix_status']]);
            }
        }

        //审批状态
        if (!empty($params['audit_status'])) {
            $builder->andWhere("sa.audit_state in ({audit_status:array})", ['audit_status' => $params['audit_status']]);
        }

        //审批编号
        if (!empty($params['serial_no'])) {
            $builder->andWhere("sa.serial_no = :serial_no:", ['serial_no' => $params['serial_no']]);
        }

        //发送状态
        if (!empty($params['send_status']) && is_array($params['send_status'])) {
            $builder->andWhere("sa.send_status in ({send_status:array})", ['send_status' => $params['send_status']]);
        }

        //停职日期
        if (!empty($params['stop_duties_date'])) {
            $builder->andWhere("sa.stop_duties_date <= :stop_duties_date:",
                ['stop_duties_date' => $params['stop_duties_date']]);
        }

        //不包括 处理状态
        if (!empty($params['is_not_status'])) {
            $builder->andWhere("sa.status != :is_not_status:", ['is_not_status' => $params['is_not_status']]);
        }

        return $builder;
    }

    /**
     * 格式化数据
     * @param $data
     * @param $staffId
     * @return array
     */
    public function formatAuditList($data, $staffId)
    {
        $storeListToId  = $jobTitleInfoToId = $list = [];
        $jobTitleId     = array_values(array_unique(array_column($data, 'job_title')));
        $sysStoreId     = array_values(array_unique(array_column($data, 'sys_store_id')));
        $department_ids = array_values(array_unique(array_column($data, 'node_department_id')));

        $suspensionIds = array_values(array_unique(array_column($data, 'id')));

        $auditParams['audit_ids']   = $suspensionIds;
        $auditParams['audit_type']  = ApprovalEnums::APPROVAL_TYPE_SUSPENSION;
        $auditParams['approval_id'] = $staffId;
        $auditParams['audit_state'] = Enums::audit_status_1;//审批人是 当前登录人。且审批状态为待审批
        $approvalList               = AuditApprovalRepository::getApprovalList($auditParams);

        $approvalListToId = array_column($approvalList, null, 'biz_value');

        $storeList = (new SysStoreService())->getStoreListByIds($sysStoreId);

        $storeList[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $jobTitleInfo = (new DepartmentService())->getJobList('', $jobTitleId, true);

        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $department_list = (new DepartmentService())->getAllDepartment($department_ids);

        foreach ($data as $value) {
            $oneData['id']              = $value['id'];
            $oneData['staff_info_id']   = $value['staff_info_id'];
            $oneData['name']            = $value['name'];
            $oneData['serial_no']       = $value['serial_no'];
            $oneData['job_title']       = $value['job_title'];
            $oneData['job_title_name']  = $jobTitleInfoToId[$value['job_title']] ?? '';
            $oneData['department_id']   = $value['node_department_id'];
            $oneData['department_name'] = $department_list[$value['node_department_id']] ?? '';
            $oneData['store_id']        = $value['sys_store_id'];
            $oneData['store_name']      = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['store_name'] : '';

            $oneData['stop_duties_date'] = !empty($value['stop_duties_date']) ? date('Y-m-d',
                strtotime($value['stop_duties_date'])) : '';

            $state = $value['state'];
            if ($value['state'] == HrStaffInfoModel::STATE_ON_JOB && $value['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
            }
            $oneData['state_text'] = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';

            $audit_status_text = !empty($value['audit_state']) ? static::$t->_('audit_status.' . $value['audit_state']) : '';
            if ($value['audit_state'] == SuspensionAuditModel::NO_NEED_AUDIT) {
                $audit_status_text = self::$t->_('no_need_audit');
            }

            $oneData['audit_status_text'] = $audit_status_text;

            $fixStatus = $value['status'];
            if (isset($approvalListToId[$value['id']])) {
                $fixStatus = SuspensionAuditModel::STATUS_PENDING;
            }
            $oneData['fix_status_text'] = !empty($fixStatus) ? static::$t->_(SuspensionAuditModel::$fix_status_text[$fixStatus]) : '';

            $oneData['apply_staff_name'] = !empty($value['apply_staff_info_id']) ? $value['apply_name'] . '(' . $value['apply_staff_info_id'] . ')' : '';
            $oneData['last_audit_name']  = !empty($value['last_audit_approval_id']) ? $value['last_audit_approval_name'] . '(' . $value['last_audit_approval_id'] . ')' : '';

            $oneData['audit_time'] = empty($value['audit_time']) ? '' : $value['audit_time'];
            $oneData['created_at'] = show_time_zone($value['created_at']);

            //是否展示撤销按钮：0不展示，1展示
            $is_cancel = 0;
            if (in_array($value['audit_state'], [
                    SuspensionAuditModel::NO_NEED_AUDIT,
                    Enums::audit_status_2,
                ]) && strtotime(date('Y-m-d')) < strtotime($oneData['stop_duties_date']) && $value['status'] != SuspensionAuditModel::STATUS_CANCEL) {
                $is_cancel = 1;
            }
            $oneData['is_cancel'] = $is_cancel;

            //是否展示审批按钮：0 不展示，1展示
            //处理状态为待审批才可以 展示
            $oneData['is_audit'] = $value['audit_state'] == Enums::audit_status_1 && $fixStatus == SuspensionAuditModel::STATUS_PENDING ? 1 : 0;

            $list[] = $oneData;
        }

        return $list;
    }

    /**
     * 停职管理导出
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function auditExport($params)
    {
        $count = $this->getAuditCountQuery($params);
        if ($count > self::LIMIT_DOWNLOAD_NUM) {
            throw new ValidationException(self::$t->_('file_download_limit', ['num' => self::LIMIT_DOWNLOAD_NUM]));
        }

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "staff_suspension_export" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "audit_list";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['staff_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = show_time_zone(gmdate('Y-m-d H:i:s', time()), 'Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        return $hcmExcelTaskId;
    }

    /**
     * 导入文件
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function auditImport($params)
    {
        $operatorId = $params['staff_id'];
        $importPath = $params['file_url'];

        $signInfo = SuspensionRepository::getSignConfigInfo($operatorId);
        if (empty($signInfo)) {
            throw new BusinessException(self::$t->_('maintain_signature_config_import'));//请先维护您的签字配置后再导入

        }

        $args['lang']      = $params['lang'];
        $args['file_name'] = $params['file_name'];
        $this->uploadCheck($importPath);

        (new AsyncImportTaskService())->insertTask($operatorId,
            AsyncImportTaskModel::SUSPENSION_AUDIT, $importPath, $args);

        return true;
    }

    /**
     * 检验行数
     * @param $importPath
     * @throws BusinessException
     */
    public function uploadCheck($importPath)
    {
        $tmpDir      = sys_get_temp_dir();            // 获取系统的临时目录路径
        $fileName    = basename($importPath);         // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;     // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($importPath))) {
            throw new BusinessException('System error');
        }

        $config       = ['path' => dirname($tmpFilePath)];
        $fileRealName = basename($tmpFilePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet()->setSkipRows(2);
        $excelData = $excel->getSheetData();

        $number = 0;
        foreach (static::yieldData()($excelData) as $item) {
            if (empty($item) || empty(array_filter($item))) {
                continue;
            }
            ++$number;
        }
        if ($number = 0) {
            throw new BusinessException(static::$t->_('file_content_is_null'));
        }

        if ($number > self::IMPORT_NUM) {
            throw new BusinessException(static::$t->_('import_count_limit_error', ['number' => self::IMPORT_NUM]));
        }
        @unlink($tmpFilePath);
    }

    public function dealImportData($filePath, $operatorId, $fileName, $importId)
    {
        //获取Excel数据
        $excelData = $this->getExcelData($filePath,
            [
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,//工号
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//停职日期
                2 => \Vtiful\Kernel\Excel::TYPE_STRING,//hold类型
                3 => \Vtiful\Kernel\Excel::TYPE_STRING,//hold原因
                4 => \Vtiful\Kernel\Excel::TYPE_STRING,//限制月
                5 => \Vtiful\Kernel\Excel::TYPE_STRING,//违规原因
                6 => \Vtiful\Kernel\Excel::TYPE_STRING,//违规条款
                7 => \Vtiful\Kernel\Excel::TYPE_STRING,//事故报告和附件
            ]);
        //去重 保留行号大的数据
        $data = $fileData = $header = $insertData = [];
        foreach (static::yieldData()($excelData) as $key => $datum) {
            //表头
            if ($key == 0) {
                continue;
            }
            if ($key == 1) {
                $header    = $datum;
                $header[8] = 'result';
                continue;
            }

            if (empty($datum) || empty(array_filter($datum))) {
                continue;
            }

            //去除多余空行
            if (empty($datum[0]) && empty($datum[1]) && empty($datum[2]) && empty($datum[5]) && empty($datum[6]) && empty($datum[7])) {
                continue;
            }
            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);

        if (!empty($data)) {
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError, $dataOne] = $this->dealItem($operatorId, $item, $importId);
                $fileData[] = $item;
                if (!empty($dataOne)) {
                    $insertData[] = $dataOne;
                }

                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }

        if (!empty($insertData)) {
            (new SuspensionAuditModel())->batch_insert($insertData);
        }

        $excel_file = $this->exportExcel($header, $fileData, $fileName);
        $flashOss   = new FlashOss();
        $oss_path   = 'staff_face_blacklist' . '/' . $fileName;
        $flashOss->uploadFile($oss_path, $excel_file['data']);
        return ['url' => $oss_path, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }

    public function dealItem($operatorId, $item, $importId)
    {
        $data = [];
        try {
            if (empty($item[0])) {
                throw new BusinessException(self::$t->_('wrong_staff_id'));//工号错误

            }
            $staffInfo = StaffInfoRepository::getInfoByStaffInfoId($item[0], true);
            if (empty($staffInfo)) {
                throw new BusinessException(self::$t->_('wrong_staff_id'));//工号错误
            }

            if ($staffInfo['formal'] == HrStaffInfoModel::FORMAL_0 || $staffInfo['is_sub_staff'] != HrStaffInfoModel::IS_SUB_STAFF_NO || !in_array($staffInfo['hire_type'],
                    self::$hireTypes)) {
                throw new BusinessException(self::$t->_('wrong_staff_id'));//工号错误
            }

            if ($staffInfo['state'] != HrStaffInfoModel::STATE_ON_JOB) {
                throw new BusinessException(self::$t->_('staff_info_state_error'));//员工非在职状态，不可提交停职申请
            }

            if ($staffInfo['sys_store_id'] == GlobalEnums::HEAD_OFFICE_ID) {
                throw new BusinessException(self::$t->_('staff_info_not_head_office'));//不可选择Head Office员工
            }

            //停职日期
            if (empty($item[1])) {
                throw new BusinessException(self::$t->_('stop_date_format_error'));//停职日期格式错误
            }

            $stopDate = $this->handleUploadFileDate($item[1]);
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $stopDate) || DateTime::createFromFormat('Y-m-d',
                    $stopDate) === false) {
                throw new BusinessException(self::$t->_('stop_date_format_error'));//停职日期格式错误
            }
            $item[1] = $stopDate;


            $startDate = date('Y-m-d');
            $endDate   = date('Y-m-d', strtotime('+4 day'));

            if (strtotime($stopDate) < strtotime($startDate) || strtotime($stopDate) > strtotime($endDate)) {
                throw new BusinessException(self::$t->_('stop_date_limit_error'));//停职日期只能选择今天开始的最近5天
            }

            $holdTypes = array_keys(HoldStaffManageModel::$hold_type);

            if (trim($item[2]) == '') {
                throw new BusinessException(self::$t->_('hold_type_error'));//hold类型错误
            }

            $holdTypeReason = intval(trim($item[2]));
            if (!in_array($holdTypeReason, $holdTypes)) {//hold 类型
                throw new BusinessException(self::$t->_('hold_type_error'));//hold类型错误
            }
            //hold原因KEY

            $holdReason = (new HoldManageService())->getHoldReason();

            //hold类型 对应的hold原因
            $hold_month_begin = $hold_month_end = null;
            if ($holdTypeReason != HoldStaffManageModel::HOLD_TYPE_NO && $holdTypeReason != 0) {
                //工资 和  工资+提成 不能有周期限制。
                if ($holdTypeReason == HoldStaffManageModel::HOLD_TYPE_WAGES || $holdTypeReason == HoldStaffManageModel::HOLD_TYPE_WAGES_PERCENTAGE) {
                    if (!empty($item[4])) {
                        throw new BusinessException(self::$t->_('limit_cycle_error'));//限制周期错误
                    }
                }

                if (empty($item[3])) {
                    throw new BusinessException(self::$t->_('hold_reason_error'));//hold原因错误
                }

                if (!isset($holdReason[$holdTypeReason])) {
                    throw new BusinessException(self::$t->_('hold_reason_error'));//hold原因错误
                }

                $reason_ids = array_column($holdReason[$holdTypeReason], 'reason_id');

                if (!in_array($item[3], $reason_ids)) {
                    throw new BusinessException(self::$t->_('hold_reason_relate_error'));//hold原因和hold类型不匹配

                }

                //限制月：只有提成 hold 限制月，月份才有效
                if (!empty($item[4]) && $holdTypeReason == HoldStaffManageModel::HOLD_TYPE_PERCENTAGE) {
                    $dates = explode('-', $item[4]);
                    $start = $dates[0];
                    $end   = $dates[1];
                    if (!$this->monthPreg($start) || !$this->monthPreg($end)) {
                        throw new BusinessException(self::$t->_('limit_cycle_error'));//限制周期错误
                    }

                    [$startMonth, $startYear] = explode('/', $dates[0]);
                    [$endMonth, $endYear] = explode('/', $dates[1]);

                    $start = $startYear . '-' . $startMonth;
                    $end   = $endYear . '-' . $endMonth;

                    if (strtotime($start) > strtotime($end)) {
                        throw new BusinessException(self::$t->_('limit_cycle_error'));//限制周期错误
                    }

                    $hold_month_begin = date('Y-m-01', strtotime($start));
                    $hold_month_end   = date('Y-m-t', strtotime($end));

                    // 导入日期为在1号-14号，可选择上月及以后的月份；导入日期在15号-月底，可选择当月及以后的月份
                    if (!$this->getStartMonth($hold_month_begin)) {
                        throw new BusinessException(self::$t->_('limit_cycle_error'));//限制周期错误
                    }
                }
            }

            if ($holdTypeReason == HoldStaffManageModel::HOLD_TYPE_NO) {
                if (!empty($item[3])) {
                    throw new BusinessException(self::$t->_('hold_reason_error'));//hold原因错误
                }
                if (!empty($item[4])) {
                    throw new BusinessException(self::$t->_('limit_cycle_error'));//限制周期错误
                }
            }

            $content_char_count = mb_strlen(trim($item[5]));
            if (($content_char_count > 3000 || $content_char_count < 1)) {
                throw new BusinessException(self::$t->_('violation_content_is_empty'));//违规内容不可为空或最多支持3000个字符
            }

            $clause_char_count = mb_strlen(trim($item[6]));
            if (($clause_char_count > 3000 || $clause_char_count < 1)) {
                throw new BusinessException(self::$t->_('violation_clause_is_empty'));//违规条款不可为空或最多支持3000个字符
            }

            $file_url = [];
            if (!empty(trim($item[7]))) {
                $fileUrls = explode(';', trim($item[7]));
                foreach ($fileUrls as $oneUrl) {
                    if (empty($oneUrl)) {
                        continue;
                    }

                    //防止分隔符，是 非分号。地址 结束位置不是 下列后缀，不允许导入
                    $fileArray = explode('http', trim($oneUrl));
                    foreach ($fileArray as $one) {
                        if (empty($one)) {
                            continue;
                        }
                        $one = 'http' . $one;
                        if (!preg_match('/^https?:\/\/.*\.(?:jpg|jpeg|png|doc|docx|xlsx|xls|mp4|avi|mov)$/i', $one)) {
                            throw new BusinessException(self::$t->_('accident_report_and_attachments_error'));//事故报告和附件格式错误
                        }
                    }

                    $url['url']  = $oneUrl;
                    $oneUrlArray = explode('/', $oneUrl);
                    $url['name'] = end($oneUrlArray);
                    $file_url[]  = $url;
                }

                if (count($file_url) > 9) {
                    throw new BusinessException(self::$t->_('accident_report_and_attachments_num_error'));//事故报告和附件最多不超过9个

                }
            }

            $operatorInfo = StaffInfoRepository::getInfoByStaffInfoId($operatorId, true);

            $rpc = (new ApiClient('by', '', 'getRandomId', self::$language));
            $rpc->withParam([]);
            $dataRes = $rpc->execute();
            if (isset($dataRes['code']) && $dataRes['code'] == 1) {
                $serial_no = $dataRes['data'];
            } else {
                throw new BusinessException(self::$t->_('retry_later'));//系统错误 请稍后再试。
            }

            $data['staff_info_id']       = $item[0];
            $data['serial_no']           = 'RNTE' . $serial_no;
            $data['description']         = '';
            $data['status']              = SuspensionAuditModel::STATUS_AGREE;
            $data['file_url']            = empty($file_url) ? '' : json_encode($file_url, JSON_UNESCAPED_UNICODE);
            $data['apply_staff_info_id'] = $operatorId;
            $data['apply_name']          = $operatorInfo['name'];
            $data['stop_duties_date']    = $stopDate;
            $data['hold_type']           = $holdTypeReason;
            $data['hold_reason']         = $item[3];
            $data['month_begin']         = $hold_month_begin;
            $data['month_end']           = $hold_month_end;
            $data['violation_content']   = $item[5];
            $data['violation_clause']    = $item[6];
            $data['import_id']           = $importId;
            $data['sign_staff_id']       = $operatorId;

            $item[8]   = 'success';
            $haveError = false;
        } catch (\Exception $e) {
            $haveError = true;
            $item[8]   = $e->getMessage();
        }

        return [$item, $haveError, $data];
    }

    public function monthPreg($month)
    {
        if (empty($month)) {
            return false;
        }
        $pattern = '/^(0[1-9]|1[0-2])\/\d{4}$/';
        if (preg_match($pattern, $month)) {
            return true;
        }
        return false;
    }

    /**
     * 撤销审批
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function auditCancel($params)
    {
        $auditInfo = SuspensionRepository::getAudit(['id' => $params['id']]);
        if (empty($auditInfo)) {
            throw new BusinessException('not found suspension audit' . self::$t->_('data_error'));//事故报告和附件格式错误

        }

        if ($auditInfo['status'] == SuspensionAuditModel::STATUS_CANCEL) {
            throw new BusinessException(self::$t->_('suspension_audit_is_cancel'));//该停职申请已被撤销。

        }

        $operatorInfo = StaffInfoRepository::getInfoByStaffInfoId($params['staff_id'], true);

        $updateData['last_operator_id']   = $params['staff_id'];
        $updateData['last_operator_name'] = $operatorInfo['name'];
        $updateData['last_operator_time'] = date('Y-m-d H:i:s');
        $updateData['status']             = SuspensionAuditModel::STATUS_CANCEL;
        $db                               = $this->getDI()->get("db_backyard");
        $db->updateAsDict("suspension_audit", $updateData, ["conditions" => 'id=' . intval($params['id'])]);

        return ['id' => $params['id']];
    }

    /**
     * 停职申请查看
     * @param $params
     * @return mixed
     * @throws BusinessException
     */
    public function auditCheck($params)
    {
        $auditInfo = SuspensionRepository::getAudit(['id' => $params['id']]);
        if (empty($auditInfo)) {
            throw new BusinessException('not found suspension audit' . self::$t->_('data_error'));//事故报告和附件格式错误

        }

        $staffInfo = StaffInfoRepository::getInfoByStaffInfoId($auditInfo['staff_info_id'], true);
        $applyInfo = StaffInfoRepository::getInfoByStaffInfoId($auditInfo['apply_staff_info_id'], true);

        $storeList = (new SysStoreService())->getStoreListByIds([
            $staffInfo['sys_store_id'],
            $applyInfo['sys_store_id'],
        ]);

        $storeList[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $storeListToId = [];
        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $jobTitleInfo     = (new DepartmentService())->getJobList('',
            [$staffInfo['job_title'], $applyInfo['job_title']], true);
        $jobTitleInfoToId = [];
        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        $department_list = (new DepartmentService())->getAllDepartment([
            $staffInfo['node_department_id'],
            $applyInfo['node_department_id'],
        ]);

        //停职员工信息
        $suspension_staff_info['staff_info_id']   = $staffInfo['staff_info_id'];
        $suspension_staff_info['name']            = $staffInfo['name'];
        $suspension_staff_info['job_title_name']  = $jobTitleInfoToId[$staffInfo['job_title']] ?? '';
        $suspension_staff_info['department_name'] = $department_list[$staffInfo['node_department_id']] ?? '';
        $suspension_staff_info['store_name']      = isset($storeListToId[$staffInfo['sys_store_id']]) ? $storeListToId[$staffInfo['sys_store_id']]['store_name'] : '';

        $state = $staffInfo['state'];
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB && $staffInfo['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
            $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
        }
        $suspension_staff_info['state_text'] = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';

        //停职申请人信息
        $applyInfo_info['name']            = !empty($auditInfo['apply_name']) ? $auditInfo['apply_name'] . '(' . $auditInfo['apply_staff_info_id'] . ')' : '';
        $applyInfo_info['job_title_name']  = $jobTitleInfoToId[$applyInfo['job_title']] ?? '';
        $applyInfo_info['department_name'] = $department_list[$applyInfo['node_department_id']] ?? '';
        $applyInfo_info['description']     = !empty($auditInfo['description']) ? $auditInfo['description'] : '';
        $applyInfo_info['file_url']        = !empty($auditInfo['file_url']) ? json_decode($auditInfo['file_url'],
            true) : [];
        $applyInfo_info['audit_state']     = $auditInfo['audit_state'];


        //获取审批流
        if ($auditInfo['audit_state'] != SuspensionAuditModel::NO_NEED_AUDIT) {
            $auditParams['audit_ids']   = [$params['id']];
            $auditParams['audit_type']  = ApprovalEnums::APPROVAL_TYPE_SUSPENSION;
            $auditParams['approval_id'] = $params['staff_id'];
            $auditParams['audit_state'] = Enums::audit_status_1;//审批人是 当前登录人。且审批状态为待审批
            $approvalList               = AuditApprovalRepository::getApprovalList($auditParams);

            $approvalListToId = array_column($approvalList, null, 'biz_value');

            $audit_params['staff_id']        = $auditInfo['apply_staff_info_id'];
            $audit_params['audit_show_type'] = ApprovalEnums::AUDIT_SHOW_TYPE_APPLY;//我审批的
            if (isset($approvalListToId[$params['id']])) {
                $audit_params['staff_id']        = $params['staff_id'];
                $audit_params['audit_show_type'] = ApprovalEnums::AUDIT_SHOW_TYPE_APPROVAL;//我审批的
            }

            $audit_params['id_union']        = 'id_' . $params['id'];
            $audit_params['staff_id']        = $params['staff_id'];
            $audit_params['type']            = ApprovalEnums::APPROVAL_TYPE_SUSPENSION;
            $audit_params['locale']          = self::$language;
            $audit_params['date_created']    = $auditInfo['created_at'] ?? '';//待确认
            $audit_params['audit_show_type'] = ApprovalEnums::AUDIT_SHOW_TYPE_APPROVAL;//我审批的
            //获取审批列表
            $auditDetail = (new WorkflowService())->getAuditDetailV2($audit_params,
                $params['staff_id']) ?: [];

            $stream          = $auditDetail['stream'] ?? [];
            $head            = $auditDetail['head'] ?? [];
            $can_edit_field  = $auditDetail['can_edit_field'] ?? [];
            $suspension_info = $auditDetail['suspension_info'] ?? null;
        } else {
            if (empty($auditInfo['import_id'])) {
                throw new BusinessException('not found import_id ' . self::$t->_('data_error'));
            }
            $info = AsyncImportTaskModel::findFirst($auditInfo['import_id']);
            if (empty($info)) {
                throw new BusinessException('not found import_id ' . self::$t->_('data_error'));
            }
            $info = $info->toArray();

            $operator_name = '';
            $operatorInfo  = StaffInfoRepository::getInfoByStaffInfoId($info['operator_id'], true);
            if (!empty($operatorInfo)) {
                $jobTitleInfo     = (new DepartmentService())->getJobList('',
                    [$operatorInfo['job_title']], true);
                $jobTitleInfoToId = [];
                if ($jobTitleInfo) {
                    $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
                }
                $operator_name = $operatorInfo['name'] . '(' . $operatorInfo['staff_info_id'] . ')';
                $operator_name = isset($jobTitleInfoToId[$operatorInfo['job_title']]) ? $operator_name . '-' . $jobTitleInfoToId[$operatorInfo['job_title']] : $operator_name;
            }

            $import_info['title']         = self::$t->_('suspension_batch_import');
            $import_info['created_at']    = show_time_zone($info['created_at']);
            $import_info['operator_name'] = $operator_name;

            //停职信息
            $suspension_info['stop_duties_date']  = $auditInfo['stop_duties_date'];
            $suspension_info['hold_type']         = $auditInfo['hold_type'];
            $suspension_info['hold_reason']       = $auditInfo['hold_reason'];
            $suspension_info['month_begin']       = !empty($auditInfo['month_begin']) ? date('Y-m',
                strtotime($auditInfo['month_begin'])) : '';
            $suspension_info['month_end']         = !empty($auditInfo['month_end']) ? date('Y-m',
                strtotime($auditInfo['month_end'])) : '';
            $suspension_info['violation_content'] = $auditInfo['violation_content'];
            $suspension_info['violation_clause']  = $auditInfo['violation_clause'];
        }

        if ($auditInfo['status'] == SuspensionAuditModel::STATUS_CANCEL) {
            $operator_name = '';
            $operatorInfo  = StaffInfoRepository::getInfoByStaffInfoId($auditInfo['last_operator_id'], true);
            if (!empty($operatorInfo)) {
                $jobTitleInfo     = (new DepartmentService())->getJobList('',
                    [$operatorInfo['job_title']], true);
                $jobTitleInfoToId = [];
                if ($jobTitleInfo) {
                    $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
                }
                $operator_name = $operatorInfo['name'] . '(' . $operatorInfo['staff_info_id'] . ')';
                $operator_name = isset($jobTitleInfoToId[$operatorInfo['job_title']]) ? $operator_name . '-' . $jobTitleInfoToId[$operatorInfo['job_title']] : $operator_name;
            }

            $cancel_info['title']         = self::$t->_('flow_audit_action.6');
            $cancel_info['created_at']    = $auditInfo['last_operator_time'];
            $cancel_info['operator_name'] = $operator_name;
        }

        $data['serial_no']             = $auditInfo['serial_no'];
        $data['suspension_staff_info'] = $suspension_staff_info;
        $data['apply_info']            = $applyInfo_info;
        $data['suspension_info']       = $suspension_info;
        $data['head']                  = empty($head) ? ['created_at' => show_time_zone($auditInfo['created_at'])] : $head;
        $data['stream']                = empty($stream) ? null : $stream;
        $data['can_edit_field']        = empty($can_edit_field) ? null : $can_edit_field;
        $data['import_info']           = empty($import_info) ? null : $import_info;
        $data['cancel_info']           = empty($cancel_info) ? null : $cancel_info;

        return $data;
    }

    /**
     * 审批
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function auditSubmit($params)
    {
        $auditInfo = SuspensionRepository::getAudit(['id' => $params['audit_id']]);
        if (empty($auditInfo)) {
            throw new BusinessException('not found suspension audit' . self::$t->_('data_error'));
        }

        $today  = date('Y-m-d');
        $endDay = date('Y-m-d', strtotime('+5 days'));//可选择明天开始的最近5天日期
        if (!empty($params['stop_duties_date']) && (strtotime($params['stop_duties_date']) <= strtotime(date($today)) || strtotime($params['stop_duties_date']) > strtotime(date($endDay)))) {
            throw new BusinessException(self::$t->_('stop_date_limit_error_tomorrow'));
        }

        $data['audit_id']          = $params['audit_id'];
        $data['status']            = $params['status'];
        $data['reject_reason']     = empty($params['reject_reason']) ? '' : $params['reject_reason'];
        $data['stop_duties_date']  = $params['stop_duties_date'];
        $data['hold_type']         = $params['hold_type'];
        $data['hold_reason']       = $params['hold_reason'];
        $data['month_begin']       = empty($params['month_begin']) ? null : $params['month_begin'];
        $data['month_end']         = empty($params['month_end']) ? null : $params['month_end'];
        $data['violation_content'] = $params['violation_content'];
        $data['violation_clause']  = $params['violation_clause'];
        $data['staff_id']          = $params['staff_id'];
        $data['staff_name']        = $params['staff_name'];

        $apiClient = new ApiClient('by', '', 'suspension_audit', self::$language);
        $apiClient->withParam($data);
        $result = $apiClient->execute();
        if ($result && $result['code'] != 1) {
            throw new BusinessException($result['message']);
        }

        return true;
    }

    public function checkOtherInfo($params)
    {
        $auditInfo = SuspensionRepository::getAudit(['id' => $params['audit_id']]);
        if (empty($auditInfo)) {
            throw new BusinessException('not found suspension audit' . self::$t->_('data_error'));
        }

        $today  = date('Y-m-d');
        $endDay = date('Y-m-d', strtotime('+5 days'));//可选择明天开始的最近5天日期
        if (!empty($params['stop_duties_date']) && (strtotime($params['stop_duties_date']) <= strtotime(date($today)) || strtotime($params['stop_duties_date']) > strtotime(date($endDay)))) {
            throw new BusinessException(self::$t->_('stop_date_limit_error_tomorrow'));
        }

        $data['audit_id']          = $params['audit_id'];
        $data['status']            = $params['status'];
        $data['reject_reason']     = empty($params['reject_reason']) ? '' : $params['reject_reason'];
        $data['stop_duties_date']  = $params['stop_duties_date'];
        $data['hold_type']         = $params['hold_type'];
        $data['hold_reason']       = $params['hold_reason'];
        $data['month_begin']       = empty($params['month_begin']) ? null : $params['month_begin'];
        $data['month_end']         = empty($params['month_end']) ? null : $params['month_end'];
        $data['violation_content'] = $params['violation_content'];
        $data['violation_clause']  = $params['violation_clause'];
        $data['staff_id']          = $params['staff_id'];
        $data['staff_name']        = $params['staff_name'];

        $apiClient = new ApiClient('by', '', 'suspension_audit_check', self::$language);
        $apiClient->withParam($data);
        $result = $apiClient->execute();
        if ($result && $result['code'] != 1) {
            throw new BusinessException($result['message']);
        }

        return $result['data'];
    }

    /**
     * 获取模板
     * @return mixed
     * @throws BusinessException
     */
    public function importTemplate()
    {
        $fileUrl = (new SettingEnvService())->getSetVal('suspension_audit_import_template');
        if (empty($fileUrl)) {
            throw new BusinessException(self::$t->_('data_error'));
        }
        $data['file_url'] = $fileUrl;

        return $data;
    }

    public function executeStaff($params)
    {
        $db       = $this->getDI()->get('db_backyard');
        $date     = $params['date'];
        $staff_id = $params['staff_id'] ?? 0;

        while (true) {
            $where['audit_status']     = [SuspensionAuditModel::NO_NEED_AUDIT, Enums::audit_status_2];
            $where['send_status']      = [SuspensionAuditModel::SEND_STATUS_0];
            $where['is_not_status']    = SuspensionAuditModel::STATUS_CANCEL;
            $where['stop_duties_date'] = $date;
            $where['page_size']        = 1000;
            $where['page_num']         = 1;

            if (!empty($params['staff_id'])) {
                $where['stop_staff_id'] = $staff_id;
            }
            $auditList = $this->getAuditListQuery($where, false);

            if (empty($auditList)) {
                break;
            }
            $jobTitleId = array_column($auditList, 'job_title');

            //获取签名信息
            $signList          = SuspensionRepository::getSignConfigList();
            $signListToStaffId = !empty($signList) ? array_column($signList, 'sign_url', 'staff_info_id') : [];
            //签名工号
            $signStaffId   = !empty($signList) ? array_column($signList, 'staff_info_id') : [];
            $signStaffInfo = [];
            if (!empty($signStaffId)) {
                $signStaffInfo = StaffInfoRepository::getStaffIdForColumns($signStaffId,
                    ['staff_info_id', 'name', 'job_title']);
                $jobTitleId    = !empty($signStaffInfo) ? array_merge($jobTitleId,
                    array_column($signStaffInfo, 'job_title')) : $jobTitleId;
            }
            $jobTitleId = array_values(array_unique($jobTitleId));

            $jobTitleInfo = (new DepartmentService())->getJobList('', $jobTitleId, true);

            $jobTitleInfoToId = [];
            if ($jobTitleInfo) {
                $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
            }

            foreach ($auditList as $oneData) {
                if (in_array($oneData['state'], [HrStaffInfoModel::STATE_SUSPEND, HrStaffInfoModel::STATE_RESIGN])) {
                    $updateData['send_status'] = SuspensionAuditModel::SEND_STATUS_5;
                    $db->updateAsDict("suspension_audit", $updateData,
                        ["conditions" => 'id=' . intval($oneData['id'])]);
                    $this->logger->info([
                        'action'        => 'StaffSuspensionTask-executeAction',
                        'staff_info_id' => $oneData['staff_info_id'],
                        'result'        => '该员工停职或离职，无需处理',
                    ]);
                    continue;
                }

                $changeRes = $this->changeStaffState($oneData);
                if (!$changeRes) {
                    $this->logger->error([
                        'action'        => 'StaffSuspensionTask-executeAction',
                        'staff_info_id' => $oneData['staff_info_id'],
                        'res'           => $changeRes,
                        'result'        => '修改员工在职状态失败，需要重新执行',
                    ]);
                    continue;
                }

                if ($oneData['hold_type'] != HoldStaffManageModel::HOLD_TYPE_NO) {
                    $hold_type = $oneData['hold_type'];
                    if ($oneData['hold_type'] == HoldStaffManageModel::HOLD_TYPE_WAGES_PERCENTAGE) {
                        $hold_type = HoldStaffManageModel::HOLD_TYPE_WAGES . ',' . HoldStaffManageModel::HOLD_TYPE_PERCENTAGE;
                    }

                    $holdReason = (new HoldManageService())->getHoldReason();
                    $reason     = array_column($holdReason[$oneData['hold_type']], null, 'reason_id');

                    $holdData['staff_info_id'] = $oneData['staff_info_id'];
                    $holdData['hold_type']     = $hold_type;
                    $holdData['hold_reason']   = isset($reason[$oneData['hold_reason']]) ? $reason[$oneData['hold_reason']]['key'] : '';
                    $holdData['handle_people'] = isset($reason[$oneData['hold_reason']]) ? $reason[$oneData['hold_reason']]['handle_people'] : '';
                    $holdData['month_begin']   = $oneData['month_begin'];
                    $holdData['month_end']     = $oneData['month_end'];
                    $this->addHold($holdData);
                }

                if (!isset($signListToStaffId[$oneData['sign_staff_id']])) {
                    $updateData['send_status'] = SuspensionAuditModel::SEND_STATUS_4;
                    $db->updateAsDict("suspension_audit", $updateData,
                        ["conditions" => 'id=' . intval($oneData['id'])]);
                    $this->logger->error([
                        'action'        => 'StaffSuspensionTask-executeAction',
                        'staff_info_id' => $oneData['staff_info_id'],
                        'result'        => '未找到签字信息，联系产品，工号：' . $oneData['sign_staff_id'],
                    ]);
                    continue;
                }

                $oneData['job_title_name'] = $jobTitleInfoToId[$oneData['job_title']] ?? '';

                $oneData['sign_url']            = $signListToStaffId[$oneData['sign_staff_id']];
                $sign_job_title                 = !empty($signStaffInfo[$oneData['sign_staff_id']]) ? $signStaffInfo[$oneData['sign_staff_id']]['job_title'] : 0;
                $oneData['sign_job_title_name'] = $jobTitleInfoToId[$sign_job_title] ?? '';
                $oneData['sign_name']           = !empty($signStaffInfo[$oneData['sign_staff_id']]) ? $signStaffInfo[$oneData['sign_staff_id']]['name'] : '';

                $pdfFileName          = 'NET_' . $oneData['name'] . ' ' . $oneData['stop_duties_date'];
                $pdfFileName          = str_replace('/', '', $pdfFileName);
                $oneData['file_name'] = $pdfFileName;

                //生成pdf 失败的跳过， 不能影响
                $url = $this->createAuditPdf($oneData);
                if (empty($url)) {
                    $this->logger->error([
                        'action'        => 'StaffSuspensionTask-executeAction',
                        'staff_info_id' => $oneData['staff_info_id'],
                        'result'        => '停职文件生成失败--会重新取出重新生成，请检查数据',
                    ]);
                    $updateData['send_status'] = SuspensionAuditModel::SEND_STATUS_6;
                    $db->updateAsDict("suspension_audit", $updateData,
                        ["conditions" => 'id=' . intval($oneData['id'])]);
                    continue;
                }

                $send_status = SuspensionAuditModel::SEND_STATUS_1;

                if (!empty($oneData['personal_email'])) {
                    //为了给文件命名
                    $oneData['file_url'] = $this->fileDownload($url, '', $pdfFileName . '.pdf');
                    $sendRes             = $this->sendAuditEmail($oneData);
                    if (!$sendRes) {
                        $send_status = SuspensionAuditModel::SEND_STATUS_3;
                    }
                } else {
                    $send_status = SuspensionAuditModel::SEND_STATUS_2;
                }

                $updateData['pdf_url']     = $url;
                $updateData['send_status'] = $send_status;

                $db->updateAsDict("suspension_audit", $updateData, ["conditions" => 'id=' . intval($oneData['id'])]);
            }
        }
    }

    /**
     * hold
     * @param $params
     * @return bool
     */
    public function addHold($params)
    {
        $hold_params = [
            "staff_info_id" => $params['staff_info_id'],
            "type"          => $params['hold_type'],
            "hold_reason"   => $params['hold_reason'],
            "hold_remark"   => "",
            "hold_time"     => date('Y-m-d H:i:s'),
            "hold_source"   => HoldStaffManageModel::HOLD_SOURCE_SUSPENSION_AUDIT,
            "handle_people" => $params['handle_people'],
        ];
        if ($params['hold_type'] == HoldStaffManageModel::HOLD_TYPE_PERCENTAGE && !empty($params['month_begin']) && !empty($params['month_end'])) {
            $hold_params['month_begin'] = $params['month_begin'];
            $hold_params['month_end']   = $params['month_end'];
        }
        $hold_res = (new HoldManageService())->synchronizeHoldStaff($hold_params);
        if (empty($hold_res)) {
            $this->getDI()->get('logger')->write_log([
                'SuspensionAudit-addHold' => [
                    'params' => $hold_params,
                    'result' => $hold_res,
                ],
            ], 'error');
            return false;
        }

        return true;
    }

    /**
     * 变更员工在职状态，停职
     * @param $data
     * @return bool
     */
    public function changeStaffState($data)
    {
        $updateStateData[] = [
            "staff_info_id"    => $data['staff_info_id'],
            "type"             => HrStaffInfoModel::STATE_SUSPEND,//停职
            "day"              => $data['stop_duties_date'],
            'stop_duty_reason' => StaffEnums::STOP_DUTY_REASON_UNDER_INVESTIGATION,//调查中
            'src'              => 'hcm',
        ];
        $apiClient         = new ApiClient('hris', '', 'sync_staff_state');
        $apiClient->setParams([$updateStateData]);
        $result = $apiClient->execute();
        if (isset($result['code'], $result['data']) && $result['code'] == 1) {
            foreach ($result['data'] as $body) {
                if ('ok' == $body['msg']) {
                    return true;
                } else {
                    $this->getDI()->get('logger')->write_log([
                        'SuspensionAudit-changeStaffState' => [
                            'params' => $updateStateData,
                            'result' => $result,
                        ],
                    ], 'error');
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 生成pdf
     * @param $params
     * @return string
     * @throws Exception
     */
    public function createAuditPdf($params)
    {
        if (empty($params['sign_url'])) {
            return '';
        }

        $data['staff_info_id']     = $params['staff_info_id'];
        $data['name']              = $params['name'];
        $data['job_title']         = $params['job_title_name'];
        $data['date']              = $params['stop_duties_date'];
        $data['violation_content'] = $params['violation_content'];
        $data['violation_clause']  = $params['violation_clause'];

        $data['sign_name']           = $params['sign_name'];
        $data['sign_job_title_name'] = $params['sign_job_title_name'];

        $companyConfigInfo = (new \App\Modules\Ph\Services\CertificateService())->getCompanyConfigInfo(['staff_info_id' => $params['staff_info_id']]);

        $pdfHeaderFooterSetting['headerTemplate']      = $this->getCollectionHeader($companyConfigInfo);
        $pdfHeaderFooterSetting['footerTemplate']      = '<div></div>';
        $pdfHeaderFooterSetting['printBackground']     = true;
        $pdfHeaderFooterSetting['format']              = 'a4';
        $pdfHeaderFooterSetting['displayHeaderFooter'] = true;

        $img_data = [
            ['name' => 'select_sign_img', 'url' => $params['sign_url']],
        ];

        $path       = BASE_PATH . '/app/views/suspension_pdf_temp/FILE_TYPE_NTE_PREVENTIVE_SUSPENSION.ftl';
        $pdfTempUrl = $this->getPdfTemp($path);

        $pdfFileName = $params['file_name'];
        $pdfRes      = (new FormPdfServer())->getInstance()->generatePdf($pdfTempUrl, $data, $img_data,
            $pdfHeaderFooterSetting, $pdfFileName, 'attchment');
        if (!isset($pdfRes['object_url'])) {
            $this->logger->write_log('sendAuditEmail createAuditPdf 停职申请发送邮件 pdf 生产失败',
                "notice");
            return '';
        }

        return $pdfRes['object_url'];
    }

    /**
     * 发送邮件
     * @param $params
     * @return bool
     */
    public function sendAuditEmail($params)
    {
        try {
            $staffsEmail = $params['personal_email'];
            //发送邮件
            $title   = self::$t->_('suspension_email_audit_title',
                [
                    'staff_id'   => $params['staff_info_id'],
                    'staff_name' => $params['name'],
                    'date'       => $params['stop_duties_date'],
                ]);
            $content = self::$t->_('suspension_email_audit_content');
            if (empty($staffsEmail)) {
                $this->logger->notice('suspensionSendAuditEmail 邮件发送失败,邮箱不能为空，staff_id' . $params['staff_id']);

                return false;
            }

            $ccEmailData = (new SettingEnvService())->getSetVal('suspension_email_file_config', ',');

            $sendResult = \App\Library\BiMail::send($staffsEmail, $title, $content, [$params['file_url']],
                $ccEmailData);
            if (!$sendResult) {
                $json = json_encode(['staff_info_id' => $params['staff_id'], 'email' => $staffsEmail],
                    JSON_UNESCAPED_UNICODE);
                $this->logger->notice('suspensionSendAuditEmail :邮件发送失败' . $json);
                return false;
            }
            return true;
        } catch (Exception $e) {
            $this->logger->notice("suspensionSendAuditEmail 邮件发送失败staff_info_id:" . $params['staff_id'] . ",接收人邮箱异常email:" . $staffsEmail . ', message:' . $e->getMessage());
            return false;
        }
    }

    public function sendRemind($date)
    {
        $exportData                      = [];
        $where['send_status']            = [
            SuspensionAuditModel::SEND_STATUS_2,
            SuspensionAuditModel::SEND_STATUS_3,
            SuspensionAuditModel::SEND_STATUS_4,
        ];
        $where['stop_duties_start_date'] = $date;
        $where['stop_duties_end_date']   = $date;
        $where['page_size']              = 1000;
        $where['page_num']               = 1;
        while (true) {
            $auditList = $this->getAuditListQuery($where, false);
            if (empty($auditList)) {
                break;
            }

            foreach ($auditList as $oneData) {
                $data['staff_info_id']    = $oneData['staff_info_id'];
                $data['stop_duties_date'] = $oneData['stop_duties_date'];

                $result = '';
                if ($oneData['send_status'] == SuspensionAuditModel::SEND_STATUS_2) {
                    $result = 'Personal Email is empty';
                }

                if ($oneData['send_status'] == SuspensionAuditModel::SEND_STATUS_3) {
                    $result = 'Personal Email is error';
                }

                if ($oneData['send_status'] == SuspensionAuditModel::SEND_STATUS_4) {
                    $result = 'Not found signature';
                }

                if ($oneData['send_status'] == SuspensionAuditModel::SEND_STATUS_6) {
                    $result = 'System error, PDF Create fail';
                }

                $data['result'] = $result;
                $exportData[]   = array_values($data);
            }
            $where['page_num']++;
        }

        if (empty($exportData)) {
            $this->logger->info('suspension-sendRemind : 数据为空，不发送邮件');
            return 'success';
        }
        $header = [
            'Employee No.',
            'Suspension date',
            'Failure reason',
        ];

        $file_name = uniqid('NTE Letter Failed Sent List ' . date('Ymd') . '_') . '.xlsx';

        $file_data             = $this->exportExcel($header, $exportData, $file_name);
        $emailData['email']    = (new SettingEnvService())->getSetVal('suspension_email_file_config', ',');
        $emailData['file_url'] = $file_data['data'];
        if ($this->sendRemindEmail($emailData)) {
            return 'success';
        }
    }

    public function sendRemindEmail($params)
    {
        if (empty($params['email'])) {
            $this->logger->error("sendRemindEmail 通知邮件 发送失败 邮箱不能为空");
            return false;
        }
        try {
            $staffsEmail = $params['email'];
            //发送邮件
            $title      = self::$t->_('suspension_send_remind_email_title');
            $content    = self::$t->_('suspension_send_remind_email_content');
            $sendResult = \App\Library\BiMail::send($staffsEmail, $title, $content, [$params['file_url']]);
            if (!$sendResult) {
                return false;
            }
            return true;
        } catch (Exception $e) {
            $this->logger->notice("部门自动变更失败通知邮件 发送失败  message: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 上传日期格式的兼容处理
     * @param $file_src_date
     * @return mixed
     */
    protected function handleUploadFileDate($file_src_date)
    {
        $date = '';
        // 时间戳
        if (preg_match('/\d{10}/', $file_src_date)) {
            $date = date('Y-m-d', $file_src_date);
        } else {
            if (stripos($file_src_date, '/') > 0) {
                $date = date('Y-m-d', strtotime($file_src_date));
            } else {
                if (stripos($file_src_date, '-') > 0) {
                    $date = date('Y-m-d', strtotime($file_src_date));
                }
            }
        }

        return $date;
    }

    /**
     * 导入日期为在1号-14号，可选择上月及以后的月份；导入日期在15号-月底，可选择当月及以后的月份
     * @param $starMonth
     * @return bool
     * @throws Exception
     */
    public function getStartMonth($starMonth)
    {
        $today        = new DateTime();
        $day          = (int)$today->format('d');
        $currentMonth = (int)$today->format('m');
        $currentYear  = (int)$today->format('Y');

        $limitMonth = sprintf('%04d-%02d-01', $currentYear, $currentMonth);

        if ($day >= 1 && $day <= self::HOLD_LIMIT) {
            // 1号-14号：可选择上月及以后的月份
            $month = $currentMonth - 1;
            $year  = $currentYear;

            if ($month < 1) {
                $month = 12;
                $year--;
            }

            $limitMonth = sprintf('%04d-%02d-01', $year, $month);
        }

        if (strtotime($starMonth) < strtotime($limitMonth)) {
            return false;
        }

        return true;
    }

}
