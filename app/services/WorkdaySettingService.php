<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\RolesModel;
use App\Models\backyard\SysStoreModel;
use app\models\backyard\WorkdaySettingV2Model;
use Exception;
use OSS\Core\OssException;
use OSS\OssClient;
use Phalcon\Mvc\Model;

class WorkdaySettingService extends BaseService
{

    public $count = 0;
    public $param;
    protected $storeData = [];
    protected $jobTitleData = [];

    /**
     * 最近生效中的非全勤配置
     * @param $sys_store_id
     * @param $job_title
     * @param $date_at
     * @return false|Model|void
     */
    protected function getEffectSettingInfo($sys_store_id,$job_title,$date_at)
    {
        //非全勤
        $notFullAttendanceInfo = WorkdaySettingV2Model::findFirst([
            'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and (max_days_num > 0 or max_days_num is null)  and  (end_date > :end_date: or end_date is null) and effect_date <= :effect_date:',
            'bind'       => [
                'store_id'    => $sys_store_id,
                'job_title'   => $job_title,
                'end_date'    => $date_at,
                'effect_date' => $date_at,
            ],
            'order'      => 'effect_date desc',
        ]);
        if (empty($notFullAttendanceInfo)) {
            return false;
        }
        return $notFullAttendanceInfo;
    }


    /**
     * 生效中的全勤数据
     * @param $sys_store_id
     * @param $job_title
     * @param $date_at
     * @return false|Model|void
     */
    protected function getFullAttendanceSettingInfo($sys_store_id,$job_title,$date_at)
    {
        $fullAttendanceInfo = WorkdaySettingV2Model::findFirst([
            'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and max_days_num = 0 and  end_date >= :end_date: and effect_date <= :effect_date:',
            'bind'       => ['store_id' => $sys_store_id,'job_title'=> $job_title, 'end_date' => $date_at,'effect_date' => $date_at],
        ]);
        if (empty($fullAttendanceInfo)) {
            return false;
        }
        return $fullAttendanceInfo;
    }


    /**
     * 指定日期所在周生效中的全勤数据
     * @param $sys_store_id
     * @param $job_title
     * @param $date_at
     * @return false|Model|void
     */
    public function getWeekFullAttendanceSettingInfo($sys_store_id,$job_title,$date_at)
    {
        $bind = ['store_id' => $sys_store_id,'job_title'=> $job_title, 'end_date' => DateHelper::weekEnd($date_at),'effect_date' => DateHelper::weekStart($date_at),];

        $fullAttendanceInfo = WorkdaySettingV2Model::findFirst([
            'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and max_days_num = 0 and  !(effect_date > :end_date: or end_date < :effect_date:)  ',
            'bind'       => $bind,
        ]);
        if (empty($fullAttendanceInfo)) {
            return false;
        }
        return $fullAttendanceInfo;
    }


    /**
     * 取消休息日验证配置
     * @param $staffInfo
     * @param $date_at
     * @param $operator
     * @return array
     * @throws ValidationException
     */
    public function checkOffDayCancel($staffInfo, $date_at,$operator): array
    {
        $result = ['',[]];

        $unrestricted = (new SettingEnvService())->getSetVal('UnrestrictedSetRest', ',');
        if (in_array($operator, $unrestricted)) {
            return $result;
        }

        //非全勤 生效中配置
        $notFullAttendanceInfo = $this->getEffectSettingInfo($staffInfo['sys_store_id'],$staffInfo['job_title'],$date_at);
        if (empty($notFullAttendanceInfo)) {
            return $result;
        }
        $start_date = DateHelper::weekStart($date_at);
        $end_date   = DateHelper::weekEnd($date_at);
        $offDays    = HrStaffWorkDaysModel::find([
            'columns'    => 'date_at',
            'conditions' => 'staff_info_id = :staff_info_id: and date_at != :current_date_at: and date_at >= :start_date: and date_at <= :end_date_at:',
            'bind'       => [
                'staff_info_id'   => $staffInfo['staff_info_id'],
                'start_date'      => $start_date,
                'end_date_at'     => $end_date,
                'current_date_at' => $date_at,
            ],
        ])->toArray();
        if ($notFullAttendanceInfo->days_num && count($offDays) < $notFullAttendanceInfo->days_num) {
            $set_val    = (new SettingEnvService())->getSetVal('modify_today_rest', ',');
            $week_start = DateHelper::weekStart($date_at);
            $start_date = date('Y-m-d', strtotime('+1 day'));
            if (in_array($operator, $set_val)) {
                $start_date = date('Y-m-d');
            }
            $start_date = max($start_date, $week_start);
            $date_list = DateHelper::DateRange(strtotime($start_date), strtotime($end_date));
            $date_list = array_diff($date_list, [$date_at]);
            $result[0] = self::$t->_('21841_workday_cancel_error_1', [
                'staff_info_id' => $staffInfo['staff_info_id'],
                'num'           => $notFullAttendanceInfo->days_num,
                'date_at'       => $date_at,
                'week'          => self::$t->_('default_rest_day_' . date('N', strtotime($date_at))),
            ]);
            $date_at_list = array_values(array_diff($date_list, array_column($offDays, 'date_at')));
            if (empty($date_at_list)) {
                throw new ValidationException(self::$t->_('21841_workday_cancel_error_2',
                    ['num' => $notFullAttendanceInfo->days_num,]));
            }
            foreach ($date_at_list as $day) {
                $result[1][] = [
                    'date_at' => $day,
                    'week'    => self::$t->_('default_rest_day_' . date('N', strtotime($day))),
                ];
            }
            return $result;
        }
        return $result;
    }

    /**
     * 默认休息日验证
     * @param $defaultRestNum
     * @param $sys_store_id
     * @param $job_title
     * @param $date_at
     * @return bool
     */
    public function checkDefaultOffDay($defaultRestNum,$sys_store_id,$job_title,$date_at): bool
    {
        //全勤
        $fullAttendanceInfo = $this->getWeekFullAttendanceSettingInfo($sys_store_id,$job_title,$date_at);
        if(!empty($fullAttendanceInfo)){
            return false;
        }

        //非全勤
        $notFullAttendanceInfo = $this->getEffectSettingInfo($sys_store_id,$job_title,$date_at);
        if(empty($notFullAttendanceInfo)){
            return true;
        }
        if ($notFullAttendanceInfo->max_days_num && $defaultRestNum > $notFullAttendanceInfo->max_days_num) {
            return false;
        }
        if ($notFullAttendanceInfo->days_num && $defaultRestNum < $notFullAttendanceInfo->days_num) {
            return false;
        }
        return true;
    }


    /**
     * @param $staffInfo
     * @param $date_at
     * @return bool
     * @throws ValidationException
     */
    public function checkOffDayAdd($staffInfo,$date_at,$operator): bool
    {
        $unrestricted = (new SettingEnvService())->getSetVal('UnrestrictedSetRest', ',');
        if (in_array($operator, $unrestricted)) {
            return true;
        }
        //全勤
        $fullAttendanceInfo = $this->getFullAttendanceSettingInfo($staffInfo['sys_store_id'],$staffInfo['job_title'],$date_at);
        if(!empty($fullAttendanceInfo)){
            throw new ValidationException(self::$t->_('21841_workday_add_error_1',['staff_info_id' => $staffInfo['staff_info_id'],'num'=>0]));
        }

        //非全勤
        $notFullAttendanceInfo = $this->getEffectSettingInfo($staffInfo['sys_store_id'],$staffInfo['job_title'],$date_at);
        if(empty($notFullAttendanceInfo)){
            return true;
        }

        $wordDayNum = HrStaffWorkDaysModel::count([
            'conditions' => 'staff_info_id = :staff_info_id: and date_at >= :start_date: and date_at <= :end_date_at:',
            'bind'       => ['staff_info_id' => $staffInfo['staff_info_id'],'start_date'=> DateHelper::weekStart($date_at),'end_date_at'=> DateHelper::weekEnd($date_at)],
        ]);
        if(!empty($notFullAttendanceInfo->max_days_num) &&  $wordDayNum + 1 > $notFullAttendanceInfo->max_days_num){
            throw new ValidationException(self::$t->_('21841_workday_add_error_1',['staff_info_id' => $staffInfo['staff_info_id'],'num'=>$notFullAttendanceInfo->max_days_num]));
        }
        return true;
    }

    /**
     * 轮休配置提示
     * @param $param
     * @return string[]|void
     */
    public function getPageRemindData($param)
    {
        $result        = [];
        $userInfo      = (new StaffService())->get_fbi_user_info($param['operate_id']);
        $user_position = $userInfo['position_category'] = empty($userInfo['position_category']) ? [] : explode(',',
            $userInfo['position_category']);

        if (in_array(RolesModel::ROLES_STORE_MANAGER, $user_position) || in_array(RolesModel::ROLES_STORE_SUPERVISOR,
                $user_position)) {//网点
            $param['pre_store_id'] = $userInfo['organization_id'];
        }
        //没有指定网点搜索 并且有 pre id 说明角色是 网点主管或者网点经理 查询管辖网点 看是不是超过1个如果是一个就取 如果超1个返回空
        if (empty($param['store_id']) && !empty($param['pre_store_id'])) {
            $masterStore       = (new WorkdayService())->getMasterStore($param);
            $param['store_id'] = empty($masterStore) ? [] : [$masterStore];
        }
        //没有网点参数 没数据
        if (empty($param['store_id'])) {
            return $result;
        }
        //有可能搜总部
        $param['store_id'] = array_diff($param['store_id'], ['-1']);
        if (empty($param['store_id'])) {
            return $result;
        }
        //超过2个 没数据
        if (count($param['store_id']) > 1) {
            return $result;
        }
        //只有一个
        $param['store_id'] = current($param['store_id']);

        //全勤
        $fullAttendanceInfo = WorkdaySettingV2Model::find([
            'conditions' => 'store_id = :store_id: and is_delete = 0 and max_days_num = 0 and  end_date >= :end_date:',
            'bind'       => ['store_id' => $param['store_id'], 'end_date' => date('Y-m-d')],
            'order'      => 'effect_date asc',
        ])->toArray();
        if(!empty($fullAttendanceInfo)){

            foreach ($fullAttendanceInfo as $item) {
                $result[] = self::$t->_('workday_setting_remind_1',['effect_date' => $item['effect_date'],'end_date' => $item['end_date'],'job_title' => $this->showJobTitleName($item['job_title'])]);
            }
        }

        //非全勤的 本周生效的
        $notFullAttendanceInfo = WorkdaySettingV2Model::find([
            'conditions' => 'store_id = :store_id: and is_delete = 0  and (max_days_num is null or max_days_num > 0) and if(ISNULL(end_date),true,end_date != effect_date) and (end_date is null or end_date >= :end_date:) ',
            'bind'       => ['store_id' => $param['store_id'],'end_date' => date('Y-m-d')],
            'order'      => 'effect_date asc',
        ])->toArray();
        if(!empty($notFullAttendanceInfo)){
            foreach ($notFullAttendanceInfo as $item) {

                if (!empty($item['effect_date']) && !empty($item['end_date']) && !is_null($item['days_num']) && !is_null($item['max_days_num']) && $item['days_num'] !== $item['max_days_num']) {
                    //2025-06-01至2025-06-05要求Van每周最小设置1个休息日，每周最大设置2个休息日
                    $result[] = self::$t->_('workday_setting_remind_2', [
                        'effect_date' => $item['effect_date'],
                        'end_date'    => $item['end_date'],
                        'min_num'     => $item['days_num'],
                        'max_num'     => $item['max_days_num'],
                        'job_title'   => $this->showJobTitleName($item['job_title']),
                    ]);
                } elseif (!empty($item['effect_date']) && !empty($item['end_date']) && (is_null($item['days_num']) || is_null($item['max_days_num']))) {
                    if (is_null($item['days_num'])) {
                        //2025-06-01至2025-06-05要求Van每周最大设置X个休息日
                        $result[] = self::$t->_('workday_setting_remind_6', [
                            'effect_date' => $item['effect_date'],
                            'end_date'    => $item['end_date'],
                            'max_num'     => $item['max_days_num'],
                            'job_title'   => $this->showJobTitleName($item['job_title']),
                        ]);
                    } else {
                        //2025-06-01至2025-06-05要求Van每周最小设置X个休息日
                        $result[] = self::$t->_('workday_setting_remind_7', [
                            'effect_date' => $item['effect_date'],
                            'end_date'    => $item['end_date'],
                            'min_num'     => $item['days_num'],
                            'job_title'   => $this->showJobTitleName($item['job_title']),
                        ]);
                    }
                } elseif (!empty($item['effect_date']) && empty($item['end_date']) && !is_null($item['days_num']) && !is_null($item['max_days_num']) && $item['days_num'] !== $item['max_days_num']) {
                    //2025-06-01后要求Van每周最小设置1个休息日，每周最大设置2个休息日
                    $result[] = self::$t->_('workday_setting_remind_3', [
                        'effect_date' => $item['effect_date'],
                        'min_num'     => $item['days_num'],
                        'max_num'     => $item['max_days_num'],
                        'job_title'   => $this->showJobTitleName($item['job_title']),
                    ]);
                } elseif (!empty($item['effect_date']) && empty($item['end_date']) && (is_null($item['days_num']) || is_null($item['max_days_num']))) {
                    if (is_null($item['days_num'])) {
                        //2025-06-01后要求Van每周最大设置X个休息日
                        $result[] = self::$t->_('workday_setting_remind_8', [
                            'effect_date' => $item['effect_date'],
                            'max_num'     => $item['max_days_num'],
                            'job_title'   => $this->showJobTitleName($item['job_title']),
                        ]);
                    } else {
                        //2025-06-01后要求Van每周最小设置X个休息日
                        $result[] = self::$t->_('workday_setting_remind_9', [
                            'effect_date' => $item['effect_date'],
                            'min_num'     => $item['days_num'],
                            'job_title'   => $this->showJobTitleName($item['job_title']),
                        ]);
                    }
                } elseif ($item['days_num'] === $item['max_days_num'] && !empty($item['effect_date']) && !empty($item['end_date'])) {
                    //2025-06-01至2025-06-05要求Van每周设置1个休息日
                    $result[] = self::$t->_('workday_setting_remind_4', [
                        'effect_date' => $item['effect_date'],
                        'end_date'    => $item['end_date'],
                        'num'         => $item['days_num'],
                        'job_title'   => $this->showJobTitleName($item['job_title']),
                    ]);
                } elseif ($item['days_num'] === $item['max_days_num'] && !empty($item['effect_date']) && empty($item['end_date'])) {
                    //2025-06-01后要求Van每周设置1个休息日
                    $result[] = self::$t->_('workday_setting_remind_5', [
                        'effect_date' => $item['effect_date'],
                        'num'         => $item['days_num'],
                        'job_title'   => $this->showJobTitleName($item['job_title']),
                    ]);
                }
            }
        }


        return $result;

    }



    public function getList()
    {
        $param         = $this->param;
        $param['size'] = $param['size'] ?: 100;
        $param['page'] = $param['page'] ?: 1;
        //分页数据
        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->from(['w' => WorkdaySettingV2Model::class]);
        $audit_object->leftJoin(SysStoreModel::class, 'w.store_id = s.id', 's');

        //通用数据权限限定条件
        $audit_object = $this->buildConditions($audit_object);
        $audit_object->columns(
            "w.id,w.store_id,s.name store_name, s.manage_region region_id, s.manage_piece piece_id,s.category,w.job_title, w.days_num,w.max_days_num,w.updated_at,w.effect_date,w.is_delete,w.end_date"
        );

        $audit_object->offset(($param['page'] - 1) * $param['size']);
        $audit_object->limit($param['size']);
        $audit_object->orderBy('w.id DESC');
        $data = $audit_object->getQuery()->execute()->toArray();
        if (empty($data)) {
            return [];
        }
        //分类名称

        foreach ($data as &$da) {
            $t_key               = SysStoreService::$category[$da['category']] ?? '';
            $da['category_text'] = self::$t->_($t_key);
            //职位
            $da['job_name']  = $this->showJobTitleName($da['job_title']);
            $da['region_name'] = $this->showRegionName($da['region_id']);
            $da['piece_name']  = $this->showPieceName($da['piece_id']);
            $da['updated_at']  = show_time_zone($da['updated_at'],'Y-m-d H:i');
            $da['delete_at'] = '';
            if ($da['is_delete']) {
                $da['delete_at'] = $da['updated_at'];
            }
            $da['delete_status_text'] = self::$t->_('workday_setting_status_' . $da['is_delete']);
        }
        return $data;
    }

    public function getCount()
    {
        //分页数据
        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->from(['w' => WorkdaySettingV2Model::class]);
        $audit_object->leftJoin(SysStoreModel::class, 'w.store_id = s.id', 's');
        //通用数据权限限定条件
        $audit_object = $this->buildConditions($audit_object);
        $audit_object->columns("count(1) as total");
        //查总数
        $totalInfo   = $audit_object->getQuery()->getSingleResult();
        $this->count = intval($totalInfo->total);
        return $this;
    }


    public function buildConditions($builder)
    {
        //单选
        if (!empty($this->param['region_id'])) {
            $builder->andWhere("s.manage_region = :region_id:", ['region_id' => $this->param['region_id']]);
        }
        //多选
        if (!empty($this->param['piece_id'])) {
            $builder->inWhere("s.manage_piece", $this->param['piece_id']);
        }

        //多选
        if (!empty($this->param['store_id'])) {
            $builder->inWhere("w.store_id", $this->param['store_id']);
        }

        if (!empty($this->param['start_time']) && !empty($this->param['end_time'])) {
            $builder->betweenWhere('w.effect_date', $this->param['start_time'], $this->param['end_time']);
        }

        if (!empty($this->param['store_type'])) {
            $builder->andWhere('s.category = :category:', ['category' => $this->param['store_type']]);
        }

        if (!empty($this->param['job_title'])) {
            $builder->inWhere('w.job_title', array_values($this->param['job_title']));
        }
        //0 全部，1 已删除， 2 未删除
        if (!empty($this->param['status'])) {
            $status = 0;
            if($this->param['status'] == 1){
                $status = 1;
            }
            $builder->andWhere('w.is_delete = :status:', ['status' => $status]);
        }

        return $builder;
    }


    //轮休配置 批量导入休息几天的规则 导入动作
    public function import($param)
    {
        //重复记录
        $exist = AsyncImportTaskModel::findFirst([
            'conditions' => 'import_path = :url: and operator_id = :staff_id:',
            'bind'       => [
                'url'      => $param['file_url'],
                'staff_id' => $param['user_info']['id'],
            ],
        ]);
        if ($exist) {
            throw new ValidationException('Duplicate files');
        }
        $userId = $param['user_info']['id'];
        //文件类型
        $fileName  = basename($param['file_url']);
        $fileInfo  = pathinfo($fileName);
        $localName = date('Y-m-d') . '_' . time() . $userId . '.' . $fileInfo['extension'];
        if ($fileInfo['extension'] !== "xlsx") {
            throw  new ValidationException(self::$t->_('only_xls_xlsx'));
        }
        $filePath = sys_get_temp_dir() . '/' . $localName;

        //下载到本地
        $this->downloadFile($param['file_url'], $filePath);
        $config = ['path' => dirname($filePath)];
        $excel  = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($localName)->openSheet();
        //跳过文件头
        $data = $excel->getSheetData();
        //前两行是标题
        unset($data[0]);
        unset($data[1]);
        $data = array_values($data);
        //不能超过 5000
        if (count($data) > 5000) {
            throw new ValidationException(self::$t->_('excel_limit_5000'));
        }
        if (empty(array_filter(array_column($data, '0')))) {
            throw new ValidationException(self::$t->_('21369_hcm_error_message_005'));
        }

        $json       = ['lang' => self::$language];
        $fileServer = new AsyncImportTaskService();
        $resultName = $fileServer->getResultFileName($param['file_type'], $param['user_info']['id']);
        //写数据库 async_import_task
        $model                      = new AsyncImportTaskModel();
        $insert['operator_id']      = $param['user_info']['id'];
        $insert['import_type']      = $param['file_type'];
        $insert['import_path']      = $param['file_url'];
        $insert['result_file_name'] = $resultName;
        $insert['args_json']        = json_encode($json);
        return $model->create($insert);
    }


    /**
     * 处理导入数据
     * @param $da
     * @param $createDate
     * @param $operatorId
     * @return array
     */
    protected function checkItem($da, $createDate,$operatorId)
    {
        //获取原调休日当天OT申请记录
        $db = BackyardBaseModel::beginTransaction($this);
        //是否需要全勤
        $limitDays = [0, 1, 2, 3, 4, 5, 6, 7];
        try {
            do {
                $errorMessage = '';//错误原因
                $storeId      = trim($da[0]);
                $job          = $da[1];
                $daysNum      = $da[2];//最少休息日数
                $daysNumMax   = $da[3];//最多休息日数
                $startDate    = $da[4];
                $endDate      = $da[5];
                $startDate = $startDate ? date('Y-m-d', $startDate) : null;
                $endDate = $endDate ? date('Y-m-d', $endDate) : null;
                if (empty($storeId)) {
                    $errorMessage = self::$t->_('store_id_error');
                    break;
                }
                if (empty($job)) {
                    $errorMessage = self::$t->_('job_id_error');
                    break;
                }
                if (!is_null($daysNum) && !is_numeric($daysNum)) {
                    $errorMessage = self::$t->_('limit_days_num');
                    break;
                }
                if (!is_null($daysNumMax) && !is_numeric($daysNumMax)) {
                    $errorMessage = self::$t->_('limit_days_num');
                    break;
                }
                if (is_null($daysNum) && is_null($daysNumMax)) {
                    $errorMessage = self::$t->_('max_min_days_number_error_1');
                    break;
                }
                
                if(is_numeric($daysNum) && is_numeric($daysNumMax) && $daysNum > $daysNumMax){
                    $errorMessage = self::$t->_('max_min_days_number_error_2');
                    break;
                }

                if ($daysNumMax === floatval(0)) {
                    if ((empty($startDate) || empty($endDate))) {
                        $errorMessage = self::$t->_('21841_date_error_1');
                        break;
                    }
                    if ($startDate <= date('Y-m-d',strtotime($createDate))) {
                        $errorMessage = self::$t->_('21841_date_error_1');
                        break;
                    }
                    if ($startDate > $endDate) {
                        $errorMessage = self::$t->_('21841_date_error_1');
                        break;
                    }
                }else{
                    if (!empty($startDate)) {
                        $errorMessage = self::$t->_('21841_date_error_2');//无效条件，默认生效日期为下个非全勤周的周一
                        break;
                    }
                    if (!empty($endDate)) {
                        $errorMessage = self::$t->_('21841_date_error_3');//无效条件，默认结束日期为长期
                        break;
                    }
                }
                if (!is_null($daysNum) && !in_array($daysNum, $limitDays)) {
                    $errorMessage = self::$t->_('limit_days_num');
                    break;
                }
                if (!is_null($daysNumMax) && !in_array($daysNumMax, $limitDays)) {
                    $errorMessage = self::$t->_('limit_days_num');
                    break;
                }
                //网点id 不存在
                if (empty($this->storeData[$storeId])) {
                    $errorMessage = self::$t->_('schedule_suggest_error_store');
                    break;
                }
                if (empty($this->jobTitleData[$job])) {
                    $errorMessage = self::$t->_('fsc_position_error_1');
                    break;
                }
            } while (0);
            //整理导出结果数据
            $da[4]       = $startDate;
            $da[5]       = $endDate;
            $da[6]       = $this->storeData[$storeId]['store_name'];                   //网点名称
            $da[7]       = $this->storeData[$storeId]['region_name'];                  //大区
            $da[8]       = $this->storeData[$storeId]['piece_name'];                   //片区
            $categoryKey = $categoryList[$this->storeData[$storeId]['category']] ?? '';//网点类型
            $da[9]       = self::$t->_($categoryKey);
            $da[11]      = $createDate;//更新时间 用操作导入的时间
            if ($errorMessage) {
                $db->rollback();
                $da[12] = $errorMessage;
                return [$da, true];
            }

            //如果存在excel 重复  后面的 覆盖前面的
            $insertData['job_title']    = $job;
            $insertData['store_id']     = $storeId;
            $insertData['days_num']     = $daysNum;
            $insertData['max_days_num'] = $daysNumMax;
            $insertData['effect_date']  = $startDate;
            $insertData['end_date']     = $endDate;
            $insertData['operator_id']  = $operatorId;
            $this->processData($insertData,$createDate);
            $da[4] =  $insertData['effect_date'];
            $da[12] = 'Success';
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $da[12] = $e->getMessage().$e->getTraceAsString();
            return [$da, true];
        }
        return [$da, false];
    }

  
    /**
     * 获取生效日期
     * @param $insertData
     * @param $createDate
     * @return false|string
     */
    protected function getEffectDate($insertData, $createDate)
    {
        //默认是下周一
        $effect_date = $this->getNextMonday($createDate);
        $week        = [];
        $modelJump   = WorkdaySettingV2Model::find([
            'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and max_days_num = 0 and end_date >= :end_date:',
            'bind'       => [
                'store_id'  => $insertData['store_id'],
                'job_title' => $insertData['job_title'],
                'end_date'  => $effect_date,
            ],
            'order'      => 'end_date asc',
        ])->toArray();
        if (!empty($modelJump)) {
            foreach ($modelJump as $item) {
                //看看全勤的范围涉及多少周
                while ($item['effect_date'] <= $item['end_date']) {
                    $week[]              = date('oW', strtotime($item['effect_date']));
                    $item['effect_date'] = date('Y-m-d', strtotime($item['effect_date'] . ' +1 day'));
                }
            }
        }
        if (empty($week)) {
            return $effect_date;
        }
        $week = array_unique($week);
        sort($week);
        while (true) {
            $_week = date('oW', strtotime($effect_date));
            //生效日期所在周 是全勤周，就继续往下找
            if (in_array($_week, $week)) {
                $effect_date = date('Y-m-d', strtotime($effect_date . ' +7 day'));
                continue;
            }
            break;
        }
        return $effect_date;
    }


    /**
     * 处理数据
     * @param $insertData
     * @param $createDate
     * @return true
     * @throws Exception
     */
    protected function processData(&$insertData, $createDate)
    {

        //设置全勤
        if ($insertData['max_days_num'] === floatval(0)) {
            //将前一个非全勤的数据增加结束日期
            $model = WorkdaySettingV2Model::findFirst([
                'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and (max_days_num is null or max_days_num > 0) and effect_date <= :effect_date: ',
                'bind'       => ['store_id' => $insertData['store_id'], 'job_title' => $insertData['job_title'], 'effect_date' => DateHelper::weekEnd($insertData['end_date'])],
                'order'      => 'effect_date desc',
            ]);
            if (!empty($model)) {
                $lastSunday      = date('Y-m-d', strtotime($insertData['effect_date'] . ' last sunday'));
                $model->end_date = max($lastSunday, $model->effect_date);
                if ($model->end_date == $model->effect_date) {
                    $model->is_delete = 1;
                }
                $model->save();
            }
            //清除轮休
            $this->clearOffDay($insertData['store_id'], $insertData['job_title'], $insertData['effect_date'],
                $insertData['end_date'],$insertData['operator_id']);
        } else {
            //非全勤
            //获取生效日期
            $insertData['effect_date'] = $this->getEffectDate($insertData,$createDate);
            //将前一个非全勤的数据增加结束日期
            $model = WorkdaySettingV2Model::findFirst([
                'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and (max_days_num is null or max_days_num > 0) and effect_date < :effect_date: and end_date is null',
                'bind'       => ['store_id' => $insertData['store_id'], 'job_title' => $insertData['job_title'], 'effect_date' => $createDate],
                'order'      => 'effect_date desc',
            ]);
            if (!empty($model)) {
                $model->end_date = max(DateHelper::weekEnd($createDate), $model->effect_date);
                $model->save();
            }
            //如果未来有全勤的，则将当前非全勤的数据增加结束日期
            $full_attendance_model = WorkdaySettingV2Model::findFirst([
                'conditions' => 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and max_days_num = 0 and effect_date >= :effect_date:',
                'bind'       => ['store_id' => $insertData['store_id'], 'job_title' => $insertData['job_title'], 'effect_date' => $insertData['effect_date']],
                'order'      => 'effect_date asc',
            ]);
            if (!empty($full_attendance_model)) {
                $lastSunday      = date('Y-m-d', strtotime($full_attendance_model->effect_date . ' last sunday'));
                $insertData['end_date'] = max($lastSunday, $insertData['effect_date']);
            }
        }
        //验证重复
        $conditions = 'store_id = :store_id: and job_title = :job_title: and is_delete = 0 and effect_date = :effect_date:';
        $bind       = [
            'store_id'    => $insertData['store_id'],
            'job_title'   => $insertData['job_title'],
            'effect_date' => $insertData['effect_date'],
        ];
        //全勤
        if ($insertData['max_days_num'] === floatval(0)) {
            $conditions       .= ' and end_date = :end_date:';
            $bind['end_date'] = $insertData['end_date'];
        } else {
            $conditions .= ' and (max_days_num is null or max_days_num > 0)';
        }

        $checkModel = WorkdaySettingV2Model::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'effect_date desc',
        ]);
        if (!empty($checkModel)) {
            $checkModel->days_num     = $insertData['days_num'];
            $checkModel->max_days_num = $insertData['max_days_num'];
            $checkModel->end_date     = $insertData['end_date'];
            $checkModel->save();
        } else {
            $model = new WorkdaySettingV2Model();
            $model->create($insertData);
        }

        return true;
    }

    /**
     * 清除休息日
     * @param $store_id
     * @param $job_title
     * @param $effect_date
     * @param $end_date
     * @param $operator_id
     * @return true
     * @throws Exception
     */
    protected function clearOffDay($store_id, $job_title, $effect_date, $end_date, $operator_id): bool
    {
        $staffIds = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => 'sys_store_id = :store_id: and job_title = :job_title: and formal in (1,4) and is_sub_staff = 0',
            'bind'       => [
                'store_id'  => $store_id,
                'job_title' => $job_title,
            ],
        ])->toArray();
        $staffIds = array_column($staffIds, 'staff_info_id');
        if (empty($staffIds)) {
            return true;
        }

        $offDay = HrStaffWorkDaysModel::find([
            'conditions' => 'staff_info_id in ({staff_info_id:array})  and date_at >= :effect_date: and date_at <= :end_date:',
            'bind'       => [
                'staff_info_id' => $staffIds,
                'effect_date'   => $effect_date,
                'end_date'      => $end_date,
            ],
        ])->toArray();

        if (!empty($offDay)) {
            $workDayServer = new WorkdayService();
            foreach ($offDay as $item) {
                $workDayServer->cancel($item['staff_info_id'], $item['date_at'], $operator_id);
                (new HrStaffWorkDaysChangeModel())->insert_record([
                    'month'         => date('Y-m', strtotime($item['date_at'])),
                    'staff_info_id' => $item['staff_info_id'],
                    'date_at'       => $item['date_at'],
                    'type'          => HrStaffWorkDaysChangeModel::TYPE_WORKDAY_SETTING,
                    'operator'      => $operator_id,
                ]);
                $workDayServer->staff_set_work_day_send_message($item['staff_info_id'], $item['date_at'], 1);//轮休发送消息
            }
        }

        return true;
    }



    public function uploadImport($param,$filePath, $fileName): array
    {
        //获取Excel数据
        $excelData = $this->getExcelData($filePath,
            [1 => \Vtiful\Kernel\Excel::TYPE_INT,2 => \Vtiful\Kernel\Excel::TYPE_DOUBLE,3 => \Vtiful\Kernel\Excel::TYPE_DOUBLE,4 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP, 5 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP]);
        //去重 保留行号大的数据
        $data = $fileData = [];
        $createDate = show_time_zone($param['created_at']);
        $operatorId = $param['operator_id'];
        foreach (static::yieldData()($excelData) as $key => $datum) {
            //表头
            if ($key <= 1) {
                $fileData[] = $datum;
                continue;
            }
            //导出使用
            if ($key == 2) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           //追加头
                $datum[11]  = 'Update Time';                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            //导入结果
                $datum[12] = 'Reason for error';                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          //错误原因
                $fileData[] = $datum;
                continue;
            }
            //去除多余空行
            if (empty($datum[0]) && empty($datum[1])) {
                continue;
            }

            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);
        if (!empty($data)) {
            $this->initConfig(array_values(array_unique(array_column($data, '0'))));
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError] = $this->checkItem($item,$createDate,$operatorId);
                $fileData[] = $item;
                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }
        $url = $this->makeReturnReport($fileData, $fileName);
        return ['url' => $url, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }



    protected function initConfig($storeIds)
    {
        $this->storeData = array_column((new SysStoreService())->getStoreListByIds($storeIds),null,'id');
        //所有职位
        $jobTitleData = HrJobTitleModel::find([
            'columns' => 'id,job_name',
        ])->toArray();
        $this->jobTitleData = empty($jobTitleData) ? [] : array_column($jobTitleData, 'job_name', 'id');
    }


    /**
     * 导入操作返回结果报告&导出结果报告
     * @param $fileData
     * @param $fileName
     * @param int $cellHeight
     * @param int $width
     * @return string
     * @throws OssException
     */
    public function makeReturnReport(
        $fileData,
        $fileName,
        $cellHeight = 80,
        $width = 18
    ) {
        $excel     = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
        $excel = $excel->fileName($fileName);
        $format = new \Vtiful\Kernel\Format($excel->getHandle());
        $wrapStyle = $format->wrap()->toResource();
        foreach ($fileData as $row => $rowItem) {
            foreach ($rowItem as $column => $colItem) {
                if ($row == 0) {
                    if ($column == 0) {
                        $excel->mergeCells('A1:M1', $colItem);
                        $excel->setRow('A1:M1', $cellHeight, $wrapStyle);
                        $excel->setColumn('A:M', $width);
                    } else {
                        break;
                    }
                } else {
                    $excel->insertText($row, $column, $colItem);
                }
            }
        }
        $filePath = $excel->output();

        $flashOss = new FlashOss();
        $object   = 'workday_setting/' . date('Ymd') . '/' . $fileName;
        $flashOss->uploadFile($object, $filePath, [
            OssClient::OSS_CONTENT_DISPOSTION => 'attachment',
            OssClient::OSS_CONTENT_TYPE       => OssClient::DEFAULT_CONTENT_TYPE,
        ]);
        return $object;
    }
    //导入数据 结果 任务调用
    public function importResult($param, $userId)
    {
        $fileName  = basename($param['import_path']);
        $fileInfo  = pathinfo($fileName);
        $localName = date('Y-m-d') . '_' . time() . $userId . '.' . $fileInfo['extension'];
        $filePath  = sys_get_temp_dir() . '/' . $localName;
       
        if(!file_exists($filePath)){
            //下载到本地
            $this->downloadFile($param['import_path'], $filePath);
        }

        $result  = $this->uploadImport($param,$filePath,$fileName);

        $info = AsyncImportTaskModel::findFirst($param['id']);
        if (empty($info)) {
            throw new \Exception('任务信息错误 没找到对应记录' . $param['id']);
        }

        $info->status           = AsyncImportTaskModel::STATE_EXECUTED;
        $info->result_file_name = $param['result_file_name'];
        $info->result_path      = $result['url'];
        $info->fail_number      = $result['fail_number'];
        $info->success_number   = $result['success_number'];
        $info->update();
        return true;
    }



    //列表也导出 头信息
    public function exportHeader()
    {
        //网点id 网点 大区 片区 网点类型 职位 每周轮休天数 更新时间 生效日期
        $header['store_id']            = static::$t->_('ability_store_id');
        $header['dot']                 = static::$t->_('dot');
        $header['store_area']          = static::$t->_('store_area');
        $header['store_district']      = static::$t->_('store_district');
        $header['store_category_name'] = static::$t->_('store_category_name');
        $header['job_title']           = static::$t->_('job_title');
        $header['days_num']            = static::$t->_('min_off_day_num');
        $header['max_days_num']        = static::$t->_('max_off_day_num');
        $header['refresh_time']        = static::$t->_('refresh_time');
        $header['effective_date']      = static::$t->_('effective_date');
        $header['end_date']            = static::$t->_('expiry_date');
        $header['status']              = static::$t->_('status');
        $header['delete_at']           = static::$t->_('delete_at');
        return $header;
    }

    //根据当前时间 或者 参数日期 获取下周一
    public function getNextMonday($date = '')
    {
        // 创建一个 DateTime 对象，表示当前日期
        $currentDate = new \DateTime($date);
        // 获取当前日期的星期几（1 表示星期一，7 表示星期日）
        $currentDayOfWeek = $currentDate->format('N');
        // 计算距离下周一还有多少天
        if ($currentDayOfWeek == 1) {
            // 如果今天是星期一，直接加 7 天
            $daysToAdd = 7;
        } else {
            // 否则计算到下周一的天数
            $daysToAdd = 8 - $currentDayOfWeek;
        }
        // 添加天数，得到下周一的日期
        $nextMonday = $currentDate->modify("+{$daysToAdd} days");
        // 输出下周一的日期
        return $nextMonday->format('Y-m-d');
    }


    public function getTmp($params)
    {
        $server      = new SettingEnvService();
        $data['url'] = $server->getSetVal('workday_setting_template_v2');
        return $data;
    }

    public function deleteData($params)
    {
        $deleteAll = false;
        if (!empty($params['is_delete_all'])) {
            $deleteAll = true;
        }
        $sql      = "update workday_setting_v2 set is_delete = 1";
        if ($deleteAll === false && !empty($params['ids'])) {
            $placeholders = implode(',', array_fill(0, count($params['ids']), '?'));
            $sql          .= " where id in ({$placeholders})";
            $this->db_backyard->execute($sql, $params['ids']);
        } else {
            //删除 update
            $this->db_backyard->execute($sql);
        }

        return true;
    }

    public function deleteNum()
    {
        $info = WorkdaySettingV2Model::findFirst([
            'conditions' => 'is_delete = 0',
            'columns'    => 'count(*) as num',
        ]);
        return $info->num;
    }

    //导出数据整理
    public function formatExport($data)
    {
        if (empty($data)) {
            return [];
        }
        $return = [];
        foreach ($data as $da) {
            $row['store_id']      = $da['store_id'];
            $row['store_name']    = $da['store_name'];
            $row['region_name']   = $da['region_name'];
            $row['piece_name']    = $da['piece_name'];
            $row['category_text'] = $da['category_text'];
            $row['job_name']      = $da['job_name'];
            $row['days_num']      = $da['days_num'];
            $row['max_days_num']  = $da['max_days_num'];
            $row['updated_at']    = $da['updated_at'];
            $row['effect_date']   = $da['effect_date'];
            $row['end_date']      = $da['end_date'];
            $row['status']        = $da['delete_status_text'];
            $row['deleted_at']    = $da['delete_at'];
            $return[]             = $row;
        }
        return $return;
    }


}