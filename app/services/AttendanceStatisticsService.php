<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\AttendanceEnums;
use App\Library\AttendanceUtil;
use App\Library\BaseService;
use App\Library\CacheKeyEnums;
use App\Library\DateHelper;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BusinessTripModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrShift;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\OsStaffInfoExtendModel;
use app\models\backyard\PersistentOutsourceStaffInfoModel;
use App\Models\backyard\RolesModel;
use App\Models\backyard\StaffAuditImageModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffAuditModel;
use App\Models\backyard\StaffAuditReissueForBusinessModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\AttendanceDataV2Model;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrOutsourcingOrderDetailModel;
use App\Models\backyard\HrOutsourcingOrderModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\bi\HrStaffManageRegionsModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\SysStoreModel;
use App\Models\fle\TicketDeliveryModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\HrStaffShiftHistoryModel;
use App\Modules\My\library\AttendanceStatisticsBox;
use App\Repository\HrStaffShiftMiddleDateRepository;
use App\Repository\HrStaffShiftRepository;
use App\Traits\FreeShiftTrait;
use Phalcon\Acl\Role;

class AttendanceStatisticsService extends BaseService
{
    use FreeShiftTrait;

    /**
     * 网点类型
     * @var string[]
     */
    public static $store_kind = [
        '1' => 'HUB',
        '2' => 'DC&SP',
        '3' => 'Shop',
        '4' => 'U-project',
        '5' => 'FH',
        '6' => 'BDC',
    ];

    /**
     * 网点类型
     * @var \int[][]
     */
    public static $store_kind_arr = [
        '1' => [8],
        '2' => [1, 2],
        '3' => [4],
        '4' => [7],
        '5' => [6],
        '6' => [10],
    ];

    /**
     * 查询attendance_data_v2的字段
     * @var string[]
     */
    protected $solidify_attendance_data_v2_query_field = [
        'leave_type',
        'attendance_started_at',
        'attendance_end_at',
        'stat_date',
        'staff_info_id',
        "shift_start",
        "shift_end",
        "BT",
        "BT_Y",
        'AB',
        'PH',
        'OFF',
        'job_title',
    ];

    protected $static_staff_week_working_data = [];

    /**
     * QAQC角色id
     * @var int
     */

    public $query_type = 1;//1 网点 2总部

    public $special_ph_staff      = [];
    public $special_ph_staff_2022 = [];

    public $leave_status_1 = 1;//请假审批中
    public $leave_status_2 = 2;//请假已通过
    public $leave_type_0   = 0;//全天假
    public $leave_type_1   = 1;//上午半天
    public $leave_type_2   = 2;//下午半天

    public $leave_half_time = 5 * 3600; //请半天假的 打卡时间偏移量

    /**
     * 获取员工考勤信息
     * @param array $params
     * @return array
     */
    public function getAttendanceData(array $params)
    {
        if (empty($params['staff_info_ids'])) {
            return [];
        }
        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $attendanceData = [];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($this->solidify_attendance_data_v2_query_field);
            $builder->from(['a' => AttendanceDataV2Model::class]);

            $builder->inWhere('a.staff_info_id', $staff_id);

            if (!empty($params['start_date'])) {
                $builder->andWhere('a.stat_date >= :start_date: ', ['start_date' => $params['start_date']]);
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere('a.stat_date <= :end_date: ', ['end_date' => $params['end_date']]);
            }
            $res = $builder->getQuery()->execute()->toArray();
            if (!empty($res)) {
                $attendanceData = array_merge($attendanceData, $res);
            }
        }

        if (!empty($attendanceData)) {
            $hr_shift = $this->getHrShiftInfo();
            $hr_shift = array_column($hr_shift, 'type', 'start');
            foreach ($attendanceData as &$value) {
                $value['shift_type'] = $hr_shift[$value['shift_start']] ?? '';
            }
        }
        return $attendanceData;
    }

    /**
     * 获取班次类型信息
     * @return array|mixed
     */
    public function getHrShiftInfo()
    {
        $key = CacheKeyEnums::TABLE_HR_SHIFT;
        $shiftInfo = $this->getCache($key);
        if ($shiftInfo) {
            return json_decode($shiftInfo, true);
        }
        $shiftInfo = HrShift::find()->toArray();
        $this->setCache($key, json_encode($shiftInfo), 86400);
        return $shiftInfo;
    }

    /**
     * 获取总部员工数据
     * @param $params
     * @return array
     */
    public function getHarderOfficeStaffInfo($params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->info("getHarderOfficeStaffInfo  params" . json_encode($params));

        $returnData = [
            'list'  => [],
            'total' => 0,
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as total");
        $builder->from(['h' => HrStaffInfoModel::class]);

        $builder->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j');
        $builder->leftJoin(SysDepartmentModel::class, 'h.node_department_id = dep.id', 'dep');

        $builder->where(
            "h.formal in (1, 4) and h.sys_store_id = '-1' and h.staff_info_id != 10000 and h.is_sub_staff = 0"
        );
        $builder->andWhere("((h.state in (1,3) and h.hire_date <= :end_date: ) or (h.state=2 and h.leave_date > :start_date: and h.hire_date <= :end_date: ))",['start_date'=>$params['start_date'],'end_date'=>$params['end_date']]);

        if (!empty($params['staff_info_id'])) { //工号
            $builder->andWhere('h.staff_info_id = :staff_info_id: ', ['staff_info_id' => $params['staff_info_id']]);
        }

        if (!empty($params['department'])) { //部门搜索 传参统一为department
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere('h.node_department_id', $deptIds);
            }
        }

        //在职状态
        if(! empty($params['staff_state'])){
            $tmp_sql = [];
            if(in_array(HrStaffInfoModel::STATE_PENDING_RESIGNATION,$params['staff_state'])){
                $tmp_sql[] = '( h.state = 1 and h.wait_leave_state  = 1 )';
            }
            if(in_array(HrStaffInfoModel::STATE_ON_JOB,$params['staff_state'])){
                $tmp_sql[] = '( h.state = '.HrStaffInfoModel::STATE_ON_JOB .' and h.wait_leave_state  = 0) ';
            }
            if(in_array(HrStaffInfoModel::STATE_RESIGN,$params['staff_state'])){
                $tmp_sql[] = 'h.state = '.HrStaffInfoModel::STATE_RESIGN;
            }
            if(in_array(HrStaffInfoModel::STATE_SUSPEND,$params['staff_state'])){
                $tmp_sql[] = 'h.state = '.HrStaffInfoModel::STATE_SUSPEND;
            }
            $_sql = implode(' or ',$tmp_sql);
            $builder->andWhere($_sql);
        }

        //获取 不在当地发薪不打卡名单  要整个周期都在 白名单里  5 分钟缓存
        $attendance_white_list = (new AttendanceWhiteListService())->setExpire(60 * 5)->getAllCycleWhiteListFromCache(['start_date'=>$params['start_date'],'end_date'=>$params['end_date']]);
        if(!empty($attendance_white_list['type_not_paid_locally'])){
            //只要考勤周期期间有不在「不打卡不发薪白名单」的时段就需要支持查询，如果全周期都在白名单则不能查询/导出该员工
            $builder->andWhere(
                ' h.staff_info_id  NOT IN ({not_paid_locally_white_list:array}) ',['not_paid_locally_white_list'=>$attendance_white_list['type_not_paid_locally']]
            );

        }



        //数据权限
        if (!empty($params['staff_id'])) {
            //调用hris 接口获取该用户可视范围权限（网点，部门，员工）
            $result = $this->buildStaffPurviewData($params['staff_id']);
            if ($result['condition'] && $result['bind']) {
                $builder->andWhere($result['condition'], $result['bind']);
            }
        }

        if (!empty($params['hire_type'])) {
            $builder->inWhere('h.hire_type', $params['hire_type']);
        }

        //多选职位
        if (!empty($params['job_ids']) && is_array($params['job_ids'])) {
            $builder->inWhere('h.job_title', $params['job_ids']);
        }

        $totalInfo = $builder->getQuery()->getSingleResult();

        $returnData['total'] = intval($totalInfo->total);


        if (!$returnData['total']) {
            return $returnData;
        }
        $builder->columns(
            "
                h.staff_info_id as id,
                h.staff_info_id,
                h.name,
                j.job_name,
                h.sys_department_id as organization_id,
                h.sys_department_id,
                h.job_title,
                h.node_department_id,
                dep.name as node_department_name,
                h.hire_date,
                h.hire_type,
                h.leave_date,
                h.state,
                h.wait_leave_state,
                h.stop_duties_date,
                h.sex,
                h.job_title_grade_v2,
                h.nationality,
                ''as province_name,
                '' as province_code,
                '' as manage_geography_code,
                '' as manage_region,
                '' as manage_piece,
                '' as store_name,
                '' as store_no,
                '' as store_category,
                h.sys_store_id,
                h.week_working_day");
        if(isset($params['page']) && isset($params['size'])){
            $page = intval($params['page']);
            $size = intval($params['size']);
            $offset = $size * ($page - 1);
            $builder->limit($size, $offset);
        }
        $returnData['list'] = $builder->getQuery()->execute()->toArray();

        return $returnData;
    }

    /**
     * 获取网点员工数据
     * @param $params
     * @return array
     */

    public function getStoreStaffInfo($params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->info("getStoreStaffInfo  params" . json_encode($params));

        $returnData = [
            'list'  => [],
            'total' => 0,
        ];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as total");
        $builder->from(['h' => HrStaffInfoModel::class]);

        $builder->leftJoin(HrJobTitleModel::class, 'h.job_title = j.id', 'j');
        $builder->leftJoin(SysStoreModel::class, 'h.sys_store_id = s.id', 's');
        $builder->leftJoin(SysProvinceModel::class, 's.province_code = p.code', 'p');

        $builder->where("h.formal in (1, 4) and h.sys_store_id <> '-1' and h.is_sub_staff = 0 ");
        $builder->andWhere("((h.state in (1,3) and h.hire_date <= :end_date: ) or (h.state=2 and h.leave_date > :start_date: and h.hire_date <= :end_date: ))",['start_date'=>$params['start_date'],'end_date'=>$params['end_date']]);

        $builder = $this->getStoreStaffRegion($builder, $params);

        if (!empty($params['staff_info_id'])) { //工号
            $builder->andWhere('h.staff_info_id = :staff_info_id: ', ['staff_info_id' => $params['staff_info_id']]);
        }

        if (!empty($params['store_id'])) { //网点
            $builder->inWhere('h.sys_store_id ', $params['store_id']);
        }

        //在职状态
        if(! empty($params['staff_state'])){
            $tmp_sql = [];
            if(in_array(HrStaffInfoModel::STATE_PENDING_RESIGNATION,$params['staff_state'])){
                $tmp_sql[] = '( h.state = 1 and h.wait_leave_state  = 1 )';
            }
            if(in_array(HrStaffInfoModel::STATE_ON_JOB,$params['staff_state'])){
                $tmp_sql[] = '( h.state = '.HrStaffInfoModel::STATE_ON_JOB .' and h.wait_leave_state  = 0) ';
            }
            if(in_array(HrStaffInfoModel::STATE_RESIGN,$params['staff_state'])){
                $tmp_sql[] = 'h.state = '.HrStaffInfoModel::STATE_RESIGN;
            }
            if(in_array(HrStaffInfoModel::STATE_SUSPEND,$params['staff_state'])){
                $tmp_sql[] = 'h.state = '.HrStaffInfoModel::STATE_SUSPEND;
            }
            $_sql = implode(' or ',$tmp_sql);
            $builder->andWhere($_sql);
        }

        if (!empty($params['piece_id'])) { // 片区
            $builder->andWhere('s.manage_piece in ({piece_id:array}) ', ['piece_id' => $params['piece_id']]);
        }

        if (!empty($params['region_id'])) { // 大区
            $builder->andWhere('s.manage_region = :region_id: ', ['region_id' => $params['region_id']]);
        }

        if (!empty($params['store_kind'])) { // 网点类型
            $builder->andWhere(
                's.category in ({store_kind:array}) ',
                ['store_kind' => is_array($params['store_kind']) ? array_values($params['store_kind']) : explode(",", $params['store_kind'])]
            );
        }



        //获取 不在当地发薪不打卡名单  要整个周期都在 白名单里  5 分钟缓存
        $attendance_white_list = (new AttendanceWhiteListService())->setExpire(60 * 5)->getAllCycleWhiteListFromCache(['start_date'=>$params['start_date'],'end_date'=>$params['end_date']]);
        if(!empty($attendance_white_list['type_not_paid_locally'])){
            //只要考勤周期期间有不在「不打卡不发薪白名单」的时段就需要支持查询，如果全周期都在白名单则不能查询/导出该员工
            $builder->andWhere(
                ' h.staff_info_id  NOT IN ({not_paid_locally_white_list:array}) ',['not_paid_locally_white_list'=>$attendance_white_list['type_not_paid_locally']]
            );

        }


        if (!empty($params['staff_id'])) {
            //调用hris 接口获取该用户可视范围权限（网点，部门，员工）
            $result = $this->buildStaffPurviewData($params['staff_id']);
            if ($result['condition'] && $result['bind']) {
                $builder->andWhere($result['condition'], $result['bind']);
            }
        }
        if (!empty($params['hire_type'])) {
            $builder->inWhere('h.hire_type', $params['hire_type']);
        }

        //多选职位
        if (!empty($params['job_ids']) && is_array($params['job_ids'])) {
            $builder->inWhere('h.job_title', $params['job_ids']);
        }
        $totalInfo = $builder->getQuery()->getSingleResult();

        $returnData['total'] = intval($totalInfo->total);

        if (!$returnData['total']) {
            return $returnData;
        }

        $builder->columns(
            "
                h.staff_info_id as id,
                h.name,
                j.job_name,
                h.sys_store_id as organization_id,
                h.sys_store_id ,
                h.hire_date,
                h.leave_date,
                h.hire_type,
                h.state,
                h.sex,
                h.wait_leave_state,
                h.stop_duties_date,
                p.name as province_name,
                p.code as province_code,
                p.manage_geography_code,
                s.manage_region as manage_region,
                s.manage_piece as manage_piece,
                s.name as store_name,
                s.store_no as store_no,
                s.sorting_no,
                s.category as store_category,
                '' AS node_department_name,
                h.week_working_day,
                h.job_title,
                h.node_department_id,
                h.staff_info_id,
                h.job_title_grade_v2,
                h.nationality
                "
        );
        if (isset($params['page']) && isset($params['size'])) {
            $page = intval($params['page']);
            $size = intval($params['size']);
            $offset = $size * ($page - 1);
            $builder->limit($size, $offset);
        }

        $returnData['list'] = $builder->getQuery()->execute()->toArray();

        return $returnData;
    }

    /**
     * 与马来、老挝有差异化
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getStoreStaffRegion($builder, $params)
    {

        if (!empty($params['geography_code'])) { // 区域
            $builder->andWhere(
                'p.manage_geography_code in ({geography_code:array})',
                ['geography_code' => $params['geography_code']]
            );
        }
        return $builder;
    }


    public function buildStaffPurviewData($staff_info_id, $pre = 'h')
    {
        $return = ['condition' => '', 'bind' => []];

        if (env('sa_id') == $staff_info_id) {
            return $return;
        }

        //调用hris 接口获取该用户可视范围权限（网点，部门，员工）
        $purview = (new StaffInfoService())->StaffDataPurviewV2($staff_info_id);


        $tmp_where = '';
        $tmp_bind = [];
        if ($purview && isset($purview['stores']) && $purview['stores']) {
            $areaStores = array_values(array_unique(array_filter($purview['stores'])));
            if ($areaStores) {
				if(in_array('-2',$areaStores)) {
					$tmp_where                        .= " {$pre}.sys_store_id  != :purview_sys_store_id:";
					$tmp_bind['purview_sys_store_id'] = '-1';
				}else{
					$tmp_where .= " {$pre}.sys_store_id in ({purview_sys_store_id:array})";
					$tmp_bind['purview_sys_store_id'] = $areaStores;
				}
            }
        }
        if ($purview && isset($purview['departments']) && $purview['departments']) {
            $departmentIds = array_values(array_unique(array_filter($purview['departments'])));
            if ($departmentIds) {
                $tmp_where .= $tmp_where ? ' or ' : ' ';
                $tmp_where .= "  {$pre}.node_department_id in ({purview_node_department_id_2:array}) ";
                $tmp_bind['purview_node_department_id_2'] = $departmentIds;
            }
        }

        if ($purview && isset($purview['flash_home_departments']) && $purview['flash_home_departments']) {
            $departmentIds = array_unique(array_filter($purview['flash_home_departments']));
            if ($departmentIds) {
                $tmp_where .= $tmp_where ? ' or ' : ' ';
                $tmp_where .= " {$pre}.sys_store_id = :flash_home_departments_store_id: and  {$pre}.node_department_id in ({flash_home_departments_ids:array}) ";
                $tmp_bind['flash_home_departments_store_id'] = '-1';
                $tmp_bind['flash_home_departments_ids'] = array_values($departmentIds);

            }
        }

        if ($purview && isset($purview['staff_ids']) && $purview['staff_ids']) {
            $staffIds = array_values(array_unique(array_filter($purview['staff_ids'])));
            if ($staffIds) {
                $tmp_where .= $tmp_where ? ' or ' : ' ';
                $tmp_where .= " {$pre}.staff_info_id in ({purview_staff_info_id:array})";
                $tmp_bind['purview_staff_info_id'] = $staffIds;
            }
        }

        return ['condition' => $tmp_where, 'bind' => $tmp_bind];
    }

    /**
     * 获取请假信息
     * @param array $params
     * @return array
     */
    public function getStaffAuditInfo(array $params)
    {
        $returnData = [];

        if (empty($params['staff_info_ids'])) {
            return $returnData;
        }
        $staff_ids = array_chunk($params['staff_info_ids'], 500);

        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                "
                    a.staff_info_id,
                    s.date_at,
                    a.audit_reason,
                    a.leave_type,
                    group_concat(i.image_path) as image_path,
                    a.status,
                    s.type
                "
            );

            $builder->from(['s' => StaffAuditLeaveSplitModel::class]);

            $builder->leftJoin(StaffAuditModel::class, 'a.audit_id = s.audit_id', 'a');

            $builder->leftJoin(StaffAuditImageModel::class, 'a.audit_id=i.audit_id', 'i');

            $builder->where("a.audit_type = 2 and a.status in (1,2) and a.parent_id = 0"); //查询申请中和已通过的

            if (!empty($params['start_date'])) { //开始时间
                $builder->andWhere('s.date_at >= :start_time:', ['start_time' => $params['start_date']]);
            }
            if (!empty($params['end_date'])) { //截止时间
                $builder->andWhere('s.date_at <= :end_time:', ['end_time' => $params['end_date']]);
            }

            $builder->inWhere('a.staff_info_id',$staff_id);
            $builder->groupBy("a.audit_id, s.date_at, s.type");
            $res = $builder->getQuery()->execute()->toArray();
            $returnData = array_merge($res,$returnData);
        }

        return $returnData;
    }

    /**
     * 将请假数据 格式化一下
     * @param array $auditData
     * @return array
     */
    protected function handleAuditDataUseToSelf(array $auditData)
    {
        $returnData = [];
        if (empty($auditData)) {
            return $returnData;
        }
        $list = array_chunk($auditData,300);
        foreach($list as $d_list){
            foreach ($d_list as $key => $value) {
                $returnData[$value['staff_info_id']][$value['date_at']]['leave_type'][] = $value['leave_type'];
                $returnData[$value['staff_info_id']][$value['date_at']]['reason'] = $value['audit_reason'];
                if(empty($returnData[$value['staff_info_id']][$value['date_at']]['image_path'])){
                    $returnData[$value['staff_info_id']][$value['date_at']]['image_path'] = $value['image_path'];
                }else{
                    $returnData[$value['staff_info_id']][$value['date_at']]['image_path'] .= ',' . $value['image_path'];
                }
                //去重重复的假期
                $returnData[$value['staff_info_id']][$value['date_at']]['leave_arr'][$value['leave_type'] . '-' . $value['status'] . '-' . $value['type']] = [
                    'leave_type' => $value['leave_type'],//请假类型
                    'status'     => $value['status'],    //请假审批状态
                    'type'       => $value['type'],      //请假日期类型 0-一天 1-上午 2-下午'
                ];
            }
        }

        return $returnData;
    }


    /**
     * @description: 获取出差打卡数据
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/2/25 12:15
     */
    public function getStaffAuditReissueInfo(array $params)
    {
        $returnData = [];

        if (empty($params['staff_info_ids'])) {
            return $returnData;
        }
        $staff_chunk_ids = array_chunk($params['staff_info_ids'], 500);
        foreach($staff_chunk_ids as $staff_ids){
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                '
                   s.staff_info_id,
                   s.attendance_date,
                   s.start_time,
                   s.end_time,
                   s.status,
                   s.start_shift,
                   s.end_shift,
                   s.shift_id,
                   s.start_time_zone,
                   s.end_time_zone
                '
            );

            $builder->from(['s' => StaffAuditReissueForBusinessModel::class]);

            $builder->where('s.status in (1,2)'); //查询申请中和已通过的

            if (!empty($params['start_date'])) { //开始时间
                $builder->andWhere('s.attendance_date >= :start_time:', ['start_time' => $params['start_date']]);
            }
            if (!empty($params['end_date'])) { //截止时间
                $builder->andWhere('s.attendance_date <= :end_time:', ['end_time' => $params['end_date']]);
            }
            if (!empty($staff_ids)) {
                $builder->inWhere('s.staff_info_id', $staff_ids);
            }
            $res = $builder->getQuery()->execute()->toArray();
            $returnData[] = $res;
        }

        $returnData = array_merge(...$returnData);

        return $returnData;
    }


    /**
     * 将出差打卡数据格式化一下
     * @param array $auditData
     * @return array
     */
    protected function handleAuditReissueDataUseToSelf(array $auditData)
    {
        $returnData = [];
        if (empty($auditData)) {
            return $returnData;
        }
        //获取班次
        $hr_shift = $this->getHrShiftInfo();
        $hr_shift = array_column($hr_shift, 'type', 'start');

        foreach ($auditData as $key => $value) {
            $start_time_zone = is_null($value['start_time_zone']) ? $this->timeOffset : $value['start_time_zone'];
            $end_time_zone = is_null($value['end_time_zone']) ? $this->timeOffset : $value['end_time_zone'];

            $returnData[$value['staff_info_id']][$value['attendance_date']] = [
                'start_time' => empty($value['start_time']) ? '' : date(
                    'Y-m-d H:i:s',
                    strtotime(
                        $value['start_time']
                    ) + $start_time_zone * 3600
                ),//上班卡
                'end_time'   => empty($value['end_time']) ? '' : date(
                    'Y-m-d H:i:s',
                    strtotime($value['end_time']) + $end_time_zone * 3600
                ),                               //下班
                'status'     => $value['status'],//审批类型
                'start_shift' => $value['start_shift'] ?? '', //班次开始时间
                'end_shift'   => $value['end_shift'] ?? '',   //班次结束时间
                'shift_type' => $hr_shift[$value['start_shift']] ?? '',//班次类型

            ];
        }
        return $returnData;
    }

    /**
     * 获取弹性班次配置
     * @return array
     */
    public function getFreeShiftConfig(): array
    {
        return \App\Models\backyard\SettingEnvModel::getMultiEnvByCode([
            'free_shift_position',
            'day_shift_duration',
            'night_shift_duration',
        ]);
    }



    /**
     * 获取员工考勤数据
     * @param $params
     */
    public function getStaffsAttendanceInfo($params)
    {
        if (empty($params['start_date'])) {
            throw new ValidationException('start_date empty');
        }

        if (empty($params['end_date'])) {
            throw new ValidationException('end_date empty');
        }

        $returnData = [
            'total' => 0,
            'list'  => [],
        ];
        //获取员工信息
        if ($this->query_type == 2) {
            $staffs = $this->getHarderOfficeStaffInfo($params);
            if (!$staffs['list']) {
                return $returnData;
            }
        } else {
            $staffs = $this->getStoreStaffInfo($params);
        }
        if (!$staffs['list']) {
            return $returnData;
        }
        [$returnData['total'], $list] = [$staffs['total'], $staffs['list']];
        //获取考勤信息

        $params['staff_info_ids'] = array_column($list, 'id');

        //获取考勤信息
        $attendanceData = $this->getAttendanceData($params);
        //获取请假信息
        $apply_info = $this->getStaffAuditInfo($params);

        //获取出差打卡数据
        $reissue_info = $this->getStaffAuditReissueInfo($params);

        //出差
        $backyard_attendance_bll = new BackyardAttendanceService();
        $trip_where = " and apply_user in ({apply_user:array}) ";
        $trip_where_params['apply_user'] = $params['staff_info_ids'] ?? [0];

        $trip_data = $backyard_attendance_bll->getTripData(
            $params['start_date'] ?? '',
            $params['end_date'] ?? '',
            $trip_where,
            $trip_where_params
        );

        //用固化表的数据是最好的
        $this->getStaffWeekWorkingDay($params);

        $staff_week_working_data = array_column($list, 'week_working_day', 'id');

        //获取固化的员工班次信息
        $staff_shift_history_data = $this->getShiftByDate($params);
        //构建考情数据
        $attendanceData = $this->handleAttendanceDataToViewV2(
            $params,
            $attendanceData,
            $staff_week_working_data,
            $apply_info,
            $reissue_info,
            $trip_data,
            $staff_shift_history_data
        );
        $this->logger->info("AttendanceStatisticsExportTask before handelAttendanceOtherInfo");

        $returnData['list'] = $this->handelAttendanceOtherInfo($list, $attendanceData, $params);
        $this->logger->info("AttendanceStatisticsExportTask after handelAttendanceOtherInfo");

        return $returnData;
    }

    /**
     * 从固化表获取员工的工作制
     * @param $params
     */
    public function getStaffWeekWorkingDay($params)
    {
        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $staticData = [];
        foreach ($staff_ids as $staff_id) {

            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['week_working_day', 'staff_info_id', 'stat_date','province_code','job_title', 'node_department_id', 'store_id', 'hire_type']);
            $builder->from(['a' => HrStaffTransferModel::class]);

            $builder->inWhere('a.staff_info_id', $staff_id);

            if (!empty($params['start_date'])) {
                $builder->andWhere('a.stat_date >= :start_date: ', ['start_date' => $params['start_date']]);
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere('a.stat_date <= :end_date: ', ['end_date' => $params['end_date']]);
            }
            if ($res = $builder->getQuery()->execute()->toArray()) {
                foreach ($res as $re) {
                    if(isCountry('MY')){
                        $staticData[$re['staff_info_id'].'-'.$re['stat_date']] = $re;
                    }else{
                        $this->static_staff_week_working_data[$re['staff_info_id']][$re['stat_date']] = $re['week_working_day'];
                    }
                }
            }
        }
        return $staticData;
    }

    /**
     * 整理员工信息
     * @param $staff_list
     * @param $attendanceData
     * @param $params
     * @return mixed
     */
    protected function handelAttendanceOtherInfo($staff_list, $attendanceData, $params)
    {
        $sysService = new SysService();
        $region_info = array_column($sysService->getRegionListFromCache(), 'name', 'id');
        $piece_info = array_column($sysService->getPieceListFromCache(), 'name', 'id');
        $store_info = array_column($sysService->getStoreListFromCache(), 'name', 'id');
        //网点区域
	    $sorting_no_map =  array_column( $sysService->storeGeographyCodeFromCache(),'label','value');
	    //网点类型
	    $store_category_map =  SysStoreService::$category;

        foreach ($staff_list as &$item) {
            $item['attend_data'] = $attendanceData[$item['id']] ?? $this->getEmptyAttendance($params);
            $item['manage_region'] = $item['manage_region'] ? $region_info[$item['manage_region']] : '';
            $item['manage_piece'] = $item['manage_piece'] ? $piece_info[$item['manage_piece']] : '';

            $item['state'] = $item['wait_leave_state'] == 1 && in_array($item['state'], [1, 2]) ? self::$t->_(
                'wait_leave_state_1'
            ) : self::$t->_('staff_state_' . $item['state']);
            $item['store_name'] = $this->query_type == 1 ? ($store_info[$item['organization_id']] ?? '') : '';

            foreach ($item['attend_data'] as $attend_datum) {
                //统计旷工
                $item['absenteeism_num'] += $attend_datum['absenteeism_num']??0;
                //迟到统计
                $item['late_num'] += $attend_datum['late_num']??0;
                //迟到分钟数统计
                $item['late_time'] += $attend_datum['late_time']??0;
                //早退统计

                $item['leave_early_num'] += $attend_datum['leave_early_num']??0;
                //早退分钟数
                $item['leave_early_time'] += $attend_datum['leave_early_time']??0;
            }
//
//	        $item['absenteeism_num'] = array_sum(array_column($item['attend_data'], 'absenteeism_num'));
//	        //迟到统计
//	        $item['late_num'] = array_sum(array_column($item['attend_data'], 'late_num'));
//	        //迟到分钟数统计
//	        $item['late_time'] = array_sum(array_column($item['attend_data'], 'late_time'));
//	        //早退统计
//	        $item['leave_early_num'] = array_sum(array_column($item['attend_data'], 'leave_early_num'));
//	        //早退分钟数
//	        $item['leave_early_time'] = array_sum(array_column($item['attend_data'], 'leave_early_time'));
//
	        //网点区域
	        $item['manage_geography_code_name'] = isset($item['manage_geography_code']) ? ($sorting_no_map[$item['manage_geography_code']] ?? '') : '';
	        //网点类型             s.category as store_category,
	        $item['store_category_name'] = isset($item['store_category']) ? ($store_category_map[$item['store_category']] ?? '') : '';
	        unset($item['sorting_no']);
        }
        return $staff_list;
    }


    /**
     * 获取轮休日
     *
     * @param $start_date
     * @param $end_date
     * @param $staff_info_ids
     * @return array
     */
    public function getStaffOffDayData($start_date, $end_date, $staff_info_ids)
    {
        $staff_ids = array_chunk($staff_info_ids, 400);
        $offDay = [];
        foreach ($staff_ids as $staff_id) {
            $hr_staff_work_days = \App\Models\backyard\HrStaffWorkDaysModel::class;
            $leave_sql = "select type, staff_info_id,date_at,concat(staff_info_id,'-',date_at) as unique_key
            from {$hr_staff_work_days} where  date_at >= :start_date: and date_at <= :end_date: and staff_info_id in ({staff_info_ids_str:array}) ";
            $where_params['start_date'] = $start_date;
            $where_params['end_date'] = $end_date;
            $where_params['staff_info_ids_str'] = $staff_id;
            $re = $this->modelsManager->executeQuery($leave_sql, $where_params)->toArray();
            if ($re) {
                $offDay = array_merge($offDay, $re);
            }
        }

        return empty($offDay) ? [] : array_column($offDay, null, 'unique_key');
    }

    /**
     * 构建考勤数据
     * @param array $params 日期等参数
     * @param array $attendanceData 考勤信息
     * @param array $staff_week_working_data 员工工作制信息
     * @param array $apply_info 请假信息
     * @param array $reissue_info 出差打卡数据
     * @param array $trip_data 出差数据
     * @param array $staff_shift_history_data 历史班次信息
     * @return array
     */
    protected function handleAttendanceDataToViewV2(
        array $params,
        array $attendanceDataAll,
        array $staff_week_working_data,
        array $apply_info,
        array $reissue_info = [],
        array $trip_data = [],
        array $staff_shift_history_data = [],
        array $staticStaffInfoData = []
    ) {
        $returnData = [];
        if (empty($attendanceDataAll)) {
            return $returnData;
        }
        //组合假期
        $holiday = (new HolidayService())->getHoliday(['date'=>$params['start_date']]);
        $day5_holidays = $holiday[HrStaffInfoModel::WEEK_WORKING_DAY_FIVE];
        $day6_holidays = $holiday[HrStaffInfoModel::WEEK_WORKING_DAY_SIX];

        //员工个人的ph
        $staffAddPublicHoliday = (new StaffPublicHolidayService())->getMultiStaffData($params['staff_info_ids'],$params['start_date']);


        $date_range = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));

        //请假数据
        $apply_info = !empty($apply_info) ? $this->handleAuditDataUseToSelf($apply_info) : [];

        //出差打卡数据

        $reissue_info = !empty($reissue_info) ? $this->handleAuditReissueDataUseToSelf($reissue_info) : [];
        //出差
        $trap_data2date = AttendanceUtil::btSection2Date($date_range, $trip_data);

        //历史班次信息
        $staff_shift_history_data = !empty($staff_shift_history_data) ? $this->handleShiftByDate($staff_shift_history_data) : [];

        //获取现在员工班次
        $hr_staff_shift_data = $this->getStaffShift($params);

        //支援信息
        $supportServer = new StaffSupportStoreServer();
        $supportData = $supportServer->getSupportDataBetween($params['staff_info_ids'], $params['start_date'], $params['end_date']);
        $supportData = $supportServer->formatSupportData($supportData, $params['start_date'], $params['end_date']);
        $supportText = self::$t->_("on_support");


        //班员工休息日
        $staffOffDay = $this->getStaffOffDayData(
            $params['start_date'],
            $params['end_date'],
            $params['staff_info_ids']
        );
        $free_shift_config = $this->getFreeShiftConfig();
        $this->setFreeShiftInfo($free_shift_config);
        //请假枚举
        $leave_type_config = AttendanceEnums::$leave_type;

        $attendanceDataAll = array_chunk($attendanceDataAll,500);

        foreach ($attendanceDataAll as $attendanceData) {

            foreach ($attendanceData as $value) {

                $type = "";
                $reason = "";
                $image_path = "";
                $background = 0;
                $start_at = self::$t->_("lack_of_work_card");//默认缺卡
                $end_at = self::$t->_("lack_of_work_card");  //默认缺卡
                $leave_type = [];
                $_leave_type = [];
                $holiday_str = '';
                $is_punch_in = 0;
                $is_punch_out = 0;
                //计算旷工天数
                $absenteeism_num = 0;
                //计算迟到次数
                $late_num = 0;
                //计算迟到时长
                $late_time = 0;
                //计算早退次数
                $leave_early_num = 0;
                //计算早退时长
                $leave_early_time = 0;
                //出差状态拼接
                $business_travel = '';
                //优先使用固化表的工作制
                $week_working_day = $this->static_staff_week_working_data[$value['staff_info_id']][$value['stat_date']] ?? $staff_week_working_data[$value['staff_info_id']];
                $_day5_holidays = $day5_holidays;
                $_day6_holidays = $day6_holidays;
                if($week_working_day == \App\Models\backyard\HrStaffInfoModel::WEEK_WORKING_DAY_5){
                    $_day5_holidays = array_merge($day5_holidays,$staffAddPublicHoliday[$value['staff_info_id']]??[]);
                }else{
                    $_day6_holidays = array_merge($day6_holidays,$staffAddPublicHoliday[$value['staff_info_id']]??[]);
                }

                if (!empty($value['shift_start']) && strlen($value['shift_start']) < 5) {
                    $value['shift_start'] = '0' . $value['shift_start'];
                }
                if (!empty($value['shift_end']) && strlen($value['shift_end']) < 5) {
                    $value['shift_end'] = '0' . $value['shift_end'];
                }
                //如果当v 2 表里班次信息不存在 则尝试获取固化班次信息
                $value['shift_start'] = empty($value['shift_start']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['start']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['start'] : $value['shift_start'];
                $value['shift_end']   = empty($value['shift_end']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['end']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['end'] : $value['shift_end'];
                $value['shift_type']  = empty($value['shift_type']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_type']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_type'] : $value['shift_type'];

                //如果固化表也没有获取 hr_staff_shift 表班次
                $value['shift_start'] = empty($value['shift_start']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_start'] : $value['shift_start'];
                $value['shift_end']   = empty($value['shift_end']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_end'] : $value['shift_end'];
                $value['shift_type']  = empty($value['shift_type']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_type'] : $value['shift_type'];


                //出差打卡 重新赋值打卡时间和班次
                $value = $this->assignReissueAttendanceData($reissue_info, $value);
                $business_travel = $value['business_travel'] ?? '';

                $s1 = !empty($value['shift_start']) ? $value['shift_start'] : '08:00';       //上班班次
                $s2 = !empty($value['shift_end']) ? $value['shift_end'] : '17:00';           //下班班次
                $check_start = date('Y-m-d H:i', strtotime($value['attendance_started_at']));//上班打卡时间
                $shift_start = "{$value['stat_date']} {$s1}";
                $shift_start = date('Y-m-d H:i', strtotime($shift_start));//班次开始时间

                $check_end = date('Y-m-d H:i', strtotime($value['attendance_end_at']));//下班打卡时间
                $shift_end = "{$value['stat_date']} {$s2}";
                $shift_end = date('Y-m-d H:i', strtotime($shift_end));
                if ($s1 > $s2) {
                    $shift_end = date('Y-m-d H:i', strtotime("{$shift_end} +1 day"));
                }

                //$background 0 白色  1 灰色 2 黄色  3 红色

                //上班打卡
                if (!empty($value['attendance_started_at'])) {
                    //迟到
                    $is_punch_in = 10;
                    //打卡时间大于班次时间 是迟到
                    if ($check_start > $shift_start) {
                        $background = 2; //黄色
                        $is_punch_in = 5;
                    }

                    $start_at = date('H:i', strtotime($value['attendance_started_at'])) . $business_travel;
                }

                if (!empty($value['attendance_end_at'])) {
                    //早退
                    $is_punch_out = 10;
                    if ($check_end < $shift_end) {
                        $background = 2;
                        $is_punch_out = 5;
                    }

                    $end_at = date('H:i', strtotime($value['attendance_end_at'])) . $business_travel;
                }

                //正常打卡
                if ($is_punch_out == 10 && $is_punch_in == 10) {
                    $background = 0;
                }

                //班次为空 给红色
                if (($is_punch_out || $is_punch_in) && (empty($value['shift_start']) || empty($value['shift_end'])) && !$this->isFreeShiftJobTitle($value['job_title'])) {
                    $background = 3;
                }

                // AB
                if (empty($is_punch_out) && empty($is_punch_in) && !empty($value['AB'])) {
                    $type = self::$t->_("absenteeism");//这是旷工;
                    $background = 3;                   //缺勤
                    $absenteeism_num = 1;              //记录一天旷工
                }

                //如果没有上班卡 或者没有下班卡 就是缺卡 要显示红色
                if (empty($is_punch_out) || empty($is_punch_in)) {
                    $background = 3;   //缺卡
                }


                //6天班公共假期
                if (in_array($value['stat_date'], $_day6_holidays) && $week_working_day == 6) {
                    $holiday_str = "PH";
                    $background = 1;
                    $absenteeism_num = 0;//旷工天数

                   if (!isset($staffOffDay[$value['staff_info_id'] . '-' . $value['stat_date']]) && ($is_punch_out == 0 && $is_punch_in == 0)) {
                        //不是休息日 并且没有打上班卡和下班卡
                        $background = 3;
                        $type = self::$t->_("absenteeism_working_day_6");//这是安排加班,未出勤;
                        $absenteeism_num = 1;
                    }
                }

                //5天班公共假期
                if (in_array($value['stat_date'], $_day5_holidays) && $week_working_day != 6) {
                    $holiday_str = "PH";
                    $background = 1;
                    $absenteeism_num = 0;//旷工天数
                }


                //休息日
                if (isset($staffOffDay[$value['staff_info_id'] . '-' . $value['stat_date']])) {
                    $background = 1;
                    $type = self::$t->_('2017');
                    $absenteeism_num = 0;//旷工天数
                }


                // 注意从这里开始 type 是.=
                //判断是否请假
                //请假图片
                //如果请假了
                $applyLeaveType = NULL;//没请假
                if (isset($apply_info[$value['staff_info_id']]) && isset($apply_info[$value['staff_info_id']][$value['stat_date']])) {
                    //看当前日期请的是什么假
                    $leave_type_arr = [];
                    foreach ($apply_info[$value['staff_info_id']][$value['stat_date']]['leave_arr'] as $leave) {
                        // 假期类型  -- 请假天数 -- (如果在审批中 则显示审批中 否则不显示)
                        $_leave_type[] = self::$t->_(
                                $leave_type_config[(int)$leave['leave_type']] ?? 'undefined leave type - ' . (int)$leave['leave_type']
                            ) . //假期类型
                            self::$t->_('leave_day_' . (int)$leave['type']) . //请假天数
                            ($leave['status'] == $this->leave_status_1 ? self::$t->_(
                                'leave_status_' . (int)$leave['status']
                            ) : ''); //  (如果在审批中 则显示审批中 否则不显示)
                        //看请假是否申请通过
                        //如果通过
                        if ($leave['status'] == $this->leave_status_2) {
                            $leave_type_arr[] = $leave['type'];
                            $absenteeism_num = 0;                             //旷工天数
                            $background = $background == 2 ? 0 : $background; //如果是黄色 迟到早退 重新计算颜色

                            //如果是请了一天假或者 请了上午和下午
                            if ($leave['type'] == $this->leave_type_0 || empty(
                                array_diff(
                                    [$this->leave_type_1, $this->leave_type_2],
                                    $leave_type_arr
                                )
                                )) {
                                $background = 1; // 考勤变灰
                                $type = '';
                                //如果是上午半天假 && 并且打了上班卡和下班卡
                                $applyLeaveType = $this->leave_type_0;
                            } else {
                                if ($leave['type'] == $this->leave_type_1 && !empty($is_punch_in) && !empty($is_punch_out)) {
                                    //需要判断下午上班打卡是否正常
                                    //首先把上午打卡时间 推后 5 小时
                                    $shift_start = date('Y-m-d H:i', (strtotime($shift_start) + $this->leave_half_time));
                                    $is_punch_in = 10;
                                    $is_punch_out = 10;
                                    //然后比较
                                    //打卡时间大于班次时间 是迟到
                                    if ($check_start > $shift_start) {
                                        $background = 2; //黄色
                                        $is_punch_in = 5;
                                    }
                                    //打卡时间小于班次时间 是早退
                                    if ($check_end < $shift_end) {
                                        $background = 2;
                                        $is_punch_out = 5;
                                    }
                                    $applyLeaveType = $this->leave_type_1;

                                    //如果是下午半天假 && 并且打了上班卡和下班卡
                                } else {
                                    if ($leave['type'] == $this->leave_type_2 && !empty($is_punch_in) && !empty($is_punch_out)) {
                                        //需要判断上午下班打卡是否正常
                                        //首先把下班打卡时间提前五小时
                                        $shift_end = date('Y-m-d H:i', (strtotime($shift_end) - $this->leave_half_time));
                                        $is_punch_in = 10;
                                        $is_punch_out = 10;
                                        //然后比较
                                        //打卡时间大于班次时间 是迟到
                                        if ($check_start > $shift_start) {
                                            $background = 2; //黄色
                                            $is_punch_in = 5;
                                        }
                                        //打卡时间小于班次时间 是早退
                                        if ($check_end < $shift_end) {
                                            $background = 2;
                                            $is_punch_out = 5;
                                        }
                                        $applyLeaveType = $this->leave_type_2;

                                    }
                                }
                            }
                        }
                    }

                    //休息日
                    if (isset($staffOffDay[$value['staff_info_id'] . '-' . $value['stat_date']])) {
                        $background = 1;
                        $type = self::$t->_('2017');
                        $absenteeism_num = 0;//旷工天数
                    }

                    $type .= '  ' . implode(' ', $_leave_type);
                    $reason = $apply_info[$value['staff_info_id']][$value['stat_date']]['reason'] ?: '';        //请假的原因
                    $image_path = $apply_info[$value['staff_info_id']][$value['stat_date']]['image_path'] ?: '';//图片
                    if (!empty($image_path)) {
                        $imgData    = explode(',', $image_path);
                        $imgData    = array_unique($imgData);
                        $image_path = implode(',', $imgData);
                    }
                }

                //判断是否出差
                if (isset($trap_data2date[$value['staff_info_id'] . '-' . $value['stat_date']])) {
                    $type .= " " . self::$t->_('business_trip');
                }

                //出差打卡审批中
                if (!empty($business_travel)) {
                    $background = 3;//红色
                }


                //计算迟到时长
                if ($is_punch_in == 5 && in_array($background, [2,3])) {
                    $late_num = 1;                                           //迟到1 次
                    $late_time = $this->diffTime($check_start, $shift_start);//计算迟到时长
                }
                //计算早退
                if ($is_punch_out == 5 && in_array($background, [2,3])) {
                    $leave_early_num = 1;                                       //早退1 次
                    $leave_early_time = $this->diffTime($check_end, $shift_end);//计算早退时长
                }

                //打卡时间
                $punch_info = "in:{$start_at} out:{$end_at}";
                $shift_time = $value['shift_start'] ? $value['shift_start'] . '-' . $value['shift_end'] : '';
                $shift_type = $value['shift_type'] ? self::$t->_(strtolower($value['shift_type'])) : '';

                if($value['stat_date'] > date('Y-m-d')){
                    $punch_info       = '';
                    $background       = 0;
                    $type             = 0;
                    $absenteeism_num  = 0;
                    $leave_early_num  = 0;
                    $late_num         = 0;
                    $late_time        = 0;
                    $leave_early_time = 0;
                }


                if ($this->isFreeShiftJobTitle($value['job_title'])) {
                    $leave_early_num  = 0;
                    $late_num         = 0;
                    $late_time        = 0;
                    $leave_early_time = 0;
                    if ($background == 2) {
                        $background = 0;
                    }
                    //设置 早退 黄色
                    //请全天假 不进行 统计。
                    if($applyLeaveType !== 0 && $background != 1) {
                        $leaveData['attendance_started_at'] = $value['attendance_started_at'];
                        $leaveData['attendance_end_at'] = $value['attendance_end_at'];
                        $leaveData['stat_date'] = $value['stat_date'];
                        $leaveData['type'] = $applyLeaveType;
                        $min = $this->setLiveLeaveEarly($leaveData);
                        if($min) {
                            $background = 2;
                            $leave_early_num = 1;
                            $leave_early_time = $min;
                        }
                    }

                    [$shift_time_type, $duration] = $this->getFreeShiftTypeAndDuration($value['stat_date'],
                        $value['attendance_started_at']);

                    $shift_time = $shift_time_type ? self::$t->_('free_shit_type_' . $shift_time_type,
                        ['duration' => $duration]) : '';
                    $shift_type = self::$t->_('shift');
                }

                //新增 支援显示文案
                $supportKey = $value['staff_info_id'] . '-' . $value['stat_date'];
                $isSupport = empty($supportData[$supportKey]) ? '' : $supportText;

                $returnData[$value['staff_info_id']][] = [
                    'stat_date'        => $value['stat_date'],
                    'value'            => ($is_punch_out || $is_punch_in) ? "{$punch_info} {$type} {$isSupport}" : "{$type} {$isSupport}",
                    'background'       => $background,
                    'reason'           => $reason,
                    'image_path'       => $image_path,
                    'shift_time'       => $shift_time,
                    'shift_type'       => $shift_type,
                    'holiday'          => $holiday_str,  //假期,
                    'absenteeism_num'  => $absenteeism_num,
                    'late_num'         => $late_num,
                    'late_time'        => $late_time,
                    'leave_early_num'  => $leave_early_num,
                    'leave_early_time' => $leave_early_time,
                ];
            }
        }




        return $this->handelVacancyData($returnData, $date_range);
    }


    protected function getEmptyAttendance($params)
    {
        $date_range = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));

        $data = [];
        foreach ($date_range as $item) {
            $data[] = [
                'stat_date'  => $item,
                'value'      => 0,
                'background' => 0,
                'reason'     => '',
                'image_path' => '',
                'shift_time' => '',
                'shift_type' => '',
                'holiday'    => '',
            ];
        }
        return $data;
    }

    /**
     * 空缺填补
     * @param $attendanceData
     * @param $date_range
     * @return array
     */
    protected function handelVacancyData(array $attendanceData, array $date_range)
    {
        if (empty($attendanceData)) {
            return [];
        }

        foreach ($attendanceData as &$item) {
            $has_date = array_column($item, 'stat_date');

            foreach ($date_range as $day) {
                if (!in_array($day, $has_date)) {
                    $m = [
                        'stat_date'        => $day,
                        'value'            => 0,
                        'background'       => 0,
                        'reason'           => '',
                        'image_path'       => '',
                        'shift_time'       => '',
                        'shift_type'       => '',
                        'holiday'          => '',
                        'absenteeism_num'  => 0,
                        'late_num'         => 0,
                        'late_time'        => 0,
                        'leave_early_num'  => 0,
                        'leave_early_time' => 0,
                    ];
                    array_push($item, $m);
                }
            }
            array_multisort($item, SORT_ASC, SORT_REGULAR);
        }
        return $attendanceData;
    }

    /**
     * 保存到导出表
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function handleExportAttendance(array $params)
    {
        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "Attendance_statistics_export".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR."main";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and  type = :type: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
                'type'        => HcmExcelTackModel::TYPE_ATTENDANCE,
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }
        //判断是否是fbi 操作的 导出
        $headerData = $this->request->getHeaders();
        $params['From'] = isset($headerData['From']) && $headerData['From'] === 'fbi' ? $headerData['From'] : '';

        $excelTask = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name = $action_name;
        $excelTask->file_name = $params['file_name'];
        $excelTask->type = HcmExcelTackModel::TYPE_ATTENDANCE;
        $excelTask->staff_info_id = $params['staff_id'];
        $excelTask->args_json = base64_encode(json_encode($params));
        $excelTask->created_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;
        if (!empty($hcmExcelTaskId) && $params['From'] == 'fbi') {
            (new BllService())->addFbiExportMessage(
                $params['staff_id'],
                'hcm_attendance_statistics_export_main',
                $params['file_name'],
                $hcmExcelTaskId
            );
        }
        return $hcmExcelTaskId;
    }

    public function getStaffOutsourcingType(array $staff_info_ids): array
    {
        $returnData = [];
        if(empty($staff_info_ids)){
            return  $returnData;
        }
        $staff_ids = array_chunk($staff_info_ids, 500);

        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['staff_info_id','value']);

            $builder->from(HrStaffItemsModel::class);

            $builder->where("item = 'OUTSOURCING_TYPE'"); //查询申请中和已通过的
            $builder->inWhere('staff_info_id',$staff_id);
            $res = $builder->getQuery()->execute()->toArray();
            $returnData = array_merge($res,$returnData);
        }

        return $returnData;
    }


    /**
     * 外协员工考勤
     * @param array $params
     * @return array
     */
    public function handleOutsourcingAttendance(array $params)
    {
        $result = [
            'total' => 0,
            'list'  => [],
        ];
        //没有这个 代表是从考勤进来的 要走权限 否则从 外协公司推送进来的不走权限验证
        if(!isset($params['is_push_setting_task_type']) || empty($params['is_push_setting_task_type'])) {
            $storeAuth = $this->getAuthStore($params['staffInfo']);

            $logger = $this->getDI()->get('logger');
            $logger->info("handleOutsourcingAttendance  storeAuth:" . json_encode($storeAuth, JSON_UNESCAPED_UNICODE));

            if ($storeAuth['is_store_user']) {
                if (empty($storeAuth['store_id'])) {
                    return $result;
                }
                $params['store_id'] = !empty($params['store_id']) ? (array_intersect($params['store_id'], $storeAuth['store_id']) ? : ['empty_store']) : $storeAuth['store_id'];//数组
            }

        }
        $returnData = $this->getOutsourcingStaffInfo($params);
        if ($returnData['total'] == 0) {
            return $result;
        }
        $params['staff_info_ids'] = array_column($returnData['list'], 'id');


        $staffOutsourcingType = array_column($this->getStaffOutsourcingType($params['staff_info_ids']),'value','staff_info_id');

        //获取员工派件任务
        $staff_ticket_info = $this->getTicketDelivery($params);

        $sysService = new SysService();
        //大区信息
        $region_info = array_column($sysService->getRegionListFromCache(), 'name', 'id');
        //片区信息
        $piece_info = array_column($sysService->getPieceListFromCache(), 'name', 'id');
        //网点信息
        $store_info = array_column($sysService->getStoreListFromCache(), 'name', 'id');

        //职位
        $job_title = array_column((new HrJobTitleService())->getJobTitleListFromCache(false),'job_name','id');

        //部门
        $department_list = (new DepartmentService())->getAllDepartmentFromCache();



        //获取班次表数据
        $shiftInfo = array_column($this->getHrShiftInfo(), null, 'id');

        //外协工单信息
        $staffOutsourcingOrder = $this->getStaffOutsourcingOrder($params);
        //员工打卡数据
        $attendanceInfo = $this->getStaffWorkAttendance($params);

        //时间范围
        $date_range = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));

        $osCompanyList = $sysService->outsource_company();

        $osCompanyListToId = !empty($osCompanyList) ? array_column($osCompanyList, 'label', 'value') : [];

        //获取外协员工固化信息
        $osStaffPersistentInfo = $this->getPersistentOsStaffInfo($params);
        //构造返回数据

        $result['total'] = $returnData['total'];

        foreach ($returnData['list'] as $key => $item) {
            $work_day = [];
            $shift_id_arr = [];
            $orderInfo = [
                'shift_id'         => 0,
                'effective_date'   => '',
                'employment_date'  => '',
                'employment_days'  => 0,
                'staff_name'       => '',
                'work_day'         => [],
            ];
            //工单创建的外协员工，获取相关信息
            if (in_array(
                $item['staff_type'],
                [2, 3]
            ) && isset($staffOutsourcingOrder[$item['id']])) {
                foreach($staffOutsourcingOrder[$item['id']] as $order){
                    $orderInfo = $order;
                    for ($i = 0; $i < $orderInfo['employment_days']; $i++) {
                        $employment_day = date("Y-m-d", strtotime("+{$i} day", strtotime($orderInfo["employment_date"])));
                        $work_day[$employment_day] = $store_info[$orderInfo['store_id']] ?? '';
                        $shift_id_arr[$employment_day] = $orderInfo['shift_id'] ?? '';
                    }
                }

            }

            if(isCountry('TH')) {
                $company_name_ef = $osCompanyListToId[$item['company_item_id']] ?? '';
            } else{
                $company_name_ef = $item['company_name_ef']; //员工表中的
            }

            $item = array_merge($orderInfo, $item);

            $_res = [
                'name'             => $item['name'],
                'staff_info_id'    => $item['id'],
                'staff_type'       => $item['staff_type'],
                'company_name_ef'  => $company_name_ef,
                'outsourcing_type' => isset($staffOutsourcingType[$item['id']]) ? self::$t->_('outsource_' . $staffOutsourcingType[$item['id']]) : '',
                'store_name'       => $store_info[$item['sys_store_id']] ?? '',
                'region'           => $item['manage_region'] ? $region_info[$item['manage_region']] : '',
                'piece'            => $item['manage_piece'] ? $piece_info[$item['manage_piece']] : '',
                'attend_data'      => [],
                'node_department_id'   => $item['node_department_id'],
                'job_title'            => $item['job_title'],
                'job_name'             => $job_title[$item['job_title']] ?? '',
                'node_department_name' => $department_list[$item['node_department_id']] ?? '',

            ];


            foreach ($date_range as $day) {
                $values = '';
                $is_work_day = isset($work_day[$day]) || $item['staff_type'] == 1 ? 1 : 0;
                $background = $is_work_day ? 0 : 5;
                $shift_start = '';
                $shift_end = '';
                $working_minutes = ''; //打卡分钟数
                $working_duration = ''; //打卡时长
                //工单创建的外协员工 找班次
                if (in_array($item['staff_type'], [2, 3])) {
                    if (isset($shift_id_arr[$day], $shiftInfo[$shift_id_arr[$day]])) {
                        $shift_start = $shiftInfo[$shift_id_arr[$day]]['start'];
                        $shift_end = $shiftInfo[$shift_id_arr[$day]]['end'];
                    }
                }

                $_attendanceInfo             = $attendanceInfo[$item['id']][$day] ?? [];
                //(废弃)外协公司取自打卡表(staff_work_attendance)
                //$companyNameEfFromAttendance = $_attendanceInfo['company_name_ef'] ?? '';

                //上述取外协公司的逻辑被替换为从外协员工信息固化表(persistent_outsource_staff_info)里取数据
                $_persistentStaffInfo        = $osStaffPersistentInfo[$item['id']][$day] ?? [];
                $companyNameEfFromPersistent = isCountry('TH')
                    ? ($_persistentStaffInfo['company_id'] ?? '')
                    : ($_persistentStaffInfo['company_name_ef'] ?? '');

                //优先从固化表里取数据，如无数据，则为空
                if (isCountry()) {
                    $companyId     = $companyNameEfFromPersistent ?: '';
                    $companyNameEf = $companyId ? $osCompanyListToId[$companyId] : '';
                } else {
                    $companyNameEf = $companyNameEfFromPersistent ?: '';
                }
                $shift_start = $_attendanceInfo ? $_attendanceInfo['shift_start'] : $shift_start;
                $shift_end = $_attendanceInfo ? $_attendanceInfo['shift_end'] : $shift_end;

                if (!empty($shift_start) && strlen($shift_start) < 5) {
                    $shift_start = '0' . $shift_start;
                }
                if (!empty($shift_end) && strlen($shift_end) < 5) {
                    $shift_end = '0' . $shift_end;
                }

                if (!empty($_attendanceInfo)) {
                    $start = $_attendanceInfo["started_at"] ? substr($_attendanceInfo["started_at"], 11, 5) : 'N/A';
                    $end = $_attendanceInfo["end_at"] ? substr($_attendanceInfo["end_at"], 11, 5) : 'N/A';
                    //计算上班时长 -- 小时
                    if( $_attendanceInfo["started_at"] && $_attendanceInfo["end_at"]){
                        $working_minutes = $this->diffTime(date('Y-m-d H:i', strtotime($_attendanceInfo["end_at"])),date('Y-m-d H:i', strtotime($_attendanceInfo["started_at"])));
                        $working_duration =  $this->minuteToHour($working_minutes);
                    }

                    $values = "in:{$start} out:{$end}";
                    $shift_start = !empty($shift_start) ? $shift_start : '08:00';
                    $shift_end = !empty($shift_end) ? $shift_end : '17:00';
                    if ($start > $shift_start && $work_day) {
                        $background = 2; //迟到
                    }

                    if ($end < $shift_end && $work_day) {
                        $background = 2; //早退
                    }

                    if ($start == 'N/A' && $end == 'N/A' && $work_day) {
                        $background = 3; //缺勤
                    }
                }

                if ($is_work_day && empty($_attendanceInfo)) {
                    $background = 3;
                    $values = 'AB';
                    if (isset($staff_ticket_info[$item['id']][$day])) {
                        $values = 'AB(h)';
                    }
                }

                $operate = $shift_start ? $shift_start . '-' . $shift_end : '';


                if ($item['staff_type'] == 1) {
                    if (!empty($item['leave_date']) && $item['state'] == 2  && ($day > date('Y-m-d', strtotime($item['leave_date'])) || $day < date('Y-m-d', strtotime($item['hire_date'])))) {
                        $values = 0;
                        $background = 0;
                        $operate = '';
                    }

                    if (!empty($item['hire_date']) && $item['state'] == 1 && $day < date(
                        'Y-m-d',
                        strtotime($item['hire_date'])
                    )) {
                        $values = 0;
                        $background = 0;
                        $operate = '';
                    }
                }


                $_res['attend_data'][] = [
                    'stat_date'   => $day,
                    'value'       => $values,
                    'background'  => $background,
                    'operate'     => $operate,
                    'reason'      => '',
                    'image_path'  => '',
                    'shift_type'  => '',
                    'working_day' => $is_work_day,
                    'store_name'  => $work_day[$day] ?? '',
                    'working_minutes'=>$working_minutes,
                    'working_duration'=>$working_duration,
                    'company_name_ef' => $companyNameEf,//新增当日所在外协公司
                    'shift' => $shift_start ? $shift_start . '-' . $shift_end : '',
                ];
            }
            $result['list'][] = $_res;
        }
        return $result;
    }


    /**
     * 获取外协工单员工的工单信息
     * @param $params
     * @return array
     */
    protected function getStaffOutsourcingOrder($params)
    {
        if (empty($params['staff_info_ids'])) {
            return [];
        }

        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $result = [];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                "
                    h.effective_date,
                    h.store_id,
                    h.shift_id,
                    d.staff_info_id,
                    h.employment_date,
                    h.employment_days                  
              "
            );
            $builder->from(['h' => HrOutsourcingOrderModel::class]);
            $builder->innerJoin(HrOutsourcingOrderDetailModel::class, 'h.serial_no = d.serial_no', 'd');
            $builder->where('h.status in (2,3)');

            $builder->inWhere('d.staff_info_id ', $staff_id);

            if (!empty($params['start_date'])) {
                $builder->andWhere(
                    'h.invalid_date > :start_date: ',
                    ['start_date' => $params['start_date'] . ' 00:00:00']
                );
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere(
                    'h.effective_date <= :end_date: ',
                    ['end_date' => $params['end_date'] . ' 23:59:59']
                );
            }
            $res = $builder->getQuery()->execute()->toArray();


            if (!empty($res)) {
                $result = array_merge($result, $res);
            }
        }
        $data = [];
        foreach($result  as $v){
            $data[$v['staff_info_id']][] = $v;
        }

        return $data;
    }

    /**
     * 获取外协员工固化信息
     * @param $params
     * @return array
     */
    protected function getPersistentOsStaffInfo($params)
    {
        if (empty($params['staff_info_ids'])) {
            return [];
        }

        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $result = [];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                "h.staff_info_id,
                h.company_id,
                h.company_name_ef,
                h.stat_date"
            );
            $builder->from(['h' => PersistentOutsourceStaffInfoModel::class]);
            $builder->inWhere('h.staff_info_id ', $staff_id);
            if (!empty($params['start_date'])) {
                $builder->andWhere(
                    'h.stat_date >= :start_date: ',
                    ['start_date' => $params['start_date'] . ' 00:00:00']
                );
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere(
                    'h.stat_date <= :end_date: ',
                    ['end_date' => $params['end_date'] . ' 23:59:59']
                );
            }
            $res = $builder->getQuery()->execute()->toArray();
            if (!empty($res)) {
                $result = array_merge($result, $res);
            }
        }
        $data = [];
        foreach($result  as $v){
            $data[$v['staff_info_id']][$v['stat_date']] = $v;
        }
        return $data;
    }


    /**
     * 获取员工打卡表数据
     * @param $params
     * @return array
     */
    public function getStaffWorkAttendance($params)
    {
        if (empty($params['staff_info_ids'])) {
            return [];
        }
        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $result = [];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                "staff_info_id,CONVERT_TZ(started_at,'+00:00','" . $this->timeZone . "') as started_at,CONVERT_TZ(end_at,'+00:00','" . $this->timeZone . "') as end_at,attendance_date,shift_start,shift_end,company_name_ef"
            );
            $builder->from(['a' => StaffWorkAttendanceModel::class]);

            $builder->inWhere('a.staff_info_id', $staff_id);
            if (!empty($params['start_date'])) {
                $builder->andWhere('a.attendance_date >= :start_date: ', ['start_date' => $params['start_date']]);
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere('a.attendance_date <= :end_date: ', ['end_date' => $params['end_date']]);
            }

            if (!empty($params['start_date_updated_at'])) {
                $builder->andWhere('a.updated_at >= :start_date_updated_at: ', ['start_date_updated_at' => $params['start_date_updated_at']]);
            }

            if (!empty($params['end_date_updated_at'])) {
                $builder->andWhere('a.updated_at <= :end_date_updated_at: ', ['end_date_updated_at' => $params['end_date_updated_at']]);
            }


            $res = $builder->getQuery()->execute()->toArray();
            if (!empty($res)) {
                $result = array_merge($result, $res);
            }
        }
        $return = [];
        if (!empty($result)) {
            foreach ($result as $key => $value) {
                $return[$value['staff_info_id']][$value['attendance_date']] = $value;
            }
        }
        return $return;
    }

    /**
     * 获取员工派件任务
     * @param $params
     * @return array
     */
    public function getTicketDelivery($params)
    {
        if (empty($params['staff_info_ids'])) {
            return [];
        }
        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $result = [];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('staff_info_id,date(created_at) created_date');
            $builder->from(TicketDeliveryModel::class);

            $builder->inWhere('staff_info_id', $staff_id);
            $builder->andWhere('state in (0,1,2)');
            if (!empty($params['start_date'])) {
                $builder->andWhere(
                    'created_at >= :start_date: ',
                    ['start_date' => $params['start_date'] . ' 17:00:00']
                );
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere('created_at < :end_date: ', ['end_date' => $params['end_date'] . ' 17:00:00']);
            }
            $builder->groupBy('staff_info_id,created_date');
            $res = $builder->getQuery()->execute()->toArray();
            if (!empty($res)) {
                $result = array_merge($result, $res);
            }
        }
        $return = [];
        if (!empty($result)) {
            foreach ($result as $key => $value) {
                $return[$value['staff_info_id']][$value['created_date']] = $value['created_date'];
            }
        }
        return $return;
    }

    /**
     * 获取网点员工数据
     * @param $params
     * @return array
     */
    public function getOutsourcingStaffInfo($params)
    {
        $logger = $this->getDI()->get('logger');
        $logger->info("getOutsourcingStaffInfo  params" . json_encode($params));

        $returnData = [
            'list'  => [],
            'total' => 0,
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count(1) as total");
        $builder->from(['h' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'h.sys_store_id = s.id', 's');
        $builder->leftJoin(SysProvinceModel::class, 's.province_code = p.code', 'p');
        $builder->leftJoin(OsStaffInfoExtendModel::class, 'h.staff_info_id = osie.staff_info_id', 'osie');


        $builder->where("h.staff_type in (1, 2, 3) and h.sys_store_id != '-1'");

        if (!empty($params['staff_info_id'])) { //工号
            $builder->andWhere('h.staff_info_id = :staff_info_id: ', ['staff_info_id' => $params['staff_info_id']]);
        }

        if (!empty($params['store_id'])) { //网点
            $builder->inWhere('h.sys_store_id', $params['store_id']);
        }

        if (!empty($params['piece_id'])) { // 片区
            $builder->andWhere('s.manage_piece in ({piece_id:array}) ', ['piece_id' => $params['piece_id']]);
        }

        if (!empty($params['region_id'])) { // 大区
            $builder->andWhere('s.manage_region = :region_id: ', ['region_id' => $params['region_id']]);
        }

        if (!empty($params['job_title'])) { // 职位
            $builder->andWhere('h.job_title = :job_title: ', ['job_title' => $params['job_title']]);
        }

        if (!empty($params['node_department_id'])) { // 所属部门
            $builder->andWhere('h.node_department_id = :node_department_id: ', ['node_department_id' => $params['node_department_id']]);
        }

        if (!empty($params['company_name_ef'])) { // 所属公司
            $builder->andWhere('h.company_name_ef = :company_name_ef: ', ['company_name_ef' => $params['company_name_ef']]);
        }

        if (!empty($params['os_company_id']) && !is_array($params['os_company_id'])) { // 所属公司--新枚举
            $builder->andWhere('osie.company_item_id = :os_company_id: ', ['os_company_id' => $params['os_company_id']]);
        }

        if (!empty($params['os_company_id']) && is_array($params['os_company_id'])) { // 所属公司--新枚举
            $builder->andWhere('osie.company_item_id in ({os_company_id:array})', ['os_company_id' => $params['os_company_id']]);
        }

        //如果没有这个参数 就按照考勤来找  ,如果有这个参数 代表要获取所有的外协员工 id
        if(!isset($params['is_all_staff_info']) || empty($params['is_all_staff_info'])) {

            $_condition = "( EXISTS  (select 1 from App\Models\backyard\HrOutsourcingOrderModel as o inner join App\Models\backyard\HrOutsourcingOrderDetailModel as od on o.serial_no = od.serial_no  where h.staff_info_id = od.staff_info_id and  o.effective_date <= :effective_date: and o.invalid_date >= :invalid_date: and o.status in (2,3) limit 1  )";
            $_condition .= " OR  (h.hire_date <= :end_date: and h.state = 1  and h.staff_type = 1 )";
            $_condition .= " OR  (h.leave_date >= :start_date: and h.state = 2 and h.staff_type = 1 ) )";
            $_bind = [
                'effective_date' => "{$params['end_date']} 23:59:59",
                'invalid_date'   => "{$params['start_date']} 00:00:00",
                'end_date'       => $params['end_date'],
                'start_date'     => $params['start_date'],
            ];
            $builder->andWhere($_condition, $_bind);

        }

        $totalInfo = $builder->getQuery()->getSingleResult();
        $returnData['total'] = intval($totalInfo->total);

        if (!$returnData['total']) {
            return $returnData;
        }
        $builder->columns(
            "
                h.staff_info_id as id,
                h.name,
                h.sys_store_id,
                h.hire_date,
                h.leave_date,
                h.state,
                h.staff_type,
                h.stop_duties_date,
                h.company_name_ef,
                p.name as province_name,
                p.code as province_code,
                p.manage_geography_code,
                s.manage_region as manage_region,
                s.manage_piece as manage_piece,
                s.name as store_name,
                h.week_working_day,
                h.job_title,
                h.node_department_id,
                h.company_name_ef,
                osie.company_item_id
            "
        );
        if (isset($params['page']) && isset($params['size'])) {
            $page = intval($params['page']);
            $size = intval($params['size']);
            $offset = $size * ($page - 1);
            $builder->limit($size, $offset);
        }

        $returnData['list'] = $builder->getQuery()->execute()->toArray();
        return $returnData;
    }

    /**
     * 网点的可视范围
     * @param $staffInfo
     * @return mixed
     */
    public function getAuthStore($staffInfo)
    {
        $key = CacheKeyEnums::STORE_AUTH_DATA . $staffInfo['id'];
        $info = $this->getCache($key);
        if ($info) {
            return json_decode($info, true);
        }
        unset($info);
        if ($staffInfo['organization_type'] == 1) {
            $info['store_id'] = [$staffInfo['organization_id']];
            $info['is_store_user'] = 1;
        } else {
            $store = $this->getManageRegionsPieceNew($staffInfo['id']);

            if (in_array($staffInfo['id'], explode(',', env('sa_id')))) {
                $store = [];
            }
            if ($store === false) {
                $info['store_id'] = false;
                $info['is_store_user'] = 1;
            } elseif (!empty($store)) {
                $info['store_id'] = $store;
                $info['is_store_user'] = 1;
            } else {
                $info['store_id'] = true;
                $info['is_store_user'] = 0;
            }
        }
        //管辖范围配置
        //https://flashexpress.feishu.cn/docx/SIRgdJiHXouA0Uxk5XjcUrntnuh
        $staff_roles = $staffInfo['position_category'] ? explode(',', $staffInfo['position_category']) : [];
        if (is_array($info['store_id']) && !empty($info['store_id']) && in_array(RolesModel::ROLES_OUTLET_SUPERVISOR_ID,
                $staff_roles)) {
            //查找管辖的网点范围
            $store_infos = (new DataPermissionService())->getAllManageStore($staffInfo);
            if ($store_infos === true) {
                $info['store_id']      = true;
                $info['is_store_user'] = 0;
            } else {
                $info['store_id'] = array_values(array_unique(array_merge($info['store_id'],
                    array_column($store_infos, 'store_id'))));
            }
        }

        $time = 600;
        if (RUNTIME != 'pro') {
            $time = 1;
        }
        $this->setCache($key, json_encode($info), $time);
        return $info;
    }

    /**
     * 获取登录员工的可视网点
     * @param $staff_info_id
     * @return array|bool
     */
    public function getManageRegionsPieceNew($staff_info_id)
    {
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: ',
            'columns'    => 'job_title',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);

        if (!$staff_info) {
            return false;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("s.id AS sys_store_id");
        $builder->from(['hsmr' => HrStaffManageRegionsModel::class]);
        $area_store = [];
        //11 Area Manager   79 Regional Manager
        if (in_array($staff_info->job_title, [11, 79])) {
            $builder->join(SysStoreModel::class, 'hsmr.region_id = s.manage_region', 's');
            $builder->where('staff_info_id = :staff_info_id:  and s.manage_region > 0',['staff_info_id' => $staff_info_id,]);
            $area_store = $builder->getQuery()->execute()->toArray();
        } else {
            if ($staff_info->job_title == 269) {//269 District Manager
                $builder->join(
                    SysStoreModel::class,
                    ' hsmr.piece_id = s.manage_piece',
                    's'
                );
                $builder->where('staff_info_id = :staff_info_id:  and  s.manage_piece > 0 ',['staff_info_id' => $staff_info_id]);

                $area_store = $builder->getQuery()->execute()->toArray();
            }
        }

        return $area_store ? array_column($area_store, 'sys_store_id') : [];
    }

    /**
     * 保存到导出表
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function handleExportOutsourcingAttendance(array $params)
    {
        $action_name = 'outsourcing_attendance_statistics_export'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'main';

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and  type = :type: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['staff_id'],
                'type'        => HcmExcelTackModel::TYPE_ATTENDANCE,
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        //判断是否是fbi 操作的 导出
        $headerData = $this->request->getHeaders();
        $params['From'] = isset($headerData['From']) && $headerData['From'] === 'fbi' ? $headerData['From'] : '';

        $excelTask = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name = $action_name;
        $excelTask->file_name = $params['file_name'];
        $excelTask->type = HcmExcelTackModel::TYPE_ATTENDANCE;
        $excelTask->staff_info_id = $params['staff_id'];
        $excelTask->args_json = base64_encode(json_encode($params));
        $excelTask->created_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $flag = $excelTask->save();

        $hcmExcelTaskId = $excelTask->id;
        //fbi 下载中心 增加一条记录 等任务跑完 同步更新 fbi 下载中心状态
        if (!empty($hcmExcelTaskId) && $params['From'] == 'fbi') {
            (new BllService())->addFbiExportMessage(
                $params['staff_id'],
                'outsourcing_attendance_statistics_export_main',
                $params['file_name'],
                $hcmExcelTaskId
            );
            $this->logger->info("fbi sync excel task {$params['staff_id']} {$hcmExcelTaskId}");
        }

        return $flag;
    }

    /**
     * 获取外出申请信息
     * @param $params
     * @return array
     */
    public function getGoOutWorks($params): array
    {
        $returnData = [];

        if (empty($params['staff_info_ids'])) {
            return $returnData;
        }
        $list = StaffAuditReissueForBusinessModel::find([
            'conditions' => 'staff_info_id in ({apply_user:array}) and business_trip_type in ({type:array})
             and attendance_date<= :e: and attendance_date>= :s:',
            'bind'       => [
                'apply_user' => $params['staff_info_ids'],
                's'          => $params['start_date'],
                'e'          => $params['end_date'],
                'type'       => [BusinessTripModel::BTY_GO_OUT],
            ],
            'columns'    => 'staff_info_id,attendance_date,status',
        ])->toArray();
        foreach ($list as $item) {
            $returnData[$item['staff_info_id'] . '-' . $item['attendance_date']] = $item['status'];
        }
        return $returnData;
    }

    /**
     * @description:计算两个时间之间相差的分钟数
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/2/24 19:50
     */
    public function diffTime($date1, $date2)
    {
        if (empty($date1) || empty($date2)) {
            return 0;
        }
        return (int)((abs(strtotime($date1) - strtotime($date2))) / 60);
    }


    /**
     * @description: 分钟换算小时和分钟
     * @param $minute int
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/8/1 10:09
     */
    public function minuteToHour( $minute = 0 ) {
        $runtime = '';
        $h       = floor(($minute / 60));
        $m       = floor(($minute % 60));
        if ($h != '0') {
            if ($m == '0') {
                $runtime = $h . 'h';
            } else {
                $runtime = $h . 'h' . $m . 'm';
            }
        } else {
            $runtime = $m . 'm';
        }
        return $runtime;
    }

    /**
     * @description:获取员工历史班次信息
     * <AUTHOR> L.J
     * @time       : 2022/8/19 15:24
     */
    public function getShiftByDate($params){

        if (empty($params['staff_info_ids']) || empty($params['start_date']) || empty($params['end_date'])) {
            return [];
        }
        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $shiftData = [];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            //有 end  只能 * ?
            //$builder->columns("staff_info_id,start,end,shift_type,shift_day");
            $builder->columns(["*"]);
            $builder->from(['a' => HrStaffShiftHistoryModel::class]);

            $builder->inWhere('a.staff_info_id', $staff_id);

            if (!empty($params['start_date'])) {
                $builder->andWhere('a.shift_day >= :start_date: ', ['start_date' => $params['start_date']]);
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere('a.shift_day <= :end_date: ', ['end_date' => $params['end_date']]);
            }
            $res = $builder->getQuery()->execute()->toArray();
            if (!empty($res)) {
                $shiftData = array_merge($shiftData, $res);
            }
        }

        return $shiftData;
    }

    /**
     * 获取员工历史班次信息格式化一下
     * @param array $shiftData
     * @return array
     */
    public function handleShiftByDate(array $shiftData)
    {
        $returnData = [];
        if (empty($shiftData)) {
            return $returnData;
        }
        foreach ($shiftData as $key => $value) {
            $returnData[$value['staff_info_id']][$value['shift_day']] = $value;
        }
        return $returnData;
    }
    /**
     * @description:出差打卡重新赋值打卡时间和班次时间
     * @param array $reissue_info
     * @param array $value
     * @return array
     * <AUTHOR> L.J
     * @time       : 2022/8/19 17:05
     */
    protected function assignReissueAttendanceData(array $reissue_info,array $value){
        if (empty($reissue_info)) {
            return $value;
        }

            //出差打卡存在 并且在审批中 并且 v2 表里 没有上班卡 和下班卡
        if (isset($reissue_info[$value['staff_info_id']][$value['stat_date']])
            && $reissue_info[$value['staff_info_id']][$value['stat_date']]['status'] == $this->leave_status_1
            && empty($value['attendance_started_at'])
            && empty($value['attendance_end_at'])) {
            $value['business_travel'] = self::$t->_('leave_status_1'); //  (如果在审批中 则显示审批中 否则不显示)

            //上班打卡时间 重置
            if (!empty($reissue_info[$value['staff_info_id']][$value['stat_date']]['start_time'])) {
                $value['attendance_started_at']  = date(
                    'Y-m-d H:i',
                    strtotime(
                        $reissue_info[$value['staff_info_id']][$value['stat_date']]['start_time']
                    )
                );
            }

            //下班打卡时间重置
            if (!empty($reissue_info[$value['staff_info_id']][$value['stat_date']]['end_time'])) {
                $value['attendance_end_at']  = date(
                    'Y-m-d H:i',
                    strtotime(
                        $reissue_info[$value['staff_info_id']][$value['stat_date']]['end_time']
                    )
                );
            }
            //班次重置
            $value['shift_start'] = empty($value['shift_start']) && isset($reissue_info[$value['staff_info_id']][$value['stat_date']]['start_shift']) ?
                $reissue_info[$value['staff_info_id']][$value['stat_date']]['start_shift'] : $value['shift_start'];
            $value['shift_end']   = empty($value['shift_end']) && isset($reissue_info[$value['staff_info_id']][$value['stat_date']]['end_shift']) ?
                $reissue_info[$value['staff_info_id']][$value['stat_date']]['end_shift'] : $value['shift_end'];
            $value['shift_type']  = empty($value['shift_type']) && isset($reissue_info[$value['staff_info_id']][$value['stat_date']]['shift_type']) ?
                $reissue_info[$value['staff_info_id']][$value['stat_date']]['shift_type'] : $value['shift_type'];
            $value['shift_id']  = empty($value['shift_id']) && isset($reissue_info[$value['staff_info_id']][$value['stat_date']]['shift_id']) ?
                $reissue_info[$value['staff_info_id']][$value['stat_date']]['shift_id'] : $value['shift_id'];

        }
        return $value;
    }

    //获取员工当前班次信息
    public function getStaffShift($params)
    {
        if (empty($params['staff_info_ids'])) {
            return [];
        }

        $staff_ids               = array_chunk($params['staff_info_ids'], 400);
        $shiftData               = [];
        $today                   = date('Y-m-d');
        foreach ($staff_ids as $staff_id) {
            $staff   = array_values($staff_id);
            $midData = HrStaffShiftMiddleDateRepository::getStaffShiftMiddleDateList([
                'staff_info_ids' => $staff,
                'begin_day'      => $today,
                'end_day'        => $today,
            ]);
            if ($midData) {
                $shiftData = array_merge($shiftData, $midData);
                $staff     = array_values(array_diff($staff, array_column($midData, 'staff_info_id')));
            }
            if(empty($staff)){
                continue;
            }
            $res = HrStaffShiftRepository::getShift('start as shift_start, [end] as shift_end, shift_id, shift_type,staff_info_id',
                'staff_info_id in ({staff_info_id:array})', ['staff_info_id' => $staff]);
            if (!empty($res)) {
                $shiftData = array_merge($shiftData, $res);
            }
        }

        return array_column($shiftData, null,'staff_info_id');
    }

    //获取网点快递员出勤人数

    /**
     * @param $local
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function getStoreCourierAttendanceNumForBI($local, $params): array
    {
        if (empty($params['store_list']) || !is_array($params['store_list'])) {
            throw new BusinessException('store_list empty');
        }
        if (count($params['store_list']) > 100) {
            throw new BusinessException('store_list 最多100');
        }

        if (empty($params['date_at'])) {
            throw new BusinessException('date_at empty');
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('swa.organization_id,count(distinct(swa.staff_info_id)) as total');
        $builder->from(['swa' => StaffWorkAttendanceModel::class]);
        $builder->leftJoin(HrStaffWorkDaysModel::class,
            'swa.staff_info_id = swd.staff_info_id and swa.attendance_date = swd.date_at', 'swd');
        $builder->leftJoin(HrStaffApplySupportStoreModel::class,
            'sass.staff_info_id =  swa.staff_info_id
            AND sass.employment_begin_date <= swa.attendance_date
            AND sass.employment_end_date >= swa.attendance_date
            AND support_status IN (2, 3)',
            'sass');

        $builder->where('swd.staff_info_id is null and sass.staff_info_id is null and swa.started_store_id is not null');
        $builder->andWhere('swa.attendance_date = :date_at:', ['date_at' => $params['date_at']]);
        $builder->inWhere('swa.organization_id', $params['store_list']);
        $builder->inWhere('swa.job_title', [13, 110,1015,1199]);//'Van Courier','Car Courier','Bike Courier','Van Courier (Project)'
        $builder->groupBy('swa.organization_id');
        $all_attendance_data = $builder->getQuery()->execute()->toArray();
        return ['code'    => ErrCode::SUCCESS,
                'message' => 'success!',
                'data'    => array_column($all_attendance_data, 'total', 'organization_id'),
        ];
    }

    /**
     * 标记 主播因 实际工作时长 小于 应工作时长 标记 早退
     *
     * 仅主播职位 使用
     *
     * @param $data
     * @return bool
     */
    public function setLiveLeaveEarly($data)
    {
        if(empty($data['attendance_started_at']) || empty($data['attendance_end_at']) || $data['attendance_started_at'] == AttendanceStatisticsBox::NOT_ATTENDANCE_START_AT || $data['attendance_end_at'] == AttendanceStatisticsBox::NOT_ATTENDANCE_END_AT) {
            return false;
        }

        $hours = $this->getLiveHours($data['stat_date'], $data['attendance_started_at'], $data['type'] ?? 0);

        //不考虑宽限分钟数，精确到分钟
        $start_at = date('Y-m-d H:i', strtotime($data['attendance_started_at']));
        $end_at = date('Y-m-d H:i', strtotime($data['attendance_end_at']));

        //实际工作时长 大于 等于 应工作时长 则 不标记, 按分钟
        $min = floor((strtotime($end_at) - strtotime($start_at)) / 60);
        if($min >= $hours * 60) {
            return false;
        }
        //早退分钟数。
        return ($hours * 60 - $min);
    }


}
