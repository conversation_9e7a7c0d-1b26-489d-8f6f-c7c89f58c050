<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\DateHelper;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffAuditModel;
use App\Models\backyard\StaffAuditReissueForBusinessModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysStoreModel;
use App\Models\bi\BiBaseModel;
use App\Models\backyard\TripleToLeaveModel;

class StaffAbsentSuspensionService extends BaseService
{

    public $hire_type_condition;

    public $absentDays = 3; //员工连续旷工N天后停职
    public $leaveDays = 4; //员工连续旷工N天后离职  注意 马来的停职日期不是脚本执行的当天而是 旷工的第一天，计算离职时注意判断逻辑
    public $staffAbsent = []; //连续旷工需要停职的员工!
    public $staffAbsentDate = []; //旷工人员日期
    public $absentDaysLeave = [];//旷工时间查询范围

    public $runtime = 'dev';
    public $template_map = [
        'mail2higher_staff' => '%s สวัสดีครับ <br />
                พนักงาน %s（%s）มีการขาดงานติดกัน %s วันในระบบแล้ว <br />
                ตอนนี้ระบบเปลี่ยนสถานะการทำงานของพนักงานดังกล่าวเป็นพักงานเรียบร้อยแล้ว พนักงานที่มีสถานะการทำงานเป็นพักงาน จะไม่สามารถเข้าระบบใดๆ ได้ทั้งสิ้น กนุณารีบตรวจสอบและรายงานตัวให้ HR ทันที <br/> <br />
                หากไม่มีการดำเนินการใดๆภายใน %s วัน ในวันที่ %s พนักงานดังกล่าวจะเปลี่ยนสถานะการทำงานเป็น ลาออกอัตโนมัติ',
        'mail2hris' => 'สวัสดีครับ <br />
                พนักงาน %s มีการขาดงานติดกัน %s วันในระบบแล้ว <br />
                ตอนนี้ระบบเปลี่ยนสถานะการทำงานของพนักงานดังกล่าวเป็นพักงานเรียบร้อยแล้ว พนักงานที่มีสถานะการทำงานเป็นพักงาน จะไม่สามารถเข้าระบบใดๆ ได้ทั้งสิ้น กนุณารีบตรวจสอบและรายงานตัวให้ HR ทันที <br/> <br />
                หากไม่มีการดำเนินการใดๆภายใน %s วัน ในวันที่ %s พนักงานดังกล่าวจะเปลี่ยนสถานะการทำงานเป็น ลาออกอัตโนมัติ'
    ];

    //是否发送短信
    protected $isNeedSms = false;


    /**
     * 雇佣类型的筛选
     * @param $hire_type_condition
     * @return void
     */
    public function setHireTypeCondition($hire_type_condition)
    {
        $this->hire_type_condition = $hire_type_condition;
    }


    /**
     * 这是旷工停职\离职天数
     * @return bool
     */
    public function setICAbsentDaysConfig(): bool
    {
        $settingEnv = new SettingEnvService();
        if($IC_staff_leave_days = $settingEnv->getSetVal('IC_staff_leave_days')) {
            $this->leaveDays = $IC_staff_leave_days;
        }
        if($IC_staff_absent_days = $settingEnv->getSetVal('IC_staff_absent_days')) {
            $this->absentDays = $IC_staff_absent_days;
        }
        return true;
    }


    /**
     * 这是旷工停职\离职天数
     * @return bool
     */
    public function setPartTimeICAbsentDaysConfig(): bool
    {
        $settingEnv = new SettingEnvService();//兼职
        if($part_time_IC_staff_leave_days = $settingEnv->getSetVal('part_time_IC_staff_leave_days')) {
            $this->leaveDays = $part_time_IC_staff_leave_days;
        }
        if($part_time_IC_staff_absent_days = $settingEnv->getSetVal('part_time_IC_staff_absent_days')) {
            $this->absentDays = $part_time_IC_staff_absent_days;
        }
        $this->isNeedSms = true;
        return true;
    }



    public function __construct()
    {
        parent::__construct();
        $settingEnv = new SettingEnvService();
        if($staff_absent_days = $settingEnv->getSetVal('staff_absent_days')) {
            $this->absentDays = $staff_absent_days;
        }
        if ($staff_leave_days = $settingEnv->getSetVal('staff_leave_days')) {
            $this->leaveDays = $staff_leave_days;
        }
        $leave = $settingEnv->getSetVal('staff_absent_days_leave');//每个国家可以按照实际情况配置不同的时间范围阶梯
        $this->absentDaysLeave = empty($leave) ? [9, 30, 90, 200] : explode(',', $leave);
        $this->runtime = env('runtime');
    }

    /**
     * 获取白名单用户
     * @return array
     */
    public function getWhiteList(): array
    {
        $settingEnv = new SettingEnvService();
        $stop = explode(',', $settingEnv->getSetVal('hris_stop_ignore_staff')); // 源数据在BI的配置
        return array_unique(array_merge( $stop, [10000]));
    }

    /**
     * 获取旷工需要停职的人员
     * @param $staffIds
     * @param $suspensionHandleDate
     * @return $this|array
     */
    public function calculateAbsenteeismStaff($staffIds, $suspensionHandleDate)
    {
        if ($this->runtime != 'pro') {
            $staffIds = $this->getTestStaffIds();
            if (empty($staffIds)) {
                return [];
            }
        }
        $staffInfo = $this->getStaffInfo($staffIds, $suspensionHandleDate);
        if (empty($staffInfo)) {
            $this->getDI()->get('logger')->info("calculateAbsenteeismStaff getStaffInfo empty");
            return [];
        }
        $staffInfo = array_column($staffInfo, null, 'staff_info_id');
        $whiteList = $this->getWhiteList();
        $this->getDI()->get('logger')->info("calculateAbsenteeismStaff getWhiteList:" . json_encode($whiteList));
        //剔除白名单用户
        foreach ($whiteList as $whiteStaffId) {
            if (isset($staffInfo[$whiteStaffId])) {
                unset($staffInfo[$whiteStaffId]);
            }
        }
        //基础员工连续旷工范围
        $startTime = date('Y-m-d', strtotime("{$suspensionHandleDate} -{$this->absentDays} day"));
        $endTime = date('Y-m-d', strtotime("{$suspensionHandleDate} -1 day"));
        $markISRS = $this->handleAbsent($staffInfo, $startTime, $endTime, $this->absentDays);
        if ($markISRS) {
            foreach ($this->absentDaysLeave as $day) {
                $endTime = date('Y-m-d', strtotime("{$startTime} -1 day"));
                $startTime = date('Y-m-d', strtotime($endTime) - 3600 * 24 * $day);
                $markISRS = $this->handleAbsent($markISRS, $startTime, $endTime, $day);
                if (empty($markISRS)) {
                    break;
                }
            }
            //记录异常人员数据
            $this->getDI()->get('logger')->info("calculateAbsenteeismStaff unusual:" . json_encode($markISRS));
        }
        //销毁没达到旷工天数的员工 & 排序旷工日期
        foreach ($this->staffAbsentDate as $staffInfoId => $dateArr) {
            if (count($dateArr) < $this->absentDays) {
                unset($this->staffAbsentDate[$staffInfoId]);
            } else {
                sort($this->staffAbsentDate[$staffInfoId]);
            }
        }
        return $this->staffAbsentDate;
    }

    /**
     * 除了生产环境以外，都需要指定员工来操作
     * @return array|false|string[]
     */
    public function getTestStaffIds()
    {
        $staffIds = [];
        if ($this->runtime != 'pro') {
            $settingEnv = new SettingEnvService();
            $staffIds = explode(',', $settingEnv->getSetVal('hris_stop_staff_info_id'));
        }
        return $staffIds;
    }

    /**
     * 判定停职的开始日期
     * @param $staffAbsentDate
     * @return false|mixed
     */
    public function getStopBeginDate($staffAbsentDate)
    {
        return current($staffAbsentDate);
    }

    /**
     * 处理需要停职的人员
     * @return array
     * @throws \Exception
     */
    public function doAbsenteeism(): array
    {
        $insertData = [];
        $updateStateData = [];
        $stopSuccessIds = [];
        $stopErrorIds = [];
        $type = TripleToLeaveModel::TYPE_CONTINUE_ABSENT;//1:连续旷工
        $this->logger->notice("doAbsenteeism info:" . json_encode(['staffAbsentDate' => $this->staffAbsentDate],JSON_UNESCAPED_UNICODE));
        if ($this->staffAbsent) {
            foreach ($this->staffAbsent as $staffInfoId) {
                if (isset($this->staffAbsentDate[$staffInfoId])) {
                    $stop_begin_date = $this->getStopBeginDate($this->staffAbsentDate[$staffInfoId]);
                } else {
                    $this->logger->info('doAbsenteeism not found $staffInfoId:'.$staffInfoId);
                    continue;
                }
                $insertData[$staffInfoId] = [
                    'staff_info_id' => $staffInfoId,
                    'stop_begin_date' => $stop_begin_date,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'type' => $type,
                    'stop_state' => TripleToLeaveModel::STOP_TYP_ERROR,
                    'stop_duties_date' => $this->getStopDutiesDate($staffInfoId),
                    'stop_date_detail' => isset($this->staffAbsentDate[$staffInfoId]) ? json_encode($this->staffAbsentDate[$staffInfoId]): "",
                ];
                $updateStateData[] = [
                    "staff_info_id" => $staffInfoId,
                    "type" => 3,// 1在职 2离职 3停职
                    "day" => $this->getStopDutiesDate($staffInfoId),
                    'stop_duty_reason' => 6, //三天未打卡
                    'payment_markup' => 8, //连续旷工
                    'leave_reason' => 2,
                    'payment_state' => 2,
                    'src'=>'hcm',
                ];
            }
            if (empty($updateStateData)) {
                $this->logger->info("doAbsenteeism updateStateData empty!!" );
                return $stopSuccessIds;
            }
            //进行停职
            $updateStateDataChunk = array_chunk($updateStateData, 5);
            $apiClient = new ApiClient('hris','','sync_staff_state');
            foreach ($updateStateDataChunk as $item) {
                $apiClient->setParams([$item]);
                $result = $apiClient->execute();
                if (isset($result['code'],$result['data']) && $result['code'] == 1) {
                    foreach ($result['data'] as $body) {
                        if ('ok' == $body['msg']) {
                            $insertData[$body['staff_info_id']]['stop_state'] = TripleToLeaveModel::STOP_TYPE_SUCCESS;
                            $stopSuccessIds[]                                 = $body['staff_info_id'] ?? 0;
                        } else {
                            $stopErrorIds[] = $body['staff_info_id'] ?? 0;
                        }
                    }
                }
            }

            $tripleToLeaveModel = new TripleToLeaveModel();
            $inertRes =$tripleToLeaveModel->batch_insert(array_values($insertData));
            $this->logger->info("doAbsenteeism success:" .json_encode($stopSuccessIds,JSON_UNESCAPED_UNICODE)
                ."doAbsenteeism error:" .json_encode($stopErrorIds,JSON_UNESCAPED_UNICODE)
                . " insertData:" .json_encode($insertData,JSON_UNESCAPED_UNICODE)
                . "insertData result-1  " . $inertRes
            );

            $db          = BackyardBaseModel::beginTransaction($this);
            $tripleToLeaveModel = TripleToLeaveModel::findFirst([
                'conditions' => "created_at >= :created_at: and type = 1",
                'bind' => ['created_at' =>  date('Y-m-d 00:00:00')],
            ]);

            if (empty($tripleToLeaveModel)) {
                $inertRes =(new TripleToLeaveModel())->batch_insert(array_values($insertData));
                $this->logger->alert("doAbsenteeism success:" .json_encode($stopSuccessIds,JSON_UNESCAPED_UNICODE)
                    ."doAbsenteeism error:" .json_encode($stopErrorIds,JSON_UNESCAPED_UNICODE)
                    . " insertData:" .json_encode($insertData,JSON_UNESCAPED_UNICODE)
                    . "insertData result-2 " . $inertRes
                );
                if(!$inertRes){
                    $this->logger->error("doAbsenteeism error  insertData:" .json_encode($insertData,JSON_UNESCAPED_UNICODE));
                }
            }
            $db->commit();

            if ($stopSuccessIds) {
                $this->logger->info("doAbsenteeism hold  handleHoldData:" .json_encode($stopSuccessIds,JSON_UNESCAPED_UNICODE));
                //hold管理
                $this->handleHold($stopSuccessIds);
                //给上级和Hr发送邮件
                try {

                    $this->sendEmail($stopSuccessIds);
                } catch (\Exception $e) {
                    $this->logger->error("doAbsenteeism sendEmail error:" . json_encode(['staffAbsent' => $stopSuccessIds, 'message' => $e->getMessage(), 'file' => $e->getFile(), 'line' => $e->getLine()],JSON_UNESCAPED_UNICODE));
                }
                //兼职ic 发送短信
                if ($this->isNeedSms) {
                    $this->sendSms($stopSuccessIds);
                }
            }
        }
        return $stopSuccessIds;
    }

    public function sendSms($stopSuccessIds)
    {
        $staffInfoService = new StaffInfoService;
        $staffInfo = $staffInfoService->getStaffInfoListByStaffIds($stopSuccessIds);
        $staffsLang  = (new StaffService())->getStaffEquipmentLanguage($stopSuccessIds);

        $apiClient      = new ApiClient('sms_rpc', '', 'send', 'th');
        foreach ($staffInfo as $item) {
            if(empty($item['mobile'])){
                $this->logger->notice(['params'=>$item,'旷工停职'=>'mobile empty']);
                continue;
            }
            $staffLang    = $staffsLang[$item['staff_info_id']] ?? getCountryDefaultLang();
            if(strtolower($staffLang) == 'my'){
                $staffLang    =  getCountryDefaultLang();
            }
            $t            = BaseService::getTranslation($staffLang);
            $content = $t->_('part_time_ic_continuous_absenteeism_sms');
            $rpcParam = [
                'src'    => 'hr-continuous-absenteeism-suspend',
                'mobile' => $item['mobile'],
                'msg'    => $content,
                'type'   => 0,
                'code'   => 'th',
                'delay'  => 0,
            ];
            $apiClient->setParams([$rpcParam]);
            $apiClient->execute();
        }
    }


    /**
     * hold类型
     * @param $hire_type -- 雇佣类型
     * @return string
     */
    public function getHoldType($hire_type): string
    {
        //hold类型(1.工资hold,2.提成hold)
        $type = '1,2';
        if (in_array($hire_type,HrStaffInfoModel::$agentTypeTogether)) {
            $type = '2';
        }
        return $type;
    }

    /**
     * hold管理
     * @param $staffIds
     */
    public function handleHold($staffIds)
    {
        $addHour     = $this->getDi()->get('config')->application->add_hour;
        $holdManage  = new HoldManageService();
        $holdSuccess = [];
        $holdError   = [];
        $staffInfo   = (new StaffInfoService())->getStaffInfoListByStaffIds(array_values($staffIds));
        foreach ($staffInfo as $item) {
            $param                  = [
                'type'        => $this->getHoldType($item['hire_type']),   //hold类型(1.工资hold,2.提成hold)
                'hold_reason' => 'off_3_days',                             //hold原因
                'hold_remark' => '',                                       //hold备注
                'hold_time'   => gmdate('Y-m-d', time() + $addHour * 3600),//hold时间
                'hold_source' => 3,                                        //来源，固定是3
            ];
            $param['staff_info_id'] = $item['staff_info_id'];
            $bool                   = $holdManage->synchronizeHoldStaff($param);
            if ($bool) {
                $holdSuccess[] = $item['staff_info_id'];
            } else {
                $holdError[] = $item['staff_info_id'];
            }
        }
        $model              = TripleToLeaveModel::class;
        $bind['updated_at'] = date('Y-m-d H:i:s');
        $bind['created_at'] = date('Y-m-d') . ' 00:00:00';
        $sql                = "update {$model} set hold_state= :hold_state:,updated_at=:updated_at: where staff_info_id in({staff_ids:array}) and type=1 and created_at >= :created_at: ";
        if ($holdSuccess) {
            $bind['hold_state'] = TripleToLeaveModel::HOLD_STATE_SUCCESS;
            $bind['staff_ids']  = $holdSuccess;
            $this->modelsManager->executeQuery($sql, $bind)->success();
        }
        if ($holdError) {
            $bind['hold_state'] = TripleToLeaveModel::HOLD_STATE_ERROR;
            $bind['staff_ids']  = $holdError;
            $this->modelsManager->executeQuery($sql, $bind)->success();
        }
    }

    /**
     *  获取员工停职日期（马来需要重写）
     * @param $staffId
     * @return false|string
     */
    public function getStopDutiesDate($staffId)
    {
        return date('Y-m-d');
    }

    /**
     * 发送邮件
     * @param $staffIds
     * @throws Exception
     */
    public function sendEmail($staffIds)
    {
        $this->logger->info("doAbsenteeism sendEmail staffIds:".json_encode($staffIds,JSON_UNESCAPED_UNICODE));

        $hrStaffInfo = HrStaffInfoModel::class;

        $sql = "SELECT 
                hsi.staff_info_id
                ,hsi.name staff_info_name
                ,hsi.manger as higher_staff_info_id
                ,hsi_t.name as higher_staff_info_name
                ,hsi_t.email as higher_staff_info_email
                ,hsi_t.personal_email as higher_staff_info_personal_email            
            FROM {$hrStaffInfo} hsi 
            LEFT JOIN {$hrStaffInfo} hsi_t on hsi_t.staff_info_id = hsi.manger
            WHERE hsi.staff_info_id IN ({staff_ids:array})
            
            GROUP BY hsi.id ";
        $bind['staff_ids'] = $staffIds;
        $info = '';
        $data = $this->modelsManager->executeQuery($sql, $bind)->toArray();

        $this->logger->info("doAbsenteeism HigherStaff:".json_encode($data,JSON_UNESCAPED_UNICODE));

        foreach ($data as $staffInfo) {
            $mail_content = $this->getHigherStaffMailContent($staffInfo);
            $info .= "{$staffInfo['staff_info_id']} （{$staffInfo['staff_info_name']}）";
            $title = $this->getHigherStaffMailTitle();
            try {
                //发邮件给上级
                if ($staffInfo['higher_staff_info_email']) {
                    BiMail::send($staffInfo['higher_staff_info_email'], $title, $mail_content);
                }
            }catch (\Exception $e) {
                $this->logger->info("higher_staff_info_email error".json_encode(['email' =>$staffInfo['higher_staff_info_email'],'title' => $title,'mail_content' => $mail_content],JSON_UNESCAPED_UNICODE));
            }
            try {
                //发邮件给上级
                if ($staffInfo['higher_staff_info_personal_email']) {
                    BiMail::send($staffInfo['higher_staff_info_personal_email'], $title, $mail_content);
                }
            }catch (\Exception $e) {
                $this->logger->info("higher_staff_info_email error".json_encode(['email' =>$staffInfo['higher_staff_info_personal_email'],'title' => $title,'mail_content' => $mail_content],JSON_UNESCAPED_UNICODE));
            }

        }
        $this->logger->info("doAbsenteeism HigherStaffMail:".json_encode($staffIds,JSON_UNESCAPED_UNICODE));

        //马来不发邮件了
        if (isCountry('MY')) {
            return ;
        }
        //给HR发送邮件
        if ($info && $this->runtime == 'pro' ) {
            $mail_content = $this->getHRMailContent($info);
            $attachments = $this->getEmailAttachments($staffIds);
            $this->sendHR($this->getHigherStaffMailTitle(), $mail_content, $attachments);
            $this->logger->info("doAbsenteeism sendHR:".json_encode(['staff'=>$staffIds,'info'=>$info],JSON_UNESCAPED_UNICODE));
        }

    }

    /**
     *
     * @param $staffIds
     *
     */
    public function getEmailAttachments($staffIds)
    {
        return [];
    }

    /**
     * @return string
     *
     */
    public function getHigherStaffMailTitle(): string
    {
        return "Stop duty staff email";

    }

    /**
     * 获取给上级邮件的内容 （不同国家重写）
     * @param $staffInfo
     * @return string
     */
    public function getHigherStaffMailContent($staffInfo): string
    {
        [$absentDays, $leaveDays] = (new SettingEnvService())->getAbsentAndLeaveDays();
        return sprintf($this->template_map['mail2higher_staff']
            , $staffInfo['higher_staff_info_id'] . '(' . $staffInfo['higher_staff_info_name'] . ')'
            , $staffInfo['staff_info_id']
            , $staffInfo['staff_info_name']
            , $absentDays
            , $leaveDays
            , $leaveDays
        );
    }

    /**
     * 获取给HR的邮件内容 （不同国家重写）
     * @param $info
     * @return string
     */
    public function getHRMailContent($info): string
    {
        [$absentDays, $leaveDays] = (new SettingEnvService())->getAbsentAndLeaveDays();
       return sprintf($this->template_map['mail2hris'],
            $info,
           $absentDays,
           $leaveDays,
           $leaveDays
        );
    }

    /**
     * 发送邮件给Hr
     * 不同国家可重写
     * @param $title
     * @param $content
     * @param array $attachments
     * @return bool
     * @throws \PHPMailer\PHPMailer\Exception
     */
    public function sendHR($title, $content, $attachments = []): bool
    {
        $mail         = (new \App\Library\Mailer((new BiMail)->initConfig()));
        $mail_address = (new SettingEnvService())->getSetVal('stop_duty_absent_email', ',');
        if (empty($mail_address)) {
            $this->logger->alert('stop_duty_absent_email 邮箱为空');
            return true;
        }
        return $mail->send($mail_address, $title, $content, $attachments);
    }

    /**
     * 旷工计算
     * @param $staffInfo
     * @param $startTime
     * @param $endTime
     * @param $absentDaysLeave
     * @return array
     */
    public function handleAbsent($staffInfo, $startTime, $endTime, $absentDaysLeave): array
    {
        $markIncreaseStatRangeStaff = []; //标记增加范围统计的员工
        $checkRangeDate = DateHelper::DateRange(strtotime($startTime), strtotime($endTime));
        $checkRangeDate = array_reverse($checkRangeDate);
        $staffInfoChunk = array_chunk($staffInfo, 800);

        //获取打卡白名单
        $attendance_white_list = (new AttendanceWhiteListService())->getAttendanceWhiteListData(['start_date'=>$startTime,'end_date'=>$endTime]);
        $this->getDI()->get('logger')->info('handleAbsent attendanceWhiteList:'.json_encode($attendance_white_list). ' data =>'.json_encode(['start_date'=>$startTime,'end_date'=>$endTime]));

        foreach ($staffInfoChunk as $itemChunk) {
            $this->getDI()->get('logger')->info("handleAbsent chunkInfo:" . json_encode(['info' => $itemChunk, 'startTime' => $startTime, 'endTime' => $endTime, 'absentDaysLeave' => $absentDaysLeave]));
            //开始获取条件数据
            [$staffOffDays, $staffWorkAttendance, $staffLeaveDay, $staffHoliday, $staffBusinessDay] = $this->initAbsentFilterData($itemChunk, $startTime, $endTime);
            foreach ($itemChunk as $staff) {
                foreach ($checkRangeDate as $date) {
                    //存在打卡 请假  早于入职日期 出差&外出打卡审批
                    // 如果在打卡白名单
                    if (
                        (isset($staffWorkAttendance[$staff['staff_info_id']]) && in_array($date, $staffWorkAttendance[$staff['staff_info_id']]))
                    || (isset($staffLeaveDay[$staff['staff_info_id']]) && in_array($date, $staffLeaveDay[$staff['staff_info_id']]))
                    || (strtotime($date) < strtotime($staff['hire_date']))
                        || (isset($staffBusinessDay[$staff['staff_info_id']]) && in_array($date, $staffBusinessDay[$staff['staff_info_id']]))
                        || in_array($staff['staff_info_id'],$attendance_white_list[$date]['type_paid_locally'] ?? [] )  //当地发薪资不用打卡名单
                        || in_array($staff['staff_info_id'],$attendance_white_list[$date]['type_not_paid_locally'] ?? [] )  //不在当地发薪资不用打卡名单
                    ) {
                        break;
                    }
                    //存在休息日 存在法定节假日   增加范围统计
                    if ((isset($staffOffDays[$staff['staff_info_id']]) && in_array($date, $staffOffDays[$staff['staff_info_id']]))
                        || (isset($staffHoliday[$staff['staff_info_id']]) && in_array($date, $staffHoliday[$staff['staff_info_id']]))
                    ) {
                        //增加范围统计
                        $this->addMarkRange($markIncreaseStatRangeStaff,$startTime ,$date , $staff);
                        continue;
                    }
                    //统计旷工日期
                    if (isset($this->staffAbsentDate[$staff['staff_info_id']])) {
                        array_push($this->staffAbsentDate[$staff['staff_info_id']], $date);
                    } else {
                        $this->staffAbsentDate[$staff['staff_info_id']] = [$date];
                    }
                    //标记达到连续旷工需要停职的员工
                    if ($this->absentDays == count($this->staffAbsentDate[$staff['staff_info_id']])) {
                        array_push($this->staffAbsent, $staff['staff_info_id']);
                        break;
                    }
                    //增加范围统计
                    $this->addMarkRange($markIncreaseStatRangeStaff,$startTime ,$date , $staff);
                }
            }
        }

        return $markIncreaseStatRangeStaff;
    }

    /**
     * 标记增加范围统计的员工
     * 每个查询梯度最后一天结束后，当前员工还没有满足连续旷工天数 则进入下一个范围筛选
     */
    public function addMarkRange(&$mark,$startTime ,$date , $staff)
    {
        $staffAbsentDateCount = $this->staffAbsentDate[$staff['staff_info_id']] ?? [];
        if ($startTime == $date && count($staffAbsentDateCount) < $this->absentDays) {
            array_push($mark, $staff);
        }
    }
    /**
     * 初始化旷工筛选数据
     * @param $staffInfo
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function initAbsentFilterData($staffInfo, $startTime, $endTime): array
    {
        $staffIds = array_column($staffInfo, 'staff_info_id');
        $this->getDI()->get('logger')->info("initAbsentFilterData param:" . json_encode(['$staffIds' => $staffIds, '$startTime' => $startTime, '$endTime' => $endTime]));
        $staffOffDays = $this->getStaffOffDays($staffInfo, $startTime, $endTime);
        $staffWorkAttendance = $this->getStaffWorkAttendance($staffIds, $startTime, $endTime);
        $staffLeaveDay = $this->getLeaveDate($staffIds, $startTime, $endTime);
        $staffHoliday = $this->getHoliday(['date' => $startTime, 'staff' => $staffInfo]);
        $staffBusinessDay = $this->getBusinessDay($staffIds, $startTime, $endTime);
        $this->getDI()->get('logger')->info("initAbsentFilterData data:" . json_encode(['$staffOffDays' => $staffOffDays, '$staffWorkAttendance' => $staffWorkAttendance, '$staffLeaveDay' => $staffLeaveDay, '$staffHoliday' => $staffHoliday, '$staffBusinessDay' => $staffBusinessDay]));
        return [$staffOffDays, $staffWorkAttendance, $staffLeaveDay, $staffHoliday, $staffBusinessDay];
    }

    /**
     * 获取员工法定节假日
     * 统一以人为维度 保证上层调用统一
     * th vn id 使用一个 la my ph 重写
     * @param $param ['date'=> $startTime, 'staff' => $staffInfo]
     * @return array[]
     */
    public function getHoliday($param): array
    {
        // 指定部门和职位  或者指定工号 增加ph
        $data = [];
        //员工个人的PH
        $staffPublicHoliday = ( new StaffPublicHolidayService())->getMultiStaffData(array_column($param['staff'], 'staff_info_id'),$param['date']);
        $holiday = (new HolidayService())->getHoliday($param);
        if (isset($param['staff'])) {
            foreach ($param['staff'] as $item) {
                $data[$item['staff_info_id']] = array_merge($holiday[$item['week_working_day']]??[], $staffPublicHoliday[$item['staff_info_id']]??[]);
            }
        }
        return $data;
    }

    /**
     * 获取员工基础信息
     * @param $staffIds
     * @param  $suspensionHandleDate
     * @return array
     */
    public function getStaffInfo($staffIds, $suspensionHandleDate): array
    {

        $bind['hire_date'] = date('Y-m-d 00:00:00', strtotime("{$suspensionHandleDate} -{$this->absentDays} day"));
        $model = HrStaffInfoModel::class;
        $sysStore = SysStoreModel::class;
        $sql = "select hsi.staff_info_id,hsi.week_working_day,hsi.sys_store_id,hsi.sex,ss.province_code,hsi.node_department_id,hsi.job_title,DATE(hsi.hire_date) as hire_date
                from {$model} hsi
                LEFT JOIN {$sysStore} ss on hsi.sys_store_id = ss.id
                where hsi.formal in (1,4) and hsi.state = 1 and hsi.is_sub_staff = 0 and hsi.hire_date < :hire_date:
                ";
        if ($staffIds) {
            $bind['staff_ids'] = $staffIds;
            $sql .= " and staff_info_id in ({staff_ids:array})";
        }

        if ($this->hire_type_condition) {
            $sql .= ' and ' .$this->hire_type_condition;
        }

        $data = $this->modelsManager->executeQuery($sql, $bind)->toArray();
        return $data ?: [];
    }

    /**
     * 获取员工休息日
     * @param $staffInfo
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getStaffOffDays($staffInfo, $startTime, $endTime): array
    {
        $staffIds = array_column($staffInfo, 'staff_info_id');
        $returnData = [];
        $dateRange = DateHelper::DateRange(strtotime($startTime), strtotime($endTime));
        $staffDays = (new AttendanceStatisticsService())->getStaffOffDayData($startTime, $endTime, $staffIds);
        foreach ($staffInfo as $item) {
            foreach ($dateRange as $date) {
                if (isset($staffDays[$item['staff_info_id'] . '-' . $date])) {
                    $returnData[$item['staff_info_id']][] = $date;
                }
            }
        }
        return $returnData;
    }

    /**
     * 员工打卡日期
     * @param $staffIds
     * @param $startTime
     * @param $endTime
     * @return array  ['staff_id' => [date,date...]]
     */
    public function getStaffWorkAttendance($staffIds, $startTime, $endTime): array
    {
        $returnData = [];
        $model = StaffWorkAttendanceModel::class;
        $sql = "select staff_info_id,attendance_date from {$model} where staff_info_id in ({staff_ids:array}) and  attendance_date between  :start_time: and :end_time:";
        $bind['staff_ids'] = $staffIds;
        $bind['start_time'] = $startTime;
        $bind['end_time'] = $endTime;
        $data = $this->modelsManager->executeQuery($sql, $bind)->toArray();
        foreach ($data as $item) {
            if (isset($returnData[$item['staff_info_id']])) {
                array_push($returnData[$item['staff_info_id']], $item['attendance_date']);
            } else {
                $returnData[$item['staff_info_id']] = [$item['attendance_date']];
            }
        }
        return $returnData;
    }

    /**
     * 获取请假日期
     * @param $staffIds
     * @param $startTime
     * @param $endTime
     * @return array ['staff_id' => [date,date...]]
     */
    public function getLeaveDate($staffIds, $startTime, $endTime): array
    {
        $returnData = [];
        $staffAudit = StaffAuditModel::class;
        $staffAuditLeaveSplit = StaffAuditLeaveSplitModel::class;
        $sql = "select s.staff_info_id, s.date_at
                from {$staffAuditLeaveSplit} s
                join {$staffAudit} a on s.staff_info_id = a.staff_info_id and a.audit_id = s.audit_id               
                where a.staff_info_id in  ({staff_ids:array})
                and a.audit_type = 2 
                and a.status = 2
                and a.leave_type !=15
                and s.date_at between  :start_time: and :end_time: ";
        $bind['staff_ids'] = $staffIds;
        $bind['start_time'] = $startTime;
        $bind['end_time'] = $endTime;
        $data = $this->modelsManager->executeQuery($sql, $bind)->toArray();
        foreach ($data as $item) {
            if (isset($returnData[$item['staff_info_id']])) {
                array_push($returnData[$item['staff_info_id']], $item['date_at']);
            } else {
                $returnData[$item['staff_info_id']] = [$item['date_at']];
            }
        }
        return $returnData;
    }

    /**
     * 获取出差打卡临时表数据
     * @param $staffIds
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getBusinessDay($staffIds, $startTime, $endTime): array
    {
        $returnData = [];
        $model = StaffAuditReissueForBusinessModel::class;
        $sql = "select staff_info_id,attendance_date from {$model} where staff_info_id in ({staff_ids:array}) and  attendance_date between  :start_time: and :end_time: and status in (1,2)";
        $bind['staff_ids'] = $staffIds;
        $bind['start_time'] = $startTime;
        $bind['end_time'] = $endTime;
        $data = $this->modelsManager->executeQuery($sql, $bind)->toArray();
        foreach ($data as $item) {
            if (isset($returnData[$item['staff_info_id']])) {
                array_push($returnData[$item['staff_info_id']], $item['attendance_date']);
            } else {
                $returnData[$item['staff_info_id']] = [$item['attendance_date']];
            }
        }
        return $returnData;
    }

    /**
     * 手动停职员工
     */
    public function manualStopStaffs($staffIds,$syncStaffState)
    {
        if (empty($staffIds)) {
            return;
        }
        $updateStateData = [];
        foreach ($staffIds as $staffId) {
            $updateStateData[] = [
                "staff_info_id" => $staffId,
                "type" => 3,// 1在职 2离职 3停职
                "day" => $this->getStopDutiesDate($staffId),
                'stop_duty_reason' => 6, //三天未打卡
                'payment_markup' => 8,   //连续旷工
                'leave_reason' => 2,
                'payment_state' => 2,
                'src' => 'hcm',
            ];
        }
        $stopSuccessIds = $stopErrorIds = [];
        //进行停职
        $updateStateDataChunk = array_chunk($updateStateData, 5);
        $apiClient            = new ApiClient('hris', '', 'sync_staff_state');
        foreach ($updateStateDataChunk as $item) {
            if ($syncStaffState) {
                $apiClient->setParams([$item]);
                $result = $apiClient->execute();
                if (isset($result['code'], $result['data']) && $result['code'] == 1) {
                    foreach ($result['data'] as $body) {
                        if ('ok' == $body['msg']) {
                            $stopSuccessIds[] = $body['staff_info_id'] ?? 0;
                        } else {
                            $stopErrorIds[] = $body['staff_info_id'] ?? 0;
                        }
                    }
                }
            }

        }
        if (!$syncStaffState) {
            $stopSuccessIds = $staffIds;
        }
        var_dump($stopSuccessIds, $stopErrorIds);
        if ($stopSuccessIds) {
            //修正 triple_to_leave 状态
            $find = TripleToLeaveModel::find([
                'conditions' => 'created_at >=:created_at: and  type =1  and stop_state =2 and staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $stopSuccessIds,'created_at' => date('Y-m-d 00:00:00')],
            ]);
            if ($find) {
                $find->update([
                    'stop_state' => TripleToLeaveModel::STOP_TYPE_SUCCESS,
                ]);
            }
            $this->logger->info("doAbsenteeism hold  handleHoldData:" . json_encode($stopSuccessIds,
                    JSON_UNESCAPED_UNICODE));
            //hold管理
            $this->handleHold($stopSuccessIds);
            //给上级和Hr发送邮件
            try {
                $this->sendEmail($stopSuccessIds);
            } catch (\Exception $e) {
                $this->logger->error("doAbsenteeism sendEmail error:" . json_encode([
                        'staffAbsent' => $stopSuccessIds,
                        'message'     => $e->getMessage(),
                        'file'        => $e->getFile(),
                        'line'        => $e->getLine(),
                    ], JSON_UNESCAPED_UNICODE));
            }
        }
    }

    public function doAbsenteeismFix(): array
    {
        $insertData = [];
        $updateStateData = [];
        $stopSuccessIds = [];
        $stopErrorIds = [];
        $type = TripleToLeaveModel::TYPE_CONTINUE_ABSENT;//1:连续旷工


        if(isCountry('MY')){

            $s = '{"120410":["2024-03-03","2024-03-04"],"122441":["2024-03-03","2024-03-04"],"123430":["2024-03-03","2024-03-04"],"126970":["2024-03-03","2024-03-04"],"128157":["2024-03-03","2024-03-04"],"128380":["2024-03-03","2024-03-04"],"128778":["2024-03-03","2024-03-04"],"129989":["2024-03-03","2024-03-04"],"130043":["2024-03-03","2024-03-04"],"130600":["2024-03-02","2024-03-04"],"132084":["2024-03-03","2024-03-04"],"132647":["2024-03-03","2024-03-04"],"133179":["2024-03-03","2024-03-04"],"133734":["2024-03-02","2024-03-04"],"133824":["2024-03-02","2024-03-04"],"135377":["2024-03-02","2024-03-03"],"137145":["2024-03-03","2024-03-04"],"137864":["2024-03-02","2024-03-04"],"139023":["2024-03-03","2024-03-04"],"139099":["2024-03-03","2024-03-04"],"139208":["2024-03-03","2024-03-04"],"139411":["2024-03-03","2024-03-04"],"139789":["2024-03-03","2024-03-04"],"141350":["2024-03-03","2024-03-04"],"141624":["2024-03-03","2024-03-04"],"141737":["2024-03-03","2024-03-04"],"141751":["2024-03-03","2024-03-04"],"142070":["2024-03-03","2024-03-04"],"142975":["2024-03-03","2024-03-04"],"143362":["2024-03-03","2024-03-04"],"2000022":["2024-03-03","2024-03-04"],"143798":["2024-03-03","2024-03-04"],"143812":["2024-03-02","2024-03-04"],"2000405":["2024-03-02","2024-03-04"],"2000634":["2024-03-03","2024-03-04"],"2000663":["2024-03-03","2024-03-04"],"2000666":["2024-03-03","2024-03-04"],"2000733":["2024-03-03","2024-03-04"],"2000737":["2024-03-03","2024-03-04"],"2000759":["2024-03-03","2024-03-04"],"2000788":["2024-03-03","2024-03-04"],"2000798":["2024-03-03","2024-03-04"],"144011":["2024-03-03","2024-03-04"],"2000829":["2024-03-03","2024-03-04"],"2000835":["2024-03-03","2024-03-04"],"2000839":["2024-03-03","2024-03-04"],"144014":["2024-03-03","2024-03-04"],"2000850":["2024-03-03","2024-03-04"],"2000853":["2024-03-03","2024-03-04"],"2000867":["2024-03-03","2024-03-04"],"2000875":["2024-03-03","2024-03-04"],"2000883":["2024-03-03","2024-03-04"],"144037":["2024-03-03","2024-03-04"],"2000894":["2024-03-03","2024-03-04"],"2000905":["2024-03-03","2024-03-04"],"2000906":["2024-03-03","2024-03-04"]}';

        }
        if(isCountry('TH')){
            $s = '{"41025":["2024-03-02","2024-03-03","2024-03-04"],"59305":["2024-03-01","2024-03-03","2024-03-04"],"61929":["2024-03-01","2024-03-02","2024-03-04"],"80130":["2024-03-01","2024-03-03","2024-03-04"],"617443":["2024-03-01","2024-03-03","2024-03-04"],"624466":["2024-03-01","2024-03-02","2024-03-04"],"626176":["2024-03-02","2024-03-03","2024-03-04"],"627791":["2024-03-02","2024-03-03","2024-03-04"],"631568":["2024-03-02","2024-03-03","2024-03-04"],"635232":["2024-03-02","2024-03-03","2024-03-04"],"638697":["2024-03-02","2024-03-03","2024-03-04"],"647440":["2024-03-01","2024-03-02","2024-03-04"],"656762":["2024-03-02","2024-03-03","2024-03-04"],"661047":["2024-03-02","2024-03-03","2024-03-04"],"662098":["2024-03-02","2024-03-03","2024-03-04"],"664718":["2024-03-02","2024-03-03","2024-03-04"],"665134":["2024-03-02","2024-03-03","2024-03-04"],"668393":["2024-03-02","2024-03-03","2024-03-04"],"668430":["2024-03-02","2024-03-03","2024-03-04"],"670374":["2024-03-02","2024-03-03","2024-03-04"],"672725":["2024-03-02","2024-03-03","2024-03-04"],"672830":["2024-03-02","2024-03-03","2024-03-04"],"674858":["2024-03-02","2024-03-03","2024-03-04"],"675527":["2024-03-02","2024-03-03","2024-03-04"],"676312":["2024-03-02","2024-03-03","2024-03-04"],"677399":["2024-03-02","2024-03-03","2024-03-04"],"677458":["2024-03-02","2024-03-03","2024-03-04"],"678976":["2024-03-02","2024-03-03","2024-03-04"],"679589":["2024-03-02","2024-03-03","2024-03-04"],"680705":["2024-03-02","2024-03-03","2024-03-04"],"681602":["2024-03-02","2024-03-03","2024-03-04"],"682668":["2024-03-02","2024-03-03","2024-03-04"],"683044":["2024-03-01","2024-03-02","2024-03-04"],"683267":["2024-03-02","2024-03-03","2024-03-04"],"683417":["2024-03-01","2024-03-03","2024-03-04"],"684404":["2024-03-02","2024-03-03","2024-03-04"],"685768":["2024-03-01","2024-03-03","2024-03-04"],"686005":["2024-03-02","2024-03-03","2024-03-04"],"2000126":["2024-03-02","2024-03-03","2024-03-04"],"686291":["2024-03-01","2024-03-03","2024-03-04"],"686716":["2024-03-02","2024-03-03","2024-03-04"],"686741":["2024-03-02","2024-03-03","2024-03-04"],"2000379":["2024-03-01","2024-03-02","2024-03-04"],"687096":["2024-03-02","2024-03-03","2024-03-04"],"2000574":["2024-03-02","2024-03-03","2024-03-04"],"687309":["2024-03-02","2024-03-03","2024-03-04"],"2000695":["2024-03-02","2024-03-03","2024-03-04"],"687476":["2024-03-02","2024-03-03","2024-03-04"],"2000743":["2024-03-02","2024-03-03","2024-03-04"],"687510":["2024-03-02","2024-03-03","2024-03-04"],"2000864":["2024-03-02","2024-03-03","2024-03-04"],"2001112":["2024-03-02","2024-03-03","2024-03-04"],"2001182":["2024-03-02","2024-03-03","2024-03-04"],"688228":["2024-03-02","2024-03-03","2024-03-04"],"2001362":["2024-03-02","2024-03-03","2024-03-04"],"688478":["2024-03-02","2024-03-03","2024-03-04"],"688519":["2024-03-02","2024-03-03","2024-03-04"],"688523":["2024-03-02","2024-03-03","2024-03-04"],"688535":["2024-03-02","2024-03-03","2024-03-04"],"688694":["2024-03-01","2024-03-02","2024-03-04"],"2001817":["2024-03-02","2024-03-03","2024-03-04"],"688712":["2024-03-01","2024-03-03","2024-03-04"],"2001837":["2024-03-02","2024-03-03","2024-03-04"],"2001857":["2024-03-01","2024-03-02","2024-03-04"],"2001875":["2024-03-02","2024-03-03","2024-03-04"],"2001938":["2024-03-02","2024-03-03","2024-03-04"],"2001939":["2024-03-02","2024-03-03","2024-03-04"],"2001940":["2024-03-02","2024-03-03","2024-03-04"],"2001994":["2024-03-02","2024-03-03","2024-03-04"],"2002046":["2024-03-01","2024-03-03","2024-03-04"],"2002072":["2024-03-02","2024-03-03","2024-03-04"],"2002080":["2024-03-02","2024-03-03","2024-03-04"],"2002094":["2024-03-02","2024-03-03","2024-03-04"],"2002104":["2024-03-02","2024-03-03","2024-03-04"],"2002121":["2024-03-02","2024-03-03","2024-03-04"],"2002122":["2024-03-02","2024-03-03","2024-03-04"],"2002125":["2024-03-02","2024-03-03","2024-03-04"],"2002134":["2024-03-02","2024-03-03","2024-03-04"],"2002135":["2024-03-02","2024-03-03","2024-03-04"],"2002140":["2024-03-02","2024-03-03","2024-03-04"],"2002141":["2024-03-02","2024-03-03","2024-03-04"],"688931":["2024-03-02","2024-03-03","2024-03-04"],"2002147":["2024-03-02","2024-03-03","2024-03-04"],"2002156":["2024-03-02","2024-03-03","2024-03-04"],"2002163":["2024-03-02","2024-03-03","2024-03-04"],"2002167":["2024-03-02","2024-03-03","2024-03-04"],"2002190":["2024-03-02","2024-03-03","2024-03-04"],"2002193":["2024-03-02","2024-03-03","2024-03-04"],"2002196":["2024-03-02","2024-03-03","2024-03-04"],"688948":["2024-03-02","2024-03-03","2024-03-04"],"688957":["2024-03-02","2024-03-03","2024-03-04"],"2002222":["2024-03-02","2024-03-03","2024-03-04"],"2002227":["2024-03-02","2024-03-03","2024-03-04"],"2002230":["2024-03-02","2024-03-03","2024-03-04"],"2002253":["2024-03-02","2024-03-03","2024-03-04"],"2002256":["2024-03-02","2024-03-03","2024-03-04"],"688992":["2024-03-02","2024-03-03","2024-03-04"]}';
        }
        if(isCountry('PH')){
            $s= '{"128843":["2024-03-01","2024-03-02","2024-03-04"],"140873":["2024-03-02","2024-03-03","2024-03-04"],"143700":["2024-03-02","2024-03-03","2024-03-04"],"145223":["2024-03-01","2024-03-02","2024-03-04"],"149910":["2024-03-01","2024-03-02","2024-03-04"],"150358":["2024-03-02","2024-03-03","2024-03-04"],"150786":["2024-03-01","2024-03-02","2024-03-04"],"151969":["2024-03-02","2024-03-03","2024-03-04"],"163565":["2024-03-01","2024-03-03","2024-03-04"],"166248":["2024-03-02","2024-03-03","2024-03-04"],"169529":["2024-03-02","2024-03-03","2024-03-04"],"171943":["2024-03-02","2024-03-03","2024-03-04"],"172514":["2024-03-02","2024-03-03","2024-03-04"],"176262":["2024-03-02","2024-03-03","2024-03-04"],"178384":["2024-03-02","2024-03-03","2024-03-04"],"179058":["2024-03-02","2024-03-03","2024-03-04"],"179607":["2024-03-02","2024-03-03","2024-03-04"],"180637":["2024-03-02","2024-03-03","2024-03-04"],"181070":["2024-03-02","2024-03-03","2024-03-04"],"182143":["2024-03-02","2024-03-03","2024-03-04"],"182568":["2024-03-01","2024-03-02","2024-03-03"],"182743":["2024-03-02","2024-03-03","2024-03-04"],"182762":["2024-03-02","2024-03-03","2024-03-04"],"183134":["2024-03-01","2024-03-03","2024-03-04"],"183199":["2024-03-01","2024-03-02","2024-03-04"],"183585":["2024-03-02","2024-03-03","2024-03-04"],"183645":["2024-03-02","2024-03-03","2024-03-04"],"184505":["2024-03-02","2024-03-03","2024-03-04"],"185718":["2024-03-02","2024-03-03","2024-03-04"],"185740":["2024-03-02","2024-03-03","2024-03-04"],"186058":["2024-03-01","2024-03-02","2024-03-03"],"186268":["2024-03-01","2024-03-02","2024-03-04"],"186294":["2024-03-01","2024-03-02","2024-03-04"],"186920":["2024-03-02","2024-03-03","2024-03-04"],"187164":["2024-03-02","2024-03-03","2024-03-04"],"187267":["2024-03-01","2024-03-02","2024-03-04"],"187298":["2024-03-01","2024-03-02","2024-03-04"],"187378":["2024-03-01","2024-03-02","2024-03-04"],"187446":["2024-03-02","2024-03-03","2024-03-04"],"187470":["2024-03-02","2024-03-03","2024-03-04"],"187629":["2024-03-02","2024-03-03","2024-03-04"],"187647":["2024-03-02","2024-03-03","2024-03-04"],"187890":["2024-03-02","2024-03-03","2024-03-04"],"187896":["2024-03-02","2024-03-03","2024-03-04"],"187974":["2024-03-02","2024-03-03","2024-03-04"],"187998":["2024-03-02","2024-03-03","2024-03-04"],"188038":["2024-03-02","2024-03-03","2024-03-04"],"188120":["2024-03-02","2024-03-03","2024-03-04"],"188121":["2024-03-02","2024-03-03","2024-03-04"],"188143":["2024-03-02","2024-03-03","2024-03-04"],"188200":["2024-03-02","2024-03-03","2024-03-04"],"188264":["2024-03-02","2024-03-03","2024-03-04"],"188277":["2024-03-02","2024-03-03","2024-03-04"],"188280":["2024-03-02","2024-03-03","2024-03-04"],"188282":["2024-03-01","2024-03-02","2024-03-04"],"188382":["2024-03-02","2024-03-03","2024-03-04"],"188405":["2024-03-02","2024-03-03","2024-03-04"],"188409":["2024-03-02","2024-03-03","2024-03-04"],"188465":["2024-03-01","2024-03-02","2024-03-04"],"188495":["2024-03-02","2024-03-03","2024-03-04"],"188519":["2024-03-02","2024-03-03","2024-03-04"],"188573":["2024-03-02","2024-03-03","2024-03-04"],"188626":["2024-03-01","2024-03-02","2024-03-04"],"188659":["2024-03-02","2024-03-03","2024-03-04"],"188706":["2024-03-02","2024-03-03","2024-03-04"],"188725":["2024-03-02","2024-03-03","2024-03-04"],"188732":["2024-03-02","2024-03-03","2024-03-04"],"188743":["2024-03-02","2024-03-03","2024-03-04"],"188754":["2024-03-02","2024-03-03","2024-03-04"],"188766":["2024-03-02","2024-03-03","2024-03-04"],"2000039":["2024-03-02","2024-03-03","2024-03-04"],"188834":["2024-03-02","2024-03-03","2024-03-04"],"188837":["2024-03-02","2024-03-03","2024-03-04"],"188838":["2024-03-02","2024-03-03","2024-03-04"],"188840":["2024-03-02","2024-03-03","2024-03-04"],"188844":["2024-03-02","2024-03-03","2024-03-04"],"188850":["2024-03-02","2024-03-03","2024-03-04"],"188859":["2024-03-02","2024-03-03","2024-03-04"],"188861":["2024-03-02","2024-03-03","2024-03-04"],"188874":["2024-03-02","2024-03-03","2024-03-04"],"188884":["2024-03-01","2024-03-02","2024-03-04"],"188911":["2024-03-02","2024-03-03","2024-03-04"],"188920":["2024-03-02","2024-03-03","2024-03-04"],"2000045":["2024-03-02","2024-03-03","2024-03-04"]}';
        }
        if(empty($s)){
            dd('22222');
        }


        $this->staffAbsentDate  = json_decode($s,true);


        $this->logger->notice("doAbsenteeism info:" . json_encode(['staffAbsentDate' => $this->staffAbsentDate],JSON_UNESCAPED_UNICODE));
        $this->staffAbsent = array_keys($this->staffAbsentDate);

        if ($this->staffAbsent) {
            foreach ($this->staffAbsent as $staffInfoId) {
                if (isset($this->staffAbsentDate[$staffInfoId])) {
                    $stop_begin_date = $this->getStopBeginDate($this->staffAbsentDate[$staffInfoId]);
                } else {
                    $this->logger->info('doAbsenteeism not found $staffInfoId:'.$staffInfoId);
                    continue;
                }
                $stopSuccessIds[]  = $staffInfoId;
                $insertData[$staffInfoId] = [
                    'staff_info_id' => $staffInfoId,
                    'stop_begin_date' => $stop_begin_date,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'type' => $type,
                    'stop_state' => TripleToLeaveModel::STOP_TYPE_SUCCESS,
                    'stop_duties_date' => $this->getStopDutiesDate($staffInfoId),
                    'stop_date_detail' => isset($this->staffAbsentDate[$staffInfoId]) ? json_encode($this->staffAbsentDate[$staffInfoId]): "",
                ];
                $updateStateData[] = [
                    "staff_info_id" => $staffInfoId,
                    "type" => 3,// 1在职 2离职 3停职
                    "day" => $this->getStopDutiesDate($staffInfoId),
                    'stop_duty_reason' => 6, //三天未打卡
                    'payment_markup' => 8, //连续旷工
                    'leave_reason' => 2,
                    'payment_state' => 2,
                    'src'=>'hcm',
                ];
            }
            if (empty($updateStateData)) {
                $this->logger->info("doAbsenteeism updateStateData empty!!" );
                return $stopSuccessIds;
            }
            //进行停职
            $tripleToLeaveModel = new TripleToLeaveModel();
            $inertRes =$tripleToLeaveModel->batch_insert(array_values($insertData));
            $this->logger->info("doAbsenteeism success:" .json_encode($stopSuccessIds,JSON_UNESCAPED_UNICODE)
                ."doAbsenteeism error:" .json_encode($stopErrorIds,JSON_UNESCAPED_UNICODE)
                . " insertData:" .json_encode($insertData,JSON_UNESCAPED_UNICODE)
                . "insertData result-1  " . $inertRes
            );

            sleep(2);

            $db          = BackyardBaseModel::beginTransaction($this);
            $tripleToLeaveModel = TripleToLeaveModel::findFirst([
                'conditions' => "created_at >= :created_at: and type = 1",
                'bind' => ['created_at' =>  date('Y-m-d 00:00:00')],
            ]);

            if (empty($tripleToLeaveModel)) {
                $inertRes =(new TripleToLeaveModel())->batch_insert(array_values($insertData));
                $this->logger->notice("doAbsenteeism success:" .json_encode($stopSuccessIds,JSON_UNESCAPED_UNICODE)
                    ."doAbsenteeism error:" .json_encode($stopErrorIds,JSON_UNESCAPED_UNICODE)
                    . " insertData:" .json_encode($insertData,JSON_UNESCAPED_UNICODE)
                    . "insertData result-2 " . $inertRes
                );
                if(!$inertRes){
                    $this->logger->error("doAbsenteeism error  insertData:" .json_encode($insertData,JSON_UNESCAPED_UNICODE));
                }
            }
            $db->commit();

            if ($stopSuccessIds) {
                $this->logger->info("doAbsenteeism hold  handleHoldData:" .json_encode($stopSuccessIds,JSON_UNESCAPED_UNICODE));
                //hold管理
                $this->handleHold($stopSuccessIds);
                //给上级和Hr发送邮件
                try {
                    $this->sendEmail($stopSuccessIds);
                } catch (\Exception $e) {
                    $this->logger->error("doAbsenteeism sendEmail error:" . json_encode(['staffAbsent' => $stopSuccessIds, 'message' => $e->getMessage(), 'file' => $e->getFile(), 'line' => $e->getLine()],JSON_UNESCAPED_UNICODE));
                }
            }
        }
        return $stopSuccessIds;
    }
}