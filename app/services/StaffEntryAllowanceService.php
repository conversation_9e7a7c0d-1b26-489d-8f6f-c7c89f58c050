<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\DateHelper;
use App\Models\backyard\StaffEntryAllowanceModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\SysStoreModel;

class StaffEntryAllowanceService extends BaseService
{
    /**
     * 例子
     * 选择七月份点击计算  入职时间是 6月2号 should paid month   7月   already paid month 空  判断逻辑的时间范围是  6月2号～当前脚本计算的时间
     * 选择八月份点击计算   入职时间是 6月2号 should paid month  8月   already paid month 七月  时间范围  6月2号～当前脚本计算的时间
     * 选择的月份就为数据库记录的月份，每个人至多有有两条数据
     *
     */
    /**
     * 入职补贴开始月份
     * @var string
     */
    public $startMonth = '2022-06-01';

    /**
     * ('bike courier','van courier','boat courier','Tricycle Courier')
     * @var int[]
     */
    public $jobTitles = [13, 110, 452, 1000];
    /**
     * 发放入职津贴的额度
     * @var int
     */
    public $paidMonty = 1500;
    public $excelHeader = [
        'staff ID',
        'Branch',
        'position',
        'state',
        'hire date',
        'leave date',
        'Last Work Day',
        'stop duties date',
        'already paid month',
        'already paid',
        'should paid month',
        'should paid',
        'times',
    ];

    /**
     * 跑每月入职补贴
     * @param $month
     * @param $staffIds
     * @param $endTime
     * @return string
     */
    public function setEntryAllowanceData($month, $staffIds,$endTime=''): string
    {
        $data = [];
        $storeIds = $this->getStoreIds();
        if (empty($storeIds)) {
            $this->logger->notice('not Found Store');
            return 'not Found Store';
        }
        //计算固化数据是否符合的时间范围
        if (empty($endTime)) {
            $endTime = date('Y-m-d',strtotime("-1 days"));
        }
        $startTime = $this->getTimeRangeStart($month);
        //入职时间在计算时所选月份的上上月和上月考勤周期
        //入职补贴从2022-06-01生效，因此任何月份文件仅显示入职日期在2022-06-01及以后员工
        [$HireStartTime, $HireEndTime] = $this->getHireTimeRange($month);
        $this->logger->info('time range:' . json_encode([$startTime, $endTime]));
        $bind = ['start' => $HireStartTime, 'end' => $HireEndTime,'unpaid_courier'=>HrStaffInfoModel::HIRE_TYPE_UN_PAID];
        $conditions = 'hire_date >= :start: and hire_date <=:end:  and is_sub_staff=0  and hire_type!=:unpaid_courier:';
        if ($staffIds) {
            $conditions .= ' and staff_info_id in ({ids:array})';
            $bind['ids'] = $staffIds;

        }

        $conditions              .= " and (hire_date < :date_limit_1: and  (hire_date < :date_limit_2: and sys_store_id in ({fruit_store_ids:array})))";
        $bind['date_limit_1']    = '2025-07-01 00:00:00';
        $bind['date_limit_2']    = '2025-05-29 00:00:00';
        $bind['fruit_store_ids'] = ['TH22030602', 'TH22100100'];//水果网点

        $staffFind = HrStaffInfoModel::find([
            'columns' => 'staff_info_id,DATE(hire_date) as hire_date,state,DATE(leave_date) as leave_date,sys_store_id',
            'conditions' => $conditions,
            'bind' => $bind,
        ])->toArray();
        $prepareIds = array_column($staffFind, 'staff_info_id');
        $staffFindMap = array_column($staffFind, null, 'staff_info_id');
        $prepareIds = array_chunk($prepareIds, 300);
        $model = StaffEntryAllowanceModel::class;
        $bindDelete['month'] = $month;
        $deleteSql = "update  {$model} set is_deleted=1 where month=:month:";
        if($staffIds) {
            $deleteSql.= ' and staff_info_id in ({ids:array})';
            $bindDelete['ids'] = $staffIds;
        }
        $this->modelsManager->executeQuery($deleteSql,$bindDelete)->success();
        foreach ($prepareIds as $staffIds) {
            //实际天数
            $solidifyData = $this->getStaffSolidifyData($startTime, $endTime, $staffIds, $storeIds);
            foreach ($solidifyData as $staffId => $item) {
                $shouldEndTime = $endTime;
                if (HrStaffInfoModel::STATE_RESIGN == $staffFindMap[$staffId]['state']
                    && strtotime($staffFindMap[$staffId]['leave_date']) < strtotime($shouldEndTime)
                ) {
                    $shouldEndTime = $staffFindMap[$staffId]['leave_date'];
                }
                //应该匹配的天数
                $countDaysRange = DateHelper::DateRange(strtotime($staffFindMap[$staffId]['hire_date']), strtotime($shouldEndTime));
                $countDay = DateHelper::timeDays($staffFindMap[$staffId]['hire_date'], $shouldEndTime);
                //已经匹配的天数
                $intersect = count(array_intersect($countDaysRange, $item));
                //不符合条件
                if ($countDay != $intersect) {
                    $this->logger->info($staffId . 'can not match' . json_encode([$month, [$staffFindMap[$staffId]['hire_date'], $shouldEndTime], $countDay]));
                    continue;
                }
                //满足条件数据
                $data[] = [
                    'staff_info_id' => $staffId,
                    'month' => $month,
                    'hire_date' => $staffFindMap[$staffId]['hire_date'],
                    'already_paid_month' => '',
                    'already_paid' => 0,
                    'should_paid_month' => $month,
                    'should_paid' => $this->paidMonty,
                    'time' => 1, //默认都是第一次
                ];
            }
        }
        if ($data) {
            $this->logger->info($month . ' handle Staff Ids' . json_encode(array_column($data, 'staff_info_id')));
            $data = array_chunk($data, 200);
            foreach ($data as $datum) {
                $staffIds = array_column($datum, 'staff_info_id');
                $find = StaffEntryAllowanceModel::find([
                    'columns' => 'GROUP_CONCAT(month) as month,staff_info_id',
                    'conditions' => 'staff_info_id in ({ids:array}) and is_deleted=0',
                    'bind' => ['ids' => $staffIds],
                    'group' => 'staff_info_id',
                ])->toArray();
                $findMap = array_column($find, 'month', 'staff_info_id');
                foreach ($datum as $staffEntry) {
                    if (isset($findMap[$staffEntry['staff_info_id']])) {
                        $monthMap = explode(',', $findMap[$staffEntry['staff_info_id']]);
                        if ($staffEntry['month'] < $monthMap[0]) {
                            //大的月份需要把小的月份存储
                            $this->modelsManager->executeQuery("update {$model} set already_paid_month=:already_paid_month:,already_paid=:already_paid:,times=2 where month=:month: and staff_info_id=:id: and is_deleted=0",
                                [
                                    'already_paid_month' => $staffEntry['month'],
                                    'month' => $monthMap[0],
                                    'id' => $staffEntry['staff_info_id'],
                                    'already_paid' => $this->paidMonty,
                                ])->success();
                            (new StaffEntryAllowanceModel())->create($staffEntry);
                        }
                        if ($staffEntry['month'] > $monthMap[0]) {
                            $staffEntry['already_paid_month'] = $monthMap[0];
                            $staffEntry['already_paid'] = $this->paidMonty;
                            $staffEntry['times'] = 2;
                            (new StaffEntryAllowanceModel())->create($staffEntry);
                        }
                    } else {
                        (new StaffEntryAllowanceModel())->create($staffEntry);
                    }
                }
            }
        }
        return 'handle count :' . count($data);
    }

    /**
     * 获取开始时间
     * @param $month
     * @return string
     */
    public function getTimeRangeStart($month): string
    {
        $startTime = date('Y-m-24', strtotime($month . ' -3 month'));
        if ($startTime < $this->startMonth) {
            $startTime = $this->startMonth;
        }
        return $startTime;
    }

    /**
     * 入职时间在计算时所选月份的上上月和上月考勤周期
     *
     */
    public function getHireTimeRange($month): array
    {
        $HireEndTime = date('Y-m-23', strtotime($month . ' -1 month'));
        $HireStartTime = date('Y-m-24', strtotime($HireEndTime . ' -2 month'));
        if ($HireStartTime < $this->startMonth) {
            $HireStartTime = $this->startMonth;
        }
        return [$HireStartTime, $HireEndTime];
    }

    /**
     * 获取入职补贴数据
     * @param $month
     * @param $staffIds
     * @return array
     */
    public function getEntryAllowanceData($month, $staffIds): array
    {
        $data = [];
        $staffInfo = [];
        [$startTime, $endTime] = $this->getHireTimeRange($month);
        $conditions = 'month=:month: and hire_date >=:start: and hire_date <=:end: and is_deleted=0';
        $bind['start'] = $startTime;
        $bind['end'] = $endTime;
        $bind['month'] = $month;
        if ($staffIds) {
            $bind['ids'] = $staffIds;
            $conditions .= ' and  staff_info_id in ({ids:array})';
        }
        $find = StaffEntryAllowanceModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'order' => 'month,staff_info_id',
        ])->toArray();
        $staffIds = array_column($find, 'staff_info_id');
        if ($staffIds) {
            $staffInfo = $this->getStaffInfo($staffIds);
            if ($staffInfo) {
                $staffInfo = array_column($staffInfo, null, 'staff_info_id');
            }
        }
        $yieldData = function ($data) {
            foreach ($data as $item) {
                yield $item;
            }
        };
        foreach ($yieldData($find) as $item) {
            $staffId = $item['staff_info_id'];
            //排除子账号
            if ($staffInfo[$staffId]['is_sub_staff'] == 1) {
                continue;
            }
            if ($staffInfo[$staffId]['wait_leave_state'] == 1) {
                $staffInfo[$staffId]['state_name'] = static::$t->_('wait_leave_state');
            } else {
                $staffInfo[$staffId]['state_name'] = static::$t->_('state_' . $staffInfo[$staffId]['state']);
            }
            if ($staffInfo[$staffId]['state'] == HrStaffInfoModel::STATE_ON_JOB) {
                $staffInfo[$staffId]['stop_duties_date'] = '';
                $staffInfo[$staffId]['leave_date'] = '';
            }
            if ($staffInfo[$staffId]['state'] == HrStaffInfoModel::STATE_RESIGN) {
                $staffInfo[$staffId]['stop_duties_date'] = '';
            }
            if ($staffInfo[$staffId]['state'] == HrStaffInfoModel::STATE_SUSPEND) {
                $staffInfo[$staffId]['leave_date'] = '';
            }
            $staffInfo[$staffId]['last_work_day'] = ($staffInfo[$staffId]['state'] == 2)
                ? date('Y-m-d', strtotime($staffInfo[$staffId]['leave_date'] . ' -1 days'))
                : '';
            $data[] = [
                'staff_id' => $staffId,
                'branch' => $staffInfo[$staffId]['store_name'] ?? '',
                'position' => $staffInfo[$staffId]['job_name'] ?? '',
                'state' => $staffInfo[$staffId]['state_name'] ?? 'unknown',
                'hire_date' => date('d/m/Y', strtotime($item['hire_date'])),
                'leave_date' => $staffInfo[$staffId]['leave_date'] ? date('d/m/Y',
                    strtotime($staffInfo[$staffId]['leave_date'])) : '',
                'last_work_day' => $staffInfo[$staffId]['last_work_day'] ? date('d/m/Y',
                    strtotime($staffInfo[$staffId]['last_work_day'])) : '',
                'stop_duties_date' => $staffInfo[$staffId]['stop_duties_date'] ? date('d/m/Y',
                    strtotime($staffInfo[$staffId]['stop_duties_date'])) : '',
                'already_paid_month' => $item['already_paid_month'],
                'already_paid' => $item['already_paid'],
                'should_paid_month' => $item['should_paid_month'],
                'should_paid' => $item['should_paid'],
                'times' => $item['times'],
            ];
        }
        return $data;
    }

    /**
     * 用户信息
     * @param $staffIds
     * @return mixed
     */
    public function getStaffInfo($staffIds)
    {
        $hr_staff_info = HrStaffInfoModel::class;
        $sys_store = SysStoreModel::class; //网点
        $hr_job_title = HrJobTitleModel::class; //职位
        $user_sql = "select
                 hsi.staff_info_id
                ,hsi.sys_store_id
                ,hsi.job_title
                ,hsi.wait_leave_state
                ,hsi.state
                ,DATE(hsi.hire_date) as hire_date
                ,DATE(hsi.leave_date) as leave_date
                ,DATE(hsi.stop_duties_date) as stop_duties_date
                ,hjt.job_name
                ,ss.name as store_name
                ,hsi.hire_date
                ,hsi.is_sub_staff
                from {$hr_staff_info} hsi
                left join {$sys_store} ss on ss.id = hsi.sys_store_id
                left join {$hr_job_title} hjt on hjt.id = hsi.job_title
                where  staff_info_id in ({ids:array})";
        return $this->modelsManager->executeQuery($user_sql, ['ids' => $staffIds])->toArray();
    }

    /**
     * 获取符合条件的员工固化数据
     * @param $start_date
     * @param $end_date
     * @param $staff_ids
     * @param $storeIds
     * @return array
     */
    public function getStaffSolidifyData($start_date, $end_date, $staff_ids, $storeIds): array
    {
        if (empty($storeIds)) {
            return [];
        }
        $bind = ['start_date' => $start_date, 'end_date' => $end_date];
        $conditions = 'stat_date >= :start_date: and stat_date<=:end_date:';
        if ($staff_ids) {
            $conditions .= " and staff_info_id in ({staff_info_id:array})";
            $bind['staff_info_id'] = $staff_ids;
        }
        $bind['store_id'] = $storeIds;
        $conditions .= " and store_id in ({store_id:array})";
        $bind['job_title'] = $this->jobTitles;
        $conditions .= " and job_title in ({job_title:array}) and state=1";
        $data = HrStaffTransferModel::find([
            'columns' => 'staff_info_id,stat_date',
            'conditions' => $conditions,
            'bind' => $bind,
        ])->toArray();
        $staffInfo = [];
        foreach (static::yieldData()($data) as $item) {
            $staffInfo[$item['staff_info_id']][] = $item['stat_date'];
        }
        return $staffInfo;
    }

    /**
     * 获取设置的网点信息
     * @return array
     */
    public function getStoreIds(): array
    {
        $store = [];
        $storeFind = (new SettingEnvService())->getSetVal('entry_allowance_store');
        if (empty($storeFind)) {
            return $store;
        }
        return  explode(',', $storeFind);
    }

    /**
     * 获取入职补贴数据 for Salary
     * @param $month
     * @param $staffIds
     * @return array
     */
    public function getEntryAllowanceDataForSalary($month, $staffIds): array
    {
        $conditions = 'month=:month: and is_deleted=0';
        $bind['month'] = $month;
        if ($staffIds) {
            $bind['ids'] = $staffIds;
            $conditions .= ' and  staff_info_id in ({ids:array})';
        }
        $find = StaffEntryAllowanceModel::find([
            'columns' => 'staff_info_id,should_paid',
            'conditions' => $conditions,
            'bind' => $bind,
        ])->toArray();
        return array_column($find,'should_paid','staff_info_id');
    }
}
