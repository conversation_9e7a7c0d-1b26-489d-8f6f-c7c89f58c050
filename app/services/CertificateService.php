<?php
/**
 * Created by <PERSON><PERSON><PERSON>tor<PERSON>.
 * User: nick
 * Date: 12/8/21
 * Time: 3:56 PM
 */

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\Enums;
use App\Library\Enums\CertificateEnums;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\FileOssUrlModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\OsStaffInfoExtendModel;
use App\Models\backyard\SendTaxLogModel;
use App\Models\backyard\SendTaxModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\TaxPdf2022Model;
use App\Models\backyard\TaxPdf2023Model;
use App\Models\backyard\TaxPdf2024Model;
use App\Models\backyard\TaxPdfHourWarehouseKeeperModel;
use App\Models\backyard\TaxPdfTempModel;
use App\Models\backyard\TaxSendStateModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SalaryGongziModel;
use App\Repository\HrStaffInfoRepository;
use App\Repository\StaffPayrollCompanyInfoRepository;
use Phalcon\Exception;
use App\Library\Exception\BusinessException;

/**
 * 迁移过来的 class 来源： fbi  app/BLL/SalaryBLL.php
 */
class CertificateService extends BaseService{
    public static $tax_redis_key      = 'tax_for_pdf';
    public static $download_redis_key = 'down_for_pdf';


    public $staff_info = [];//只有页面单个 发送用到
    const CERTIFICATE_TYPE_ON_JOB = 2;
    const CERTIFICATE_TYPE_CIMB = 7;
    public static $certificate_type    = [
        1 => 'salary',
        self::CERTIFICATE_TYPE_ON_JOB => 'on_job',//在职证明
        3 => 'leave_job',
        4 => 'payroll',
        5 => 'salary',
        6 => 'salary',
        7 => 'cimb', //<CIMB开户函>
    ];
    public static $month_list          = [
        1  => 'มกราคม',
        2  => 'กุมภาพันธ์',
        3  => 'มีนาคม',
        4  => 'เมษายน',
        5  => 'พฤษภาคม',
        6  => 'มิถุนายน',
        7  => 'กรกฎาคม',
        8  => 'สิงหาคม',
        9  => 'กันยายน',
        10 => 'ตุลาคม',
        11 => 'พฤศจิกายน',
        12 => 'ธันวาคม',
    ];

    //公司 id
    public $company_ids = [
        Enums::COMPANY_EXPRESS,
        Enums::COMPANY_FULFILLMENT,
        Enums::COMPANY_PAY,
        Enums::COMPANY_MONEY,
        Enums::COMPANY_COMMERCE,//这个公司没有图片 pdf空白就行了
        Enums::COMPANY_HOME,
    ];



    const JOB_CRETIFICATE_CATEGROY = 2;
    const EMPLOYMENT_CRETIFICATE_CATEGROY = 3;
    const WAGES_CRETIFICATE_CATEGROY = 4;
    const JOB_CRETIFICATE_DOWNLOAD = 'job_certificate_download';
    const EMPLOYMENT_CRETIFICATE_DOWNLOAD = 'employment_certificate_download';
    const WAGES_CRETIFICATE_DOWNLOAD = 'wages_certificate_download';
    const TAWI50_DOWNLOAD = '50tawi_download';
    public static $certificateCategory = [
        self::JOB_CRETIFICATE_CATEGROY        => self::JOB_CRETIFICATE_DOWNLOAD,//在职证明
        self::EMPLOYMENT_CRETIFICATE_CATEGROY => self::EMPLOYMENT_CRETIFICATE_DOWNLOAD,//就业证明
        self::WAGES_CRETIFICATE_CATEGROY      => self::WAGES_CRETIFICATE_DOWNLOAD//工资证明
    ];

    //VN ID 使用
    public $v_param = [
        'id'                           => 'Required|Int|IntGt:0|>>>:[id] params error',
        'company_logo_url'             => 'Required|ArrLenGe:1|>>>:[company_logo_url] params error',//公司logo
        'company_name'                 => 'Required|StrLenGeLe:1,200|>>>:[company_name] params error',//公司名称
        'company_short_name'           => 'Required|StrLenGeLe:1,200|>>>:[company_short_name] params error',//公司简称
        'company_registration_no'      => 'StrLenGeLe:0,200|>>>:[company_registration_no] params error',//公司注册号
        'company_address'              => 'Required|StrLenGeLe:1,200|>>>:[company_address] params error',//公司地址
        'company_phone'                => 'StrLenGeLe:0,200|>>>:[company_phone] params error',//公司电话
        'company_web_url'              => 'StrLenGeLe:0,200|>>>:[company_web_url] params error',//公司网址
        'company_email'                => 'StrLenGeLe:0,200|>>>:[company_email] params error',//公司邮箱
        'labor_name'                   => 'StrLenGeLe:0,200|>>>:[labor_name] params error',//代表人姓名
        'labor_job_title'              => 'StrLenGeLe:0,200|>>>:[labor_job_title] params error',//代表人职位
    ];

    /**
     * 制作员工登记表Excel
     * @param $params
     * @return string
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Writer_Exception
     */
    public function makeStaffRegisterFormExcel($params): string
    {
        $returnData = $this->staffRegisterFormData($params);
        $fileOrigin = APP_PATH . '/views/certificate/staff_register_form.xlsx';
        $phpExcel   = \PHPExcel_IOFactory::load($fileOrigin);
        $sheet      = $phpExcel->getActiveSheet();
        $sheet->setCellValue('A1', $returnData['company_name'] ?? '');
        $sheet->setCellValue('A2', $returnData['fixed'] ?? '');
        $sheet->setCellValue('A3', $returnData['addr'] ?? '');
        $sheet->setCellValue('A4', $returnData['manager_mess'] ?? '');
        $styleArray = [
            'alignment' => [
                'horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                'vertical'   => \PHPExcel_Style_Alignment::VERTICAL_CENTER,
            ],
            'borders'   => [
                'allborders' => [
                    'style' => \PHPExcel_Style_Border::BORDER_THIN,
                    'color' => ['argb' => 'FF000000'], // 黑色边框
                ],
            ],
        ];
        $rowNumber  = 6; // 开始的行号
        $list       = $returnData['list'] ?? [];
        foreach ($list as $serialNumber => $row) {
            $column = 'A'; // 开始的列号
            foreach ($row as $index => $cellValue) {
                $cellId = $column . $rowNumber;
                $sheet->setCellValue($cellId, $cellValue);
                if ($index == 7) {
                    //身份证号
                    $sheet->getStyle($cellId)->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
                    $sheet->setCellValueExplicit($cellId, $cellValue);
                } else {
                    $sheet->setCellValue($cellId, $cellValue);
                }
                // 工资
                if ($index == 10) {
                    $sheet->getStyle($cellId)->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED1);
                }
                $sheet->getStyle($cellId)->applyFromArray($styleArray);
                $column++;
            }
            $rowNumber++;
        }
        $filePath  = sys_get_temp_dir() . '/' . $params['file_name'];
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $objWriter->save($filePath);
        return $filePath;
    }
    /**
     * 员工登记表数据
     * @param $params
     * @return array
     */
    public function staffRegisterFormData($params): array
    {
        $jurisdiction = (new StaffService())->getStaffJurisdiction($params['operate_id']);
        if (in_array(HrStaffManageStoreModel::$all_id,$jurisdiction['stores'])) {
            $jurisdiction['stores'] = array_column((new SysService())->getStoreListFromCache('id',false),'id');
        }
        //没有管辖权限
        if (empty($jurisdiction['departments']) &&
            empty($jurisdiction['stores']) &&
            empty($jurisdiction['regions']) &&
            empty($jurisdiction['pieces']) &&
            empty($jurisdiction['store_categories'])
        ) {
            echo ' no permission' . PHP_EOL;
            return [];
        }
        $salaryIdsFind    = SalaryGongziModel::find([
            'columns'    => 'staff_info_id,max(excel_month) as excel_month',
            'conditions' => 'excel_month>=:start: and excel_month<=:end: and staff_info_id in ({staff_ids:array})',
            'bind'       => [
                'start'     => $params['start_month'],
                'end'       => $params['end_month'],
                'staff_ids' => $params['staff_ids'],
            ],
            'group'      => 'staff_info_id',
        ])->toArray();
        $salaryIdsFindMap = array_column($salaryIdsFind, null, 'staff_info_id');
        $firstStaffId     = $maxMonth = null;
        foreach ($params['staff_ids'] as $staffId) {
            if (isset($salaryIdsFindMap[$staffId])) {
                $firstStaffId = $staffId;
                $maxMonth     = $salaryIdsFindMap[$staffId]['excel_month'];
                break;
            }
        }
        if (empty($firstStaffId)) {
            return [];
        }
        $this->logger->info('firstStaffId ' . $firstStaffId . ' maxMonth ' . $maxMonth);
        $firstData = SalaryGongziModel::findFirst([
            'columns'    => 'staff_info_id,sys_store_id,company_id',
            'conditions' => 'excel_month=:excel_month: and staff_info_id=:staff_info_id:',
            'bind'       => [
                'excel_month'   => $maxMonth,
                'staff_info_id' => $firstStaffId,
            ],
        ]);
        //员工登记表薪资数据
        $salaryData = $this->staffRegisterFormSalaryData($params, $firstData->sys_store_id, $salaryIdsFind);

        if (empty($salaryData)) {
            return [];
        }
        $staffIds = array_keys($salaryData);

        $returnData = [];
        //员工登记表中列表上方信息
        $this->staffRegisterFormUpData($firstData, $returnData);
        //员工登记表中列表数据
        $this->staffRegisterFormStaffData($staffIds, $salaryData, $returnData,
            $jurisdiction);
        return $returnData;
    }

    /**
     *  员工登记表中员工薪资信息
     * @param $params
     * @param $sysStoreId
     * @param $salaryIdsFind
     * @return array
     */
    private function staffRegisterFormSalaryData($params, $sysStoreId, $salaryIdsFind): array
    {
        $this->logger->info([$params, $sysStoreId, $salaryIdsFind]);
        $salaryData = [];
        //包含其他员工并且不是总部 查$sysStoreId 所有人
        if ($params['type'] == 'other_export' && $sysStoreId != '-1') {
            $salaryIdsFind = SalaryGongziModel::find([
                'columns'    => 'staff_info_id,max(excel_month) as excel_month',
                'conditions' => 'excel_month>=:start: and excel_month<=:end: and sys_store_id =:sys_store_id:',
                'bind'       => [
                    'start'        => $params['start_month'],
                    'end'          => $params['end_month'],
                    'sys_store_id' => $sysStoreId,
                ],
                'group'      => 'staff_info_id,excel_month',
            ])->toArray();
        }
        //其他情况默认输入的工号
        foreach ($salaryIdsFind as $salaryIdsFindItem) {
            $bind = [
                'excel_month'  => $salaryIdsFindItem['excel_month'],
                'staff_id'     => $salaryIdsFindItem['staff_info_id'],
                'sys_store_id' => $sysStoreId,
            ];
            $staff                             = SalaryGongziModel::findFirst([
                'columns'    => 'staff_info_id,sys_store_id,company_id,salary_base',
                'conditions' => 'excel_month=:excel_month: and staff_info_id =:staff_id: and sys_store_id=:sys_store_id:',
                'bind'       => $bind,
            ]);
            if (empty($staff)) {
                $this->logger->info($bind);
                continue;
            }
            $salaryData[$staff->staff_info_id] = [
                'staff_info_id' => $staff->staff_info_id,
                'excel_month'   => $salaryIdsFindItem['excel_month'],
                'salary_base'   => $staff->salary_base,
            ];
            $this->logger->info($salaryIdsFindItem['excel_month'] . ' ' . $staff->staff_info_id);
        }
        return $salaryData;
    }

    /**
     * 员工登记表中员工信息
     * @param $staffIds
     * @param $salaryData
     * @param $returnData
     * @param $jurisdiction
     * @return void
     */
    private function staffRegisterFormStaffData($staffIds, $salaryData, &$returnData, $jurisdiction): void
    {

        //工号从小至大排列
        sort($staffIds);
        $items         = [
            'BIRTHDAY',            //出生日期
            'REGISTER_COUNTRY',    //户口所在国家
            'REGISTER_PROVINCE',   //户口所在省
            'REGISTER_CITY',       //户口所在市
            'REGISTER_DISTRICT',   //户口所在乡
            'REGISTER_POSTCODES',  //户口所在邮编
            'REGISTER_HOUSE_NUM',  //户口所在门牌号
            'REGISTER_VILLAGE_NUM',//户口所在村号
            'REGISTER_VILLAGE',    //户口所在村
            'REGISTER_ALLEY',      //户口所在巷
            'REGISTER_STREET',     //户口所在街道
        ];
        $staffIdsChunk = array_chunk($staffIds, 1000);
        $staffInfoData = [];
        $staffItems    = [];
        $numberSort    = 1;
        static::$t             = BaseService::getTranslation();
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');
        foreach ($staffIdsChunk as $staffIdsChunkIds) {
            $itemListFind = HrStaffItemsModel::find([
                'conditions' => 'staff_info_id  in ({staff_ids:array}) and item in({item_arr:array})',
                'bind'       => ['staff_ids' => $staffIdsChunkIds, 'item_arr' => $items],
                'columns'    => 'item,value,staff_info_id',
            ]);
            foreach ($itemListFind as $find) {
                $staffItems[$find->staff_info_id][$find->item] = $find->value;
            }
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hsi.staff_info_id,hsi.name,hsi.sex,hsi.nationality,hsi.identity,hsi.hire_date,hsi.state,hsi.leave_date,hsi.job_title,hsi.sys_store_id,hsi.node_department_id,ss.manage_piece,ss.manage_region,ss.category');
            $builder->from(['hsi' =>HrStaffInfoModel::class]);
            $builder->leftJoin(SysStoreModel::class, 'ss.id=hsi.sys_store_id', 'ss');
            $builder->where('hsi.staff_info_id  in ({staff_ids:array})',['staff_ids' => $staffIdsChunkIds]);
            $staffInfoFind = $builder->getQuery()->execute();
            foreach ($staffInfoFind as $item) {
                if (!in_array($item->node_department_id, $jurisdiction['departments'])
                    && !in_array($item->sys_store_id, $jurisdiction['stores'])
                    && !in_array($item->manage_region, $jurisdiction['regions'])
                    && !in_array($item->manage_piece, $jurisdiction['pieces'])
                    && !in_array($item->category, $jurisdiction['store_categories'])) {
                    continue;
                }
                $itemData        = $staffItems[$item->staff_info_id] ?? [];
                $staffInfoData[] = [
                    $numberSort,
                    $item->staff_info_id,
                    $item->name,
                    static::$t->_('sex_' . $item->sex),
                    $workingCountryList[$item->nationality] ?? '',
                    isset($itemData['BIRTHDAY']) ? date('d/M/Y', strtotime($itemData['BIRTHDAY'])) : '',
                    $this->getStaffAddress($itemData),
                    $item->identity,
                    //index 7
                    date('d/M/Y', strtotime($item->hire_date)),
                    $this->showJobTitleName($item->job_title),
                    isset($salaryData[$item->staff_info_id]) ? $salaryData[$item->staff_info_id]['salary_base'] : '0.00',
                    //index 10
                    $item->state == HrStaffInfoModel::STATE_RESIGN ? date('d/M/Y',
                        strtotime($item->leave_date . ' last day')) : '',
                ];
                ++$numberSort;
            }
        }
        $returnData['list'] = $staffInfoData;
    }
    /**
     * 员工登记表中列表上方信息
     * @param $firstData
     * @param $returnData
     */
    private function staffRegisterFormUpData($firstData,&$returnData){
        $returnData['company_name'] = CertificateEnums::$register_form_th[$firstData->company_id] ?? '';
        $returnData['fixed']        = 'ทะเบียนลูกจ้าง ตาม พรบ.คุ้มครองแรงงาน พ.ศ.2541 ';
        if ($firstData->sys_store_id == '-1') {
            $returnData['addr'] = CertificateEnums::$register_form_company_addr[$firstData->company_id] ?? '';
        } else {
            //取值MS-网点管理-详细地址 和 行政区域
            $store              = SysStoreModel::findFirst([
                'columns'    => 'province_code,city_code,district_code,postal_code,detail_address,name',
                'conditions' => 'id=:id:',
                'bind'       => ['id' => $firstData->sys_store_id],
            ]);
            $sys_service        = new SysService();
            $province           = $sys_service->getProvinceDetail($store->province_code);
            $city               = $sys_service->getCityDetail($store->city_code);
            $district           = $sys_service->getDistrictDetail($store->district_code);
            $returnData['addr'] = empty($store) ? '' :$store->name .':'. $store->detail_address .' '.($district['name'] ?? '') . ' ' . ($city['name'] ?? '') . ' ' . ($province['name'] ?? '') . ' ' . $store->postal_code;
        }
        //直线上级信息
        $managerIdFind              = HrStaffInfoModel::findFirst([
            'columns'    => 'manger',
            'conditions' => 'staff_info_id=:staff_info_id:',
            'bind'       => ['staff_info_id' => $firstData->staff_info_id],
        ]);
        $returnData['manager_mess'] = '';
        if ($managerIdFind) {
            $manager = HrStaffInfoModel::findFIrst([
                'columns'    => 'staff_info_id,name,job_title,mobile',
                'conditions' => 'staff_info_id=:staff_info_id:',
                'bind'       => ['staff_info_id' => $managerIdFind->manger],
            ]);
            if ($manager) {
                $returnData['manager_mess'] = $manager->staff_info_id . ' ' . $manager->name . ' ' . $this->showJobTitleName($manager->job_title);
                if ($manager->mobile) {
                    $returnData['manager_mess'] .= '(' . $manager->mobile . ')';
                }
            }
        }
    }
    /**
     * 扣税证明 列表页
     * @param $param
     * @return mixed
     * @throws ValidationException
     */
    public function tax_list($param)
    {
        if (empty($param['year_at'])) {
            throw new ValidationException('need input for which year');
        }
        //对应年的 1月到12月 数据
        $year  = intval($param['year_at']);
        $start = $year.'-01';
        $end   = $year.'-12';

        $builder = $this->modelsManager->createBuilder();
        $column  = 's.staff_info_id,s.company_id,staff.email,staff.personal_email';

        $builder->innerjoin(HrStaffInfoModel::class, 's.staff_info_id = staff.staff_info_id', 'staff');
        $builder->andWhere('staff.is_sub_staff = 0 and staff.formal in (1, 4)');

        if ($year == '2021') {//21年特殊 导入的数据
            $builder->from(['s' => TaxPdfTempModel::class]);
        }else if($year == '2022'){//改需求 从新表取
            $builder->from(['s' => TaxPdf2022Model::class]);
        }else if($year == '2023'){//改需求 从新表取
            $builder->from(['s' => TaxPdf2023Model::class]);
        }else if($year == '2024'){//改需求 从新表取
            $builder->from(['s' => TaxPdf2024Model::class]);
        } else {//从工资表取数据
            $builder->andWhere('s.status = 3');//取paid 状态
            $builder->from(['s' => SalaryGongziModel::class]);
            $builder->betweenWhere('s.excel_month', $start, $end);
            //新增 paid day  要在对应年区间
            $start_date = $start . '-01';
            $end_date = $end . '-31';
            $builder->betweenWhere('s.paid_day', $start_date, $end_date);

        }

        //搜索 指定工号 逗号分隔
        $staff_arr = [];
        if (!empty($param['staff_ids'])) {
            //过滤
            $param['staff_ids'] = trim($param['staff_ids']);
            $param['staff_ids'] = str_replace('，', ',', $param['staff_ids']);
            $param['staff_ids'] = trim(preg_replace("/(\s+)/", ",", $param['staff_ids']));
            //过滤完 还有
            if (!empty($param['staff_ids'])) {
                $staff_arr = explode(',', $param['staff_ids']);
                $staff_arr = array_values(array_filter($staff_arr));
                $builder->andWhere('staff.staff_info_id in ({ids:array})', ['ids' => $staff_arr]);
            }
        }

        //新增 发送状态筛选 去send_state 表 -1 没发送  1 发送成功 2 发送失败
        if(!empty($param['send_state'])){
            //1，2 情况 联表查询
            if(in_array($param['send_state'],array(TaxSendStateModel::SEND_STATE_SUCCESS,TaxSendStateModel::SEND_STATE_FAILED))){

                if($year == '2022' || $year == '2023' || $year == '2024' ){
                    $builder->leftjoin(TaxSendStateModel::class, 's.staff_info_id = tss.staff_info_id', 'tss');
                    $builder->andWhere('tss.year_time = :year: and tss.state = :send_state:',['year' => $year,'send_state' => intval($param['send_state'])]);
                }else{
                    $stateStaff = TaxSendStateModel::find([
                        'columns' => 'staff_info_id',
                        'conditions' => 'year_time = :year: and state = :send_state:',
                        'bind' => ['year' => $year,'send_state' => intval($param['send_state'])]
                    ])->toArray();

                    $stateStaff = empty($stateStaff) ? [] : array_column($stateStaff,'staff_info_id');
                    $builder->InWhere('staff.staff_info_id', $stateStaff);
                }
            }
            //这情况 要 排除查询 就是 这个发送表里没有数据的
            if($param['send_state'] == TaxSendStateModel::SEND_STATE_HAVE_NOT){
                if($year == '2022' || $year == '2023' || $year == '2024' ){
                    $builder->leftjoin(TaxSendStateModel::class, 's.staff_info_id = tss.staff_info_id', 'tss');
                    $builder->andWhere('tss.state is null');
                    //$builder->andWhere('NOT EXISTS (select 1 from App\Models\backyard\TaxSendStateModel tss where s.staff_info_id = tss.staff_info_id)');
                }else{
                    $ignoreStaff = TaxSendStateModel::find([
                        'columns' => 'staff_info_id',
                        'conditions' => 'year_time = :year:',
                        'bind' => ['year' => $year]
                    ])->toArray();

                    $ignoreStaff = empty($ignoreStaff) ? [] : array_column($ignoreStaff,'staff_info_id');
                    if(!empty($ignoreStaff)){
                        $builder->notInWhere('staff.staff_info_id', $ignoreStaff);
                    }
                }


            }
        }

        //公司筛选
        if (!empty($param['company_id'])) {
            $builder->andWhere('s.company_id = :company_id:', ['company_id' => intval($param['company_id'])]);
        }

        //员工状态筛选
        if (!empty($param['state']) && $param['state'] == Enums::HRIS_WORKING_STATE_1) {
            $builder->andWhere('staff.state = :state:', ['state' => intval($param['state'])]);
        }
        //离职日期筛选
        if (!empty($param['state']) && $param['state'] == Enums::HRIS_WORKING_STATE_2 && (!empty($param['leave_start']) && !empty($param['leave_end']))) {
            $builder->andWhere('staff.state = 2');
            $builder->betweenWhere('staff.leave_date', $param['leave_start'], $param['leave_end']);
        }

        //停职日期筛选
        if (!empty($param['state']) && $param['state'] == Enums::HRIS_WORKING_STATE_3 && (!empty($param['stop_start']) && !empty($param['stop_end']))) {
            $builder->andWhere('staff.state = 3');
            $builder->betweenWhere('staff.stop_duties_date', $param['stop_start'], $param['stop_end']);
        }

        $builder->columns($column);
        if($year > '2022'){
            $builder->groupBy("s.staff_info_id,s.company_id");
        }
        //操作批量下载或者导出 要返回data
        if(!empty($param['is_send']) || !empty($param['is_download'])){
            $builder->columns('s.staff_info_id');
            return $builder->getQuery()->execute()->toArray();
        }
        if($year > '2022'){
            $page_count = $this->getBuilderTotal($builder,true);
        }else{
            $page_count = $this->getBuilderTotal($builder);
        }

        $page       = empty($param['page']) ? 1 : intval($param['page']);
        $size       = empty($param['size']) ? 50 : intval($param['size']);
        $offset     = $size * ($page - 1);
        $builder->limit($size, $offset);
        //如果有工号搜索 展示的顺序要和输入的工号保持一样顺序
        if (!empty($param['staff_ids'])) {
            $staff_str = implode(',', $staff_arr);
            $builder->orderBy(" field(s.staff_info_id,{$staff_str})");
        }
        $builder->columns($column);
        $data = $builder->getQuery()->execute()->toArray();
        if(empty($data)){
            $return['list']  = [];
            $return['count'] = 0;
            return $return;
        }

        //发送状态
        $stateList = TaxSendStateModel::$tax_send_state;

        //发送状态数据获取 没筛选的情况下 啥都有
        if(empty($param['send_state'])){
            $conditions = 'year_time = :year:';
            $bind = ['year' => $year];
            if(!empty($param['staff_ids'])){
                $conditions .= ' and staff_info_id in ({ids:array})';
                $bind['ids'] = array_values($staff_arr);
            }

            $sendData = TaxSendStateModel::find([
                'conditions' => $conditions,
                'bind' => $bind,
            ])->toArray();
            $sendData = empty($sendData) ? [] : array_column($sendData,'state','staff_info_id');

        }else{
            //筛选的情况 不用查 直接塞进去
            $sendStateParam = intval($param['send_state']);
            $sendTextKey = $stateList[$sendStateParam];
        }

        //新增字段整理  年份 公司 发送状态
        foreach ($data as &$da){
            //公司名称
            $da['company_text'] = empty($da['company_id']) ? '' : Enums\SalaryEnums::COMPANY_TYPE_NAME_MAP['default'][$da['company_id']];
            //发送状态
            $sendState = $sendData[$da['staff_info_id']] ?? 0;
            $t_key = $sendTextKey ?? $stateList[$sendState] ?? 'send_state_yet';//默认 没发送
            $da['send_state_text'] = self::$t->_($t_key);
        }

        $return['list']  = $data;
        $return['count'] = intval($page_count);
        return $return;
    }


    //刷新接口用 如果未完成 返回计数 完成 返回 下载连接
    public function refresh_tax($param)
    {
        if (empty($param['id'])) {
            throw new ValidationException('need id input');
        }
        if ($param['id'] >= 528 && $param['id'] <= 542) {
            $param['id'] = 529;
        }

        $id                = intval($param['id']);
        $redis             = $this->getDI()->get("redis");
        $return['failed']  = (int)$redis->get(self::$tax_redis_key."_{$id}_failed");
        $return['success'] = (int)$redis->get(self::$tax_redis_key."_{$id}_success");
        $return['in_all']  = (int)$redis->get(self::$tax_redis_key."_{$id}");

        return $return;
    }


    /**
     * 页面操作发送 存入log表 等任务消费发送 新增 批量下载入口 也用这个方法
     * @param $param
     * @return mixed
     * @throws ValidationException
     */
    public function save_send_log($param)
    {
        $data = $this->tax_list($param);
        if (empty($data)) {
            throw new ValidationException('nothing to send out');
        }

        //is_send 页面发送操作 is_download 页面操作 批量下载
        $redis_key      = $param['is_send'] ? self::$tax_redis_key : self::$download_redis_key;
        $action_name    = $param['is_send'] ? 'Certificate'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'excel_log' : 'Certificate'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'zip_log';
        $tack_type      = $param['is_send'] ? HcmExcelTackModel::TYPE_TAX : HcmExcelTackModel::TYPE_TAX_DOWNLOAD;
        $tack_file_name = $param['is_send'] ? date('YmdHis').'-tax_send_log_'.$param['user_info']['id'].'.xlsx' : date('YmdHis').'-tax_download_log_'.$param['user_info']['id'].'.zip';

        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        $main_insert['operator'] = $param['user_info']['id'];
        $main_insert['year_at']  = $param['year_at'];

        $model = new SendTaxModel();
        $flag  = $model->create($main_insert);

        if (!$flag) {
            $db->rollback();
            throw new ValidationException("save_send_log failed");
        }

        $id        = $model->id;
        $log_model = new SendTaxLogModel();
        $staff_ids = array_unique(array_column($data, 'staff_info_id'));

        $count = count($staff_ids);
        $redis = $this->getDI()->get("redis");

        $staff_ids = array_chunk($staff_ids, 5000);
        foreach ($staff_ids as $item){
            $insert = [];
            foreach ($item as $staffId){
                $row['origin_id']     = $id;
                $row['staff_info_id'] = $staffId;
                $insert[] = $row;
                $redis->lpush($redis_key, "{$staffId}_{$param['year_at']}_{$id}");
            }
            $log_model->batch_insert($insert);
        }

        //下载列表 记录
        $param_str = base64_encode(json_encode($param));
        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: 
            and  type = :type: and status = 0  
            and action_name = :action_name: 
            and is_delete = 0
            and args_json = :param_str:',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $main_insert['operator'],
                'type'        => $tack_type,
                'param_str'   => $param_str,
            ],
        ]);

        if ($excelTask) {
            $db->rollback();
            throw new ValidationException('download task is running,waiting for a while please');
        }
        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir.'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $tack_file_name;
        $excelTask->type            = $tack_type;
        $excelTask->staff_info_id   = $main_insert['operator'];
        $excelTask->args_json       = base64_encode(json_encode(['id' => $id]));
        $excelTask->created_at      = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);

        $flag = $excelTask->save();

        if (!$flag) {
            $db->rollback();
            throw new ValidationException("save_send_log failed");
        }

        $db->commit();
        //redis 计数 总数/成功/失败
        $redis->set($redis_key."_{$id}", $count, 18 * 3600);//总数
        $redis->set($redis_key."_{$id}_success", 0, 18 * 3600);//成功
        $redis->set($redis_key."_{$id}_failed", 0, 18 * 3600);//失败
        return $id;
    }


    //扣税证明 发送邮件内容 和pdf 附件 任务消费用
    public function send_tax_pdf($staff_id, $year, $origin_id)
    {
        //取出 数据库里面没有发送的记录 和对应的id
        $redis    = $this->getDI()->get("redis");
        $log_info = SendTaxLogModel::findFirst("origin_id = {$origin_id} and staff_info_id = {$staff_id}");
        if (empty($log_info)) {
            return true;
        }

        $log_info->is_send = 1;
        $log_info->update();
        $start = $year.'-01';
        $end   = $year.'-12';
        $temp_flag   = false;
        if ($year == '2021') {//22年 临时方案 明年不用查这个库
            $salary_list = TaxPdfTempModel::find("staff_info_id = {$staff_id}")->toArray();
            if (!empty($salary_list)) {
                $salary_list[0]['excel_month'] = '2021-01';//临时表里 没有工资表的这个字段 拼一个进去 pdf模板渲染用
                $temp_flag                     = true;
            }
        }else if ($year == '2022') {//23年 临时方案
            $salary_list = TaxPdf2022Model::find("staff_info_id = {$staff_id} and excel_month = '2022-12'")->toArray();
        }else if ($year == '2023') {//23年 临时方案
            $salary_list = TaxPdf2023Model::find("staff_info_id = {$staff_id} and excel_month = '2023-12'")->toArray();
        }else if ($year == '2024') {//24年 临时方案
            $salary_list = TaxPdf2024Model::find("staff_info_id = {$staff_id} and excel_month = '2024-12'")->toArray();
        }else {
            //新增 paid day  要在对应年区间
            $start_date = $start . '-01';
            $end_date = $end . '-31';
            $salary_list = SalaryGongziModel::find([
                'conditions' => 'staff_info_id = :staff_id: and excel_month between :start: and :end: and paid_day between :start_date: and :end_date: and status = 3',
                'bind' => [
                    'staff_id' => $staff_id,
                    'start' => $start,
                    'end' => $end,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                ]
            ])->toArray();
        }

        if (empty($salary_list)) {
            $this->logger->info('send_tax_pdf empty '.$staff_id.'_'.$year);
            $log_info->reason = 'not salary info';
            $log_info->update();
            $redis->incr(self::$tax_redis_key."_{$origin_id}_failed");

            //新增发送状态记录
            $this->saveSendState($staff_id,$year,false);
            return true;
        }

        //看数据库有没有
        $oss_server = new OssService();
        $url        = [];
        $date       = date('Y-m-d');
        //整理 数据 获取对应 坐标 后期做成配置页面
        $staff_info = HrStaffInfoModel::findFirst("staff_info_id = {$staff_id}");
        if (empty($staff_info)) {
            $this->logger->info('staff info empty '.$staff_id.'_'.$year);
            $log_info->reason = 'not staff info';
            $log_info->update();
            $redis->incr(self::$tax_redis_key."_{$origin_id}_failed");
            //新增发送状态记录
            $this->saveSendState($staff_id,$year,false);
            return true;
        }
        $staff_info = $staff_info->toArray();
        $pdf_data   = $this->format_tax_pdf_data($salary_list, $temp_flag, $staff_info);
        //获取签名图 地址
        $signModel = new TaxSettingService();
        $signUri = $signModel->getImgUri($year);

        $companyMap = array_column(StaffPayrollCompanyInfoRepository::allData(), null,'id');
        $companyMap = array_map(function($item){
            $company_seal_url = !empty($item['company_seal_url']) ? json_decode($item['company_seal_url'], true) : [];
            $item['company_seal_url'] = !empty($company_seal_url) ? $company_seal_url[0]['object_url'] : '';
            return $item;
        },$companyMap);
        //生成 pdf 连接 有可能是多个
        foreach ($pdf_data as $company_id => $da) {
            $oss_insert['staff_info_id'] = $staff_id;
            $oss_insert['file_type']     = Enums::OSS_FILE_TYPE_11;
            $oss_insert['lang']          = 1;//1 泰语 2 中文 3 英文
            $oss_insert['date_at']       = $date;
            $oss_insert['month']         = $start;
            $oss_insert['origin_id']     = $origin_id;
            $oss_insert['oss_year']      = $year;

            $fill_data = $this->get_tax_coordination($da);
            //盖章图片
            if (!empty($companyMap[$company_id]['company_seal_url'])) {
                $fill_data['image'] = [
                    'path' => $companyMap[$company_id]['company_seal_url'],
                    'x'    => 740,
                    'y'    => 1280,

                ];
            }
            //签名图 坐标
            $fill_data['image_sign'] = [
                'path' => $signUri,
                'x'    => 580,
                'y'    => 1250,

            ];

            $url[] = $oss_server->imagick_pdf('tax_pdf.pdf', $fill_data, OssService::PDF_SPACE_CERTIFICATE, $oss_insert,
                $staff_id);
        }

        $this->getDI()->get('logger')->info($staff_id." send tax pdf url ".json_encode($url, JSON_UNESCAPED_UNICODE));

        //发邮件
        $thai_year    = $year + 543;
        $title        = "หนังสือรับรองหัก ณ ที่จ่าย(50ทวิ) ประจำปี{$thai_year}/{$year}年预扣税证明";
        $content      = "หนังสือรับรองหัก ณ ที่จ่าย(50 ทวิ) ประจำปี{$thai_year} ส่งเรียบร้อยแล้วกรุณาตรวจสอบ. {$year}年预扣税证明已发送，请查收。";
        $mail_contend = '';
        if (!empty($url)) {
            foreach ($url as $u) {
                $mail_contend .= "<p><a href='{$u}'>{$staff_id}_certificate</a></p>";
            }
        }
        $mail_contend = $content.$mail_contend;


        //发消息
        $staff_info_id               = $staff_id;
        $id                          = time().$staff_info_id.rand(1000000, 9999999);
        $param['staff_users']        = [$staff_info_id];//数组 多个员工id
        $param['message_title']      = $title;
        $param['message_content']    = "<p style='font-size: 45px'>หนังสือรับรองหัก ณ ที่จ่าย (50 ทวิ) ประจำปี {$thai_year} ส่งไปที่อีเมลบริษัท/อีเมลส่วนตัว เรียบร้อยแล้วกรุณาตรวจสอบ.</br>{$year}年预扣税证明已发送到企业邮箱/个人邮箱，请查收。</p>";
        $param['staff_info_ids_str'] = $staff_info_id;
        $param['id']                 = $id;
        $param['category']           = -1;

        $this->getDI()->get('logger')->write_log('send tax message '.json_encode($param), 'info');
        $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', self::$language));
        $bi_rpc->setParams([$param]);
        $bi_rpc->execute();
        //发邮件 容易抛异常 在这捕获 然后改成已发送 并且发送失败记录
        $flag_1 = $flag_2 = false;
        $log_info->reason = '';
        //先发个人邮箱
        try {
            if (!empty($staff_info['personal_email'])) {
                $flag_2 = BiMail::send_salary($staff_info['personal_email'], $title, $mail_contend);
            }
            $this->getDI()->get('logger')->info("send tax pdf {$staff_id} {$year} 发送邮件 个人 {$flag_2}");
        } catch (\Exception $e) {
            //回写 发送失败 日志
            $log_info->reason .= 'personal email in the blacklist';
            $this->getDI()->get('logger')->write_log('send tax '.$staff_id.' mail failed '.$e->getMessage(), 'info');
        }

        //非离职员工 发企业邮箱
        try {
            if (!empty($staff_info['email']) && $staff_info['state'] != 2) {
                $flag_1 = BiMail::send_salary($staff_info['email'], $title, $mail_contend);
            }
            $this->getDI()->get('logger')->info("send tax pdf {$staff_id} {$year} 发送邮件 企业 {$flag_1} ");
        } catch (\Exception $e) {
            //回写 发送失败 日志
            $log_info->reason .= 'company email in the blacklist';
            $this->getDI()->get('logger')->write_log('send tax '.$staff_id.' mail failed '.$e->getMessage(), 'info');
        }

        if ($flag_1 || $flag_2)//发送成功
        {
            $redis->incr(self::$tax_redis_key."_{$origin_id}_success");
            //新增发送状态记录
            $this->saveSendState($staff_id, $year, true);
            $this->logger->info('send_tax success '.$staff_id.'_'.$year);
        } else {//发送失败
            $redis->incr(self::$tax_redis_key."_{$origin_id}_failed");
            //回写 发送失败 日志
            $log_info->reason = 'not found email';
            $this->saveSendState($staff_id, $year, false);
        }

        $log_info->update();
        return true;
    }


    //pdf 数据 前端预览用
    public function get_salary_data($param)
    {
        if (empty($param['staff_info_id']) || empty($param['year_at'])) {
            throw new \Exception('param error');
        }

        $staff_id = intval($param['staff_info_id']);
        $year     = $param['year_at'];

        $start = $year.'-01';
        $end   = $year.'-12';

        $temp_flag = false;
        if ($year == '2021') {//22年 临时方案 明年不用查这个库
            $salary_list = TaxPdfTempModel::find("staff_info_id = {$staff_id}")->toArray();
            if (!empty($salary_list)) {
                $salary_list[0]['excel_month'] = '2021-01';
                $temp_flag                     = true;
            }
        } elseif ($year == '2022') {//23年 临时方案 明年不用查这个库
            $salary_list = TaxPdf2022Model::find("staff_info_id = {$staff_id}")->toArray();
        } elseif ($year == '2023') {//23年
            $salary_list = TaxPdf2023Model::find("staff_info_id = {$staff_id}")->toArray();
        } elseif ($year == '2024') {//24年
            $salary_list = TaxPdf2024Model::find("staff_info_id = {$staff_id}")->toArray();
        } else {
            //新增 paid day  要在对应年区间
            $start_date = $start . '-01';
            $end_date = $end . '-31';
            $salary_list = SalaryGongziModel::find([
                'conditions' => 'staff_info_id = :staff_id: and excel_month between :start: and :end: and paid_day between :start_date: and :end_date: and status = 3',
                'bind' => [
                    'staff_id' => $staff_id,
                    'start' => $start,
                    'end' => $end,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                ]
            ])->toArray();
        }

        if (empty($salary_list)) {
            throw new \Exception('no salary info');
        }

        //整理 数据 获取对应 坐标 后期做成配置页面
        $pdf_data = $this->format_tax_pdf_data($salary_list, $temp_flag);

        if (empty($pdf_data)) {
            throw new \Exception('no pdf info');
        }
        $signModel = new TaxSettingService();
        $companyMap = array_column(StaffPayrollCompanyInfoRepository::allData(), null,'id');
        $companyMap = array_map(function($item){
            $company_seal_url = !empty($item['company_seal_url']) ? json_decode($item['company_seal_url'], true) : [];
            $item['company_seal_url'] = !empty($company_seal_url) ? $company_seal_url[0]['object_url'] : '';
            return $item;
        },$companyMap);

        foreach ($pdf_data as $company_id => &$da) {
            $da['company_img'] = '';
            if (!empty($companyMap[$company_id]['company_seal_url'])) {
                $da['company_img'] = base64_encode(file_get_contents($companyMap[$company_id]['company_seal_url']));
            }

            //从配置 获取签名图片
            $signUri = $signModel->getImgUri($year);
            $sign_data      = file_get_contents($signUri);
            $da['sign_url'] = base64_encode($sign_data);
        }
        return array_values($pdf_data);
    }

    //页面操作 邮箱发送 可手动输入邮箱
    public function sendByMail($param)
    {
        $staff_id           = intval($param['staff_info_id']);
        $year               = $param['year_at'];

        //获取pdf 地址
        $url = $this->task_tool($staff_id, $year,1);

        //验证邮箱是否重复
        if(!empty($param['e_mail']) && ($this->staff_info['email'] == $param['e_mail'] || $this->staff_info['personal_email'] == $param['e_mail'])){
            throw new ValidationException(self::$t->_('same_mail_notice'));
        }

        if(empty($param['e_mail']) && empty($this->staff_info['email']) && empty($this->staff_info['personal_email'])){
            throw new ValidationException(self::$t->_('empty_mail_notice'));
        }

        try{
            //发邮件 标题和内容
            $thai_year    = $year + 543;
            $title        = "หนังสือรับรองหัก ณ ที่จ่าย(50ทวิ) ประจำปี{$thai_year}/{$year}年预扣税证明";
            $content      = "หนังสือรับรองหัก ณ ที่จ่าย(50 ทวิ) ประจำปี{$thai_year} ส่งเรียบร้อยแล้วกรุณาตรวจสอบ. {$year}年预扣税证明已发送，请查收。";
            $mail_contend = '';
            foreach ($url as $u) {
                $mail_contend .= "<p><a href='{$u}'>{$staff_id}_certificate</a></p>";
            }
            $mail_contend = $content.$mail_contend;
            //操作发送 该员工的 个人邮箱 企业邮箱 和参数输入邮箱
            $mailList = [];

            //个人邮箱
            if (!empty($this->staff_info['personal_email'])) {
                $mailList[] = $this->staff_info['personal_email'];

            }
            //企业邮箱 离职的不发
            if (!empty($this->staff_info['email']) && $this->staff_info['state'] != 2) {
                $mailList[] = $this->staff_info['email'];
            }

            //页面手动输入了邮箱
            if(!empty($param['e_mail'])){
                $mailList[] = $param['e_mail'];
            }

            $flag = BiMail::send_salary($mailList, $title, $mail_contend);

            //发消息
            $id                          = time().$staff_id.rand(1000000, 9999999);
            $param['staff_users']        = [$staff_id];//数组 多个员工id
            $param['message_title']      = $title;
            $param['message_content']    = "<p style='font-size: 45px'>หนังสือรับรองหัก ณ ที่จ่าย (50 ทวิ) ประจำปี {$thai_year} ส่งไปที่อีเมลบริษัท/อีเมลส่วนตัว เรียบร้อยแล้วกรุณาตรวจสอบ.</br>{$year}年预扣税证明已发送到企业邮箱/个人邮箱，请查收。</p>";
            $param['staff_info_ids_str'] = $staff_id;
            $param['id']                 = $id;
            $param['category']           = -1;

            $this->getDI()->get('logger')->write_log('send single tax message '.json_encode($param), 'info');
            $bi_rpc = (new ApiClient('bi_rpc', '', 'add_kit_message', self::$language));
            $bi_rpc->setParams([$param]);
            $bi_rpc->execute();


            //发送成功后 写入对应员工的发送状态
            $this->saveSendState($staff_id,$year,$flag);

            $this->getDI()->get('logger')->info("页面操作发送至邮箱 {$staff_id} {$flag}");

            return $flag;
        }catch (\Exception $e){
            //记录 发送失败 日志 保存 发送状态表
            $this->getDI()->get('logger')->error("页面操作发送至邮箱 {$staff_id} 发送失败 ".$e->getMessage());
            $this->saveSendState($staff_id,$year,false);
            return false;
        }
    }


    /**
     * //整理pdf 数据
     * @param $data
     * @param bool $temp_flag 21年 导入数据的原因 员工地址的数据源不同
     * @param array $staff_info
     * @return array
     */
    protected function format_tax_pdf_data($data, $temp_flag = false, $staff_info = [])
    {
        $companyMap = array_column(StaffPayrollCompanyInfoRepository::allData(), null,'id');
        //返回 公司维度 的多个 pdf数据
        $company_data = [];
        foreach ($data as $da) {
            if (empty($da['company_id'])) {
                continue;
            }

            if ($temp_flag) {
                $company_data[$da['company_id']]['address'] = $da['address'];
            }
            //计算支付金额
            if (empty($company_data[$da['company_id']]['tax_base'])) {
                $company_data[$da['company_id']]['tax_base'] = $da['tax_base'];
            } else {
                $company_data[$da['company_id']]['tax_base'] += $da['tax_base'];
            }

            //计算税额
            if (empty($company_data[$da['company_id']]['tax'])) {
                $company_data[$da['company_id']]['tax'] = $da['tax'];
            } else {
                $company_data[$da['company_id']]['tax'] += $da['tax'];
            }

            //计算社保金额
            if (empty($company_data[$da['company_id']]['social'])) {
                $company_data[$da['company_id']]['social'] = $da['social'];
            } else {
                $company_data[$da['company_id']]['social'] += $da['social'];
            }

            //新增了两个字段 21年 导入的数据没有
            if ($temp_flag) {
                $company_data[$da['company_id']]['compensation']
                    = $company_data[$da['company_id']]['tax_compensation']
                    = $company_data[$da['company_id']]['retirement_over5y']
                    = $company_data[$da['company_id']]['tax_retirement_over5y']
                    = 0;
                continue;
            }

            //赔偿金退休金、工作5年以上退休金
            if (empty($company_data[$da['company_id']]['compensation'])) {
                $company_data[$da['company_id']]['compensation'] = $da['compensation'];
            } else {
                $company_data[$da['company_id']]['compensation'] += $da['compensation'];
            }
            if (empty($company_data[$da['company_id']]['tax_compensation'])) {
                $company_data[$da['company_id']]['tax_compensation'] = $da['tax_compensation'];
            } else {
                $company_data[$da['company_id']]['tax_compensation'] += $da['tax_compensation'];
            }
            if (empty($company_data[$da['company_id']]['retirement_over5y'])) {
                $company_data[$da['company_id']]['retirement_over5y'] = $da['retirement_over5y'];
            } else {
                $company_data[$da['company_id']]['retirement_over5y'] += $da['retirement_over5y'];
            }
            if (empty($company_data[$da['company_id']]['tax_retirement_over5y'])) {
                $company_data[$da['company_id']]['tax_retirement_over5y'] = $da['tax_retirement_over5y'];
            } else {
                $company_data[$da['company_id']]['tax_retirement_over5y'] += $da['tax_retirement_over5y'];
            }
        }

        //员工信息name 身份证号 identity  国籍判断 税号 住址取户口所在地址
        $staff_id = intval($data[0]['staff_info_id']);

        $year = date('Y', strtotime($data[0]['excel_month']));
        //发送日期
        $send_year = date('Y');
        $day       = date('d');
        $month     = intval(date('m'));
        //TODO 查询用户信息 第1次
        if (empty($staff_info)) {
            $staff_info = HrStaffInfoModel::findFirst("staff_info_id = {$staff_id}");
            if (empty($staff_info)) {
                return false;
            }
            $staff_info = $this->staff_info = $staff_info->toArray();
        }

        //根据国籍 NATIONALITY 不同 显示 身份证 或者 税号
        $item_arr = [
            'REGISTER_PROVINCE', //户口所在省
            'REGISTER_CITY', //户口所在市
            'REGISTER_DISTRICT', //户口所在乡
            'REGISTER_POSTCODES', //户口所在邮编
            'REGISTER_HOUSE_NUM', //户口所在门牌号
            'REGISTER_VILLAGE_NUM', //户口所在村号
            'REGISTER_VILLAGE', //户口所在村
            'REGISTER_ALLEY', //户口所在巷
            'REGISTER_STREET', //户口所在街道
            'NATIONALITY',//国籍 1是泰国
            'TAX_NO',//税号 目前没有 新增一个 历史数据是导入过来的

        ];

        $item_info = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id = :staff_id: and item in({item_arr:array})',
            'bind'       => ['staff_id' => $staff_id, 'item_arr' => $item_arr],
        ])->toArray();


        //拼接 户口所在地
        $staff_address = '';
        $tax_no        = '';
        if (!empty($item_info)) {
            $item_info = array_column($item_info, 'value', 'item');

            $staff_address = $this->getStaffAddress($item_info);
            //判断国籍 如果 泰国 显示 身份证  不是泰国 显示税号（税号是导入进来的）
            if (!empty($item_info['NATIONALITY']) && $item_info['NATIONALITY'] == 1) {
                //泰国 显示身份证 税号展示空
                $tax_no = '';
            } else {//非泰国 身份账号是空 税号展示
                if (!empty($item_info['TAX_NO'])) {
                    $tax_no = (string)$item_info['TAX_NO'];
                }
                $staff_info['identity'] = '';
            }
        }

        $th_month        = Enums::$thai_month;

        foreach ($company_data as $c_id => &$c) {
            $c['c_id_no']       = empty($companyMap[$c_id]['company_registration_no']) ? '' : $companyMap[$c_id]['company_registration_no'];
            $c['c_id_name']     = empty($companyMap[$c_id]['company_name_local']) ? '' : $companyMap[$c_id]['company_name_local'];
            $c['c_id_address']  = empty($companyMap[$c_id]['company_address_local']) ? '' : $companyMap[$c_id]['company_address_local'];
            $c['staff_id_no']   = $temp_flag ? $data[0]['identity'] : $staff_info['identity'];
            $c['tax_no']        = $tax_no;
            $c['staff_name']    = $staff_info['name'];
            $c['staff_address'] = $temp_flag ? $c['address'] : $staff_address;//21年 地址用导入的数据 其他是用item表 拼的
            $c['staff_info_id'] = $staff_id;
            $c['year_at']       = intval($year) + 543;
            $c['send_year_at']  = intval($send_year) + 543;

            //千分位
            $c['show_tax_base'] = number_format($c['tax_base'], 2);

            $c['show_tax']    = number_format($c['tax'], 2);
            $c['show_social'] = number_format($c['social'], 2);

            $c['day']        = $day;
            $c['thai_month'] = $th_month[$month];


            //新增字段 赔偿金
            $c['show_compensation_text'] = 'เงินชดเชยตามกฎหมายแรงงาน';
            $c['show_compensation']     = number_format($c['compensation'], 2);
            $c['show_compensation_tax'] = number_format($c['tax_compensation'], 2);

            //新增字段 5年 退休金
            $c['show_retirement_text'] = 'เงินชดเชยตามกฎหมายแรงงาน';
            $c['show_retirement']     = number_format($c['retirement_over5y'], 2);
            $c['show_retirement_tax'] = number_format($c['tax_retirement_over5y'], 2);

            //新增的字段 加一起 两个金额 只能有一个 或者都没有 计算总计用
            $add_amount = bcadd($c['compensation'], $c['retirement_over5y'], 2);
            $add_tax    = bcadd($c['tax_compensation'], $c['tax_retirement_over5y'], 2);

            //总额
            $c['show_in_all']     = number_format(bcadd($c['tax_base'], $add_amount, 2), 2);
            $c['show_in_all_tax'] = number_format(bcadd($c['tax'], $add_tax, 2), 2);

            //税额 泰文 金额描述
            $c['tax_text']   = baht_text(bcadd($c['tax'], $add_tax, 2));

        }

        return $company_data;
    }


    /**
     * 23年 新需求 员工地址 曼谷和其他地方不同 要特殊处理 https://flashexpress.feishu.cn/docx/LDAVdNZKroZ34fxr7CucXpihnpb
     *
    1. 如果 省 为曼谷  “กรุงเทพ”时，地址格式为：บ้านเลขที่{门牌号} หมู่บ้าน{村庄} หมู่{村号} ซอย{巷} ถนน{街道} แขวง{乡} เขต{市} {省} {邮编}
     *
    2. 如果 省不为曼谷 “กรุงเทพ”时，地址格式为：บ้านเลขที่{门牌号} หมู่บ้าน{村庄} หมู่{村号} ซอย{巷} ถนน{街道} ตำบล{乡} อำเภอ{市} จังหวัด{省} {邮编}
    3. 以上每个地址之间使用空格间隔
    4. 如果有某个地址为空，则此项的 泰语单位 和 后方空格 也无需显示
    5. 如果所有地址均为空，则使用默认地址显示：อาคารยูนิลีเวอร์เฮ้าส์ ชั้นที่ 7และ8 เลขที่ 161 ถนนพระรามเก้า แขวงห้วยขวาง เขตห้วยขวาง กรุงเทพมหานคร 10310
     * @param array $item_info 信息组
     * @param string $default_address 默认地址
     * @return string
     */
    public function getStaffAddress($item_info, $default_address = 'อาคารยูนิลีเวอร์เฮ้าส์ ชั้นที่ 7และ8 เลขที่ 161 ถนนพระรามเก้า แขวงห้วยขวาง เขตห้วยขวาง กรุงเทพมหานคร 10310'): string
    {
        $isBKK = false;
        //是曼谷
        if (!empty($item_info['REGISTER_PROVINCE']) && strtoupper($item_info['REGISTER_PROVINCE']) == 'TH01') {
            $isBKK = true;
        }

        //获取 省市区数据
        $sysService = new SysService();
        $provinces  = $sysService->getProvinceListFromCache("code,name");
        $provinces  = empty($provinces) ? [] : array_column($provinces, 'name', 'code');
        $cities     = $sysService->getCityListFromCache("code,name");
        $cities     = empty($cities) ? [] : array_column($cities, 'name', 'code');
        $districts  = $sysService->getDistrictListFromCache("code,name");
        $districts  = empty($districts) ? [] : array_column($districts, 'name', 'code');
        //员工的信息
        $province = empty($item_info['REGISTER_PROVINCE']) ? '' : empty($provinces[$item_info['REGISTER_PROVINCE']]) ? $item_info['REGISTER_PROVINCE'] : $provinces[$item_info['REGISTER_PROVINCE']];
        $city     = empty($item_info['REGISTER_CITY']) ? '' : empty($cities[$item_info['REGISTER_CITY']]) ? $item_info['REGISTER_CITY'] : $cities[$item_info['REGISTER_CITY']];
        $district = empty($item_info['REGISTER_DISTRICT']) ? '' : empty($districts[$item_info['REGISTER_DISTRICT']]) ? $item_info['REGISTER_DISTRICT'] : $districts[$item_info['REGISTER_DISTRICT']];

        $address = '';
        $address .= empty($item_info['REGISTER_HOUSE_NUM']) ? '' : "บ้านเลขที่{$item_info['REGISTER_HOUSE_NUM']} ";//门牌号
        $address .= empty($item_info['REGISTER_VILLAGE']) ? '' : "หมู่บ้าน{$item_info['REGISTER_VILLAGE']} ";//村庄
        $address .= empty($item_info['REGISTER_VILLAGE_NUM']) ? '' : "หมู่{$item_info['REGISTER_VILLAGE_NUM']} ";//村号
        $address .= empty($item_info['REGISTER_ALLEY']) ? '' : "ซอย{$item_info['REGISTER_ALLEY']} ";//巷
        $address .= empty($item_info['REGISTER_STREET']) ? '' : "ถนน{$item_info['REGISTER_STREET']} ";//街道
        //从乡开始 不一样了
        if ($isBKK) {
            $address .= empty($district) ? '' : "แขวง{$district} ";//乡
            $address .= empty($city) ? '' : "เขต{$city} ";//市
            $address .= empty($province) ? '' : "กรุงเทพมหานคร ";//省 如果是曼谷 固定写死 全称
        } else {
            $address .= empty($district) ? '' : "ตำบล{$district} ";//乡
            $address .= empty($city) ? '' : "อำเภอ{$city} ";//市
            $address .= empty($province) ? '' : "จังหวัด{$province} ";//省
        }
        //拼接 邮编
        $address .= empty($item_info['REGISTER_POSTCODES']) ? '' : "{$item_info['REGISTER_POSTCODES']} ";//邮编
        //如果 地址为空 用默认的
        if (empty($address)) {
            $address = $default_address;
        }
        return $address;
    }




    //获取 扣税证明 对应变量坐标配置
    protected function get_tax_coordination($fill_data)
    {
        //需要计算起始位置的成员 x轴
        $num_px          = 8;
        //逗号分隔符的 宽度是 4 对应的小数点要落到竖虚线上 需要计算下
        $_px = 4;

        $show_tax_base_x = $this->getXY($fill_data['show_tax_base'],$num_px,$_px);
        $show_tax_x = $this->getXY($fill_data['show_tax'],$num_px,$_px);

        //可能为空 两个金额只有一个有 或者都没有
        $show_add_x = $show_add_x_tax = 0;
        $show_add_amount = $show_add_amount_tax = '';
        if($fill_data['show_compensation'] != '0.00'){
            //这个字段的描述
            $add_text = 'เงินชดเชยตามกฎหมายแรงงาน';
            $show_add_amount = $fill_data['show_compensation'];
            $show_add_amount_tax = $fill_data['show_compensation_tax'];
            $show_add_x = $this->getXY($fill_data['show_compensation'],$num_px,$_px);
            $show_add_x_tax = $this->getXY($fill_data['show_compensation_tax'],$num_px,$_px);
        }
        if($fill_data['show_retirement'] != '0.00'){
            $add_text = 'เงินชดเชยตามกฎหมายแรงงาน';
            $show_add_amount = $fill_data['show_retirement'];
            $show_add_amount_tax = $fill_data['show_retirement_tax'];
            $show_add_x = $this->getXY($fill_data['show_retirement'],$num_px,$_px);
            $show_add_x_tax = $this->getXY($fill_data['show_retirement_tax'],$num_px,$_px);
        }

        //总计
        $show_in_all_x = $this->getXY($fill_data['show_in_all'],$num_px,$_px);
        $show_in_all_tax_x = $this->getXY($fill_data['show_in_all_tax'],$num_px,$_px);


        //注释上面的是对应项 格式化给换行了
        $data = [
            'page_1' => [
                ['x' => 632, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][0] ?? ''],
                //纳税人身份证号1
                ['x' => 660, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][1] ?? ''],
                //纳税人身份证号2
                ['x' => 681, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][2] ?? ''],
                //纳税人身份证号3
                ['x' => 702, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][3] ?? ''],
                //纳税人身份证号4
                ['x' => 723, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][4] ?? ''],
                //纳税人身份证号5
                ['x' => 751, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][5] ?? ''],
                //纳税人身份证号6
                ['x' => 772, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][6] ?? ''],
                //纳税人身份证号7
                ['x' => 793, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][7] ?? ''],
                //纳税人身份证号8
                ['x' => 814, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][8] ?? ''],
                //纳税人身份证号9
                ['x' => 835, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][9] ?? ''],
                //纳税人身份证号10
                ['x' => 860, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][10] ?? ''],
                //纳税人身份证号11
                ['x' => 881, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][11] ?? ''],
                //纳税人身份证号12
                ['x' => 910, 'y' => 158, 'fz' => 22, 'text' => $fill_data['c_id_no'][12] ?? ''],
                //纳税人身份证号13

                ['x' => 90, 'y' => 183, 'fz' => 22, 'text' => $fill_data['c_id_name'] ?? ''],
                //纳税人姓名
                ['x' => 110, 'y' => 225, 'fz' => 22, 'text' => $fill_data['c_id_address'] ?? ''],
                //纳税人地址

                ['x' => 632, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][0] ?? ''],
                //员工身份证号
                ['x' => 660, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][1] ?? ''],
                //员工身份证号
                ['x' => 681, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][2] ?? ''],
                //员工身份证号
                ['x' => 702, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][3] ?? ''],
                //员工身份证号
                ['x' => 723, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][4] ?? ''],
                //员工身份证号
                ['x' => 751, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][5] ?? ''],
                //员工身份证号
                ['x' => 772, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][6] ?? ''],
                //员工身份证号
                ['x' => 793, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][7] ?? ''],
                //员工身份证号
                ['x' => 814, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][8] ?? ''],
                //员工身份证号
                ['x' => 835, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][9] ?? ''],
                //员工身份证号
                ['x' => 860, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][10] ?? ''],
                //员工身份证号
                ['x' => 881, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][11] ?? ''],
                //员工身份证号
                ['x' => 912, 'y' => 279, 'fz' => 22, 'text' => $fill_data['staff_id_no'][12] ?? ''],
                //员工身份证号


                //新换位置
                ['x' => 632, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][0] ?? ''],
                //非泰国税号
                ['x' => 660, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][1] ?? ''],
                //非泰国税号
                ['x' => 681, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][2] ?? ''],
                //非泰国税号
                ['x' => 702, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][3] ?? ''],
                //非泰国税号
                ['x' => 723, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][4] ?? ''],
                //非泰国税号
                ['x' => 751, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][5] ?? ''],
                //非泰国税号
                ['x' => 772, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][6] ?? ''],
                //非泰国税号
                ['x' => 793, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][7] ?? ''],
                //非泰国税号
                ['x' => 814, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][8] ?? ''],
                //非泰国税号
                ['x' => 835, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][9] ?? ''],
                //非泰国税号
                ['x' => 860, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][10] ?? ''],
                //非泰国税号
                ['x' => 881, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][11] ?? ''],
                //非泰国税号
                ['x' => 912, 'y' => 279, 'fz' => 22, 'text' => $fill_data['tax_no'][12] ?? ''],
                //非泰国税号


//                ['x' => 704, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][0] ?? ''],//非泰国税号
//                ['x' => 732, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][1] ?? ''],//非泰国税号
//                ['x' => 753, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][2] ?? ''],//非泰国税号
//                ['x' => 774, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][3] ?? ''],//非泰国税号
//                ['x' => 795, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][4] ?? ''],//非泰国税号
//                ['x' => 822, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][5] ?? ''],//非泰国税号
//                ['x' => 843, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][6] ?? ''],//非泰国税号
//                ['x' => 864, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][7] ?? ''],//非泰国税号
//                ['x' => 885, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][8] ?? ''],//非泰国税号
//                ['x' => 914, 'y' => 310, 'fz' => 22, 'text' => $fill_data['tax_no'][9] ?? ''],//非泰国税号


                ['x' => 100, 'y' => 310, 'fz' => 22, 'text' => $fill_data['staff_name'] ?? ''],
                //员工姓名
                ['x' => 106, 'y' => 358, 'fz' => 22, 'text' => $fill_data['staff_address'] ?? ''],
                //员工住址
                ['x' => 863, 'y' => 122, 'fz' => 22, 'text' => $fill_data['staff_info_id'] ?? ''],
                //员工工号


                ['x' => 575, 'y' => 530, 'fz' => 22, 'text' => $fill_data['year_at'] ?? ''],
                //泰国年
                ['x' => 790 - $show_tax_base_x, 'y' => 530, 'fz' => 22, 'text' => $fill_data['show_tax_base'] ?? ''],
                //支付金额 算坐标
                ['x' => 909 - $show_tax_x, 'y' => 530, 'fz' => 22, 'text' => $fill_data['show_tax'] ?? ''],
                //税额 算坐标


                ['x' => 790 - $show_in_all_x, 'y' => 1145, 'fz' => 22, 'text' => $fill_data['show_in_all'] ?? ''],
                //总支付金额  算坐标
                ['x' => 909 - $show_in_all_tax_x, 'y' => 1145, 'fz' => 22, 'text' => $fill_data['show_in_all_tax'] ?? ''],
                //总税额  算坐标

                ['x' => 320, 'y' => 1180, 'fz' => 22, 'text' => $fill_data['tax_text'] ?? ''],
                //总税额（文字）
                ['x' => 615, 'y' => 1208, 'fz' => 22, 'text' => $fill_data['show_social'] ?? ''],
                //社保金额
                ['x' => 550, 'y' => 1325, 'fz' => 22, 'text' => $fill_data['day'] ?? ''],
                //日
                ['x' => 640, 'y' => 1325, 'fz' => 22, 'text' => $fill_data['thai_month'] ?? ''],
                //月
                ['x' => 750, 'y' => 1325, 'fz' => 22, 'text' => $fill_data['send_year_at'] ?? ''],
                //发送的年

            ],
        ];

        $merge_data = [];
        if (!empty($add_text)) {
            $merge_data[] = ['x' => 250, 'y' => 1108, 'fz' => 22, 'text' => $add_text ?? ''];
            //退休金补偿金 合并一起放这了 新增字段描述文字
            $merge_data[] = ['x' => 575, 'y' => 1108, 'fz' => 22, 'text' => $fill_data['year_at'] ?? ''];
            $merge_data[] = ['x' => 790 - $show_add_x, 'y' => 1108, 'fz' => 22, 'text' => $show_add_amount ?? ''];

            //补偿金或退休金 金额
            $merge_data[] = ['x' => 909 - $show_add_x_tax,'y' => 1108,'fz' => 22,'text' => $show_add_amount_tax ?? ''];
            //补偿金或退休金 税额

            $data['page_1'] = array_merge($data['page_1'], $merge_data);
        }

        return $data;
    }

    //pdf 画左边 动态获取 小数点之前每个数字的位置
    protected function getXY($showAmount, $num_px, $_px)
    {
        $show_x = strlen(explode('.', str_replace(',', '', $showAmount))[0]) * $num_px;
        if (strstr($showAmount, ',')) {
            $arr = explode(',', $showAmount);
            $show_x += (count($arr) - 1) * $_px;
        }
        return $show_x;
    }


    /**
     *
     * //下载 和批量下载 页面单个员工发送邮箱 用
     * @param $staff_id
     * @param $year
     * @param int $is_download
     * @param null $extend ['mail' => '<EMAIL>']
     * @return array|bool
     * @throws \App\Library\Exception\BusinessException
     * @throws \ImagickException
     */
    public function task_tool($staff_id, $year, $is_download = 0,$extend = null)
    {
        //取出 数据库里面没有发送的记录 和对应的id
        $start = $year.'-01';
        $end   = $year.'-12';
        $temp_flag   = false;
        if ($year == '2021') {//22年 临时方案 明年不用查这个库
            $salary_list = TaxPdfTempModel::find("staff_info_id = {$staff_id}")->toArray();
            if (!empty($salary_list)) {
                $salary_list[0]['excel_month'] = '2021-01';
                $temp_flag                     = true;
            }
        }else if ($year == '2022') {//23年 临时方案 明年不用查这个库
            $salary_list = TaxPdf2022Model::find("staff_info_id = {$staff_id}")->toArray();
        }else if ($year == '2023') {
            $salary_list = TaxPdf2023Model::find("staff_info_id = {$staff_id}")->toArray();
        }else if ($year == '2024') {
            $salary_list = TaxPdf2024Model::find("staff_info_id = {$staff_id}")->toArray();
        }else {
            $salary_list = SalaryGongziModel::find("staff_info_id = {$staff_id} and excel_month >= '{$start}' and excel_month <= '{$end}'")->toArray();
        }

        if (empty($salary_list)) {
            $this->logger->info('send_tax_pdf empty '.$staff_id.'_'.$year);
            return null;
        }

        //看数据库有没有
        $oss_server = new OssService();
        $url        = [];
        //整理 数据 获取对应 坐标 后期做成配置页面
        $pdf_data = $this->format_tax_pdf_data($salary_list, $temp_flag);
        //获取签名图 地址
        $signModel = new TaxSettingService();
        $signUri = $signModel->getImgUri($year);
        $companyMap = array_column(StaffPayrollCompanyInfoRepository::allData(), null,'id');
        $companyMap = array_map(function($item){
            $company_seal_url = !empty($item['company_seal_url']) ? json_decode($item['company_seal_url'], true) : [];
            $item['company_seal_url'] = !empty($company_seal_url) ? $company_seal_url[0]['object_url'] : '';
            return $item;
        },$companyMap);
        //生成 pdf 连接 有可能是多个
        foreach ($pdf_data as $company_id => $da) {
            $fill_data = $this->get_tax_coordination($da);
            if (!empty($companyMap[$company_id]['company_seal_url'])) {
                $fill_data['image'] = [
                    'path' => $companyMap[$company_id]['company_seal_url'],
                    'x'    => 740,
                    'y'    => 1280,

                ];
            }

            //签名图 坐标
            $fill_data['image_sign'] = [
                'path' => $signUri,
                'x'    => 580,
                'y'    => 1250,

            ];
            $url[] = $oss_server->imagick_pdf('tax_pdf.pdf', $fill_data, OssService::PDF_SPACE_CERTIFICATE, [],$staff_id);
        }

        $this->getDI()->get('logger')->info("staff_id : {$staff_id} send tax pdf url ".json_encode($url,JSON_UNESCAPED_UNICODE));

        if ($is_download) {
            return $url;
        }

        return true;
    }


    //任务消费 下载文件到对应文件夹 等着都完事了 好打包
    public function tar_task($staff_id, $year, $origin_id)
    {
        $log_info          = SendTaxLogModel::findFirst("origin_id = {$origin_id} and staff_info_id = {$staff_id}");
        $log_info->is_send = 1;
        $log_info->update();

        $url = $this->task_tool($staff_id, $year, 1);

        if (empty($url)) {
            $this->getDI()->get('logger')->write_log("download tax empty url {$staff_id} {$origin_id}", 'info');
            $log_info->reason = 'empty url';
            $log_info->update();
        }

        $operator_info = SendTaxModel::findFirst($origin_id);
        if (empty($operator_info)) {
            $this->getDI()->get('logger')->write_log("download tax failed {$staff_id} {$origin_id}", 'info');
            $log_info->reason = 'empty main info';
            $log_info->update();
        }

        $path = sys_get_temp_dir().'/'.$operator_info->operator.'_'.$origin_id.'/';
        if (!is_dir($path)) {
            mkdir($path);
        }

        //放到对应文件夹里面等着打包
        foreach ($url as $k => $u) {
            $index    = $k + 1;
            $filename = $path."{$staff_id}_{$year}_{$index}.pdf";
            ob_start();
            readfile($u);
            $content = ob_get_contents();
            ob_end_clean();
            $fp2 = fopen($filename, "a");
            fwrite($fp2, $content);
            fclose($fp2);
        }
        $log_info->update();
        return true;
    }

    /**
     * //扣税证明 新增 员工发送状态 显示 写表 tax_send_state
     * @param $staffId
     * @param $year
     * @param $flag
     * @return boolean
     */
    public function saveSendState($staffId,$year,$flag){
        $exist = TaxSendStateModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and year_time = :year:',
            'bind' => ['staff_id' => $staffId,'year' => $year]
        ]);

        if(empty($exist)){
            $insert['staff_info_id'] = $staffId;
            $insert['year_time'] = $year;
            $insert['state'] = $flag == true ? TaxSendStateModel::SEND_STATE_SUCCESS : TaxSendStateModel::SEND_STATE_FAILED;
            $model = new TaxSendStateModel();
            return $model->create($insert);
        }

        $exist->state = $flag == true ? TaxSendStateModel::SEND_STATE_SUCCESS : TaxSendStateModel::SEND_STATE_FAILED;
        return $exist->update();
    }





    /**
     * 证明下载
     * @param int $staff_id 员工id
     * @param array $type_arr [type => int ,use_type =>  int]    type 证明类型  1- 工资条 2- 在职证明  3- 就业证明（离职证明） 4 薪资证明  use_type 仅ph有  离职证明 目前使用地方在fbi后台 等待迁移到hcm证明下载
     * @param array $date_array 对应证明的日期 工资条可能是多个到月份 在职 单个到日期天  就业证明 固定一份 日期为申请日期
     * @param int $leave_job_type 是否验证 英文名  1 不生成pdf文件  2，3 生成 且限制英文名不能唯恐
     * @param int $mail_type 邮箱类型 by调用 在职证明和 工资证明 选择 邮箱类型 1 企业邮箱 2 个人邮箱 默认企业
     * @param int $is_send 是否需要发送  默认需要  0 不需要
     * @return array
     * @throws ValidationException
     */
    public function certificate_download($staff_id, $type_arr , $date_array = array(), $leave_job_type = 3, $mail_type = 1, $is_send = 1){
        if (empty($date_array)) {
            $date_array[] = date('Y-m', time());
        }

        $type = intval($type_arr['file_type']);
        if(empty($type)){
            $return['code'] = -3;
            $return['msg'] = 'wrong parameter';
            return $return;
        }
        //获取用户信息 没有 返回
        $staff_info = $this->getStaffInfo($staff_id);
        $return['code'] = 1;
        $return['msg'] = '';

        if(empty($staff_info)){
            $return['code'] = -1;
            $return['msg'] = 'no such staff';
            return $return;
        }

        $type_name = self::$certificate_type[$type];
        //根据type  获取对应pdf 模板 所需数据
        $pdf_data = array();
        if(in_array($type,array(OssService::SALARY_PDF,OssService::SALARY_PDF_CN,OssService::SALARY_PDF_EN))){//工资条 信息
            //判断 如果 当天 不是当月最后一天 的 13点之后 不允许 下载当月 pdf
            $current_last_day = date('Y-m-d',strtotime("last day of"));//当月最后一天
            foreach ($date_array as $k => $da){
                //如果 月份 等于当月 并且 不符合条件 删除该月份
                if($da == date('Y-m',time()) && (date('Y-m-d H:i:s') < $current_last_day.' 13:00:00')){
                    unset($date_array[$k]);
                    break;
                }
            }
            if(empty($date_array)){
                $return['code'] = -8;
                $return['msg'] = 'no salary info';
                return $return;
            }
            $date_array = array_values($date_array);

            $date_str = "'" . implode("','", $date_array) . "'";
            //换数据源了
            $paid_type = Enums\SalaryEnums::SALARY_STATUS_PAID;
            $sql = "select staff_info_id as staff_id ,excel_month,paid_day ,staff_name from salary_gongzi
                where  staff_info_id = {$staff_id} and excel_month in ({$date_str})
                and status = {$paid_type}
               ";
            $check = $this->db_rby->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if(empty($check)){
                $return['code'] = -22;
                $return['msg'] = 'no salary info';
                return $return;
            }
            $date_array = array_column($check,'excel_month');
        }

        //4 是新增的 工资证明 就是原来的 在职证明  然后把在职证明 里面的工资信息去掉
        if($type == OssService::ON_JOB_PDF || $type == OssService::PAYROLL_PDF){//在职证明 个人信息 英文 日期 和入职日期 薪资构成
            $sql = " select * from hr_staff_salary where staff_info_id = {$staff_id} ";
            $salary_info = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

            $pdf_data['base_salary'] = round($salary_info['base_salary'] / 100 ,2);
            $pdf_data['name'] = nameSpecialCharsReplace(preg_replace("/\(.*\)/", "", $staff_info['name']));
            $pdf_data['name_en'] = empty($staff_info['name_en']) ? $staff_info['name'] : $staff_info['name_en'];

            $pdf_data['job_title_name'] = $staff_info['job_title_name'];
            $pdf_data['department_name'] = $staff_info['department_name'];

            $en_date = intval(strftime("%d",time())) . strftime(" %B %Y",time());
            $pdf_data['en_date'] = $en_date;
            $pdf_data['hire_date_en'] = intval(strftime("%d",strtotime($staff_info['hire_date']))) . strftime(" %B %Y",strtotime($staff_info['hire_date']));

            //泰国拆分 年月日用
            $pdf_data['th_hire_d'] = intval(date('d',strtotime($staff_info['hire_date'])));
            $hire_key = intval(date('m',strtotime($staff_info['hire_date'])));
            $pdf_data['th_hire_m'] = self::$month_list[$hire_key];
            $pdf_data['th_hire_y'] = (string)(date('Y',strtotime($staff_info['hire_date'])) + 543);

            $current_month = intval(date('m',time()));

            $pdf_data['th_send_date'] = intval(date('d',time())) . ' ' . self::$month_list[$current_month] . ' ' . (date('Y',time()) + 543);

            $th_job_deadline = 'เป็นต้นมาจนถึงปัจจุบัน';
            $en_job_deadline = 'Present';
            if ($staff_info['state'] == HrStaffInfoModel::STATE_RESIGN) {
                $leave_date      = strtotime($staff_info['leave_date']);
                $th_job_deadline = 'ถึงวันที่ ' . date('d', $leave_date) . ' เดือน ' . self::$month_list[date('m',
                        $leave_date)] . ' พ.ศ ' . (date('Y', $leave_date) + 543);
                $en_job_deadline = intval(strftime("%d", $leave_date)) . strftime(" %B %Y", $leave_date);
            }
            $pdf_data['th_job_deadline'] = $th_job_deadline;
            $pdf_data['en_job_deadline'] = $en_job_deadline;

            $pdf_data['en_money'] = $pdf_data['split_money'] = $pdf_data['th_money'] = '';
            if(!empty($pdf_data['base_salary'])){
                //英文钱 字符串
                $pdf_data['en_money'] = num2Word($pdf_data['base_salary']);
                //英泰文 数字钱  千分位
                $pdf_data['split_money'] = number_format($pdf_data['base_salary']);
                //泰文钱
                $pdf_data['th_money'] = bahttext($pdf_data['base_salary']);
            }

        }

        if ($type == OssService::LEAVE_JOB_PDF) { // 就业证明
            $sql = " select * from hr_staff_salary where staff_info_id = {$staff_id} ";
            $salary_info = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $pdf_data['base_salary'] = round($salary_info['base_salary'] / 100 ,2);

            $pdf_data['name'] = nameSpecialCharsReplace(preg_replace("/\(.*\)/", "", $staff_info['name']));
            $pdf_data['name_en'] = empty($staff_info['name_en']) ? '' : $staff_info['name_en'];
            $pdf_data['job_title_name'] = $staff_info['job_title_name'];
            $pdf_data['department_name'] = $staff_info['department_name'];
            //如果没有英文名 并且需要发送邮件  不能发送
            if(empty($staff_info['name_en']))
                return ["code" => -66,"msg" => "no EN name"];

            //入职离职时间 英文的
            $pdf_data['hire_date_en'] = intval(strftime("%d",strtotime($staff_info['hire_date']))) . strftime(" %B %Y",strtotime($staff_info['hire_date']));
            $pdf_data['leave_date_en'] = intval(strftime("%d",strtotime($staff_info['leave_date']))) . strftime(" %B %Y",strtotime($staff_info['leave_date']));
            $pdf_data['en_send_date'] = intval(strftime("%d",time())) . strftime(" %B %Y",time());

            //泰文
            $m = intval(date('m', time()));
            $pdf_data['th_send_date'] = intval(date('d ', time())) . ' ' . self::$month_list[$m] . ' ' . (date(' Y', time()) + 543);
            //入职离职
            $pdf_data['th_hire_d'] = intval(date('d',strtotime($staff_info['hire_date'])));
            $hire_key = intval(date('m',strtotime($staff_info['hire_date'])));
            $pdf_data['th_hire_m'] = self::$month_list[$hire_key];
            $pdf_data['th_hire_y'] = (string)(date('Y',strtotime($staff_info['hire_date'])) + 543);


            $pdf_data['th_leave_d'] = intval(date('d',strtotime($staff_info['leave_date'])));
            $leave_key = intval(date('m',strtotime($staff_info['leave_date'])));
            $pdf_data['th_leave_m'] = self::$month_list[$leave_key];
            $pdf_data['th_leave_y'] = (string)(date('Y',strtotime($staff_info['leave_date'])) + 543);

            $pdf_data['en_money'] = $pdf_data['split_money'] = $pdf_data['th_money'] = '';
            if(!empty($pdf_data['base_salary'])){
                //英文钱 字符串
                $pdf_data['en_money'] = num2Word($pdf_data['base_salary']);
                //英泰文 数字钱  千分位
                $pdf_data['split_money'] = number_format($pdf_data['base_salary']);
                //泰文钱
                $pdf_data['th_money'] = bahttext($pdf_data['base_salary']);
            }
        }

        //生成pdf 更改为 获取 oss的文件地址
        $oss_bll = new OssService();
        $url_list = array();
        foreach($date_array as $d){
            //换新逻辑 查询 file_oss_url 文件类型 $type 1 工资条pdf 2在职证明 3 离职证明（就业证明）4 薪资证明（不同于工资条）

            //on job 在职证明 payroll 薪资证明 是ymd
            if(in_array($type,array(OssService::ON_JOB_PDF,OssService::PAYROLL_PDF))){
                $oss_insert['date_at'] = $d;

            }else {
                $oss_insert['month'] = $d;
                //lang 1 泰文 默认 2 中文 3 英文
                $oss_insert['lang'] = FileOssUrlModel::LANG_TH;
                if ($type == OssService::SALARY_PDF) {
                    $oss_insert['lang'] = FileOssUrlModel::LANG_TH;
                }
                if ($type == OssService::SALARY_PDF_CN) {
                    $oss_insert['lang'] = FileOssUrlModel::LANG_ZH;
                }
                if ($type == OssService::SALARY_PDF_EN) {
                    $oss_insert['lang'] = FileOssUrlModel::LANG_EN;
                }
            }

            $exist = $oss_bll->get_info($staff_id,$type,$oss_insert);
            $url_name = "{$type_name}_{$staff_id}_{$d}";

            if(empty($exist)){//没有已经上传的oss文件 需生成pdf 上传 入库
                if(in_array($type,array(OssService::SALARY_PDF,OssService::SALARY_PDF_CN,OssService::SALARY_PDF_EN))){//工资条 信息
                    $ext_bll = new SalaryService();
                    $url_list[$url_name] = $ext_bll->format_pdf($staff_id,$d,0,$oss_insert['lang']);
                }else{
                    //模板渲染数据
                    $code = $this->get_file_code($staff_id,$type,$d);
                    $year = date('Y',time());
                    $thai_year = intval($year) + 543;
                    $view_data['pdf_data'] = $pdf_data;
                    //泰文版：HRFlashTH-00001-2563
                    //英文版：HRFlashEN-00001-2020
                    $view_data['en_code'] = "HRFlashEN-{$code}-{$year}";
                    $view_data['thai_code'] = "HRFlashTH-{$code}-{$thai_year}";
                    $view_data['staff_info'] = $staff_info;
                    $view_data['th_year'] = $thai_year;
                    //入库数据
                    $oss_insert['staff_info_id'] = $staff_id;
                    $oss_insert['file_type'] = $type;

                    $url_list[$url_name] = $this->createPdf($staff_id, $view_data, $oss_insert);
                }
            }else{
                $url_list[$url_name] = $exist;
            }
        }

        if(!$is_send)
            return $url_list;

        //发送邮件 和附件
        $param['date_array'] = $date_array;
        $param['staff_info'] = $staff_info;
        $param['type'] = $type;
        $param['url_list'] = (in_array($type,array(OssService::SALARY_PDF,OssService::SALARY_PDF_CN,OssService::SALARY_PDF_EN))) ? $url_list : array_values($url_list);
        $mail = $this->get_template($param);
        if ($type == OssService::LEAVE_JOB_PDF) {
            $e_mail = (string)$staff_info['personal_email'];
        } else {
            //新增逻辑 by 可选邮箱发送类型
            if($mail_type == 1)//企业邮箱
                $e_mail = $staff_info['email'];
            else if($mail_type == 2)//个人邮箱
                $e_mail = $staff_info['personal_email'];
            else
                $e_mail = empty($staff_info['email']) ? $staff_info['personal_email'] : $staff_info['email'];
        }


        if ($leave_job_type == 3) {
            if ($e_mail) {
                $this->logger->info("certificate_download:{$type_name} 发送 {$staff_id}-{$e_mail} -");
                try {
                    $sendEmail = BiMail::send_salary(strval($e_mail),$mail['title'],$mail['content']);
                    $this->logger->info("certificate_download_content:{$staff_id}-{$e_mail} ".json_encode([$mail['title'],$mail['content']]));
                }catch (\Exception $e){
                    $this->logger->info("certificate_download: $e_mail 发送失败 {$staff_id}  code [" . $e->getCode(). '] message:' .$e->getMessage());
                    if($e->getCode() == 559){
                        throw new ValidationException('Please check whether email is available!');
                    }else{
                        $this->logger->notice("certificate_download: $e_mail 发送失败 {$staff_id}  code [" . $e->getCode(). '] message:' .$e->getMessage());
                        throw $e;
                    }
                }
                $this->logger->info("certificate_download:{$type_name} 发送结果：{$sendEmail} {$staff_id}-{$e_mail} -");
            } else {
                //没有邮箱
                return [
                    "code" => -2,
                    "msg" => "no such email",
                ];
            }
        }
        return $return;
    }

    //获取文件编码  编码示例泰文版：HRFlashTH-00001-2563 英文版：HRFlashEN-00001-2020 中间部分 为自增
    protected function get_file_code($staff_id, $type,$date){
        //看是否已经生成
        $sql = " select code from certificate_code where staff_info_id = {$staff_id} and `type` = {$type} and date_at = '{$date}' order  by created_at";
        $info = $this->getDI()->get('db_backyard')->fetchAll($sql);
        if(!empty($info)){
            $r = end($info);
            return $r['code'];
        }

        //没有记录
        $sql = " select count(1) from certificate_code where  `type` = {$type} ";
        $num = $this->getDI()->get('db_backyard')->fetchColumn($sql);
        //默认 5位数 如果 过万 位数加一 递增
        $length = 5;
        if($num > '99999'){
            $length = strlen($num);
        }

        if(empty($num))
            $num = '1';
        else
            $num++;

        //示例 HRFlashTH-00001
        $code = str_pad($num,$length,"0",STR_PAD_LEFT);

        //
        $row[] = [
            'staff_info_id' => $staff_id,
            'type' => $type,
            'code' => $code,
            'date_at' => date("Y-m-d", strtotime($date)),
        ];
        $model = new BaseModel();
        $model->table_batch_insert($row,'db_backyard', 'certificate_code');

        return $code;
    }

    /**动态返回 模板 工资条 和 在职证明 邮件标题内容模板
     * 新增 url_list参数 oss 文件地址链接
     * @param $param
     * @return mixed
     */
    protected function get_template($param){
        $return['title'] = "";
        $return['content'] = '';

        if(in_array($param['type'],array(OssService::SALARY_PDF,OssService::SALARY_PDF_CN,OssService::SALARY_PDF_EN))){
            $staff_info = $param['staff_info'];
            /**
             * 1. 邮件主题：x.x工资条
            （月份为：选择发送的月份
            比如
            开始年月：2019.05 结束年月：2019.06，主题是：2019.05-2019.06工资条
            如果 开始年月：2019.05 结束年月：2019.05，主题是：2019.05工资条）
            2. 邮件内容：
            您好，您的本月工资条已附属在以下附件。
            3. 邮件附件：选择年月的工资条
             */

            $start = $param['date_array'][0];
            $end = end($param['date_array']);
            $return['title'] = "({$start} ~ {$end}) {$staff_info['id']}.{$staff_info['name']} สลิปเงินเดือน 工资条";
            if($start == $end)
                $return['title'] = "({$start}) {$staff_info['id']}.{$staff_info['name']} สลิปเงินเดือน 工资条";

            $date = "({$start} ~ {$end})";
            if ($start == $end) {
                $date = "(" . $start . ")";
            }
            $return['content'] = '您好，<br/><br/>
                                您' . $date . '工资条已申请成功，请点击下方链接下载。<br/><br/>
                                谢谢<br/><br/>
                                สวัสดีค่ะ<br/><br/>
                                ตามที่ท่านได้ส่งคำร้องขอสลิปเงินเดือนกับทางบริษัทฯ ขณะนี้ทางบริษัทฯ ได้ดำเนินการจัดส่งสลิปเงินเดือนตามที่่<br/> 
                                ท่านขอเรียบร้อยแล้ว<br/>
                                ท่านสามารถดาวน์โหลดเอกสารดังกล่าวได้ตามลิงก์ด้านล่างนี้<br/>
                                ขอบคุณค่ะ';
            if(!empty($param['url_list'])){
                $return['content'] .= "</br></br>";
                foreach ($param['url_list'] as $show_name => $url){
                    $return['content'] .= "<a href='{$url}'>{$show_name}</a></br>";
                }
            }
        }
        if($param['type'] == 2){
            /**
             * 1. 邮件主题：在职证明
            2. 邮件内容：
            您好，您的在职证明已附属在以下附件。
            3. 邮件附件：英文与泰文的在职证明
             */
            $return['title'] = "หนังสือรับรองพนักงาน 在职证明";
            $return['content'] = "您好，<br/><br/>
                                您在本公司申请的在职证明（仍然在职）已申请成功，<a href='{$param['url_list'][0]}'>请点击此处下载</a>。<br/><br/>
                                谢谢<br/><br/>
                                สวัสดีค่ะ<br/><br/>
                                ตามที่ท่านได้ส่งคำร้องขอหนังสือรับรองพนักงาน(พนักงานปัจจุบัน) ไว้กับทางบริษัทฯ ขณะนี้ทางบริษัทฯ ได้ดำเนิน<br/>
                                การจัดทำหนังสือรับรองฯ ตามที่ท่านขอเรียบร้อยแล้ว<br/>
                                ท่านสามารถ<a href='{$param['url_list'][0]}'>ดาวน์โหลดเอกสารดังกล่าวได้ที่นี่</a></br>
                                ขอบคุณค่ะ<br/>";

        }


        if ($param['type'] == 3) {

            $return['title'] = "หนังสือรับรองพนักงาน 就业证明";
            $return['content'] = "您好<br/><br/> 
                                 您在本公司申请的就业证明（已经离职）已申请成功，请<a href='{$param['url_list'][0]}'>请点击此处下载</a>。<br/><br/> 
                                 谢谢<br/><br/> 
                                 สวัสดีค่ะ<br/><br/> 
                                 ตามที่ท่านได้ส่งคำร้องขอหนังสือรับรองพนักงาน(พนักงานลาออก) ไว้กับทางบริษัทฯ ขณะนี้ทางบริษัทฯ ได้ดำเนิน<br/> 
                                 การจัดทำหนังสือรับรองฯ ตามที่ท่านขอเรียบร้อยแล้ว<br/> 
                                 ท่านสามารถ<a href='{$param['url_list'][0]}'>ดาวน์โหลดเอกสารดังกล่าวได้ที่นี่</a></br>
                                 ขอบคุณค่ะ<br/>";
        }

        if($param['type'] == 4){
            $return['title'] = "หนังสือรับรองเงินเดือน 工资证明";
            $return['content'] = "您好<br/><br/> 
                                 您在本公司申请的工资证明已申请成功，请<a href='{$param['url_list'][0]}'>请点击此处下载</a>。<br/><br/> 
                                 谢谢<br/><br/> 
                                 สวัสดีค่ะ<br/><br/> 
                                 ตามที่ท่านได้ส่งคำร้องขอหนังสือรับรองเงินเดือน ไว้กับทางบริษัทฯ ขณะนี้ทางบริษัทฯ ได้ดำเนินการจัดทำหนังสือ<br/> 
                                 รับรองฯ ตามที่ท่านขอเรียบร้อยแล้ว<br/> 
                                 ท่านสามารถ<a href='{$param['url_list'][0]}'>ดาวน์โหลดเอกสารดังกล่าวได้ที่นี่</a> <br/><br/>
                                 ขอบคุณค่ะ<br/>";
        }

        return $return;
    }

    //证明下载 列表
    public function get_down_list($param){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(s.staff_info_id) as total');
        $builder->from(['s' => HrStaffInfoReadModel::class]);
        $builder->leftJoin(SysStoreModel::class,'s.sys_store_id=ss.id','ss');
        $builder->andWhere('s.formal = 1 and s.is_sub_staff = 0');
        //type=3离职证明
        if(!empty($param['type']) && $param['type'] == CertificateService::EMPLOYMENT_CRETIFICATE_CATEGROY){
            $builder->andWhere(' s.state = 2');
        }

        if(!empty($param['staff_info_id']))
            $builder->andWhere('s.staff_info_id = :staff_info_id: or s.name like :name_str:', ['staff_info_id' => $param['staff_info_id'],'name_str' => "%{$param['staff_info_id']}%"]);

        $jurisdiction = (new StaffService())->getStaffJurisdiction($param['operator']);
        //没有管辖权限
        if (empty($jurisdiction['departments']) &&
            empty($jurisdiction['stores']) &&
            empty($jurisdiction['regions']) &&
            empty($jurisdiction['pieces']) &&
            empty($jurisdiction['store_categories'])
        ) {
            $builder->andWhere('s.staff_info_id = :id:', ['id' => -1]);
        }
        $permissionConditions = '';
        $permissionConditionsBind = [];
        if (!empty($jurisdiction['departments'])) {
            $permissionConditions .= ' or s.node_department_id in ({node_ids:array}) ';
            $permissionConditionsBind['node_ids'] = $jurisdiction['departments'];
        }
        if (in_array(HrStaffManageStoreModel::$all_id,$jurisdiction['stores'])) {
            $jurisdiction['stores'] = array_column((new SysService())->getStoreListFromCache('id',false),'id');
        }
        if (!empty($jurisdiction['stores'])) {
            $permissionConditions .= ' or s.sys_store_id in ({store_ids:array}) ';
            $permissionConditionsBind['store_ids'] = $jurisdiction['stores'];
        }
        if (!empty($jurisdiction['regions'])) {
            $permissionConditions .= ' or ss.manage_region in ({regions:array}) ';
            $permissionConditionsBind['regions'] = $jurisdiction['regions'];
        }
        if (!empty($jurisdiction['pieces'])) {
            $permissionConditions .= ' or ss.manage_piece in ({pieces:array}) ';
            $permissionConditionsBind['pieces'] = $jurisdiction['pieces'];
        }
        if (!empty($jurisdiction['store_categories'])) {
            $permissionConditions .= ' or ss.category in ({categories:array}) ';
            $permissionConditionsBind['categories'] = $jurisdiction['store_categories'];
        }
        if ($permissionConditionsBind) {
            $builder->andWhere(trim($permissionConditions,' or'),$permissionConditionsBind);
        }
        //部门
        if(!empty($param['department_id'])){
            $builder->andWhere('s.node_department_id = :node_department_id:', ['node_department_id' => $param['department_id']]);
        }
        //职位
        if(!empty($param['job_id'])){
            $builder->andWhere('s.job_title = :job_title:', ['job_title' => $param['job_id']]);
        }
        //入职
        if(!empty($param['hire_start']) && !empty($param['hire_end'])){
            $start_1 = date('Y-m-d 00:00:00',strtotime($param['hire_start']));
            $end_1 = date('Y-m-d 00:00:00',strtotime($param['hire_end']));
            $builder->betweenWhere('hire_date',$start_1,$end_1);
        }
        //离职
        if(!empty($param['leave_start']) && !empty($param['leave_end'])){
            $start_2 = date('Y-m-d 00:00:00',strtotime($param['leave_start']));
            $end_2 = date('Y-m-d 00:00:00',strtotime($param['leave_end']));
            $builder->betweenWhere('leave_date',$start_2,$end_2);
        }
        $totalInfo = $builder->getQuery()->getSingleResult();
        $count = intval($totalInfo->total);


        $builder->columns(
            's.staff_info_id, 
            s.name, 
            s.node_department_id, 
            s.job_title job_id,
            SUBSTR (hire_date, 1,10) hire_date, 
            SUBSTR (leave_date, 1,10) leave_date'
        );

        //分页
        $limit  = $param['page_size'] ?? 10;
        $offset = (($param['page'] ?? 1) - 1) * $limit;
        $builder->limit($limit, $offset);

        $list = $builder->getQuery()->execute()->toArray();

        if(!empty($list)){
            //职位
            $job_title_service = new HrJobTitleService();
            $job_title = $job_title_service->getJobTitleListFromCache();
            $job_title_list = array_column($job_title , 'job_name' , 'id');
            //部门
            $sysService = new SysService();
            $department_list = $sysService->getDepartmentListFromCache();
            $department_list = array_column($department_list , 'name' , 'id');

            foreach ($list as &$item) {
                $item['department_name'] = $department_list[$item['node_department_id']];
                $item['job_name'] = $job_title_list[$item['job_id']];
            }
        }


        return array('list'=>$list,'count'=>$count);
    }

    public function getMail($staff_info_id){
        $list = HrStaffInfoReadModel::findFirst(['columns' => 'staff_info_id, email, personal_email',
            'conditions' => "staff_info_id = :staff_info_id:", 'bind'=>['staff_info_id'=>$staff_info_id]
        ]);
        return $list ? $list->toArray() : [];
    }

    public function getStaffInfo($staff_info_id)
    {
        if (empty($staff_info_id)) {
            return [];
        }
        $staffModel      = \App\Models\backyard\HrStaffInfoModel::class;
        $jobModel        = HrJobTitleModel::class;
        $departmentModel = SysDepartmentModel::class;
        $first           = $this->modelsManager->executeQuery("
        select s.state,s.leave_date, s.staff_info_id as id,s.name,s.name_en,s.personal_email,s.email,j.job_name as job_title_name,IF(s.node_department_id>0,nd.name,d.name) as department_name,s.hire_date,s.leave_date,nd.company_id,identity,s.contract_company_id
        from {$staffModel} s
        left join {$jobModel} j on j.id=s.job_title
        left join {$departmentModel} d on d.id=s.sys_department_id
        left join {$departmentModel} nd on nd.id=s.node_department_id
        where s.staff_info_id=:id:
        ", ['id' => $staff_info_id])->getFirst();
        return $first ? $first->toArray() : [];
    }


    /**
     * 获取配置详情
     * @return array
     * 废弃
     */
    public function getSettingConfigDetailOld()
    {
        $settingEnvService = new SettingEnvService();
        $certificateInfo = [];
        $codeList = array_keys(CertificateEnums::$certificateConfigList);
        foreach ($codeList as $oneConfigCode) {
            $settingEnv = $settingEnvService->getSetVal($oneConfigCode);
            $settingEnv = !empty($settingEnv) ? json_decode($settingEnv, true) : [];
            $certificateInfo[$oneConfigCode]['name_en'] = $settingEnv['name_en'] ?? '';
            $certificateInfo[$oneConfigCode]['name_th'] = $settingEnv['name_th'] ?? '';
            $certificateInfo[$oneConfigCode]['job_title_en'] = $settingEnv['job_title_en'] ?? '';
            $certificateInfo[$oneConfigCode]['job_title_th'] = $settingEnv['job_title_th'] ?? '';
            $certificateInfo[$oneConfigCode]['sign_img_url'] = $settingEnv['sign_img_url'] ?? '';
        }

        return $certificateInfo;
    }

    /**
     * 在职/工资/就业证明设置-保存
     * @param $params
     * @return bool
     * 废弃
     */
    public function settingConfigEditOld($params)
    {
        $code = $params['certificate_config_code'];
        unset($params['certificate_config_code']);
        $params['time'] = date('Y-m-d H:i:s');
        $data = json_encode($params, JSON_UNESCAPED_UNICODE);
        (new SettingEnvService())->saveOneSettingEnv($code, CertificateEnums::$certificateConfigList[$code], $data, false);
        return true;
    }


    /**
     * 获取证明文件配置详情
     * @return array
     */
    public function getSettingConfigDetail()
    {
        return (new StaffPayrollCompanyInfoService())->getDetail();
    }

    /**
     * 证明文件配置-保存
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function settingConfigEdit($params)
    {
        (new StaffPayrollCompanyInfoService())->edit($params);

        return true;
    }


    /**
     * 获取员工及pdf 页眉页脚 信息--仅TH 使用
     * @param $staff_info_id
     * @param bool $base64
     * @return mixed
     */
    public function getStaffCompanyInfo($staff_info_id, $base64 = true , $options = [])
    {
        //员工信息
        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($staff_info_id);

        if (isCountry()) {
            $companyId = $staffInfo['contract_company_id'];
        } else {
            $companyId = $this->getCompanyId($staffInfo['node_department_id'], $staffInfo['nationality']);
        }
        $companyConfigInfo = $this->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);

        //文件抬头 公司名称（页眉）
        $data['header_company_name'] = $companyConfigInfo['company_name'] ?? '';
        //公司logo
        $data['header_logo_url'] = $companyConfigInfo['company_logo_url'] ?? '';

        //拼接页脚信息
        $company_address   = $companyConfigInfo['company_address'] ?? '';//公司地址
        $company_phone     = $companyConfigInfo['company_phone'] ?? '';  //公司电话
        $company_web_url   = $companyConfigInfo['company_web_url'] ?? '';//公司网址

        $company_phone_web = [];
        if (!empty($company_phone)) {
            $company_phone_web[] = 'Tel:'.$company_phone;
        }

        if (!empty($company_web_url)) {
            $company_phone_web[] = 'Website:'.$company_web_url;
        }

        $company_phone_web_string = empty($company_phone_web) ? '' : "</br>".implode(' ', $company_phone_web);
        $data['footer_content']   = $company_address.$company_phone_web_string;

        //文件内容 公司名称
        $data['content_company_name']    = $companyConfigInfo['company_short_name'] ?? '';
        $data['content_company_name_th'] = $companyConfigInfo['company_short_name_local'] ?? '';

        //公司印章
        $data['company_seal_url'] = $companyConfigInfo['company_seal_url'] ?? '';

        //员工职位
        $job_title                       = (new HrJobTitleService())->getJobTitleDetail($staffInfo['job_title']);
        $data['job_title_name_position'] = $job_title['job_name'] ?? '';

        //检查 一级部门是否是
        $isFlashExpress = $this->checkDepartmentIsExpress($staffInfo['node_department_id']);

        $data['payroll_email'] = ($isFlashExpress && $staffInfo['nationality'] == HrStaffItemsModel::NATIONALITY_1 && $staffInfo['sys_store_id'] != Enums\GlobalEnums::HEAD_OFFICE_ID) ? CertificateEnums::PAYROLL_EMAIL_2 : CertificateEnums::PAYROLL_EMAIL_HO;
        $data['company_phone'] = $company_phone;

        //获取 员工 所属公司代表人信息
        $data['name_en']      = $companyConfigInfo['labor_name'] ?? '';
        $data['name_th']      = $companyConfigInfo['labor_name_local'] ?? '';
        $data['job_title_en'] = $companyConfigInfo['labor_job_title'] ?? '';
        $data['job_title_th'] = $companyConfigInfo['labor_job_title_local'] ?? '';
        $data['sign_img_url'] = $companyConfigInfo['labor_sign_url'] ?? '';

        [$header, $footer] = $this->getHeaderFooter($data['header_company_name'], $companyConfigInfo['company_logo_url_base64'], $data['footer_content'], $options);
        if($base64) {
            $data['header_template']  = $header;
            $data['footer_template']  = $footer;
        }

        return $data;
    }

    /**
     * 获取员工 所属公司
     * @param $node_department_id
     * @param $nationality
     * @return int
     */
    public function getCompanyId($node_department_id, $nationality)
    {
        $department = (new SysDepartmentService())->getDepartmentDetail($node_department_id);
        if (empty($department)) {
            return CertificateEnums::COMPANY_FLASH_EXPRESS;
        }
        $departmentArray = explode('/', $department['ancestry_v3']);
        if (empty($departmentArray[2])) {
            return CertificateEnums::COMPANY_FLASH_EXPRESS;
        }
        //th 国籍
        if (in_array($departmentArray[2], [CertificateEnums::COMPANY_FFL, CertificateEnums::COMPANY_FCM, CertificateEnums::COMPANY_FP, CertificateEnums::COMPANY_MONEY, CertificateEnums::COMPANY_HOME_OPERATION, CertificateEnums::BULK_BULK_ID, CertificateEnums::INCORPORATION_ID]) && $nationality == HrStaffItemsModel::NATIONALITY_1) {
            return $departmentArray[2];
        }

        return CertificateEnums::COMPANY_FLASH_EXPRESS;
    }

    /**
     * 判断是否是 flash express 部门
     * @param $node_department_id
     * @return bool
     */
    public function checkDepartmentIsExpress($node_department_id)
    {
        $department = (new SysDepartmentService())->getDepartmentDetail($node_department_id);
        if (empty($department)) {
            return false;
        }

        $departmentArray = explode('/', $department['ancestry_v3']);
        if (empty($departmentArray[2])) {
            return false;
        }

        if($departmentArray[2] != CertificateEnums::COMPANY_FLASH_EXPRESS) {
            return false;
        }

        return true;
    }

    /**
     * 是否需要 页脚
     * @param $node_department_id
     * @return bool
     */
    public function checkDepartmentFooter($node_department_id)
    {
        $department = (new SysDepartmentService())->getDepartmentDetail($node_department_id);
        if (empty($department)) {
            return true;
        }

        $departmentArray = explode('/', $department['ancestry_v3']);
        if (empty($departmentArray[2])) {
            return true;
        }

        //  F COM公司  Flash Home 不需要页脚
        if(in_array($departmentArray[2], [CertificateEnums::COMPANY_FCM, CertificateEnums::COMPANY_HOME_OPERATION])) {
            return false;
        }

        return true;
    }

    /**
     * 获取公司logo页眉页脚
     * @param $companyName
     * @param $companyLogoBase64
     * @param $footerContent
     * @return array
     */
    public function getHeaderFooter($companyName, $companyLogoBase64, $footerContent , $options = [])
    {
        $header = '
    <div style="width: 100%;margin: 0 12mm;box-sizing: border-box;">
        <table
        style="border-bottom: 2px solid #333;line-height: 6mm;width: 100%;font-weight: 700;font-size: 4mm;font-family: sans-serif; background-size: 38mm;">
        <tr>
            <td>
            <div>' . $companyName . '</div>
            </td>
            <td style="height: 7mm;text-align: right;">
            <img style="height: 7mm; width: auto;object-fit: cover;"
                src="' . $companyLogoBase64. '" />
            </td>
        </tr>
        </table>
    </div>';

        if (!empty($options['is_footer_sign'])) {
            //页尾巴签字图
            $footerSignImg = '';
            if (!empty($options['footer_sign_url'])) {
                $options['footer_sign_url'] = img_base64_encode($options['footer_sign_url']);
                $footerSignImg              = '<img style="width: 35mm; height: 15mm; " src="'.$options['footer_sign_url'].'">';
            }

            $t         = $this->getTranslation($options['lang'] ?? self::$language);
            $signTitle = $t->_('staff_sign_title');

            $footer = '<div style=" width: 297mm; box-sizing: border-box;">
    <div style="width: 100%; text-align: right; font-size: 3mm; display: flex; justify-content: flex-end; align-items: flex-end;">
      <span style="margin-right: 4mm;">'.$signTitle.':</span>
      <div style="width: 35mm; height: 17mm; padding-bottom: 2mm; box-sizing: border-box; border-bottom: 1px dashed black; margin-right: 6mm;">
      '.$footerSignImg.'
      </div>
    </div>
    <div style="width: 100%;text-align: center; font-size: 2mm; ">
      <p>'.$footerContent.'</p>
    </div>
  </div>';
        } else {
            $footer = '<div style=" width: 297mm; box-sizing: border-box; height: 10mm "><div style="width: 100%;text-align: center; font-size: 2mm; "><p>'.$footerContent.'</p></div></div>';
        }

        return [$header, $footer];
    }

    /**
     * 生成pdf
     * @param $staff_id
     * @param $pdf_data
     * @param $oss_insert
     * @return mixed|string
     * @throws \Exception
     */
    public function createPdf($staff_id,$pdf_data, $oss_insert)
    {
        $type_name = self::$certificate_type[$oss_insert['file_type']];

        //获取员工 所属 公司 信息
        $pdf_company_data = $this->getStaffCompanyInfo($staff_id);
        $pdf_data['company_data'] = $pdf_company_data;
        //模板地址
        $tmpPath = APP_PATH . '/views/salary/' . $type_name . '_pdf.ftl';
        $pdf_temp_url = $this->getPdfTemp($tmpPath);

        $file_name = time().rand(1,10000)."_{$staff_id}";
        $pdf_img_data[] = ['name' => 'company_seal_url', 'url' => $pdf_company_data['company_seal_url']];//公司印章
        $pdf_img_data[] = ['name' => 'sign_img_url', 'url' => $pdf_company_data['sign_img_url']];//法人签字

        $singPdfSetting = [
            'format'              => 'a4',
            'displayHeaderFooter' => true,
            'headerTemplate'      => $pdf_company_data['header_template'],
            'footerTemplate'      => $pdf_company_data['footer_template'],
        ];

        $res = (new FormPdfServer())->getInstance()->generatePdf($pdf_temp_url, $pdf_data, $pdf_img_data, $singPdfSetting,
            $file_name, '');//attchment

        $url = $res['object_url'] ?? '';

        if ($url) {
            //保存 file_oss_url
            $insert['staff_info_id'] = $staff_id;
            $insert['month']         = $oss_insert['month'] ?? '';
            $insert['file_type']     = $oss_insert['file_type'];
            $insert['lang']          = $oss_insert['lang'] ?? '';
            $insert['path']          = urldecode($res['object_key']);
            $insert['bucket']        = $res['bucket_name'];
            $model                   = new FileOssUrlModel();
            $model->create($insert);
        }
        return $url;

    }

    /**
     * 获取公司配置信息
     * @param $params
     * @return array
     */
    public function getPdfCompanyInfoByCompanyId($params)
    {
        if(empty($params)) {
            //兜底逻辑
            $params['id'] = CertificateEnums::COMPANY_FLASH_EXPRESS;
        }
        $data = StaffPayrollCompanyInfoRepository::getOne($params);
        if(empty($data)) {//兜底逻辑
            $where['id'] = CertificateEnums::COMPANY_FLASH_EXPRESS;
            $data = StaffPayrollCompanyInfoRepository::getOne($where);
        }

        $company_logo_url = !empty($data['company_logo_url']) ? json_decode($data['company_logo_url'], true) : [];
        $data['company_logo_url'] = !empty($company_logo_url) ? $company_logo_url[0]['object_url'] : '';

        $company_card_url = !empty($data['company_card_url']) ? json_decode($data['company_card_url'], true) : [];
        $data['company_card_url'] = !empty($company_card_url) ? $company_card_url[0]['object_url'] : '';

        $labor_sign_url   = !empty($data['labor_sign_url']) ? json_decode($data['labor_sign_url'], true) : [];
        $data['labor_sign_url']   = !empty($labor_sign_url) ? $labor_sign_url[0]['object_url'] : '';

        $company_seal_url = !empty($data['company_seal_url']) ? json_decode($data['company_seal_url'], true) : [];
        $data['company_seal_url'] = !empty($company_seal_url) ? $company_seal_url[0]['object_url'] : '';

        return $data;
    }

    /**
     * 获取公司配置信息--th 走的是 th的service
     * @param $params
     * @return array
     */
    public function getCompanyConfigInfo($params)
    {
        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($params['staff_info_id']);

        $departmentId = !empty($params['department_id']) ? $params['department_id'] : $staffInfo['node_department_id'];

        $companyId = $this->getCompanyId($departmentId, $staffInfo['nationality']);

        return $this->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);
    }

    /**
     * 获取 公司配置信息 winHr用
     * @param $params
     * @return array
     */
    public function getCompanyConfigInfoWinHr($params)
    {
        $companyId = $params['department_id'];

        if(!empty($params['department_id']) && !empty($params['nationality'])) {
            $companyId = $this->getCompanyId($params['department_id'], $params['nationality']);
        }

        return $this->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);
    }

    /**
     * 根据company id获取company信息
     * @param $local
     * @param $params
     * @return array
     */
    public function getCompanyConfigInfoByCompanyId($local,$params)
    {
        $companyId = $params['company_id'];
        return $this->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 批量导入
     * @param integer $staff_info_id 当前登陆工号
     * @param string $file_url 文件OSS地址
     * @param string $lang 前端传递的语种
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function importHour($staff_info_id, $file_url, $lang)
    {
        $this->uploadCheck($file_url);
        return (new AsyncImportTaskService())->insertTask($staff_info_id,
            AsyncImportTaskModel::CERTIFICATE_IMPORT_HOUR, $file_url, ['lang' => $lang], false, false);
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 批量导入 - 写任务前相关校验
     * @param string $file_url 阿里云地址
     * @throws BusinessException
     * @throws ValidationException
     */
    private function uploadCheck($file_url)
    {
        $tmp_dir       = sys_get_temp_dir();              // 获取系统的临时目录路径
        $file_name     = basename($file_url);             // 提取文件名
        $tmp_file_path = $tmp_dir . '/' . $file_name;     // 构建临时文件路径
        if (!file_put_contents($tmp_file_path, file_get_contents($file_url))) {
            @unlink($tmp_file_path);
            throw new BusinessException('System error');
        }

        $config         = ['path' => dirname($tmp_file_path)];
        $file_real_mame = basename($tmp_file_path);
        $excel          = new \Vtiful\Kernel\Excel($config);
        //请上传.xlsx文件！
        $extension = pathinfo($file_real_mame)['extension'] ?? '';
        if (!in_array($extension, ['xlsx'])) {
            throw new ValidationException(self::$t->_('21369_hcm_error_message_001'));
        }

        //文件内容为空
        $excel->openFile($file_real_mame)->openSheet()->setSkipRows(1);
        $sheet_data = $excel->getSheetData();

        @unlink($tmp_file_path);

        if (empty($sheet_data)) {
            throw new ValidationException(self::$t->_('21369_hcm_error_message_002'));
        }

        //必填，同一文件只能有一个年份
        $clear_excel_data = [];
        foreach ($sheet_data as $key => $row) {
            $clear_excel_data[] = $row[0];
        }
        $year = array_unique($clear_excel_data);
        if (count($year) > 1) {
            throw new ValidationException(static::$t->_('21369_hcm_error_message_003'));
        } elseif (!$this->isValidMysqlYear($year[0])) {
            //年份必须有值且是yyyy格式
            throw new ValidationException(static::$t->_('21369_hcm_error_message_004'));
        }
    }


    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 批量导入 - 逻辑处理
     * @param string $file_path 构建临时文件路径
     * @param object $task 人物对象
     * @return bool
     * @throws BusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \OSS\Core\OssException
     */
    public function dealImportHourData($file_path, $task)
    {
        //获取Excel数据
        $excel_data = $this->getExcelData($file_path,
            [
                0 => \Vtiful\Kernel\Excel::TYPE_INT,
                1 => \Vtiful\Kernel\Excel::TYPE_STRING,
            ]);

        //移除表头
        $header                      = array_shift($excel_data);
        $tax_pdf_hour_warehouse_data = $this->excelToData($excel_data);

        //证件号码组
        $identity_data = array_column($tax_pdf_hour_warehouse_data, 'identity');
        $identity      = array_values(array_unique(array_filter($identity_data)));

        //根据证件号码组获取每个证件号码下最晚入职日期所在的员工信息
        $os_staff_list = $this->getOutsourceHrStaffList($identity);

        //获取在所选年份已有数据数据的证件号码
        $year                        = $tax_pdf_hour_warehouse_data[0]['tax_year'] ?? '';//因一个文件里年份都一样
        $tax_pdf_hour_warehouse_list = $this->getTaxPdfHourWarehouseKeeperList($year, array_keys($os_staff_list));

        //统计每个证件号在文件中存在的次数
        $identity_numbers = array_count_values($identity_data);

        $create_data    = [];
        $success_number = $error_number = 0;
        foreach ($tax_pdf_hour_warehouse_data as $key => $item) {
            //错误信息组
            $error_msg = [];

            //输入员工的证件号码，有效格式为13位数字
            $identity = $item['identity'] ?? '';
            if (empty($identity) || !preg_match('/^[1-9]\d{12}$/', $identity)) {
                $error_msg[] = 'Employee ID Card must be 13 digits';
            } elseif ($identity_numbers[$identity] >= 2) {
                //文件中有多个相同的Employee ID Card数据
                $error_msg[] = 'Employee ID Card is repeat in the file';
            } elseif (!isset($os_staff_list[$identity])) {
                //未在外协员工管理找到对应「身份证/护照」：Employee ID Card does not exist in the system
                $error_msg[] = 'Employee ID Card does not exist in the system';
            } elseif (isset($tax_pdf_hour_warehouse_list[$identity])) {
                //在所选年份已有数据：Employee ID Card has exist
                $error_msg[] = 'Employee ID Card has exist';
            }

            //输入员工的收入，有效格式为7位整数和2位小数
            $income = $item['income'] ?? '';
            if (!$this->isValidIncome($income)) {
                $error_msg[] = 'Total for PND1 must be 7 digits(2 decimal places)';
            }

            if (!empty($error_msg)) {
                $error_number++;
                $excel_data[$key][3] = 'failed';
                $excel_data[$key][4] = implode(';', $error_msg);
            } else {
                $success_number++;
                $excel_data[$key][3] = 'success';
                $excel_data[$key][4] = '';
                $one_staff_info = $os_staff_list[$identity];
                $create_data[]  = [
                    'tax_year'       => $year,
                    'identity'       => $identity,
                    'income'         => $income,
                    'staff_info_id'  => $one_staff_info['staff_info_id'],
                    'name'           => $one_staff_info['name'],
                    'personal_email' => $one_staff_info['personal_email'],
                    'hire_date'      => $one_staff_info['hire_date'],
                    'pdf_url'        => '',
                    'state'          => TaxPdfHourWarehouseKeeperModel::SEND_STATE_WAIT,
                ];
            }
        }

        $file_name  = $task->result_file_name;
        $header[3] = 'Status';
        $header[4] = 'Remark';
        $excel_file = $this->exportExcel($header, $excel_data, $file_name);
        $flashOss   = new FlashOss();
        $oss_path   = 'certificate_50tawi_hour' . '/' . $file_name;
        $flashOss->uploadFile($oss_path, $excel_file['data']);

        //存储数据
        $db = $this->getDI()->get('db_backyard');
        $db->begin();

        try {
            if (!empty($create_data)) {
                (new TaxPdfHourWarehouseKeeperModel())->batch_insert($create_data);
            }

            //调整导出结果
            $task                 = AsyncImportTaskModel::findFirst($task->id);
            $task->result_path    = $oss_path;
            $task->fail_number    = $error_number;
            $task->success_number = $success_number;
            $task->status         = AsyncImportTaskModel::STATE_EXECUTED;
            $task->save();

            $db->commit();

            return true;
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('dealImportHourData Import Error' . $e->getMessage() . ' ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * 转excel索引
     * @param array $excel_data excel数据
     * @return array
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'tax_year',//年份
            'identity',//身份证/护照
            'income',  //收入
        ];
        $data     = [];
        foreach ($excel_data as $line => $info) {
            foreach ($data_key as $index => $key) {
                $data[$line][$key] = trim($info[$index]);
            }
        }
        return $data;
    }

    /**
     * 年份用的是mysql year类型，需要判断是否是合理年份
     * @param integer $input 年份
     * @return bool
     */
    public function isValidMysqlYear($input)
    {
        if (!is_numeric($input)) {
            return false;
        }

        $year   = (int)$input;
        $strVal = (string)$year;

        // MySQL YEAR(4)格式验证
        return strlen($strVal) === 4
            && $strVal[0] !== '0'  // 排除前导零
            && $year >= 1901
            && $year <= 2155;
    }

    /**
     * 输入员工的收入，有效格式为7位整数和2位小数
     * @param string $input 收入
     * @return bool
     */
    public function isValidIncome($input)
    {
        // 转换为字符串处理
        $strVal = (string)$input;

        // 格式验证正则（允许纯零值）
        if (!preg_match('/^
        (?:0(?:\.\d{1,2})?|         # 允许0.x 或 0.xx（x是任意数字）
        [1-9]\d{0,6}(?:\.\d{1,2})?) # 1-7位整数 + 可选小数
        $/x', $strVal)) {
            return false;
        }

        // 数值范围验证
        $value = (float)$strVal;
        return $value >= 0 && $value <= 9999999.99;
    }

    /**
     * 获取每个员工最晚入职日期所在的员工信息
     * @param array $identity 身份证/护照
     * @return array
     */
    public function getOutsourceHrStaffList($identity)
    {
        if (empty($identity)) {
            return [];
        }

        // 读取符合条件的外协信息
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hr' => HrStaffInfoModel::class]);
        $builder->columns('hr.staff_info_id, hr.personal_email, hr.name, hr.hire_date, hr.identity');
        $builder->where('hr.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_0]);
        $builder->inWhere('hr.identity', $identity);
        $items = $builder->getQuery()->execute()->toArray();
        $premier_identity = array_values(array_unique(array_column($items, 'identity')));
        if (empty($premier_identity)) {
            return [];
        }

        // 获取每个身份证下最晚的入职日期
        $max_hire_date = $this->modelsManager->createBuilder()
            ->from(['hr' => HrStaffInfoModel::class])
            ->columns('hr.identity, MAX(hr.hire_date) as max_hire_date')
            ->where('hr.formal = :formal:', ['formal' => HrStaffInfoModel::FORMAL_0])
            ->inWhere('hr.identity', $premier_identity)
            ->groupBy('hr.identity')
            ->getQuery()
            ->execute()
            ->toArray();
        $identity_max_hire_date = array_column($max_hire_date, 'max_hire_date', 'identity');

        // 获取每个身份证下最晚入职的那条员工记录
        $identity_group = [];
        foreach ($items as $staff_info) {
            $identity = $staff_info['identity'];
            if (isset($identity_max_hire_date[$identity]) && $staff_info['hire_date'] && $staff_info['hire_date'] == $identity_max_hire_date[$identity]) {
                $identity_group[$identity] = $staff_info;
            }
        }
        return $identity_group;
    }

    /**
     * 获取在所选年份已有数据数据的证件号码
     * @param integer $year 年份
     * @param array $identity 身份证/护照
     * @return array
     */
    public function getTaxPdfHourWarehouseKeeperList($year, $identity)
    {
        if (empty($year) || empty($identity)) {
            return [];
        }
        $items = TaxPdfHourWarehouseKeeperModel::find([
            'conditions' => 'tax_year = :tax_year: and identity in ({identity:array})',
            'bind'       => ['tax_year' => $year, 'identity' => $identity],
        ])->toArray();
        return array_column($items, null, 'identity');
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 列表
     * @param array $params 参数组
     * @return array
     */
    public function getHourList($params)
    {
        $page_size   = empty($params['page_size']) ? 20 : $params['page_size'];
        $page_num    = empty($params['page_num']) ? 1 : $params['page_num'];
        $offset     = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(TaxPdfHourWarehouseKeeperModel::class);
        $builder->columns('count(id) AS count');
        $builder->where('tax_year = :tax_year:', ['tax_year' => $params['tax_year']]);
        //身份证/护照
        if (!empty($params['identity'])) {
            $builder->andWhere('identity = :identity:', ['identity' => $params['identity']]);
        }
        //发送状态：-1未发送 1 发送成功 2 发送失败
        if (!empty($params['state'])) {
            $builder->andWhere('state = :state:', ['state' => $params['state']]);
        }
        $count = $builder->getQuery()->getSingleResult()->count;
        $list = [];
        if ($count > 0) {
            $builder->columns('id, tax_year, identity, personal_email, pdf_url, state');
            $builder->limit($page_size, $offset);
            $builder->orderby('id');
            $list = $builder->getQuery()->execute()->toArray();
            foreach ($list as &$item) {
                $item['state_text'] = static::$t->_(TaxSendStateModel::$tax_send_state[$item['state']]);
            }
        }

        return ['list' => $list, 'count' => $count, 'page_size' => $page_size, 'page_num' => $page_num];
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 信息
     * @param integer $id id
     * @return mixed
     * @throws ValidationException
     */
    private function getTaxPdfHourInfo($id)
    {
        $tax_pdf_hour_info = TaxPdfHourWarehouseKeeperModel::findFirst(['conditions' => 'id = :id:', 'bind' => ['id' => $id]]);
        if (empty($tax_pdf_hour_info)) {
            throw new ValidationException('21369_hcm_error_message_005');
        }
        return $tax_pdf_hour_info;
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 发送邮件
     * @param array $params 参数组
     * @return bool
     * @throws ValidationException
     */
    public function sendHourEmail($params)
    {
        $tax_pdf_hour_info = $this->getTaxPdfHourInfo($params['id']);
        return $this->sendEmail($tax_pdf_hour_info, $params);
    }

    /**
     * 发送邮件
     * @param object $tax_pdf_hour_info 小时工仓管发送50tawi记录信息
     * @param array $params
     * @return bool
     */
    public function sendEmail($tax_pdf_hour_info, $params = [])
    {
        $message = 'success';
        //若从未生成需要生成pdf
        if (empty($tax_pdf_hour_info->pdf_url)) {
            $tax_pdf_hour_info->pdf_url = $this->createHourPdf($tax_pdf_hour_info);
        }

        //发送结果
        $send_personal_email_result = $send_get_email_result = false;

        //只有有pdf的才能发
        if ($tax_pdf_hour_info->pdf_url) {
            //无论是否发送过，都可发送邮件
            $year      = $tax_pdf_hour_info->tax_year;
            $thai_year = $year + 543;
            $title     = "หนังสือรับรองหัก ณ ที่จ่าย (50 ทวิ) ประจำปี {$thai_year}";
            $content   = "หนังสือรับรองหัก ณ ที่จ่าย (50 ทวิ) ประจำปี {$thai_year} ส่งเรียบร้อยแล้วกรุณาตรวจสอบ.";
            $content  .= "<p><a href='{$tax_pdf_hour_info->pdf_url}'>{$tax_pdf_hour_info->identity}_หนังสือรับรองหัก ณ ที่จ่าย (50 ทวิ) ประจำปี {$thai_year}</a></p>";

            //先发个人邮箱
            if (!empty($tax_pdf_hour_info->personal_email)) {
                try {
                    $send_personal_email_result = BiMail::send_salary($tax_pdf_hour_info->personal_email, $title, $content);
                    $this->logger->info([
                        'sendTaxPdfHour email ' => $tax_pdf_hour_info->personal_email,
                        'send_result'           => ($send_personal_email_result ? 'success' : 'fail'),
                    ]);
                } catch (\Exception $e) {
                    $this->logger->info([
                        'sendTaxPdfHour email ' => $tax_pdf_hour_info->personal_email,
                        'message'               => $e->getMessage() . ' ' . $e->getTraceAsString(),
                    ]);
                }
            } else {
                $message = 'Personal email is blank';
            }

            //自定义邮箱
            if (!empty($params['email'])) {
                try {
                    $send_get_email_result = BiMail::send_salary($params['email'], $title, $content);
                    $this->logger->info([
                        'sendTaxPdfHour email ' => $params['email'],
                        'send_result'           => ($send_get_email_result ? 'success' : 'fail'),
                    ]);
                } catch (\Exception $e) {
                    $this->logger->info([
                        'sendTaxPdfHour email ' => $params['email'],
                        'message'               => $e->getMessage() . ' ' . $e->getTraceAsString(),
                    ]);
                }
            }
        } else {
            $message = 'Sent Failed';
        }

        //任意成功，视为发送成功
        if ($send_personal_email_result || $send_get_email_result) {
            $state = TaxPdfHourWarehouseKeeperModel::SEND_STATE_SUCCESS;
        } else {
            $state = TaxPdfHourWarehouseKeeperModel::SEND_STATE_FAILED;
            $message = $message != 'success' ? $message : 'Sent Failed';
        }

        $tax_pdf_hour_info->state = $state;
        $res = $tax_pdf_hour_info->save();
        if (!$res) {
            $this->logger->error(['sendTaxPdfHour' => "{$tax_pdf_hour_info->id} save state={$state} error"]);
        }
        return $message;
    }

    /**
     * 生成pdf
     * @param object $tax_pdf_hour_info 小时工仓管发送50tawi记录信息
     * @return mixed
     */
    private function createHourPdf($tax_pdf_hour_info)
    {
        $template_url = (new SettingEnvService())->getSetVal('tax_pdf_hour_warehouse_template_url');
        $income_arr   = explode('.', $tax_pdf_hour_info->income);
        $data         = [
            'identity'     => $tax_pdf_hour_info->identity,                        //员工身份证/护照
            'name'         => $tax_pdf_hour_info->name,                            //员工姓名
            'address'      => $this->getAddress($tax_pdf_hour_info->staff_info_id),//员工地址
            'thai_year'    => (string)($tax_pdf_hour_info->tax_year + 543),        //佛历年，即公历年+543
            'income_int'   => number_format($income_arr[0]),                       //收入、收入合计-整数
            'income_float' => $income_arr[1],                                      //收入、收入合计-整数
            'thai_tax'     => baht_text(0.00),                              //税额合计的泰语
            'day'          => date('d'),                                     //生成pdf的日期
            'thai_month'   => Enums::$thai_month[intval(date('m'))],         //生成pdf的月份-泰文
        ];
        //证明下载设置-50tawi对应年份配置的签字人
        $img_data    = [
            [
                'name' => 'sign_url',
                'url'  => (new TaxSettingService())->getImgUri($tax_pdf_hour_info->tax_year),
            ],
        ];
        $pdf_options = [
            'displayHeaderFooter' => true,
            'headerTemplate'      => '',
            'footerTemplate'      => '',
            'format'              => 'A4',
            'printBackground'     => true
        ];
        $file_name   = "{$tax_pdf_hour_info->tax_year}_{$tax_pdf_hour_info->identity}";
        $res         = (new FormPdfServer())->getInstance()->generatePdf($template_url, $data, $img_data, $pdf_options, $file_name);
        if (isset($res['object_url'])) {
            $tax_pdf_hour_info->pdf_url = $res['object_url'];
        } else {
            $this->logger->error(['sendTaxPdfHour createHourPdf Error' => $tax_pdf_hour_info->toArray()]);
        }
        return $tax_pdf_hour_info->pdf_url;
    }

    /**
     * 员工地址
     * @param integer $staff_id 工号
     * @return string
     */
    private function getAddress($staff_id)
    {
        //根据国籍 NATIONALITY 不同 显示 身份证 或者 税号
        $item_arr = [
            'REGISTER_PROVINCE',    //户口所在省
            'REGISTER_CITY',        //户口所在市
            'REGISTER_DISTRICT',    //户口所在乡
            'REGISTER_POSTCODES',   //户口所在邮编
            'REGISTER_HOUSE_NUM',   //户口所在门牌号
            'REGISTER_VILLAGE_NUM', //户口所在村号
            'REGISTER_VILLAGE',     //户口所在村
            'REGISTER_ALLEY',       //户口所在巷
            'REGISTER_STREET',      //户口所在街道
        ];

        $item_info = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id = :staff_id: and item in({item_arr:array})',
            'bind'       => ['staff_id' => $staff_id, 'item_arr' => $item_arr],
        ])->toArray();

        //拼接 户口所在地
        $address = 'เลขที่ 731 อาคารพีเอ็ม ทาวเวอร์ ชั้นที่ 9 ถนนดินแดง แขวงดินแดง เขตดินแดง กรุงเทพมหานคร 10400';
        if (!empty($item_info)) {
            $item_info = array_column($item_info, 'value', 'item');
            $address   = $this->getStaffAddress($item_info, 'เลขที่ 731 อาคารพีเอ็ม ทาวเวอร์ ชั้นที่ 9 ถนนดินแดง แขวงดินแดง เขตดินแดง กรุงเทพมหานคร 10400');
        }
        return $address;
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 批量发送邮件
     * @param array $params 参数组
     * @return array
     */
    public function batchSendHourEmail($params)
    {
        $excel_data = [];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(TaxPdfHourWarehouseKeeperModel::class);
        $builder->where('tax_year = :tax_year:', ['tax_year' => $params['tax_year']]);
        //身份证/护照
        if (!empty($params['identity'])) {
            $builder->andWhere('identity = :identity:', ['identity' => $params['identity']]);
        }
        //发送状态：-1未发送 1 发送成功 2 发送失败
        if (!empty($params['state'])) {
            $builder->andWhere('state = :state:', ['state' => $params['state']]);
        }
        $items = $builder->getQuery()->execute();
        $count = count($items->toArray());
        if ($count > 0) {
            foreach ($items as $item) {
                $this->logger->info(date('Y-m-d H::i:s') . ' id：' . $item->id . ' sendEmail start');
                $message = $this->sendEmail($item);
                $this->logger->info(date('Y-m-d H::i:s') . ' id：' . $item->id . ' sendEmail end');
                $excel_data[] = [
                    $item->identity,
                    $message == 'success' ? 'success' : 'failed',
                    $message == 'success' ? '' : $message
                ];
            }
        }
        $this->logger->info(['本次需发送邮件个数' => $count, '发送数据情况' => $excel_data, '数据参数组' => $params]);
        return $excel_data;
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 下载
     * @param array $params 参数组
     * @return bool
     * @throws ValidationException
     */
    public function downloadHourPdf($params)
    {
        $tax_pdf_hour_info = $this->getTaxPdfHourInfo($params['id']);
        $pdf_url = $this->downloadPdf($tax_pdf_hour_info, $params);
        if (empty($pdf_url)) {
            throw new ValidationException('21369_hcm_error_message_007');
        }
        return $pdf_url;
    }

    /**
     * 下载pdf
     * @param object $tax_pdf_hour_info 小时工仓管发送50tawi记录信息
     * @return mixed
     */
    public function downloadPdf($tax_pdf_hour_info)
    {
        if (empty($tax_pdf_hour_info->pdf_url)) {
            $tax_pdf_hour_info->pdf_url = $this->createHourPdf($tax_pdf_hour_info);
            $res = $tax_pdf_hour_info->save();
            if (!$res) {
                $this->logger->error(['downloadPdf' => "{$tax_pdf_hour_info->id} save pdf_url={$tax_pdf_hour_info->pdf_url} error"]);
            }
        }
        return $tax_pdf_hour_info->pdf_url;
    }

    /**
     * 21369【TH丨HCM丨薪酬】小时工仓管发送50tawi - 批量下载
     * @param array $params 参数组
     * @return array
     */
    public function batchDownloadHourPdf($params)
    {
        $pdf_data = [];
        $builder  = $this->modelsManager->createBuilder();
        $builder->from(TaxPdfHourWarehouseKeeperModel::class);
        $builder->where('tax_year = :tax_year:', ['tax_year' => $params['tax_year']]);
        //身份证/护照
        if (!empty($params['identity'])) {
            $builder->andWhere('identity = :identity:', ['identity' => $params['identity']]);
        }
        //发送状态：-1未发送 1 发送成功 2 发送失败
        if (!empty($params['state'])) {
            $builder->andWhere('state = :state:', ['state' => $params['state']]);
        }
        $items = $builder->getQuery()->execute();
        $count = count($items->toArray());
        if ($count > 0) {
            foreach ($items as $item) {
                $this->logger->info(date('Y-m-d H::i:s') . ' id：' . $item->id . ' download start');
                $pdf_url = $this->downloadPdf($item);
                $this->logger->info(date('Y-m-d H::i:s') . ' id：' . $item->id . ' download end');
                if ($pdf_url) {
                    $pdf_data[] = [
                        'pdf_url'   => $pdf_url,
                        'file_name' => "{$item->tax_year}_{$item->identity}.pdf",
                    ];
                }
            }
        }
        $this->logger->info(['本次需下载pdf个数' => $count, '发送数据情况' => $pdf_data, '数据参数组' => $params]);
        return $pdf_data;
    }
}