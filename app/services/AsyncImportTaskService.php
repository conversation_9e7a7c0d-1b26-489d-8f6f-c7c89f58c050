<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Models\backyard\AsyncImportTaskModel;

/**
 * 异步导入
 * Class AsyncImportTaskService
 * @package App\Services
 */
class AsyncImportTaskService extends BaseService
{


    /**
     * 自定义结果文件名字
     * @var string
     */
    public $customizeResultFileName = '';
    /**
     * @throws BusinessException
     * @throws \Exception
     */
    public function downloadResultFile($id)
    {
        $first = AsyncImportTaskModel::findFirst($id);
        if (empty($first)) {
            throw new BusinessException('not  found  task');
        }
        if ($first->status == AsyncImportTaskModel::STATE_WAIT_EXECUTE) {
            throw new BusinessException(static::$t->_('task running'));
        }
        if (empty($first->result_path)) {
            throw new BusinessException(static::$t->_('async_import_task_not_result'));
        }
        return (new FlashOss())->signUrl($first->result_path, 3600 * 24 * 30);
    }

    /**
     * 任务列表
     * @param $params
     * @param $pageSize
     * @param $pageNum
     * @return array
     */
    public function list($params, $pageSize, $pageNum): array
    {
        $type       = $params['type'];
        $operatorId = $params['operator_id'];
        $pageSize   = empty($pageSize) ? 20 : $pageSize;
        $pageNum    = empty($pageNum) ? 1 : $pageNum;
        $offset     = $pageSize * ($pageNum - 1);
        $condition['bind']['is_deleted']  = AsyncImportTaskModel::IS_NOT_DELETED;
        $condition['order']               = 'update_at DESC';
        if (!empty($type) && is_array($type)){
            $condition['bind']['import_type'] = $type;
            $condition['conditions']          = 'import_type in ({import_type:array}) and is_deleted=:is_deleted:';
        }else{
            $condition['bind']['import_type'] = $type;
            $condition['conditions']          = 'import_type=:import_type: and is_deleted=:is_deleted:';
        }
        if (!empty($operatorId)) {
            $condition['conditions']      .= ' and operator_id = :operator_id:';
            $condition['bind']['operator_id'] = $operatorId;
        }
        $totalCount           = AsyncImportTaskModel::count($condition);
        $condition['limit']   = $pageSize;
        $condition['offset']  = $offset;
        $list                 = AsyncImportTaskModel::find($condition)->toArray();
        foreach ($list as &$item) {
            $item['created_at']  = show_time_zone($item['created_at']);
            $item['update_at']   = show_time_zone($item['update_at']);
            $item['status_name'] = static::$t->_('async_import_task_status_' . $item['status']);
            $this->listExtendByType($item);
            unset($item['args_json']);
        }
        return ['list' => $list, 'total' => $totalCount, 'page_size' => $pageSize, 'page_num' => $pageNum];
    }

    protected function listExtendByType(&$data)
    {
        if ($data['import_type'] == AsyncImportTaskModel::SALARY_BASE_DATA) {
            $args                     = json_decode($data['args_json'], true);
            $data['type_name']        = empty($args['type_name']) ? '' : $args['type_name'];
            $data['origin_file_name'] = empty($args['file_name']) ? '' : $args['file_name'];
        }
        if ($data['import_type'] == AsyncImportTaskModel::OUTSOURCING_ORDER) {
            $args                     = json_decode($data['args_json'], true);
            $data['origin_file_name'] = empty($args['file_name']) ? '' : $args['file_name'];
        }
        if ($data['import_type'] == AsyncImportTaskModel::COURIER_VEHICLE_INSPECTION) {
            $args                     = json_decode($data['args_json'], true);
            $data['origin_file_name'] = empty($args['file_name']) ? '' : $args['file_name'];
        }
        if ($data['import_type'] == AsyncImportTaskModel::SUSPENSION_AUDIT) {
            $args                     = json_decode($data['args_json'], true);
            $data['origin_file_name'] = empty($args['file_name']) ? '' : $args['file_name'];
        }
    }
    /**
     * 删除任务
     * @throws BusinessException
     */
    public function delete($id, $operatorId)
    {
        $first = AsyncImportTaskModel::findFirst($id);
        if (empty($first)) {
            throw new BusinessException('not  found  task');
        }
        $first->is_deleted  = 1;
        $first->operator_id = $operatorId;
        $first->save();
    }

    /**
     * 获取任务
     * @param $importType
     * @return null
     */
    public function getTask($importType)
    {
        $first = AsyncImportTaskModel::findFirst([
            'conditions' => 'import_type=:import_type: and is_deleted=:is_deleted: and status=:status:',
            'bind'       => [
                'import_type' => $importType,
                'is_deleted'  => 0,
                'status'      => AsyncImportTaskModel::STATE_WAIT_EXECUTE,
            ],
        ]);
        return $first ?: null;
    }

    /**
     * 添加任务
     * @param integer $operatorId 操作用户
     * @param integer $importType
     * @param string $importPath  导入文件url（oss地址）
     * @param array $args 参数
     * @param bool $needArgsCheck 是否需要根据参数过滤数据
     * @return mixed
     * @throws BusinessException
     */
    public function insertTask($operatorId, $importType, $importPath, $args = [], $needArgsCheck = false)
    {
        if (empty($operatorId)) {
            throw new BusinessException('Abnormal operation');
        }
        if (!in_array($importType, AsyncImportTaskModel::$importTypeMap)) {
            throw new BusinessException('Error of import type');
        }
        $conditions = 'import_type=:import_type: and is_deleted=:is_deleted: and status=:status: and operator_id=:operator_id:';
        $bind       = [
            'operator_id' => $operatorId,
            'import_type' => $importType,
            'is_deleted'  => 0,
            'status'      => AsyncImportTaskModel::STATE_WAIT_EXECUTE,
        ];
        if ($needArgsCheck) {
            $conditions        .= ' and args_json=:args_json:';
            $bind['args_json'] = json_encode($args, JSON_UNESCAPED_UNICODE);
        }
        $find = AsyncImportTaskModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
        if ($find) {
            throw new BusinessException(static::$t->_('async_import_task_run'));
        }
        $insert = [
            'operator_id'      => $operatorId,
            'import_type'      => $importType,
            'import_path'      => $importPath,
            'result_file_name' => $this->getResultFileName($importType, $operatorId),
            'args_json'        => json_encode($args, JSON_UNESCAPED_UNICODE),
        ];
        return (new AsyncImportTaskModel)->i_create($insert);
    }

    /**
     * 获取结果文件名
     * @param $importType
     * @param $staffId //用户工号 唯一性
     * @return string
     */
    public function getResultFileName($importType, $staffId = ''): string
    {
        if ($this->customizeResultFileName) {
            return $this->customizeResultFileName;
        }
        $nameList = [
            AsyncImportTaskModel::SCHEDULING_SUGGESTION         => 'Scheduling_suggestions_error_' . date('YmdHis') . $staffId . '.xlsx',
            AsyncImportTaskModel::BATCH_IMPORT_LEAVE            => 'Rest_day_import_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::BATCH_IMPORT_COMPENSATORY_OFF => 'Add_off_day_and_OT_import_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::BATCH_IMPORT_JOB_GRADE        => 'Result_import_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::ATTENDANCE_IMPORT_LEAVE       => 'attendance_import_leave_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::ATTENDANCE_IMPORT_OT          => 'attendance_import_ot_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::ATTENDANCE_IMPORT_REISSUE     => 'attendance_import_makeup_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::ATTENDANCE_IMPORT_FULL_PRISE  => 'attendance_import_full_prise_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::STAFF_FACE_BLACKLIST          => 'Blackface_list_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::STAFF_INFO_BATCH_UPDATE       => 'staff_batch_update_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::PAYROLL_FILE_IMPORT_2316      => 'payroll_file_1316_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::OUT_SOURCING_BLACKLIST_IMPORT => 'out_sourcing_blacklist_import_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::CERTIFICATE_IMPORT_HOUR       => 'certificate_import_hour_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::WORKDAY_SETTING               => 'rotating_rest_error_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::STAFF_INFO_BATCH_UPDATE_JOB   => 'staff_batch_update_job_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::COURIER_VEHICLE_INSPECTION    => 'courier_vehicle_check_result_' . date('YmdHis') . $staffId  . '.xlsx',
            AsyncImportTaskModel::SUSPENSION_AUDIT              => 'upload_nte_result_' . date('YmdHis') . $staffId  . '.xlsx',
        ];
        return $nameList[$importType] ?? '';
    }

    /**
     * @description 校验导入行数
     * @param $importPath
     * @param int $importMaxRowNum
     * @throws BusinessException
     */
    public function uploadCheck($importPath, int $importMaxRowNum = 1000)
    {
        $tmpDir      = sys_get_temp_dir();                // 获取系统的临时目录路径
        $fileName     = basename($importPath);             // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;         // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($importPath))) {
            throw new BusinessException('System error');
        }
        $config       = ['path' => dirname($tmpFilePath)];
        $fileRealName = basename($tmpFilePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet();
        $excelData = $excel->getSheetData();
        if (!isset($excelData[1])) {
            throw new BusinessException('Please set the date header of the second row');
        }

        // 校验导入行数
        if ($this->checkIsImportDataBeyondMaxLineCount($excelData, $importMaxRowNum, 2)) {
            throw new BusinessException(static::$t->_('error_msg_over_line_limit_num', [
                'line_limit_num' => $importMaxRowNum,
            ]));
        }
        @unlink($tmpFilePath);;
    }
}
