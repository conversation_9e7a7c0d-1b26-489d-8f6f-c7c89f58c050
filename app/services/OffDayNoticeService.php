<?php


namespace app\services;

use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\DateHelper;
use App\Library\Enums\ApprovalEnums;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Models\backyard\AuditApprovalModel;
use App\Models\backyard\AuditLogModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrPenaltyAppeal;
use App\Models\backyard\HrPenaltyDetailModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\SysStoreModel;
use Exception;
use OSS\OssClient;

/**
 *
 */
class OffDayNoticeService extends BaseService
{
    //接受邮件的指定职位
    const EMAIL_JOB_TITLES = [16, 269, 1491, 79, 1485, 1427, 1428, 1461, 1453, 1659];
    //附件第一行
    const EXCEL_TITLE = 'Rules:
1) [Original Rest Day] must have no rest days in the natural week or had attended to work on rest day;
2) [Original Rest Day] must be within the current salary cycle;
3) [Original Rest Day] can be canceled, but it needs to meet the conditions: i) the employee has clocked in and clocked out on the day and the punch time is longer than 7.5 hours; ii) the employee has not applied for OT;
4) [Replacement New Rest Day] must after the natural week of the [Original Rest Day];
5) Before system put [Replacement New replacement rest], the normal rest day of the week must be set, and maximum 2 rest days (including new replacement rest day) can be put in the week;
6) [Replacement New Rest Day] only in the current salary cycle and after;
7) [Replacement New Rest Day] cannot be a public holiday, and there is no leave record on that day.

*Important: please follow the principle of payroll cut off date, assume today is 24th and onwards, please do not put rest day adjustments for date 23rd and before.
*Important: This template is only for 6-day shift employees to process a large number of rest day freeze rest day during the big promotion period, and other individual employee rest day adjustments should be processed individually.
*Important: Please double-check that the employee ID is correct, and please follow the format of YYYY-MM-DD for the date.';

    /**
     * 获取指定时间范围未配置休息日的员工
     * @param $start_date
     * @param $end_date
     * @param $dep_ids
     * @param string $columns
     * @return mixed
     */
    protected function getSetOffDayNeedStaffs(
        $start_date,
        $end_date,
        $dep_ids,
        string $columns = 'i.staff_info_id,i.name,i.sys_store_id,i.manger,store.name as store_name'
    ) {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['i' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'i.sys_store_id = store.id', 'store');
        $builder->leftJoin(HrJobTitleModel::class, 'i.job_title = jt.id', 'jt');
        $builder->inWhere('i.node_department_id', $dep_ids);
        $builder->andWhere('i.hire_date <= :hire_date:', ['hire_date' => date('Y-m-d H:i:s', strtotime($start_date))]);
        $builder->andWhere('(i.state in ({state:array}) or (i.state = :leave_state: and i.leave_date >= :leave_date: )) and i.week_working_day = :week_working_day: and i.is_sub_staff = :is_sub_staff: and i.formal in ({formal:array})',
            [
                'state'            => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
                'leave_state'      => HrStaffInfoModel::STATE_RESIGN,
                'leave_date'       => date('Y-m-d H:i:s', strtotime($start_date .' +1 week')),
                'week_working_day' => HrStaffInfoModel::WEEK_WORKING_DAY_6,
                'is_sub_staff'     => 0,
                'formal'           => [HrStaffInfoModel::FORMAL_EDITING, HrStaffInfoModel::FORMAL_INTERN],
            ]);
        $builder->andWhere("NOT EXISTS (SELECT w.staff_info_id FROM App\Models\backyard\HrStaffWorkDaysModel as w WHERE
                w.staff_info_id = i.staff_info_id AND (w.src_week =:src_week: OR ( w.date_at >= :start_date: AND w.date_at <= :end_date: AND w.src_week = 0 ))) ",
            ['src_week' => date('oW', strtotime($start_date)), 'start_date' => $start_date, 'end_date' => $end_date]);

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据部门和职位获取工号
     * @param $dep_ids
     * @param $job_titles
     * @return mixed
     */
    protected function getStaffByDepartmentIdAndJobTitle($dep_ids, $job_titles)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('i.staff_info_id,i.email,i.job_title,i.sys_store_id');
        $builder->from(['i' => HrStaffInfoModel::class]);
        $builder->inWhere('i.node_department_id', $dep_ids);
        if (RUNTIME == 'dev') {
             //$builder->inWhere('i.staff_info_id', [120165]);
        }
        $builder->inWhere('i.job_title', $job_titles);
        $builder->andWhere("state in ({state:array}) and is_sub_staff = :is_sub_staff: and formal in ({formal:array}) and email is not null and email != '' ",
            [
                'state'        => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
                'is_sub_staff' => 0,
                'formal'       => [HrStaffInfoModel::FORMAL_EDITING, HrStaffInfoModel::FORMAL_INTERN],
            ]);
        $managers = $builder->getQuery()->execute()->toArray();

        $dataPermissionService = new DataPermissionService();
        $attendanceTool        = new AttendanceToolService();
        foreach ($managers as &$manager) {
            if ($manager['job_title'] == '16') {
                $manager['staffs'] = $attendanceTool->findStaffIds(['store_id' => [$manager['sys_store_id']]]);
            } else {
                $manager['staffs'] = $dataPermissionService->manageAllStaff($manager['staff_info_id']);
            }
        }
        return $managers;
    }

    /**
     * 获取离职的上级
     * @param $superiors
     * @return array
     */
    public function getNotOnlineSuperiorsIds($superiors): array
    {
        if(empty($superiors)){
            return [];
        }
        $notOnlineSuperiors = HrStaffInfoModel::find([
            'conditions' => ' staff_info_id in ({staff_info_id:array}) and state = :state: ',
            'bind'       => [
                'staff_info_id' => $superiors,
                'state'         => HrStaffInfoModel::STATE_RESIGN,
            ],
            'columns'    => 'staff_info_id,state',
        ])->toArray();
        return array_column($notOnlineSuperiors, 'staff_info_id');
    }

    /**
     * 整理发送信息
     * @param array $superiorAndStaff
     * @param string $titleKey
     * @param string $contentKey
     * @return true
     */
    protected function extracted(array $superiorAndStaff, string $titleKey, string $contentKey): bool
    {
        $superiors             = array_keys($superiorAndStaff);
        $notOnlineSuperiorsArr = $this->getNotOnlineSuperiorsIds($superiors);

        $messageService        = new MessagesService();
        $staffService          = new StaffService();
        $staffAccountLang      = $staffService->getStaffEquipmentLanguage($superiors);
        foreach ($superiorAndStaff as $superior => $staffs) {
            //上级离职了，不发
            if (in_array($superior, $notOnlineSuperiorsArr)) {
                continue;
            }

            //获取最近的语言
            $t                 = BaseService::getTranslation($staffAccountLang[$superior]);
            $_staff_info_id    = $t->_('staff_info_id');
            $_name             = $t->_('name');
            $_biaodian_fen_hao = $t->_('biaodian_fen_hao');//分号
            $_biaodian_mao_hao = $t->_('biaodian_mao_hao');//冒号
            $content           = "<div style='font-size: 40px; margin: 0 20px 0 20px '>";
            $content           .= $t->_($contentKey) . "<br>";
            foreach ($staffs as $staff) {
                $content .= $_staff_info_id . "$_biaodian_mao_hao" . $staff['staff_info_id'] . "$_biaodian_fen_hao" . $_name . "$_biaodian_mao_hao" . $staff['name'] . "<br>";
            }
            $content .= "</div>";
            //发送消息
            $add_message_param = [
                'staff_info_ids_str' => $superior,
                'staff_users'        => [['id' => $superior]],
                'message_title'      => $t->_($titleKey),
                'message_content'    => $content,
            ];

            $messageService->add_kit_message($add_message_param);
        }
        return true;
    }

    /**
     * 每周一
     * 给上级发送消息
     * 提醒未给下属当周设置休息日
     * @throws BusinessException
     */
    public function notSetCurrentWeek($date_at): bool
    {
        //获取当前周的第几天 周日是 0 周一 到周六是 1 -6
        if (RUNTIME == 'pro' && date("w", strtotime($date_at)) != 1) {
            throw new BusinessException('not Monday !');
        }
        //获取本周的开始和截止时间
        [$week_start, $week_end] = Datehelper::getThisWeekMondaySunday($date_at);

        //获取除去节假日之外的本周的已经设置了轮休工号
        $condition = ' date_at >= :week_start: AND date_at <= :week_end: ';
        $bind      = [
            'week_start' => $week_start,
            'week_end'   => $week_end,
        ];
        $this->logger->info(['bind' => $bind]);

        $workDayModel      = HrStaffWorkDaysModel::find([
            'conditions' => $condition,
            'bind'       => $bind,
            'columns'    => 'staff_info_id',
            'group'      => 'staff_info_id',
        ]);
        $issetOffDayStaffs = [];
        if ($workDayModel) {
            $issetOffDayStaffs = array_column($workDayModel->toArray(), 'staff_info_id');
        }

        $weekWorkDay6Staffs = HrStaffInfoModel::find([
            'conditions' => ' state = :state: and week_working_day in ({week_working_day:array}) and is_sub_staff = :is_sub_staff: and formal in ({formal:array}) and rest_type = :rest_type:',
            'bind'       => [
                'state'            => 1,
                'rest_type'        => HrStaffInfoModel::REST_TYPE_1,
                'week_working_day' => [HrStaffInfoModel::WEEK_WORKING_DAY_5, HrStaffInfoModel::WEEK_WORKING_DAY_6],
                'is_sub_staff'     => 0,
                'formal'           => [1, 4],
            ],
            'columns'    => 'staff_info_id,name,manger',
        ]);
        if (!$weekWorkDay6Staffs) {
            throw new BusinessException('not find data');
        }
        $weekWorkDay6StaffsInit = $weekWorkDay6Staffs->toArray();
        $weekWorkDay6Staffs     = array_column($weekWorkDay6StaffsInit, 'name', 'staff_info_id');

        //获取打卡白名单
        $white_list = (new AttendanceWhiteListService())->getAllCycleWhiteList([
            'start_date' => $week_start,
            'end_date'   => $week_end,
        ]);
        $staff1     = $white_list['type_paid_locally'] ?? [];     // 当地发新不打卡白名单
        $staff2     = $white_list['type_not_paid_locally'] ?? []; // 不在当地发新不打卡白名单


        //获取未设置的轮休的全部员工的工号 = 在职的6天班的员工  与 已设置的工号 diff  再于  两个白名单的工号  diff

        $withoutStaffs       = array_merge($staff1, $staff2, $issetOffDayStaffs);
        $needSendNoticeStaff = array_diff(array_keys($weekWorkDay6Staffs), $withoutStaffs);
        //构建主管和下属的数组
        $superiorAndStaff = [];
        foreach ($weekWorkDay6StaffsInit as $item) {
            if (in_array($item['staff_info_id'], $needSendNoticeStaff)) {
                $superiorAndStaff[$item['manger']][] = $item;
            }
        }
        return $this->extracted($superiorAndStaff, 'off_day_setting_reminder_title', 'off_day_setting_reminder');
    }


    /**
     * 每周一9:00
     * 校验Network Management[316]部门及其子部门的6天班员工是否在上个自然周无休息日（不含新调休日）且当周未标记调休，
     * 给直线上级发送BY系统消息
     * @param $date_at
     * @param $dep_id
     * @return true
     * @throws BusinessException
     */
    public function notSetBeforeWeek($date_at, $dep_id): bool
    {
        //获取当前周的第几天 周日是 0 周一 到周六是 1 -6
        if (RUNTIME == 'pro' && date("w", strtotime($date_at)) != 1) {
            throw new BusinessException('not Monday !');
        }
        $dep_ids = (new SysDepartmentService())->getDepartmentAndSubDepartmentIds($dep_id);
        //获取本周的开始和截止时间
        [$week_start, $week_end] = Datehelper::getThisWeekMondaySunday($date_at);
        $staffs = $this->getSetOffDayNeedStaffs($week_start, $week_end, $dep_ids);
        if (empty($staffs)) {
            return true;
        }
        $superiorAndStaff = [];
        foreach ($staffs as $item) {
            $superiorAndStaff[$item['manger']][] = $item;
        }

        return $this->extracted($superiorAndStaff, 'off_day_setting_reminder_title',
            'before_week_off_day_setting_reminder');
    }


    /**
     */
    protected function logicEmail(&$managers, $start_date, $end_date, array $dep_ids)
    {
        //不要调整这个 导出的excel会用这个
        $columns = "store.name as store_name,i.staff_info_id,jt.job_name,'" . $start_date . "' as stat_start,null as new_rest_day";
        $staffs  = $this->getSetOffDayNeedStaffs($start_date, $end_date, $dep_ids, $columns);

        foreach ($managers as &$manager) {
            foreach ($staffs as $staff) {
                if (in_array($staff['staff_info_id'], $manager['staffs'])) {
                    $manager['data'][] = $staff;
                }
            }
        }
        return $managers;
    }

    /**
     * 生成文件、发送邮件
     * @param $data
     * @param $title
     * @param $content
     * @return bool
     */
    protected function makeExcelAndSendEmail($data, $title, $content): bool
    {
        foreach ($data as $datum) {
            if (empty($datum['data'])) {
                $this->logger->info(['data' => $datum, 'makeExcelAndSendEmail' => 'data empty']);
                continue;
            }
            $excel         = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
            $fileName      = 'List of employees without rest day.xlsx';
            $excel         = $excel->fileName($fileName);
            $format        = new \Vtiful\Kernel\Format($excel->getHandle());
            $wrapStyle     = $format->wrap()->toResource();
            $datum['data'] = array_merge([
                [self::EXCEL_TITLE, '', '', '', ''],
                ['Branch Name', 'Staff ID', 'Position', 'Origin Rest Day', 'Replacement New Rest Day'],
            ], $datum['data']);
            foreach ($datum['data'] as $row => $rowItem) {
                $rowItem = array_values($rowItem);
                foreach ($rowItem as $column => $colItem) {
                    if ($row == 0) {
                        if ($column == 0) {
                            $excel->mergeCells('A1:E1', $colItem);
                            $excel->setRow('A1:E1', 280, $wrapStyle);
                            $excel->setColumn('A:E', 25);
                        } else {
                            break;
                        }
                    } else {
                        if ($row == 1) {
                            $excel->insertText($row, $column, $colItem);
                        } else {
                            if ($column == 3) {
                                $excel->insertDate($row, $column, strtotime($colItem), 'yyyy-mm-dd');
                            } else {
                                $excel->insertText($row, $column, $colItem);
                            }
                        }
                    }
                }
            }
            $filePath = $excel->output();
            $tmp      = '';
            if (RUNTIME == 'dev') {
                $tmp            = " (" . $datum['staff_info_id'] . ")";
                $datum['email'] = ['<EMAIL>', '<EMAIL>','<EMAIL>'];
            }
            try {
                (new \App\Library\Mailer((new BiMail)->initConfig()))->send($datum['email'], $title . $tmp, $content,
                    [$filePath]);
            } catch (Exception $e) {
                $this->logger->notice(['data' => $datum, 'makeExcelAndSendEmail' => 'send email fail '.$e->getMessage()]);
            }
        }
        return true;
    }


    /**
     * 每周一 9点发送上周的
     * 未设置休息日的的员工给指定职位
     * @param $date_at
     * @param $dep_id
     * @param array $job_titles
     * @return void
     * @throws BusinessException
     */
    public function notSetBeforeWeekEmail($date_at, $dep_id, array $job_titles)
    {
        //获取部门及子部门
        $dep_ids = (new SysDepartmentService())->getDepartmentAndSubDepartmentIds($dep_id);
        //获取指定日期的周一和周日
        [$start_date, $end_date] = Datehelper::getThisWeekMondaySunday($date_at);
        //获取指定部门、指定职位的员工
        $managers = $this->getStaffByDepartmentIdAndJobTitle($dep_ids, $job_titles);

        if (empty($managers)) {
            throw new BusinessException('接收人为空！');
        }
        //获取邮件发送所需数据
        $this->logicEmail($managers, $start_date, $end_date, $dep_ids);

        $start_date_timestamp = date('j/n', strtotime($start_date));
        $end_date_timestamp   = date('j/n', strtotime($end_date));
        $t                    = self::getTranslation('en');
        $title                = $t->_('list_of_employees_without_rest_day_title',
            ['start_date' => $start_date_timestamp, 'end_date' => $end_date_timestamp]);
        $content              = $t->_('list_of_employees_without_rest_day_content',
            ['start_date' => $start_date_timestamp, 'end_date' => $end_date_timestamp]);
        //发送邮件

        $this->makeExcelAndSendEmail($managers, $title, $content);
    }


    /**
     * 每月24号9点发送前一个计薪周期的
     * 未设置休息日的的员工给指定职位
     * @param $date_at
     * @param $dep_id
     * @param array $job_titles
     * @return void
     * @throws BusinessException
     */
    public function notSetBeforeSalaryPeriodEmail($date_at, $dep_id, array $job_titles)
    {
        //上个月24 到本月23
        $cycle_start = date('Y-m-24', strtotime(date('Y-m', strtotime($date_at)) . ' last month'));
        $cycle_end   = date('Y-m-23', strtotime(date('Y-m', strtotime($date_at))));
        $dateList    = DateHelper::getLimitTimeMondaySunday($cycle_start, $cycle_end);

        $dep_ids  = (new SysDepartmentService())->getDepartmentAndSubDepartmentIds($dep_id);
        $managers = $this->getStaffByDepartmentIdAndJobTitle($dep_ids, $job_titles);
        if (empty($managers)) {
            throw new BusinessException('接收人为空！');
        }
        foreach ($dateList as $date) {
            [$start_date, $end_date] = $date;
            $this->logicEmail($managers, $start_date, $end_date, $dep_ids);
        }
        $start_date_timestamp = date('j/n', strtotime($cycle_start));
        $end_date_timestamp   = date('j/n', strtotime($cycle_end));
        $t                    = self::getTranslation('en');
        $title                = $t->_('month_list_of_employees_without_rest_day_title',
            ['start_date' => $start_date_timestamp, 'end_date' => $end_date_timestamp]);
        $content              = $t->_('month_list_of_employees_without_rest_day_content',
            ['start_date' => $start_date_timestamp, 'end_date' => $end_date_timestamp]);
        $this->makeExcelAndSendEmail($managers, $title, $content);
    }


}
