<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrDataBackupModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\RemoveHrStaffInfoModel;

class SettingEnvService extends BaseService
{
    public static $s_v_param = ['code' => 'StrLenGe:1', 'remark' => 'StrLenGeLe:0,255'];
    public static $remove_hr_code = 'probation_staff'; //转正评估白名单

    public static $no_check_attendance_pieces = ['code'=>'no_check_attendance_piece_config', 'remark' => '不检验打卡地点的片区名单'];
    //本国独有的配置项
    /**
     * @deprecated
     * @var array
     */
    protected $country_key_list = [];

    /**
     * @deprecated
     * 能给前端编辑的KEY
     */
    protected  $common_key_list = [
        'WinHR' => [
            'send_offer_white_credentials'               => '发送offer 薪资审批 证件号 白名单',
            'salary_permission_staff'                    => '薪资结构工号',
            'salary_permission_roles'                    => '薪资结构角色',
            'resume_appendix_staff'                      => '简历-附件工号',
            'resume_appendix_roles'                      => '简历-附件角色',
            'expected_salary_staff'                      => '期望薪资、当前薪资 工号',
            'expected_salary_roles'                      => '期望薪资、当前薪资 角色',
            'allow_edit_hc_manager_roleids'              => '允许修改hc负责人的角色(多个用英文逗号隔开）',
            'allow_edit_hc_manager_staffids'             => '允许修改hc负责人的工号(多个用英文逗号隔开）',
            'get_hc_manager_by_dept_ids'                 => '设置获取hc负责人的部门条件（ta部门ID，获取该部门以及子部门下所有部门的员工）',
            'get_hc_manager_by_roles'                    => '设置获取hc负责人的角色条件',
            'hc_manager_store_type_dept_ids'             => '设置hc招聘负责人-管辖网点类型的部门ID（显示Head Office/Onsite选项的部门）',
            'registration_link'                          => '简历登记link',
            // https://interview.flashexpress.com/#/resume
            'flash_express_co_ltd'                       => '企业官网',
            // https://www.flashexpress.co.th/
            'interviewer_black_list'                     => '面试官岗位ID排除名单',
            //13,37,97,98,106,110,155,271,300,452,473,474,545
            //'recruit_channel' => '招聘渠道ID', //13,37,97,98,106,110,155,271,300,452,473,474,545
            'my_department_submitter'                    => 'MY简历提供人-部门ID-白名单',
            'resume_attched_permission'                  => '已发offer列表-offer附件操控权限',
            'bg_attched_permission'                      => '已发offer列表-背调附件操控权限',
            // 薪资审批按钮权限配置 start
            'salary_offer_departments'                   => '申请薪资审批部门ids配置',
            'salary_offer_staffs'                        => '申请薪资审批工号ids配置',
            // 薪资审批按钮权限配置  end
            // 快递业务线部门
            'express_line_departments'                   => '快递业务线',
            // 一线职位快递
            'first_line_jobs'                            => '一线职位快递',
            'front_line_employee_jobids'                 => '一线员工职位id列表',
            // winhr 申请薪资审批公司列表
            'salary_approve_company_list'                => '薪资审批公司名称列表，英文|号隔开',
            'offer_signature_person'                     => 'offer签字-审批人员',
            'offer_sign_approval_role_type_1'            => 'offer签字审批角色1 pdf_type= 2、4(17758)',
            'offer_sign_approval_role_type_2'            => 'offer签字审批角色1 pdf_type= 1、3(56780)',
            'winhr_contract_import_tpl'                  => 'Excel导入合同模板',
            //'winhr_ta_department_ids' => 'TA部门ID',
            'interviewerJobIds'                          => '面试官角色ID',
            'car_supplement_hc_jd'                       => '发送offer-车补-jd',
            'talent_acquisition_department_id'           => 'Talent Acquisition部门ID',
            'customer_resume_h5_ananex'                  => '客服简历h5附件采集链接',
            'customer_resume_staff_info_id'              => '客服完善简历分配客服id',
            'customer_resume_job_id'                     => '客服完善简历职位配置',
            'hclist_edit_permission_staffids'            => 'HC管理-HC列表-编辑-权限配置-工号',
            'hclist_edit_permission_roles'               => 'HC管理-HC列表-编辑-权限配置-角色',
            'resume_remove_permission_staffids'          => '人才库/简历管理-简历信息清除-权限配置-工号',
            'resume_remove_permission_roles'             => '人才库/简历管理-简历信息清除-权限配置-角色',
            'interview_button_permission_staffids'       => '面试管理-权限配置-工号',
            'interview_button_permission_roles'          => '面试管理-权限配置-角色',
            'edit_resume_recruiter_staff_id'             => '配置可以修改招聘负责人工号',
            'ph_contract_courier_job_title'              => '菲律宾电子合同-快递员职位ID',
            'winhr_hc_all_permissions_staffs'            => '配置hc全部权限的员工ID',
            'batch_hc_manager_staff_ids'                 => '批量操作hc权限-按照员工配置',
            'batch_hc_manager_job_title_ids'             => '批量操作hc权限-按照职位配置',
            'batch_hc_manager_departmentids'             => '批量操作hc权限-按照部门配置',
            'selected_sp_store_ids'                      => 'selected sp',
            'ms_store_ids'                               => 'ms1 & ms3',
            'winhr_staff_role_ids'                       => '查看全部简历-按照角色配置',
            'winhr_contract_frontline_position'          => '电子合同一线职位配置',
            'winhr_del_resume_offer_send_email'          => 'winhr支持快速返聘已离职员工邮件地址',
            'winhr_first_line_jd_ids'                    => '网络一线JD岗位ID配置',
            'first_line_contract_staff_ids'              => '一线员工合同内容',
            'head_office_contract_staff_ids'             => '非一线员工合同内容',
            'managed_employees_contracts_staff_ids'      => '可查看管辖范围内的员工的合同-按工号配置，英文逗号隔开',
            'contract_week_working_department'           => '合同-工作时长-部门id配置',
            'create_resume_verify'                       => '创建简历时校验开关-N代表关，Y代表开',
            'non_recommend_resume_ids'                   => '不使用简历筛选的职位,部门id|职位id 英文逗号分隔',
            'replace_hc_jobids'                          => '发offer时可互相更换HC的职位|配置职位ID，若多个用英文逗号隔开',
            'replace_hc_hire_type'                       => '发offer时可更换HC的雇佣类型|配置雇佣类型ID，若多个用英文逗号隔开',
            'replace_hc_staff_ids'                       => '发offer时可更换HC的工号|配置工号，多个用英文逗号隔开',
            'onboarding_default_roles'                   => 'winhr-入职表单-角色默认,格式：职位ID,职位ID|角色ID,角色ID#职位ID|角色ID',
            'micro_h5_jdid'                              => 'H5微官网可投递的职位',
            'refresh_resume_information_for_reentry_ids' => '可在WHR投递记录操作返聘的工号',
            'abnormal_transfer_send_email'               => '异常转岗合同通知邮件地址，逗号分割',
            'hrlist_edit_demand_num_staffids'            => '配置增加HC需求人数的权限的工号(英文逗号分割)',
            'hc_appaly_id'                               => '可申请HC的工号(英文逗号分割)',
            'edit_hire_type_staff'                       => '办理入职可编辑雇佣类型的工号(英文逗号分割)',

        ],
        'Backyard' => [
            'live_list'                          => '部分员工 参与静默活体检测 1是全部开启',
            'oss_fle_url'                        => '图片地址推送fle dev01-id-svc.fex.pub/fle-svc/com.flashexpress.fle.svc.api.OssSvc',
            'ai_face_host'                       => 'ai 人脸识别接口地址',
            'ai_live_host'                       => 'ai 静默活体接口地址',
            'alive_score'                        => '打卡 静默活体分数值',
            'ai_mask_score'                      => 'ai 口罩识别分数 上线时候先配置-10 上全量',
            'ai_mask_switch'                     => 'ai 口罩识别开关 0关 1开',
            'ai_mask_host'                       => 'ai 口罩识别接口地址 https://iai-training.flashsoftapi.com/v1/analyze-face',
            'by_stop_or_leave'                   => '员工未回款停职开关 1:不检查回款 0:检查回款',
            'OT_AREA'                            => 'ot 1.5倍 开通的区域配置',
            'ot_ignore_store'                    => 'ot1.5倍放开8小时限制网点',
            'miles_job_title'                    => '需要上报里程的职位id',
//            'store_temporary_coordinate_new'     => 'new 网点临时考勤坐标store_temporary:coordinates 98.434823,19.349259,TH55010101|',
            'special_job_title'                  => '需要查找管辖范围的打卡特殊职位配置(职位id)',
            'leave_25_list'                      => '新冠隔离假可申请名单',
            'leave_26_list'                      => '新冠治疗假可申请名单',
            'rmq_pushup_test'                    => 'push测试人员 16966,16997',
            'send_message_replace_cpo_staff'     => '消息审批流cpo 替换工号',
            'hc_share_position'                  => 'hc职位共享职位',
            'hc_share_department'                => 'hc职位共享部门',
            'accident_report_acceptor_config'    => '事故上报处理人配置',
            'accident_report_ccto_config'        => '事故上报抄送人配置',
            'accident_report_config'             => '事故上报初始配置',
            'accident_report_subtask_config'     => '事故上报子任务配置',
            'underlying_staff_job_title'         => '可申请加班费以及计算迟到早退职位',
            'is_check_ticket_pickup'             => '是否验证快递员揽收任务 1:验证 0:不验证',
            'by_recommender_resume_jd'           => 'by网点主管推荐简历-期待职位',
            'by_recommender_resume_jd_kd'        => 'by网点主管推荐简历-期待职位-快递职位',
            'by_recommender_resume_jd_other'     => 'by-推荐简历-期待职位-热门内推职位',
            'by_recommender_resume_positions'    => 'by网点主管推荐简历-入口权限',
            'hc_cc_by_hrbp_staff'                => 'HC通过后在hc_cc_by_hrbp_dep部门里抄送工号,默认 70782',
            'hc_cc_by_hrbp_dep'                  => 'HC通过后会触发抄送部门id 包含子部门(默认30001, 60001, 50001)',
            'hc_cc_by_hrbp_staff_1'              => 'HC通过后在hc_cc_by_hrbp_dep部门里抄送工号第2',
            'hc_cc_by_hrbp_dep_1'                => 'HC通过后会触发抄送部门id包含子部门第2',
            'hc_cc_by_hrbp_staff_2'              => 'HC通过后在hc_cc_by_hrbp_dep部门里抄送工号第3',
            'hc_cc_by_hrbp_dep_2'                => 'HC通过后会触发抄送部门id包含子部门第4',
            'hc_cc_by_hrbp_staff_3'              => 'HC通过后在hc_cc_by_hrbp_dep部门里抄送工号第4',
            'hc_cc_by_hrbp_dep_3'                => 'HC通过后会触发抄送部门id包含子部门第4',
            'hc_cc_by_hrbp_staff_4'              => 'HC通过后在hc_cc_by_hrbp_dep部门里抄送工号第5',
            'hc_cc_by_hrbp_dep_4'                => 'HC通过后会触发抄送部门id包含子部门第5',
            'validation_oil_card'                => '车辆信息：油卡公司-Shell-是否需要验证 0：无 1：必须',
            'close_entrance_jobtitle_ids'        => '关闭出差以及外出申请入口权限的职位ID',
            'is_check_finished_study'            => '培训系统限制打卡 0：不限制 1：限制',
            'company_regulatory_framework'       => '企业规章制度',
            'osm_app_last_version'               => 'Osm-app最新版本，是否强更（0推荐更新,1强追更新）',
            'my_tp1_staffids'                    => '配置显示tp1入口的工号',
            'sub_staff_support_switch'           => '支援期间子账号功能是否打开0关闭,1打开',
            'entry_confirm_roles'                => '有到岗确认权限的角色',
            'entry_confirm_job_title'            => '有到岗确认权限的职位',
            'resignation_notice_first_line_jobs' => '离职通知期 一线职位ID',
            'package_confirm_permission'         => '包材管理员权限配置校验',
            'onsite_rules_date'                  => 'onsite补贴规则及生效日期(年-月-日|揽件量大于等于的值)',
            'facial_recognition_mailbox'         => '人脸识别反馈渠道（支持邮箱、line群、微信群等）',
            'notice_black_list'                  => '不接收转岗消息员工工号(多个用英文逗号隔开)',
            'by_ui_version'                      => 'by前端UI版本号',
            'is_check_has_resign_assets'         => '打卡是否验证未处理完成的资产 0不验证 1验证',
            'check_has_resign_assets_job_title'  => '验证未处理完成资产职位',
            'shop_free_manage_region_ids'        => '员工商城非免费工服大区id配置，使用英文逗号分隔',
            'interior_goods_time_range_settings_money' =>'员工商城无头件配置下单金额范围(日期中划线连接)',
            'interior_goods_bank_list'                   => '员工商城账户信息',
            'attendance_remind'                          => '打卡提醒时间点，单位分钟，最多逗号隔开3个值',
            'auto_interior_orders_department_ids'        => '自动下单工服部门ID(英文逗号分隔)',
            'auto_interior_orders_job_ids'               => '自动下单工服职位ID(英文逗号分隔)',
            'auto_interior_orders_department_id_spu_ids' => '查看全量工服尺码人员ID配置',
            'all_interior_orders_entry_ids'              => '查看全量工服尺码人员ID配置',
            'batch_interior_orders_entry_day'            => '批量下发工服入职天数',
            'all_batch_interior_orders_entry_ids'        => '查看全量批量下单工服人员ID',
            'interior_entry_days'                        => '可购买大于1件免费工服天数限制',
            'interior_refund_reminder_emails' => '员工商城退款提醒邮箱,可以配置多个邮箱，每个邮箱用,号分隔',
            'interior_free_staff_formal'                 => '免费工服员工formal类型,可以配置多个，用英文逗号号分隔',
            'interior_free_department_set'               => '总计免费工服件数配置',
            'interior_goods_scm_stock_ids'               => '员工商城分仓配置，用于配置不同产品类型的出库仓库ID，1工服2无头件值为仓ID',
            'interior_non_sale_goods_id'                 => '非售卖商品的ID',
            'interior_deposit_handling_fee'              => '押金手续费',
            'interior_goods_auto_price_email'            => '员工商城-自动定价、下架邮件组，多个逗号分隔',
            'interior_order_forbidden_store_category'    => '员工商城收货地址禁用网点类型：用于配置不可作为收货地址的网点类型，支持配置多个，使用,号分隔',
            'interior_order_forbidden_store_name'        => '员工商城收货地址禁用网点名称：用于配置不可作为收货地址的网点名称，支持配置多个，使用,号分隔',
            'interior_order_unclaimed_node_sn'           => '无头件成本中心：用于配置无头件的成本中心',
            'hire_change_job_titles'                     => '转个人代理职位',
            'outsourcing_attendance_should_detect_time'  => '外协刷脸校验-间隔时间-单位秒',
            'show_oa_file_folder_staff_ids'              => '开启OA文件夹特殊工号',
            'show_oa_file_folder_roles'                  => '开启OA文件夹角色ID',
            'interior_goods_raincoat_set'                => '员工商城雨衣相关配置信息，SPU-ID组、职位ID组、入职天数、最大免费件数',
            'advance_fuel_job_title_ids'                 => '预支油费允许申请的职位配置(多个用英文逗号隔开)',
            'show_oa_file_folder_job_title'              => 'OA入口权限屏蔽职位的配置',
            'oa_folder_num_show_switch'                  => '是否开启oa小红点数量，默认1 ：1开启 0不开启',
            'max_number_of_staffs_mentored'              => '同时可辅导的最大员工数',
            'workbench_job_title'                        => '显示by工作台入口职位',
            'workbench_sys_department_id'                => '显示by工作台入口部门',
            'edit_password_captcha'                      => '开启设置密码腾讯云验证码，0代表关闭，1代表开启',
            'edit_password_change_otp'                   => '开启忘记密码更换设备短信邮箱验证码，0代表关闭，1代表开启',
            'by_hcm_role'                                => 'BY展示HCM入口的角色(多个用英文逗号隔开)',
            'by_hcm_staff_id'                            => 'BY展示HCM入口的工号(多个用英文逗号隔开)',
            'interior_flash_pay_handling_fee'            => '员工商城-Pay支付手续费(计算商品价值)',
            'interior_flash_pay_qrcode_image_ftl'        => '员工商城-FlashPay扫码付订单支付页图片模板地址',
            'mail_new_goods_not_msg_staff_ids'           => '员工商城上新推送不发送名单(多个用英文逗号隔开)',
            'edit_hc_demand_num_dept_ids'                => 'BY审批详情中，可以调整 Hc 需求人数的部门id，(多个用英文逗号隔开)',
        ],
        'HRIS' => [
            'hr_staff_email_edit'                              => '邮件编辑权限',
            'week_working_day_edit'                            => '根据工号设置【外协员工工作订单】的配置权限，不同配置组只能配置对应职位的订单',
            'outsourcing_edit_a'                               => '快递员 配置组A',
            'outsourcing_edit_b'                               => 'DC仓管 配置组B',
            'outsourcing_edit_c'                               => 'Hub仓管 配置组C',
            'hris_staff_super_admin'                           => 'hris 项目超管自定义角色',
            'edit_hire_date_role'                              => '修改入职日期范围不受限制',
            'edit_hire_date_role_10'                           => '修改入职日期范围10天',
            'pass_reset_role_id'                               => '员工管理等重置密码角色',
            'ManagerLeaveEmail'                                => '直线上级离职发邮件 <EMAIL>,<EMAIL>',
            'probation_staff'                                  => '转正评估白名单',
            'staff_info_download'                              => 'Flash员工管理下载导出权限（工号）',
            'job_grade_download'                               => 'Flash员工管理下载导出权限（职级）',
            'staff_attachment_info_view_roles'                 => '员工附件信息查看权限（角色id）',
            'outsourcing_edit_pwd'                             => '重置外协密码职位权限',
            'assets_manager_staffs'                            => '离职管理-资产处理权限',
            'assets_manager_staffs_department'                 => '离职管理-资产处理权限-部门',
            'assets_manager_staffs_job'                        => '离职管理-资产处理权限-职位',
            'staff_bank_id_list'                               => '员工银行选项数据（配置银行ID,适用与whr、hcm-正式员工）',
            'by_staff_bank_id_list'                            => '员工银行选项数据（配置银行ID,适用、backyard系统）',
            'staff_bank_id_list_independent'                   => '员工银行选项数据（配置银行ID,适用与whr、hcm-个人代理）',
            'by_staff_bank_id_list_independent'                => '员工银行选项数据（配置银行ID,适用、backyard系统-个人代理）',
            'site_allowance_job_ids'                           => '享受区域补贴的职位ID(英文逗号分割)',
            'site_allowance_store_date'                        => '享受区域补贴的网点ID与生效日期(TH0001001/2022-02-02,TH1233444/2022-03-03)',
            'attendance_allowance_job_ids'                     => '享受出勤补贴的职位ID(英文逗号分割)',
            'attendance_allowance_store_date_amount'           => '享受出勤补贴的网点ID与生效日期与补贴金额(TH0001001/2022-02-02/100,)',
            'hris_validate_ticket_pickup'                      => '验证是否有未完成的揽件任务 0不验证1验证',
            'hris_validate_ticket_delivery'                    => '验证是否有未完成的派件任务 0不验证1验证',
            'hris_validate_store_receivable_bill_detail'       => '验证未回款的快递员公款 0不验证1验证',
            'hris_validate_ka_profile'                         => '验证员工绑定了客户0不验证 1验证',
            'hris_validate_store_delivery_barangay_staff_info' => '菲律宾/马来 验证员工已绑定派送码0不验证 1验证',
            'hris_validate_sys_district'                       => '非菲律宾/马来 该员工是片区负责人 0不验证 1验证',
            'hris_validate_store_remittance_bill'              => '验证是否有未向总部汇款项 0不验证 1验证',
            'hris_validate_reserve_fund_apply'                 => '验证备用金是否归还 0不验证1验证',
            'hris_os_leave_date_num'                           => '外协工号离职天数校验',
            'hris_os_leave_reason'                             => '外协工号校验指定离职原因',
            'entry_allowance_store'                            => '入职津贴发放网点',
            'jurisdiction_area_roles'                          => '员工详情中显示"管辖区域"的角色id',
            'jurisdiction_department_roles'                    => '员工详情中显示"管辖部门"的角色id',
            'jurisdiction_region_roles'                        => '员工详情中显示"管辖大区"的角色id',
            'jurisdiction_piece_roles'                         => '员工详情中显示"管辖大区"的角色id',
            'jurisdiction_store_roles'                         => '员工详情中显示"管辖网点"的角色id',
            'jurisdiction_category_roles'                      => '员工详情中显示"管辖网点类型"的角色id',
            'jurisdiction_franchisee_region_roles'             => '员工详情中显示"管辖加盟商大区"的角色id',
            'jurisdiction_franchisee_piece_roles'              => '员工详情中显示"管辖加盟商片区"的角色id',
            'hris_template_send_message'                       => '普通消息、签字消息按模板发送人，多个时以西文逗号分隔',
            'warning_add_all'                                  => '【警告书发送】可向所有人发送警告书的角色id',
            'warning_add_jurisdiction'                         => '【警告书发送】可按数据管辖范围管辖范围发送警告书的角色id',
            'warning_list_all'                                 => '【警告书发送记录】查看所有警告书发送记录的特定角色id',
            'warning_list_jurisdiction'                        => '【警告书发送记录】查看数据管辖范围内警告书发送记录的角色id',
            'report_list_all'                                  => '【举报列表】查看所有举报的特定角色id',
            'report_list_jurisdiction'                         => '【举报列表】查看数据管辖范围内举报的角色id',
            'report_list_qa_qc_roles'                          => '【举报列表】查看QAQC专属举报原因的角色id',
            'only_visible_to_yourself_btn'                     => '问卷消息-【仅自己可见本条消息】权限，工号名单(多个用英文逗号隔开）',
            'leave_manage_data_permission_all'                 => '离职管理 - 可查看所有工号数据权限角色',
            'leave_manage_data_permission_part'                => '离职管理 - 可查看管辖范围工号数据权限角色',
            'view_job_title_grade'                             => '可查看操作记录中职级字段工号(多个用英文逗号隔开）',
            'job_title_vehicle_type'                           => '【正式员工】涉及车辆信息职位ID',
            'outsource_job_title_vehicle_type'                 => '【外协员工】涉及车辆信息职位ID',
            'auto_update_job_grade_position'                   => '职级自动变更涉及的一线职位',
            'staff_info_batch_update_roles'                    => '员工管理-批量更新信息-角色配置',
            'batch_outsourcing_leave_job_edit'                 => '按工号设置此外协员工管理 批量离职',
            'batch_outsourcing_leave_job_upload_file_template' => '外协批量上传离职文件模版',
            'outsourcing_order_check_role_id'                  => '外协工单列表查看权限（角色id，多个用英文逗号隔开）',
            'staff_not_apply_outsourcing_job_title_id'         => '不允许正式员工做外协的职位，不配代表所有职位都允许，配置-1代表所有职位都不允许',
            'staff_information_audit_all_roles'                => '员工信息审核-查看所有数据的角色id',
            'staff_information_audit_all_staff_ids'            => '员工信息审核-查看所有数据的工号',
            'staff_information_audit_jurisdictions_roles'      => '员工信息审核-查看管辖范围数据的角色id',
            'staff_information_audit_jurisdictions_staff_ids'  => '员工信息审核-查看管辖范围数据的工号',
            'msg_not_audit_staff_ids'                          => '发送消息不需要走审批流程的操作人工号 如 2323,333',
            'suspension_file_cc_email'                         => '停职文件发送邮箱-抄送',
            'modify_today_rest'                                => '修改当天轮休，工号以逗号隔开',
            'modify_today_shift'                               => '修改当天班次，工号以逗号隔开',
            'shift_courier_job_title'                          => '排班建议-快递员',
            'shift_dc_job_title'                               => '排班建议-仓管员',
            'shift_no_limited_branch'                          => '不受限制修改班次时间网点名单,逗号隔开',
            'shift_no_limited_type'                            => '不受限制修改班次时间网点类型,逗号隔开',
            'shift_no_limited_department'                      => '不受限制修改班次时间部门,逗号隔开',
            'resignation_to_employment_staffids'               => '允许修改离职变在职的工号(多个用英文逗号隔开）',
            'job_transfer_front_line_position'                 => '转岗一线职位（职位id,多个用英文逗号隔开）',
            'job_transfer_position_across_grade'               => '可跨职级转岗的职位（职位id,多个用英文逗号隔开）',
            'month_edit_shift_num'                             => '每个月可修改员工不同班次次数 默认3次',
            'hris_email'                                       => '待离职提醒邮件中联系邮箱',
            'c_employee'                                       => 'C类员工特殊逻辑，以逗号隔开的工号',
            'outsourcing_edit_long'                            => '配置中的工号可以修改长期外协订单配置',
            'all_questionnaire_permission_staff_id'            => '可查看全部问卷的工号(多个用英文逗号隔开）',
            'outsource_do_setting_shift_job_title_config'      => '外协进入员工班次设置的职位id（英文逗号,分隔）',
            'out_sourcing_blacklist_import_template_url'       => '外协黑名单导入模版url',
            'staff_bank_id_list_outsource'                     => '外协员工银行选项（配置银行ID,适用外协工作订单、外协员工管理）',
            'staff_email_roles'                                => '必填企业邮箱的角色（配置角色id）',
            'email_position'                                   => '自动生成邮箱名称的职位（配置职位id）',
            'hris_entry_email_it'                              => '配置入离职开通关闭企业邮箱通知邮件地址，多个地址之间,分隔',
        ],
        'CRM'      => [
            'crm_white_list' => 'crm 权限白名单',
        ],
        'FBI' => [
            'only_oil_card_job_title'                    => 'FBI车辆管理仅添加油卡信息的职位id',
            'kit_facial_verify_face_check'               => 'kit开启数据安全人脸验证，0代表关闭，1代表开启，默认0',
            'kit_facial_verify_used_device'              => 'kit开启常用设备人脸验证，0代表关闭，1代表开启，默认0',
            'kit_facial_verify_new_wifi'                 => 'kit开启新增WiFi人脸验证，0代表关闭，1代表开启，默认0',
            'kit_facial_verify_token_expiration_date'    => 'kit登录X天后自动过期，需重新登录刷脸。默认值30',
            'kit_facial_verify_captcha'                  => 'kit开启登录腾讯云验证码，0代表关闭，1代表开启，默认0',
            'kit_facial_verify_otp'                      => 'kit当设备原因（无摄像头）无法人脸验证时是否进行OTP验证，0代表关闭验证，1代表开启验证，默认0',
            'kit_facial_verify_position'                 => 'kit需要验证人脸的职位ID，用逗号隔开。仅Kit读取职位列表',
            'kit_facial_verify_whitelist_noverifiy_face' => 'kit无需人脸校验名单，以逗号隔开的工号',
            'kit_anti_cheating_store'                    => 'kit开启防作弊登录的网点ID，逗号隔开; 1全网开启; 0关闭',
            'kit_anti_cheating_staff_id'                 => 'kit防作弊登录白名单',
            'kit_anti_cheating_branchtype'               => 'kit关闭作弊登录的网点类型ID，多个逗号隔开',
        ],
        'SYS' => [
            'hris_url'                     => 'hris接口host',
            'ai_r_c_config'                => 'AI识别车辆登记配置',
            'ai_v_t_c_config'              => 'AI车辆税证识别配置',
            'payroll_backyard_url'         => 'by前端薪资查看登录url',
            'payroll_send_staff_ids'       => '发送工资条按钮权限',
            'hub_full_attendance_store'    => '全勤奖指定网点',
            'hub_full_attendance_position' => '全勤奖职位',
            'staff_absent_days'            => '员工连续旷工N天后停职',
            'staff_leave_days'             => '员工连续旷工N天后离职',
            'notpaid_leave_days'           => '未回公款停职后N天离职',
            'stop_duty_cod_email'          => '未回公款停职发邮件',
            'stop_duty_absent_email'       => '旷工停职发邮件',
            'waiting_resignation_email'    => '待离职员工通知邮件收件人配置',
            'resigned_staff_email'         => '离职通知邮件收件人配置',
        ],
        'OA'       => [
            'hc_budget_summary_staffs'            => 'hc 预算汇总数据权限工号--全部数据',
            'hc_budget_summary_roles'             => 'hc 预算汇总数据权限角色--全部数据',
            'hc_budget_summary_deps'              => 'hc 预算汇总数据权限部门--全部数据',
            'hc_budget_summary_range_roles'       => 'hc 预算汇总数据权限角色--读管辖范围',
            'hc_budget_common_staffs'             => 'hc 预算汇总数据权限--可查看自己所属一级部门和下级部门的预算数据',
            'hc_budget_special_job'               => '组织和公司负责人职位，申请HC预算时，需要走特殊审批流的职位',
            'organization_department_staffs'      => '配置在此处的工号，可查看组织架构全部数据',
            'organization_department_roles'       => '配置在此处的角色ID，访问组织架构需要读取关系范围展示数据',
            'organization_job_title_staffs'       => '配置在此处的工号，可查看职位体系全部数据',
            'organization_job_title_roles'        => '配置在此处的角色ID，访问职位体系需要读取关系访问展示数据',
            'hc_share_department_position'        => 'hc 共享预算配置 (部门|职位,职位#部门|职位,职位)',
            'organization_edit_info_roles_id'     => '[组织架构]配置在此处的角色ID，可编辑组织信息-按管辖范围；非此角色，则可编辑所有',
            'organization_edit_sap_info_roles_id' => '[组织架构]配置在此处的角色ID，可编辑SAP信息-按管辖范围；非此角色，则可编辑所有',
            'show_relevance_store_department_ids' => '关联网点”的组织(含下级)，关联后部门负责人将自动同步至MS网点负责人',
            'c_position'                          => 'C类职位特殊逻辑',

        ],
    ];

    /**
     * 能给前端编辑的KEY
     */
    public static $version_list = [
        'ios' => [
            'IOS_3' => 'IOS_3',
        ],
        'android' => [
            'LIST_ANDROID_3' => '灰度名单',
            'GREY_ANDROID_3' => '灰度版本号',
            'GREY_URL_ANDROID_3' => '灰度下载链接',
            'version_switch' => '灰度开关',
            'ANDROID_3' => '全量版本',
            'ALL_URL_ANDROID_3' => '全量连接',
            'release_note' => '升级提示',
        ],
    ];


    /**
     * @description:
     * 返回指定的
     */
    public function getSettingList($params)
    {
        $data = [];
        $sd   = SettingEnvModel::find()->toArray();
        $sd   = array_column($sd, null, 'code');

        $all_key_list = [];
        foreach ($this->common_key_list as $sys => $item) {
            $all_key_list[$sys] = array_merge($item,$this->country_key_list[$sys]??[]);
        }

        foreach ($all_key_list as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $data[$k][] = [
                    'code'    => $k1,
                    'set_val' => $sd[$k1]['set_val'] ?? '',
                    'remark'  => $sd[$k1]['remark'] ?? $v1,
                ];
            }
        }
        return $data;
    }

    public function getSettingListV2($params)
    {
        $data = [];
        $all_key_list = SettingEnvModel::find(
            [
                'conditions' => 'is_show = :is_show:',
                'bind'       => ['is_show' => 1],
                'columns'    => ['code', 'set_val', 'remark', 'category'],
                'order'      => 'sort asc',
            ]
        )->toArray();

        foreach ($all_key_list as $v) {
            $k          = SettingEnvModel::$category_list[$v['category']];
            $data[$k][] = [
                'code'    => $v['code'],
                'set_val' => $v['set_val'],
                'remark'  => $v['remark'],
            ];
        }
        $result = [];
        $map = SettingEnvModel::$category_list;
        foreach ($map as $category => $name) {
            $result[$name] = $data[$name] ?? [];
        }
        return $result;
    }


    public function getVersionList($params)
    {
       $data = [];
       $sd = SettingEnvModel::find()->toArray();
       $sd = array_column($sd,null,'code');
       foreach (self::$version_list as $k => $v) {
          foreach ($v as $k1 => $v1) {
            $data[$k][] = [
                'code' => $k1 ,
                'set_val' => $sd[$k1]['set_val'] ?? '' ,
                'remark' => $sd[$k1]['remark'] ?? $v1 ,
            ];
          }
       }
       return $data;
    }


    public function listByParam($param)
    {
        if (empty($param['code']) || !is_array($param['code'])) {
            return [];
        }

        return SettingEnvModel::find([
            'conditions' => "code in ({codes:array})",
            'bind'       => ['codes' => $param['code']],
        ])->toArray();
    }

    /**
     * 单个保存env
     */
    public function saveSettingByCode($params)
    {
        $v_key = $params['code'];
        $v_val = $params['set_val'] ?? '';
        $v_remark = $params['remark'] ?? '';
        $data = SettingEnvModel::findFirst(
            [
                'conditions' => 'code = ?1',
                'bind' => [
                    1 => $v_key,
                ],
            ]
        );
        $backup_data = [
            'code'   => $v_key,
            'before' => '',
        ];
        if(empty($data)){
            $data = new SettingEnvModel();
        }else{
            $backup_data['before'] = $data->set_val;
        }

        if (!in_array($v_key, ['xz_ticket_add_power','salary_approve_company_list','prove-download-setting'])) {
            $v_val = str_replace('，',',',$v_val);//中文逗号
            $v_val = str_replace(' ','',$v_val);//空格
            $v_val = trim($v_val);
        }

	    //todo  转正评估白名单
	    if($v_key == self::$remove_hr_code){
		    $params['log_set_val'] = empty($data) ? '' : $data->set_val;
		    $res = (new RemoveHrStaffInfoService)->HrProbationSave($params);
	    }

        if (is_array($v_val)) {
            $v_val = json_encode($v_val, JSON_UNESCAPED_UNICODE);// 行政工单存储json
        }

        $data->code    = $v_key;
        $data->set_val = $v_val;
        $data->remark = $v_remark;
        $result = $data->save();

        $backup_data['after'] = $v_val;

        //备份信息
        $backup = new HrDataBackupModel();
        $backup->table = 'setting_env';
        $backup->content = json_encode($backup_data,JSON_UNESCAPED_UNICODE);
        $backup->traceid = molten_get_traceid();
        $backup->staff_info_id = self::getUserId();
        $backup->save();

        //TODO 待迁移出去
        if($v_key == 'rmq_pushup_test'){
            $d = (new PushService())->svcpush();
        }
        //todo hc招聘负责人 数据部门条件设置变更处理
        if($result && $v_key == 'get_hc_manager_by_dept_ids'){
            $res = (new HcManagerService())->syncHcManagerList($v_key);
        }

        return $result;
    }

    public function batchSave($param){
        if(empty($param))
            return false;
        $flag = false;
        foreach ($param as $v){
            $info = SettingEnvModel::findFirst("code = '{$v['code']}'");
            if(!empty($info)){
                $info->set_val = $v['set_val'];
                $flag = $info->update();
                if(!$flag)
                    break;

            }
        }
        return $flag;

    }


    /**
     * 白名单列表+搜索
     */
    public function wirteList($params)
    {
        try {
            $returnData = [
                'list' => [],
                'total'=> 0,
            ];
            $staff_id = $params['staff_id'];
            $page = intval($params['page']);
            $size = intval($params['size']);
            $offset = $size * ($page - 1);

            $builder = $this->modelsManager->createBuilder();
            $builder->from(RemoveHrStaffInfoModel::class);
            //$builder->columns(['*']);
            $builder->where('staff_info_id = :staff_info_id: ', ['staff_info_id'=>$staff_id]);
            $returnData['total']  = $this->getBuilderTotal($builder);
            if (!$returnData['total']) {
                return  $returnData;
            }
            $builder->limit($size, $offset);
            $builder->orderBy('staff_info_id desc');
            $list = $builder->getQuery()->execute()->toArray();
            $returnData['list']  = $list;
            return  $returnData;
        } catch (\Exception $e) {
            throw  $e;
        }
    }

    /**
     * 白名单添加
     */
    public function wirteListSave($params)
    {
        try {
            $staff_id = $params['staff_id'];
            $remark = $params['remark'];
            $returnData = RemoveHrStaffInfoModel::findFirst(
                [
                    'conditions' => 'staff_info_id = ?1',
                    'bind' => [
                        1 => $staff_id,
                    ],
                ]
                    );
            if(empty($returnData)){
                $m = new RemoveHrStaffInfoModel;
                $m->staff_info_id = $staff_id;
                $m->remark = $remark;
                $returnData = $m->save();
            }

            return  $returnData;
        } catch (\Exception $e) {
            throw  $e;
        }
    }

    /**
     * 白名单删除
     */
    public function wirteListDel($params)
    {
        try {
            $staff_id = $params['staff_id'];
            $returnData = RemoveHrStaffInfoModel::findFirst(
                [
                    'conditions' => 'staff_info_id = ?1',
                    'bind' => [
                        1 => $staff_id,
                    ],
                ]
            )->delete();
            return  $returnData;
        } catch (\Exception $e) {
            throw  $e;
        }
    }

    /**
     * 获取配置项
     * @param $code
     * @param null $separator
     * @return array|false|string|string[]
     */
    public function getSetVal($code, $separator = null)
    {
        if (empty($code)) {
            return false;
        }

        $setting = SettingEnvModel::findFirst([
            'conditions' => "code = :code:",
            'bind'       => ['code' => $code],
            'columns'    => ['set_val'],
        ]);
        if ($separator !== null) {
            return !empty($setting) && $setting->set_val ? explode($separator, $setting->set_val) : [];
        }
        return !empty($setting) ? $setting->set_val : '';
    }
    /**
     * 获取多个配置项
     * @param array $codes
     * @return array
     */
    public function listByCode(array $codes = []): array
    {
        if(empty($codes) || !is_array($codes))
            return [];

        return SettingEnvModel::find([
            'conditions' => "code in ({codes:array})",
            'bind' => array('codes' =>$codes),
            'columns' => ['set_val','code'],
        ])->toArray();
    }

    /**
     * 单个保存env
     * @param $code
     * @param $remark
     * @param string $setVal
     * @param bool $isTrimString
     * @return bool
     */
    public function saveOneSettingEnv($code, $remark, $setVal='', $isTrimString = true)
    {
        if(empty($code) || empty($remark)){
            return false;
        }
        if($isTrimString) {
            $setVal = str_replace('，',',', $setVal);//中文逗号
            $setVal = str_replace(' ','', $setVal);//空格
            $setVal = trim($setVal);
        }

        $data = SettingEnvModel::findFirst(['conditions' => 'code = ?1', 'bind' => [1 => $code]]);
        if(empty($data)){
            $data = new SettingEnvModel();
        }
        $data->code = $code;
        $data->set_val = $setVal;
        $data->remark = $remark;
        return $data->save();
    }

    /**
     * 获取拥有 按模板发送权限的人员id
     * @return false|string[]
     */
    public function getHrisTemplateSendMessagePermission()
    {
        // 读取setting_env表中配置 拥有按模板发送权限的员工id
        $settingEvnRet = $this->getSetVal("hris_template_send_message");
        // 如果不存在，则说明所有员工都没有权限，返回false
        if (empty($settingEvnRet)) {
            return false;
        }
        // 当前登入人id，存在配置的数组，返回true
        return explode(',', $settingEvnRet);
    }

    /**
     * @description 员工连续旷工天数后停职、离职
     * @return array
     */
    public function getAbsentAndLeaveDays()
    {
        $staff_absent_days = $this->getSetVal('staff_absent_days');
        $staff_leave_days = $this->getSetVal('staff_leave_days');
        return [$staff_absent_days, $staff_leave_days];
    }

    /**
     * 自身配置重复校验v1
     * 格式  11/date,22/date...
     * @throws BusinessException
     */
    public function specialFormatSelfRepeatV1($val): array
    {
        $val        = str_replace('，', ',', $val);
        $current    = [];
        $selfRepeat = [];
        foreach (explode(',', $val) as $item) {
            [$partOne] = explode('/', $item);
            if (in_array($partOne, $current)) {
                $selfRepeat[] = $partOne;
                continue;
            }
            $current[] = $partOne;
        }

        //自己重复
        if ($selfRepeat) {
            throw new BusinessException(static::$t->_("special_formatself_repeat_v1", [
                'stores' => implode(',',
                    array_unique($selfRepeat)),
            ]));
        }
        return $current;
    }

    /**
     * 配置与相同格式配置重复校验v1
     * 格式  11/date,22/date... or store_id,store_id,store_id...
     * @throws BusinessException
     */
    public function specialFormatFilterIntersectV1($code, $val, $otherCode,$checkSelf = true)
    {
        $val       = str_replace('，', ',', $val);
        $otherCode = array_values(array_diff($otherCode, [$code]));
        if ($checkSelf) {
            $current   = $this->specialFormatSelfRepeatV1($val);
        } else {
            $current = explode(',', $val);
        }

        $s         = new SettingEnvService();
        $list      = $s->listByParam(['code' => $otherCode]);
        $message   = [];
        foreach ($list as $listItem) {
            $compare = [];
            foreach (explode(',', $listItem['set_val']) as $item) {
                [$storeId] = explode('/', $item);
                $compare[] = $storeId;
            }
            $intersect = array_intersect($current, $compare);
            if ($intersect) {
                $message[] = static::$t->_('special_format_filter_intersect_tip_1',
                    ['stores' => implode(',', $intersect), 'remark' => $listItem['remark']]);
            }
        }
        if ($message) {
            throw new BusinessException(implode('', $message) . static::$t->_('special_format_filter_intersect_tip_2'));
        }
    }
    
    public function checkIsNumeric($code, $val)
    {
        if (in_array($code, [
            'no_carriage_car_rental_coefficient',
        ])){
            if (!is_numeric($val) || $val <= 0){
                throw new BusinessException(static::$t->_('special_format_filter_intersect_tip_2'));
            }
        }
    }
}
