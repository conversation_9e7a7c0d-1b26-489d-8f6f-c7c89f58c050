<?php
/**
 * Author: Bruce
 * Date  : 2023-03-10 18:28
 * Description:
 */

namespace App\Services;


use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\CeoMailEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\CeoMailActionLogModel;
use App\Models\backyard\CeoMailFollowModel;
use App\Models\backyard\CeoMailProblemCategoryModel;
use App\Models\backyard\CeoMailProblemTransferRecordModel;
use App\Models\backyard\CeoMailStaffProblemOrderModel;
use App\Models\backyard\CeoMailSysNoticeModel;
use App\Models\backyard\FlashBoxSettingModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HcmPermissionModel;
use App\Models\backyard\HcmRolePermissionInfoModel;
use App\Models\backyard\HcmStaffPermissionInfoModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\MailReplyFromCeoModel;
use App\Models\backyard\MailToCeoModel;
use App\Models\backyard\RolesModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Repository\CeoMailActionLogRepository;
use App\Repository\CeoMailFollowRepository;
use App\Repository\CeoMailProblemCategoryRepository;
use App\Repository\CeoMailProblemTransferRecordRepository;
use App\Repository\CeoMailStaffProblemOrderRepository;
use App\Repository\CeoMailSysNoticeRepository;
use App\Repository\HcmPermissionRepository;
use App\Repository\HrStaffInfoPositionRepository;
use App\Repository\HrStaffInfoRepository;
use App\Repository\StaffInfoRepository;
use App\Repository\SysStoreRepository;
use Exception;

class CeoMailService extends BaseService
{
    private $flash_box_red_dot_resign_data_time ;

    public function __construct()
    {
        parent::__construct();
        $this->flash_box_red_dot_resign_data_time = env('flash_box_red_dot_resign_data_time', '2024-11-07');
    }

    public $staffInfoId = 0;

    const LIMIT_DOWNLOAD_NUM = 300000;

    const REPLY_DEADLINE = 7;//回复期限
    /**
     * 获取 有权限的初始化数据
     * @param array $params
     *
     * @return array $data
     */
    public function getInitData($params)
    {
        $data = [
            'tab_item'              => [],
            'problem_category_item' => [],
            'reply_status_item'     => $this->getReplyStatusItem(),
            'evaluate_status_item'  => $this->getStaffScoreItem(),
            'staff_state'           => [],
            'hire_type_list'        => (new SysService())->HrisHireTypeList(),
        ];

        // 在职状态
        foreach (Enums::$hris_working_state as $key => $value) {
            $data['staff_state'][] = ['value' => $key, 'label' => self::$t->_($value)];
        }

        // [1] 获取当前工号tab访问权限
        $tab_permission_key = $this->getStaffTabPermissionKeys($params['user_id']);

        if (empty($tab_permission_key)) {
            return $data;
        }

        // tab 列表
        $tab_item = $this->getTabItemByKeys($tab_permission_key);
        if (empty($tab_item)) {
            return $data;
        }

        $data['tab_item'] = $tab_item;

        // [2] 获取相应的问题分类列表
        $default_tab_key               = $this->getTabKeyByEncryptKey($tab_item[0]['tab_key']);
        $data['problem_category_item'] = $this->getProblemCategoryListByTabKey($default_tab_key);

        return $data;
    }

    /**
     * 按工号查询是否有相关权限 + 角色权限
     * @param $staff_info_id
     * @param $position
     * @param $returnIds
     * @return array
     */
    public function getPermissionByStaffId($staff_info_id, $position, $returnIds = false)
    {
        //配置了个人的维度，就是用个人表的权限
        $permission_ids  = [];
        $staffPermission = HcmStaffPermissionInfoModel::find(
            [
                'conditions' => 'is_deleted = 0 and staff_info_id = ?1',
                'bind'       => [
                    1 => $staff_info_id,
                ],
            ]
        )->toArray();
        if (!empty($staffPermission)) {
            $permission_ids = array_column($staffPermission, 'permission_id');
        }

        if (!empty($position)) {
            //配置了角色的维度，就是用角色表的权限
            $rolePermission = HcmRolePermissionInfoModel::find(
                [
                    'columns'    => 'permission_id',
                    'conditions' => 'role_id IN ({ids:array}) and is_deleted = 0',
                    'bind'       => [
                        'ids' => $position,
                    ],
                ]
            )->toArray();
            if (!empty($rolePermission)) {
                $permission_ids = array_merge($permission_ids, array_column($rolePermission, 'permission_id'));
            }
        }
        $permission_ids = array_values(array_unique($permission_ids));
        if (empty($permission_ids)) {
            return [];
        }
        if($returnIds) {
            return $permission_ids;
        }
        return $this->getPermissionInfo($permission_ids);
    }

    /**
     * 获取菜单信息
     * @param $permission_ids
     * @return mixed
     */
    public function getPermissionInfo($permission_ids)
    {
        if (!$permission_ids) {
            return [];
        }
        $rolePermission      = HcmPermissionModel::find(
            [
                'columns'    => 'key',
                'conditions' => 'ancestry = :ancestry: AND id IN ({permission_ids:array}) and is_deleted = 0',
                'bind'       => [
                    'permission_ids' => $permission_ids,
                    'ancestry'       => CeoMailEnums::FLASH_BOX_MENU_ID,
                ],
            ]
        );
        $rolePermissionToUrl = [];
        if ($rolePermission) {
            $rolePermissionToUrl = array_column($rolePermission->toArray(), 'key');
        }

        return $rolePermissionToUrl;
    }

    /**
     * 获取当前用户访问tab的权限keys
     * @param $userId
     * @return array
     */
    public function getStaffTabPermissionKeys($userId)
    {
        if (empty($userId)) {
            return [];
        }

        $position_category_info = HrStaffInfoPositionModel::Find([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind'       => [
                'staff_id' => $userId,
            ],
        ])->toArray();


        $position_category = array_column($position_category_info, 'position_category');

        $tab_permission_keys = $this->getAllTabKey();
        if (in_array(CeoMailEnums::SUPER_MASTER_POSITION_ID, $position_category) || in_array($userId,
                explode(',', env('sa_id')))) {
            return $tab_permission_keys;
        }

        $role_permission_conf_keys = $this->getPermissionByStaffId($userId, $position_category);

        $staff_permission_keys = [];
        foreach ($tab_permission_keys as $key) {
            if (in_array($key, $role_permission_conf_keys)) {
                $staff_permission_keys[] = $key;
            }
        }

        return $staff_permission_keys;
    }

    /**
     * 获取数据 查看 回复权限
     * @param $userId
     * @param $tab_key
     * @param $category_id
     * @return array
     */
    public function getOperatePermission($userId, $tab_key, $category_id)
    {
        if (empty($userId)) {
            return [];
        }

        $position_category_info = HrStaffInfoPositionModel::Find([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind'       => [
                'staff_id' => $userId,
            ],
        ])->toArray();

        $position_category = array_column($position_category_info, 'position_category');

        $result['is_permission_reply'] = false;
        $result['is_permission_check'] = false;

        if (in_array(CeoMailEnums::SUPER_MASTER_POSITION_ID, $position_category) || in_array($userId,
                explode(',', env('sa_id')))) {
            $result['is_permission_reply'] = true;
            $result['is_permission_check'] = true;
            return $result;
        }

        $role_staff_permission_ids = $this->getPermissionByStaffId($userId, $position_category, true);
        if(empty($role_staff_permission_ids)) {
            return $result;
        }

        //th payroll 下有 子集 权限
        if($tab_key == CeoMailEnums::PAYROLL_TAB_KEY && !empty($category_id) && isCountry()) {
            $tab_key = CeoMailEnums::PAYROLL_SUB_TAB_KEYS[$category_id] ?? '';
        }
        if(empty($tab_key)) {
            return [];
        }

        $where['key'] = $tab_key;
        $parentInfo = HcmPermissionRepository::getPermissionOne($where, ['id']);
        if(empty($parentInfo)) {
            return $result;
        }

        $wheres['parent_ids'] = [$parentInfo['id']];
        $childInfo = HcmPermissionRepository::getPermission($wheres, ['id', 'key']);
        if(empty($childInfo)) {
            return $result;
        }

        $childInfoToId = array_column($childInfo, 'key','id');

        $operatePermission = array_values(array_intersect(array_keys($childInfoToId), $role_staff_permission_ids));
        if(empty($operatePermission)) {
            return $result;
        }
        foreach ($operatePermission as $one) {
            if(isset($childInfoToId[$one]) && $childInfoToId[$one] == CeoMailEnums::PERMISSION_REPLY_KEY) {
                $result['is_permission_reply'] = true;
            }

            if(isset($childInfoToId[$one]) && $childInfoToId[$one] == CeoMailEnums::PERMISSION_CHECK_KEY) {
                $result['is_permission_check'] = true;
            }
        }
        return $result;
    }

    /**
     * 获取所有 tab
     * @return array
     */
    public function getAllTabKey()
    {
        return array_keys(CeoMailEnums::TAB_PROBLEM_CATEGORY_MAP);
    }

    /**
     * 获取回复状态列表
     */
    public function getReplyStatusItem()
    {
        $data = [];
        foreach (CeoMailEnums::REPLY_STATUE_ITEM as $key => $value) {
            $data[] = [
                'value' => $key,
                'label' => self::$t->_($value),
            ];
        }

        return $data;
    }

    /**
     * 获取评分列表
     */
    public function getStaffScoreItem()
    {
        $score = [];
        for ($i = 1; $i <= 5; $i++) {
            $score[] = $i;// .' ' . $this->getTranslation()['ceo_mail_score_001'];
        }

        return $score;
    }

    /**
     * 根据加密的 encrypt_tab_key 找对应的明文key
     * @param string $encrypt_key
     * @return string $tab_key
     */
    public function getTabKeyByEncryptKey(string $encrypt_key)
    {
        if (empty($encrypt_key)) {
            return '';
        }

        $tab_category_item = CeoMailEnums::TAB_PROBLEM_CATEGORY_MAP;
        foreach ($tab_category_item as $tab_key => $category_ids) {
            if (md5($tab_key) == $encrypt_key) {
                return $tab_key;
            }
        }

        return '';
    }

    /**
     *
     * @param string $tab_key
     * @return array|array[]
     * @throws Exception
     */
    public function getProblemCategoryListByTabKeyV2(string $tab_key)
    {
        $category_item = $this->getProblemCategoryListByTabKey($tab_key);
        if ($tab_key == CeoMailEnums::PAYROLL_TAB_KEY && isCountry('TH')) {
            return $category_item;
        }

        $list = [
            'default_category' => [],
            'ic_category'      => [],
        ];

        foreach ($category_item as $key => $value) {
            if ($value['use_type'] == CeoMailProblemCategoryModel::USE_TYPE_ALL) {
                $list['default_category'][] = $value;
                $list['ic_category'][]      = $value;
            } elseif ($value['use_type'] == CeoMailProblemCategoryModel::USE_TYPE_PERMANENT) {
                $list['default_category'][] = $value;
            } elseif ($value['use_type'] == CeoMailProblemCategoryModel::USE_TYPE_LNT) {
                $list['default_category'][] = $value;
            } elseif ($value['use_type'] == CeoMailProblemCategoryModel::USE_TYPE_IC) {
                $list['ic_category'][] = $value;
            }
        }
        return $list;
    }


    /**
     * 获取 tab_key 获取对应的分类列表
     * @param string $tab_key
     * @return array
     * @throws Exception
     */
    public function getProblemCategoryListByTabKey(string $tab_key)
    {
        $result = [];

        if (empty($tab_key)) {
            return $result;
        }

        $ids = $this->getProblemCategoryListByRelatedId($tab_key);
        $result = $this->getProblemCategoryListByParentIds($ids);

        if ($tab_key == CeoMailEnums::PAYROLL_TAB_KEY) { //如果是payroll
            $param['tab_key']        = $tab_key;
            $param['problem_status'] = [CeoMailEnums::HAVE_NO_REPLY_STATUS, CeoMailEnums::HAVE_TIMED_OUT_SYS_STATUS];
            $param['user_id'] = $this->staffInfoId;
            $categoryIds = $this->filterProblemCategoryByStaffPermission($this->staffInfoId);
            foreach ($result as $k => $v) {
                if (!in_array($v['category_id'], $categoryIds)) {
                    unset($result[$k]);
                } else {
                    $param['category_id'] = $v['category_id'];
                    $count = $this->getPayrollOrderCountByCategoryIds($param);
                    $result[$k]['waiting_reply_count'] = $count ?? 0;
                }
            }
            $result = array_values($result);
        }

        return $result;
    }

    /**
     * 获取指定问题分类列表 By 一级分类ID
     * 如果只有 1 个一级分类，则只获取子类列表; 如果有多个 一级分类，则获取多个列表
     * @param array $ids
     * @param array $filter_category_ids
     * @return array $result
     */
    public function getProblemCategoryListByParentIds(array $ids, array $filter_category_ids = [])
    {
        if(empty($ids)) {
           return [];
        }
        $category_name = 'category_name_'.$this->getLanguage(self::$language);
        $data          = CeoMailProblemCategoryModel::find([
            'columns'    => "id AS category_id, ".$category_name." AS category_name, parent_id, use_type",
            'conditions' => 'id IN ({ids:array}) AND display_status = :display_status: ',
            'bind'       => [
                'ids'            => $ids,
                'display_status' => CeoMailProblemCategoryModel::DISPLAY_STATUS_SHOW,
            ],
            'order'      => 'parent_id ASC, sort_index DESC',
        ])->toArray();

        $_tmp_result = [];
        foreach ($data as $value) {
            // 过滤指定的类别
            if (!empty($filter_category_ids) && in_array($value['category_id'], $filter_category_ids)) {
                continue;
            }

            $parent_id = $value['parent_id'];

            if ($parent_id == 0) {
                $_tmp_result[$value['category_id']] = $value;
            } else {
                $_tmp_result[$parent_id]['sub_category'][] = $value;
            }
        }

        $result = [];
        foreach ($_tmp_result as $__key => $__value) {
            if (!empty($__value['sub_category'])) {
                foreach ($__value['sub_category'] as $__sub_category) {
                    $result[] = [
                        'category_id'   => $__sub_category['category_id'],
                        'use_type'      => $__sub_category['use_type'],
                        'category_name' => $__value['category_name'].' - '.$__sub_category['category_name'],
                    ];
                }
            } else {
                $result[] = [
                    'category_id'   => $__value['category_id'],
                    'use_type'      => $__value['use_type'],
                    'category_name' => $__value['category_name'],
                ];
            }
        }

        return $result;
    }

    /**
     * 获取指定问题分类列表 By 一级分类ID
     * 如果只有 1 个一级分类，则只获取子类列表; 如果有多个 一级分类，则获取多个列表
     * @param array $parent_ids
     * @param array $filter_category_ids
     * @return array $result
     * 废弃
     */
    public function getProblemCategoryListByParentIdss(array $parent_ids, array $filter_category_ids = [])
    {
        $category_name = 'category_name_'.$this->getLanguage(self::$language);
        $data          = CeoMailProblemCategoryModel::find([
            'columns'    => "id AS category_id, ".$category_name." AS category_name, parent_id",
            'conditions' => '(id IN ({ids:array}) OR parent_id IN ({parent_ids:array})) AND display_status = :display_status: ',
            'bind'       => [
                'ids'            => $parent_ids,
                'parent_ids'     => $parent_ids,
                'display_status' => CeoMailProblemCategoryModel::DISPLAY_STATUS_SHOW,
            ],
            'order'      => 'parent_id ASC, sort_index DESC',
        ])->toArray();

        $_tmp_result = [];
        foreach ($data as $value) {
            // 过滤指定的类别
            if (!empty($filter_category_ids) && in_array($value['category_id'], $filter_category_ids)) {
                continue;
            }

            $parent_id = $value['parent_id'];

            if ($parent_id == 0) {
                $_tmp_result[$value['category_id']] = $value;
            } else {
                $_tmp_result[$parent_id]['sub_category'][] = $value;
            }
        }

        $result = [];
        foreach ($_tmp_result as $__key => $__value) {
            if (!empty($__value['sub_category'])) {
                foreach ($__value['sub_category'] as $__sub_category) {
                    $result[] = [
                        'category_id'   => $__sub_category['category_id'],
                        'category_name' => $__value['category_name'].' - '.$__sub_category['category_name'],
                    ];
                }
            } else {
                $result[] = [
                    'category_id'   => $__value['category_id'],
                    'category_name' => $__value['category_name'],
                ];
            }
        }

        return $result;
    }

    /**
     * 根据 tab_key 获取 类别ID
     * @param array $tab_keys
     * @return array $result
     */
    public function getCategoryIdsByTabKey(array $tab_keys)
    {
        $result = [];

        if (empty($tab_keys)) {
            return $result;
        }

        foreach ($tab_keys as $key_v) {
            $_tmp_category_ids = CeoMailEnums::TAB_PROBLEM_CATEGORY_MAP[$key_v];
            $_tmp_category_ids = $_tmp_category_ids ? $_tmp_category_ids : [];
            $result            = array_merge($result, $_tmp_category_ids);
        }

        return $result;
    }


    /**
     * 获取指定问题分类列表 By 子类ID
     * 如果只有 1 个一级分类，则只获取子类列表; 如果有多个 一级分类，则获取多个列表，树状结构返回
     * @param array $category_ids
     * @return array $result
     */
    public function getProblemCategoryListByIds(array $category_ids)
    {
        $category_name = 'category_name_'.$this->getLanguage(self::$language);


        $data = CeoMailProblemCategoryModel::find([
            'columns'    => "id AS category_id, $category_name AS category_name, parent_id",
            'conditions' => 'id in ({ids:array}) AND display_status = :display_status: ',
            'bind'       => [
                'ids'            => $category_ids,
                'display_status' => CeoMailProblemCategoryModel::DISPLAY_STATUS_SHOW,
            ],
            'order'      => 'parent_id ASC, sort_index DESC',
        ])->toArray();


        $result = [];
        foreach ($data as $value) {
            $parent_id = $value['parent_id'];

            if ($parent_id == 0) {
                $result[$value['category_id']] = $value['category_name'];
            } else {
                // 查询父类分类名称
                $parent_category_sql           = "
                    -- 获取指定一级问题分类名称
                    SELECT 
                        $category_name AS category_name
                    FROM 
                        ceo_mail_problem_category
                    WHERE
                        id = :parent_id
                    ";
                $bind                          = ['parent_id' => $parent_id];
                $parent_category_name          = $this->db_rby->fetchColumn($parent_category_sql, $bind);
                $result[$value['category_id']] = $parent_category_name.' - '.$value['category_name'];
            }
        }

        return $result;
    }

    /**
     * 通过tab_key查询 分类信息
     * @param $tab_key
     * @return array
     */
    public function getProblemCategoryListByRelatedId($tab_key)
    {
        $conditions = ' related_tab_id is not NULL and parent_id != :parent_id: and display_status = :display_status:';
        $bind['parent_id'] = 0;
        $bind['display_status'] = CeoMailProblemCategoryModel::DISPLAY_STATUS_SHOW;
        if($tab_key != CeoMailEnums::ALL_TAB_KEY) {
            $conditions .= ' and related_tab_id = :related_tab_id:';
            $bind['related_tab_id'] = $tab_key;
        } else {
            $conditions .= ' and id not in ({ids:array})';
            $bind['ids'] = $this->getAllDataTabNotDisplayIds();
        }
        $categoryInfo = CeoMailProblemCategoryRepository::getCategoryInfo('id,parent_id', $conditions, $bind);
        $categoryInfoPrentId = array_values(array_unique(array_column($categoryInfo, 'parent_id')));
//        print_r($categoryInfoPrentId);die;
        $categoryInfoId = array_column($categoryInfo, 'id');

        return array_merge($categoryInfoId, $categoryInfoPrentId);
    }

    /**
     * 获取不允许 all data tab 展示的分类
     * @return array
     */
    public function getAllDataTabNotDisplayIds()
    {
        return CeoMailEnums::TAB_ALL_DATA_NO_DISPLAY;
    }

    //获取系统语言--如果不是 中英泰，则默认返回 EN
    public function getLanguage($language)
    {
        $allLanguage = ['en', 'th', 'zh'];
        if ($language == 'zh-CN') {
            $language = 'zh';
        }
        if (in_array($language, $allLanguage)) {
            return $language;
        }
        return 'en';
    }

    /**
     * 根据tab key 获取tab列表
     * @param array $tab_keys
     * @param bool $is_return_source_tab_key
     *
     * @return array $result
     */
    public function getTabItemByKeys(array $tab_keys, bool $is_return_source_tab_key = false)
    {
        if (empty($tab_keys)) {
            return [];
        }

        $tab_item = [];
        foreach ($tab_keys as $key) {
            $_curr_tab_item = [
                'tab_key'   => md5($key),
                'tab_title' => self::$t->_($key),
            ];

            if ($is_return_source_tab_key) {
                $_curr_tab_item['source_tab_key'] = $key;
            }

            $tab_item[] = $_curr_tab_item;
        }

        return $tab_item;
    }

    /**
     * 查询列表数据
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffProblemOrderList($params)
    {
        // 日期格式校验
        if (!empty($params['start_date']) && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['start_date'])) {
            throw new ValidationException('start_date param error');
        }

        if (!empty($params['end_date']) && !preg_match('/^\d{4}-\d{2}-\d{2}$/', $params['end_date'])) {
            throw new ValidationException('end_date param error');
        }

        $tab_key = $this->getTabKeyByEncryptKey($params['encrypt_tab_key']);
        if (empty($tab_key)) {
            throw new ValidationException('tab_key param error');
        }
        $params['tab_key'] = $tab_key;

        $this->staffInfoId = $params['user_id'];
        $category_item = $this->getProblemCategoryListByTabKey($tab_key);
        if (empty($category_item)) {
            return ['total' => 0, 'list' => []];
        }

        $param['category_item'] = array_column($category_item, 'category_id');

        $params['page']     = empty($params['page']) ? 1 : $params['page'];
        $params['page_num'] = empty($params['page_num']) ? 20 : $params['page_num'];

        // 如果TAB对应的问题分类有值，则获取其对应的子类ID 列表, 获取不到则返回其自身
        $category_ids = [];
        if (!empty($params['category_id'])) {
            $category_ids[] = $params['category_id'];
            $params['hire_type_not_ic'] = 1;
        }
        if (!empty($params['ic_category_id'])) {
            $category_ids[] = $params['ic_category_id'];
            $params['hire_type_only_ic'] = 1;
            $params['category_id'] = $params['ic_category_id'];
        }

        if (!empty($params['hire_type_not_ic']) && !empty($params['hire_type_only_ic'])) {
            $params['hire_type_not_ic'] = $params['hire_type_only_ic'] = 0;
        }

        if (isCountry('TH') && $tab_key == CeoMailEnums::PAYROLL_TAB_KEY) {
            $params['hire_type_not_ic'] = $params['hire_type_only_ic'] = 0;
        }

        if (empty($category_ids) && !empty($param['category_item'])) {
            $category_ids = $this->getSubCategoryIds($param['category_item']);
        }

        $params['category_ids'] = $category_ids;

        $total = $this->getListQuery($params, true);
        $data['total'] = !empty($total) ? intval($total['count']) : 0;
        if(isset($params['is_check_data_num']) && $params['is_check_data_num'] == true) {
            return $data;
        }
        $list  = $this->getListQuery($params);
        if (!empty($list)) {
            $list = $this->formatList($list, $category_ids);
        }
        //获取 查看 回复权限
        $res = $this->getOperatePermission($params['user_id'], $tab_key, $params['category_id']);
        $data['is_permission_reply'] = $res['is_permission_reply'];
        $data['is_permission_check'] = $res['is_permission_check'];

        $data['list']  = $list;

        return $data;
    }

    /**
     * 格式化数据
     * @param $list
     * @param $category_ids
     * @return mixed
     */
    public function formatList($list, $category_ids)
    {
        $storeListToId      = $jobTitleInfoToId = [];
        $jobTitleId         = array_values(array_unique(array_column($list, 'job_title')));
        $sysStoreId         = array_values(array_unique(array_column($list, 'sys_store_id')));
        $staff_category_ids = array_column($list, 'problem_category_v2');

        $storeList   = (new SysStoreService())->getStoreListByIds($sysStoreId);
        $storeList[] = [
            'id'          => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $jobTitleInfo = (new DepartmentService())->getJobList('', $jobTitleId, true);

        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $latest_feedback_id_item   = array_column($list, 'latest_feedback_id');
        $latest_feedback_info_list = $this->getFeedbackListByIds($latest_feedback_id_item);
        if (!empty($latest_feedback_info_list)) {
            $latest_feedback_info_list = array_column($latest_feedback_info_list, null, 'id');
        }

        // 根据分类id 查找对应的分类展示
        $problem_category_item  = $this->getProblemCategoryListByIds(array_values(array_unique(array_merge($category_ids,$staff_category_ids))));

        $orderProblem = array_column($list, 'problem_no');
        $problem_nos = array_chunk($orderProblem, 1000);

        $allRecordList = $allNoticeList = $allActionLog = [];
        foreach ($problem_nos as $oneBatch) {
            $transferListInfo = CeoMailProblemTransferRecordRepository::getTransferRecordList(['problem_nos' => $oneBatch]);
            $recordList       = array_column($transferListInfo, null, 'problem_no');
            $allRecordList    = array_merge($allRecordList, $recordList);

            $noticeListInfo = CeoMailSysNoticeRepository::getNoticeList(['problem_nos' => $oneBatch],
                ['problem_no', 'img_url', 'remark']);
            $noticeList     = array_column($noticeListInfo, null, 'problem_no');
            $allNoticeList  = array_merge($allNoticeList, $noticeList);
            //关闭备注
            $actionLog      = CeoMailActionLogRepository::getCloseRemarkImageList($oneBatch);
            $allActionLog   = array_merge($allActionLog, $actionLog);
        }

        $tabName = $this->getCategoryTabName();

        $add_hour = $this->config->application->add_hour;
        foreach ($list as $key => $value) {
            $state = $value['state'];
            if ($value['state'] == 1 && $value['wait_leave_state'] == 1) {//待离职
                $state = 999;
            }
            $value['staff_state']     = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';

            $value['problem_category_name'] = $problem_category_item[$value['sys_category_id_v2']] ?? '';
            $problem_category_name = explode('-', $value['problem_category_name']);
            //导出要拆开
            $value['problem_category_name_parent'] = $problem_category_name[0];//大类
            $value['problem_category_name_sub']    = $problem_category_name[1];//小类


            $staff_problem_category_name = explode('-', $problem_category_item[$value['problem_category_v2']] ?? '');
            //员工提交的大类小类
            $value['staff_problem_category_name_parent'] = $staff_problem_category_name[0]??'';//大类
            $value['staff_problem_category_name_sub']    = $staff_problem_category_name[1]??'';//小类


            // 已评价 == 已完成
            // 超时未反馈 == 已完成
            if ($value['is_evaluate'] || $value['problem_status'] == CeoMailEnums::HAVE_TIMED_OUT_STATUS) {
                $value['problem_status'] = CeoMailEnums::HAVE_COMPLETED_STATUS;
            }

            // 问题工单状态
            $value['problem_status_text'] = self::$t->_(CeoMailEnums::REPLY_STATUE_ITEM[$value['problem_status']]);

            // 0 值处理
            $value['first_reply_staff_id'] = $value['first_reply_staff_id'] ? $value['first_reply_staff_id'] : '';
            $value['last_deal_staff_id']   = $value['last_deal_staff_id'] ? $value['last_deal_staff_id'] : '';
            $value['first_reply_time']     = $value['first_reply_time'] ? $value['first_reply_time'] : '';
            $value['last_deal_time']       = $value['last_deal_time'] ? $value['last_deal_time'] : '';
            $value['staff_score']          = $value['staff_score'] ? $value['staff_score'] : '';

            // 获取最新反馈的描述/图片
            $latest_problem_image = $latest_feedback_info_list[$value['latest_feedback_id']]['problem_image'] ?? '';
            $latest_problem_desc  = $latest_feedback_info_list[$value['latest_feedback_id']]['problem_desc'] ?? '';
            if (empty($latest_problem_image) && empty($latest_problem_desc)) {
                $latest_problem_image = $value['problem_image'];
                $latest_problem_desc  = $value['problem_desc'];
            }

            $value['problem_image'] = !empty($latest_problem_image) ? $latest_problem_image : '';
            $value['problem_desc']  = $latest_problem_desc;

            // 字符串转数组
            $value['staff_evaluate'] = !empty($value['staff_evaluate']) ? $value['staff_evaluate'] : '';

            // 间隔时长格式转换
            $value['first_reply_interval_time'] = $this->timeSecondsFormatConversion($value['first_reply_interval_time']);

            // 回复期限
            $value['reply_deadline'] = date('Y-m-d H:i:s',
                strtotime($value['create_time']) + self::REPLY_DEADLINE * 86400);

            // 若手机号是脱敏 或 空值, 则获取新的手机号
            if (empty($value['staff_mobile']) && !empty($value['mobile'])) {
                $value['staff_mobile'] = $value['mobile'];
            }

            $value['store_name'] = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['store_name'] ?? '' : '';
            $value['job_title']  = $jobTitleInfoToId[$value['job_title']] ?? '';

            //员工所属片区名、大区名、所属区域名
            $value['piece_name']  = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['piece_name'] ?? '' : '';
            $value['region_name'] = isset($storeListToId[$value['sys_store_id']]) ? $storeListToId[$value['sys_store_id']]['region_name'] ?? '' : '';
            $value['sorting_no'] =  $this->getSortingNo($value['sys_store_id']);//所属区域。
            // 转单后首次回复历经时长
            $value['transfer_first_reply_interval_time'] = $this->timeSecondsFormatConversion($value['transfer_first_reply_interval_time']);
            //转单后首次回复时间
            $value['transfer_first_reply_time'] = !empty($value['transfer_first_reply_time']) ? $value['transfer_first_reply_time'] : '';
            //完成时间
            $value['complete_time'] =  $value['problem_status'] == CeoMailEnums::HAVE_COMPLETED_STATUS ? date('Y-m-d H:i:s', (strtotime($value['update_time']) + $add_hour * 3600)) : '';
            //发起到完成历经时长
            $value['all_interval_time'] = $this->timeSecondsFormatConversion($value['all_interval_time']);
            //最新转单组织
            $value['source_transfer_tab'] =  isset($allRecordList[$value['problem_no']]) ? ($tabName[$allRecordList[$value['problem_no']]['before_transfer_category_id']] ?? '') : '';
            //归属组织
            $value['belong_tab'] =  $tabName[$value['sys_category_id_v2']];//工单当前，小类所属组织

            $value['close_order_remark'] = $this->dealExportRemark($value['problem_no'],$allNoticeList,$allActionLog);//关闭备注
            $value['all_img_url']        = $this->dealExportImage($value['problem_no'],$allNoticeList,$allActionLog) ;//图片
            //最新移交时间
            $value['transfer_time'] = isset($allRecordList[$value['problem_no']]) ? $allRecordList[$value['problem_no']]['transfer_time'] : '';
            $value['hire_type_text'] =  static::$t->_('hire_type_'.$value['hire_type']);//雇佣类型。
            $value['feedback_waiting_interval_time'] =  !empty($value['feedback_waiting_interval_time']) ? $this->timeSecondsFormatConversion($value['feedback_waiting_interval_time']) : '-';//提问等待时间。

            unset($value['department_id']);
            unset($value['state']);
            unset($value['wait_leave_state']);
            unset($value['avatar_url']);
            unset($value['evaluate_time']);
            unset($value['update_time']);
            unset($value['is_read_sys_reply']);
            unset($value['is_evaluate']);
            unset($value['latest_feedback_id']);
            unset($value['sys_store_id']);
            unset($value['mobile']);
            unset($value['sys_category_id_v2']);
            $list[$key] = $value;
        }
        return $list;
    }

    protected function dealExportRemark($problem_no, $allNoticeList, $allActionLog)
    {
        $remark = [];
        if (!empty($allNoticeList[$problem_no])) {
            $remark[] = $allNoticeList[$problem_no]['remark'];
        }
        if (!empty($allActionLog[$problem_no])) {
            $remark[] = implode(";\n",array_column($allActionLog[$problem_no],'content'));
        }
        return implode(";\n", $remark);
    }

    protected function dealExportImage($problem_no, $allNoticeList, $allActionLog)
    {
        $img_url = [];
        if (!empty($allNoticeList[$problem_no])) {
            $img_url[] = str_replace(',', "\n", $allNoticeList[$problem_no]['img_url']);
        }
        if (!empty($allActionLog[$problem_no])) {

            $img_url[] = str_replace(',', "\n", implode(';',array_column($allActionLog[$problem_no],'img_url')));
        }
        return trim(implode("\n", $img_url),';');
    }

    public function getSortingNo($storeId)
    {
        $areaInfo = (new ProbationService())->getAreaByStore($storeId);
        return $areaInfo ? GlobalEnums::$areas[$areaInfo['id']] : '';
    }

    /**
     * 列表查询
     * @param $params
     * @param bool $isCount
     * @return array
     * @throws \Exception
     */
    public function getListQuery($params, $isCount = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(*) as count');
        $builder->from(['cmspo' => CeoMailStaffProblemOrderModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'cmspo.staff_id = hsi.staff_info_id', 'hsi');

        $builder = $this->getBuilderWhere($builder, $params);

        if($builder === false) {
            return [];
        }

        if (!empty($params['hire_type_only_ic'])) {
            $builder->inWhere('hsi.hire_type', HrStaffInfoModel::$agentTypeTogether);
        }

        if (!empty($params['hire_type_not_ic'])) {
            $builder->andWhere('hsi.hire_type not in ({hire_type_not_ic:array})',
                ['hire_type_not_ic' => HrStaffInfoModel::$agentTypeTogether]);
        }
        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        $builder->columns('cmspo.id, cmspo.problem_no, cmspo.staff_id, cmspo.staff_name, cmspo.staff_mobile, cmspo.avatar_url, cmspo.problem_desc, cmspo.problem_image, cmspo.department_id, cmspo.department_name,cmspo.create_time,cmspo.first_reply_staff_id,cmspo.first_reply_time, cmspo.last_deal_staff_id, cmspo.last_deal_time, cmspo.last_reply_staff_id, cmspo.last_reply_staff_id, cmspo.problem_status, cmspo.is_evaluate,cmspo.staff_score, cmspo.staff_evaluate, cmspo.staff_evaluate_remark,cmspo.evaluate_time,cmspo.all_interval_time,cmspo.first_reply_interval_time,cmspo.update_time, cmspo.is_read_sys_reply, cmspo.latest_feedback_id,cmspo.latest_feedback_time,cmspo.sys_category_id_v2, cmspo.problem_category_v2, hsi.state, hsi.wait_leave_state, hsi.job_title, hsi.sys_store_id, hsi.mobile, hsi.hire_type, cmspo.transfer_first_reply_time, cmspo.transfer_first_reply_interval_time, cmspo.update_time, cmspo.transfer_num, cmspo.reply_num, cmspo.feedback_waiting_interval_time');
        if (empty($params['is_download'])) {
            $builder->limit($params['page_num'], $params['page_num'] * ($params['page'] - 1));
        }
        $builder->orderBy('cmspo.feedback_waiting_interval_time DESC, cmspo.create_time DESC');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 构建查询条件
     * @param $builder
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function getBuilderWhere($builder, $params)
    {
        $before_seven_days_time = date('Y-m-d H:i:s', time() - 7 * 86400);
        $config = $this->getConfig($params['category_id'], $params['tab_key']);
        if($params['tab_key'] != $this->getAllDataTabKey()) {
            do {
                //判断是否存在配置
                if (empty($config)) {
                    $builder->inWhere('cmspo.sys_category_id_v2', $params['category_ids']);
                    break;
                }

                $storeStaff     = $config['store_staffs'] ?? [];
                $categoryConfig = $config['setting'] ?? [];

                //获取分配的页签
                $categoryIdsArr = [];
                if ($params['tab_key'] == CeoMailEnums::PAYROLL_TAB_KEY && isCountry()) {
                    $categoryIdsArr = $this->filterProblemCategoryByStaffPermission($params['user_id']);
                    if (empty($categoryIdsArr)) { //没有分配页签权限
                        return false;
                    }
                }

                //判断开关是否打开,
                //如果打开则不判断权限部分
                if (isset($categoryConfig['is_open_switch']) && $categoryConfig['is_open_switch'] == FlashBoxSettingModel::FLASH_BOX_SWITCH_CLOSED) {
                    //一级 + 二级的
                    if ($params['tab_key'] == CeoMailEnums::PAYROLL_TAB_KEY && isCountry()) {
                        if (isset($categoryIdsArr) && $categoryIdsArr) {
                            $builder->inWhere('cmspo.sys_category_id_v2', $categoryIdsArr);
                        }

                        if (!empty($params['category_id'])) {
                            $builder->andWhere('cmspo.sys_category_id_v2 = :sys_category_id_v2:', ['sys_category_id_v2' => $params['category_id']]);
                        }
                    } else {
                        $builder->inWhere('cmspo.sys_category_id_v2', $params['category_ids']);
                    }
                    break;
                }

                if ($categoryConfig['way_to_obtain_jurisdiction'] == 0) { //当前菜单配置的管辖范围
                    $getStaffData = (new StaffService())->getStaffJurisdiction($params['user_id'], HrStaffManageDepartmentModel::TYPE_FLASH_BOX);
                } else { //员工管理信息中的管辖范围
                    $getStaffData =  (new StaffService())->getStaffJurisdiction($params['user_id']);
                }

                //没有管辖权限
                if (empty($getStaffData['departments']) &&
                    empty($getStaffData['stores']) &&
                    empty($getStaffData['regions']) &&
                    empty($getStaffData['pieces']) &&
                    empty($getStaffData['store_categories'])
                ) {
                    return false;
                }

                if ($categoryConfig['way_to_obtain_staff_department'] == 0) { //员工在总部进行发薪

                    //查询管辖网点
                    if (in_array(HrStaffManageStoreModel::$all_id, $getStaffData['stores'])) {

                        //总部部门权限 & 全部网点的权限
                        //总部权限: 总部权限 & (在总部工作 + 网点工作但总部发薪的人)
                        //网点权限: 全部网点权限 & (在网点工作 - 网点工作但总部发薪的人)
                        if (!empty($getStaffData['departments'])) {
                            $builder->andWhere("(hsi.node_department_id IN({department_id:array}) and (hsi.sys_store_id = '-1' or hsi.staff_info_id IN({staff_header_office:array}))) or 
                                (hsi.sys_store_id != '-1' and hsi.staff_info_id NOT IN({staff_ids:array}))", [
                                'department_id'       => $getStaffData['departments'],
                                'staff_header_office' => $storeStaff,
                                'staff_ids'           => $storeStaff,
                            ]);
                        } else {

                            //网点权限: 全部网点权限 & (在网点工作 - 网点工作但总部发薪的人)
                            $builder->andWhere("hsi.sys_store_id != '-1' and hsi.staff_info_id NOT IN({staff_ids:array})", [
                                'staff_ids' => $storeStaff,
                            ]);
                        }

                    } else {
                        //获取大区、片区、网点类型、网点的并集
                        $getStaffData['stores'] = (new StaffService())->getStaffJurisdictionStore($getStaffData);
                        if (!empty($getStaffData['stores']) && !empty($getStaffData['departments'])) {

                            //总部部门权限 & 部分网点的权限
                            //总部权限: 总部权限 & (在总部工作 + 网点工作但总部发薪的人)
                            //网点权限: 部分网点权限 & (在总部工作 - 网点工作但总部发薪的人)
                            $builder->andWhere("(hsi.node_department_id IN({department_id:array}) and (hsi.sys_store_id = '-1' or hsi.staff_info_id IN({staff_header_office:array}))) or 
                                (hsi.sys_store_id IN({store_ids:array}) and hsi.staff_info_id NOT IN({staff_ids:array}))", [
                                'department_id'       => $getStaffData['departments'],
                                'staff_header_office' => $storeStaff,
                                'store_ids'           => $getStaffData['stores'],
                                'staff_ids'           => $storeStaff,
                            ]);

                        } else if (!empty($getStaffData['stores'])) {

                            //网点权限: 部分网点权限 & (在总部工作 - 网点工作但总部发薪的人)
                            $builder->andWhere("hsi.sys_store_id IN({store_ids:array}) and hsi.staff_info_id NOT IN({staff_ids:array})", [
                                'store_ids'           => $getStaffData['stores'],
                                'staff_ids'           => $storeStaff,
                            ]);
                        } else if (!empty($getStaffData['departments'])) {

                            //总部权限: 总部权限 & (在总部工作 + 网点工作但总部发薪的人)
                            $builder->andWhere("hsi.node_department_id IN({department_id:array}) and (hsi.sys_store_id = '-1' or hsi.staff_info_id IN({staff_header_office:array}))", [
                                'department_id'       => $getStaffData['departments'],
                                'staff_header_office' => $storeStaff,
                            ]);
                        } else {
                            return false;
                        }
                    }

                } else {
                    //查询管辖网点
                    if (in_array(HrStaffManageStoreModel::$all_id, $getStaffData['stores'])) { //全部网点

                        //总部部门权限 & 全部网点的权限
                        //总部权限: 总部权限 & 在总部工作
                        //网点权限: 全部网点权限 & 在网点工作
                        if (!empty($getStaffData['departments'])) {
                            $builder->andWhere("(hsi.node_department_id IN({department_id:array}) and hsi.sys_store_id = '-1') or hsi.sys_store_id != '-1'",
                                [
                                    'department_id' => $getStaffData['departments'],
                                ]);
                        } else {

                            //网点权限: 全部网点权限 & 在网点工作
                            $builder->andWhere("hsi.sys_store_id != '-1'");
                        }

                    } else { //部分网点
                        //获取大区、片区、网点类型、网点的并集
                        $getStaffData['stores'] = (new StaffService())->getStaffJurisdictionStore($getStaffData);
                        if (!empty($getStaffData['stores']) && !empty($getStaffData['departments'])) {

                            //总部部门权限 & 部分网点的权限
                            //总部权限: 总部权限
                            //网点权限: 部分网点权限
                            $builder->andWhere("(hsi.node_department_id IN({department_id:array}) and hsi.sys_store_id = '-1') or hsi.sys_store_id IN({store_ids:array})",
                                [
                                    'department_id' => $getStaffData['departments'],
                                    'store_ids'     => $getStaffData['stores'],
                                ]);

                        } else if (!empty($getStaffData['stores'])) {

                            //网点权限: 部分网点权限
                            $builder->andWhere("hsi.sys_store_id IN({store_ids:array})",
                                [
                                    'store_ids'     => $getStaffData['stores'],
                                ]);
                        } else if (!empty($getStaffData['departments'])) {

                            //总部权限: 总部权限 & 在总部工作
                            $builder->andWhere("hsi.node_department_id IN({department_id:array}) and hsi.sys_store_id = '-1'", [
                                'department_id'       => $getStaffData['departments'],
                            ]);
                        } else {
                            return false;
                        }
                    }
                }


                if ($params['tab_key'] == CeoMailEnums::PAYROLL_TAB_KEY && isCountry()) { //payroll下
                    if (!empty($params['category_id'])) {
                        $builder->andWhere('cmspo.sys_category_id_v2 = :sys_category_id_v2: and cmspo.sys_category_id_v2 IN({category:array})', [
                            'sys_category_id_v2' => $params['category_id'],
                            'category' => $categoryIdsArr,
                        ]);
                    } else {
                        $builder->inWhere('cmspo.sys_category_id_v2', $categoryIdsArr);
                    }
                } else { //除了payroll下
                    $builder->inWhere('cmspo.sys_category_id_v2', $params['category_ids']);
                }

            } while(0);

        } else {
            $builder->where('cmspo.sys_category_id_v2 in ({sys_category_ids:array})',
                ['sys_category_ids' => $params['category_ids']]);
        }

        //按订单编号 精确查询
        if (!empty($params['problem_no'])) {
            $builder->andWhere('cmspo.problem_no = :problem_no: ', ['problem_no' => $params['problem_no']]);
        }

        //按雇佣类型
        if (!empty($params['hire_type'])) {
            $builder->andWhere('hsi.hire_type in ({hire_type:array}) ', ['hire_type' => $params['hire_type']]);
        }

        //提交时间
        if (!empty($params['start_date']) && !empty($params['end_date'])) {
            $builder->betweenWhere('cmspo.latest_feedback_time', $params['start_date'].' 00:00:00',
                $params['end_date'].' 23:59:59');
        }

        //首次提交时间
        if (!empty($params['start_create_time']) && !empty($params['end_create_time'])) {
            $builder->betweenWhere('cmspo.create_time', $params['start_create_time'].' 00:00:00',
                $params['end_create_time'].' 23:59:59');
        }

        //按工号
        if (!empty($params['staff_id'])) {
            $builder->andWhere('cmspo.staff_id = :staff_id: ', ['staff_id' => $params['staff_id']]);
        }

        //首次回复人
        if (!empty($params['first_reply_staff_id'])) {
            $builder->andWhere('cmspo.first_reply_staff_id = :first_reply_staff_id: ',
                ['first_reply_staff_id' => $params['first_reply_staff_id']]);
        }

        //最后处理人
        if (!empty($params['last_deal_staff_id'])) {
            $builder->andWhere('cmspo.last_deal_staff_id = :last_deal_staff_id: ',
                ['last_deal_staff_id' => $params['last_deal_staff_id']]);
        }

        //回复状态
        if(!empty($params['reply_status'])) {
            $reply_status = [];
            //待回复
            if(in_array(CeoMailEnums::HAVE_NO_REPLY_STATUS, $params['reply_status'] )) {
                $reply_status[] = CeoMailEnums::HAVE_NO_REPLY_STATUS;
            }
            //已回复
            if(in_array(CeoMailEnums::HAVE_REPLIED_STATUS, $params['reply_status'] )) {
                $reply_status[] = CeoMailEnums::HAVE_REPLIED_STATUS;
            }

            //已完成 + 系统侧回复后，by侧员工3天内未再次提问：by侧 超时 也是 已完成的一种
            if(in_array(CeoMailEnums::HAVE_COMPLETED_STATUS, $params['reply_status'] )) {
                $reply_status[] = CeoMailEnums::HAVE_COMPLETED_STATUS;
                $reply_status[] = CeoMailEnums::HAVE_TIMED_OUT_STATUS;
            }

            //关闭处理中
            if(in_array(CeoMailEnums::HAVE_CLOSING_STATUS, $params['reply_status'])) {
                $reply_status[] = CeoMailEnums::HAVE_CLOSING_STATUS;
            }

            //系统侧回复超时 状态
            if(in_array(CeoMailEnums::HAVE_TIMED_OUT_SYS_STATUS, $params['reply_status'])) {
                $reply_status[] = CeoMailEnums::HAVE_TIMED_OUT_SYS_STATUS;
            }

            if (!empty($reply_status)) {
                $builder->andWhere('cmspo.problem_status in ({problem_status:array})',
                    ['problem_status' => $reply_status]);
            }

        }

        if (!empty($params['problem_status'])) {
            $builder->inWhere("cmspo.problem_status", $params['problem_status']);
        }

        //评价结果
        if (!empty($params['evaluate_score'])) {
            $builder->andWhere('cmspo.staff_score = :staff_score: ', ['staff_score' => $params['evaluate_score']]);
        }

        if (!empty($params['is_red_dot_resign_data'])) {
            $builder->andWhere('hsi.state in ({state_1_3:array}) OR ( hsi.state = :state_2: and hsi.leave_date >= :red_dot_leave_date:)',
                [
                    'state_1_3'          => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
                    'state_2'            => HrStaffInfoModel::STATE_RESIGN,
                    'red_dot_leave_date' => $this->flash_box_red_dot_resign_data_time,
                ]);
        } else {
            //在职状态
            if (!empty($params['staff_state']) && is_array($params['staff_state'])) {
                if (in_array(Enums::HRIS_WORKING_STATE_4, $params['staff_state'])) {
                    $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state=:state: AND hsi.wait_leave_state=:wait_leave_state:)",
                        [
                            'states'           => $params['staff_state'],
                            'state'            => Enums::HRIS_WORKING_STATE_1,
                            'wait_leave_state' => Enums::WAIT_LEAVE_STATE,
                        ]);
                } elseif (!in_array(Enums::HRIS_WORKING_STATE_4,
                        $params['staff_state']) && in_array(Enums::HRIS_WORKING_STATE_1, $params['staff_state'])) {
                    $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state != :wait_leave_state:",
                        ['states' => $params['staff_state'], 'wait_leave_state' => Enums::WAIT_LEAVE_STATE]);
                } else {
                    $builder->andWhere("hsi.state IN ({states:array})", ['states' => $params['staff_state']]);
                }
            }
        }


        if (!empty($params['store_id'])) {
            $builder->andWhere("hsi.sys_store_id = :store_id:", ['store_id' => $params['store_id']]);
        }

        //所属片区
        if (!empty($params['piece_id'])) {
            $storeIds = SysStoreModel::find([
                "manage_piece = :piece_id: and state = 1",
                "bind" => [
                    "piece_id" => $params['piece_id'],
                ],
                "columns" => "id",
            ])->toArray();
            $storeIds = array_column($storeIds, 'id');
            $builder->inWhere("hsi.sys_store_id", $storeIds);
        }

        //所属大区
        if (!empty($params['region_id'])) {
            $storeIds = SysStoreModel::find([
                "manage_region IN({region_id:array}) and state = 1",
                "bind" => [
                    "region_id" => $params['region_id'],
                ],
                "columns" => "id",
            ])->toArray();
            $storeIds = array_column($storeIds, 'id');
            $builder->inWhere("hsi.sys_store_id", $storeIds);
        }

        //所属部门
        if (!empty($params['department_id'])) {
            $departmentInfo = SysDepartmentModel::findFirst([
                "id = :department_id:",
                "bind" => [
                    "department_id" => $params['department_id'],
                ],
                "columns" => "id,ancestry_v3",
            ]);
            if (!empty($departmentInfo)) {
                $departmentIds = SysDepartmentModel::find([
                    "id = :department_id: or ancestry_v3 like :chain:",
                    "bind" => [
                        "department_id" => $params['department_id'],
                        "chain"         => $departmentInfo->ancestry_v3.'/%',
                    ],
                    "columns" => "id",
                ])->toArray();
                $departmentIdsArr = array_column($departmentIds, "id");
                if (!empty($departmentIdsArr)) {
                    $builder->inWhere("hsi.node_department_id", $departmentIdsArr);
                }
            }
        }

        return $builder;
    }

    /**
     * @description 过滤Payroll下的页签
     * @param $staff_info_id
     * @return array
     */
    public function filterProblemCategoryByStaffPermission($staff_info_id)
    {
        return [];
    }

    /**
     * @description 获取配置
     * @param $category_id
     * @return array
     */
    private function getConfig($category_id, $tab_id): array
    {
        $config = (new SettingEnvService())->getSetVal('store_staffs_gongzi_ho');

        if (CeoMailEnums::PAYROLL_TAB_KEY == $tab_id && isCountry()) {
            $listConfig = FlashBoxSettingModel::findFirst([
                "category_id = :category_id:",
                "bind" => [
                    "category_id" => $category_id,
                ],
            ]);
        } else {
            $listConfig = FlashBoxSettingModel::findFirst([
                "tab_id = :tab_id:",
                "bind" => [
                    "tab_id" => $tab_id,
                ],
            ]);
        }

        return [
            'store_staffs' => explode(',', $config),
            'setting' => $listConfig ? $listConfig->toArray(): [],
        ];
    }


    /**
     * 获取全部数据 tab key
     * @return string
     */
    public function getAllDataTabKey()
    {
        return CeoMailEnums::ALL_TAB_KEY;
    }

    /**
     * 获取指定分类的子类, 为空则返回自身
     * @param array $category_id_item
     * @return array $result
     */
    public function getSubCategoryIds(array $category_id_item)
    {
        $result = CeoMailProblemCategoryModel::find([
            'columns'    => "id",
            'conditions' => 'parent_id IN ({parent_ids:array}) AND display_status = :display_status: ',
            'bind'       => [
                'parent_ids'     => $category_id_item,
                'display_status' => CeoMailProblemCategoryModel::DISPLAY_STATUS_SHOW,
            ],
        ])->toArray();

        if (!empty($result)) {
            return array_column($result, 'id');
        }

        return $category_id_item;
    }

    /**
     * 获取员工反馈列表
     * @param array $ids
     * @return array $list
     */
    public function getFeedbackListByIds(array $ids)
    {
        if (empty($id_list)) {
            return [];
        }

        return MailToCeoModel::find([
            'columns'    => "id, content AS problem_desc, img_url AS problem_image",
            'conditions' => 'id IN ({ids:array}) ',
            'bind'       => ['ids' => $ids],
        ])->toArray();
    }

    /**
     * 时长转换: 秒 -> xx h xx min xx s
     * @param int $seconds
     * @return string $string '10h25min30s'
     */
    public function timeSecondsFormatConversion(int $seconds)
    {
        if (empty($seconds)) {
            return '';
        }

        $hour   = floor($seconds / 3600);
        $minute = floor(($seconds - $hour * 3600) / 60);
        $second = $seconds - $hour * 3600 - $minute * 60;

        $string = '';
        if ($hour > 0) {
            $string .= $hour.'h ';
        }

        if ($minute > 0) {
            $string .= $minute.'m ';
        }

        if ($second) {
            $string .= $second.'s ';
        }

        return trim($string);
    }

    /**
     * 获取未处理数量
     * @param $params
     * @return array
     * @throws Exception
     */
    public function getWaitingReplyProblemOrderCount($params)
    {
        $result = [];
        // [1] 获取当前工号tab访问权限
        $tab_permission_key = $this->getStaffTabPermissionKeys($params['user_id']);
        if (empty($tab_permission_key)) {
            return $result;
        }

        // [2] 获取每个tab的类别id
        $result = $this->getTabItemByKeys($tab_permission_key, true);
        foreach ($result as $index => $tab) {
            // [3] 获取每个类别id对应的待回复问题工单数
            if ($tab['source_tab_key'] == CeoMailEnums::ALL_TAB_KEY) {
                $_tab_category_list = $this->getProblemCategoryListByTabKey($tab['source_tab_key']);
                $_tab_category_id_item = array_column($_tab_category_list, 'category_id');

                $tab['waiting_reply_count'] = $this->getProblemOrderCountByCategoryIds($_tab_category_id_item, [CeoMailEnums::HAVE_NO_REPLY_STATUS, CeoMailEnums::HAVE_TIMED_OUT_SYS_STATUS]);

            } else {
                $_tab_category_list = $this->getProblemCategoryListByTabKey($tab['source_tab_key']);
                $_tab_category_id_item = array_column($_tab_category_list, 'category_id');

                $params['tab_key']        = $tab['source_tab_key'];
                $params['category_ids']   = $_tab_category_id_item;
                $params['problem_status'] = [CeoMailEnums::HAVE_NO_REPLY_STATUS, CeoMailEnums::HAVE_TIMED_OUT_SYS_STATUS];
                $tab['waiting_reply_count'] = $this->getPayrollOrderCountByCategoryIds($params);
            }

            unset($tab['source_tab_key']);

            // [4] 汇总
            $result[$index] = $tab;
        }
        return $result;
    }

    /**
     * 获取 指定类别 问题工单数
     * @param $params
     * @return int
     * @throws Exception
     */
    public function getPayrollOrderCountByCategoryIds($params)
    {
        //获取员工问题工单总记录数
        $params['is_red_dot_resign_data'] = true;
        $total = $this->getListQuery($params, true);

        return !empty($total) ? intval($total['count']) : 0;

    }

    /**
     * 获取 指定类别 问题工单数
     * 过滤 离职员工
     * @param array $category_id_item
     * @param array $problem_status
     *
     * @return int $count
     */
    public function getProblemOrderCountByCategoryIds(array $category_id_item, array $problem_status = [])
    {
        if (empty($category_id_item)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(*) as count');
        $builder->from(['cmspo' => CeoMailStaffProblemOrderModel::class]);
        $builder->innerJoin(HrStaffInfoModel::class, 'cmspo.staff_id = hsi.staff_info_id', 'hsi');
        $builder->where('hsi.state in ({state_1_3:array}) OR (hsi.state = :state_2: and hsi.leave_date >= :red_dot_leave_date:)',
            [
                'state_1_3'          => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
                'state_2'            => HrStaffInfoModel::STATE_RESIGN,
                'red_dot_leave_date' => $this->flash_box_red_dot_resign_data_time,
            ]);

        $builder->andwhere('cmspo.sys_category_id_v2 IN ({sys_category_id:array})',
            ['sys_category_id' => $category_id_item]);

        $builder->andwhere('cmspo.problem_status IN ({problem_status:array})',
            ['problem_status' => $problem_status]);

        $total = $builder->getQuery()->getSingleResult()->toArray();

        return !empty($total) ? intval($total['count']) : 0;
    }

    /**
     * 获取工单详情
     * @param $params
     * @return array
     */
    public function getProblemOrderDetail($params)
    {
        if (empty($params['problem_no'])) {
            return [];
        }

        $problem_no = $params['problem_no'];

        // 获取问题工单基本信息
        $base_info = $this->getProblemOrderBaseInfo($problem_no);
        if (empty($base_info)) {
            return [];
        }

        $categoryOne = CeoMailProblemCategoryRepository::getOne(['id' => $base_info['sys_category_id_v2']]);

        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($base_info['staff_id'],
            ['hire_type', 'node_department_id', 'sys_store_id', 'job_title', 'state', 'wait_leave_state']);
        // 获取问题工单分类
        $category_info              = $this->getProblemCategoryListByIds([$base_info['problem_category_v2']]);
        $base_info['category_name'] = $category_info[$base_info['problem_category_v2']] ?? '';

        $staff_feedback_list = $this->getCeoToMailInfo($problem_no);

        $sys_reply_list = $this->getMailReplyFromCeoInfo($problem_no);

        $sys_transfer_list = $this->getCeoMailTransferRecordList($problem_no);

        $sys_notice_list = $this->getCeoMailSysNoticeList($problem_no);

        $action_log_list = $this->getCeoMailActionLog($problem_no);

        // [5] 合并各渠道列表
        $list = array_merge($staff_feedback_list, $sys_reply_list, $sys_transfer_list, $sys_notice_list,$action_log_list);

        // [6] 按处理时间正序
        $create_times = array_column($list, 'create_time');
        array_multisort($create_times, SORT_ASC, $list);
        $dataList = [];
        // [7] 处理消息上下文处理时长格式
        if (!empty($list)) {
            $duration_translation   = self::$t->_('ceo_mail_005');
            foreach ($list as $key => $value) {
                $_tmp_val               = [];
                $interval_time          = '';
                $text_remark            = '';
                $_tmp_val['staff_name'] = $value['staff_name'].'('.$value['staff_id'].')';

                $closeData = [];
                //后台操作记录
                if (array_key_exists('action_log_id', $value)) {
                    //跟踪调查
                    if ($value['biz_type'] == CeoMailActionLogModel::BIZ_TYPE_FOLLOW) {
                        $_tmp_val['msg_obj']   = 'follow';
                        $value['text_content'] = self::$t->_('designate_responsible_person') . '-' . self::$t->_('follow_type_' . $value['follow_type']) . '-' . $value['follow_staff_name'] . '(' . $value['follow_staff_id'] . ')';
                    } elseif ($value['biz_type'] == CeoMailActionLogModel::BIZ_TYPE_CLOSE) {//关闭备注
                        $_tmp_val['msg_obj']   = 'close_remark';
                        $value['text_content'] = '';
                    }
                    $text_remark           = $value['content'];
                } elseif (array_key_exists('staff_msg_id', $value)) {
                    $_tmp_val['msg_obj'] = 'staff';
                } elseif (array_key_exists('sys_msg_id', $value)) {
                    $_tmp_val['msg_obj'] = 'sys';
                } elseif (array_key_exists('transfer_msg_id', $value)) {
                    $_tmp_val['msg_obj']   = 'transfer';
                    $value['text_content'] = $value['transfer_department_name'];
                    $text_remark           = $value['leave_message'] ?? '';
                } elseif (array_key_exists('auto_sys_msg_id', $value)) {
                    $_tmp_val['msg_obj']   = 'auto_sys';
                    $value['text_content'] = self::$t->_('ceo_mail_system_feedback_ask');
                    $_tmp_val['status']    = $value['status'];
                    $text_remark           = $value['remark'];
                    //系统侧，发起关闭工单，员工侧的 处理状态：1 已解决，2未解决，3超时未处理
                    if($value['status'] != CeoMailSysNoticeModel::STATUS_PENDING) {
                        $value['update_time'] = show_time_zone($value['update_time']);
                        $interval = strtotime($value['update_time']) - strtotime($value['create_time']);
                        $interval_time_close = '';
                        if (!empty($interval)) {
                            $interval_time_close = ' '.$duration_translation.': '.$this->timeSecondsFormatConversion($interval);
                        }
                        $closeData['staff_name'] = $base_info['staff_name'].'('.$base_info['staff_id'].')';
                        $closeData['msg_obj'] = 'staff';
                        $closeData['create_time']  = $value['update_time'].$interval_time_close;
                        $closeData['text_content'] = self::$t->_(CeoMailSysNoticeModel::$status_text[$value['status']]);
                    }
                }

                if (!empty($value['interval_time'])) {
                    $interval_time = ' '.$duration_translation.': '.$this->timeSecondsFormatConversion($value['interval_time']);
                }

                $_tmp_val['create_time']  = $value['create_time'].$interval_time;
                $_tmp_val['text_content'] = $value['text_content'];
                $_tmp_val['text_remark']  = $text_remark;
                $_tmp_val['image_item']   = !empty($value['image_item']) ? explode(',', $value['image_item']) : [];
                $_tmp_val['file_url']     = !empty($value['file_url']) ? $value['file_url'] : '';

                $dataList[] = $_tmp_val;

                //系统侧，发起关闭工单，员工侧的 处理状态：1 已解决，2未解决，3超时未处理
                if(!empty($closeData)) {
                    $dataList[] = $closeData;
                }
            }
        }

        if ($base_info['is_evaluate'] || $base_info['problem_status'] == 3) {
            $base_info['problem_status'] = 2;
        }

        $problem_order_close = [];
        foreach ($sys_notice_list as $key => $item) {
            $problem_order_close[$key]['create_time'] = $item['create_time'];
            $problem_order_close[$key]['text_remark'] = $item['remark'];
            $problem_order_close[$key]['staff_name']  = $item['staff_name'].'('.$item['staff_id'].')';
        }
        $is_follow_up = CeoMailFollowModel::ISSET_DATA;
        if(isCountry('TH') && $categoryOne['related_tab_id'] == CeoMailEnums::HRBP_TAB_KEY) {
            if(empty(CeoMailFollowRepository::getOne(['problem_no' => $base_info['problem_no']]))) {
                $is_follow_up = CeoMailFollowModel::NOT_ISSET_DATA;
            }
        } else {
            $is_follow_up = CeoMailFollowModel::NOT_DISPLAY_DATA;
        }


        $problem_order_info = [
            'is_replied'            => !!$base_info['first_reply_staff_id'] || $staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN,
            'problem_no'            => $base_info['problem_no'],
            'staff_id'              => $base_info['staff_id'],
            'staff_name'            => $base_info['staff_name'],
            'hire_type_text'        => !empty($staffInfo['hire_type']) ? static::$t->_('hire_type_'.$staffInfo['hire_type']) : '',//雇佣类型。
            'staff_mobile'          => $base_info['staff_mobile'],
            'category_name'         => $base_info['category_name'],

            'sys_category_id_v2'    => $base_info['sys_category_id_v2'],
            'store_name'            => $this->showStoreName($staffInfo['sys_store_id']),
            'department_name'       => $this->showDepartmentName($staffInfo['node_department_id']),
            'job_title_name'        => $this->showJobTitleName($staffInfo['job_title']),
            'staff_state_text'      => $this->showStaffStateName($staffInfo['state'], $staffInfo['wait_leave_state']),
            'staff_state'           => $staffInfo['state'],
            'problem_status'        => $base_info['problem_status'],
            'is_evaluate'           => $base_info['is_evaluate'],
            'staff_score'           => $base_info['staff_score'],
            'staff_evaluate'        => $base_info['staff_evaluate'] ? explode(',', $base_info['staff_evaluate']) : [],
            'staff_evaluate_remark' => $base_info['staff_evaluate_remark'],
            'evaluate_time'         => $base_info['evaluate_time'],
            'is_follow_up'          => $is_follow_up,
        ];

        return [
            'problem_order_info'  => $problem_order_info,
            'problem_order_item'  => $dataList,
            'problem_order_close' => $problem_order_close,
        ];
    }

    /**
     * 获取工单基本信息
     * @param $problem_no
     * @return array
     */
    public function getProblemOrderBaseInfo($problem_no)
    {
        $baseInfo = CeoMailStaffProblemOrderModel::findFirst([
            'conditions' => ' problem_no = :problem_no: ',
            'bind'       => [
                'problem_no' => $problem_no,
            ],
        ]);

        return !empty($baseInfo) ? $baseInfo->toArray() : [];
    }

    /**
     * 获取问题工单员工反馈列表
     * @param $problem_no
     * @return mixed
     */
    public function getCeoToMailInfo($problem_no)
    {
        return MailToCeoModel::find([
            'conditions' => ' problem_no = :problem_no: ',
            'bind'       => [
                'problem_no' => $problem_no,
            ],
            'columns'    => "id AS staff_msg_id, staff_id, staff_name, content AS text_content, img_url AS image_item, create_time",
        ])->toArray();
    }

    /**
     * 获取问题工单系统回复列表
     * @param $problem_no
     * @return mixed
     */
    public function getMailReplyFromCeoInfo($problem_no)
    {
        return MailReplyFromCeoModel::find([
            'conditions' => ' problem_no = :problem_no: ',
            'bind'       => [
                'problem_no' => $problem_no,
            ],
            'columns'    => "id AS sys_msg_id, staff_id, staff_name, content AS text_content, img_url AS image_item, file_url, create_time, reply_interval_time AS interval_time",
        ])->toArray();
    }

    /**
     * 获取问题工单系统移交列表
     * @param $problem_no
     * @return mixed
     */
    public function getCeoMailTransferRecordList($problem_no)
    {
        return CeoMailProblemTransferRecordModel::find([
            'conditions' => ' problem_no = :problem_no: ',
            'bind'       => [
                'problem_no' => $problem_no,
            ],
            'columns'    => "id AS transfer_msg_id, transfer_department_id, transfer_department_name, transfer_time AS create_time, staff_id, staff_name, transfer_interval_time AS interval_time, leave_message",
        ])->toArray();
    }

    /**
     * 获取问题工单系统移交列表
     * @param $problem_no
     * @return mixed
     */
    public function getCeoMailSysNoticeList($problem_no)
    {
        return CeoMailSysNoticeModel::find([
            'conditions' => ' problem_no = :problem_no: ',
            'bind'       => [
                'problem_no' => $problem_no,
            ],
            'order'      => 'id desc',
            'columns'    => "id AS auto_sys_msg_id,img_url as image_item, status, remark, create_time, staff_id, staff_name, interval_time, update_time",
        ])->toArray();
    }

    /**
     * 获取BP投诉跟进调查任务分配记录
     * @param $problem_no
     * @return mixed
     */
    public function getCeoMailActionLog($problem_no)
    {
        return CeoMailActionLogModel::find([
            'conditions' => ' problem_no = :problem_no: ',
            'bind'       => [
                'problem_no' => $problem_no,
            ],
            'order'      => 'id desc',
            'columns'    => "id AS action_log_id, biz_type,content,img_url as image_item, create_time,follow_staff_id,follow_staff_name,follow_type, staff_id, staff_name, update_time",
        ])->toArray();
    }

    /**
     * 回复工单
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function problemOrderReply($params)
    {
        $problem_no    = $params['problem_no'];
        $content       = $params['content'];
        $image_content = $params['image_item'] ? implode(',', $params['image_item']) : '';
        $file_url = $params['file_url'] ? $params['file_url'] : '';
        $staff_id      = $params['user_info']['id'];
        $staff_name    = $params['user_info']['name'];

        // [1] 验证工单是否存在
        $base_info = $this->getProblemOrderBaseInfo($problem_no);
        if (empty($base_info) || in_array($base_info['problem_status'], [2, 3, 4])) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        if(!$this->checkData($params)) {
            throw new ValidationException(self::$t->_('ceo_mail_check_data'));
        }

        $transferInfo = CeoMailProblemTransferRecordRepository::getTransferOne(['problem_no' => $problem_no]);

        // [2] 获取工单最近一条反馈信息的时间和ID
        $curr_th_date_time   = date('Y-m-d H:i:s');
        $reply_interval_time = strtotime($curr_th_date_time) - strtotime($base_info['latest_feedback_time']);
        $reply_interval_time = $reply_interval_time > 0 ? $reply_interval_time : 0;
        try {
            $db_back = $this->db_backyard;
            $db_back->begin();

            $reply_insert_data['mail_id']             = $base_info['latest_feedback_id'];
            $reply_insert_data['staff_id']            = $staff_id;
            $reply_insert_data['staff_name']          = $staff_name;
            $reply_insert_data['content']             = $content;
            $reply_insert_data['img_url']             = $image_content;
            $reply_insert_data['file_url']            = $file_url;
            $reply_insert_data['create_time']         = $curr_th_date_time;
            $reply_insert_data['problem_no']          = $problem_no;
            $reply_insert_data['reply_interval_time'] = $reply_interval_time;

            $db_back->insertAsDict('mail_reply_from_ceo', $reply_insert_data);

            //更新移交后 首次回复信息
            if (!empty($transferInfo) && empty($transferInfo['transfer_first_reply_staff_id'])) {
                $update_data['transfer_first_reply_staff_id']      = $staff_id;
                $update_data['transfer_first_reply_time']          = $curr_th_date_time;
                $interval_time = strtotime($curr_th_date_time) - strtotime($transferInfo['transfer_time']);
                $update_data['transfer_first_reply_interval_time'] = $interval_time > 0 ? $interval_time : 0 ;

                $db_back->updateAsDict("ceo_mail_problem_transfer_record", $update_data,
                    ["conditions" => 'id = ? ', "bind" => [$transferInfo['id']]]);
            }


            if (!empty($base_info['first_reply_staff_id'])) {
                //非首次回复
                $update_data['last_deal_staff_id']  = $staff_id;
                $update_data['last_deal_time']      = $curr_th_date_time;
                $update_data['problem_status']      = CeoMailEnums::HAVE_REPLIED_STATUS;
                $update_data['is_read_sys_reply']   = 0;
                $update_data['last_reply_staff_id'] = $staff_id;
                $update_data['last_reply_time']     = $curr_th_date_time;
            } else {
                // 首次回复
                $first_reply_interval_time = strtotime($curr_th_date_time) - strtotime($base_info['create_time']);
                $first_reply_interval_time = $first_reply_interval_time > 0 ? $first_reply_interval_time : 0;

                $update_data['first_reply_staff_id']      = $staff_id;
                $update_data['last_deal_staff_id']        = $staff_id;
                $update_data['first_reply_time']          = $curr_th_date_time;
                $update_data['last_deal_time']            = $curr_th_date_time;
                $update_data['problem_status']            = CeoMailEnums::HAVE_REPLIED_STATUS;
                $update_data['is_read_sys_reply']         = 0;
                $update_data['first_reply_interval_time'] = $first_reply_interval_time;
                $update_data['last_reply_staff_id']       = $staff_id;
                $update_data['last_reply_time']           = $curr_th_date_time;
            }
            $update_data['feedback_waiting_interval_time'] = 0;//员工提问等待时间 回复了就 清零


            $db_back->updateAsDict("ceo_mail_staff_problem_order", $update_data,
                ["conditions" => 'problem_no = ? ', "bind" => [$problem_no]]);

            //回复次数+1
            $sql         = "update ceo_mail_staff_problem_order set reply_num = reply_num + 1 where problem_no = :problem_no";
            $query_param = [
                'problem_no' => $problem_no,
            ];
            $db_back->execute($sql, $query_param);

            $updateFeedBack['is_reply'] = 1;
            $db_back->updateAsDict("mail_to_ceo", $updateFeedBack, ["conditions" => 'problem_no = ?', "bind" => [$problem_no]]);

            $db_back->commit();
            $lang = (new StaffService())->getAcceptLanguage($base_info['staff_id']);
            // push backyard
            $push_param = [
                'staff_id'        => $base_info['staff_id'],
                'message_content' => BaseService::getTranslation($lang)->_(CeoMailEnums::SYS_REPLY_STAFF_PUSH_CONTENT_KEY),
            ];

            $this->sendPushToStaffFlashBox($push_param);

            $res['code']    = ErrCode::SUCCESS;
            $res['message'] = 'SUCCESS';
            $res['data']    = [];
            return $res;
        } catch (\Exception $e) {
            $db_back->rollback();
            $this->logger->write_log('CEO MAIL - 员工问题工单回复'.$e->getMessage(), 'notice');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 回复完成，发送push
     * @param $params
     * @return bool
     */
    public function sendPushToStaffFlashBox($params)
    {
        //发push
        $data   = [
            "staff_info_id"   => $params['staff_id'],  //推送人员工ID
            "src"             => "backyard",      //1:'kit'; 2:'backyard','c';
            "message_title"   => !empty($params['title']) ? $params['title'] : "Flash Box",  //标题
            "message_content" => $params['message_content'] ?? '',//内容
            'message_scheme'  => "flashbackyard://fe/html?url=".urlencode(env('by_h5_endpoint').'flash-box?from=feedback'),
        ];
        $client = new ApiClient('bi_rpc', '', 'push_to_staff');
        $client->setParamss($data);
        $res = $client->execute();
        if (!$res) {
            $this->getDI()->get('logger')->info('send Flash box message push to backyard  return false :'.json_encode($data));
            return false;
        }
        return true;
    }

    public function getTransferProblemCategoryItem()
    {
        $list          = [];
        $category_item = $this->getTransferProblemCategoryKey();
        foreach ($category_item as $key => $value) {
            $list[] = [
                'category_id'   => $value,
                'category_name' => self::$t->_($key),
            ];
        }

        return $list;
    }

    /**
     * $problem_no
     * @return array|array[]
     * @throws ValidationException
     */
    public function getTransferProblemCategoryItemV2($problem_no)
    {
        $list          = [
            'default_category' => [],
            'ic_category'      => [],
        ];
        $category_item = $this->getTransferProblemCategoryKey();
        if (!empty($problem_no)) {
            $orderInfo = $this->getProblemOrderBaseInfo($problem_no);
            if (empty($orderInfo)) {
                throw new ValidationException(self::$t->_('data_error'));
            }
            $staffInfo = (new StaffInfoService())->getStaffInfoByIdv4($orderInfo['staff_id'], 'hire_type');
            $is_ic     = in_array($staffInfo['hire_type'], HrStaffInfoModel::$agentTypeTogether);
        }
        $categoryInfo    = CeoMailProblemCategoryRepository::getCategoryInfo('id,use_type', 'id in ({ids:array})',
            ['ids' => array_values($category_item)]);
        $categoryUseType = array_column($categoryInfo, 'use_type', 'id');
        foreach ($category_item as $key => $value) {
            if ($categoryUseType[$value] == CeoMailProblemCategoryModel::USE_TYPE_ALL) {
                $list['default_category'][] = [
                    'category_id'   => $value,
                    'category_name' => self::$t->_($key),
                ];
                $list['ic_category'][]      = [
                    'category_id'   => $value,
                    'category_name' => self::$t->_($key),
                ];
            } elseif ($categoryUseType[$value] == CeoMailProblemCategoryModel::USE_TYPE_PERMANENT) {
                $list['default_category'][] = [
                    'category_id'   => $value,
                    'category_name' => self::$t->_($key),
                ];
            } elseif ($categoryUseType[$value] == CeoMailProblemCategoryModel::USE_TYPE_IC) {
                $list['ic_category'][] = [
                    'category_id'   => $value,
                    'category_name' => self::$t->_($key),
                ];
            }
        }

        if (isset($is_ic)) {
            return $is_ic ? $list['ic_category'] : $list['default_category'];
        }

        return $list;
    }

    /**
     * @param $params
     * @param $userInfo
     * @return true
     * @throws ValidationException
     */
    public function closeRemark($params, $userInfo): bool
    {
        $orderInfo = $this->getProblemOrderBaseInfo($params['problem_no']);
        if (empty($orderInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        if (!in_array($orderInfo['problem_status'],
            [CeoMailEnums::HAVE_COMPLETED_STATUS, CeoMailEnums::HAVE_TIMED_OUT_STATUS])) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $assignFollowLogModel              = new CeoMailActionLogModel();
        $assignFollowLogModel->problem_no  = $orderInfo['problem_no'];
        $assignFollowLogModel->order_id    = $orderInfo['id'];
        $assignFollowLogModel->biz_type    = CeoMailActionLogModel::BIZ_TYPE_CLOSE;
        $assignFollowLogModel->content     = $params['content'];
        $assignFollowLogModel->img_url     = $params['image_item'] ? implode(',', $params['image_item']) : '';
        $assignFollowLogModel->staff_id    = $userInfo['id'];
        $assignFollowLogModel->staff_name  = $userInfo['name'];
        $assignFollowLogModel->create_time = date('Y-m-d H:i:s');
        return $assignFollowLogModel->save();
    }



    public function getTransferProblemCategoryKey()
    {
        return CeoMailEnums::PROBLEM_ORDER_TRANSFER_CATEGORY_ITEM;
    }

    /**
     * 工单移交
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function problemOrderTransfer($params)
    {
        $problem_no  = $params['problem_no'];
        $content     = $params['content'];
        $category_id = $params['category_id'];
        $user_info   = $params['user_info'];

        if (!in_array($category_id, array_values($this->getTransferProblemCategoryKey()))) {
            throw new ValidationException('category_id param error');
        }

        if(!$this->checkData($params)) {
            throw new ValidationException(self::$t->_('ceo_mail_check_data'));
        }

        // [1] 问题工单验证
        $base_info = $this->getProblemOrderBaseInfo($problem_no);
        if (empty($base_info) || in_array($base_info['problem_status'], [2, 3])) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $curr_th_time = date('Y-m-d H:i:s');

        // 与上一次动作间隔时长: 上一次的动作[员工反馈/系统回复/系统移交]
        $latest_action_time = $base_info['last_deal_time'];
        if ($base_info['latest_feedback_time'] > $latest_action_time) {
            $latest_action_time = $base_info['latest_feedback_time'];
        }
        $transfer_interval_time = strtotime($curr_th_time) - strtotime($latest_action_time);
        $transfer_interval_time = $transfer_interval_time > 0 ? $transfer_interval_time : 0;

        // [2] 问题分类信息获取
        $fields        = "parent_id,related_department_id, related_department_name";
        $category_info = $this->getProblemCategoryInfo($category_id, $fields);
        if ($category_info['parent_id'] && empty($category_info['related_department_name'])) {
            $category_info = $this->getProblemCategoryInfo($category_info['parent_id'], $fields);
        }

        try {
            $db_back = $this->db_backyard;
            $db_back->begin();

            // [3] 添加移交记录
            $transfer_data['problem_no']               = $problem_no;
            $transfer_data['mail_id']                  = $base_info['latest_feedback_id'];
            $transfer_data['transfer_category_id']     = $category_id;
            $transfer_data['leave_message']            = $content;
            $transfer_data['transfer_time']            = $curr_th_time;
            $transfer_data['transfer_department_id']   = $category_info['related_department_id'];
            $transfer_data['transfer_department_name'] = $category_info['related_department_name'];
            $transfer_data['transfer_interval_time']   = $transfer_interval_time;
            $transfer_data['staff_id']                 = $user_info['id'];
            $transfer_data['staff_name']               = $user_info['name'];

            //移交前所属分类
            $transfer_data['before_transfer_category_id'] = $base_info['sys_category_id_v2'];

            $db_back->insertAsDict('ceo_mail_problem_transfer_record', $transfer_data);


            // [4] 更新问题工单基本信息
            $update_order_status_data['last_deal_staff_id']                 = $user_info['id'];
            $update_order_status_data['last_deal_time']                     = $curr_th_time;
            $update_order_status_data['sys_category_id_v2']                 = $category_id;

            $update_order_status_data['problem_status']                     = CeoMailEnums::HAVE_NO_REPLY_STATUS;//移交后，全都是待回复
            //最新的一次移交 需要 重置 移交后首次回复人信息
            $update_order_status_data['transfer_first_reply_staff_id']      = 0;
            $update_order_status_data['transfer_first_reply_time']          = null;
            $update_order_status_data['transfer_first_reply_interval_time'] = 0;

            $db_back->updateAsDict("ceo_mail_staff_problem_order", $update_order_status_data,
                ["conditions" => 'problem_no = ?', "bind" => [$problem_no]]);

            //移交次数+1
            $sql = "update ceo_mail_staff_problem_order set transfer_num = transfer_num + 1  where problem_no = :problem_no";
            $query_param = [
                'problem_no' => $problem_no,
            ];
            $db_back->execute($sql, $query_param);

            $db_back->commit();

            $res['code']    = ErrCode::SUCCESS;
            $res['message'] = 'SUCCESS';
            $res['data']    = [];

            return $res;
        } catch (\Exception $e) {
            $db_back->rollback();
            $this->logger->write_log('CEO MAIL - 员工问题工单移交 '.$e->getMessage(), 'notice');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 获取分类信息
     * @param int $category_id
     * @param string $fields
     * @return array
     */
    public function getProblemCategoryInfo(int $category_id, string $fields = '')
    {
        if (empty($category_id)) {
            return [];
        }

        $get_fields = $fields ? $fields : '*';

        $categoryInfo = CeoMailProblemCategoryModel::findFirst([
            'conditions' => ' id = :category_id: ',
            'bind'       => [
                'category_id' => $category_id,
            ],
            'columns'    => $get_fields,
        ]);

        return !empty($categoryInfo) ? $categoryInfo->toArray() : [];
    }


    /**
     * 关闭对话
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function problemOrderCloseSession($params)
    {
        $problem_no = $params['problem_no'];
        $remark     = $params['remark'];
        $staff_id   = $params['user_info']['id'];
        $staff_name = $params['user_info']['name'];

        // [1] 验证工单是否存在
        $base_info = $this->getProblemOrderBaseInfo($problem_no);
        if (empty($base_info) || in_array($base_info['problem_status'], [2, 3, 4])) {
            throw new ValidationException(self::$t->_('ceo_mail_close_session_notice_1'));
        }

        $staffInfo = StaffInfoRepository::getInfoByStaffInfoId($base_info['staff_id']);
        $staff_is_resign = $staffInfo->state == HrStaffInfoModel::STATE_RESIGN;

        // [4.1] 是否是首次回复
        if (!$staff_is_resign && empty($base_info['first_reply_staff_id'])) {
            throw new ValidationException(self::$t->_('ceo_mail_close_session_notice_2'));
        }

        // [2] 获取工单最近一条反馈信息的时间和ID
        $curr_th_date_time   = date('Y-m-d H:i:s');
        $reply_interval_time = strtotime($curr_th_date_time) - strtotime($base_info['latest_feedback_time']);
        $reply_interval_time = $reply_interval_time > 0 ? $reply_interval_time : 0;

        try{
            $db_back = $this->db_backyard;
            $db_back->begin();
            // [3] 创建系统消息
            $reply_insert_data['mail_id']       = $base_info['latest_feedback_id'];
            $reply_insert_data['staff_id']      = $staff_id;
            $reply_insert_data['staff_name']    = $staff_name;
            $reply_insert_data['content']       = 'ceo_mail_system_feedback_ask';
            $reply_insert_data['remark']        = $remark;
            $reply_insert_data['create_time']   = $curr_th_date_time;
            $reply_insert_data['problem_no']    = $problem_no;
            $reply_insert_data['interval_time'] = $reply_interval_time;
            $reply_insert_data['img_url']       = !empty($params['image_item']) ? implode(',', $params['image_item']) : null;

            $db_back->insertAsDict('ceo_mail_sys_notice', $reply_insert_data);
            $sys_notice_id = $db_back->lastInsertId();

            // [4] 更新问题工单状态: 回复状态, 回复时间, 回复人

            $update_data['last_deal_staff_id']  = $staff_id;
            $update_data['last_deal_time']      = $curr_th_date_time;
            $update_data['problem_status']      = $staff_is_resign ? CeoMailEnums::HAVE_COMPLETED_STATUS : CeoMailEnums::HAVE_CLOSING_STATUS;
            $update_data['is_read_sys_reply']   = 0;
            $update_data['last_reply_staff_id'] = $staff_id;
            $update_data['last_reply_time']     = $curr_th_date_time;

            $db_back->updateAsDict("ceo_mail_staff_problem_order", $update_data,
                ["conditions" => 'problem_no = ?', "bind" => [$problem_no]]);

            // [5] 更新工单对应的员工反馈列表状态
            $updateFeedBack['is_reply'] = 1;
            $db_back->updateAsDict("mail_to_ceo", $updateFeedBack, ["conditions" => 'problem_no = ?', "bind" => [$problem_no]]);

            $db_back->commit();
            if (!$staff_is_resign) {
                //发push
                $problem_category_item = $this->getProblemCategoryListByIds([$base_info['sys_category_id_v2']]);

                $lang    = (new StaffService())->getAcceptLanguage($base_info['staff_id']);
                $title   = BaseService::getTranslation($lang)->_(CeoMailEnums::SYS_REPLY_STAFF_CLOSING_TITLE_KEY);
                $content = BaseService::getTranslation($lang)->_(CeoMailEnums::SYS_REPLY_STAFF_CLOSING_CONTENT_KEY, [
                    'date'             => date('Y-m-d', strtotime($base_info['create_time'])),
                    'problem_category' => $problem_category_item[$base_info['sys_category_id_v2']] ?? '',
                ]);

                //发送签字消息
                $add_message_param = [
                    'staff_info_ids_str' => $base_info['staff_id'],
                    'staff_users'        => [['id' => $base_info['staff_id']]],
                    'add_userid'         => $staff_id,
                    'message_title'      => $title,
                    'message_content'    => $this->formatUrlTemplate($problem_no),
                    'category'           => -1,
                    'related_id'         => $sys_notice_id,
                ];

                $apiClient = new ApiClient('bi_rpc', '', 'add_kit_message', 'th');
                $apiClient->setParamss($add_message_param);
                $re = $apiClient->execute();
                $this->logger->info([
                    'res'               => $re,
                    'func'              => 'send_close_session_message',
                    'add_message_param' => $add_message_param,
                ]);
            }

            $res['code']    = ErrCode::SUCCESS;
            $res['message'] = 'SUCCESS';
            $res['data']    = [];
            return $res;
        } catch (\Exception $e) {
            $db_back->rollback();
            $this->logger->write_log('CEO MAIL - 员工问题工单移交 '.$e->getMessage(), 'notice');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    //格式话 消息详情
    public function formatUrlTemplate($problem_no)
    {
        $pathName = env('by_h5_endpoint') . 'flash-box-msg';
        $msg_url = $pathName."?problem_no=$problem_no";
        $tpl = <<<EOF
<meta http-equiv="X-Frame-Options" content="SAMEORIGIN" />
<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' />
<div style='postion:fixed;left:0;top:0;width:100%;height:100%'>
<iframe src='{$msg_url}' width='100%' height='100%' scrolling='no' frameborder='0'>
</iframe></div>
EOF;
        return $tpl;
    }

    /**
     * 导出
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function exportProblemOrder($params)
    {
        $params['is_check_data_num'] = true;
        $result = $this->getStaffProblemOrderList($params);
        if($result['total'] > self::LIMIT_DOWNLOAD_NUM) {
            throw new ValidationException(self::$t->_('file_download_limit',['num' => self::LIMIT_DOWNLOAD_NUM]));
        }
        unset($params['is_check_data_num']);

        try{
            //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
            $action_name = "ceo_mail".HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR."main";
            $headerData = $this->request->getHeaders();
            $params['From'] = isset($headerData['From']) && $headerData['From'] === 'fbi' ? $headerData['From'] : '';

            //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
            $file_name = $params['file_name'];
            unset($params['file_name']);
            $result = (new ExcelService())->insertTask($params['user_id'], $action_name, $params, 0, $file_name);
            if($result['code'] != 1) {
                return $result;
            }
            if ($params['From'] == 'fbi') {
                (new BllService())->addFbiExportMessage(
                    $params['user_id'],
                    'hcm_ceo_mail_export_main',
                    $file_name,
                    $result['data']['task_id']
                );
            }
            return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => ''];
        } catch (Exception $e) {
            $this->logger->write_log('FLASH BOX-异步导出，出现异常, 原因是' . $e->getMessage(), 'error');
            $code    = ErrCode::SYSTEM_ERROR;
            $message = $e->getMessage();
        }
        return ['code' => $code, 'message' => $message, 'data'    => ''];
    }

    /**
     * 获取tab 名称
     * @return array
     */
    public function getCategoryTabName()
    {
        $tabName = CeoMailProblemCategoryRepository::getCategoryInfo('id,related_department_name', ' parent_id = :parent_id:', ['parent_id' => 0]);
        $tabNameToId = array_column($tabName, 'related_department_name', 'id');

        $subCategoryList = CeoMailProblemCategoryRepository::getCategoryInfo('id,related_department_name,parent_id', ' parent_id != :parent_id:', ['parent_id' => 0]);

        $data = [];
        foreach ($subCategoryList as $oneData) {
            $data[$oneData['id']] = empty($oneData['related_department_name']) ? ($tabNameToId[$oneData['parent_id']] ?? '') : $oneData['related_department_name'];
        }

        return $data;
    }

    /**
     *
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function checkData($params)
    {
        $tab_key = $this->getTabKeyByEncryptKey($params['tab_key']);

        $category_list = $this->getProblemCategoryListByTabKey($tab_key);

        // [1] 验证工单是否存在
        $base_info = $this->getProblemOrderBaseInfo($params['problem_no']);
        if (empty($base_info)) {
            throw new ValidationException(self::$t->_('data_error'));
        }
        $category_ids = empty($category_list) ? [] : array_column($category_list, 'category_id');

        // 泰国，payroll下 存在二级页签，以分类为 页签，特殊处理
        if($tab_key == CeoMailEnums::PAYROLL_TAB_KEY && isCountry()) {
            if(empty($params['payroll_category_id'])) {
                throw new ValidationException('payroll_category_id is not empty');
            }

            if(!in_array($params['payroll_category_id'], $category_ids)) {
                throw new ValidationException('payroll_category_id is error');
            }

            return $base_info['sys_category_id_v2'] == $params['payroll_category_id'] ? true : false;

        }

        return in_array($base_info['sys_category_id_v2'], $category_ids) ? true : false;
    }

    /**
     * 获取可选hrbp
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getBpList($params)
    {
        $orderInfo = $this->getProblemOrderBaseInfo($params['problem_no']);
        if (empty($orderInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($orderInfo['staff_id']);
        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('staff_id_not_found'));
        }

        if ($params['type'] == HrStaffManageDepartmentModel::TYPE_STAFF) {
            //员工管理管辖数据权限：查询管辖指定 【工号（被管辖）】，指定角色的的工号
            $list = $this->getStaffHRBP($staffInfo['staff_info_id']);
        } else {
            $where['not_leave'] = HrStaffInfoModel::STATE_RESIGN;
            $where['formal']    = [HrStaffInfoModel::FORMAL_1];
            $where['roles']     = [RolesModel::ROLES_HR_BP];
            $list               = (new HrStaffInfoPositionRepository())->getStaffList($where,
                ['hsi.staff_info_id as staff_id', 'hsi.name']);
        }
        return ['list' => $list];
    }

    /**
     * 审批流管辖范围HRBP
     * @param $staff_info_id
     * @return array|mixed
     */
    public function getStaffHRBP($staff_info_id)
    {
        //rpc调用
        $client = new ApiClient('by', '', 'get_staff_hrbp', self::$language);
        $result = $client->withParam(['staff_info_id' => $staff_info_id])->execute();
        if (empty($result)) {
            return [];
        }
        $managerStaffIds = explode(',', $result);
        //去除 不接受转岗消息名单
        $notice_black_list = (new SettingEnvService())->getSetVal('notice_black_list', ',');
        if (!empty($notice_black_list)) {
            $managerStaffIds = array_values(array_diff($managerStaffIds, $notice_black_list));
        }
        if (empty($managerStaffIds)) {
            return [];
        }
        $where['on_job_wait'] = HrStaffInfoModel::STATE_ON_JOB;
        $where['formal']      = [HrStaffInfoModel::FORMAL_1];
        $where['roles']       = [RolesModel::ROLES_HR_BP];
        $where['staff_ids']   = $managerStaffIds;
        return (new HrStaffInfoPositionRepository())->getStaffList($where,
            ['hsi.staff_info_id as staff_id', 'hsi.name']);
    }


    /**
     * @deprecated
     * 获取 员工管理管辖范围中 管辖指定工号的 指定角色工号
     * @param $staffInfoId
     * @param $roles
     * @return array|mixed
     */
    public function getManageStaffList($staffInfoId, $roles)
    {
        if (empty($roles) || empty($staffInfoId)) {
            return [];
        }
        $managerStaffIds = (new StaffService())->getRoleManageStaffList($staffInfoId);

        //去除 不接受转岗消息名单
        $notice_black_list = (new SettingEnvService())->getSetVal('notice_black_list', ',');
        if (!empty($notice_black_list)) {
            $managerStaffIds = array_values(array_diff($managerStaffIds, $notice_black_list));
        }

        if (empty($managerStaffIds)) {
            return [];
        }

        $where['on_job_wait'] = HrStaffInfoModel::STATE_ON_JOB;
        $where['formal']      = [HrStaffInfoModel::FORMAL_1];
        $where['roles']       = $roles;
        $where['staff_ids']   = $managerStaffIds;
        return (new HrStaffInfoPositionRepository())->getStaffList($where,
            ['hsi.staff_info_id as staff_id', 'hsi.name']);
    }

    /**
     * 添加投诉跟进
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function addFollow($params)
    {
        $orderInfo = $this->getProblemOrderBaseInfo($params['problem_no']);
        if (empty($orderInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $category = CeoMailProblemCategoryRepository::getOne(['id' => $orderInfo['sys_category_id_v2']]);
        if (empty($category)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        //只有 HRBP 下的分类才可以，跟进
        if ($category['related_tab_id'] != CeoMailEnums::HRBP_TAB_KEY) {
            throw new ValidationException(self::$t->_('ceo_mail_check_data'));
        }

        //检验一下，follow_staff_id 是否是 管辖的hrbp
        $bpList = $this->getBpList($params);
        if (empty($bpList['list'])) {
            throw new ValidationException(self::$t->_('follow_bp_not_manager'));
        }
        $hrBpStaffId = array_column($bpList['list'], 'staff_id');
        if (!in_array($params['follow_staff_id'], $hrBpStaffId)) {
            throw new ValidationException(self::$t->_('follow_bp_not_manager'));
        }

        $followInfo = CeoMailFollowRepository::getOne(['problem_no' => $params['problem_no']]);
        if (!empty($followInfo)) {
            throw new ValidationException(self::$t->_('isset_follow'));//已经存在跟进，不可以再次跟进
        }

        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($orderInfo['staff_id']);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('staff_id_not_found'));
        }
        $storeInfo = [];
        if ($staffInfo['sys_store_id'] != GlobalEnums::HEAD_OFFICE_ID) {
            $storeInfo = (new SysStoreRepository())->getStoreById($staffInfo['sys_store_id']);
        }

        $db = BackyardBaseModel::beginTransaction($this);
        try {
            $data['order_id']           = $orderInfo['id'];
            $data['problem_no']         = $orderInfo['problem_no'];
            $data['staff_info_id']      = $orderInfo['staff_id'];
            $data['node_department_id'] = $staffInfo['node_department_id'];
            $data['job_title']          = $staffInfo['job_title'];
            $data['mobile']             = $staffInfo['mobile'];
            $data['hire_type']          = $staffInfo['hire_type'];
            $data['region_id']          = !empty($storeInfo['manage_region']) ? intval($storeInfo['manage_region']) : 0;
            $data['piece_id']           = !empty($storeInfo['manage_piece']) ? intval($storeInfo['manage_piece']) : 0;
            $data['store_id']           = $staffInfo['sys_store_id'];
            $data['follow_staff_id']    = $params['follow_staff_id'];
            $data['follow_type']        = $params['type'];
            $data['remark']             = empty($params['remark']) ? '' : $params['remark'];
            $data['operate_id']         = $params['user_id'];
            $data['status']             = CeoMailFollowModel::STATUS_PENDING;
            $data['order_created_at']   = $orderInfo['create_time'];//当地时区
            $result = $this->getDI()->get('db_backyard')->insertAsDict('ceo_mail_follow', $data);
            if (!$result) {
                throw new ValidationException(self::$t->_('db_update_fail'));
            }
            $follow_id                               = $this->getDI()->get('db_backyard')->lastInsertId();
            $followStaff                             = StaffInfoRepository::getInfoByStaffInfoId($data['follow_staff_id']);
            $assignFollowLogModel                    = new CeoMailActionLogModel();
            $assignFollowLogModel->problem_no        = $orderInfo['problem_no'];
            $assignFollowLogModel->order_id          = $orderInfo['id'];
            $assignFollowLogModel->biz_type          = CeoMailActionLogModel::BIZ_TYPE_FOLLOW;
            $assignFollowLogModel->follow_id         = $follow_id;
            $assignFollowLogModel->content           = $data['remark'];
            $assignFollowLogModel->follow_staff_id   = $data['follow_staff_id'];
            $assignFollowLogModel->follow_staff_name = $followStaff->name ?? '';
            $assignFollowLogModel->follow_type       = $data['follow_type'];
            $assignFollowLogModel->staff_id          = $params['user_id'];
            $assignFollowLogModel->staff_name        = $params['user_name'];
            $assignFollowLogModel->create_time       = date('Y-m-d H:i:s');
            $assignFollowLogModel->save();
            $db->commit();
        }catch (Exception $e){
            $db->rollBack();
            throw $e;
        }

        $lang = (new StaffService())->getAcceptLanguage($data['follow_staff_id']);
        // push backyard
        $push_param = [
            'staff_id'        => $data['follow_staff_id'],
            'title'           => BaseService::getTranslation($lang)->_('hr_probation_field_msg_notice'),
            'message_content' => BaseService::getTranslation($lang)->_('flash_box_follow_push'),
        ];

        $this->sendPushToStaffFlashBoxFollow($push_param);

        return true;
    }

    /**
     * 查询列表
     * @param $params
     * @return mixed
     */
    public function followList($params)
    {
        $params['page_num']  = empty($params['page_num']) ? 1 : $params['page_num'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];

        //获取员工角色
        $staffRoles = HrStaffInfoPositionRepository::getHrStaffRoles($params['user_id']);

        $data['total'] = 0;
        $data['list']  = [];

        //超管不走数据权限
        $limitFlag = true;
        if (in_array($params['user_id'], explode(',', env('sa_id')))) {
            $limitFlag = false;//不限制
        }
        // HR Management[17]、HRIS管理员[41]、超级管理员[99]、HR系统管理员[115]，hrbp, 可以查看数据
        if (!array_intersect(RolesModel::$staffCeoDataPermissions, $staffRoles) && $limitFlag) {
            return $data;
        }
        // hrbp 需要走管辖范围的数据,
        if(!array_intersect(RolesModel::$staffCeoSuperPermissions, $staffRoles) && array_intersect(RolesModel::$staffCeoDataLimitPermissions, $staffRoles) && $limitFlag) {
            $permissionSql = $this->getStaffDataPermissionSql($params['user_id']);
            //不为空，一定走的是hrbp 角色，就得走管辖
            if(!empty($permissionSql) && $limitFlag) {
                if(empty($permissionSql['builder_sql']) || empty($permissionSql['builder_bind'])) {
                    return $data;
                }
                $params['permission_sql'] = $permissionSql;
            }
        }

        $total         = $this->getFollowQuery($params, true);
        $data['total'] = !empty($total) ? intval($total['count']) : 0;

        if (isset($params['is_check_data_num']) && $params['is_check_data_num'] == true) {
            return $data;
        }

        if($data['total'] == 0) {
            return $data;
        }

        $list = $this->getFollowQuery($params);
        if ($list) {
            $list = $this->formatFollowList($list);
        }

        $data['list'] = $list;

        return $data;
    }

    /**
     * 查询
     * @param $params
     * @param bool $isCount
     * @return mixed
     */
    public function getFollowQuery($params, $isCount = false)
    {
        $columns = [
            'cmf.id',
            'cmf.order_id',
            'cmf.problem_no',
            'cmf.staff_info_id',
            'hsi.name',
            'hsi.state',
            'hsi.hire_type',
            'hsi.wait_leave_state',
            'hsi.node_department_id',
            'hsi.job_title',
            'hsi.sys_store_id',
            'hsi.mobile',
            'cmf.order_created_at',
            "DATE_FORMAT(CONVERT_TZ(cmf.created_at, '+00:00', '{$this->timeZone}'), '%Y-%m-%d %H:%i:%s') as created_at",
            'cmf.follow_staff_id',
            'cmf.status',
            'cmf.fix_result',
            'cmf.category',
            'cmf.fix_time',
            'cmf.follow_message',
        ];
        if ($isCount) {
            $columns = 'count(*) as count';
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['cmf' => CeoMailFollowModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'cmf.staff_info_id = hsi.staff_info_id', 'hsi');
        $builder = $this->getFollowBuilderWhere($builder, $params);
        if ($isCount) {
            return $builder->getQuery()->getSingleResult()->toArray();
        }

        $builder->limit($params['page_size'], $params['page_size'] * ($params['page_num'] - 1));

        $builder->orderBy('cmf.id desc');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getFollowBuilderWhere($builder, $params)
    {
        //员工id
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere('hsi.staff_info_id LIKE :staff_info_id:',
                ['staff_info_id' => '%' . $params['staff_info_id'] . '%']);
        }

        //在职状态
        if (!empty($params['states'])) {
            if (in_array(Enums::HRIS_WORKING_STATE_4, $params['states'])) {
                $builder->andWhere("hsi.state IN ({states:array}) OR (hsi.state=:state: AND hsi.wait_leave_state=:wait_leave_state:)",
                    [
                        'states'           => $params['states'],
                        'state'            => Enums::HRIS_WORKING_STATE_1,
                        'wait_leave_state' => Enums::WAIT_LEAVE_STATE,
                    ]);
            } elseif (!in_array(Enums::HRIS_WORKING_STATE_4, $params['states']) && in_array(Enums::HRIS_WORKING_STATE_1,
                    $params['states'])) {
                $builder->andWhere("hsi.state IN ({states:array}) AND hsi.wait_leave_state != :wait_leave_state:",
                    ['states' => $params['states'], 'wait_leave_state' => Enums::WAIT_LEAVE_STATE]);
            } else {
                $builder->andWhere("hsi.state IN ({states:array})", ['states' => $params['states']]);
            }
        }

        //搜索部门 或子部门
        if (!empty($params['department_id'])) {
            //不包含子部门
            if (empty($params['is_sub_department'])) {
                $builder->andWhere('hsi.node_department_id = :node_department_id:',
                    [
                        'node_department_id' => $params['department_id'],
                    ]);
            } else {//包含子部门
                $depServer = new SysDepartmentService();
                $subIds    = $depServer->getChildrenListByDepartmentId($params['department_id'], true);
                $subIds[]  = $params['department_id'];
                $builder->inWhere('hsi.node_department_id', $subIds);
            }
        }

        //大区、片区
        $builder = $this->getWhereByRegionPiece($builder, $params);

        //网点
        if (!empty($params['store_id'])) {
            $builder->andWhere('hsi.sys_store_id in ({store_id:array})', ['store_id' => $params['store_id']]);
        }

        //处理状态
        if (!empty($params['status'])) {
            $builder->andWhere('cmf.status in ({status:array})', ['status' => $params['status']]);
        }

        //处理结果
        if (!empty($params['fix_result'])) {
            $builder->andWhere('cmf.fix_result in ({fix_result:array})', ['fix_result' => $params['fix_result']]);
        }

        //问题类型
        if (!empty($params['category'])) {
            $builder->andWhere('cmf.category in ({category:array})', ['category' => $params['category']]);
        }

        //分配任务时间 -- 0时区
        if (!empty($params['start_time']) && !empty($params['end_time'])) {
            $start_time = gmdate('Y-m-d H:i:s', strtotime($params['start_time'] . ' 00:00:00'));
            $end_time   = gmdate('Y-m-d H:i:s', strtotime($params['end_time'] . ' 23:59:59'));
            $builder->betweenWhere('cmf.created_at', $start_time, $end_time);
        }

        //工单首次提交时间 当地时区
        if (!empty($params['order_start_time']) && !empty($params['order_end_time'])) {
            $builder->betweenWhere('cmf.order_created_at', $params['order_start_time'] . ' 00:00:00',
                $params['order_end_time'] . ' 23:59:59');
        }

        //超管不走数据权限
        if (in_array($params['user_id'], explode(',', env('sa_id')))) {
            return $builder;
        }

        //获取登录人可查看的员工的条件：网点、部门 条件员工权限
        //数据权限
        if (!empty($params['permission_sql']['builder_sql']) && !empty($params['permission_sql']['builder_bind'])) {
            $builder->andWhere(implode(' or ', $params['permission_sql']['builder_sql']), $params['permission_sql']['builder_bind']);
        }

        return $builder;
    }

    /**
     * 数据格式化
     * @param $data
     * @return array
     */
    public function formatFollowList($data)
    {
        $storeListToId = $jobTitleInfoToId = $operatorInfoToId = $list = [];
        $jobTitleId    = array_values(array_unique(array_column($data, 'job_title')));
        $sysStoreId    = array_values(array_unique(array_column($data, 'sys_store_id')));

        $followStaffIds = array_values(array_unique(array_column($data, 'follow_staff_id')));

        $departmentIds = array_values(array_unique(array_column($data, 'node_department_id')));

        $storeList = (new SysStoreService())->getStoreListByIds($sysStoreId);

        $storeList[] = [
            'id'    => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'category'    => 0,
            'region_name' => '',
            'piece_name'  => '',
        ];

        if ($storeList) {
            $storeListToId = array_column($storeList, null, 'id');
        }

        $orderIds  = array_values(array_unique(array_column($data, 'order_id')));
        $orderList = CeoMailStaffProblemOrderRepository::getOrderList(['ids' => $orderIds]);

        //投诉分类
        $orderCategoryName = [];
        if (!empty($orderList)) {
            $problemCategoryIds = array_values(array_unique(array_column($orderList, 'sys_category_id_v2')));
            // 根据分类id 查找对应的分类展示
            $problem_category_item = $this->getProblemCategoryListByIds($problemCategoryIds);

            $orderListToId = array_column($orderList, 'sys_category_id_v2', 'id');
            foreach ($orderListToId as $orderId => $sysCategoryId) {
                $orderCategoryName[$orderId] = $problem_category_item[$sysCategoryId] ?? [];
            }
        }


        $jobTitleInfo       = (new DepartmentService())->getJobList('', $jobTitleId, true);
        $followStaffIdsInfo = (new UserService())->getStaffInfo($followStaffIds);

        if ($jobTitleInfo) {
            $jobTitleInfoToId = array_column($jobTitleInfo, 'name', 'id');
        }

        $followStaffIdsInfoToId = !empty($followStaffIdsInfo) ? array_column($followStaffIdsInfo, 'name',
            'staff_info_id') : [];

        //部门名称
        $departmentList = (new DepartmentService())->getDepartmentInfo($departmentIds);
        $departmentList = empty($departmentList) ? [] : array_column($departmentList, 'name', 'id');

        $list = [];
        foreach ($data as $key => $oneData) {
            $list[$key]['id']            = $oneData['id'];
            $list[$key]['staff_info_id'] = $oneData['staff_info_id'];
            $list[$key]['name']          = $oneData['name'];

            $state = $oneData['state'];
            if ($oneData['state'] == HrStaffInfoModel::STATE_ON_JOB && $oneData['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
            }

            $list[$key]['state_text']            = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';
            $list[$key]['department_name']       = $departmentList[$oneData['node_department_id']] ?? '';
            $list[$key]['job_title_name']        = $jobTitleInfoToId[$oneData['job_title']] ?? '';
            $list[$key]['region_name']           = empty($storeListToId[$oneData['sys_store_id']]) ? '' : ($storeListToId[$oneData['sys_store_id']]['region_name'] ?? '');
            $list[$key]['piece_name']            = empty($storeListToId[$oneData['sys_store_id']]) ? '' : ($storeListToId[$oneData['sys_store_id']]['piece_name'] ?? '');
            $list[$key]['store_name']            = empty($storeListToId[$oneData['sys_store_id']]) ? '' : ($storeListToId[$oneData['sys_store_id']]['store_name'] ?? '');
            $list[$key]['mobile']                = $oneData['mobile'];
            $list[$key]['order_created_at']      = $oneData['order_created_at'];
            $list[$key]['created_at']            = $oneData['created_at'];

            $followName = isset($followStaffIdsInfoToId[$oneData['follow_staff_id']]) ? '(' . $followStaffIdsInfoToId[$oneData['follow_staff_id']] . ')' : '';
            $list[$key]['follow_staff_name']     = $oneData['follow_staff_id'] . $followName;
            
            $list[$key]['status_text']           = !empty(CeoMailFollowModel::$follow_status[$oneData['status']]) ? self::$t->_(CeoMailFollowModel::$follow_status[$oneData['status']]) : '';
            $list[$key]['fix_result_text']       = !empty(CeoMailFollowModel::$follow_result[$oneData['fix_result']]) ? self::$t->_(CeoMailFollowModel::$follow_result[$oneData['fix_result']]) : '';
            $list[$key]['category_text']         = $oneData['category'] ? self::$t->_('follow_category_'.$oneData['category']) : '';
            $list[$key]['fix_time']              = !empty($oneData['fix_time']) ? $oneData['fix_time'] : '';
            $list[$key]['fix_processing_time']   = !empty($list[$key]['fix_time']) && strtotime($list[$key]['fix_time']) > strtotime($list[$key]['created_at']) ? floor((strtotime($list[$key]['fix_time']) - strtotime($list[$key]['created_at'])) / 3600) : '';
            $list[$key]['hire_type_text']        = !empty($oneData['hire_type']) ? static::$t->_('hire_type_' . $oneData['hire_type']) : '';
            $list[$key]['problem_no']            = $oneData['problem_no'];
            $list[$key]['problem_category_name'] = $orderCategoryName[$oneData['order_id']] ?? '';
            $list[$key]['follow_message']        = $oneData['follow_message'];
        }

        return $list;
    }


    /**
     * 大区、片区
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getWhereByRegionPiece($builder, $params)
    {
        //片区
        $store_ids = [];
        if (!empty($params['piece_id'])) {
            $piece_store_list    = SysStoreModel::find([
                'columns'    => 'id',
                'conditions' => ' state = :state: AND manage_piece in ({manage_piece:array})',
                'bind'       => ['state' => SysStoreModel::STATE_1, 'manage_piece' => $params['piece_id']],
            ])->toArray();
            $store_ids           = array_column($piece_store_list, 'id');
            $params['region_id'] = '';// 如果选了片区，则不按大区筛选
        }
        //大区
        if (!empty($params['region_id'])) {
            $region_store_list = SysStoreModel::find([
                'columns'    => 'id',
                'conditions' => ' state = :state: AND manage_region in ({manage_region:array})',
                'bind'       => ['state' => SysStoreModel::STATE_1, 'manage_region' => $params['region_id']],
            ])->toArray();
            $store_ids         = array_column($region_store_list, 'id');
        }

        if (!empty($store_ids)) {
            $builder->andWhere('hsi.sys_store_id IN ({store_ids:array})', ['store_ids' => $store_ids]);
        }

        return $builder;
    }

    /**
     * 跟进详情
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function followDetail($params)
    {
        $followInfo = CeoMailFollowRepository::getOne(['id' => $params['follow_id']]);
        if (empty($followInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $staffInfo = StaffInfoRepository::getInfoByStaffInfoId($followInfo['staff_info_id'], true);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $storeList   = (new SysStoreService())->getStoreListByIds([$staffInfo['sys_store_id']]);
        $storeList[] = [
            'id'    => -1,
            'store_name'  => GlobalEnums::HEAD_OFFICE,
            'category'    => 0,
            'region_name' => '',
            'piece_name'  => '',
        ];

        $storeListToId = !empty($storeList) ? array_column($storeList, null, 'id') : [];

        $orderInfo = CeoMailStaffProblemOrderRepository::getOrderOne(['problem_no' => $followInfo['problem_no']]);
        //投诉分类
        $orderCategoryName = '';
        if (!empty($orderInfo)) {
            // 根据分类id 查找对应的分类展示
            $problem_category_item = $this->getProblemCategoryListByIds([$orderInfo['sys_category_id_v2']]);
            $orderCategoryName     = $problem_category_item[$orderInfo['sys_category_id_v2']] ?? '';
        }

        $followStaffInfo = StaffInfoRepository::getInfoByStaffInfoId($followInfo['follow_staff_id'], true);

        $jobTitleInfo     = (new DepartmentService())->getJobList('', [$staffInfo['job_title']], true);
        $jobTitleInfoToId = !empty($jobTitleInfo) ? array_column($jobTitleInfo, 'name', 'id') : [];


        //部门名称
        $departmentList     = (new DepartmentService())->getDepartmentInfo([$staffInfo['node_department_id']]);
        $departmentListToId = empty($departmentList) ? [] : array_column($departmentList, 'name', 'id');

        $state = $staffInfo['state'];
        if ($staffInfo['state'] == HrStaffInfoModel::STATE_ON_JOB && $staffInfo['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
            $state = HrStaffInfoModel::STATE_PENDING_RESIGNATION;
        }

        $staff_info['staff_info_id']       = $staffInfo['staff_info_id'];
        $staff_info['name']                = $staffInfo['name'];
        $staff_info['department_name']     = $departmentListToId[$staffInfo['node_department_id']] ?? '';
        $staff_info['job_title_name']      = $jobTitleInfoToId[$staffInfo['job_title']] ?? '';
        $staff_info['region_name']         = !empty($storeListToId[$staffInfo['sys_store_id']]) ? ($storeListToId[$staffInfo['sys_store_id']]['region_name'] ?? '') : '';
        $staff_info['piece_name']          = !empty($storeListToId[$staffInfo['sys_store_id']]) ? ($storeListToId[$staffInfo['sys_store_id']]['piece_name'] ?? '') : '';
        $staff_info['store_name']          = !empty($storeListToId[$staffInfo['sys_store_id']]) ? ($storeListToId[$staffInfo['sys_store_id']]['store_name'] ?? '') : '';
        $staff_info['state_text']          = !empty(Enums::$hris_working_state[$state]) ? self::$t->_(Enums::$hris_working_state[$state]) : '';
        $staff_info['hire_type_text']      = !empty($staffInfo['hire_type']) ? static::$t->_('hire_type_' . $staffInfo['hire_type']) : '';
        $staff_info['mobile']              = $staffInfo['mobile'] ?? '';
        $staff_info['problem_no']          = $followInfo['problem_no'] ?? '';
        $staff_info['order_category_name'] = $orderCategoryName;

        $orderDetail = $this->getProblemOrderDetail(['problem_no' => $followInfo['problem_no']]);

        $session_record = $orderDetail['problem_order_item'] ?? [];

        $followName = !empty($followStaffInfo['name']) ? '(' . $followStaffInfo['name'] . ')' : '';
        $fix_info['follow_staff_name']   = $followInfo['follow_staff_id'] . $followName;
        $fix_info['status_text']         = !empty(CeoMailFollowModel::$follow_status[$followInfo['status']]) ? self::$t->_(CeoMailFollowModel::$follow_status[$followInfo['status']]) : '';
        $fix_info['fix_result_text']     = !empty(CeoMailFollowModel::$follow_result[$followInfo['fix_result']]) ? self::$t->_(CeoMailFollowModel::$follow_result[$followInfo['fix_result']]) : '';
        $fix_info['category_text']       = $followInfo['category'] ? self::$t->_('follow_category_'.$followInfo['category']) : '';
        $fix_info['fix_time']            = $followInfo['fix_time'] ?? '';

        $createTime = show_time_zone($followInfo['created_at']);
        $fix_info['fix_processing_time'] = !empty($fix_info['fix_time']) && strtotime($fix_info['fix_time']) > strtotime($createTime) ? floor((strtotime($fix_info['fix_time']) - strtotime($createTime)) / 3600) : '';
        $fix_info['follow_message']      = $followInfo['follow_message'] ?? '';
        $fix_info['image_url']           = !empty($followInfo['image_url']) ? json_decode($followInfo['image_url'], true) : [];

        $result['staff_info']     = $staff_info;
        $result['session_record'] = $session_record;
        $result['fix_info']       = $fix_info;

        return $result;
    }

    /**
     * 导出
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function followListExport($params)
    {
        $params['is_check_data_num'] = true;
        $result                      = $this->followList($params);
        if ($result['total'] > self::LIMIT_DOWNLOAD_NUM) {
            throw new ValidationException(self::$t->_('file_download_limit', ['num' => self::LIMIT_DOWNLOAD_NUM]));
        }
        unset($params['is_check_data_num']);

        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "ceo_mail" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "follow_export";

        $excelTask = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['user_id'],
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->staff_info_id   = $params['user_id'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = show_time_zone(gmdate('Y-m-d H:i:s', time()), 'Y-m-d H:i:s');
        $excelTask->save();
        $hcmExcelTaskId = $excelTask->id;

        return $hcmExcelTaskId;
    }

    /**
     * 获取 hrpb 数据权限
     * @param $staffId
     * @return array|bool
     */
    public function getStaffPermissionData($staffId)
    {
        // HRBP
        $result = (new StaffPermissionService())->setExpire(60 * 3)->dominDepartmentsAndStoresFromCache($staffId);

        $this->logger->info(['staff_id' => $staffId, 'purview_v2' => $result]);

        return $result;
    }

    /**
     * 获取当前员工数据权限
     * @param $staffId
     * @param string $pre
     * @return array
     */
    public function getStaffDataPermissionSql($staffId, $pre = 'hsi')
    {
        //获取员工角色
        $staffRoles = HrStaffInfoPositionRepository::getHrStaffRoles($staffId);

        // 查看所有数据权限：特殊角色HR Management[17]、HRIS管理员[41]、超级管理员[99]、HR系统管理员[115]
        if (array_intersect(RolesModel::$staffCeoSuperPermissions, $staffRoles)) {
            return [];
        }

        $purview = $this->getStaffPermissionData($staffId);

        $return_data = [
            'builder_sql'  => [],
            'builder_bind' => [],
        ];

        if ($purview && !empty($purview['stores'])) {
            if (in_array(Enums::ALL_STORE_PERMISSION, $purview['stores'])) {
                $return_data['builder_sql'][]                = " ( {$pre}.sys_store_id NOT IN ({sys_store_id:array}) ) ";
                $return_data['builder_bind']['sys_store_id'] = [Enums\GlobalEnums::HEAD_OFFICE_ID];
            } else {
                $return_data['builder_sql'][]                = " ( {$pre}.sys_store_id IN ({sys_store_id:array}) ) ";
                $return_data['builder_bind']['sys_store_id'] = $purview['stores'];
            }
        }

        if ($purview && !empty($purview['flash_home_departments'])) {
            $return_data['builder_sql'][]                          = " ( {$pre}.node_department_id IN ({flash_home_departments:array}) and {$pre}.sys_store_id = " . Enums\GlobalEnums::HEAD_OFFICE_ID . ")";
            $return_data['builder_bind']['flash_home_departments'] = $purview['flash_home_departments'];
        }

        return $return_data;
    }

    /**
     * 待处理的投诉跟进，跟进工号 离职 则 重新查找一个bp
     * @return bool
     */
    public function replaceFollowStaff()
    {
        $this->logger->info('replaceFollowStaffTask start');

        //查询
        $where['status'] = CeoMailFollowModel::STATUS_PENDING;//待处理
        $where['state']  = HrStaffInfoModel::STATE_RESIGN;//投诉跟进人。离职
        $followList      = (new CeoMailFollowRepository())->getStaffList($where,
            ['cmf.id', 'cmf.staff_info_id', 'cmf.follow_staff_id', 'cmf.follow_type', 'cmf.problem_no']);

        if (empty($followList)) {
            $this->logger->info('replaceFollowStaff is empty');
            return false;
        }
        $db = $this->getDI()->get("db_backyard");
        foreach ($followList as $oneFollow) {
            //获取 新的hrbp
            //查找 管辖 指定工号的hrbp工号-》员工管理管辖范围
            $mangerIds = $this->getStaffHRBP($oneFollow['staff_info_id']);

            if (empty($mangerIds)) {
                continue;
            }

            $hrBpStaffId = array_column($mangerIds, 'staff_id');

            sort($hrBpStaffId);

            $follow_staff_id = 0;
            foreach ($hrBpStaffId as $oneId) {
                //如果是 当前 跟进人 已经是 这个bp,则跳过
                if ($oneId == $oneFollow['follow_staff_id']) {
                    continue;
                }

                //如果是工单员工本人， 不选
                if ($oneId == $oneFollow['staff_info_id']) {
                    continue;
                }
                //选一个工号最小的bp
                $follow_staff_id = $oneId;
            }

            $this->logger->info([
                'replaceFollowStaff_not_fount_bp' => [
                    'follow_id'            => $oneFollow['id'],
                    'staff_info_id'        => $oneFollow['staff_info_id'],
                    'bp_list'              => $hrBpStaffId,
                    'find_follow_staff_id' => $follow_staff_id,
                ],
            ]);

            if (empty($follow_staff_id)) {
                continue;
            }

            $lang                    = (new StaffService())->getAcceptLanguage($follow_staff_id);
            $data['follow_staff_id'] = $follow_staff_id;
            $db->updateAsDict("ceo_mail_follow", $data, ["conditions" => 'id=' . intval($oneFollow['id'])]);
            $push_param = [
                'staff_id'        => $follow_staff_id,
                'title'           => BaseService::getTranslation($lang)->_('hr_probation_field_msg_notice'),
                'message_content' => BaseService::getTranslation($lang)->_('flash_box_follow_push'),
            ];

            $this->sendPushToStaffFlashBoxFollow($push_param);
            $this->logger->info([
                'replaceFollowStaff-update' => [
                    'id'                     => $oneFollow['id'],
                    'before_follow_staff_id' => $oneFollow['follow_staff_id'],
                    'after_follow_staff_id'  => $data['follow_staff_id'],
                ],
            ]);
        }

        $this->logger->info('replaceFollowStaffTask end');

        return true;
    }

    /**
     * 选好BP，发送push
     * @param $params
     * @return bool
     */
    public function sendPushToStaffFlashBoxFollow($params)
    {
        //发push
        $data   = [
            "staff_info_id"   => $params['staff_id'],  //推送人员工ID
            "src"             => "backyard",      //1:'kit'; 2:'backyard','c';
            "message_title"   => !empty($params['title']) ? $params['title'] : "Flash Box",  //标题
            "message_content" => $params['message_content'] ?? '',//内容
            'message_scheme'  => "flashbackyard://fe/html?url=" . urlencode(env('by_h5_endpoint') . 'follow-list'),
        ];
        $client = new ApiClient('bi_rpc', '', 'push_to_staff');
        $client->setParamss($data);
        $res = $client->execute();
        if (!$res) {
            $this->logger->info('send Flash box follow message push to backyard  return false :' . json_encode($data));
            return false;
        }
        return true;
    }
}