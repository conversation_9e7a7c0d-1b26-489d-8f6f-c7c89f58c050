<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OrganizationEnums;
use App\Library\FlashOss;
use App\Library\StaffUtils;
use App\Library\Validation\ValidationException;
use App\Models\backyard\FranchiseeManagePieceModel;
use App\Models\backyard\FranchiseeManageRegionModel;
use App\Models\backyard\HrOrganizationDepartmentFranchiseePieceRelationModel;
use App\Models\backyard\HrOrganizationDepartmentFranchiseeRegionRelationModel;
use App\Models\backyard\HrOrganizationDepartmentPieceRelationModel;
use App\Models\backyard\HrOrganizationDepartmentRegionRelationModel;
use App\Models\backyard\HrOrganizationDepartmentStoreRelationModel;
use App\Models\backyard\HrStaffInfoModel;
use app\models\backyard\RoleEnumsModel;
use App\Models\backyard\RolesModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Models\bi\HrAreaManagerStoreModel;
use App\Repository\HrJobDepartmentRelationRepository;
use App\Repository\HrOperateLogsRepository;
use App\Repository\HrStaffInfoPositionRepository;
use App\Repository\HrStaffItemsRepository;
use Exception;

class OrganizationDepartmentTaskService extends BaseService
{
    const ORG_REGION_UPDATE_TYPE = 1;//大区变更部门
    const ORG_PIECE_UPDATE_TYPE = 2;//片区变更大区
    const ORG_STORE_UPDATE_TYPE = 3;//网点变更片区
    /**
     * 获取大区要更新的信息
     * @param $maxStage
     * @param int $num
     * @return mixed
     */
    public function getRegionUpdateList($maxStage, $num = 20)
    {
        return SysManageRegionModel::find([
            'columns'    => '
            id,
            deleted,
            updated_at,
            CONCAT(unix_timestamp(updated_at),id) as key
            ',
            'conditions' => ' CONCAT(unix_timestamp(updated_at),id) > :key:',
            'bind'       => [
                'key' => $maxStage,
            ],
            'order'      => 'key ASC',
            'limit'      => $num,
        ])->toArray();
    }

    /**
     * 获取片区要更新的信息
     * @param $maxStage
     * @param int $num
     * @return mixed
     */
    public function getPieceUpdateList($maxStage, $num = 20)
    {
        return SysManagePieceModel::find([
            'columns'    => '
            id,
            manage_region_id,
            deleted as is_deleted,
            updated_at,
            CONCAT(unix_timestamp(updated_at),id) as key
            ',
            'conditions' => ' CONCAT(unix_timestamp(updated_at),id) > :key:',
            'bind'       => [
                'key' => $maxStage,
            ],
            'order'      => 'key ASC',
            'limit'      => $num,
        ])->toArray();
    }

    /**
     * 获取网点要更新的信息
     * @param $maxStage
     * @param int $num
     * @return mixed
     */
    public function getStoreUpdateList($maxStage, $num = 10)
    {
        return SysStoreModel::find([
            'columns'    => '
            id,
            category,
            updated_at,
            manage_region,
            manage_piece,
            state,
            CONCAT(unix_timestamp(updated_at),id) as key
            ',
            'conditions' => ' CONCAT(unix_timestamp(updated_at),id) > :key:',
            'bind'       => [
                'key' => $maxStage,
            ],
            'order'      => 'key ASC',
            'limit'      => $num,
        ])->toArray();
    }

    /**
     * 处理更新，为了保证一致性，单表单次查询
     * @return bool
     */
    public function addRegionUpdateList($info)
    {
        $this->logger->info('自动-同大区区数据开始 '.json_encode($info));

        $orgService = (new OrganizationDepartmentService());

        $orgService->backyardDb = $this->di->get('db_backyard');
        $orgService->backyardDb->begin();

        try {
            $regionRelationInfo = HrOrganizationDepartmentRegionRelationModel::findFirst([
                'conditions' => ' region_id = :region_id: ',
                'bind'       => ['region_id' => $info['id']],
            ]);

            //场景1 没有数据，直接添加
            if (empty($regionRelationInfo)) {
                $regionRelationInfo             = new HrOrganizationDepartmentRegionRelationModel();
                $regionRelationInfo->region_id  = $info['id'];
                $regionRelationInfo->state      = OrganizationEnums::STATE_REMOVE;
                $regionRelationInfo->is_deleted = $info['deleted'];
                $regionRelationInfo->save();
                $this->logger->info('自动-同步大区数据添加 END');

                return $orgService->backyardDb->commit();
            }

            //场景2 其他数据未发生变更
            if ($info['deleted'] == $regionRelationInfo->is_deleted) {
                throw new ValidationException('Success');
            }

            //大区状态发生变化
            $regionRelationInfo->is_deleted = $info['deleted'];
            $regionRelationInfo->save();

            if ($info['deleted'] == GlobalEnums::NO_DELETED) {
                $updateData = ['level_state' => OrganizationEnums::LEVEL_STATE_NORMAL];
            } else {
                $updateData = ['level_state' => OrganizationEnums::LEVEL_STATE_REMOVE];
            }

            //片区
            $orgService->updateCommonPieceByRegion($regionRelationInfo->region_id, $updateData);

            //网点
            $orgService->updateCommonStoreByRegion($regionRelationInfo->region_id, $updateData);

            //补偿已经删除数据 概率特别低
            if ($updateData['level_state'] == OrganizationEnums::LEVEL_STATE_NORMAL) {
                $orgService->updateCommonStoreByRegionLevelState($regionRelationInfo->region_id);
            }

            $this->logger->info('自动-同步大区数据更新 '.json_encode($updateData));

            return $orgService->backyardDb->commit();
        } catch (ValidationException $exception) {
            $orgService->backyardDb->rollback();
            return true;
        } catch (Exception $exception) {
            $orgService->backyardDb->rollback();
            throw $exception;
        }
    }

    /**
     * 处理更新，为了保证一致性，单表单次查询
     * @return bool
     */
    public function addPieceUpdateList($info)
    {
        $this->logger->info('自动-同步片区数据开始 '.json_encode($info));

        $info['manage_region_id'] = intval($info['manage_region_id'] ?? 0);

        $orgService = (new OrganizationDepartmentService());

        $orgService->backyardDb = $this->di->get('db_backyard');
        $orgService->backyardDb->begin();

        try {
            //查询
            $pieceRelationInfo = HrOrganizationDepartmentPieceRelationModel::findFirst([
                'conditions' => 'piece_id = :piece_id: ',
                'bind'       => ['piece_id' => $info['id']],
            ]);

            //查询大区信息
            $regionRelationInfo = [];
            if (!empty($info['manage_region_id'])) {
                $regionRelationInfo = HrOrganizationDepartmentRegionRelationModel::findFirst([
                    'conditions' => ' region_id = :region_id: ',
                    'bind'       => ['region_id' => $info['manage_region_id']],
                ]);

                $regionRelationInfo = $regionRelationInfo ? $regionRelationInfo->toArray() : [];
            }

            //场景1 没有数据，直接添加
            if (empty($pieceRelationInfo)) {
                $pieceRelationInfo                 = new HrOrganizationDepartmentPieceRelationModel();
                $pieceRelationInfo->region_id      = $info['manage_region_id'];
                $pieceRelationInfo->real_region_id = $info['manage_region_id'];
                $pieceRelationInfo->piece_id       = $info['id'];
                $pieceRelationInfo->department_id  = $regionRelationInfo['department_id'] ?? 0;
                $pieceRelationInfo->level_state    = $this->getLevelState($regionRelationInfo, OrganizationEnums::LEVEL_TYPE_REGION);
                $pieceRelationInfo->state          = $info['manage_region_id'] ? OrganizationEnums::STATE_NORMAL : OrganizationEnums::STATE_REMOVE;
                $pieceRelationInfo->is_deleted     = $info['is_deleted'];
                $pieceRelationInfo->save();

                if ($info['is_deleted'] == GlobalEnums::NO_DELETED) {
                    $syncData['update_type'] = self::ORG_PIECE_UPDATE_TYPE;
                    $syncData['region_id']   = $pieceRelationInfo->region_id;
                    $syncData['piece_id']    = $info['id'];
                    $this->sync_org_change_manager_relation($syncData);
                }

                $this->logger->info('自动-同步片区新增 '.json_encode($info));

                return $orgService->backyardDb->commit();
            }

            $updateDataPiece = $pieceRelationInfo->toArray();
            $this->logger->info('自动-同步片区数据更新 历史数据:'.json_encode($updateDataPiece));

            //场景2  换大区
            $updateDataStore = [];
            if ($updateDataPiece['real_region_id'] != $info['manage_region_id']) {
                //没解除，更新
                if ($updateDataPiece['state'] == OrganizationEnums::STATE_NORMAL) {
                    $updateDataPiece['department_id'] = $regionRelationInfo['department_id'] ?? 0;
                    $updateDataPiece['region_id']     = $info['manage_region_id'];

                    $updateDataStore['region_id']     = $updateDataPiece['region_id'];
                    $updateDataStore['department_id'] = $updateDataPiece['department_id'];

                    if ($info['is_deleted'] == GlobalEnums::NO_DELETED) {
                        $syncData['update_type'] = self::ORG_PIECE_UPDATE_TYPE;
                        $syncData['region_id']   = $updateDataPiece['region_id'];
                        $syncData['piece_id']    = $info['id'];
                        $this->sync_org_change_manager_relation($syncData);
                    }
                }

                //真实大区id
                $updateDataPiece['real_region_id'] = $info['manage_region_id'];
                $updateDataStore['real_region_id'] = $updateDataPiece['real_region_id'];

                $updateDataPiece['level_state'] = $this->getLevelState($regionRelationInfo, OrganizationEnums::LEVEL_TYPE_REGION);
                $updateDataStore['level_state'] = $this->getLevelState($updateDataPiece, OrganizationEnums::LEVEL_TYPE_PIECE);
            }

            //场景3  换状态
            if ($updateDataPiece['is_deleted'] != $info['is_deleted']) {
                $updateDataPiece['is_deleted']  = $info['is_deleted'];
                $updateDataStore['level_state'] = $this->getLevelState($updateDataPiece, OrganizationEnums::LEVEL_TYPE_PIECE);
            }

            if ($updateDataStore) {
                $this->logger->info('自动-同步片区数据更新 修改数据'.json_encode($updateDataPiece));
                $pieceRelationInfo->save($updateDataPiece);
                $orgService->updateCommonStoreByPiece($pieceRelationInfo->piece_id, $updateDataStore);
            }

            return $orgService->backyardDb->commit();
        } catch (Exception $exception) {
            $orgService->backyardDb->rollback();
            throw $exception;
        }
    }

    /**
     * 获取上级是否在使用
     * @param $info
     * @return int
     */
    public function getLevelState($info, $type = OrganizationEnums::LEVEL_TYPE_REGION)
    {
        if (empty($info)) {
            return OrganizationEnums::STATE_REMOVE;
        }

        if ($type == OrganizationEnums::LEVEL_TYPE_REGION) {
            if (
                $info['is_deleted'] == GlobalEnums::NO_DELETED
                && $info['state'] == OrganizationEnums::STATE_NORMAL
            ) {
                return OrganizationEnums::STATE_NORMAL;
            }
        } else {
            if (
                $info['is_deleted'] == GlobalEnums::NO_DELETED
                && $info['state'] == OrganizationEnums::STATE_NORMAL
                && $info['level_state'] == OrganizationEnums::LEVEL_STATE_NORMAL
            ) {
                return OrganizationEnums::STATE_NORMAL;
            }
        }

        return OrganizationEnums::STATE_REMOVE;
    }

    /**
     * 处理更新，为了保证一致性，单表单次查询
     * @return bool
     */
    public function addStoreUpdateList($info)
    {
        $this->logger->info('自动-同步网点数据开始 '.json_encode($info));

        $info['manage_region'] = intval($info['manage_region'] ?? 0);
        $info['manage_piece']  = intval($info['manage_piece'] ?? 0);
        $info['is_deleted']    = ($info['state'] == 1) ? GlobalEnums::NO_DELETED : GlobalEnums::DELETED;

        //网点信息
        $storeRelationInfo = HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => 'store_id = :store_id: ',
            'bind'       => ['store_id' => $info['id']],
        ]);

        //大区
        $regionRelationInfo = [];
        if (!empty($info['manage_region'])) {
            $regionRelationInfo = HrOrganizationDepartmentRegionRelationModel::findFirst([
                'conditions' => 'region_id = :region_id: ',
                'bind'       => ['region_id' => $info['manage_region']],
            ]);

            $regionRelationInfo = $regionRelationInfo ? $regionRelationInfo->toArray() : [];
        }

        //片区
        $pieceRelationInfo = [];
        if (!empty($info['manage_piece'])) {
            $pieceRelationInfo = HrOrganizationDepartmentPieceRelationModel::findFirst([
                'conditions' => 'piece_id = :piece_id: ',
                'bind'       => ['piece_id' => $info['manage_piece']],
            ]);

            $pieceRelationInfo = $pieceRelationInfo ? $pieceRelationInfo->toArray() : [];
        }

        //场景1 没有数据，直接添加
        if (empty($storeRelationInfo)) {
            $storeRelationInfo                 = new HrOrganizationDepartmentStoreRelationModel();

            $storeRelationInfo->real_region_id = $info['manage_region'];
            $storeRelationInfo->real_piece_id  = $info['manage_piece'];
            $storeRelationInfo->category       = $info['category'];
            $storeRelationInfo->store_id       = $info['id'];
            $storeRelationInfo->is_deleted     = $info['is_deleted'];

            //挂载大区下
            if ($info['manage_piece']) {
                $storeRelationInfo->region_id     = $pieceRelationInfo['region_id'] ?? 0;
                $storeRelationInfo->piece_id      = $info['manage_piece'];
                $storeRelationInfo->level_state   = $this->getLevelState($pieceRelationInfo, OrganizationEnums::LEVEL_TYPE_PIECE);
                $storeRelationInfo->department_id = $pieceRelationInfo['department_id'] ?? 0;
                $storeRelationInfo->state         = OrganizationEnums::STATE_NORMAL;

                $syncData['update_type'] = self::ORG_STORE_UPDATE_TYPE;
                $syncData['store_id'] = $storeRelationInfo->store_id;
                $syncData['piece_id'] = $storeRelationInfo->piece_id;
                $this->sync_org_change_manager_relation($syncData);
            //挂载片区下
            } else if ($info['manage_region']) {
                $storeRelationInfo->region_id     = $info['manage_region'];
                $storeRelationInfo->level_state   = $this->getLevelState($regionRelationInfo, OrganizationEnums::LEVEL_TYPE_REGION);
                $storeRelationInfo->department_id = $regionRelationInfo['department_id'] ?? 0;
                $storeRelationInfo->state         = OrganizationEnums::STATE_NORMAL;
            //没有挂载
            } else {
                $storeRelationInfo->level_state   = OrganizationEnums::LEVEL_STATE_REMOVE;
                $storeRelationInfo->department_id = 0;
                $storeRelationInfo->state         = OrganizationEnums::STATE_REMOVE;
            }

            $this->logger->info('自动-同步网点数据新增 '.json_encode($info));

            return $storeRelationInfo->save();
        }

        //场景2  换大区
        $updateStore = [];
        if (
            ($storeRelationInfo->real_region_id != $info['manage_region'])
            || ($storeRelationInfo->real_piece_id != $info['manage_piece'])
        ) {

            $updateStore['real_region_id'] = $info['manage_region'];
            $updateStore['real_piece_id']  = $info['manage_piece'];

            if ($info['manage_piece']) {
                $updateStore['level_state']    = $this->getLevelState($pieceRelationInfo, OrganizationEnums::LEVEL_TYPE_PIECE);

                if ($storeRelationInfo->state == OrganizationEnums::STATE_NORMAL) {
                    $updateStore['piece_id']      = $info['manage_piece'];
                    $updateStore['region_id']     = $pieceRelationInfo['region_id'] ?? 0;
                    $updateStore['department_id'] = $pieceRelationInfo['department_id'] ?? 0;

                    if($info['is_deleted'] == GlobalEnums::NO_DELETED) {
                        $syncData['update_type'] = self::ORG_STORE_UPDATE_TYPE;
                        $syncData['store_id'] = $storeRelationInfo->store_id;
                        $syncData['piece_id'] = $storeRelationInfo->piece_id;
                        $this->sync_org_change_manager_relation($syncData);
                    }
                }
            } else if ($info['manage_region']) {
                $updateStore['level_state']    = $this->getLevelState($regionRelationInfo,OrganizationEnums::LEVEL_TYPE_REGION);

                if ($storeRelationInfo->state == OrganizationEnums::STATE_NORMAL) {
                    $updateStore['department_id']  = $regionRelationInfo['department_id'] ?? 0;
                    $updateStore['region_id']      = $info['manage_region'];
                    $updateStore['piece_id']       = $info['manage_piece'];
                }
            } else {
                $updateStore['region_id']     = $info['manage_region'];
                $updateStore['piece_id']      = $info['manage_piece'];
                $updateStore['level_state']   = OrganizationEnums::LEVEL_STATE_REMOVE;
                $updateStore['department_id'] = 0;
                $updateStore['state']         = OrganizationEnums::STATE_REMOVE;
            }
        }

        //场景4  换类型
        if ($storeRelationInfo->category != $info['category']) {
            $updateStore['category'] = $info['category'];
        }

        //场景5  换状态
        if ($storeRelationInfo->is_deleted != $info['is_deleted']) {
            $updateStore['is_deleted'] = $info['is_deleted'];
        }

        $this->logger->info('自动-同步网点数据更新 '.json_encode($updateStore));

        if ($updateStore) {
            return $storeRelationInfo->save($updateStore);
        }

        return true;
    }

    /**
     * 获取大区要更新的信息
     * @param $maxStage
     * @param int $num
     * @return mixed
     */
    public function getOrganizationPieceUpdateList($maxStage, $num = 20)
    {
        return HrOrganizationDepartmentPieceRelationModel::find([
            'columns'    => '
            id,
            department_id,
            region_id,
            piece_id
            ',
            'conditions' => 'id > :id: AND department_id > 0',
            'bind'       => [
                'id'           => (int) $maxStage,
            ],
            'order'      => 'id ASC',
            'limit'      => $num,
        ])->toArray();
    }

    /**
     * 获取网点要更新的信息
     * @param $maxStage
     * @param int $num
     * @return mixed
     */
    public function getOrganizationStoreUpdateList($maxStage, $num = 20)
    {
        return HrOrganizationDepartmentStoreRelationModel::find([
            'columns'    => '
            id,
            department_id,
            region_id,
            piece_id,
            store_id
            ',
            'conditions' => 'id > :id: AND department_id > 0',
            'bind'       => [
                'id'           => (int) $maxStage,
            ],
            'order'      => 'id ASC',
            'limit'      => $num,
        ])->toArray();
    }

    /**
     * 处理更新，为了保证一致性，单表单次查询
     * @return bool
     */
    public function updatePieceLevelStateList($info)
    {
        $this->logger->info('自动-同步网点数据开始 '.json_encode($info));

        if (empty($info)) {
            return false;
        }

        //查询
        $pieceRelationInfo = HrOrganizationDepartmentPieceRelationModel::findFirst([
            'conditions' => 'piece_id = :piece_id: ',
            'bind'       => ['piece_id' => $info['piece_id']],
        ]);

        //大区
        $regionRelationInfo = [];
        if (!empty($info['region_id'])) {
            $regionRelationInfo = HrOrganizationDepartmentRegionRelationModel::findFirst([
                'conditions' => 'region_id = :region_id: ',
                'bind'       => ['region_id' => $info['region_id']],
            ]);

            $regionRelationInfo = $regionRelationInfo ? $regionRelationInfo->toArray() : [];
        }

        //大区id不存在，直接挂载
        if (empty($info['region_id'])) {
            $pieceRelationInfo->level_state    = OrganizationEnums::LEVEL_STATE_NORMAL;
        } elseif ($info['region_id']) {
            $pieceRelationInfo->level_state = $this->getLevelState($regionRelationInfo,OrganizationEnums::LEVEL_TYPE_REGION);
        }

        return $pieceRelationInfo->save();
    }

    /**
     * 处理更新，为了保证一致性，单表单次查询
     * @return bool
     */
    public function updateStoreLevelStateList($info)
    {
        $this->logger->info('自动-同步网点数据开始 '.json_encode($info));

        if (empty($info)) {
            return false;
        }

        //网点信息
        $storeRelationInfo = HrOrganizationDepartmentStoreRelationModel::findFirst([
            'conditions' => 'store_id = :store_id: ',
            'bind'       => ['store_id' => $info['store_id']],
        ]);

        //大区
        $regionRelationInfo = [];
        if (!empty($info['region_id'])) {
            $regionRelationInfo = HrOrganizationDepartmentRegionRelationModel::findFirst([
                'conditions' => 'region_id = :region_id: ',
                'bind'       => ['region_id' => $info['region_id']],
            ]);

            $regionRelationInfo = $regionRelationInfo ? $regionRelationInfo->toArray() : [];
        }

        //片区
        $pieceRelationInfo = [];
        if (!empty($info['piece_id'])) {
            $pieceRelationInfo = HrOrganizationDepartmentPieceRelationModel::findFirst([
                'conditions' => 'piece_id = :piece_id: ',
                'bind'       => ['piece_id' => $info['piece_id']],
            ]);

            $pieceRelationInfo = $pieceRelationInfo ? $pieceRelationInfo->toArray() : [];
        }

        //大区id不存在，直接挂载
        if (!empty($info['piece_id'])) {
            $storeRelationInfo->level_state    = $this->getLevelState($pieceRelationInfo,OrganizationEnums::LEVEL_TYPE_PIECE);
        } elseif (!empty($info['region_id'])) {
            $storeRelationInfo->level_state = $this->getLevelState($regionRelationInfo,OrganizationEnums::LEVEL_TYPE_REGION);
        } else {
            $storeRelationInfo->level_state = OrganizationEnums::LEVEL_STATE_NORMAL;
        }

        return $storeRelationInfo->save();
    }

    /**
     * 获取要更新的加盟商大区
     * @param $maxStage
     * @param $num
     * @return array
     */
    public function getFranchiseeRegionUpdateList($maxStage, $num = 20): array
    {
        return FranchiseeManageRegionModel::find([
            'columns'    => '
            id,
            deleted,
            updated_at,
            CONCAT(unix_timestamp(updated_at),id) as key
            ',
            'conditions' => ' CONCAT(unix_timestamp(updated_at),id) > :key:',
            'bind'       => [
                'key' => $maxStage,
            ],
            'order'      => 'key ASC',
            'limit'      => $num,
        ])->toArray();
    }

    /**
     * 处理加盟商大区更新，为了保证一致性，单表单次查询
     * @param $info
     * @return true
     * @throws ValidationException
     */
    public function addFranchiseeRegionUpdateList($info): bool
    {
        $this->logger->info('自动-同加盟商大区数据开始 '.json_encode($info));
        $orgService = (new OrganizationDepartmentService());

        $db_backyard = $this->di->get('db_backyard');
        $db_backyard->begin();
        try {
            $regionRelationInfo = HrOrganizationDepartmentFranchiseeRegionRelationModel::findFirst([
                'conditions' => ' region_id = :region_id: ',
                'bind'       => ['region_id' => $info['id']],
            ]);

            //场景1 没有数据，直接添加
            if (empty($regionRelationInfo)) {
                $regionRelationInfo             = new HrOrganizationDepartmentFranchiseeRegionRelationModel();
                $regionRelationInfo->region_id  = $info['id'];
                $regionRelationInfo->state      = OrganizationEnums::STATE_REMOVE;
                $regionRelationInfo->is_deleted = $info['deleted'];
                $regionRelationInfo->save();
                $this->logger->info('自动-同步加盟商大区数据添加 END');

                return $db_backyard->commit();
            }

            //场景2 其他数据未发生变更
            if ($info['deleted'] == $regionRelationInfo->is_deleted) {
                throw new ValidationException('Success');
            }

            //大区状态发生变化
            $regionRelationInfo->is_deleted = $info['deleted'];
            $regionRelationInfo->save();

            if ($info['deleted'] == GlobalEnums::NO_DELETED) {
                $updateData = ['level_state' => OrganizationEnums::LEVEL_STATE_NORMAL];
            } else {
                $updateData = ['level_state' => OrganizationEnums::LEVEL_STATE_REMOVE];
            }

            //片区
            $orgService->updateCommonFranchiseePieceByRegion($regionRelationInfo->region_id, $updateData);
            $this->logger->info('自动-同步加盟商大区数据更新 '.json_encode($updateData));

            return $db_backyard->commit();
        } catch (ValidationException $exception) {
            $db_backyard->rollback();
            return true;
        } catch (Exception $exception) {
            $db_backyard->rollback();
            throw $exception;
        }
    }

    /**
     * 获取片区要更新的信息
     * @param $maxStage
     * @param int $num
     * @return mixed
     */
    public function getFranchiseePieceUpdateList($maxStage, $num = 20)
    {
        return FranchiseeManagePieceModel::find([
            'columns'    => '
            id,
            manage_region_id,
            deleted as is_deleted,
            updated_at,
            CONCAT(unix_timestamp(updated_at),id) as key
            ',
            'conditions' => ' CONCAT(unix_timestamp(updated_at),id) > :key:',
            'bind'       => [
                'key' => $maxStage,
            ],
            'order'      => 'key ASC',
            'limit'      => $num,
        ])->toArray();
    }

    /**
     * 处理加盟商片区更新，为了保证一致性，单表单次查询
     * @return bool
     */
    public function addFranchiseePieceUpdateList($info)
    {
        $this->logger->info('自动-同步加盟商片区数据开始 '.json_encode($info));
        $info['manage_region_id'] = intval($info['manage_region_id'] ?? 0);

        $db_backyard = $this->di->get('db_backyard');
        $db_backyard->begin();

        try {
            //查询
            $pieceRelationInfo = HrOrganizationDepartmentFranchiseePieceRelationModel::findFirst([
                'conditions' => 'piece_id = :piece_id: ',
                'bind'       => ['piece_id' => $info['id']],
            ]);

            //查询大区信息
            $regionRelationInfo = [];
            if (!empty($info['manage_region_id'])) {
                $regionRelationInfo = HrOrganizationDepartmentFranchiseeRegionRelationModel::findFirst([
                    'conditions' => ' region_id = :region_id: ',
                    'bind'       => ['region_id' => $info['manage_region_id']],
                ]);

                $regionRelationInfo = $regionRelationInfo ? $regionRelationInfo->toArray() : [];
            }

            //场景1 没有数据，直接添加
            if (empty($pieceRelationInfo)) {
                $pieceRelationInfo                 = new HrOrganizationDepartmentFranchiseePieceRelationModel();
                $pieceRelationInfo->region_id      = $info['manage_region_id'];
                $pieceRelationInfo->real_region_id = $info['manage_region_id'];
                $pieceRelationInfo->piece_id       = $info['id'];
                $pieceRelationInfo->department_id  = $regionRelationInfo['department_id'] ?? 0;
                $pieceRelationInfo->level_state    = $this->getLevelState($regionRelationInfo, OrganizationEnums::LEVEL_TYPE_REGION);
                $pieceRelationInfo->state          = $info['manage_region_id'] ? OrganizationEnums::STATE_NORMAL : OrganizationEnums::STATE_REMOVE;
                $pieceRelationInfo->is_deleted     = $info['is_deleted'];
                $result = $pieceRelationInfo->save();

                $this->logger->info('自动-同步加盟商片区新增 '.json_encode($info) . '------' . json_encode($result));
                return $db_backyard->commit();
            }

            $updateDataPiece = $pieceRelationInfo->toArray();
            $this->logger->info('自动-同步加盟商片区数据更新 历史数据:'.json_encode($updateDataPiece));

            //场景2  换大区
            $updateDataStore = [];
            if ($updateDataPiece['real_region_id'] != $info['manage_region_id']) {
                //没解除，更新
                if ($updateDataPiece['state'] == OrganizationEnums::STATE_NORMAL) {
                    $updateDataPiece['department_id'] = $regionRelationInfo['department_id'] ?? 0;
                    $updateDataPiece['region_id']     = $info['manage_region_id'];
                }

                //真实大区id
                $updateDataPiece['real_region_id'] = $info['manage_region_id'];

                $updateDataPiece['level_state'] = $this->getLevelState($regionRelationInfo, OrganizationEnums::LEVEL_TYPE_REGION);
            }

            //场景3  换状态
            if ($updateDataPiece['is_deleted'] != $info['is_deleted']) {
                $updateDataPiece['is_deleted']  = $info['is_deleted'];
            }

            if ($updateDataPiece) {
                $this->logger->info('自动-同步片区数据更新 修改数据'.json_encode($updateDataPiece));
                $pieceRelationInfo->save($updateDataPiece);
            }

            return $db_backyard->commit();
        } catch (Exception $exception) {
            $db_backyard->rollback();
            throw $exception;
        }
    }

    /**
     * 请求hris 将 因组织变更，变更组织负责人上级 为新的组织节点的负责人
     * @param $data
     * @return bool
     */
    public function sync_org_change_manager_relation($data)
    {
        if (!isCountry(['TH', 'PH', 'MY'])) {
            return true;
        }
        $hris_rpc = new ApiClient('hris', '', 'hcm_sync_staff_manager_update', 'zh-CN');
        $hris_rpc->setParams([$data]);
        $res = $hris_rpc->execute();
        $this->logger->info(['sync_org_change_manager_relation' => ['params' => $data, 'res' => $res]]);

        return true;
    }

    /**
     * 部门自动变更
     * @param bool $isUpdate
     * @return \OSS\Http\ResponseCore|string
     * @throws ValidationException
     * @throws \OSS\Core\OssException
     */
    public function updateStaffDepartment($isUpdate = false)
    {
        $exportDataAll = $pendingDataAll = [];
        $params['page']     = 1;
        $params['pageSize'] = 500;
        while (true) {
            $list = $this->getStaffList($params);
            if (empty($list)) {
                break;
            }
            echo 'Time:' . date('Y-m-d H:i:s') . '---page:' . $params['page'] . PHP_EOL;

            [$exportData, $pendingData] = $this->formatList($list, $isUpdate);
            $exportDataAll = array_merge($exportDataAll, $exportData);
            $pendingDataAll = array_merge($pendingDataAll, $pendingData);

            $params['page']++;
        }
        $pendingNum = count($pendingDataAll);
        echo "需要处理的数据:" . $pendingNum . PHP_EOL;

        $exportDataAll = array_column($exportDataAll, NULL,'staff_info_id');
        foreach ($pendingDataAll as $onePending) {
            $syncRes = $this->syncStaffDepartmentInfoToMs($onePending);
            if (!$syncRes) {
                $onePending['fail_reason'] = 'update fail';
                $exportDataAll[$onePending['staff_info_id']] = $onePending;
            }
        }

        $header = [
            'Staff id',
            'Name',
            'branch',
            'Position',
            'Role',
            'Working days Rotation rules',
            'Old department',
            'New department',
        ];

        if ($isUpdate) {
            $header[] = 'Result';
        }

        $export = [];
        foreach ($exportDataAll as $key => $oneData) {
            $export[$key] = [
                $oneData['staff_info_id'],
                $oneData['name'],
                $oneData['store_name'],
                $oneData['job_title_text'],
                $oneData['positions_text'],
                $oneData['working_day_rest_type_text'],
                $oneData['department_name_old'],
                $oneData['department_name_new'],
            ];
            if ($isUpdate) {
                $export[$key][] = $oneData['fail_reason'];
            }
        }

        $fileName = uniqid(get_country_code() . '-departmentchange' . date('Ymd') . '_') . '.xlsx';

        //不更新，需要返回导出文件地址
        if (!$isUpdate) {
            $file_data = $this->exportExcel($header, $export, $fileName);
            //上传oss服务
            $flashOss  = new FlashOss();
            $ossObject = 'updateStaffDepartment/' . date('Ymd') . '/' . $fileName;
            $flashOss->uploadFile($ossObject, $file_data['data']);
            $url = $flashOss->signUrl($ossObject, 20 * 24 * 60 * 60);
            return $url;
        }

        //发送邮件
        if (!empty($export)) {
            $file_data        = $this->exportExcel($header, $export, $fileName);
            $emailData['email']    = (new SettingEnvService())->getSetVal('update_staff_department_by_org', ',');
            $emailData['file_url'] = $file_data['data'];
            if ($this->sendEmail($emailData)) {
                return 'success';
            }
        }

        return 'fail';
    }

    /**
     * 发送邮件
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function sendEmail($params)
    {
        if (empty($params['email'])) {
            $this->logger->error("部门自动变更失败通知邮件 发送失败 邮箱不能为空");
            return false;
        }
        try {
            $staffsEmail = $params['email'];
            //发送邮件
            $title   = '【' . get_country_code() . '】部门自动变更通知Department automatic change notification ' . date('Ymd');
            $content = "基于大区、片区、网点的架构调整，以下网点员工变更部门信息成功/失败，请知悉</br>Due to the structural adjustment of the Area, District and Branch, the following  employees success/failed to change their department information.";
            $sendResult = \App\Library\BiMail::send($staffsEmail, $title, $content, [$params['file_url']]);
            if (!$sendResult) {
                return false;
            }
            return true;
        } catch (Exception $e) {
            $this->logger->notice("部门自动变更失败通知邮件 发送失败  message: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 查询数据
     * @param $params
     * @return mixed
     */
    public function getStaffList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.sys_department_id',
            'hsi.node_department_id',
            'hsi.sys_store_id',
            'hsi.job_title',
            'hsi.mobile',
            'hsi.email',
            'hsi.personal_email',
            'hsi.formal',
            'hsi.state',
            'hsi.wait_leave_state',
            'hsi.leave_date',
            'hsi.hire_date',
            'hsi.stop_duties_date',
            'hsi.mobile_company',
            'hsi.instructor_id',
            'hsi.manger',
            'hsi.bank_no',
            'hsi.bank_type',
            'hsi.hire_type',
            'hsi.week_working_day',
            'hsi.rest_type',
            'hsi.job_title_grade_v2',
            'ss.name as store_name',
            'relation.department_id',
        ]);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'hsi.sys_store_id = ss.id', 'ss');
        $builder->leftJoin(HrOrganizationDepartmentStoreRelationModel::class, 'relation.store_id = ss.id', 'relation');

        $builder->where('hsi.state != :staff_state: and hsi.formal in ({formal:array}) and is_sub_staff = :is_sub_staff: and hsi.sys_store_id != :sys_store_id:',
            [
                'staff_state'  => HrStaffInfoModel::STATE_RESIGN,
                'formal'       => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
                'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO,
                'sys_store_id' => GlobalEnums::HEAD_OFFICE_ID,
            ]);
        $builder->andWhere('ss.category in ({category:array})', [
            'category' => [
                SysStoreModel::CATEGORY_SP,
                SysStoreModel::CATEGORY_DC,
                SysStoreModel::CATEGORY_BDC,
                SysStoreModel::CATEGORY_PDC,
                SysStoreModel::CATEGORY_CDC,
            ],
        ]);
        $builder->andWhere('relation.is_deleted = :deleted:',
            ['deleted' => HrOrganizationDepartmentStoreRelationModel::IS_DELETED_NO]);
        $builder->andWhere('relation.state = :state:',
            ['state' => HrOrganizationDepartmentStoreRelationModel::STATE_EFFECTIVE]);
        $builder->andWhere('relation.level_state = :level_state:',
            ['level_state' => HrOrganizationDepartmentStoreRelationModel::LEVEL_STATE_EFFECTIVE]);
        $builder->andWhere('hsi.node_department_id != relation.department_id');

        $offset = ($params['page'] - 1) * $params['pageSize'];
        $limit  = $params['pageSize'];
        $builder->limit($limit, $offset);

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 格式化数据
     * @param $list
     * @param $isUpdate
     * @return array
     */
    public function formatList($list, $isUpdate)
    {
        //职位
        $job_list = array_column((new HrJobTitleService())->getJobTitleListFromCache(false), 'job_name', 'id');
        $staffIds = array_column($list, 'staff_info_id');

        $staffRoles = HrStaffInfoPositionRepository::getStaffsBatch($staffIds);

        $sysService     = new SysService();
        $departmentList = array_column($sysService->getDepartmentListFromCache(), 'name', 'id');

        $roleList     = (new RolesService())->getList('', true);
        $roleListToId = array_column($roleList, 'name', 'id');

        $staffItemsToId = HrStaffItemsRepository::getStaffItemByIds($staffIds);

        $staffAreaInfo = $this->getAreaManagerStore($staffIds);
        $dep_server    = new SysDepartmentService();

        $exportDate = $pendingData = [];
        foreach ($list as &$one) {
            //角色
            $one['positions']  = isset($staffRoles[$one['staff_info_id']]) && $staffRoles[$one['staff_info_id']] != '' ? explode(',',
                $staffRoles[$one['staff_info_id']]) : [];
            $staffPositionText = [];
            foreach ($one['positions'] as $onePositions) {
                $staffPositionText[] = $roleListToId[$onePositions];
            }
            $one['positions_text'] = implode(',', $staffPositionText);
            //旧部门
            $one['department_name_old'] = isset($departmentList[$one['node_department_id']]) ? $departmentList[$one['node_department_id']] : '';
            //新部门
            $one['department_name_new'] = isset($departmentList[$one['department_id']]) ? $departmentList[$one['department_id']] : '';

            //查找部门 sys id  SearchSysDeptId 方法
            $one['sys_department_id_new'] = $dep_server->SearchSysDeptId($one['department_id']);

            //职位
            $one['job_title_text'] = isset($job_list[$one['job_title']]) ? $job_list[$one['job_title']] : '';

            $staffItems                 = !empty($staffItemsToId[$one['staff_info_id']]) ? $staffItemsToId[$one['staff_info_id']] : [];
            $one['staff_car_type']      = $staffItems['CAR_TYPE'] ?? '';
            $one['profile_photo_path']  = $staffItems['PROFILE_OBJECT_KEY'] ?? '';
            $one['bank_no_name']        = $staffItems['BANK_NO_NAME'] ?? '';
            $one['area_list']           = isset($staffAreaInfo[$one['staff_info_id']]) ? $staffAreaInfo[$one['staff_info_id']] : [];//区域经理管辖网点表
            $working_day_rest_type_text = '';
            $working_day_rest_type      = 0;
            if (!empty($one['week_working_day']) && !empty($one['rest_type'])) {
                $working_day_rest_type      = $one['week_working_day'] . $one['rest_type'];
                $working_day_rest_type_text = self::$t->_('working_day_rest_type_' . $working_day_rest_type);
            }
            $one['working_day_rest_type_text'] = $working_day_rest_type_text;
            $one['working_day_rest_type']      = $working_day_rest_type;

            //不更新，只导出，需要更新部门的信息
            if (!$isUpdate) {
                $exportDate[] = $one;
                continue;
            }
            //检查，是否可以更新部门信息
            $checkRes = $this->checkJobDepartmentRelation($one);

            if (!empty($checkRes)) {
                $one['fail_reason'] = self::$t->_('update_department_fail_reason_' . $checkRes);
                $exportDate[]       = $one;
                continue;
            }
            $one['fail_reason'] = 'SUCCESS';
            $exportDate[] = $one;

            $pendingData[] = $one;
        }

        return [$exportDate, $pendingData];
    }

    /**
     * 检查，是否可以更新部门信息
     * @param $data
     * @return int
     */
    public function checkJobDepartmentRelation($data)
    {
        $jobDepartmentRelations = HrJobDepartmentRelationRepository::getList([
            'job_id'        => $data['job_title'],
            'department_id' => $data['department_id'],
        ]);
        if (empty($jobDepartmentRelations)) {
            return 1;
        }

        $issetJobLevelRelationIds = [];
        foreach ($jobDepartmentRelations as $oneRelation) {
            if (empty($oneRelation['job_level'])) {
                continue;
            }
            $jobLevelList = explode(',', $oneRelation['job_level']);
            if (in_array($data['job_title_grade_v2'], $jobLevelList)) {
                $issetJobLevelRelationIds[] = $oneRelation['id'];
            }
        }

        if (empty($issetJobLevelRelationIds)) {
            return 2;//职级没有关联
        }

        $jobDepartmentRelationsByIds = HrJobDepartmentRelationRepository::getList(['ids' => $issetJobLevelRelationIds]);
        $issetWorkingDayRestType     = [];
        foreach ($jobDepartmentRelationsByIds as $one) {
            if (empty($one['working_day_rest_type'])) {
                continue;
            }
            $working_day_rest_type = explode(',', $one['working_day_rest_type']);
            if (in_array($data['working_day_rest_type'], $working_day_rest_type)) {
                $issetWorkingDayRestType[] = $one['id'];
            }
        }


        if (empty($issetWorkingDayRestType)) {
            return 3;//工作天数没有关联
        }

        $roleRelation = (new HrJobTitleService())->getRolesBySpecDepartmentAndJobTitle($data['department_id'],
            $data['job_title']);

        if(empty($roleRelation)) {
            return 4;//需要是网点角色
        }
        $roleSelectList   = RoleEnumsModel::find([
            'conditions' => 'type = :type: and role_id in ({role_id:array})',
            'bind'       => ['type' => RoleEnumsModel::TYPE_NETWORK, 'role_id' => $roleRelation],
        ])->toArray();

        if(empty($roleSelectList)) {
            return 4;//需要是网点角色
        }

        $roleRelation = array_column($roleSelectList,  'role_id');

        //员工角色需要 全部 在$roleRelation 里 包含
        $roles = array_diff($data['positions'], array_values($roleRelation));
        if (!empty($roles)) {
            return 4;//需要是网点角色
        }

        return 0;//有关联关系
    }

    /**
     * 变更员工部门，同步ms
     * @param $params
     * @return bool|mixed
     */
    private function syncStaffDepartmentInfoToMs($params)
    {
        $positions = empty($params['positions']) ? [RolesModel::ROLE_DEFAULT] : $params['positions'];

        //查找部门 sys id  SearchSysDeptId 方法
        $sys_department_id = $params['sys_department_id_new'];

        if ($params['sys_store_id'] == GlobalEnums::HEAD_OFFICE_ID) {
            if ((int)$sys_department_id == 0) {
                $this->logger->warning([
                    'syncStaffDepartmentInfoToMs-sys_department_id' => [
                        'params' => $params,
                        'result' => '部门数据错误',
                    ],
                ]);
                return false;
            }
            $organizationId   = $sys_department_id;
            $organizationType = 2;
        } else {
            if (empty($params['sys_store_id'])) {
                $this->logger->warning([
                    'syncStaffDepartmentInfoToMs-sys_store_id' => [
                        'params' => $params,
                        'result' => '网点数据错误',
                    ],
                ]);
                return false;
            }
            $organizationId   = $params['sys_store_id'];
            $organizationType = 1;
        }

        $department_id = empty($sys_department_id) ? null : (string)$sys_department_id;
        if ($params['department_id'] != 0) {
            $department_id = $params['department_id'];
        }

        if (empty($params['staff_car_type'])) {
            $vehicle = null;
        } else {
            $vehicle = $params['staff_car_type'] == 'Bike' ? 0 : 1;
        }

        $areaRoles = (new SettingEnvService())->getSetVal('jurisdiction_area_roles', ',');

        $manage_area_name = [];
        if (!empty(array_intersect($areaRoles, $positions))) {
            $manage_area_name = !empty($params['area_list']) ? array_map('strval', $params['area_list']) : [];
        }
        
        $db = $this->getDI()->get("db_backyard");

        try {
            $db->begin();

            //变更员工，所属部门
            $update_data = [
                'node_department_id' => $params['department_id'],
                'sys_department_id'  => $sys_department_id,
            ];
            $result = $db->updateAsDict(
                (new HrStaffInfoModel())->getSource(),
                $update_data,
                [
                    "conditions" => 'staff_info_id = ?',
                    'bind'       => [$params['staff_info_id']],
                ]
            );

            if (!$result) {
                $this->logger->warning([
                    'syncStaffDepartmentInfoToMs-updateStaff' => [
                        'params' => $params,
                        'result' => '更新员工所属部门失败',
                    ],
                ]);
                throw new Exception('syncStaffDepartmentInfoToMs update staff info fail');
            }

            //增加操作日志
            $after_operate_log_data['node_department_id']   = $params['department_id'];//新部门
            $after_operate_log_data['node_department_name'] = $params['department_name_new'];//新部门

            $before_operate_log_data['node_department_id']   = $params['node_department_id'];//旧部门
            $before_operate_log_data['node_department_name'] = $params['department_name_old'];//旧部门

            // 记录操作日志
            $operate_log             = [
                "staff_info_id" => $params['staff_info_id'],
                "after"         => $after_operate_log_data,
                "before"        => $before_operate_log_data,
                "operater"      => 10000,
            ];
            $hrOperateLogsServiceObj = HrOperateLogsService::getInstance()->init($operate_log);
            $add_ret                 = HrOperateLogsRepository::addOperateLog($hrOperateLogsServiceObj);
            if (!$add_ret) {
                $this->logger->warning([
                    'syncStaffDepartmentInfoToMs-addOperateLog' => [
                        'params' => $params,
                        'result' => '更新员工所属部门添加操作日志失败',
                    ],
                ]);
                throw new Exception('syncStaffDepartmentInfoToMs add operate log fail');
            }

            //需更新数据
            $syncData['staff_info_id']      = $params['staff_info_id'];
            $syncData['name']               = $params['name'];
            $syncData['mobile']             = $params['mobile'];
            $syncData['email']              = $params['email'];
            $syncData['personal_email']     = $params['personal_email'];
            $syncData['formal']             = $params['formal'];
            $syncData['vehicle']            = $vehicle;
            $syncData['state']              = $params['state'];
            $syncData['wait_leave_state']   = $params['wait_leave_state'];
            $syncData['leave_date']         = $params['leave_date'];
            $syncData['operator_id']        = -1;
            $syncData['hire_date']          = date('Y-m-d', strtotime($params['hire_date']));
            $syncData['event']              = 2;//更新
            $syncData['positions']          = $positions ?? [];
            $syncData['organization_id']    = $organizationId;
            $syncData['organization_type']  = $organizationType;
            $syncData['department_id']      = $department_id;
            $syncData['area_list']          = $manage_area_name;
            $syncData['profile_photo_path'] = $params['profile_photo_path'];
            $syncData['is_sub_staff']       = HrStaffInfoModel::IS_SUB_STAFF_NO;
            $syncData['stop_duties_date']   = $params['stop_duties_date'];
            $syncData['job_title']          = $params['job_title'];
            $syncData['mobile_company']     = $params['mobile_company'];
            $syncData['instructor_id']      = $params['instructor_id'];
            $syncData['manager_id']         = $params['manger'];
            $syncData['bank_no']            = $params['bank_no'];
            $syncData['bank_no_name']       = $params['bank_no_name'];
            $syncData['bank_type']          = $params['bank_type'];
            $syncData['hire_type']          = $params['hire_type'];

            $syncFleRet = StaffUtils::syncStaffToFle($syncData);

            $this->logger->info('syncStaffDepartmentInfoToMs sync fle result: ' . $syncFleRet . ' params :' . json_encode($syncData,
                    JSON_UNESCAPED_UNICODE));

            $this->logger->info([
                'syncStaffDepartmentInfoToMs-success' => [
                    'staff_info_id' => $params['staff_info_id'],
                    'before_dept'   => $params['node_department_id'],
                    'after_dept'    => $department_id,
                ],
            ]);

            if(!$syncFleRet) {
                throw new Exception('syncStaffDepartmentInfoToMs 同步ms 异常');
            }
            $db->commit();
            return true;
        }catch (Exception $e){
            $db->rollback();
            $this->logger->error(['function' => 'syncStaffDepartmentInfoToMs', 'message' => $e->getMessage(), 'Line' => $e->getLine(), 'params' => $params]);
        }

        return false;
    }

    /**
     * 获取 区域经理管辖网点表
     * @param $staffIds
     * @return array
     */
    public function getAreaManagerStore($staffIds)
    {
        if (empty($staffIds)) {
            return [];
        }

        $staffIdsBatch = array_chunk($staffIds, 20);

        $data = [];
        foreach ($staffIdsBatch as $oneBatch) {
            if (empty($oneBatch)) {
                continue;
            }
            $manageInfoList = HrAreaManagerStoreModel::find([
                'columns'    => 'staff_info_id,manage_area_name',
                'conditions' => 'staff_info_id in ({staff_info_id:array})',
                'bind'       => ['staff_info_id' => $oneBatch],
                'group'      => 'staff_info_id,manage_area_name',
            ]);


            foreach ($manageInfoList as $manageInfo) {
                $data[$manageInfo['staff_info_id']][] = $manageInfo['manage_area_name'];
            }
        }

        return $data;
    }
}