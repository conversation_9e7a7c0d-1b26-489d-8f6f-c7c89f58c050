<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\FlashOss;

class SvcService extends BaseService
{

    /**
     * hub外协考勤导出
     * @param $local
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function hubOsAttendanceExport($local, $params)
    {
        BaseService::setLanguage($local['locale'] ?? getCountryDefaultLang());
        $service   = reBuildCountryInstance(new HubOsAttendanceService());
        $file_path = $service->handleExport($params, uniqid('hub_attendance_statistics_export') . '.xlsx');
        return (new FlashOss())->signUrl($file_path, 20 * 86400);
    }


}