<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffNotRefundedSuspensionTaskModel;
use app\models\backyard\LeaveScenarioModel;
use app\models\backyard\ResignFileModel;
use App\Models\backyard\StaffLeaveReasonModel;
use App\Models\backyard\SuspensionFileModel;
use App\Models\backyard\TripleToLeaveModel;
use Exception;

class StaffLeaveService extends BaseService
{

    protected $not_return_cod_leave_mail_info_key = 'common_not_return_cod_leave';

    private $payment_markup2type_map = [
        8  => 1, //三天未打卡
        9  => 2, //三天未回款
        10 => 3, //出纳未汇款
    ];


    protected function doLeave($staff): bool
    {
        $leaveManageService = new LeaveManagerService();
        $data               = [
            [
                "staff_info_id"  => $staff['staff_info_id'],
                "type"           => 2,
                "day"            => $staff['stop_begin_date'],
                'payment_markup' => array_search($staff['type'], $this->payment_markup2type_map),
                'leave_reason'   => StaffLeaveReasonModel::LEAVE_REASON_80, //腐败/滥用职权或故意对雇主实施刑事犯罪
                'payment_state'  => 2,
                'leave_scenario' => LeaveScenarioModel::LEAVE_SCENARIO_3,//腐败/滥用职权
                'leave_source'   => HrStaffInfoModel::LEAVE_SOURCE_NOT_PAY,//未缴纳公款
                'leave_type'     => HrStaffInfoModel::LEAVE_TYPE_DISMISSAL_NO_COMPENSATION,//辞退（不赔偿）
            ],
        ];
        $ret                = $leaveManageService->update_staff_state($data);
        if (isset($ret['code']) && $ret['code'] == 0) {
            foreach ($ret['body'] as $k => $v) {
                if ($v['msg'] == 'ok') {
                    // 离职成功
                    return true;
                }
            }
        }
        return false;
    }

    protected function getEmailKey()
    {
        return $this->not_return_cod_leave_mail_info_key;
    }


    protected function sendMail($content): bool
    {
        $mail_content = self::$t->_($this->getEmailKey()) .
            "<table border='1px solid black'>
                <tr>
                    <th>staff info name(ID)</th>
                    <th>Department </th>
                    <th>Store </th>
                    <th>Leave date </th>
                </tr>" .
            $content .
            "</table>";

        $mail_address = (new SettingEnvService())->getSetVal('leave_hr_mail');
        if (empty($mail_address)) {
            $this->logger->info('邮箱为空');
            return true;
        }

        $email = explode(';', $mail_address);
        try {
            (new \App\Library\Mailer((new BiMail)->initConfig()))->send($email, 'Leave staff email', $mail_content);
        } catch (Exception $e) {
            $this->logger->notice(['data' => $content, 'mailToPay' => 'send email fail ' . $e->getMessage()]);
        }
        return true;
    }


    /**
     * 未回公款离职
     * @throws ValidationException
     */
    public function handleUnRefunded($staff_ids): bool
    {
        if (RUNTIME == 'dev' && empty($staff_ids)) {
            throw new ValidationException('请输入工号');
        }
        $service = new StaffNotRefundedSuspensionService();
        $staffs  = $service->getNotRefundStaffsFromTriple($staff_ids);

        $this->logger->info(['data' => $staffs]);

        if (empty($staffs)) {
            return true;
        }

        $mail_staff_detail = '';
        //申请恢复在职的数据
        $waitReinstatementStaff = (new SuspensionService)->getWaitReinstatementList();

        foreach ($staffs as $leaveItem) {
            if (in_array($leaveItem['staff_info_id'], $waitReinstatementStaff)) {
                $this->logger->info(['existWaitReinstatementList' => $leaveItem['staff_info_id']]);
                continue;
            }
            $leaveResult = $this->doLeave($leaveItem);
            if (empty($leaveResult)) {
                $this->logger->error(['leaveFail' => $leaveItem]);
                continue;
            }
            //更新状态
            $updateData = ['leave_date' => date('Y-m-d'), 'updated_at' => date('Y-m-d H:i:s')];
            $bind       = [$leaveItem['id'],];
            $this->getDI()->get('db_backyard')->updateAsDict(
                'triple_to_leave', $updateData, ["conditions" => 'id = ?', 'bind' => $bind,]
            );
            //构建邮件内容
            $str               = "<tr>
                        <td>{$leaveItem['staff_info_name']}({$leaveItem['staff_info_id']})</td>
                        <td>{$this->showDepartmentName($leaveItem['sys_department_id'])} </td>
                        <td>{$this->showStoreName($leaveItem['sys_store_id'])} </td>
                        <td>{$leaveItem['stop_begin_date']} </td>
                   </tr>";
            $mail_staff_detail .= $str;
        }

        if ($mail_staff_detail) {
            $this->sendMail($mail_staff_detail);
        }

        return true;
    }

    /**
     * my 个人代理 离职发送pdf邮件
     * @param $staff_info_id
     * @return bool
     */
    public function agentCreatPdf($resign_file_data)
    {
        $logger        = $this->getDI()->get('logger');
        $staff_info_id = $resign_file_data['staff_info_id'] ?? '';
        $stop_duties_date = $resign_file_data['stop_duties_date'] ?? '';
        try {
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id:',
                'bind'       => [
                    'staff_id' => $staff_info_id,
                ],
            ]);
            $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];
            if (
                !isCountry('MY') ||
                empty($staff_info) ||
                !in_array($staff_info['hire_type'], HrStaffInfoModel::$agentTypeTogether) ||
                !in_array($staff_info['leave_source'],
                    [HrStaffInfoModel::LEAVE_SOURCE_NOT_ATTENDANCE, HrStaffInfoModel::LEAVE_SOURCE_NOT_PAY]) ||
                empty($staff_info['leave_date']) ||
                empty($staff_info['signing_date']) || 
                empty($stop_duties_date)
            ) {
                throw new Exception('入口参数错误');
            }
            $logger->write_log("StaffLeaveService agentCreatPdf resign_file_data:" . json_encode($resign_file_data).
                "staff_info:" . json_encode($staff_info), 'info');
            $tmpPath                   = BASE_PATH . '/app/views/resign_pdf_temp/agent_resign.ftl';
            $boc_pdf_temp_url          = $this->getPdfTemp($tmpPath);
            $companyConfigInfo         = (new \App\Modules\My\Services\CertificateService())->getCompanyConfigInfo(['staff_info_id' => $staff_info_id]);
            $fileData['company_name']  = $companyConfigInfo['company_name'];//公司名
            $fileData['staff_name']    = $staff_info['name'];               //姓名
            $fileData['staff_info_id'] = $staff_info['staff_info_id'];      //工号
            $fileData['signing_date']  = $staff_info['signing_date'];       //合同日期
            $fileData['generate_date'] = date('Y-m-d');                     // 信函日期
            if ($staff_info['leave_source'] == HrStaffInfoModel::LEAVE_SOURCE_NOT_ATTENDANCE) {
                // 连续旷工
                $triple_to_leave_data = TripleToLeaveModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: and type = :type:',
                    'bind'       => [
                        'staff_id' => $staff_info_id,
                        'type'     => TripleToLeaveModel::TYPE_CONTINUE_ABSENT,
                    ],
                    'order'      => 'id desc',
                ]);
                $triple_to_leave_data = !empty($triple_to_leave_data) ? $triple_to_leave_data->toArray() : [];
                if (empty($triple_to_leave_data) || empty($triple_to_leave_data['stop_date_detail'])) {
                    throw new Exception('triple_to_leave.stop_date_detail is null');
                }
                $stop_date_detail = json_decode($triple_to_leave_data['stop_date_detail'], true);
                sort($stop_date_detail);
            }

            if ($staff_info['leave_source'] == HrStaffInfoModel::LEAVE_SOURCE_NOT_PAY) {
                // 个人代理/兼职个人代理未回公款离职
                //未回公款
                $hr_staff_not_refunded_suspension_task_data = HrStaffNotRefundedSuspensionTaskModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_id: or main_staff_info_id = :main_staff_info_id:',
                    'bind'       => [
                        'staff_id' => $staff_info_id,
                        'main_staff_info_id' => $staff_info_id,
                    ],
                    'order'      => 'id desc',
                ]);
                if (empty($hr_staff_not_refunded_suspension_task_data) || empty($hr_staff_not_refunded_suspension_task_data->content_json)) {
                    throw new Exception('hr_staff_not_refunded_suspension_task.business_date is null');
                }
                $_content_json = json_decode($hr_staff_not_refunded_suspension_task_data->content_json, true);
                $_not_pay_details = array_column($_content_json['not_pay_details'] ?? [], 'date');
                if (empty($_not_pay_details)){
                    throw new Exception('hr_staff_not_refunded_suspension_task.business_date is null');
                }
                $fileData['content_business_date'] = implode(',',$_not_pay_details);// 未回公款日期
                $fileData['content']               = '<p>
        Anda telah gagal dan/atau abai untuk memulangkan Cash On Delivery
        (“<strong>COD</strong>”) kepada Syarikat berdasarkan Tahap Perkhidmatan
        pada ' . $fileData['content_business_date'] . '. Selanjutnya, anda juga telah gagal untuk memperbetulkan isu tersebut atas permintaan Syarikat. 
      </p>
      <p>
        Berdasarkan Klausa 2.4 Garis Panduan Perkhidmatan, Syarikat adalah berhak untuk menamatkan Perkanjian dengan serta-merta dan menolak COD dan nilai bungkusan yang tertunggak daripada Fi Perkhidmatan anda.
      </p>';
            } elseif ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
                // 个人代理连续N天旷工离职
                // 连续旷工 - 个人代理
                if (empty($stop_date_detail[0]) || empty($stop_date_detail[count($stop_date_detail) - 1])) {
                    // 没有对应值 直接返回不生成pdf了
                    throw new Exception('triple_to_leave.stop_date_detail is null');
                }
                $fileData['stop_date_detail_date_1'] = date('d-m-Y',
                    strtotime($stop_date_detail[0]));
                $fileData['stop_date_detail_date_2'] = date('d-m-Y',
                    strtotime($stop_date_detail[count($stop_date_detail) - 1]));
                $ic_staff_absent_days = (new SettingEnvService())->getSetVal('IC_staff_absent_days') ?: 0;
                $fileData['content'] = '<p>Anda telah gagal dan/atau abai untuk melaksanakan Perkhidmatan sebanyak '.$ic_staff_absent_days.' hari secara berturut-turut selepas menerima Perkhidmatan yang terjadual dari ' . $fileData['stop_date_detail_date_1'] . ' hingga ' . $fileData['stop_date_detail_date_2'] . '. Selanjutnya, anda juga telah gagal untuk memberikan penjelasan yang memuaskan kepada Syarikat.</p>
      <p>
        Berdasarkan Klausa 2.4 Garis Panduan Perkhidmatan, Syarikat adalah berhak untuk menamatkan Perkanjian dengan serta-merta, dan mengenakan ganti rugi sebanyak RM1,500.00 daripada Fi Perkhidmatan anda. 
      </p>';
            } elseif ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT) {
                // 兼职个人代理一天旷工离职
                if (empty($stop_date_detail[0])) {
                    // 没有对应值 直接返回不生成pdf了
                    throw new Exception('triple_to_leave.stop_date_detail is null');
                }
                $fileData['stop_date_detail_date_1'] = date('d-m-Y',
                    strtotime($stop_date_detail[0]));

                $fileData['content'] = '<p>
        Anda telah gagal dan/atau abai untuk melaksanakan Perkhidmatan selepas menerima Perkhidmatan yang terjadual pada ' . $fileData['stop_date_detail_date_1'] . '. Selanjutnya, anda juga telah gagal untuk memberikan penjelasan yang memuaskan kepada Syarikat.
      </p>
      <p>
        Berdasarkan Klausa 2.4 Garis Panduan Perkhidmatan, Syarikat adalah berhak untuk menamatkan Perkanjian dengan serta-merta.
      </p>';
            } else {
                throw new Exception('leave_reason error');
            }
//            $lang = (new StaffService())->getAcceptLanguage($staff_info_id);
            $t                           = $this->getTranslation('en');
            $email_data['email_title']   = $t->_('agent_resign_email_title',
                [
                    'staff_id'   => $staff_info_id,
                    'staff_name' => $staff_info['name'],
                    'date'       => date('d-m-Y', strtotime($staff_info['leave_date'])),
                ]);
            $email_data['email_content'] = $t->_('agent_suspension_email_content');

            [$header, $footer] = (new SuspensionService())->getBodPdfHeaderFooter($companyConfigInfo);
            $singPdfSetting = [
                'format'              => 'a4',
                'displayHeaderFooter' => true,
                'footerTemplate'      => $footer,
                'headerTemplate'      => $header,
            ];
            $pdfFileName    = str_replace('/', '', $email_data['email_title']);

            $pdfRes = (new FormPdfServer())->getInstance()->generatePdf($boc_pdf_temp_url, $fileData, [],
                $singPdfSetting,
                $pdfFileName, 'attchment');

            if (!isset($pdfRes['object_url'])) {
                throw new Exception('生成pdf失败');
            }
            $insertData['staff_info_id'] = $staff_info_id;
            $insertData['type']          = ResignFileModel::TYPE_AGENT_TERMINATION_CONTRACT_STAFF;
            $insertData['file_url']      = $pdfRes['object_url'];
            $insertData['file_name']     = $pdfFileName;
            $insertData['email_title']   = $email_data['email_title'];
            $insertData['email_content'] = $email_data['email_content'];
            $insertData['operate_id']    = $resign_file_data['operate_id'] ?? 10000;
            $db_by                       = $this->getDI()->get('db_backyard');
            $db_by->insertAsDict('resign_file', $insertData);
            // 发送邮件
            if (empty($staff_info['personal_email'])) {
                throw new Exception('个人邮箱为空，发送邮件失败');
            }
            $email_data['file_url'] = $this->fileDownload($pdfRes['object_url'], '', $pdfFileName . '.pdf');
            // 获取抄送人
            $email_data['cc_email'] = (new SettingEnvService())->getSetVal('email_agent_suspension_resign',',') ?: '';
            $sendResult             = \App\Library\BiMail::send($staff_info['personal_email'],
                $email_data['email_title'],
                $email_data['email_content'], [$email_data['file_url']],
                $email_data['cc_email']);
            if (!$sendResult) {
                throw new Exception('发送邮件失败');
            }
            return true;
        } catch (Exception $e) {
            $logger->write_log("StaffLeaveService agentCreatPdf staff_info_id:" . $staff_info_id . ",message:" . $e->getMessage(),
                "error");
            return false;
        }
    }

}