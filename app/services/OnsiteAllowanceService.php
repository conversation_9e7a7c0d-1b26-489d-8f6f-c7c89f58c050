<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\DateHelper;
use App\Models\backyard\AttendanceDataMonthModel;
use App\Models\backyard\AttendanceDataV2RecalSolidModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\StaffOnsiteDataModel;

class OnsiteAllowanceService extends BaseService
{
    /**
     * 导出和计算 onsite动作
     */
    const TYPE_DOWN = 1;
    const TYPE_CAL  =2;

    /**
     * 获取 Onsite Allowance 数据
     * @param  $params
     * @param  $type
     * @return array
     */
    public function getOnsiteAllowance($params,$type= self::TYPE_DOWN): array
    {
        $start_date = $params['start_date'];  //开始时间
        $end_date   = $params['end_date'];    //结束时间

        if ($type == self::TYPE_CAL && date('Y-m', strtotime($end_date)) == '2025-05') {
            return [];
        }

        $staff_info_data = [];
        $BackyardAttendanceService = new BackyardAttendanceService();
        if (self::TYPE_DOWN == $type) {
            $where        = '';
            $where_params = [];
            if (!empty($params['staff_ids'])) {
                $where                             = ' and hsi.staff_info_id in ({hsi_staff_info_id:array})';
                $where_params['hsi_staff_info_id'] = array_values($params['staff_ids']);
            }
            $where .= '  and hsi.hire_type!=:unpaid_courier:';
            $where_params['unpaid_courier'] = HrStaffInfoModel::HIRE_TYPE_UN_PAID;
            $staff_info_data           = $BackyardAttendanceService->getStatAttendUser($start_date, $end_date, $where, $where_params);
            if (empty($staff_info_data)) {
                return [];
            }
        }
        if (self::TYPE_CAL == $type) {
            $staff_info_data = $params['staff_info_data'] ?? [];
        }

        $onsite_staff_info_ids = array_column($staff_info_data, 'staff_info_id');
        $staff_info_data       = array_column($staff_info_data, null, 'staff_info_id');

        //获取员工工号 和 Onsite 数据
        $onsite_data_list = $this->getStaffOnsiteData($start_date, $end_date, $onsite_staff_info_ids);
        $this->logger->info('方法  getOnsiteAllowance onsite_data_list 数据量==>'.count($onsite_data_list));
        if (empty($onsite_data_list)) {
            return [];
        }
        $staff_info_ids = array_column($onsite_data_list, 'staff_info_id');

        //获取考勤信息
        $attendance_data = [];
        if (self::TYPE_DOWN == $type) {
            $attendance_data = $BackyardAttendanceService->getStaffAttendanceData($start_date, $end_date, $staff_info_ids,AttendanceDataV2RecalSolidModel::class);
        }
        if (self::TYPE_CAL == $type) {
            $attendance_data = $params['attendance_data'] ?? [];
        }

        if (empty($attendance_data)) {
            return [];
        }
        $dt_data = [];
        //考勤周期
        $date_range = DateHelper::DateRange(strtotime($start_date), strtotime($end_date));
        //获取揽件量配置项
        $onsite_pickup_date_range = $this->getOnsitePickupEnv($date_range);
        //拼装数据
        foreach ($staff_info_ids as $staff_info_id) {
            if (!isset($staff_info_data[$staff_info_id])) {
                continue;  //不存在该工号  跳出
            }
            $staff_info                                                = $staff_info_data[$staff_info_id];
            $dt_data[$staff_info_id]                                   = [
                'department'    => $staff_info['department_name'],
                'branch'        => $staff_info['store_name'],
                'staff_info_id' => $staff_info['staff_info_id'],
                'staff_name'    => $staff_info['staff_info_name'],
                'position'      => $staff_info['job_name'],
                'hire_date'     => $staff_info['hire_date'],
                'leave_date'    => $staff_info['leave_date'],
                'period'        => date('m-d', strtotime($start_date)).'-'.date('m-d', strtotime($end_date)),
            ];
            $dt_data[$staff_info['staff_info_id']]['onsite_days']      = $onsite_days = 0;      // 当月给onsite补贴的天数， On(出勤)+BT(出差)+CT（培训） 计算出当月给onsite补贴的天数
            $dt_data[$staff_info['staff_info_id']]['onsite_allowance'] = $onsite_allowance = 0; // 每日Onsite Allowance Daily加和

            foreach ($date_range as $date) {
                //每日字段初始化
                if ($date < '2025-05-01') {
                    [
                        $onsite_days_daily,
                        $onsite_allowance_daily,
                    ] = $this->dealStaffDailyData($dt_data,$staff_info['staff_info_id'],
                        $date, $onsite_pickup_date_range, $onsite_data_list,$attendance_data);
                } else {
                    [
                        $onsite_days_daily,
                        $onsite_allowance_daily,
                    ] = $this->dealStaffDailyDataV2($dt_data,$staff_info['staff_info_id'],
                        $date,  $onsite_data_list,$attendance_data);
                }
                $onsite_days      += $onsite_days_daily;
                $onsite_allowance += $onsite_allowance_daily;

            }
            $dt_data[$staff_info['staff_info_id']]['onsite_days']      = $onsite_days;     // 当月给onsite补贴的天数， On(出勤)+BT(出差)+CT（培训） 计算出当月给onsite补贴的天数
            $dt_data[$staff_info['staff_info_id']]['onsite_allowance'] = $onsite_allowance;// 每日Onsite Allowance Daily加和
        }
        return self::TYPE_DOWN == $type ? $dt_data : array_column($dt_data,'onsite_allowance','staff_info_id');
    }

    /**
     * 25年4月30日及之前的
     * @param $dt_data
     * @param $staff_info_id
     * @param $date
     * @param $onsite_pickup_date_range
     * @param $onsite_data_list
     * @param $attendance_data
     * @return array|int[]
     */
    protected function dealStaffDailyData(&$dt_data,$staff_info_id,$date,$onsite_pickup_date_range,$onsite_data_list,$attendance_data): array
    {
        $key = $staff_info_id.'-'.$date;
        $dt_data[$staff_info_id][$date.'onsite_days_daily']      = '/';                             //当日给补贴的天数
        $dt_data[$staff_info_id][$date.'quantity']               = '/';                             //当天揽件量
        $dt_data[$staff_info_id][$date.'onsite_allowance_daily'] = '/';                             //当天onsite补贴金额  3. 若当日该员工不是onsite仓管，则显示为“/”
        // 当日没有 Onsite 数据 ||  当日不存在 配置揽件量
        if (!isset($onsite_data_list[$key]) || !isset($onsite_pickup_date_range[$date])) {
            return [0,0] ;
        }
        //当天onsite补贴金额
        $dt_data[$staff_info_id][$date.'onsite_allowance_daily'] = '0';                             //   2. 若当日onsite揽件量小于配置的值,当日补贴金额=0

        //揽件量要显示
        $dt_data[$staff_info_id][$date.'quantity'] = $onsite_data_list[$key]['pickup_count'] ?? '/';//当天揽件量
        //该员工当日考勤数据，直接忽略  || 揽件量小于配置揽件量
        if (!isset($attendance_data[$key]) || $onsite_pickup_date_range[$date] > $onsite_data_list[$key]['pickup_count']) {
            return [0,0] ;
        }
        //计算当日onsite补贴的天数  On(出勤)+BT(出差)+CT（培训)
        $onsite_days_daily                                                = $attendance_data[$key]['attendance_time'] + $attendance_data[$key]['CT'] + $attendance_data[$key]['BT'];
        $dt_data[$staff_info_id][$date.'onsite_days_daily'] = $onsite_days_daily = empty($onsite_days_daily) ? 0 : ($onsite_days_daily / 10);
        //当日补贴金额=Onsite Days Daily *100
        $dt_data[$staff_info_id][$date.'onsite_allowance_daily'] = $onsite_allowance_daily = empty($onsite_days_daily) ? 0 : (bcmul($onsite_days_daily,
            100));
        //累计
        return [$onsite_days_daily,$onsite_allowance_daily];
    }

    /**
     * 25年5月1日及之后的
     * @param $dt_data
     * @param $staff_info_id
     * @param $date
     * @param $onsite_data_list
     * @param $attendance_data
     * @return array|int[]
     */
    protected function dealStaffDailyDataV2(&$dt_data,$staff_info_id, $date,  $onsite_data_list,$attendance_data): array
    {
        $key                                                       = $staff_info_id . '-' . $date;
        $dt_data[$staff_info_id][$date . 'onsite_days_daily']      = '/';//当日给补贴的天数
        $dt_data[$staff_info_id][$date . '_5_10_quantity']         = '/';
        $dt_data[$staff_info_id][$date . '_10_20_quantity']        = '/';
        $dt_data[$staff_info_id][$date . '_20_more_quantity']      = '/';
        $dt_data[$staff_info_id][$date . 'onsite_allowance_daily'] = '/';
        // 当日没有 Onsite 数据 ||  当日不存在 配置揽件量
        if (!isset($onsite_data_list[$key])) {
            return [0, 0];
        }
        //当天onsite补贴金额
        $dt_data[$staff_info_id][$date . 'onsite_allowance_daily'] = '0';

        //该员工当日考勤数据，直接忽略  || 揽件量小于配置揽件量
        if (!isset($attendance_data[$key])) {
            return [0, 0];
        }
        //计算当日onsite补贴的天数  On(出勤)+BT(出差)+CT（培训)
        $onsite_days_daily                                    = $attendance_data[$key]['attendance_time'] + $attendance_data[$key]['CT'] + $attendance_data[$key]['BT'];
        $dt_data[$staff_info_id][$date . 'onsite_days_daily'] = $onsite_days_daily = empty($onsite_days_daily) ? 0 : ($onsite_days_daily / 10);
        //当日补贴金额
        $dt_data[$staff_info_id][$date . '_5_10_quantity']    =  $onsite_data_list[$key]['weight_5_10kg'];
        $_5_10_quantity =  bcmul($onsite_data_list[$key]['weight_5_10kg'], '0.1',1);
        $dt_data[$staff_info_id][$date . '_10_20_quantity']   = $onsite_data_list[$key]['weight_10_15kg'] + $onsite_data_list[$key]['weight_15_20kg'];
        $_10_20_quantity = bcmul(($onsite_data_list[$key]['weight_10_15kg'] + $onsite_data_list[$key]['weight_15_20kg']), '0.2',1);
        $dt_data[$staff_info_id][$date . '_20_more_quantity'] =  $onsite_data_list[$key]['weight_20_25kg'] + $onsite_data_list[$key]['weight_above_25kg'];
        $_20_more_quantity = bcmul(($onsite_data_list[$key]['weight_20_25kg'] + $onsite_data_list[$key]['weight_above_25kg']), '0.3',1);
        //累计
        $onsite_allowance_daily = empty($onsite_days_daily) ? 0 : (bcadd(bcadd($_5_10_quantity,$_10_20_quantity,1),$_20_more_quantity,1));
        $dt_data[$staff_info_id][$date . 'onsite_allowance_daily'] = $onsite_allowance_daily;
        return [$onsite_days_daily, $onsite_allowance_daily];
    }



    /**
     * @description:获取 Onsite  数据 主要是 员工  日期 揽件量  这里的数据 是 过滤好的
     * @param string $start_date
     * @param string $end_date
     * @param array $staff_ids 工号
     * @return     :[]
     * <AUTHOR> L.J
     * @time       : 2022/9/7 16:16
     */
    public function getStaffOnsiteData($start_date, $end_date, $staff_info_ids = []): array
    {
        $staff_ids = array_chunk($staff_info_ids, 500);
        $result    = [];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $column  = "concat(staff_info_id, '-', stat_date) as unique_key,staff_info_id,stat_date,pickup_count,weight_0_5kg,weight_5_10kg,weight_10_15kg,weight_15_20kg,weight_20_25kg,weight_above_25kg";
            $builder->columns($column);
            $builder->from(StaffOnsiteDataModel::class);
            $builder->betweenWhere('stat_date', $start_date, $end_date);
            $builder->inWhere('staff_info_id', $staff_id);
            $builder->andWhere('is_del = 0');
            $res    = $builder->getQuery()->execute()->toArray();
            $result = array_merge($result, $res);
        }
        return empty($result) ? [] : array_column($result, null, 'unique_key');
    }


    /**
     * @description:  //获取onsite揽件量配置 每日阀值配置
     * @param $date_range 日期范围
     * @return     :[ '2022-09-01'=>'10','2022-09-02'=>'20']
     * <AUTHOR> L.J
     * @time       : 2022/9/14 17:09
     */
    public function getOnsitePickupEnv($date_range = []): array
    {
        //获取阀值配置
        $setting_env                 = new SettingEnvService();
        $onsite_rules_date_env       = $setting_env->getSetVal('onsite_rules_date');
        $onsite_rules_date_env       = explode(',', $onsite_rules_date_env);
        $onsite_pickup_count_env_arr = [];
        foreach ($onsite_rules_date_env as $k => $v) {
            $env                                             = explode('|', $v);
            $onsite_pickup_count_env_arr[strtotime($env[0])] = $env[1]; // strtotime('2022-09-01')='10'; //日期=>揽件量
        }
        $onsite_pickup_date_range = [];
        foreach ($date_range as $v) {
            $v_time = strtotime($v);
            foreach ($onsite_pickup_count_env_arr as $date_time => $pickup_count) {
                if ($v_time >= $date_time) {
                    $onsite_pickup_date_range[$v] = $pickup_count;
                }
            }
        }
        return $onsite_pickup_date_range;
    }

    /**
     * onsite Allowance 同步到月度考勤中
     * @param $end_date
     * @param $data_list
     */
    public function onsiteAllowanceToAttendanceMonth($end_date, $data_list)
    {
        $data =  array_column($data_list, 'onsite_allowance', 'staff_info_id');

        if (in_array($end_date, ['2025-04-30', '2025-05-23'])) {
            return;
        }
        $month =  date('Y-m', strtotime($end_date));

        $model         = AttendanceDataMonthModel::class;
        $bind['month'] = $month;
        $totalNum      = count($data);
        $errorNum      = 0;
        foreach ($data as $staffId => $onsiteAllowance) {
            $bind['staff_id'] = $staffId;
            $bind['count']    = $onsiteAllowance;
            $result           = $this->modelsManager->executeQuery("update {$model} set onsite_allowance=:count: where staff_info_id=:staff_id: and stat_period=:month:",
                $bind)->success();
            if (!$result) {
                ++$errorNum;
            }
        }
        $this->logger->info("Month:{$month} , total onsite allowance :{$totalNum}, update month error:{$errorNum}");
    }
}
