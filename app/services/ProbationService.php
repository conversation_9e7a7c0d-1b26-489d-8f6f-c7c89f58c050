<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MessWarningEnums;
use App\Library\Enums\StaffEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AssetsHandoverModel;
use App\Models\backyard\HrJobTitleModel;
use app\models\backyard\HrProbationActValuesModel;
use App\Models\backyard\HrProbationAttendanceContractWorkerModel;
use App\Models\backyard\HrProbationAttendanceModel;
use App\Models\backyard\HrProbationAuditContractWorkerModel;
use App\Models\backyard\HrProbationAuditModel;
use App\Models\backyard\HrProbationContractWorkerModel;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\HrProbationResignModel;
use App\Models\backyard\HrProbationTargetBusinessModel;
use App\Models\backyard\HrProbationTargetDetailModel;
use App\Models\backyard\HrProbationTargetModel;
use App\Models\backyard\HrProbationTplItemModel;
use App\Models\backyard\HrProbationTplModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\MessagePdf;
use App\Models\backyard\MessageWarningModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffResignModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysProvinceModel;
use App\Repository\HrProbationTargetRepository;
use App\Modules\My\library\Enums\SalaryEnums;
use Mpdf\Mpdf;
use Phalcon\Mvc\Model\Query\Builder;
use Phalcon\Mvc\View;

class ProbationService extends BaseService
{
    public static $areaProvince = []; // 区域包含省份编码
    public static $storeList = [];

    //职级分界线
    public $job_grade = 17;    //职级分界线  <= 14

    public $probation_first_deadline_start = 40; //试用期第一次评估期限开始
    public $probation_first_deadline_end = 45;   //试用期第一次评估期限结束
    public $probation_second_deadline_start = 75;  //试用期第二次评估期限开始
    public $probation_second_deadline_end = 85;    //试用期第二次评估期限结束
    public $formal_days = 120; //转正日期
    //这里是职级大于 17 的参数
    public $probation_first_deadline_start_two = 40; //试用期第一次评估期限开始
    public $probation_first_deadline_end_two = 48;   //试用期第一次评估期限结束
    public $probation_second_deadline_start_two = 75;  //试用期第二次评估期限开始
    public $probation_second_deadline_end_two= 88;    //试用期第二次评估期限结束
    public $formal_days_two = 120; //转正日期



    const CUR_LEVEL_FIRST = 1;  //第一次评审
    const CUR_LEVEL_SECOND = 2; //第二次评审

    const AUDIT_STATUS_PENDING = 1; //审核状态，待处理
    const AUDIT_STATUS_DEAL = 2;    //审核状态，已处理
    const AUDIT_STATUS_TIMEOUT = 3; //审核状态，已超时
	
	public $currentProbationChannelType;

	public $edit_day = 91;

    protected $firsEvaluateDateFlag = true;

    public function setFirsEvaluateDateFlag($flag)
    {
        $this->firsEvaluateDateFlag = $flag;
    }
    public function getFirsEvaluateDateFlag()
    {
        return $this->firsEvaluateDateFlag;
    }
    protected $nonFrontLineFlag = false;
    public function setNonFrontLineFlag($flag)
    {
        $this->nonFrontLineFlag = $flag;
    }
    public function getNonFrontLineFlag()
    {
        return $this->nonFrontLineFlag;
    }

    // 查询接口参数验证
    public static $probation_list_validate = [
        'page'               => 'IntGe:0',
        'pagesize'           => 'IntGe:0',
        'manage_staff_id'    => 'IntGe:0',                 //直接上级
        'job_title'          => 'Arr',                     //职位 IntGe:0
        'job_title[*]'       => 'IntGe:0',
        'department'         => 'IntGe:0',                 //一级部门
        'node_department_id' => 'IntGe:0',                 //二级部门
        'hire_date_begin'    => 'Date',                    //入职开始时间
        'hire_date_end'      => 'Date',                    //入职结束时间
        'status'             => 'Arr',                     //试用期状态 'IntIn:1,2,3,4',
        'status[*]'          => 'IntIn:1,2,3,4',
        'store_area_id'      => 'Arr',                     //所属区域 'IntGe:0'
        'store_area_id[*]'   => 'StrLenGe:1',
        'first_date_begin'   => 'Date',                    //第一次期限开始时间
        'first_date_end'     => 'Date',                    //第一次期限结束时间
        'second_date_begin'  => 'Date',                    //第二次期限开始时间
        'second_date_end'    => 'Date',                    //第二次期限结束时间
        'state'              => 'Arr',                     //状态
        'store'              => 'Arr',                     // 所属网点
        'store[*]'           => 'StrLenGeLe:1,10',
        'working_country'    => 'Arr',                     // 工作所在国家
        'working_country[*]' => 'StrLenGe:1',
        'is_sub_department'  => 'Required|IntIn:0,1|>>>:is_sub_department param error', // 是否包含子部门，0 不包含 1 包含
        'first_audit_status'     => 'Arr',
        'first_audit_status[*]'  => 'IntIn:1,2,3,4',
        'second_audit_status'    => 'Arr',
        'second_audit_status[*]' => 'IntIn:1,2,3,4',
        'first_status'           => 'IntIn:0,1',
        'second_status'          => 'IntIn:0,1',
    ];

    public function __construct() {
        parent::__construct();
        //获取配置项
        $probation_evaluate_env                = $this->getProbationEvaluateEnv();
        $this->job_grade                       = $probation_evaluate_env['job_grade_exceed'] ?? $this->job_grade;
        $this->formal_days                     = $probation_evaluate_env['formal_days'] ?? $this->formal_days;
        $this->probation_first_deadline_start  = $probation_evaluate_env['evaluate_day'][1] ??
                                                 $this->probation_first_deadline_start;
        $this->probation_first_deadline_end    = $probation_evaluate_env['first_check_days'] ?? $this->probation_first_deadline_end;
        $this->probation_second_deadline_start = $probation_evaluate_env['evaluate_day'][2] ??
                                                 $this->probation_second_deadline_start;
        $this->probation_second_deadline_end   = $probation_evaluate_env['second_check_days'] ?? $this->probation_second_deadline_end;

        $this->probation_first_deadline_start_two  = $probation_evaluate_env['evaluate_day_exceed'][1] ??
                                                     $this->probation_first_deadline_start_two;
        $this->probation_first_deadline_end_two    = $probation_evaluate_env['first_check_days_exceed'] ?? $this->probation_first_deadline_end_two;
        $this->probation_second_deadline_start_two = $probation_evaluate_env['evaluate_day_exceed'][2] ??
                                                     $this->probation_second_deadline_start_two;
        $this->probation_second_deadline_end_two   = $probation_evaluate_env['second_check_days_exceed'] ?? $this->probation_second_deadline_end_two;
        $this->formal_days_two                     = $probation_evaluate_env['formal_days_exceed'] ?? $this->formal_days_two;
        $this->job_grade_exceed                     = $probation_evaluate_env['job_grade_exceed'] ?? $this->formal_days_two;


    }
    /**
     * 延长该员工试用期
     * @param $staff_info_id
     * @param $user_id
     * @return string|boolean
     */
    public function delay($staff_info_id,$user_id){

        $single = (new Builder)->columns(['hp.*','hsi.hire_date'])
            ->from(['hp' => HrProbationModel::class])
            ->leftJoin(HrStaffInfoReadModel::class,'hp.staff_info_id = hsi.staff_info_id','hsi')
            ->where(' hp.staff_info_id = :id:',['id' => intval($staff_info_id)])->getQuery()->getSingleResult();

        if(! $single){
            return 'not found user';
        }
        $item = $single->toArray();

        if(!$this->isCanDelay($item)){
            return 'can not delay';
        }


        $data = [];
        $data['is_delay'] = 1;
        $data['formal_at'] = $this->getDateByDays($item['hire_date'],150,1);
        $data['delay_staff_id'] = $user_id;

        $db = $this->getDI()->get("db_backyard");

        try {
            $db->begin();
            $db->updateAsDict("hr_probation", $data, ["conditions" => 'staff_info_id=' .intval($staff_info_id)]);
            //对正在审核的改成超时
            $sql = "update hr_probation_audit set audit_status=:audit_status where staff_info_id=:staff_info_id and cur_level=:cur_level";
            $db->execute($sql,['audit_status'=>self::AUDIT_STATUS_TIMEOUT,"staff_info_id"=>$staff_info_id,"cur_level"=>self::CUR_LEVEL_SECOND]);
            $db->commit();
        }catch (\Exception $e){
            $db->rollback();
            return 'error';
        }
        return true;
    }
    /**
     * 是否能延长试用期，先隐藏。这个功能砍掉了
     * @param $item
     * @return string
     */
    public function isCanDelay($item): string
    {
        return '0';
        $now_time = gmdate("Y-m-d", time() + ($this->timeOffset)*3600);
        $item['days_90'] = $this->getDateByDays($item['hire_date'],90,1);
        $item['days_75'] = $this->getDateByDays($item['hire_date'],75,1);

        //试用期，且在入职90天之前,且没有延长过
        if($item['status'] == StaffEnums::STATUS_PROBATION && $now_time<$item['days_90'] && $now_time>$item['days_75'] && $item['is_delay'] == '0'){
            return '1';
        }
        return '0';
    }

    /**
     * 修改转正
     * @param $params
     * @return string|boolean
     * @throws ValidationException
     * @throws BusinessException
     */
    public function formal($params)
    {
        if (isCountry('PH')) {
            $staffInfo = (new StaffService())->getHrStaffInfo($params['staff_info_id']);
            if ($staffInfo && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                throw  new BusinessException(static::$t->_('probation_not_edit_tip'));
            }
        }

        $single           = HrProbationModel::query()->where('staff_info_id = :id:',
            ['id' => $params['staff_info_id']])->execute()->getFirst();
        $item             = $single ? $single->toArray() : [];
        $status           = 1;
        $formal_at        = '';
        $is_change_status = false;
        if (!empty($item)) {
            $status    = $item['status'];
            $formal_at = $item['formal_at'];
            if ($item['status'] != $params['status']) {
                $is_change_status = true;
            }
        }

        //1->4  试用期-已转正
        //2->4  已通过-已转正
        //3->4  未通过-已转正
        if (($status == 1 && $params['status'] == 4) || ($status == 2 && $params['status'] == 4) || ($status == 3 && $params['status'] == 4)) {
            $data['formal_staff_id'] = $params['user_id'];
        }

        $data               = [];
        $data['status']     = $params['status'];
        $data['formal_at']  = date("Y-m-d", strtotime($params['formal_at']));
        $data['updated_at'] = date("Y-m-d H:i:s");
        $data['mark']       = $params['mark'] ?? '';
        //如果没有则添加
        if (empty($item)) {
            $data['created_at']    = $data['updated_at'];
            $data['staff_info_id'] = $params['staff_info_id'];
            $data['is_system']     = 1;
            return $this->getDI()->get("db_backyard")->insertAsDict("hr_probation", $data);
        }
        //记录转正记录
        $this->putFormalLog($params['user_id'], $params['staff_info_id'], $status, $data['status'], $formal_at,
            $data['formal_at']);

        // 如果 后台更改了试用期状态 则发送转正评估、发送通知、转正脚本等都不在运行
        if ($is_change_status) {
            $data['is_system'] = 1;
        }

        return $this->getDI()->get("db_backyard")->updateAsDict("hr_probation", $data,
            ["conditions" => 'id='.intval($item['id'])]);
    }

    /**
     * bi直接编辑分数
     * @param $staff_info_id
     * @param $score
     * @param $pic
     * @param $remark
     * @param $userId
     * @param string $job_content
     * @return array
     * @throws BusinessException
     */
    public function auditBi($staff_info_id, $score, $pic, $remark, $userId, $job_content = '')
    {
        if (isCountry('PH')) {
            $staffInfo = (new StaffService())->getHrStaffInfo($staff_info_id);
            if ($staffInfo && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_2) {
                throw  new BusinessException(static::$t->_('probation_not_edit_tip'));
            }
        }
        $db = $this->getDI()->get("db_backyard");
        $db->begin();
        try {
            $single = (new Builder)->columns([
                'hp.status',
                'hp.formal_at',
                'hp.id',
                'hp.staff_info_id',
                'hsi.job_title_level',
                'hsi.hire_date',
                'hsi.job_title_grade_v2',
            ])
                ->from(['hp' => HrProbationModel::class])
                ->leftJoin(HrStaffInfoModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hsi')
                ->where(' hp.staff_info_id = :id:', ['id' => intval($staff_info_id)])->getQuery()->getSingleResult();

            if (!$single) {
                //没有的话直接插入一条
                //查询入职日期
                $HrStaffInfo = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: ',
                    'bind'       => [
                        'staff_info_id' => $staff_info_id,
                    ],
                ]);
                if (empty($HrStaffInfo)) {
                    throw new \Exception('没有找到该用户数据'.$staff_info_id);
                }
                $formal_days = (int)$HrStaffInfo->job_title_grade_v2 <= $this->job_grade ? $this->formal_days : $this->formal_days_two;
                $formal_at   = $this->getDateByDays($HrStaffInfo->hire_date, $formal_days, 1);
                $arr         = [
                    'staff_info_id' => $staff_info_id,
                    'status'        => 1,
                    'is_system'     => 1,
                    'formal_at'     => $formal_at,
                    'created_at'    => gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600),
                ];
                $db->insertAsDict('hr_probation', $arr);
                $arr['id']                 = $db->lastInsertId();
                $arr['job_title_grade_v2'] = $HrStaffInfo->job_title_grade_v2;
            } else {
                $arr = $single->toArray();
            }
            $lang = (static::$t);

            $item = []; //hr_probation表

            //如果info与库里不同,证明已经修改
            foreach ($score['list'] as $k => $v) {
                foreach ($v['list'] as $kk => $vv) {
                    if ($lang->_($vv['info_key']) != $vv['info']) {
                        $score['list'][$k]['list'][$kk]['is_update'] = 1;
                    }
                }
            }

            $arr['tpl_id'] = $this->getTplIdByJobTitleGradeV2($arr['job_title_grade_v2']);

            //自己根据规则算一遍
            $score = $this->getScoreFromTpl($score, 2);
            $item['updated_at']  = gmdate("Y-m-d H:i:s", time() + ($this->timeOffset) * 3600);

            //第二次
            $score_num            = $this->getScore($score['second_score'], 1);
            $item['second_score'] = $score_num;
            $item['is_system']    = 1;
            if ($score_num >= 6000) {
                $item['status'] = StaffEnums::STATUS_PASS;    //已通过
                $item['second_status']     = HrProbationModel::SECOND_STATUS_PASS;
                //如果当前时间，大于转正时间，且分数大于等于6分，直接已转正
                if (strtotime($item['updated_at']) >= strtotime($arr['formal_at'])) {
                    $item['status']          = StaffEnums::STATUS_FORMAL;
                    $item['formal_staff_id'] = $userId;
                }
            } else {
                $item['status'] = StaffEnums::STATUS_NOT_PASS;    //未通过
                $item['second_status']     = HrProbationModel::SECOND_STATUS_NOT_PASS;
            }
            if (!empty($remark)) {
                $item['remark'] = $remark;
            }

            // 如果状态为已经通过，但是还未到转正日期 则后续转正脚本需要跑，插入转正评估数据等不许在在执行
            if ($item['status'] == StaffEnums::STATUS_PASS){
                $item['is_system']    = 2;
            }

            $db->updateAsDict("hr_probation", $item, ["conditions" => 'id='.intval($arr['id'])]);
            //下一级评审，内容大部分相同
            $tmp                  = [];
            $tmp['probation_id']  = $arr['id'];
            $tmp['staff_info_id'] = $arr['staff_info_id'];
            $tmp['tpl_id']        = $arr['tpl_id'];
            $tmp['audit_id']      = $userId;
            $tmp['audit_level']   = 2;
            $tmp['cur_level']     = self::CUR_LEVEL_SECOND;
            $tmp['audit_status']  = self::AUDIT_STATUS_DEAL;
            $tmp['status']        = $item['status'];
            $tmp['score']         = json_encode($score, JSON_UNESCAPED_UNICODE);
            $tmp['created_at']    = $item['updated_at'];
            $tmp['updated_at']    = $item['updated_at'];
            $tmp['remark']        = $remark;
            $tmp['pic']           = $pic;
            $tmp['deadline_at']   = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);
            $tmp['job_content']   = $job_content;
            $db->insertAsDict("hr_probation_audit", $tmp);

            if ($item['status'] == StaffEnums::STATUS_FORMAL) {
                $this->putFormalLog($userId, $staff_info_id, $arr['status'], StaffEnums::STATUS_FORMAL);
            }
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDi()->get('logger')->error('hcm 编辑编辑分数：'.json_encode(
                    [
                        'Err_Msg'  => $e->getMessage(),
                        'Err_Line' => $e->getLine(),
                        'Err_File' => $e->getFile(),
                        'Err_Code' => $e->getCode(),
                    ], JSON_UNESCAPED_UNICODE));
            return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'Please try again later', 'data' => []];
        }
        $db->commit();
        return ['code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => []];
    }
    /**
     * 记录转正日志
     * @param $operaterId
     * @param $staffInfoId
     * @param $beforeStatus
     * @param $afterStatus
     * @return mixed
     */

    public function putFormalLog($operaterId, $staffInfoId, $beforeStatus, $afterStatus,$formal_at='',$to_formal_at=''){

        $result = (new SettingEnvService())->getSetVal('db_migration_20220515');
        if($result){
            throw new ValidationException(self::$t->_('system_upgraded'));
        }
        $before = [];
        $before['body'] = [];
        $before['body']['staff_info_id'] = $staffInfoId;
        $before['body']['probation_status'] = $beforeStatus;

        $after = $before;
        $after['body']['probation_status'] = $afterStatus;
	    if(!empty($formal_at) || !empty($to_formal_at)){
		    $before['body']['formal_at'] = $formal_at;
		    $after['body']['formal_at'] = $to_formal_at;
	    }

        $data =[
            [
                "operater" => $operaterId,
                "staff_info_id" => $staffInfoId,
                "type" => 'probation',
                "before" => json_encode($before, JSON_UNESCAPED_UNICODE),
                "after" => json_encode($after, JSON_UNESCAPED_UNICODE),
            ],
        ];
        $client =  new ApiClient('hris','','add_operate_logs');
        $client->setParams($data);
        return  $client->execute();

    }
    /**
     * @param $staff_info_id
     * @return array|string
     * @throws \Exception
     */
    public function editDetail($staff_info_id,$probationChannelType = HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL)
    {

        $model = $probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ?HrProbationContractWorkerModel::class : HrProbationModel::class;
        $builder = new Builder();
        $single = $builder->columns(['hsi.staff_info_id','hsi.name','hsi.name_en','hsi.job_title_level','hsi.job_title_grade_v2','hsi.hire_date','hp.status','hp.cur_level','hp.hire_type','hp.probation_channel_type'])
            ->from(['hsi' => HrStaffInfoReadModel::class])
            ->leftJoin($model,'hp.staff_info_id = hsi.staff_info_id','hp')
            ->where('hsi.staff_info_id =:id:',['id' => intval($staff_info_id)])->getQuery()->getSingleResult();


        if(! $single){
            return 'not found user';
        }
        $item = $single->toArray();
        if (empty($item['probation_channel_type'])) {
            $item['probation_channel_type'] = HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL;
        }
        //找最新的一次
        $conditions = [
            'conditions' => 'staff_info_id = ?1',
            'order' => 'id desc',
            'bind'       => [
                1 =>  $staff_info_id,
            ],
        ];
        if ($probationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3) {
            $auditDataFind = HrProbationAuditContractWorkerModel::findFirst($conditions);
        } else {
            $auditDataFind = HrProbationAuditModel::findFirst($conditions);
        }

        $auditData =   $auditDataFind ? $auditDataFind->toArray() : [];
        if(empty($auditData)){
            $tpl = $this->getTplItem($this->getTplIdByJobTitleGradeV2($item['job_title_grade_v2']),[]);
        }else{
            $tpl = $this->getTplItem($auditData['tpl_id'], $auditData['score']);
        }
        if (is_string($tpl)) {
            throw new \Exception($tpl);
        }

        $item['score'] = $tpl['score'];
	    $item['job_content'] = $auditData['job_content'] ?? '';
	    $item['score_rule_text'] = (static::$t)->_('hr_probation_field_all_info') . ($tpl['score_rule_text'] ?? '');
	    //
        return $item;
    }

    /**
     * 详情
     * @param $staffInfoId
     * @return array
     */
    public function getDetail($staffInfoId): array
    {
        $leftJoinModel = $this->currentProbationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? HrProbationContractWorkerModel::class : HrProbationModel::class;
        $builder = new Builder();
        $single = $builder->columns(['hsi.staff_info_id','hsi.name','hsi.name_en','hp.first_score','hp.second_score','hp.status','hp.id','hp.formal_at','hp.hire_type','hp.probation_channel_type','hp.first_audit_status','hp.second_audit_status'])
            ->from(['hsi' => HrStaffInfoReadModel::class])
            ->leftJoin($leftJoinModel,'hp.staff_info_id = hsi.staff_info_id','hp')
            ->where('hsi.staff_info_id =:id:',['id' => intval($staffInfoId)])->getQuery()->getSingleResult();
        if (! $single) {
            return [];
        }
        $item =  $single->toArray();
        $item['first_score'] = $item['first_score'] ? $this->getScore($item['first_score']) : '0';
        $item['second_score'] = $item['second_score'] ? $this->getScore($item['second_score']) : '0';

        if (empty($item['status'])) {
            $item['status'] = 1;
        }

        $item['name_en'] = $item['name_en'] ?? '';
        $item['pic'] = "";
        $item['audit_logs'] = [];
        $item['remark'] = "";
        $item['status_text'] = (static::$t)->_("probation_status_" . $item['status']);
        if (!empty($item['id'])) {
            $tmp = $this->getProbationLogs($item['id'],$item);

            $item['audit_logs'] = $tmp['data'];
            $item['remark'] = $tmp['remark'];
            $item['pic'] = $tmp['pic'];
        }
        return $item;
    }
    /**
     * 获得分数
     * @param $score
     * @param int $flag =0，除以1000，=1乘以1000
     * @return float|int|string|null
     */
    public function getScore($score, $flag = 0)
    {
        if (empty($flag)) {
            return bcdiv($score, 1000, 2);
        } else {
            return floatval($score) * 1000;
        }
    }
    /**
     * 下载
     */
    public function download($staff_info_id)
    {
        $data = [];
        //只有中泰
        $lang_flag = 1;
        $builder = new  Builder();
        $result = $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.job_title_level',
            'hsi.job_title_grade_v2',
            'hsi.hire_date',
            'job.job_name',
            'd.name as department_name',
            'n.name as node_department_name',
            'hp.id as probation_id',
            'hp.formal_at',
            'hp.is_delay',
            'hp.first_evaluate_start',
            'hp.first_evaluate_end',
            'hp.second_evaluate_start',
            'hp.second_evaluate_end',
        ])->from(['hsi' => HrStaffInfoReadModel::class])
            ->leftJoin(HrProbationModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hp')
            ->leftJoin(HrJobTitleModel::class, 'job.id = hsi.job_title', 'job')
            ->leftJoin(SysDepartmentModel::class, 'd.id = hsi.sys_department_id', 'd')
            ->leftJoin(SysDepartmentModel::class, 'n.id = hsi.node_department_id', 'n')
            ->where('hsi.staff_info_id =:id:', ['id' => intval($staff_info_id)])
            ->getQuery()
            ->getSingleResult();
        if ($result) {
            $data = $result->toArray();
        }
        $data['lang_flag'] = $lang_flag;
        if (isCountry('TH')) {
            $data['hire_date'] = $this->getDateByDays($data['hire_date'], 0, 1);
            $data['first_deadline_date'] =  $data['first_evaluate_end'] ?? '';
            $data['second_deadline_date'] = $data['second_evaluate_end'] ?? '';
        }else{
            $data['first_deadline_date'] = "";
            $data['second_deadline_date'] = "";
            if (!empty($data['hire_date'])) {
                $data['hire_date'] = $this->getDateByDays($data['hire_date'], 0, 1);
                $deadline_date = $this->assembleEvaluationTime($data, $data);
                $data['first_deadline_date'] = $deadline_date['first_deadline_date_end'] ?? '';
                $data['second_deadline_date'] = $deadline_date['second_deadline_date_end'] ?? '';
                if (empty($data['formal_at'])) {
                    $data['formal_at'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays($data['hire_date'], $this->formal_days, 1) : $this->getDateByDays(
                        $data['hire_date'],
                        $this->formal_days_two,
                        1
                    );
                }
            }
        }

        $data['comment'] = "";

        $data['probation_id'] = $data['probation_id'] ?? 0;
        $data['job_title_level'] = !empty($data['job_title_level']) ? $data['job_title_level'] : 1;
        $data['job_title_level_text'] = StaffEnums::JOB_TITLE_LEVEL_DESC[$data['job_title_level']];

        $data['job_title_grade_v2'] = $data['job_title_grade_v2'] ?? 0;
        $data['job_title_grade_v2_text'] = 'F' . $data['job_title_grade_v2'];


        if (!empty($data['probation_id'])) {
            $data['comment'] = $this->getProbationLogs($data['probation_id'])['comment'];
        }
        $data['rules'] = [];
        for ($i = 1; $i <= 6; $i++) {
            $data['rules'][] = (static::$t)->_("hr_probation_rule_" . $i);
        }

        $item = HrProbationAuditModel::findFirst([
            'conditions' => 'probation_id = ?1',
            'order'      => 'id desc',
            'bind'       => [
                1 => $data['probation_id'],
            ],
        ]);
        if ($item) {
            $item = $item->toArray();
            $tpl_id = $item['tpl_id'];
            $score = $item['score'];
        } else {
            $tpl_id = $this->getTplIdByJobTitleGradeV2($data['job_title_grade_v2']);
            $score = null;
        }
        $tpl = $this->getTplItem($tpl_id, $score);
        if (is_string($tpl)) {
            throw new \Exception($tpl);
        }
        $data['score'] = $tpl['score'];
        $data['question_num'] = $tpl['question_num'];
        $data['score_rule_text'] = $tpl['score_rule_text'];

        $data['score']['score_text'] = $this->getScoreGrade($data['score']['score']);
        $data['score']['second_score_text'] = $this->getScoreGrade($data['score']['second_score']);

        $data['field'] = [];

        $fieldArr = [
            "name",
            "staff_info_id",
            "job_title",
            "job_title_level",
            "department",
            "hire_date",
            "first_deadline_date",
            "second_deadline_date",
            "rule",
            "info",
            "weight",
            "first_score",
            "second_score",
            "comment",
            "attendance",
            'attendance_info',
            "first_check",
            "second_check",
            "late",
            "sick",
            "casual",
            "lack",
            "alert",
            "first_alert",
            "second_alert",
            "from",
            "all_info",
            "company",
            "grade",
        ];

        foreach ($fieldArr as $field) {
            $data['field'][$field] = (static::$t)->_("hr_probation_field_" . $field);
        }

        if (isCountry('TH')) {
            $companyInfo = reBuildCountryInstance(new CertificateService())->getCompanyConfigInfo(['staff_info_id' => $staff_info_id]);
            $data['field']['company'] = $companyInfo['company_name'];
        }

        $data['field']['tpl_name'] = (static::$t)->_("hr_probation_field_title_" . $tpl_id);
        $data['attendance'] = $this->getAttendance($staff_info_id);
        $data['alert'] = $this->getAlert($staff_info_id, $data['hire_date'], $data['formal_at']);
        $this->outPdf($staff_info_id, 'staff-cn', $data);
    }


    /**
     * 非一线 下载
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function downloadNonFrontLine($params)
    {
        $user_id       = $params['user_id'] ?? '';
        $staff_info_id = $params['staff_info_id'] ?? '';
        $customize_cur_level = $params['customize_cur_level'] ?? '';
        $source        = $params['source'] ?? 'hcm';
        if (empty($staff_info_id) || empty($user_id)) {
            throw new ValidationException(static::$t->_('param_error'));
        }
        $probation = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = ?1',
            'bind'       => [
                1 => $staff_info_id,
            ],
        ]);
        if (
            empty($probation) ||
            $probation->probation_channel_type != HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE
        ) {
            throw new ValidationException(static::$t->_('data_error'));
        }
        // 获取签字数据
        $probation_data = $probation->toArray();
        $pdf_img_data   = $this->getSignData($probation_data);

        $conflate_pdf_url = [];
        $pdf_data         = [];
        // [一阶段]
        // 获取评估整体数据进行组装
        $first_pdf_data                 = $this->getNonFrontLineAssembleData($staff_info_id, $probation->id,
            HrProbationModel::CUR_LEVEL_FIRST);
        $first_pdf_data['pdf_img_data'] = $pdf_img_data['pdf_img_data'];
        $first_pdf_data                 = array_merge($first_pdf_data, $pdf_img_data['sign_time']);
        // 获取一阶段目标pdf
        $first_target_pdf_url = $this->getProbationTargetPdfUrl($user_id,
            $first_pdf_data['probation_target_detail_id'] ?? '',HrProbationModel::CUR_LEVEL_FIRST,$staff_info_id);
        if (empty($first_target_pdf_url)) {
            throw new ValidationException(static::$t->_('target_data_does_no_exist'));
        }
        $conflate_pdf_url[]               = $first_target_pdf_url;
        $pdf_data['first_target_pdf_url'] = $first_target_pdf_url;
        $this->logger->write_log('downloadNonFrontLine staff_info_id : ' . $staff_info_id . ' ,probation_target_detail_id : ' . ($first_pdf_data['probation_target_detail_id'] ?? '') . ' , first_target_pdf_url : ' . $first_target_pdf_url,
            'info');
        // 获取一阶段评分pdf
        $first_stage_pdf                 = $this->getProbationStagePdfUrl($first_pdf_data,
            HrProbationModel::CUR_LEVEL_FIRST);
        $conflate_pdf_url[]              = $first_stage_pdf['url'];
        $pdf_data['first_form_data']     = $first_stage_pdf['pdf_data'];
        $pdf_data['first_stage_pdf_url'] = $first_stage_pdf['url'];

        // [二阶段]
        if (
            $customize_cur_level == HrProbationModel::CUR_LEVEL_SECOND ||
            $probation_data['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE
        ) {
            $second_pdf_data                 = $this->getNonFrontLineAssembleData($staff_info_id, $probation->id,
                HrProbationModel::CUR_LEVEL_SECOND);
            $second_pdf_data['pdf_img_data'] = $pdf_img_data['pdf_img_data'];
            $second_pdf_data                 = array_merge($second_pdf_data, $pdf_img_data['sign_time']);
            // 获取一阶段目标pdf
            if ($first_pdf_data['probation_target_detail_id'] == $second_pdf_data['probation_target_detail_id']) {
                $second_target_pdf_url = $first_target_pdf_url;
            } else {
                $second_target_pdf_url = $this->getProbationTargetPdfUrl($user_id,
                    $second_pdf_data['probation_target_detail_id'] ?? '',HrProbationModel::CUR_LEVEL_SECOND,$staff_info_id);
                if (empty($second_target_pdf_url)) {
                    throw new ValidationException(static::$t->_('target_data_does_no_exist'));
                }
                $this->logger->write_log('downloadNonFrontLine staff_info_id : ' . $staff_info_id . ' ,probation_target_detail_id : ' . ($second_pdf_data['probation_target_detail_id'] ?? '') . ' , second_target_pdf_url : ' . $second_target_pdf_url,
                    'info');
            }
            $pdf_data['second_target_pdf_url'] = $second_target_pdf_url;
            $conflate_pdf_url[]                = $second_target_pdf_url;
            // 获取二阶段评分pdf
            $second_stage_pdf                 = $this->getProbationStagePdfUrl($second_pdf_data,
                HrProbationModel::CUR_LEVEL_SECOND);
            $conflate_pdf_url[]               = $second_stage_pdf['url'];
            $pdf_data['second_form_data']     = $second_stage_pdf['pdf_data'];
            $pdf_data['second_stage_pdf_url'] = $second_stage_pdf['url'];
        }
        //合并URL
        $conflatePdfData = (new FormPdfServer())->getInstance()->mergePdf($conflate_pdf_url);
        $conflatePdfUrl  = $conflatePdfData['object_url'] ?? '';
        if (!$conflatePdfUrl) {
            throw new \Exception('Pdf Generate Error ');
        }
        if ($source != 'hcm') {
            return [
                'pdf_url'   => $conflatePdfUrl,
                'form_data' => $pdf_data,
            ];
        }
        return $conflatePdfUrl;
    }

    /**
     * 获取阶段分数pdf
     * @param $pdf_data
     * @param $cur_level
     * @return array
     * @throws ValidationException
     */
    public function getProbationStagePdfUrl($pdf_data, $cur_level): array
    {
        if ($cur_level == 1) {
            $_pdf_name       = 'first';
            $footer_sign_url = $pdf_data['pdf_img_data']['first_done_staff_sign_url'] ?? '';
        } else {
            $_pdf_name       = 'second';
            $footer_sign_url = $pdf_data['pdf_img_data']['second_done_staff_sign_url'] ?? '';
        }
        if (static::$language == 'zh-CN') {
            $tpl_name = 'probation_non_front_line_' . $_pdf_name . '_zh';
        } elseif (static::$language == 'th') {
            $tpl_name = 'probation_non_front_line_' . $_pdf_name . '_th';
        } else {
            $tpl_name = 'probation_non_front_line_' . $_pdf_name . '_en';
        }
//        print_r($pdf_data);die;
        $tmpPath                  = BASE_PATH . '/app/views/probation/' . $tpl_name . '.ftl';
        $pdf_data['pdf_temp_url'] = $this->getPdfTemp($tmpPath, 1);
        $file_name                = $pdf_data['staff_info_id'] . time();


        $certificateService = reBuildCountryInstance(new CertificateService());

        //获取员工
        if (isCountry('MY')) {
            $pdfCompanyData = $certificateService->getStaffCompanyInfoByTarget(
                $pdf_data['staff_info_id'],
                true,
                [
                    'is_footer_sign'  => true,
                    'footer_sign_url' => $footer_sign_url,
                    'lang' => static::$language,
                ]
            );
        } else {
            $pdfCompanyData = $certificateService->getStaffCompanyInfo(
                $pdf_data['staff_info_id'],
                true,
                [
                    'is_footer_sign'  => true,
                    'footer_sign_url' => $footer_sign_url,
                    'lang' => static::$language,
                ]
            );
        }
        $pdf_data['pdf_options']  = [
            'format'              => 'a4',
            'displayHeaderFooter' => true,
            'headerTemplate'      => $pdfCompanyData['header_template'],
            'footerTemplate'      => $pdfCompanyData['footer_template'],
        ];
        $this->logger->write_log('getProbationStagePdfUrl cur_level : ' . $cur_level . ' , staff_info_id : ' . $pdf_data['staff_data']['staff_info_id'] . ' ,pdf_data : ' . json_encode($pdf_data,
                JSON_UNESCAPED_UNICODE), 'info');
        $pdfRes = (new FormPdfServer())->getInstance()->generatePdf($pdf_data['pdf_temp_url'], $pdf_data,
            $pdf_data['pdf_img_data'], $pdf_data['pdf_options'], $file_name);
        if (!isset($pdfRes['object_url'])) {
            throw new ValidationException('生成pdf失败');
        }
        return ['url' => $pdfRes['object_url'], 'pdf_data' => $pdf_data];
    }

    /**
     * 获取签字数据
     * @param $data
     * @return array[]
     */
    public function getSignData($data): array
    {
        $sign_time = [];
        $msg_ids   = [];
        foreach (
            [
                'first_stage_done_msg_id',
                'second_stage_done_msg_id',
                'first_stage_done_manager_msg_id',
                'second_stage_done_manager_msg_id',
            ] as $key
        ) {
            if (!empty($data[$key])) {
                $msg_ids[] = $data[$key];
            }
        }
        $messagePdfSignImgUrl                       = $this->getMessagePdfSignImgUrl($msg_ids);
        $sign_time['first_done_staff_sign_time']    = empty($messagePdfSignImgUrl[$data['first_stage_done_msg_id']]['sign_time']) ? '' : show_time_zone($messagePdfSignImgUrl[$data['first_stage_done_msg_id']]['sign_time']);
        $sign_time['first_done_manager_sign_time']  = empty($messagePdfSignImgUrl[$data['first_stage_done_manager_msg_id']]['sign_time']) ? '' : show_time_zone($messagePdfSignImgUrl[$data['first_stage_done_manager_msg_id']]['sign_time']);
        $sign_time['second_done_staff_sign_time']   = empty($messagePdfSignImgUrl[$data['second_stage_done_msg_id']]['sign_time']) ? '' : show_time_zone($messagePdfSignImgUrl[$data['second_stage_done_msg_id']]['sign_time']);
        $sign_time['second_done_manager_sign_time'] = empty($messagePdfSignImgUrl[$data['second_stage_done_manager_msg_id']]['sign_time']) ? '' : show_time_zone($messagePdfSignImgUrl[$data['second_stage_done_manager_msg_id']]['sign_time']);
        $sign_time['first_done_staff_sign_url']     = empty($messagePdfSignImgUrl[$data['first_stage_done_msg_id']]['sign_img_url']) ? '' : $messagePdfSignImgUrl[$data['first_stage_done_msg_id']]['sign_img_url'];
        $sign_time['first_done_manager_sign_url']   = empty($messagePdfSignImgUrl[$data['first_stage_done_manager_msg_id']]['sign_img_url']) ? '' : $messagePdfSignImgUrl[$data['first_stage_done_manager_msg_id']]['sign_img_url'];
        $sign_time['second_done_staff_sign_url']    = empty($messagePdfSignImgUrl[$data['second_stage_done_msg_id']]['sign_img_url']) ? '' : $messagePdfSignImgUrl[$data['second_stage_done_msg_id']]['sign_img_url'];
        $sign_time['second_done_manager_sign_url']  = empty($messagePdfSignImgUrl[$data['second_stage_done_manager_msg_id']]['sign_img_url']) ? '' : $messagePdfSignImgUrl[$data['second_stage_done_manager_msg_id']]['sign_img_url'];

        $pdfImgData = [
            ['name' => 'first_done_staff_sign_url', 'url' => $sign_time['first_done_staff_sign_url']],
            ['name' => 'first_done_manager_sign_url', 'url' => $sign_time['first_done_manager_sign_url']],
            ['name' => 'second_done_staff_sign_url', 'url' => $sign_time['second_done_staff_sign_url']],
            ['name' => 'second_done_manager_sign_url', 'url' => $sign_time['second_done_manager_sign_url']],
        ];
        return ['pdf_img_data' => $pdfImgData, 'sign_time' => $sign_time];
    }

    /**
     * 获取非一线重组pdf数据
     * @param $staff_info_id
     * @param $probation_id
     * @param $cur_level
     * @return array
     */
    public function getNonFrontLineAssembleData($staff_info_id, $probation_id, $cur_level): array
    {
        $return_data     = [];
        $probation_audit = HrProbationAuditModel::find([
            'conditions' => 'probation_id = :probation_id: and audit_status = :audit_status:',
            'bind'       => [
                'probation_id' => $probation_id,
                'audit_status' => HrProbationAuditModel::AUDIT_STATUS_HANDLED,
            ],
            'order'      => 'id desc',
        ])->toArray();
        if (empty($probation_audit)) {
            return $return_data;
        }
        foreach ($probation_audit as $key => $value) {
            $return_data[$value['cur_level']][$value['id']] = $value;
        }
        $return_data               = $this->assembleScore($return_data, $cur_level);
        $return_data['staff_data'] = $this->getStaffData($staff_info_id);
        return $return_data;
    }

    /**
     * 获取员工数据
     * @param $staff_info_id
     * @return array
     */
    public function getStaffData($staff_info_id): array
    {
        $info = HrStaffInfoModel::findFirst([
            'columns'    => 'staff_info_id,name,manger,job_title_level,job_title_grade_v2,hire_date,node_department_id,nationality,hire_type,job_title',
            'conditions' => 'staff_info_id = :staff_id: ',
            'bind'       => ['staff_id' => $staff_info_id],
        ]);
        if (empty($info)) {
            return [];
        }
        $info                         = $info->toArray();
        $info['node_department_name'] = $this->showDepartmentName($info['node_department_id']);
        $info['job_name']             = $this->showJobTitleName($info['job_title']);

        return $info;
    }

    /**
     * 重组分数结构
     * @param $data
     * @param $cur_level
     * @return array
     */
    public function assembleScore($data, $cur_level): array
    {
        $return_data = [];
        $max_key     = max(array_keys($data[$cur_level]));
        $new_data    = $data[$cur_level][$max_key];
        $score       = json_decode($new_data['score'], true);
        foreach ($score[$cur_level]['target_score'] as $k => $v) {
            if (!empty($v['third_score'])) {
                $final_score      = $v['third_score'];
                $final_score_text = $v['third_score_text'];
            } elseif (!empty($v['second_score'])) {
                $final_score      = $v['second_score'];
                $final_score_text = $v['second_score_text'];
            } else {
                $final_score      = !empty($v['first_score']) ? $v['first_score'] : '';
                $final_score_text = !empty($v['first_score_text']) ? $v['first_score_text'] : '';
            }
            $score[$cur_level]['target_score'][$k]['final_score'] = $final_score;
            $score[$cur_level]['target_score'][$k]['final_text']  = $final_score_text;
        }

        $return_data['stage_score_list']                      = $score[$cur_level];
        $return_data['duty_info']                             = $score['duty_info'] ?? '';
        $return_data['act_version']                           = empty($score['act_version']) ? '0' : $score['act_version'];
        $return_data['probation_target_detail_id']            = $score['probation_target_detail_id'] ?? '';
        $return_data['stage_act_values_list']                 = $this->assembleActValuesData($score['act_values_data'] ?? [],
            $cur_level);
        $return_data['stage_score_list']['final_result_text'] = $return_data['stage_score_list']['count_score'] >= 3 ? self::$t->_('pdf_probation_status_1') : self::$t->_('pdf_probation_status_0');
        return $return_data;
    }

    /**
     *  重组价值观数据
     * @param $act_values_data
     * @param $cur_level
     * @return array|void
     */
    public function assembleActValuesData($act_values_data, $cur_level)
    {
        if (empty($act_values_data)) {
            return [];
        }
        $stage_act_values_list = $act_values_data[$cur_level] ?? [];
        foreach ($stage_act_values_list as $k => $v) {
            foreach ($v['behavior_list'] as $k1 => $v1) {
                $stage_act_values_list[$k]['concept_name']                        = self::$t->_(HrProbationActValuesModel::$concept_type_list[$v1['concept_type']]);
                $stage_act_values_list[$k]['behavior_list'][$k1]['behavior_name'] = self::$t->_('probation_behavior_type_' . $v1['behavior_type']);
            }
            if (!empty($v['third_score'])) {
                $final_score      = $v['third_score'];
                $final_score_text = $v['third_score_text'];
            } elseif (!empty($v['second_score'])) {
                $final_score      = $v['second_score'];
                $final_score_text = $v['second_score_text'];
            } else {
                $final_score      = !empty($v['first_score']) ? $v['first_score'] : '';
                $final_score_text = !empty($v['first_score_text']) ? $v['first_score_text'] : '';
            }
            $stage_act_values_list[$k]['final_score'] = $final_score;
            $stage_act_values_list[$k]['final_text']  = $final_score_text;
        }
        return $stage_act_values_list;
    }
    
    /**
     * 获取目标pdf
     * @param $user_id
     * @param $target_info_id
     * @return array|false|mixed|string
     * @throws ValidationException
     */
    public function getProbationTargetPdfUrl($user_id, $target_info_id,$cur_level,$staff_info_id)
    {
        if (empty($user_id)) {
            return false;
        }
        if (empty($target_info_id)){
            // 兼容老数据
            $targetDetail     = HrProbationTargetDetailModel::find([
                'conditions' => "setting_state = :setting_state: and send_state = :send_state: and sign_state = :sign_state: and is_deleted = :is_deleted: and staff_info_id = :staff_info_id:",
                'bind'       => [
                    'setting_state' => HrProbationTargetDetailModel::SETTING_STATE_FINISH,
                    'send_state'    => HrProbationTargetDetailModel::SEND_STATE_FINISH,
                    'sign_state'    => HrProbationTargetDetailModel::SIGN_STATE_FINISH,
                    'is_deleted'    => 0,
                    'staff_info_id'    => $staff_info_id,
                ],
            ])->toArray();
            if (empty($targetDetail)){
                return false;
            }
            $targetDetail = array_column($targetDetail, 'id','stage');
            if ($cur_level == HrProbationModel::CUR_LEVEL_SECOND){
                $target_info_id = empty($targetDetail[$cur_level]) ? ($targetDetail[HrProbationModel::CUR_LEVEL_FIRST] ?? '') : $targetDetail[$cur_level];
            }else{
                $target_info_id = $targetDetail[$cur_level] ?? '';
            }
            if (empty($target_info_id)){
                return false;
            }
        }
        $targetDetailInfo = (new HrProbationTargetRepository())->getTargetDetailInfo([
            'id' => $target_info_id,
        ]);
        if (empty($targetDetailInfo)) {
            return false;
        }
        $pdfUrl = (new ProbationTargetService())->getPdfInfoByDetailId($targetDetailInfo, $user_id);
        return $pdfUrl;
    }

    /**
     * 获取二阶段pdf
     * @param $staff_info_id
     * @param $target_info_id
     * @return void
     * @throws ValidationException
     */
    public function getProbationSecondPdfUrl($staff_info_id,$target_info_id)
    {
        $targetDetailInfo = (new HrProbationTargetRepository())->getTargetDetailInfo([
            'target_id'     => $target_info_id,
        ]);

        if (empty($targetDetailInfo)) {
            throw new ValidationException(self::$t->_('target_data_state_no_finish_not_operate'));
        }
        $pdfUrl = (new ProbationTargetService())->getPdfInfoByDetailId($targetDetailInfo, $staff_info_id);
    }

    /**
     * 获取非一线pdf签字数据 by在用
     * @param $local
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getNonFrontLinePdfSignData($local, $params): array
    {
        if (!empty($local['locale'])) {
            self::setLanguage($local['locale']);
        }
        $staff_info_id = $params['staff_info_id'] ?? '';
        $user_id       = $params['user_id'] ?? '';
        $cur_level     = $params['cur_level'] ?? 1;
        if (empty($staff_info_id) || empty($user_id)) {
            throw new ValidationException(static::$t->_('param_error'));
        }
        $probation = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = ?1',
            'bind'       => [
                1 => $staff_info_id,
            ],
        ]);
        if (
            empty($probation) ||
            $probation->probation_channel_type != HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE
        ) {
            throw new ValidationException(static::$t->_('data_error'));
        }
        // 获取签字数据
        $probation_data = $probation->toArray();
        $pdf_img_data   = $this->getSignData($probation_data);
        $pdf_data       = [];
        // [一阶段]
        // 获取评估整体数据进行组装
        $first_pdf_data                 = $this->getNonFrontLineAssembleData($staff_info_id, $probation->id,
            HrProbationModel::CUR_LEVEL_FIRST);
        $pdf_data['pdf_img_data'] = $pdf_img_data['pdf_img_data'];
        $first_pdf_data                 = array_merge($first_pdf_data, $pdf_img_data['sign_time']);
        // 获取一阶段评分pdf
        $first_stage_pdf                 = $this->getProbationStagePdfUrl($first_pdf_data,
            HrProbationModel::CUR_LEVEL_FIRST);
        $pdf_data['first_form_data']     = $first_stage_pdf['pdf_data'];
        $pdf_data['first_stage_pdf_url'] = $first_stage_pdf['url'];
        // [二阶段]
        $pdf_data['is_show_second_data'] = '0';
        if ($cur_level == HrProbationModel::CUR_LEVEL_SECOND && $probation_data['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT) {
            $second_pdf_data                 = $this->getNonFrontLineAssembleData($staff_info_id, $probation->id,
                HrProbationModel::CUR_LEVEL_SECOND);
            $second_pdf_data                 = array_merge($second_pdf_data, $pdf_img_data['sign_time']);
            // 获取二阶段评分pdf
            $second_stage_pdf                 = $this->getProbationStagePdfUrl($second_pdf_data,
                HrProbationModel::CUR_LEVEL_SECOND);
            $pdf_data['second_form_data']     = $second_stage_pdf['pdf_data'];
            $pdf_data['second_stage_pdf_url'] = $second_stage_pdf['url'];
            $pdf_data['is_show_second_data']  = '1';
        }
        return [
            'code' => ErrCode::SUCCESS,
            'msg'  => 'ok',
            'data' => $pdf_data,
        ];
    }
    
    public function getMessagePdfSignImgUrl($msg_ids)
    {
        if (empty($msg_ids)){
            return [];
        }
        $messagePdf = MessagePdf::find(
            [
                'conditions' => 'msg_id in ({msg_ids:array})',
                'bind'       => [
                    'msg_ids' => $msg_ids,
                ],
            ]
        )->toArray();
        return array_column($messagePdf,null,'msg_id');
    }
    
    /**
     * @param $local
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function probationStageDoneMessage($local, $params): array
    {
        self::setLanguage($local['locale']);
        $staff_info_id = $params['staff_info_id'];
        $customize_cur_level = $params['customize_cur_level'];
        $conn = $this->getDI()->get('db_backyard');
        $conn->begin();
        try {
            $probation = HrProbationModel::findFirst([
                'conditions' => 'staff_info_id =:staff_id:',
                'bind'       => ['staff_id' => $staff_info_id],
                'for_update' => true,
            ]);
            if (empty($probation)){
                throw new ValidationException('员工信息不存在 staff_info_id:'.$staff_info_id);
            }
            if ($customize_cur_level == 1){
                if (!empty($probation->first_stage_done_msg_id) && !empty($probation->first_stage_done_manager_msg_id)){
                    throw new ValidationException('消息已发送过 first_stage_done_msg_id:'.$probation->first_stage_done_msg_id.' first_stage_done_manager_msg_id:'.$probation->first_stage_done_manager_msg_id);
                }
            }else{
                if (!empty($probation->second_stage_done_msg_id) && !empty($probation->second_stage_done_manager_msg_id)){
                    throw new ValidationException('消息已发送过 second_stage_done_msg_id:'.$probation->second_stage_done_msg_id.' second_stage_done_manager_msg_id:'.$probation->second_stage_done_manager_msg_id);
                }
            }
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id =:staff_id:',
                'bind'       => ['staff_id' => $staff_info_id],
            ]);
            $lang = (new StaffService())->getAcceptLanguage($staff_info_id);
            self::setLanguage($lang);
            $pdf_data = $this->downloadNonFrontLine([
                'user_id'=>'10000',
                'staff_info_id'=>$staff_info_id,
                'customize_cur_level'=>$customize_cur_level,
                'source'=>'backyard',
            ]);
            $manger_lang = (new StaffService())->getAcceptLanguage($staff_info_id);
            self::setLanguage($manger_lang);
            $manger_pdf_data = $this->downloadNonFrontLine([
                'user_id'=>'10000',
                'staff_info_id'=>$staff_info_id,
                'customize_cur_level'=>$customize_cur_level,
                'source'=>'backyard',
            ]);
            $msg_id = $this->sendProbationStageDoneMessage($staff_info_id, $customize_cur_level, $staff_info_id,$lang);
            $manger_msg_id = $this->sendProbationStageDoneMessage($staff_info->manger, $customize_cur_level, $staff_info_id,$manger_lang);
            if (empty($msg_id) || empty($manger_msg_id)){
                throw new ValidationException('消息发送失败');
            }
            if ($customize_cur_level == 1){
                $probation->first_stage_done_msg_id = $msg_id;
                $probation->first_stage_done_manager_msg_id = $manger_msg_id;
            }else{
                $probation->second_stage_done_msg_id = $msg_id;
                $probation->second_stage_done_manager_msg_id = $manger_msg_id;
            }
            $this->updateMessagePdf($probation->id,$msg_id,$staff_info_id,$pdf_data['form_data'] ?? '',$pdf_data['pdf_url'] ?? '');
            $this->updateMessagePdf($probation->id,$manger_msg_id,$staff_info->manger,$manger_pdf_data['form_data'] ?? '',$manger_pdf_data['pdf_url'] ?? '');
            $probation->save();
            $conn->commit();
        } catch (\Exception|ValidationException $e) {
            $conn->rollback();
            $this->logger->error([
                'function' => __FUNCTION__,
                'staff_info_id' => $staff_info_id,
                'message' => $e->getMessage(),
            ]);
            throw new ValidationException($e);
        }
        return [
            'code' => ErrCode::SUCCESS,
            'msg'  => 'ok',
            'data' => [],
        ];
    }
    
    public function getNonFrontLinePdfData($staff_info_id,$user_id,$source = 'hcm',$customize_cur_level = 0)
    {
        $score_text               = $this->probation_score_enumerate();
        $score_text               = array_column($score_text, 'label', 'value');
        
        $builder = new  Builder();
        $result = $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.manger',
            'hsi.job_title_level',
            'hsi.job_title_grade_v2',
            'hsi.hire_date',
            'hsi.node_department_id',
            'hsi.nationality',
            'hsi.hire_type',
            'job.job_name',
            'n.name as node_department_name',
            'hp.id as probation_id',
            'hp.formal_at',
            'hp.is_delay',
            'hp.cur_level',
            'hp.first_evaluate_start',
            'hp.first_evaluate_end',
            'hp.second_evaluate_start',
            'hp.second_evaluate_end',
            'hp.first_audit_status',
            'hp.second_audit_status',
            'hp.status as probation_status',
            'hp.first_status',
            'hp.second_status',
            'hp.first_stage_done_msg_id',
            'hp.second_stage_done_msg_id',
            'hp.first_stage_done_manager_msg_id',
            'hp.second_stage_done_manager_msg_id',
        ])->from(['hsi' => HrStaffInfoReadModel::class])
            ->leftJoin(HrProbationModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hp')
            ->leftJoin(HrJobTitleModel::class, 'job.id = hsi.job_title', 'job')
            ->leftJoin(SysDepartmentModel::class, 'n.id = hsi.node_department_id', 'n')
            ->where('hsi.staff_info_id =:id: and hp.probation_channel_type = :probation_channel_type:', ['id' => intval($staff_info_id),'probation_channel_type' => HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE])
            ->getQuery()
            ->getSingleResult();
        if (empty($result)) {
            throw new ValidationException(static::$t->_('data_error'));
        }
        $data = $result->toArray();
        $data['hire_date'] = !empty($data['hire_date']) ? date('Y-m-d',strtotime($data['hire_date'])) : '';
        // 根据工号获取有没有查看被评估人职级的权限
        if ($source == 'hcm'){
            $hr_rpc = (new ApiClient('hris', '', 'has_grade_permission_tab', 'zh-CN'));
            $hr_rpc->setParamss([
                "staff_info_id"=>$staff_info_id,
                "fbid"=>$user_id,
            ]);
            $res = $hr_rpc->execute();
            if (!isset($res['code']) || !isset($res['body']) || $res['code'] != 0) {
                $data['job_title_grade_v2_text'] = '';
            }else{
                $data['job_title_grade_v2_text'] = $res['body'] == 1 ? 'F'.$data['job_title_grade_v2'] : '';
            }
        }else{
            // by过来的 可以看
            $data['job_title_grade_v2_text'] = 'F'.$data['job_title_grade_v2'];
        }
        $probation_audit = HrProbationAuditModel::find([
            'conditions' => 'probation_id = :probation_id:',
            'order'      => 'id desc',
            'bind'       => [
                'probation_id' => $data['probation_id'],
            ],
        ])->toArray();
        if (empty($probation_audit)){
            $targetDetail     = HrProbationTargetDetailModel::findFirst([
                'conditions' => "setting_state = :setting_state: and send_state = :send_state: and sign_state = :sign_state: and is_deleted = :is_deleted: and staff_info_id = :staff_info_id:",
                'bind'       => [
                    'setting_state' => HrProbationTargetDetailModel::SETTING_STATE_FINISH,
                    'send_state'    => HrProbationTargetDetailModel::SEND_STATE_FINISH,
                    'sign_state'    => HrProbationTargetDetailModel::SIGN_STATE_FINISH,
                    'is_deleted'    => 0,
                    'staff_info_id'    => $staff_info_id,
                ],
                'order'      => 'stage asc',
            ]);
            if (empty($targetDetail)){
                throw new ValidationException(static::$t->_('probation_target_error'));
            }
            $targetDetail = $targetDetail->toArray();
            $data['score'] = $this->formatTargetInfoJson($targetDetail);
            $data['new_cur_level'] = 0;
        }else{
            // 重新组装score
            $_score_1 = [];
            $_score_2 = [];
            $_duty_info = '';
            $_score = [];
            $act_version = '0';
            $probation_target_detail_id = '';
            $act_values_data = [];
            foreach ($probation_audit as $key => $item){
                $_score = json_decode($item['score'],true);
                if ($item['cur_level'] == 1){
                    $_duty_info = $_score['duty_info'];
                    $act_version = empty($_score['act_version']) ? '0' : $_score['act_version'];
                    $probation_target_detail_id = $_score['probation_target_detail_id'] ?? "";
                    $act_values_data = $_score['act_values_data'] ?? [];
                    if ($item['audit_level'] == 3 && empty($_score_1)){
                        $_score_1 = $_score[1];
                    }elseif ($item['audit_level'] == 2 && empty($_score_1)){
                        $_score_1 = $_score[1];
                    }elseif ($item['audit_level'] == 1 && empty($_score_1)){
                        $_score_1 = $_score[1];
                    }
                }
                if ($item['cur_level'] == 2){
                    $_duty_info = $_score['duty_info'];
                    $act_version = empty($_score['act_version']) ? '0' : $_score['act_version'];
                    $probation_target_detail_id = $_score['probation_target_detail_id'] ?? "";
                    $act_values_data = $_score['act_values_data'] ?? [];
                    if ($item['audit_level'] == 3 && empty($_score_2)){
                        $_score_2 = $_score[2];
                    }elseif ($item['audit_level'] == 2 && empty($_score_2)){
                        $_score_2 = $_score[2];
                    }elseif ($item['audit_level'] == 1 && empty($_score_2)){
                        $_score_2 = $_score[2];
                    }
                }
            }
            $data['score'] = [
                '1'=>$_score_1,
                '2'=>!empty($_score_2) ? $_score_2 : $_score[2],
                'duty_info'=>$_duty_info,
                'act_version'=>$act_version,
                'probation_target_detail_id'=>$probation_target_detail_id,
                'act_values_data'=>$act_values_data,
            ];
            if ($source != 'hcm' && !empty($customize_cur_level) && in_array($customize_cur_level,[1,2])){
                $data['new_cur_level'] = $customize_cur_level;
            }else{
                // 转正状态非试用期中或二阶段评估状态为已完成 下载pdf 都属于2阶段
                if ($data['probation_status'] != HrProbationModel::STATUS_PROBATION || $data['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE){
                    $data['new_cur_level'] = 2;
                }elseif ($data['first_audit_status'] == 3){
                    $data['new_cur_level'] = 1;
                }else{
                    $data['new_cur_level'] = 0;
                }
            }
        }
        $data['first_deadline_date']        = $data['first_evaluate_start'] . '——' . $data['first_evaluate_end'];
        $data['second_deadline_date']       = $data['second_evaluate_start'] . '——' . $data['second_evaluate_end'];
        $data['duty_info'] = $this->score_str_replace($data['score']['duty_info'] ?? '');
        $data['count_score'] = [];
        $data['count_score_2_score_text'] = '';
        $data['count_score_1_score_text'] = '';
        if ($data['new_cur_level'] > 0){
            $scoreData = [
                1 => ['third_score' => 0, 'second_score' => 0, 'first_score' => 0],
                2 => ['third_score' => 0, 'second_score' => 0, 'first_score' => 0],
            ];
            foreach ($data['score'] as $k => $v) {
                if (!isset($v['target_score'])){
                    continue;
                }
                foreach ($v['target_score'] as $vv) {
                    if ($k == 1) {
                        if (!empty($vv['third_score'])) {
                            $scoreData[1]['third_score'] += $vv['third_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['second_score'])) {
                            $scoreData[1]['second_score'] += $vv['second_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['first_score'])) {
                            $scoreData[1]['first_score'] += $vv['first_score'] * $vv['weight'] / 100;
                        }
                    }
                    if ($k == 2) {
                        if (!empty($vv['third_score'])) {
                            $scoreData[2]['third_score'] += $vv['third_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['second_score'])) {
                            $scoreData[2]['second_score'] += $vv['second_score'] * $vv['weight'] / 100;
                        } elseif (!empty($vv['first_score'])) {
                            $scoreData[2]['first_score'] += $vv['first_score'] * $vv['weight'] / 100;
                        }
                    }
                }
            }
            $data['count_score'][1]['score'] = !empty($scoreData[1]['third_score']) ? intval(bcsub($scoreData[1]['third_score'], "0", 0)) :(!empty($scoreData[1]['second_score']) ? intval(bcsub($scoreData[1]['second_score'], "0", 0)) : (!empty($scoreData[1]['first_score']) ? intval(bcsub($scoreData[1]['first_score'], "0", 0)) : 0));
            $data['count_score_1_score_text'] = empty($data['count_score'][1]['score']) ? '' : $score_text[$data['count_score'][1]['score']];
            $data['count_score'][2]['score'] = !empty($scoreData[2]['third_score']) ? intval(bcsub($scoreData[2]['third_score'], "0", 0)) :(!empty($scoreData[2]['second_score']) ? intval(bcsub($scoreData[2]['second_score'], "0", 0)) : (!empty($scoreData[2]['first_score']) ? intval(bcsub($scoreData[2]['first_score'], "0", 0)) : 0));
            $data['count_score_2_score_text'] = empty($data['count_score'][2]['score']) ? '' : $score_text[$data['count_score'][2]['score']];
        }
        $data['score_1_target_score'] = '';
        $data['score_2_target_score'] = '';
        foreach ($data['score'][1]['target_score'] as $k=>$v){
            $data['score_1_target_score'] .= '<tr><td>'.($k+1).'</td><td>'.$this->score_str_replace($v['name']).'</td><td>'.$this->score_str_replace($v['info']).'</td><td>'.$v['weight'].'%</td><td>'.(!empty($v['third_score_text']) ? $v['third_score_text'] : (!empty($v['second_score_text']) ? $v['second_score_text'] : (!empty($v['first_score_text']) ? $v['first_score_text'] : ''))).'</td></tr>';
        }
        foreach ($data['score'][2]['target_score'] as $k=>$v){
            $data['score_2_target_score'] .= '<tr><td>'.($k+1).'</td><td>'.$this->score_str_replace($v['name']).'</td><td>'.$this->score_str_replace($v['info']).'</td><td>'.$v['weight'].'%</td><td>'.(!empty($v['third_score_text']) ? $v['third_score_text'] : (!empty($v['second_score_text']) ? $v['second_score_text'] : (!empty($v['first_score_text']) ? $v['first_score_text'] : ''))).'</td></tr>';
        }
        $data['new_cur_level_1'] = $data['new_cur_level'] == 0 ? 'style="display: none"' : '';
        $data['new_cur_level_2'] = $data['new_cur_level'] <= 1 ? 'style="display: none"' : '';
        $data['score_1_good_job'] = $this->score_str_replace($data['score'][1]['good_job'] ?? '');
        $data['score_1_no_good_job'] = $this->score_str_replace($data['score'][1]['no_good_job'] ?? '');
        $data['score_1_action_plan'] = $this->score_str_replace($data['score'][1]['action_plan'] ?? '');
        $data['score_2_good_job'] = $this->score_str_replace($data['score'][2]['good_job'] ?? '');
        $data['score_2_no_good_job'] = $this->score_str_replace($data['score'][2]['no_good_job'] ?? '');
        $data['score_2_action_plan'] = $this->score_str_replace($data['score'][2]['action_plan'] ?? '');
        $target_sing_url = $this->getTargetSingUrl($staff_info_id);
        $data['staff_sign_url'] = $target_sing_url['staff_sign_url'] ?? '';
        $data['staff_sign_time'] = !empty($target_sing_url['staff_sign_time']) ? show_time_zone($target_sing_url['staff_sign_time']) : '';
        $data['manager_sign_url'] = $target_sing_url['manager_sign_url'] ?? '';
        $data['manager_name'] = $target_sing_url['manager_name'] ?? '';
        $data['manager_sign_time'] = !empty($target_sing_url['manager_sign_time']) ? show_time_zone($target_sing_url['manager_sign_time']) : '';
        $data['first_act_values_data'] = !empty($data['score']['act_values_data']) ? ($this->refactoringStageScoreData($data['score'])['act_values_data'][1] ?? []) : [];
        $data['second_act_values_data'] = !empty($data['score']['act_values_data']) ? ($this->refactoringStageScoreData($data['score'])['act_values_data'][2] ?? []) : [];
        unset($data['score']);
        $data['first_status_text'] = empty($data['count_score'][1]['score']) ? '' : ($data['count_score'][1]['score'] >= 3 ? static::$t->_('pdf_probation_status_1'): static::$t->_('pdf_probation_status_0'));
        $data['second_status_text'] = empty($data['count_score'][2]['score']) ? '' : ($data['count_score'][2]['score'] >= 3 ? static::$t->_('pdf_probation_status_1'): static::$t->_('pdf_probation_status_0'));
        return $data;
    }

    /**
     * @param $msg_id
     * @param $staff_info_id
     * @param $data
     * @param $sign_img_url
     * @param $pdf_url
     * @return mixed
     */
    public function updateMessagePdf($probation_id,$msg_id,$staff_info_id,$data,$pdf_url)
    {
        $messagePdf = MessagePdf::findFirst(
            [
                'conditions' => 'msg_id = :message_id:',
                'bind'       => [
                    'message_id' => $msg_id,
                ],
            ]
        );
        if (empty($messagePdf)){
            $messagePdf = new MessagePdf();
        }
        $messagePdf->pdf_url = $pdf_url;
        $messagePdf->staff_info_id = $staff_info_id;
        $messagePdf->module_category = MessagePdf::MODULE_CATEGORY_PROBATION_STAGE_DONE_MESSAGE;
        $messagePdf->msg_id = $msg_id;
        $messagePdf->business_id = $probation_id;
        $messagePdf->form_data_json = json_encode($data, JSON_UNESCAPED_UNICODE);
        return $messagePdf->save();
    }
    
    public function sendProbationStageDoneMessage($staff_info_id,$customize_cur_level,$staff_id,$lang)
    {
            $t       = $this->getTranslation($lang);
            $title = $t->_('probation_stage_done_message_title',['staff_info_id'=>$staff_id,'cur_level'=>$customize_cur_level]);
            $kit_param['staff_info_ids_str'] = $staff_info_id;
            $kit_param['staff_users']        = [['id' => $staff_info_id]];
            $kit_param['message_title']      = $title;
            $kit_param['message_content']    = '';
            $kit_param['category']           = Enums\EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_STAGE_DONE');
            $res      = (new MessagesService())->add_kit_message($kit_param);
            if ($res[0] != 'ok') {
                $this->getDI()->get('logger')->write_log([
                    'function' => 'probation_stage_done_message',
                    'message'  => '消息发送失败',
                    'params'   => $kit_param,
                    'result'   => $res,
                ]);
                return false;
            }
            return $res[2][0];
    }
    
    public function score_str_replace($str)
    {
        return nl2br($str);
    }
    
    /**
     * @param $staff_info_id
     * @return array
     */
    public function getTargetSingUrl($staff_info_id): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['t.staff_info_id, t.staff_sign_url,t.staff_sign_time,t.manager_id,t.manager_sign_url,t.manager_sign_time']);
        $builder->from(['t' => HrProbationTargetBusinessModel::class]);
        $builder->Where(
            't.staff_info_id = :staff_info_id: and t.staff_state = :staff_state: and t.stage = 1 and t.state = :state:',
            [
                'staff_info_id'    => $staff_info_id,
                'staff_state' => HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS,
                'state' => HrProbationTargetBusinessModel::STATE_NORMAL,
            ]);
        $targetBusiness = $builder->getQuery()->execute()->getFirst();
        if (empty($targetBusiness)){
            return [];
        }
        return $targetBusiness->toArray();
    }

    /**
     * 转正评估非一线分数枚举
     * @return array[]
     */
    public function probation_score_enumerate(): array
    {
        return [
            ['value' => 1, 'label' => 'C'],
            ['value' => 2, 'label' => 'B-'],
            ['value' => 3, 'label' => 'B'],
            ['value' => 4, 'label' => 'B+'],
            ['value' => 5, 'label' => 'A'],
        ];
    }

    /**
     * @param $staff_info_id
     * @param $tpl_name
     * @param $data
     * @param $type
     * @return mixed|void
     * @throws BusinessException
     * @throws \Mpdf\MpdfException
     */
    public function outPdf($staff_info_id,$tpl_name, $data,$type=1)
    {
        $view = new View();
        $path = APP_PATH . '/views';
        $view->setViewsDir($path);
        $view->setVars($data);
        $view->start();
        $view->disableLevel(
            [
                View::LEVEL_LAYOUT => false,
                View::LEVEL_MAIN_LAYOUT => false,
            ]
        );
        $view->render('probation', $tpl_name);

        $view->finish();
        $content = $view->getContent();

        $mpdf = new Mpdf([
            'mode' => 'zh-CN',
            'format' => 'A4',
        ]);

        $mpdf->useAdobeCJK = true;
        $mpdf->SetDisplayMode('fullpage');
        $mpdf->SetHTMLHeader("");
        $mpdf->SetHTMLFooter("");
        $mpdf->SetAutoPageBreak(true, 0);
        $mpdf->WriteHTML($content);
        if ($type == 1) {
            Header("Access-Control-Allow-Origin: *");
            Header("Access-Control-Allow-Methods: GET,PUT,POST,DELETE,OPTIONS");
            Header("Access-Control-Allow-Headers: Origin,X-Requested-With,Content-Range,Content-Disposition,Content-Type,Authorization,Accept-Encoding,Accept-Language,Cache-Control");
            Header("Access-Control-Allow-Credentials: true");
            $mpdf->Output("probation_" . $staff_info_id . ".pdf", "D");
            exit();
        } else {
            //上传oss 保存数据表
            //指定临时目录
            $file_n = "probation_" . $staff_info_id .time().".pdf";
            $file_name = sys_get_temp_dir() . "/" . $file_n;
            $mpdf->Output($file_name, 'f');
            $res = OssHelper::uploadFile($file_name);
            unlink($file_name);
            return $res['object_url'];
        }

    }
    /**
     * 获得警告信
     * @param $staff_info_id
     * @param $hire_date
     * @param $formal_at
     * @return array
     */
    public function getAlert($staff_info_id, $hire_date, $formal_at): array
    {
        $query =  MessageWarningModel::query()->columns(['type_code','date_ats as date_at'])->where('staff_info_id = :id: and is_delete=0',['id' => $staff_info_id]);
        //staff_info_id=:id and is_delete=0
        //and date_at>=:start and date_at<=:end order by id desc limit 2

        if (!empty($hire_date)) {
            $query->andWhere('created_at>=:start:' , ['start' => $hire_date . ' 00:00:00']);
        }

        if (!empty($formal_at)) {
            $query->andWhere('created_at<=:end:' , ['end' => $formal_at. ' 00:00:00']);
        }
        $query->orderBy('id DESC')->limit(2);
        $arr = $query->execute()->toArray();
        foreach ($arr as $k => $v) {
            $arr[$k]['type_code_text'] = (static::$t)->_($v['type_code']);//需要加语言
        }
        return $arr;
    }
    public function getAttendance($staff_info_id): array
    {
        $query = $this->currentProbationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? HrProbationAttendanceContractWorkerModel::query() :HrProbationAttendanceModel::query();
        $temp = $query->where('staff_info_id = :id:',['id' => $staff_info_id])->execute()->toArray();
        $data = [];
        $data[1]['late'] = "";
        $data[1]['sick'] = "";
        $data[1]['casual'] = "";
        $data[1]['lack'] = "";

        $data[2]['late'] = "";
        $data[2]['sick'] = "";
        $data[2]['casual'] = "";
        $data[2]['lack'] = "";

        if (!empty($temp)) {
            foreach ($temp as $item) {
                $data[$item['cur_level']]['late'] = $item['late'];
                $data[$item['cur_level']]['sick'] = $item['sick'];
                $data[$item['cur_level']]['casual'] = $item['casual'];
                $data[$item['cur_level']]['lack'] = empty($item['lack']) ? $item['lack'] : bcdiv((int)$item['lack'],10,2);  // 20210918 产品要求 /10
            }
        }
        return $data;
    }
    /**
     * 获得对应等级
     * @param $score
     * @return string
     */

    public function getScoreGrade($score): string
    {
        if($score<3){
            return 'D';
        }elseif($score<5){
            return 'C';
        }elseif($score<7){
            return 'B';
        }elseif($score<9){
            return 'A';
        }
        return 'A+';
    }
    public function getTplItem($tpl_id, $score)
    {
        $arr = [];
        $find =  HrProbationTplModel::query()->where('id= :id:',['id' => $tpl_id])->execute()->getFirst();
        if ($find) {
            $arr = $find->toArray();
        }
        $itemIdArr = explode(",", $arr['item_ids']);
        $itemScoreArr = explode(",", $arr['score_rule']);
        if (count($itemIdArr) == 0 || count($itemIdArr) != count($itemScoreArr)) {
            return "tpl error";
        }
        $items = HrProbationTplItemModel::find([
            'id in ('.$arr['item_ids'].') or pid in ('.$arr['item_ids'].')',
        ])->toArray();
        if (empty($items)) {
            return "not found tpl items";
        }
        $itemArr = array_column($items, null, "id");


        $score_rule_text = "(";
        foreach ($itemIdArr as $k => $v) {
            if ($k != 0) {
                $score_rule_text .= "+";
            }
            $score_rule_text .= (static::$t)->_($itemArr[$v]['name']) . "*" . (($itemScoreArr[$k]) / 100);
        }
        $score_rule_text .= ")";
        if (!empty($score)) {
            $score = json_decode($score, 1);
            //对应的名字，info根据语言环境变化。如果更改的话，不变。
            foreach ($score['list'] as $k => $v) {
	            $subscript = $k+1;
	            $score['list'][$k]['subscript'] = $score['list'][$k]['subscript'] ?? $subscript;
                $score['list'][$k]['name'] = (static::$t)->_($v['name_key']);
                $score['list'][$k]['info'] = (static::$t)->_($v['info_key']);
	            $score['list'][$k]['state'] = (string) ($score['list'][$k]['state'] ?? 1);// 1问题 2 是纯展示
	            $i = 0;
                foreach ($v['list'] as $kk => $vv) {
                    $score['list'][$k]['list'][$kk]['name'] = (static::$t)->_($vv['name_key']);
	                $score['list'][$k]['list'][$kk]['state'] = (string) ($score['list'][$k]['list'][$kk]['state'] ?? 1);// 1问题 2 是纯展示
	                if($score['list'][$k]['list'][$kk]['state'] == 1){
		                $i++;
	                }
	                $score['list'][$k]['list'][$kk]['subscript'] = $score['list'][$k]['list'][$kk]['subscript'] ?? $score['list'][$k]['subscript'].'.'.$i;
                    if (empty($vv['is_update'])) {
                        $score['list'][$k]['list'][$kk]['info'] = (static::$t)->_($vv['info_key']);
                    }
                }
            }
        } else {
            $score = [];
            $score['score'] = "";
            $score['second_score'] = "";
            $score['list'] = [];

            foreach ($itemIdArr as $k => $v) {
                $tmp = [];
	            $tmp['subscript'] = $k+1;
                $tmp['id'] = $itemArr[$v]['id'];
                $tmp['name_key'] = $itemArr[$v]['name'];
                $tmp['name'] = (static::$t)->_($itemArr[$v]['name']);

                $tmp['info_key'] = $itemArr[$v]['info'];//个人素质总得分
                $tmp['info'] = (static::$t)->_($itemArr[$v]['info']);

                $tmp['score'] = "";
                $tmp['second_score'] = "";
                $tmp['score_rule'] = $itemScoreArr[$k];
	            $tmp['state'] = (string) ($itemArr[$v]['state'] ?? 1);
                $tmp['list'] = [];
	            $tmps[$tmp['id']]['subscript'] = 0;
                foreach ($items as $kk => $vv) {
                    if ($vv['pid'] == $tmp['id']) {
                        $tmp2 = [];
                        $tmp2['id'] = $vv['id'];
                        $tmp2['name_key'] = $vv['name'];
                        $tmp2['name'] = (static::$t)->_($vv['name']);
                        $tmp2['score'] = "";
                        $tmp2['second_score'] = "";
                        $tmp2['score_rule'] = $vv['score_rule'];

                        $tmp2['info_key'] = $vv['info'];
                        $tmp2['info'] = (static::$t)->_($vv['info']);
                        $tmp2['is_update'] = 0;
	                    $tmp2['state'] = (string) ($vv['state'] ?? 1);
	                    $tmp2['subscript'] = '';
	                    if($tmp2['state'] == 1){
		                    $tmps[$tmp['id']]['subscript'] = $tmps[$tmp['id']]['subscript']+1;
		                    $tmp2['subscript'] = $tmp['subscript'].'.'.$tmps[$tmp['id']]['subscript'];
	                    }
                        $tmp['list'][] = $tmp2;
                    }
                }
                $score['list'][] = $tmp;
            }
        }
        return ["score_rule_text" => $score_rule_text, "score" => $score, 'question_num' => count($itemArr)];
    }
    /**
     * 根据新的职级来获得模板
     * @param $grade
     * @return int
     */
    public function getTplIdByJobTitleGradeV2($grade)
    {
        $grade = intval($grade);
        //小于等于14都是模板1
        if ($grade <= 14) {
            return 1;
        }
        //17及以上，都是3
        if ($grade >= 17) {
            return 3;
        }
        //其他情况2
        return 2;
    }

    /**
     * 获取转正评估流程
     * @param $probation_id
     * @param array $probationData
     * @return array
     */
    public function getProbationLogs($probation_id,$probationData = []): array
    {
        $query = $this->currentProbationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_V3 ? HrProbationAuditContractWorkerModel::query() : HrProbationAuditModel::query();
        $arr= $query->where('probation_id = :id:',['id' => $probation_id])->execute()->toArray();
        $lntVirtualApprovalId = SettingEnvModel::get_val('lnt_virtual_approval_id');
        $remark = "";
        $pic = "";
        $comment = "";  //第一次处理意见
        $data = [];
        $staffArr = [];
        if ($arr) {

            $staff_ids = array_column($arr, "audit_id");
            if ($lntVirtualApprovalId) {
                $staff_ids[] = $lntVirtualApprovalId;
            }
            if ($probationData) {
                $staff_ids[] = $probationData['staff_info_id'];
            }
            $builder = (new HrStaffInfoReadModel())->createBuilder();
            $execute =  $builder->from(['hsi' => HrStaffInfoReadModel::class])
                ->columns('hsi.staff_info_id,hsi.name,hsi.contract_company_id')
                ->inWhere('staff_info_id',$staff_ids)->getQuery()->execute();
            if ($execute) {
                $staffArr = $execute->toArray();
                $staffArr = array_column($staffArr, null, "staff_info_id");
            }
            $curLevelMax = [];
            foreach ($arr as $k => $v) {
                $tmp = [];
                $tmp['probation_audit_id'] = $v['id'];
                $tmp['created_at'] = $v['created_at'];
                $tmp['staff_info_id'] = $v['audit_id'];

                $tmp['name'] = '';
                $tmp['department_name'] = '';
                $tmp['job_title_name'] = '';
                $tmp['is_now'] = '0';
                $tmp['score'] = '';
                $tmp['is_active'] = $v['is_active'];

                //如果最后一个且是待评估
                if ($k == count($arr) - 1 && $v['audit_status'] == 1) {
                    $tmp['is_now'] = '1';
                }

                if (!empty($staffArr[$tmp['staff_info_id']])) {
                    $tmp['name'] = $staffArr[$tmp['staff_info_id']]['name'] ?? '';
                }


                $tmp['cur_level'] = $v['cur_level'];
                $curLevelMax[$tmp['cur_level']][] = $v['id'];
                if ($tmp['cur_level'] == self::CUR_LEVEL_SECOND) {
                    $tmp['audit_status_text'] = $this->getSecondStatusText($v['status'], $v['audit_status']);
                    //已处理
                    if ($v['audit_status'] == self::AUDIT_STATUS_DEAL) {
                        $remark = $v['remark'];
                        $pic = $v['pic'];//第二阶段已处理才有
                    }
                } else {
                    $comment = $v['remark'];
                    $tmp['audit_status_text'] = (static::$t)->_("probation_audit_status_" . $v['audit_status']);
                }
                if ($this->currentProbationChannelType == HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT && isCountry('PH')) {
                    $remark = $v['remark'];
                    $pic    = $v['pic'];
                }
                //如果不是超时，算下分数
                if($v['audit_status']!=self::AUDIT_STATUS_TIMEOUT){
                    $temp = $this->getScoreFromTpl($v['score'],$v['cur_level']);
                    if($tmp['cur_level'] == self::CUR_LEVEL_FIRST){
                        $t_field = 'score';
                    }else{
                        $t_field = 'second_score';
                    }
                    $tmp['score'] = $temp[$t_field]??"";
                }

                $data[] = $tmp;
            }
        }
        $returnData = $data;
        if (isCountry('MY') && $probationData) {
            if ($staffArr[$probationData['staff_info_id']]['contract_company_id'] == SalaryEnums::LNT_ID) {
                $existData = $this->addLntDataLog($lntVirtualApprovalId, $data, $probationData, $staffArr,
                    $curLevelMax ?? []);
                if ($existData) {
                    $returnData = $existData;
                }
            }
        }
        return ["data" => $returnData, "pic" => $pic, 'remark' => $remark, 'comment' => $comment];
    }

    /**
     * MY 增加 LNT最后审批人
     * @param $lntVirtualApprovalId
     * @param $originData
     * @param $probationData
     * @param $staffArr
     * @param $curLevelMax
     * @return array
     */
    public function addLntDataLog($lntVirtualApprovalId, $originData, $probationData, $staffArr, $curLevelMax): array
    {
        if (empty($lntVirtualApprovalId)) {
            return [];
        }
        $curLevelFirst  = isset($curLevelMax[HrProbationModel::CUR_LEVEL_FIRST]) ? max($curLevelMax[HrProbationModel::CUR_LEVEL_FIRST]) : null;
        $curLevelSecond = isset($curLevelMax[HrProbationModel::CUR_LEVEL_SECOND]) ? max($curLevelMax[HrProbationModel::CUR_LEVEL_SECOND]) : null;
        if ($curLevelFirst && isset($probationData['first_audit_status'])
            && in_array($probationData['first_audit_status'],
                [HrProbationModel::FIRST_AUDIT_STATUS_DONE, HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT])
        ) {
            $addLntData[HrProbationModel::CUR_LEVEL_FIRST] = true;
        }
        if ($curLevelSecond && isset($probationData['second_audit_status'])
            && in_array($probationData['second_audit_status'],
                [HrProbationModel::SECOND_AUDIT_STATUS_DONE, HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT])
        ) {
            $addLntData[HrProbationModel::CUR_LEVEL_SECOND] = true;
        }
        if (empty($addLntData)) {
            return [];
        }
        $returnData = [];
        foreach ($originData as $item) {
            $item['created_at'] = date('Y-m-d',strtotime($item['created_at']));
            $returnData[] = $item;
            if (
                $curLevelFirst == $item['probation_audit_id'] && isset($addLntData[HrProbationModel::CUR_LEVEL_FIRST])
                || $curLevelSecond == $item['probation_audit_id'] && isset($addLntData[HrProbationModel::CUR_LEVEL_SECOND])
            ) {
                $addLnt                    = $item;
                $addLnt['created_at']      = $item['created_at'];
                $addLnt['staff_info_id']   = $lntVirtualApprovalId;
                $addLnt['name']            = $staffArr[$lntVirtualApprovalId]['name'];
                $returnData[]              = $addLnt;
            }
        }
        return $returnData;
    }
    /**
     * 根据分数模板算分数
     * @param $score string|array 分数模板
     * @param $cur_level integer 第几次审批
     * @return mixed|string
     */
    public function getScoreFromTpl($score, $cur_level)
    {
        if (is_string($score)) {
            $scoreArr = json_decode($score, 1);
        } else {
            $scoreArr = $score;
        }
        if (empty($scoreArr)) {
            return "score field error";
        }

        $field = 'score';
        if ($cur_level == 2) {
            $field = 'second_score';
        }
        $score_1 = 0;
        foreach ($scoreArr['list'] as $k => $v) {
            $t_score = 0;
            foreach ($v['list'] as $kk => $vv) {
            	//会把标签转义,需要转义回来
	            $scoreArr['list'][$k]['list'][$kk]['name'] =stripslashes(htmlspecialchars_decode($vv['name']));
	            $scoreArr['list'][$k]['list'][$kk]['info'] =stripslashes(htmlspecialchars_decode($vv['info']));
                $t_score += floatval($vv[$field]) * floatval($vv['score_rule']);   //百分比
            }

            $scoreArr['list'][$k][$field] = "" . round($t_score / 100, 2);
            $score_1 += $t_score * floatval($v['score_rule']);
        }
        $scoreArr[$field] = "" . round($score_1 / 10000, 2);
        return $scoreArr;
    }
    /**
     * 获得第二次评审时候的文字
     * @param $status
     * @param $audit_status
     * @return mixed
     */
    public function getSecondStatusText($status, $audit_status)
    {
        //还没评估
        if (empty($status)) {
            return (static::$t)->_("probation_audit_status_" . $audit_status);//需要添加语言
        } else {
            return (static::$t)->_("probation_status_" . $status);//需要添加语言
        }
    }

    /**
     * 导出
     * @param $params
     * @return string
     * @throws \Exception
     */
    public function export($params)
    {
        $params['is_export'] = 1;
        $params['page']     = 1;
        $params['pagesize'] = 1000;
        $data['rows'] = [];
        while (true) {
            $_data = $this->getList($params);
            if (empty($_data['rows'])) {
                break;
            }
            $data['rows'] = array_values(array_merge($data['rows'],$_data['rows']));
            $params['page']++;
        }

        $fieldArr = [
            "staff_info_id",
            "name",
            "name_en",
            "identity",
            "mobile_company",
            "state_name",
            "manage_staff_name",
            "manage_staff_mobile",
            "manage_staff_id",
            "manage_higher_staff_name",
            "manage_higher_staff_mobile",
            "manage_higher_staff_id",
            'status_text',
            'remark',
            "hire_date",
            "formal_at",
            "days",
            "job_title_name",
            "sys_department_name",
            "department_name",
            "sys_store_name",
//            "store_area_text",
//            "piece_name",
            "first_deadline_date",
            "first_audit_status_name",
            "first_status_name",
            "second_deadline_date",
            "second_audit_status_name",
            "second_status_name",
            "first_score",
            "second_score",
        ];

        $langArr = [
            "staff_info_id",
            "name",
            "name_en",
            "identity",
            "mobile_company",
            "state",
            "manage_staff_name",
            "manage_staff_mobile",
            "manage_staff_id",
            "manage_higher_staff_name",
            "manage_higher_staff_mobile",
            "manage_higher_staff_id",
            'status_text',
            'remark',
            "hire_date",
            "formal_at",
            "days",
            "job_title",
            "sys_department_name",
            "node_department_name",
            "sys_store_name",
//            "store_area_text",
//            "piece_name",
            "excel_first_deadline_date",//第一次评估期限
            "first_status",//第一次评估状态
            "first_name",//第一次评估结果
            "excel_second_deadline_date",//第二次评估期限
            "second_status", //第二次评估状态
            "second_name",//第二次评估结果
            "excel_first_score",
            "excel_second_score",
        ];
        if (isCountry('TH') && !empty($params['is_non_front_line_probation']) && $params['is_non_front_line_probation'] == 1) {
            $langArr[]  = 'leave_date';
            $langArr[]  = 'first_deadline_date_end_timeout_day';
            $langArr[]  = 'second_deadline_date_end_timeout_day';
            $langArr[]  = 'result_notification_text';
            $fieldArr[] = 'leave_date';
            $fieldArr[] = 'first_deadline_date_end_timeout_day';
            $fieldArr[] = 'second_deadline_date_end_timeout_day';
            $fieldArr[] = 'result_notification_text';
            $langArr = array_diff($langArr, ['remark']);
            $fieldArr = array_diff($fieldArr, ['remark']);
        }
        $header = [];
        foreach ($langArr as $k => $v) {
            $header[] = (static::$t)["hr_probation_field_".$v];
        }

        $data_list = [];
        foreach ($data['rows'] as $k => $v) {
            foreach ($fieldArr as $kk => $vv) {
                $data_list[$k][$vv] = $v[$vv];
            }
            $data_list[$k] = array_values($data_list[$k]);
        }

        $file_name = $params['file_name'];
        $file_data = $this->exportExcel($header, $data_list ?? [], $file_name);
        $flashOss  = new FlashOss();
        $ossObject = env('country_code').'/probation/'.$file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);
        return $ossObject;
    }


    public function getSelectList(): array
    {
        $data = [];
        $data['store_list'] = (new SysStoreService())->getStoreListFromCache();
        //获取['sorting_no']
	    $sorting_no_list  = array_column($data['store_list'], 'sorting_no','sorting_no');
	    $data['sorting_no_list'] = [];
	    foreach($sorting_no_list as $v){
		    $data['sorting_no_list'][]=['id'=>$v,'sorting_no'=>$v];
	    }
        $data['store_list'] = array_merge($data['store_list'] , [
            [
                'id' => -1,
                'name' => GlobalEnums::HEAD_OFFICE,
            ],
        ]);
        $data['job_title_list'] = (new HrJobTitleService())->getJobTitleListFromCache(false);
        $data['area_list'] = $this->getAreaInfo();
        foreach ([1, 2, 3, 4] as $num) {
            $data['status_list'][] = [
                'id' => $num,
                'name' => (static::$t)->_("probation_status_" . $num), //需要添加语言包probation_status_
            ];
        }
        $stateArr = [1,2,3];
        $data['state_list'] = [];
        foreach ($stateArr as $state){
            $data['state_list'][] = [
                'id' => $state,
                'name'=> (static::$t)->_("staff_state_".$state),
            ];
        }
        $data['state_list'][] = [
            'id' => 4,
            'name' => (static::$t)->_("wait_leave_state"),
        ];
        $data['department_list'] = (new SysDepartmentService())->getDeptTreeListFromCache();
        return $data;
    }

    /**
     * 获取所属区域
     * @return array
     */
    public function getAreaInfo()
    {
        $area = [];
        foreach (GlobalEnums::$areas as $k => $item) {
            $area[] = ['id' => $k,'name' => $item];
        }
        return $area;
    }

    /**
     * @description 获取试用期员工列表
     * 1. 正式员工：默认均进入试用期
     * 2. 实习生：默认不进入试用期
     * 3. 特殊合同工类型：以雇佣期间来判断是否需要进试用期
     * - 若雇佣期间大于等于12个月或大于等于365天需要进入试用期
     *
     * @throws \Exception
     */
    public function getList($params)
    {
        //todo::迁移参考：BI：app/BLL/StaffSearchBLL.php method:staffList
        $data = [];
        $builder = new Builder();
        $builder->from(['hsi' => HrStaffInfoReadModel::class]);
        
        $builder->columns("count(1) as total");
        
        $builder->leftJoin(HrProbationModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hp');
        if (isCountry(['TH','PH'])){
            if (!empty($params['is_non_front_line_probation']) && $params['is_non_front_line_probation'] == 1){
                $this->setnonFrontLineFlag(true);
            }else{
                $this->setnonFrontLineFlag(false);
            }
            $this->makeNonFrontLineBuilder($builder, $params);
        }
        $builder->andWhere("hsi.formal=1 and hsi.hire_date >= '2020-06-13' and hsi.is_sub_staff = 0");
        if (isCountry('PH')) {
            $builder->andWhere("(hsi.hire_type=1 and hp.hire_type is null or hsi.hire_type=1 and  hp.hire_type is not null and hp.hire_type = 1) or hsi.hire_type in (3,4) and hsi.hire_times >= 365");
        } else {
            $builder->andWhere("hsi.hire_type = 1 or hsi.hire_type in (3,4) and hsi.hire_times >= 365 or hsi.hire_type = 2 and hsi.hire_times >= 12");
        }


        //区域
        if (isset($params['store_area_id']) && $params['store_area_id']) {
            $storeIds = call_user_func_array([$this,'getStoresByArea'], $params['store_area_id']);
            if ($storeIds) {
                $builder->inWhere('hsi.sys_store_id', $storeIds);
            }
        }
        //获取共用的查询条件
        $builder = $this->getQueryConditions($builder, $params);
        $builder = $this->checkStaffPermission($builder, $params);
        if (false === $builder) {
            return ['page_count' => 0, 'rows' => []];
        }
        //处理评估期限
        $this->makeEvaluateDateBuilder($builder, $params);

        //查总数
        $pageCount = 0;
        if (!isset($params['is_export'])) {
            $totalInfo = $builder->getQuery()->getSingleResult();
            $pageCount = intval($totalInfo->total);
            if ($pageCount === 0) {
                return ['page_count' => 0, 'rows' => []];
            }
        }

        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.name_en',
            'hsi.job_title',
            'hsi.sys_department_id',
            'hsi.node_department_id',
            'hsi.sys_store_id',
            'hsi.hire_date',
            'hsi.state',
            'hsi.wait_leave_state',
            'hsi.leave_date',
            'hsi.job_title_grade_v2',
            'hsi.identity',
            'hsi.mobile_company',
            'hsi.manger as manage_staff_id',
            'hp.status as probation_status',
            'hp.first_score',
            'hp.second_score',
            'hp.formal_at',
            'hp.remark',
            'hp.cur_level',
            'hp.is_delay',
            'hp.mark',
            'hsi.working_country',
            'hp.first_audit_status',
            'hp.first_status',
            'hp.second_audit_status',
            'hp.second_status',
            'hp.result_notification',
            'hp.first_evaluate_start',
            'hp.first_evaluate_end',
            'hp.second_evaluate_start',
            'hp.second_evaluate_end',
            'hp.probation_channel_type',
            'hp.contract_formal_date',
        ]);
        $builder->orderBy('hsi.hire_date');

        $params['page']     = $params['page'] ?? 1;
        $params['pagesize'] = $params['pagesize'] ?? 50;
        $offset             = ($params['page'] - 1) * $params['pagesize'];
        $builder->limit($params['pagesize'], $offset);
        
        $return = ['page_count' => intval($pageCount), 'rows' => []];
        $data = $builder->getQuery()->execute()->toArray();
        if ($data) {
            $manage_staffs = [];
            $manage_high_staff_map = [];
            $manage_ids = array_column($data, 'manage_staff_id');
            //如果导出的话+上上级电话
            if ($manage_ids && isset($params['is_export'])) {
                $t_arr = HrStaffInfoModel::query()->columns(['manger', 'staff_info_id'])->inWhere('staff_info_id', array_values($manage_ids))->execute()->toArray();
                $t_ids = array_column($t_arr, "manger");

                //[manager_id] =>上级id
                $manage_high_staff_map = array_column($t_arr, "manger", "staff_info_id");
                $manage_ids = array_merge($manage_ids, $t_ids);
            }
            if ($manage_ids) {
                $manage_staffs =  HrStaffInfoReadModel::query()->columns(['staff_info_id', 'name', 'mobile'])->inWhere('staff_info_id', array_values($manage_ids))->execute()->toArray();
                $manage_staffs = array_column($manage_staffs, null, "staff_info_id");
            }


            $store_ids = array_values(array_unique(array_filter(array_column($data, "sys_store_id"))));

            $pieceArr = [];
            if ($store_ids) {
                $storeArr = (new SysStoreService())->getStoreListByIdsFromCache($store_ids);
                $pieceArr = array_column($storeArr, "piece_name", "id");
            }
        }

        $jobIds = array_column($data,'job_title');
        $nodeDepartmentId = array_column($data,'node_department_id');
        $sysDepartmentId = array_column($data,'sys_department_id');

        // 拿到所有职位信息
        $allJobTitles = (new \App\Services\HrJobTitleService())->getAllJobTitleByIds($jobIds);
        // 拿到所有部门信息
        $allDepartment = (new \App\Services\SysDepartmentService())->getAllDepartmentByIds($nodeDepartmentId, $sysDepartmentId);

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        $probation_score_enumerate = (new SysService())->probation_score_enumerate();
        $probation_score_enumerate = array_column($probation_score_enumerate, 'label', 'value');
        

        foreach ($data as $item) {
            $areaInfo = $this->getAreaInfoToList($item['sys_store_id']);

            $job_title_name = isset($allJobTitles[$item['job_title']]) ? $allJobTitles[$item['job_title']]['job_name'] : '';
            if (!empty($job_title_name) && HrJobTitleModel::STATUS_2 == $allJobTitles[$item['job_title']]['status']) {
                $job_title_name .= self::$t->_('deleted');
            }

            $department_name = $allDepartment[$item['node_department_id']]['name'] ?? ($allDepartment[$item['sys_department_id']]['name'] ?? '');
            if (!empty($department_name) && SysDepartmentModel::DELETE_1 == $allDepartment[$item['node_department_id']]['deleted']) {
                $department_name .= self::$t->_('deleted');
            }

            $sys_department_name = $allDepartment[$item['sys_department_id']]['name'] ?? '';
            if (!empty($sys_department_name) && SysDepartmentModel::DELETE_1 == $allDepartment[$item['sys_department_id']]['deleted']) {
                $sys_department_name .= self::$t->_('deleted');
            }

            $row = [
                'staff_info_id'            => $item['staff_info_id'],
                'name'                     => $item['name'],
                'name_en'                  => (string)$item['name_en'],
                'mobile'                   => (string)($item['mobile'] ?? ''),
                'state'                    => $item['state'],
                'leave_date'               => $item['leave_date'] ? date("Y-m-d", strtotime($item['leave_date'])) : '',
                'sys_store_name'           => isset($this->temp()[$item['sys_store_id']]) ? $this->temp()[$item['sys_store_id']]['name'] : '',
                'job_title_name'           => $job_title_name,
                'sys_department_name'      => $sys_department_name,
                'department_name'          => $department_name,
                'store_area_id'            => $areaInfo['store_area_id'],
                'store_area_text'          => $areaInfo['store_area_text'],
                'state_name'               => $this->getStateName($item['state'], $item['wait_leave_state']),
                'working_country'          => $workingCountryList[$item['working_country']] ?? '',
                'first_audit_status'       => $item['first_audit_status'],
                'first_status'             => $item['first_status'],
                'second_audit_status'      => $item['second_audit_status'],
                'second_status'            => $item['second_status'],
                'probation_channel_type'   => $item['probation_channel_type'] ?? HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL,
                'first_audit_status_name'  => static::$t->_('hr_probation_audit_status_' . (empty($item['first_audit_status']) ? HrProbationModel::FIRST_AUDIT_STATUS_WAIT : $item['first_audit_status'])),
                'first_status_name'        => $item['first_audit_status'] == HrProbationModel::FIRST_AUDIT_STATUS_DONE ? (static::$t->_('hr_probation_status_' . (empty($item['first_status']) ? HrProbationModel::FIRST_STATUS_NOT_PASS : $item['first_status']))) : '',
                'second_audit_status_name' => static::$t->_('hr_probation_audit_status_' . (empty($item['second_audit_status']) ? HrProbationModel::SECOND_AUDIT_STATUS_WAIT : $item['second_audit_status'])),
                'second_status_name'       => $item['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE ? (static::$t->_('hr_probation_status_' . (empty($item['second_status']) ? HrProbationModel::SECOND_STATUS_NOT_PASS : $item['second_status']))) : '',
                'contract_formal_date'     => $item['contract_formal_date'],
            ];
            ////整体代码过长 留着按照源代码 定位下位置，防止难找，没问题后可删除
            if (true) {
                $now_time = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);

                //转正评估不需要leave_source
                $item['leave_source'] = 5;
                $row['hire_date'] = $item['hire_date'] ? date("Y-m-d", strtotime($item['hire_date'])) : '';

                $row['manage_staff_id'] = $item['manage_staff_id'] ?? '';
                $row['manage_staff_name'] = '';
                $row['manage_staff_mobile'] = '';

                $row['manage_higher_staff_id'] = "";
                $row['manage_higher_staff_name'] = "";
                $row['manage_higher_staff_mobile'] = "";


                if (!empty($row['manage_staff_id']) && !empty($manage_staffs[$row['manage_staff_id']])) {
                    $row['manage_staff_name'] = $manage_staffs[$row['manage_staff_id']]['name'] ?? '';
                    $row['manage_staff_mobile'] = $manage_staffs[$row['manage_staff_id']]['mobile'] ?? '';

                    $row['manage_higher_staff_id'] = $manage_high_staff_map[$row['manage_staff_id']] ?? '';
                }

                if (isset($params['is_export']) && !empty($row['manage_higher_staff_id'])) {
                    $row['manage_higher_staff_name'] = $manage_staffs[$row['manage_higher_staff_id']]['name'] ?? '';
                    $row['manage_higher_staff_mobile'] = $manage_staffs[$row['manage_higher_staff_id']]['mobile'] ?? '';
                }

                $row['first_deadline_date']  = "";
                $row['second_deadline_date'] = "";

                $row['first_deadline_date_start'] = "";
                $row['first_deadline_date_end']   = "";

                $row['second_deadline_date_start'] = "";
                $row['second_deadline_date_end']   = "";

                $row['days_90'] = "";
                $row['is_delay'] = $item['is_delay'];
                if (isCountry(['TH','PH'])) {
                    $row['first_deadline_date_start']  = $item['first_evaluate_start'] ?? '';
                    $row['first_deadline_date_end']    = $item['first_evaluate_end'] ?? '';
                    $row['second_deadline_date_start'] = $item['second_evaluate_start'] ?? '';
                    $row['second_deadline_date_end']   = $item['second_evaluate_end'] ?? '';
                    $row['formal_at']                  = $item['formal_at'] ?? '';
                    $row['first_deadline_date']        = $item['first_evaluate_start'] . '——' . $item['first_evaluate_end'];
                    $row['second_deadline_date']       = $item['second_evaluate_start'] . '——' . $item['second_evaluate_end'];
                }else{
                    if (!empty($row['hire_date'])) {
                        //拼接评估时长
                        $row = $this->assembleEvaluationTime($row,$item);
                    }
                }

                $row['status'] = $item['probation_status'] ?? '1';
                $row['status_text'] = (static::$t)->_("probation_status_" . $row['status']);
                $row['first_score'] = $item['first_score'] ? bcdiv($item['first_score'], 1000, 2) : '0';
                $row['second_score'] = $item['second_score'] ? bcdiv($item['second_score'], 1000, 2) : '0';

                if (empty($item['hire_date'])) {
                    $row['days'] = 0;
                } else {
                    $datetime_start = new \DateTime($item['hire_date']);
                    $datetime_end = new \DateTime($now_time);
                    $row['days'] = $datetime_start->diff($datetime_end)->days;
                }

                $row['identity'] = $item['identity'] ?? "";
                $row['mobile_company'] = $item['mobile_company'] ?? "";
                $row['node_department_name'] = $this->showDepartmentName($item['node_department_id']);

                //试用期状态按钮是否变红
                $row['is_not_pass'] = '0';
                //编辑按钮是否展示
                $row['is_edit_show'] = '0';

                //试用期状态为未通过 或者 已过第二次评估期限但状态为试用期中
                if ($row['status'] == StaffEnums::STATUS_NOT_PASS || ($now_time > $row['second_deadline_date_end'] && $row['status'] == StaffEnums::STATUS_PROBATION)) {
                    $row['is_not_pass'] = '1';
                }
                $row['cur_level'] = $item['cur_level'];


                //审批意见
                $row['remark'] = $item['remark'] ?? '';

                //试用期转正时候的备注
                $row['mark'] = $item['mark'] ?? '';

                $row['piece_name'] = $pieceArr[$item['sys_store_id']] ?? "";
            }
            $row['is_edit_show'] = $this->isCanEdit($row);//BI 系统 app/BLL/ProbationBLL.php
            //新需求 来源非“BY申请离职”以及职位一下职位（Bike courier 13 /van courier 110/Branch Supervisor 16/shop officer 98/ Shop Supervisor 101）不显示某些字段 并且关系
            $row['is_show'] = 0;
            $forbidden = array(13, 110, 16, 98, 101);
            //来源 '0=空，-1其他。6批量导入。其他同hold来源,1.新增,2.hirs,3旷工3天及以上，4未缴纳公款，5backyard提交申请，'
            if ($item['leave_source'] == 5 && in_array($item['job_title'], $forbidden)) {
                $row['is_show'] = 1;
            }
            $row['is_show_download'] = true; // 下载按钮除权限外的展示逻辑
            if (isCountry(['TH','PH']) && !empty($params['is_non_front_line_probation']) && $params['is_non_front_line_probation'] == 1) {
                $row['is_result_notification_show'] = $row['status'] == HrProbationModel::STATUS_NOT_PASS && $row['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE ? '1' : '0';
                $row['result_notification_text']    = empty($item['result_notification']) ? static::$t->_('probation_result_notification_no') : static::$t->_('probation_result_notification_yes');

                $row['first_deadline_date_end_timeout_day']  = 0;
                $row['second_deadline_date_end_timeout_day'] = 0;
                if ($row['first_audit_status'] == HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT && !empty($row['first_deadline_date_end']) && $now_time >= $row['first_deadline_date_end']) {
                    $row['first_deadline_date_end_timeout_day'] = (new \DateTime($row['first_deadline_date_end']))->diff(new \DateTime($now_time))->days;
                }
                if ($row['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT && !empty($row['second_deadline_date_end']) && $now_time >= $row['second_deadline_date_end']) {
                    $row['second_deadline_date_end_timeout_day'] = (new \DateTime($row['second_deadline_date_end']))->diff(new \DateTime($now_time))->days;
                }
                
                $row['first_score'] = $item['first_score'] ? $probation_score_enumerate[$item['first_score']] : '';
                $row['second_score'] = $item['second_score'] ? $probation_score_enumerate[$item['second_score']] : '';
                $row['leave_date'] = '';
                if ($item['state'] == HrStaffInfoModel::STATE_RESIGN || ($item['state'] == HrStaffInfoModel::STATE_ON_JOB && $item['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES)){
                    $row['leave_date'] = $item['leave_date'] ? date("Y-m-d", strtotime($item['leave_date'])) : '';
                }
                $row['is_show_download'] = $row['first_audit_status'] == 3 || $row['second_audit_status'] == 3;
            }
            $return['rows'][] = $row;
        }

        return $return;
    }

    protected function makeEvaluateDateBuilder(&$builder, $params)
    {
        if ($this->getNonFrontLineFlag() && !empty($params['first_date_begin']) && !empty($params['first_date_end'])) {
            $builder->andWhere('first_evaluate_start >= :first_date_begin:  and first_evaluate_start <= :first_date_end:',
                [
                    'first_date_begin' => $params['first_date_begin'],
                    'first_date_end'   => $params['first_date_end'],
                ]);
        }
        if ($this->getNonFrontLineFlag() && !empty($params['second_date_begin']) && !empty($params['second_date_end'])) {
            $builder->andWhere('second_evaluate_start >= :second_date_begin:  and second_evaluate_end <= :second_date_end:',
                [
                    'second_date_begin' => $params['second_date_begin'],
                    'second_date_end'   => $params['second_date_end'],
                ]);
        }
    }

    protected function makeNonFrontLineBuilder(&$builder, $params)
    {
        $builder->leftJoin(HrProbationTargetModel::class, 'target.staff_info_id = hsi.staff_info_id', 'target');
        if ($this->getNonFrontLineFlag()){
            // 非一线员工试用期列表
            $builder->andWhere(
                "target.staff_info_id is not null and 
                target.send_state in ({target_send_state:array}) and 
                target.setting_state in ({target_setting_state:array}) and 
                target.sign_state in ({target_sign_state:array}) and 
                hp.probation_channel_type = :probation_channel_type:
                ",
                [
                    'target_send_state'      => [
                        HrProbationTargetModel::SEND_STATE_FINISH,
                        HrProbationTargetModel::SEND_STATE_ADJUST_FINISH,
                    ],
                    'target_setting_state'   => [
                        HrProbationTargetModel::SETTING_STATE_FINISH,
                        HrProbationTargetModel::SETTING_STATE_ADJUST,
                    ],
                    'target_sign_state'      => [
                        HrProbationTargetModel::SIGN_STATE_FINISH,
                        HrProbationTargetModel::SIGN_STATE_ADJUST_FINISH,
                    ],
                    'probation_channel_type' => HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
                ]);
            $probation_staff = (new SettingEnvService())->getSetVal('probation_staff', ',');
            if (!empty($probation_staff)) {
                $builder->andWhere('hsi.staff_info_id NOT IN ({probation_staff:array})',
                    ['probation_staff' => $probation_staff]);
            }
        }else{
            $builder->andWhere(
                "!(target.staff_info_id is not null and 
                target.send_state in ({target_send_state:array}) and 
                target.setting_state in ({target_setting_state:array}) and 
                target.sign_state in ({target_sign_state:array})) and 
                hp.probation_channel_type != :probation_channel_type:
                ",
                [
                    'target_send_state'      => [
                        HrProbationTargetModel::SEND_STATE_FINISH,
                        HrProbationTargetModel::SEND_STATE_ADJUST_FINISH,
                    ],
                    'target_setting_state'   => [
                        HrProbationTargetModel::SETTING_STATE_FINISH,
                        HrProbationTargetModel::SETTING_STATE_ADJUST,
                    ],
                    'target_sign_state'      => [
                        HrProbationTargetModel::SIGN_STATE_FINISH,
                        HrProbationTargetModel::SIGN_STATE_ADJUST_FINISH,
                    ],
                    'probation_channel_type' => HrProbationModel::PROBATION_CHANNEL_TYPE_NON_FRONTLINE,
                ]);
        }
        
    }

    /**
     * 根据网点获取对应 所属区域
     * @param $sysStoreId
     * @return mixed
     */
    public function getAreaInfoToList($sysStoreId) {
        $areaInfo = $this->getAreaByStore($sysStoreId);

        $result['store_area_id'] = $areaInfo ? $areaInfo['id'] : 0;
        $result['store_area_text'] = $areaInfo ? GlobalEnums::$areas[$areaInfo['id']] : '';
        return $result;
    }

    /**
     * 获取员工在职状态
     *
     * @param $state
     * @param $waitLeaveState
     * @return string
     */
    public function getStateName($state, $waitLeaveState): string
    {
        if ($state == 1 && $waitLeaveState == 1) {
            $stateName = (static::$t)->t('wait_leave_state_' . $waitLeaveState);
        } else {
            $stateName = (static::$t)->t('staff_state_' . $state);
        }

        return $stateName;
    }

    /**
     * 获得员工审核状态
     * @param $state
     * @return string
     */
    protected function getApproveStateName($state): string
    {
        //如果不存在，默认是未提交
        if (empty($state)) {
            $state = 1;
        }
        return (static::$t)->t('approve_state_' . $state);
    }

    /**
     * 获取网点所属区域
     * @param $storeId
     * @return array|string
     */
    public function getAreaByStore($storeId)
    {
        $province = '';
        $storeList = $this->temp();
        foreach ($storeList as $sid => $store) {
            if ($sid == $storeId) {
                $province = $store['province_code'];
                break;
            }
        }

        if (!$province) {
            return '';
        }

        foreach (self::areaProvince() as $id => $provinces) {
            if (empty($id))
                continue;
            if (in_array($province, $provinces)) {
                return ['id' => $id, 'name' => GlobalEnums::$areas[$id]];
            }
        }
        return [];
    }

    /**
     * 根据员工ID,去backyard库找该员工的审核状态
     * @param $staffIds
     * @return array
     */
    public function getApproveState($staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $assetsHandover = AssetsHandoverModel::query()->columns(['state', 'staff_id'])->inWhere('staff_id', $staffIds)->execute()->toArray();
        if ($assetsHandover) {
            $assetsHandover = array_column($assetsHandover, "state", "staff_id");
        }

        return $assetsHandover;
    }

    /**
     * 获取部门列表
     *
     * @return array
     */
    protected function sysDepartmentList(): array
    {
        return (new DepartmentService())->getAllDepartmentFromCache();
    }

    public function departmentList()
    {
        $redisKey = 'menuList_v3';
        $redis = $this->getDI()->get('redis');
        $data = $redis->get($redisKey);
        if ($data) {
            return json_decode($data, true);
        }
        $departments = SysDepartmentModel::query()->columns(['id', 'ancestry', 'name'])->where('deleted = 0')->execute()->toArray();
        $data = [];
        $children = [];
        foreach ($departments as $department) {
            if ($department['id'] == 1) {
                continue;
            }
            $data[$department['id']] = ['id' => $department['id'], 'name' => $department['name'], 'children' => []];
            $children[$department['ancestry']][] = ['id' => $department['id'], 'name' => $department['name']];
        }
        foreach ($data as $k => $item) {
            if (isset($children[$item['id']])) {
                $item['children'] = $children[$item['id']];
                $data[$k] = $item;
                foreach ($children[$item['id']] as $child) {
                    unset($data[$child['id']]);
                }
            }
        }
        $data = array_values($data);
        $redis->set($redisKey, json_encode($data, JSON_UNESCAPED_UNICODE), 86400);
        return $data;
    }

    /**
     * 是否能编辑
     * @param $item
     * @return string
     */

    public function isCanEdit($item): string
    {
        $now_time = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);
        //应该是 员工试用期状态为通过 并且当天早于转正日期  || 员工试用期状态为未通过  || 到了第二阶段评估并且员工还在试用期
        if (
            ($item['status'] == StaffEnums::STATUS_PASS && $now_time < $item['formal_at'])
            || $item['status'] == StaffEnums::STATUS_NOT_PASS
            || ($now_time >= $item['second_deadline_date_start'] && $item['status'] == StaffEnums::STATUS_PROBATION)) {
            return '1';
        } else {
            return '0';
        }
    }

    /**
     * 获得日期
     * @param $date
     * @param $days 40
     * @param int $flag 0=40天之前，1=40天之后
     * @return false|string
     */
    public function getDateByDays($date, $days, int $flag = 0)
    {
        $default = '-';
        if (!empty($flag)) {
            $default = '+';
        }
        return date("Y-m-d", strtotime($default . $days . " days", strtotime($date)));
    }

    //查询本部门和子部门
    public function deptNodeList($dept_id): array
    {
        $dept_detail = SysDepartmentModel::findFirst(['conditions' => 'id = ?1', 'bind' => [1 => $dept_id]])->toArray();
        if (isset($dept_detail['ancestry_v3'])) {
            return SysDepartmentModel::find([
                'columns' => 'id, ancestry, ancestry_v3, name',
                'conditions' => 'deleted = 0 and ancestry_v3 like :ancestry_v3: or id = :dept_id:',
                'bind' => [
                    'ancestry_v3' => $dept_detail['ancestry_v3'] . '/%',
                    'dept_id' => $dept_id,
                ],
            ])->toArray();
        }
        return [];
    }

    // 根据区域获取网点列表
    public function getStoresByArea(...$str): array
    {
        $ths = [];
        foreach ($str as $name) {
            if (isset(self::areaProvince()[$name])) {
                $ths = array_merge($ths, self::areaProvince()[$name]);
            }
        }

        $stores = [];
        $storeList = $this->temp();
        foreach ($storeList as $storeId => $store) {
            if (in_array($store['province_code'], $ths)) {
                $stores[] = $storeId;
            }
        }
        return $stores;
    }

    /**
     * 获取所属区域与省份对应列表
     * @return array
     */
    private function areaProvince(): array
    {
        if (!self::$areaProvince) {
            $provinces = SysProvinceModel::query()->where('deleted = 0')->columns(['code', 'manage_geography_code'])->execute()->toArray();
            if ($provinces && is_array($provinces)) foreach ($provinces as $province) {
                self::$areaProvince[$province['manage_geography_code']][] = $province['code'];
            }
            unset($provinces);
        }

        return self::$areaProvince;
    }

    /**
     * category:
     * 1: 收派件网点
     * 2: 分拨中心
     * 3: 第三方代理收放网点
     * 4: 拦站网点
     * 5: 收派件网点
     * 6: 加盟商网点
     * 网点分类 1: 收派件网点 2: 分拨中心 3:第三方代理 4:揽件网点(市场) 5:收派件网点(shop) 6:加盟商网点
     */
    public function temp()
    {
        if (self::$storeList) {
            return self::$storeList;
        }

        static $store;
        if (!is_null($store)) {
            return $store;
        }
        $store = [-1 => [
            'id' => '-1',
            'name' => GlobalEnums::HEAD_OFFICE,
            'state' => 1,
            'category' => 0,
            'province_code' => '0',
            "region_name" => '',
            "piece_name" => '',
            'sorting_no' => '',
        ]];
        $allStoreList = (new SysStoreService())->getStoreListFromCache();
        foreach ($allStoreList as $one) {
            if ($one['id'] == 'TH99999999') {
                continue;
            }
            $store[$one['id']] = [
                'id' => $one['id'],
                'name' => $one['name'],
                'state' => $one['state'],
                'category' => $one['category'],
                'province_code' => $one['province_code'],
                'sorting_no' => $one['sorting_no'],
            ];
        }
        self::$storeList = $store;
        return $store;
    }


    /**
     * @description: 获取转正评估配置项
     * @return     :[]
     * <AUTHOR> L.J
     * @time       : 2022/8/25 21:36
     */
    public function getProbationEvaluateEnv() {
        //[
        //                     'job_grade_exceed'         => 17, //职级分界线
        //                     'formal_days'              => 120, //小于等于 17 的转正天数
        //                     'formal_days_exceed'       => 120,// 大于 17 的转正天数
        //                     'first_check_days'         => 45,   //第一阶段终止 日期
        //                     'second_check_days'        => 85,  //第二 阶段终止 日期
        //                     'first_check_days_exceed'  => 48,   //第一阶段终止 日期  大于 17
        //                     'second_check_days_exceed' => 88,  //第二阶段终止 日期 大于 17
        //                     'evaluate_day'             => [
        //                         '1' => 40,     //小于等于 17 的第一阶段发送时间
        //                         '2' => 75,      //小于等于 17 的第二阶段发送时间
        //                     ],
        //                     'evaluate_day_exceed'      => [
        //                         '1' => 40, //大于 17 的第一阶段发送时间
        //                         '2' => 75, //大于 17 的第一阶段发送时间
        //                     ],
        //                     'cpo_staff_id'             => '56780',  //cpo 工号
        //                     //小于 job_grade_exceed 的评估天数
        //                     'duration_day'             => [
        //                         //第一阶段
        //                         '1' => [
        //                             '1' => 3, //第一次  上级 评估天数
        //                             '2' => 3, //第二次  上级上级 评估天数
        //                         ],
        //                         //第二阶段
        //                         '2' => [
        //                             '1' => 5, //第一次  上级 评估天数
        //                             '2' => 5, //第二次  上级上级 评估天数
        //
        //                         ],
        //                     ],
        //                     //大于 job_grade_exceed 的评估天数
        //                     'duration_day_exceed'      => [
        //                         //第一阶段
        //                         '1' => [
        //                             '1' => 3, //第一次  上级 评估天数
        //                             '2' => 3, //第二次  上级上级 评估天数
        //                             '3' => 3, //第二次  上级上级 评估天数
        //                         ],
        //                         //第二阶段
        //                         '2' => [
        //                             '1' => 6, //第一次  上级  评估天数
        //                             '2' => 6, //第二次  上级上级 评估天数
        //                             '3' => 3, //第三次    cop 评估天数
        //                         ],
        //                     ],
        //                 ])
        $setting_env                      = new SettingEnvService();
        $probation_evaluate_env           = $setting_env->getSetValFromCache('probation_evaluate_env');
        return (empty($probation_evaluate_env) ? [] : json_decode($probation_evaluate_env, true));

    }

    /**
     * @description:获取查询条件
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/8/29 15:04
     */
    public function  getQueryConditions($builder,$params){

        //直接上级
        if (!empty($params['manage_staff_id'])) {
            $builder->andWhere('hsi.manger = :manage_staff_id:', ['manage_staff_id' => intval($params['manage_staff_id'])]);
        }

        //试用期状态
        if (!empty($params['status'])) {
            //如果是选了试用期中
            if (in_array(StaffEnums::STATUS_PROBATION, $params['status'])) {
                $builder->andWhere('hp.status IN ({status:array}) OR hp.status IS NULL', ['status' => $params['status']]);

              //  并且 选择了是否超时
                if(!empty($params['is_timeout'])) {
                    //超过第二阶段结束时间
                    //小于等于 x 级的
                    $second_end = $this->getDateByDays(gmdate('Y-m-d', time() + ($this->timeOffset) * 3600),
                        $this->probation_second_deadline_end);
                    //大于 x 级的
                    $second_end_two = $this->getDateByDays(gmdate('Y-m-d', time() + ($this->timeOffset) * 3600),
                        $this->probation_second_deadline_end_two);
                    //超过第二阶段结束时间
                    if ($params['is_timeout'] == 1) {
                        $builder->andWhere('((hsi.hire_date< :second_end: and hsi.job_title_grade_v2 <= :job_grade:) or (hsi.hire_date< :second_end_two: and hsi.job_title_grade_v2 > :job_grade: ))',
                            [
                                'second_end'     => $second_end,
                                'job_grade'      => $this->job_grade,
                                'second_end_two' => $second_end_two,
                            ]);
                    } else {
                        //未超过过第二阶段结束时间
                        $builder->andWhere('((hsi.hire_date>= :second_end: and hsi.job_title_grade_v2 <= :job_grade:) or (hsi.hire_date>= :second_end_two: and hsi.job_title_grade_v2 > :job_grade: ))',
                            [
                                'second_end'     => $second_end,
                                'job_grade'      => $this->job_grade,
                                'second_end_two' => $second_end_two,
                            ]);
                    }
                }

            } else {
                $builder->inWhere('hp.status', $params['status']);
            }
        }
        //二级部门id
        if (!empty($params['node_department_id'])) {
            $builder->andWhere('hsi.node_department_id = :node_department_id:', ['node_department_id' => intval($params['node_department_id'])]);
        }


        //第一次评估期限
        if ($this->getFirsEvaluateDateFlag() && empty($this->getNonFrontLineFlag()) && !empty($params['first_date_begin']) && !empty($params['first_date_end'])) {
            $first_begin = $this->getDateByDays($params['first_date_begin'], $this->probation_first_deadline_start).' 00:00:00';
            $first_end = $this->getDateByDays($params['first_date_end'], $this->probation_first_deadline_start).' 23:59:59';
            //大于 x 级的评估期限
            $first_begin_two = $this->getDateByDays($params['first_date_begin'], $this->probation_first_deadline_start_two).' 00:00:00';
            $first_end_two = $this->getDateByDays($params['first_date_end'], $this->probation_first_deadline_start_two).' 23:59:59';
            if (isCountry('PH')) {
                $firstCondition = '
                (
                    (hp.id is null or hp.id is not null and hp.probation_channel_type=:probation_channel_type_1:) and ((hsi.hire_date >= :first_date_begin: and hsi.hire_date <= :first_date_end: and hsi.job_title_grade_v2 <= :job_grade: ) or (hsi.hire_date >= :first_date_begin_two: and hsi.hire_date <= :first_date_end_two: and hsi.job_title_grade_v2 > :job_grade:))
                ) or
                (
                    hp.probation_channel_type=:probation_channel_type_2: and hp.first_evaluate_start>=:first_start: and hp.first_evaluate_start<=:first_end:
                )';
                $firstConditionBind = [
                    'first_date_begin'         => $first_begin,
                    'first_date_end'           => $first_end,
                    'job_grade'                => $this->job_grade,
                    'first_date_begin_two'     => $first_begin_two,
                    'first_date_end_two'       => $first_end_two,
                    'probation_channel_type_1' => HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL,
                    'probation_channel_type_2' => HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_TO_FORMAL,
                    'first_start'              => $params['first_date_begin'],
                    'first_end'                => $params['first_date_end'],
                ];
            } else {
                $firstConditionBind = [
                    'first_date_begin'         => $first_begin,
                    'first_date_end'           => $first_end,
                    'job_grade'                => $this->job_grade,
                    'first_date_begin_two'     => $first_begin_two,
                    'first_date_end_two'       => $first_end_two,
                ];
                $firstCondition = '((hsi.hire_date >= :first_date_begin: and hsi.hire_date <= :first_date_end: and hsi.job_title_grade_v2 <= :job_grade: ) or (hsi.hire_date >= :first_date_begin_two: and hsi.hire_date <= :first_date_end_two: and hsi.job_title_grade_v2 > :job_grade:))';
            }
            $builder->andWhere($firstCondition,
               $firstConditionBind
            );

        }

        //第二次评估期限
        if (empty($this->getNonFrontLineFlag()) && !empty($params['second_date_begin']) && !empty($params['second_date_end'])) {
            $second_begin = $this->getDateByDays($params['second_date_begin'], $this->probation_second_deadline_start).' 00:00:00';
            $second_end = $this->getDateByDays($params['second_date_end'], $this->probation_second_deadline_start).' 23:59:59';
            //大于 x 级的评估期限
            $second_begin_two = $this->getDateByDays($params['second_date_begin'], $this->probation_second_deadline_start_two).' 00:00:00';
            $second_end_two = $this->getDateByDays($params['second_date_end'], $this->probation_second_deadline_start_two).' 23:59:59';
            if (isCountry('PH')) {
                $firstCondition = '
                (
                    (hp.id is null or hp.id is not null and hp.probation_channel_type=:probation_channel_type_1:) and ((hsi.hire_date >= :first_date_begin: and hsi.hire_date <= :first_date_end: and hsi.job_title_grade_v2 <= :job_grade: ) or (hsi.hire_date >= :first_date_begin_two: and hsi.hire_date <= :first_date_end_two: and hsi.job_title_grade_v2 > :job_grade:))
                ) or
                (
                    hp.probation_channel_type=:probation_channel_type_2: and hp.second_evaluate_start>=:second_start: and hp.second_evaluate_start<=:second_end:
                )';
                $firstConditionBind = [
                    'first_date_begin'         => $second_begin,
                    'first_date_end'           => $second_end,
                    'job_grade'                => $this->job_grade,
                    'first_date_begin_two'     => $second_begin_two,
                    'first_date_end_two'       => $second_end_two,
                    'probation_channel_type_1' => HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL,
                    'probation_channel_type_2' => HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_TO_FORMAL,
                    'second_start'             => $params['second_date_begin'],
                    'second_end'               => $params['second_date_end'],
                ];
            } else {
                $firstConditionBind = [
                    'first_date_begin'     => $second_begin,
                    'first_date_end'       => $second_end,
                    'job_grade'            => $this->job_grade,
                    'first_date_begin_two' => $second_begin_two,
                    'first_date_end_two'   => $second_end_two,
                ];
                $firstCondition= '((hsi.hire_date >= :first_date_begin: and hsi.hire_date <= :first_date_end: and hsi.job_title_grade_v2 <= :job_grade: ) or (hsi.hire_date >= :first_date_begin_two: and hsi.hire_date <= :first_date_end_two: and hsi.job_title_grade_v2 > :job_grade:))';
            }
            $builder->andWhere($firstCondition, $firstConditionBind);
        }
        // ph  合同工（五个月）转正式员工 类型 - 转正式员工日期筛选
        if (isCountry('PH') && !empty($params['contract_formal_date_begin']) && !empty($params['contract_formal_date_end'])) {
            $builder->andWhere('hp.probation_channel_type=:probation_channel_type: and hp.contract_formal_date >=:contract_formal_date_begin: and hp.contract_formal_date <=:contract_formal_date_end:',['probation_channel_type' => HrProbationModel::PROBATION_CHANNEL_TYPE_CONTRACT_TO_FORMAL,'contract_formal_date_begin'=>$params['contract_formal_date_begin'],'contract_formal_date_end' => $params['contract_formal_date_end']]);
        }
        //职位
        if (!empty($params['job_title'])) {
            $builder->inWhere('hsi.job_title', $params['job_title']);
        }

        if (isset($params['name']) && $params['name']) {
            $builder->andWhere('hsi.name like :name: or hsi.staff_info_id like :name: or hsi.emp_id like :name: or hsi.name_en like :name:', ['name' => $params['name']]);
        }
        if (isset($params['staff_id']) && $params['staff_id']) {
            $builder->andWhere('hsi.staff_info_id = :staff_id:', ['staff_id' => $params['staff_id']]);
        }
        if (isset($params['name_en']) && $params['name_en']) {
            $builder->andWhere('hsi.name_en like :name_en:', ['name_en' => '%'.$params['name_en'].'%']);
        }
        if (isset($params['staff_name']) && $params['staff_name']) {
            $builder->andWhere('hsi.name like :staff_name:', ['staff_name' => '%'.$params['staff_name'].'%']);
        }
        if (isset($params['hire_date_begin']) && $params['hire_date_begin'] != '0000-00-00 00:00:00') {
            $builder->andWhere('hsi.hire_date >= :hire_date_begin:', ['hire_date_begin' => $params['hire_date_begin']]);
        }
        if (isset($params['hire_date_end']) && $params['hire_date_end'] != '0000-00-00 00:00:00') {
            $builder->andWhere('hsi.hire_date <= :hire_date_end:', ['hire_date_end' => $params['hire_date_end']]);
        }

        if (!empty($params['leave_source'])) {
            $builder->andWhere('hsi.leave_source= :leave_source:', ['leave_source' => $params['leave_source']]);
        }

        if (isset($params['leave_date_begin']) && $params['leave_date_begin'] != '0000-00-00 00:00:00'
            &&
            isset($params['leave_date_end']) && $params['leave_date_end'] != '0000-00-00 00:00:00'
        ) {
            $results = StaffResignModel::query()->columns(['distinct submitter_id'])
                                       ->where('leave_date >= :leave_date_begin:', ['leave_date_begin' => $params['leave_date_begin']])
                                       ->andWhere('leave_date <= :leave_date_end:', ['leave_date_end' => $params['leave_date_end']])
                                       ->execute()
                                       ->toArray();
            $staffInfoIds = array_values(array_filter(array_column($results, 'submitter_id')));

            if ($staffInfoIds) {
                $builder->andWhere('hsi.staff_info_id in (:ids:) or hsi.leave_date >= :leave_date_begin: and hsi.leave_date <= :leave_date_end:',
                                   ['ids' => $staffInfoIds, 'leave_date_begin' => $params['leave_date_begin'], 'leave_date_end' => $params['leave_date_end']]);
            }
        }

        if(! empty($params['state'])){
            $params['state'] = is_array($params['state']) ? $params['state'] : explode(',', $params['state']);
            $tmp_sql = [];
            if(in_array(4, $params['state'])){
                $tmp_sql[] = '( hsi.state = 1 and hsi.wait_leave_state  = 1 )';
            }
            if(in_array(Enums::HRIS_WORKING_STATE_1,$params['state'])){
                $tmp_sql[] = '( hsi.state = '.Enums::HRIS_WORKING_STATE_1 .' and hsi.wait_leave_state  = 0) ';
            }
            if(in_array(Enums::HRIS_WORKING_STATE_2,$params['state'])){
                $tmp_sql[] = 'hsi.state = '.Enums::HRIS_WORKING_STATE_2;
            }
            if(in_array(Enums::HRIS_WORKING_STATE_3,$params['state'])){
                $tmp_sql[] = 'hsi.state = '.Enums::HRIS_WORKING_STATE_3;
            }
            $_sql = implode(' or ',$tmp_sql);
            $builder->andWhere($_sql);
        }

        if (isset($params['store']) && $params['store']) {
            $builder->inWhere('hsi.sys_store_id',$params['store']);
        }
        if (isset($params['stores']) && $params['stores']) {
            $stores = array_filter($params['stores'], function ($item) {
                return strval($item);
            });
            $builder->inWhere('hsi.sys_store_id', $stores);
        }

        if (isset($params['department']) && $params['department']) {
            //试用期管理 查询当前部门及自部门
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere('hsi.node_department_id', $deptIds);
            }
        }

        //工作所在国家
        if(isset($params['working_country']) && $params['working_country']){
            $builder->inWhere('hsi.working_country', array_values($params['working_country']));
        }

        if (!empty($params['first_audit_status']) && is_array($params['first_audit_status'])) {
            $condition = 'hp.first_audit_status in ({first_audit_status:array})';
            if (in_array(HrProbationModel::FIRST_AUDIT_STATUS_WAIT,$params['first_audit_status'])) {
                $condition .= '  or hp.staff_info_id is null';
            }
            $builder->andWHere($condition,['first_audit_status' => $params['first_audit_status']]);
        }
        if (!empty($params['second_audit_status']) && is_array($params['second_audit_status'])) {
            $condition = 'hp.second_audit_status in ({second_audit_status:array})';
            if (in_array(HrProbationModel::SECOND_AUDIT_STATUS_WAIT,$params['second_audit_status'])) {
                $condition .= '  or hp.staff_info_id is null';
            }
            $builder->andWHere($condition,['second_audit_status' => $params['second_audit_status']]);
        }
        if (isset($params['first_status']) && in_array($params['first_status'],HrProbationModel::$passMap)) {
            $builder->andWhere('hp.first_audit_status = :first_audit_status_1: and  hp.first_status = :first_status_1:',['first_audit_status_1'=>HrProbationModel::FIRST_AUDIT_STATUS_DONE,'first_status_1' => $params['first_status']]);
        }
        if (isset($params['second_status']) && in_array($params['second_status'],HrProbationModel::$passMap)) {
            $builder->andWhere('hp.second_audit_status = :second_audit_status_1:  and hp.second_status=:second_status_1:',['second_audit_status_1'=>HrProbationModel::SECOND_AUDIT_STATUS_DONE,'second_status_1' => $params['second_status']]);
        }

        if (!empty($params['formal_at_start']) && !empty($params['formal_at_end'])) {
            $builder->andWhere('hp.formal_at >= :formal_at_start: and hp.formal_at <= :formal_at_end: ',
                ['formal_at_start' => $params['formal_at_start'], 'formal_at_end' => $params['formal_at_end']]);
        }

        return $builder;
    }


    /**
     * @description: 获取第一阶段和第二阶段评估时长
     * @author: L.J
     * @time: 2022/10/31 10:17
     */
    public function assembleEvaluationTime($row = [],$item=[]){
        //x 级一下
        if(empty($row) || empty($item)) {
            return [];
        }
        if ($item['job_title_grade_v2'] <= $this->job_grade) {
            $row['first_deadline_date_start']  = $this->getDateByDays($row['hire_date'],
                $this->probation_first_deadline_start, 1);
            $row['first_deadline_date_end']    = $this->getDateByDays($row['hire_date'],
                $this->probation_first_deadline_end, 1);
            $row['second_deadline_date_start'] = $this->getDateByDays($row['hire_date'],
                $this->probation_second_deadline_start, 1);
            $row['second_deadline_date_end']   = $this->getDateByDays($row['hire_date'],
                $this->probation_second_deadline_end, 1);
            $row['formal_at']                  = $item['formal_at'] ?? $this->getDateByDays($row['hire_date'],
                $this->formal_days, 1);
        } else {
            //x 级以上
            $row['first_deadline_date_start']  = $this->getDateByDays($row['hire_date'],
                $this->probation_first_deadline_start_two, 1);
            $row['first_deadline_date_end']    = $this->getDateByDays($row['hire_date'],
                $this->probation_first_deadline_end_two, 1);
            $row['second_deadline_date_start'] = $this->getDateByDays($row['hire_date'],
                $this->probation_second_deadline_start_two, 1);
            $row['second_deadline_date_end']   = $this->getDateByDays($row['hire_date'],
                $this->probation_second_deadline_end_two, 1);
            $row['formal_at']                  = $item['formal_at'] ?? $this->getDateByDays($row['hire_date'],
                $this->formal_days_two, 1);
        }

        $row['first_deadline_date']  = $row['first_deadline_date_start'].'——'.$row['first_deadline_date_end'];
        $row['second_deadline_date'] = $row['second_deadline_date_start'].'——'.$row['second_deadline_date_end'];
        return $row;
    }

    /**
     * 校验当前登陆人查看数据权限
     * @param $builder
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    public function checkStaffPermission($builder, $params)
    {
        $staffInfoId = $params['staff_info_id'];

        if (empty($staffInfoId)) {
            throw new \Exception("miss args");
        }

        //获取员工角色
        $staffPosition = HrStaffInfoPositionModel::find([
            'staff_info_id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staffInfoId,
            ],
            'columns' => 'position_category',

        ])->toArray();
        $staffPositionIds = array_column($staffPosition, 'position_category');

        //获取配置权限角色
        $configureList = SettingEnvModel::getMultiEnvByCode(['probation_roles_all', 'probation_roles_part']);
        $probationRolesAll = isset($configureList['probation_roles_all']) ? explode(',', $configureList['probation_roles_all']): [];
        $probationRolesPart = isset($configureList['probation_roles_part']) ? explode(',', $configureList['probation_roles_part']): [];

        //全部权限 角色：HR Management[17]、HRIS管理员[41]、超级管理员[99]、HR系统管理员[115]
        //部分权限 角色：HRBP[68]、HR service[77]、Payroll[42]、HR Generalist
        //其余人 查看自己所属部门以及子部门的数据
        if (array_intersect($probationRolesAll, $staffPositionIds) || in_array($staffInfoId, explode(',', env('sa_id')))) {
            return $builder;
        } else if (array_intersect($probationRolesPart, $staffPositionIds)) {
            $staffService = new StaffService();
            $getStaffData = $staffService->setExpire(60 * 10)->getStaffJurisdictionFromCache($staffInfoId);

            //没有管辖权限
            if (empty($getStaffData['departments']) &&
                empty($getStaffData['stores']) &&
                empty($getStaffData['regions']) &&
                empty($getStaffData['pieces']) &&
                empty($getStaffData['store_categories'])
            ) {
                return false;
            }

            $result = (new AttendanceToolService())->generateBuilder($builder, ['part' => $getStaffData]);
            $this->logger->write_log("[checkStaffPermission][get sql]" . $result->getPhql(), 'info');
            return $result;
        } else {
            //查看自己所属部门以及子部门的数据
            $staffInfo = HrStaffInfoModel::findFirst([
                'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $staffInfoId,
                ],
                'columns' => 'staff_info_id,node_department_id',
            ]);
            if (empty($staffInfo)) {
                return false;
            }

            $departmentInfo = SysDepartmentModel::getSpecifiedDeptAndSubDept($staffInfo->node_department_id);
            if (empty($departmentInfo)) {
                return false;
            }
            $builder->inWhere('hsi.node_department_id', $departmentInfo);
            return $builder;
        }
    }

    /**
     * @param $probationId
     * @param $level
     * @return mixed|string
     */
    public function getProbationComment($probationId, $level)
    {
        $arr= HrProbationAuditModel::query()
            ->where('probation_id = :id: and cur_level=:level:',['id' => $probationId,'level'=>$level])
            ->andWhere('audit_status=:status:',['status'=> self::AUDIT_STATUS_DEAL])
            ->orderBy('id desc')
            ->limit(1)
            ->execute()
            ->toArray();
        return $arr[0]['remark'] ?? '';
    }

    /**
     * 补全历史的评估状态 评估结果
     */
    public function handleHistoryStatus($staffIds = [])
    {
        $page             = 1;
        $pageSize         = 2000;
        $hrProbationTable = (new HrProbationModel())->getSource();
        // first_audit_status second_audit_status 1待发起2评估中3已完成4已超时
        while (true) {
            $conditions = [
                'offset' => ($page - 1) * $pageSize,
                'limit'  => $pageSize,
            ];
            if ($staffIds) {
                $conditions['conditions'] = 'staff_info_id in ({ids:array})';
                $conditions['bind']       = ['ids' => $staffIds];
            }
            $find = HrProbationModel::find($conditions)->toArray();
            if (empty($find)) {
                break;
            }
            $probationIds = array_column($find, 'id');
            $findStaff    = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,IFNULL(job_title_grade_v2,0) as job_title_grade_v2',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => array_column($find, 'staff_info_id')],
            ])->toArray();
            $gradeMap     = array_column($findStaff, 'job_title_grade_v2', 'staff_info_id');
            $findAudit    = HrProbationAuditModel::find([
                'conditions' => 'probation_id in ({ids:array})',
                'bind'       => ['ids' => $probationIds],
            ])->toArray();
            $handleData   = [];
            foreach ($findAudit as $item) {
                //每个 probation 第N次  的最后一次
                $scoreMap                                              = (array)json_decode($item['score'], true);
                $handleData[$item['probation_id']][$item['cur_level']] = [
                    'audit_status' => $item['audit_status'],//1待处理，2已处理，3超时
                    'status'       => $item['status'],      //1试用期，2已通过，3未通过，4已转正
                    'audit_level'  => $item['audit_level'], //1上级，2上上级 3 上上上级
                    'version'      => $item['version'],     //有版本的代表 有上上上级审批 my除外
                    'score'        => $scoreMap['score'] ?? 0,
                    'second_score' => $scoreMap['second_score'] ?? 0,
                ];
            }
            foreach ($find as $hrProbation) {
                //当前处于第一次评估
                if ($hrProbation['cur_level'] == HrProbationModel::CUR_LEVEL_FIRST
                    && !empty($handleData[$hrProbation['id']][HrProbationModel::CUR_LEVEL_FIRST])) {
                    $audit = $handleData[$hrProbation['id']][HrProbationModel::CUR_LEVEL_FIRST];
                    //存在上上上级审批
                    $endGrade = $this->getEndGrade($audit['version'], $gradeMap[$find['staff_info_id']]);
                    //是最后一级审批了
                    if ($audit['audit_level'] >= $endGrade) {
                        $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_WAIT;
                        if ($audit['audit_status'] == 1) {
                            $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
                        }
                        if ($audit['audit_status'] == 2) {
                            $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                        }
                        if ($audit['audit_status'] == 3) {
                            $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT;
                        }
                        $firstStatus = HrProbationModel::FIRST_STATUS_NOT_PASS;
                        if ($audit['score'] >= 6 && $audit['audit_status'] == 2) {
                            $firstStatus = HrProbationModel::FIRST_STATUS_PASS;
                        }
                        $updateData = [
                            'first_audit_status' => $firstAuditStatus,
                            'first_status'       => $firstStatus,
                        ];
                    } else {
                        //超时
                        if ($audit['audit_status'] == 3) {
                            $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT;
                        } else {
                            $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
                        }
                        $updateData = [
                            'first_audit_status' => $firstAuditStatus,
                            'first_status'       => HrProbationModel::FIRST_STATUS_NOT_PASS,
                        ];
                    }
                    $this->db_backyard->updateAsDict($hrProbationTable, $updateData,
                        [
                            'conditions' => 'id = ?',
                            'bind'       => [$hrProbation['id']],
                            'bindTypes'  => [\PDO::PARAM_INT],
                        ]);
                    $updateData = [];
                    continue;
                }
                //当前处于第二次评估 (被系统处理过is_system>0 并且会cur_level=2第二次审核)
                if (($hrProbation['cur_level'] == HrProbationModel::CUR_LEVEL_SECOND || $hrProbation['is_system'] > 0)
                    && !empty($handleData[$hrProbation['id']][HrProbationModel::CUR_LEVEL_SECOND])) {
                    if (!empty($handleData[$hrProbation['id']][HrProbationModel::CUR_LEVEL_FIRST])) {
                        $firstAudit       = $handleData[$hrProbation['id']][HrProbationModel::CUR_LEVEL_FIRST];
                        $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_DONE;
                        //超时
                        if ($firstAudit['audit_status'] == 3) {
                            $firstAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT;
                        }
                        $firstStatus = HrProbationModel::FIRST_STATUS_NOT_PASS;
                        if ($firstAudit['score'] >= 6 && $firstAudit['audit_status'] == 2) {
                            $firstStatus = HrProbationModel::FIRST_STATUS_PASS;
                        }
                        $updateData = [
                            'first_audit_status' => $firstAuditStatus,
                            'first_status'       => $firstStatus,
                        ];
                    }
                    $secondAudit = $handleData[$hrProbation['id']][HrProbationModel::CUR_LEVEL_SECOND];
                    //存在上上上级审批
                    $endGrade = $this->getEndGrade($secondAudit['version'], $gradeMap[$find['staff_info_id']]);
                    //是最后一级审批了
                    if ($secondAudit['audit_level'] >= $endGrade) {
                        $secondAuditStatus = HrProbationModel::SECOND_AUDIT_STATUS_WAIT;
                        if ($secondAudit['audit_status'] == 1) {
                            $secondAuditStatus = HrProbationModel::SECOND_AUDIT_STATUS_RUN;
                        }
                        if ($secondAudit['audit_status'] == 2) {
                            $secondAuditStatus = HrProbationModel::SECOND_AUDIT_STATUS_DONE;
                        }
                        if ($secondAudit['audit_status'] == 3) {
                            $secondAuditStatus = HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT;
                        }
                        $secondStatus = HrProbationModel::SECOND_STATUS_NOT_PASS;
                        if ($secondAudit['second_score'] >= 6 && $secondAudit['audit_status'] == 2) {
                            $secondStatus = HrProbationModel::SECOND_STATUS_PASS;
                        }
                        $updateData['second_audit_status'] = $secondAuditStatus;
                        $updateData['second_status']       = $secondStatus;
                    } else {
                        if ($secondAudit['audit_status'] == 3) {
                            $secondAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT;
                        } else {
                            $secondAuditStatus = HrProbationModel::FIRST_AUDIT_STATUS_RUN;
                        }
                        $updateData['second_audit_status'] = $secondAuditStatus;
                        $updateData['second_status']       = HrProbationModel::FIRST_STATUS_NOT_PASS;
                    }
                    $this->db_backyard->updateAsDict($hrProbationTable, $updateData,
                        [
                            'conditions' => 'id = ?',
                            'bind'       => [$hrProbation['id']],
                            'bindTypes'  => [\PDO::PARAM_INT],
                        ]);
                    $updateData = [];
                }
            }
            $log = 'handleHistoryStatus  page ' . $page . ' handle count ' . count($probationIds);
            echo $log . PHP_EOL;
            $this->logger->info($log);
            ++$page;
        }
    }
    public function getEndGrade($version,$jobGrade)
    {
        if (isCountry('my') || $version == 0) {
            return 2;
        }
        if ($version > 0 && $jobGrade > $this->job_grade) {
            return 3;
        }
        return 2;
    }

    /**
     * 变更评估中处理人 （离职 更换上级）
     * @param $type -- first 代表老表 second 代表新表
     * @return void
     */
    public function changeExaminationHandleStaff($type)
    {
        $page                  = 1;
        $pageSize              = 2000;
        $hrProbationAuditTable = $type == 'first' ? 'hr_probation_audit' : 'hr_probation_audit_contract_worker';
        $conditions            = [
            'conditions' => ' status != :status: and  (first_audit_status in ({first_audit_status:array}) or  second_audit_status in ({second_audit_status:array}))',
            'bind'       => [
                'status'              => HrProbationModel::STATUS_CORRECTED,
                'first_audit_status'  => [HrProbationModel::FIRST_AUDIT_STATUS_RUN],
                //, HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT
                'second_audit_status' => [
                    HrProbationModel::SECOND_AUDIT_STATUS_RUN,
                    HrProbationModel::SECOND_AUDIT_STATUS_WAIT,
                ],
                //, HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT
            ],
        ];
        while (true) {
            $conditions['offset'] = ($page - 1) * $pageSize;
            $conditions['limit']  = $pageSize;
            if ($type == 'first') {
                $find = HrProbationModel::find($conditions)->toArray();
            } else {
                $find = HrProbationContractWorkerModel::find($conditions)->toArray();
            }
            if (empty($find)) {
                break;
            }
            $probationIds    = array_column($find, 'id');
            $staffIds        = array_column($find, 'staff_info_id');
            $managerMap      = $this->searchManagers($staffIds);
            $auditConditions = [
                'conditions' => 'probation_id in ({ids:array}) and audit_status = 1 and audit_level<=2',
                //1待处理 ;1上级，2上上级 //audit_status in (1,3)
                'bind'       => ['ids' => $probationIds],
            ];
            if ($type == 'first') {
                $findAudit = HrProbationAuditModel::find($auditConditions)->toArray();
            } else {
                $findAudit = HrProbationAuditContractWorkerModel::find($auditConditions)->toArray();
            }

            $handleData = [];
            foreach ($findAudit as $item) {
                //每个 probation 第N次  的最后一次
                $handleData[$item['probation_id']][$item['cur_level']] = [
                    'id'            => $item['id'],
                    'staff_info_id' => $item['staff_info_id'],
                    'audit_id'      => $item['audit_id'],   //审批人
                    'audit_level'   => $item['audit_level'],
                ];
            }

            $message_audit_info = [];

            foreach ($handleData as $auditItems) {
                foreach ($auditItems as $curLevel => $item) {
                    if (!empty($managerMap[$item['staff_info_id']][$item['audit_level']])
                        && $item['audit_id'] != $managerMap[$item['staff_info_id']][$item['audit_level']]) {
                        $updateData['audit_id'] = $managerMap[$item['staff_info_id']][$item['audit_level']];

                        $message_audit_info[] = $updateData;
                        $this->db_backyard->updateAsDict($hrProbationAuditTable, $updateData,
                            [
                                'conditions' => 'id = ?',
                                'bind'       => [$item['id']],
                                'bindTypes'  => [\PDO::PARAM_INT],
                            ]);
                        $log = 'changeExaminationHandleStaff  hr_probation_audit id' . $item['id'] . ' staff id ' . $item['staff_info_id'] . ' curLevel: ' . $curLevel . ' old manager:' . $item['audit_id'] . ' new manager ' . $managerMap[$item['staff_info_id']][$item['audit_level']];
                        echo $log . PHP_EOL;
                        $this->logger->info($log);
                    }
                }
            }
            if (!empty($message_audit_info)) {
                $server = new HrProbationContractService();
                $server->sendConfirmationEvaluationPush($message_audit_info);
            }

            ++$page;
        }
    }

    /**
     * 查找上级 上上级
     * @param $staffIds
     * @return array
     */
    public function searchManagers($staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $firstLeveFind    = HrStaffInfoModel::find([
            'columns'    => 'manger,staff_info_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();
        $firstLeveFindMap = array_column($firstLeveFind, 'manger', 'staff_info_id');
        $oneLeveIds       = array_column($firstLeveFind, 'manger');
        $secondLeveMap    = [];
        if ($oneLeveIds) {
            $secondLeveFind = HrStaffInfoModel::find([
                'columns'    => 'manger,staff_info_id',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $oneLeveIds],
            ])->toArray();
            $secondLeveMap  = array_column($secondLeveFind, 'manger', 'staff_info_id');
        }
        $managerMap = [];
        foreach ($staffIds as $staffId) {
            if (!empty($firstLeveFindMap[$staffId])) {
                //上级
                $managerMap[$staffId][1] = $firstLeveFindMap[$staffId];
                if (!empty($secondLeveMap[$firstLeveFindMap[$staffId]])) {
                    //上上级
                    $managerMap[$staffId][2] = $secondLeveMap[$firstLeveFindMap[$staffId]];
                }
            }
        }
        return $managerMap;
    }

    /**
     * 检测转正评估数据异常
     * @param bool $isContractFive -- 是否为合同工五个月 目前只有 PH 有
     * @return true
     */
    public function checkData(bool $isContractFive = false)
    {
        $now_date = date('Y-m-d');
        $hrProbationAuditTable = $isContractFive ? 'hr_probation_audit_contract_worker' : 'hr_probation_audit';
        $hrProbationTable = $isContractFive ? 'hr_probation_contract_worker' : 'hr_probation';
        $messTip = $isContractFive ? '合同工五个月' : '';
        $staff_id_white_list = SettingEnvModel::get_val('probation_check_data_staff_id_white_list');
        $sql = "SELECT id,probation_id,staff_info_id,cur_level,audit_status FROM $hrProbationAuditTable WHERE probation_id IN (SELECT p.id FROM $hrProbationTable AS p LEFT JOIN hr_staff_info AS s ON s.`staff_info_id`=p.`staff_info_id` WHERE p.formal_at<='{$now_date}' AND p.`status`=1 AND s.`is_sub_staff`=0 AND `formal_staff_id` IS NULL AND s.`state` !=2 and s.hire_type not in (13,14))";
        if ($staff_id_white_list){
            $sql .= " and staff_info_id not in ({$staff_id_white_list})";
        }
        $data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $new_data = [];
        foreach ($data as $v){
            $new_data[$v['staff_info_id']][$v['cur_level']][] = $v;
        }
        $error_staff_id = [];
        $first_evaluate_start_error = [];
        $second_evaluate_start_error = [];
        $formal_at_error = [];
        $base_data_error = [];
        $base_data_score_error = [];
        foreach ($new_data as $v){
            //第二阶段
            if (isset($v[2])){
                $v[2] = array_values(array_reverse($v[2], true));
                if ($v[2][0]['audit_status'] == 2){
                    $error_staff_id[] = $v[2][0]['staff_info_id'];
                }
            }elseif (isset($v[1])){
                //第一阶段
                $v[1] = array_values(array_reverse($v[1], true));
                if ($v[1][0]['audit_status'] == 2){
                    $error_staff_id[] = $v[1][0]['staff_info_id'];
                }
            }
        }
        //不是合同工五个月
        if (!$isContractFive) {
            $sql_1 = 'SELECT id,probation_id,staff_info_id,cur_level,version,score FROM hr_probation_audit WHERE probation_id IN (SELECT p.id FROM hr_probation AS p LEFT JOIN hr_staff_info AS s ON s.`staff_info_id`=p.`staff_info_id` WHERE p.`status`=1 AND s.`is_sub_staff`=0 AND s.`state` !=2 and p.probation_channel_type=2)';
            if ($staff_id_white_list){
                $sql_1 .= " and staff_info_id not in ({$staff_id_white_list})";
            }
            $data_1 = $this->getDI()->get('db_rby')->query($sql_1)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            foreach ($data_1 as $v){
                if (empty($v['version'])){
                    $base_data_score_error[] = $v['staff_info_id'];
                }
                if (empty($v['score'])){
                    $base_data_score_error[] = $v['staff_info_id'];
                }else{
                    $data = json_decode($v['score'],true);
                    if (
                        !isset($data['duty_info']) ||
                        !isset($data['1']) ||
                        !isset($data['2']) ||
                        !isset($data['1']['target_score']) ||
                        !isset($data['1']['count_score']) ||
                        !isset($data['1']['count_score_text']) ||
                        !isset($data['2']['target_score']) ||
                        !isset($data['2']['count_score']) ||
                        !isset($data['2']['count_score_text'])
                    ){
                        $base_data_score_error[] = $v['staff_info_id'];
                    }
                }
            }
        }
        $sql_2 = "SELECT p.* FROM $hrProbationTable AS p LEFT JOIN hr_staff_info AS s ON s.`staff_info_id`=p.`staff_info_id` WHERE p.`status`!=4 AND s.`is_sub_staff`=0 AND s.`state` !=2";
        if ($staff_id_white_list){
            $sql_2 .= " and p.staff_info_id not in ({$staff_id_white_list})";
        }
        $data_2 = $this->getDI()->get('db_rby')->query($sql_2)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $probation_id_arr = array_column($data_2,'id');
        $probation_id_arr_str = implode(',',$probation_id_arr);
        $sql_3 = "SELECT * from $hrProbationAuditTable where probation_id in (".$probation_id_arr_str.')';
        $data_3 = $this->getDI()->get('db_rby')->query($sql_3)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $probation_audit_all = [];
        $dateGetFunc = function ($date) {
            $datePermanentArr = explode('-', $date);
            if ($datePermanentArr[2] == '01' || $datePermanentArr[2] == '16') {
                return $date;
            }
            if (($datePermanentArr[2] > 1) && ($datePermanentArr[2] < 16)) {
                return $datePermanentArr[0] . '-' . $datePermanentArr[1] . '-' . '16';
            }
            return date('Y-m-d',
                strtotime(date('Y-m-d', strtotime($date)) . ' first day of next month'));
        };
        foreach ($data_3 as $v){
            $probation_audit_all[$v['staff_info_id']][$v['cur_level']] = $v['staff_info_id'];
        }
        foreach ($data_2 as $v){
            if (
                empty($v['first_evaluate_start']) ||
                empty($v['first_evaluate_end']) ||
                empty($v['formal_at']) ||
                (in_array($v['status'],[2,3]) && $v['first_audit_status'] ==1)
            ){
                if (!isCountry('MY')) {
                    $base_data_error[] = $v['staff_info_id'];
                }
            }
            if ((empty($v['second_evaluate_start']) ||
                empty($v['second_evaluate_end'])) && !isCountry('MY') && $v['probation_channel_type'] !=3) {
                $base_data_error[] = $v['staff_info_id'];
            }
            if (!empty($v['first_evaluate_start']) && strtotime($v['first_evaluate_start']) <= time() && empty($probation_audit_all[$v['staff_info_id']][1])){
                $first_evaluate_start_error[] = $v['staff_info_id'];
            }
            if (!empty($v['second_evaluate_start']) && strtotime($v['second_evaluate_start']) <= time() && empty($probation_audit_all[$v['staff_info_id']][2])){
                $second_evaluate_start_error[] = $v['staff_info_id'];
            }
            if (!empty($v['formal_at']) && strtotime($v['formal_at']) <= time() && $v['status'] == 2 and !in_array($v['probation_channel_type'],[3,4])){
                $formal_at_error[] = $v['staff_info_id'];
            }
            if (!empty($v['formal_at']) && in_array($v['probation_channel_type'],[3,4])) {
                if ($v['is_active'] == 0 &&  strtotime($v['formal_at']) <= time() && $v['status'] == 2) {
                    $formal_at_error[] = $v['staff_info_id'];
                }
                if  ($v['is_active'] == 1 && $v['status'] == 2) {
                    $judgeTime =$dateGetFunc($v['updated_at']);
                    if(strtotime($judgeTime) <= time()) {
                        $formal_at_error[] = $v['staff_info_id'];
                    }
                }
            }
        }

        if (!empty($error_staff_id)){
            $error_staff_id = array_values(array_unique($error_staff_id));
            $this->logger->alert($messTip.'转正评估数据异常 staff_id:'.implode(',',$error_staff_id));
        }
        if (!empty($second_evaluate_start_error)){
            $second_evaluate_start_error = array_values(array_unique($second_evaluate_start_error));
            $this->logger->alert($messTip.'转正评估数据异常二阶段应发起但未发起 staff_id:'.implode(',',$second_evaluate_start_error));
        }
        if (!empty($first_evaluate_start_error)){
            $first_evaluate_start_error = array_values(array_unique($first_evaluate_start_error));
            $this->logger->alert($messTip.'转正评估数据异常一阶段应发起但未发起 staff_id:'.implode(',',$first_evaluate_start_error));
        }
        if (!empty($base_data_error)){
            $base_data_error = array_values(array_unique($base_data_error));
            $this->logger->alert($messTip.'转正评估数据异常入职固化数据有为空的 staff_id:'.implode(',',$base_data_error));
        }
        if (!empty($base_data_score_error)){
            $base_data_score_error = array_values(array_unique($base_data_score_error));
            $this->logger->alert($messTip.'转正评估数据异常非一线数据错误 staff_id:'.implode(',',$base_data_score_error));
        }
        if (!empty($formal_at_error)){
            $formal_at_error = array_values(array_unique($formal_at_error));
            $this->logger->alert($messTip.'转正评估数据异常应转正但未转正 staff_id:'.implode(',',$formal_at_error));
        }

       if (!$isContractFive) {
           $sql_4  = "SELECT * from hr_probation where first_audit_status = 3 and probation_channel_type = 2 and (first_stage_done_msg_id is null or first_stage_done_msg_id = '')";
           $data_4 = $this->getDI()->get('db_rby')->query($sql_4)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
           if (!empty($data_4)){
               $this->logger->alert('转正评估数据异常 1阶段完成消息未发送 staff_id:'.implode(',', array_column($data_4, 'staff_info_id')));
           }
           $sql_5  = "SELECT * from hr_probation where second_audit_status = 3 and probation_channel_type = 2 and (second_stage_done_msg_id is null or second_stage_done_msg_id = '')";
           $data_5 = $this->getDI()->get('db_rby')->query($sql_5)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
           if (!empty($data_5)){
               $this->logger->alert('转正评估数据异常 2阶段完成消息未发送 staff_id:'.implode(',', array_column($data_5, 'staff_info_id')));
           }
       }
        return true;
    }

    /**
     * @param $staffInfoIds
     * @return array
     */
    public function getNonFrontLineProbationStaff($staffInfoIds): array
    {
        if (!empty($staffInfoIds)){
            $probationData       = HrProbationModel::find([
                'columns'    => ['staff_info_id,probation_channel_type'],
                'conditions' => "staff_info_id in ({staff_ids:array})",
                'bind'       => [
                    'staff_ids' => $staffInfoIds,
                ],
            ])->toArray();
            $probationData       = array_column($probationData, 'probation_channel_type', 'staff_info_id');
        }else{
            $probationData = [];
        }
        $probation_staff = (new \App\Services\SettingEnvService())->getSetVal('probation_staff', ',');
        return ['probation_data' => $probationData, 'probation_staff' => $probation_staff];
    }

    /**
     * 详情 - 非一线
     * @param $staffInfoId
     * @return array
     * @throws ValidationException
     */
    public function detailNonFrontLine($staffInfoId): array
    {
        if (empty($staffInfoId)) {
            throw new ValidationException(static::$t->_('param_error'));
        }
        $apiClient = new ApiClient('by', '', 'probation_detail_non_front_line', self::$language);
        $apiClient->withParam([
            'staff_info_id'       => $staffInfoId,
        ]);
        $res = $apiClient->execute();
        if (isset($res['error'])) {
            throw  new ValidationException($res['error']);
        }
        return $res;
    }

    /**
     * @throws BusinessException
     * @throws ValidationException
     */
    public function scoreSubmitNonFrontLine($params): bool
    {
        if (empty($params['staff_info_id']) || empty($params['stage_score_data']) || empty($params['operator_id'])) {
            throw new ValidationException(static::$t->_('param_error'));
        }
        $apiClient = new ApiClient('by', '', 'probation_score_submit_non_front_line', self::$language);
        $apiClient->withParam($params);
        $res = $apiClient->execute();
        if (isset($res['error'])) {
            throw  new ValidationException($res['error']);
        }
        return $res['code'] == 1 ? 1 : 0;
    }
    
    /**
     * @param $targetDetail
     * @return array|string
     */
    public function formatTargetInfoJson($targetDetail)
    {
        $data = $targetDetail['target_info'] ?? '';
        $duty_info = $targetDetail['duty_info'] ?? '';
        $probation_target_detail_id = $targetDetail['id'] ?? 0;
        $act_version = empty($targetDetail['act_version']) ? '0' : $targetDetail['act_version'];
        if (empty($data)) {
            return '';
        }
        $data = json_decode($data, true);
        foreach ($data as &$v) {
            $v['first_score']       = '';
            $v['first_score_text']  = '';
            $v['second_score']      = '';
            $v['second_score_text'] = '';
            $v['third_score']       = '';
            $v['third_score_text']  = '';
        }
        $return_data['1']['target_score']     = $data;
        $return_data['1']['count_score']      = '';
        $return_data['1']['count_score_text'] = '';
        $return_data['1']['good_job']         = '';
        $return_data['1']['no_good_job']      = '';
        $return_data['1']['action_plan']      = '';
        $return_data['2']['target_score']     = $data;
        $return_data['2']['count_score']      = '';
        $return_data['2']['count_score_text'] = '';
        $return_data['2']['good_job']         = '';
        $return_data['2']['no_good_job']      = '';
        $return_data['2']['action_plan']      = '';
        $return_data['duty_info']             = $duty_info ?? '';
        $return_data['act_version']             = $act_version;
        $return_data['probation_target_detail_id'] = $probation_target_detail_id;
        $return_data['act_values_data'] = $this->getProbationActValuesJson(['act_version'=>$act_version]);
        return $return_data;
    }

    public function refactoringStageScoreData($data = [])
    {
        if (!empty($data['act_values_data'])) {
            foreach ($data['act_values_data'] as $k => $v) {
                foreach ($v as $k1 => $v1) {
                    foreach ($v1['behavior_list'] as $k2 => $v2) {
                        $data['act_values_data'][$k][$k1]['concept_name']                   = static::$t->_(HrProbationActValuesModel::$concept_type_list[$v2['concept_type']]);
                        $data['act_values_data'][$k][$k1]['behavior_list'][$k2]['behavior_name'] = static::$t->_('probation_behavior_type_' . $v2['behavior_type']);
                    }
                    $data['act_values_data'][$k][$k1]['final_score'] = !empty($v1['third_score_text']) ? $v1['third_score_text'] : (!empty($v1['second_score_text']) ? $v1['second_score_text'] : (!empty($v1['first_score_text']) ? $v1['first_score_text'] : ''));
                }
            }
        }else{
            $data['act_values_data'] = [];
            $data['act_version'] = 0;
        }
        return $data;
    }

    /**
     * @param $param
     * @return array
     */
    public function getProbationActValuesJson($param): array
    {
        $version = $param['act_version'] ?? 0;
        $score = $param['score'] ?? '';
        if (empty($version)){
            return [];
        }
        $act_values = HrProbationActValuesModel::find([
            'conditions' => "version = :version: and is_deleted = 0",
            'bind'       => [
                'version' => $version,
            ],
            'columns'    => 'id,concept_type,behavior_type,grade',
            'order' => 'id ASC',
        ])->toArray();
        if (empty($act_values)){
            return [];
        }
        $return_data = [];
        foreach ($act_values as $v){
            if (!empty($score)){
                $score_1 = json_decode($score, true);
                $return_data['1'] = $score_1['act_values_data']['1'] ?? [];
            }else{
                $return_data['1'][$v['concept_type']]['behavior_list'][] = $v;
                $return_data['1'][$v['concept_type']]['first_score'] = '';
                $return_data['1'][$v['concept_type']]['first_score_text'] = '';
                $return_data['1'][$v['concept_type']]['second_score'] = '';
                $return_data['1'][$v['concept_type']]['second_score_text'] = '';
                $return_data['1'][$v['concept_type']]['third_score'] = '';
                $return_data['1'][$v['concept_type']]['third_score_text'] = '';
            }
            $return_data['2'][$v['concept_type']]['behavior_list'][] = $v;
            $return_data['2'][$v['concept_type']]['first_score'] = '';
            $return_data['2'][$v['concept_type']]['first_score_text'] = '';
            $return_data['2'][$v['concept_type']]['second_score'] = '';
            $return_data['2'][$v['concept_type']]['second_score_text'] = '';
            $return_data['2'][$v['concept_type']]['third_score'] = '';
            $return_data['2'][$v['concept_type']]['third_score_text'] = '';
        }
        return $return_data;
    }
    
    /**
     * @param $param
     * @return true
     * @throws ValidationException
     */
    public function notPassNotice($param): bool
    {
        if (empty($param['staff_info_id'])) {
            throw new ValidationException(static::$t->_('param_error'));
        }
        $staff_data = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $param['staff_info_id'],
            ],
        ]);
        if (empty($staff_data)) {
            throw new ValidationException(static::$t->_('data_error'));
        }
        $probation_data = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $param['staff_info_id'],
            ],
        ]);
        $probation_data = !empty($probation_data) ? $probation_data->toArray() : [];
        if (empty($probation_data) || $probation_data['status'] != HrProbationModel::STATUS_NOT_PASS) {
            throw new ValidationException(static::$t->_('data_error'));
        }

        $staff_data  = $staff_data->toArray();
        $lang = (new StaffService())->getAcceptLanguage($param['staff_info_id']);
        $t    = self::getTranslation($lang);
        $msg_content = $t->_("hr_probation_field_msg_to_staff_not_pass", ['name' => $staff_data['name']]);
        $msg_title = $t->_("hr_probation_field_msg_to_staff_title");
        $msg_data = [
            "id"                 => time() . $param['staff_info_id'] . rand(1000000, 9999999),
            "staff_users"        => [$param['staff_info_id']],
            "message_title"      => $msg_title,
            "message_content"    => addslashes("<div style='font-size: 30px'>" . $msg_content . "</div>"),
            "staff_info_ids_str" => $param['staff_info_id'],
            "category"           => '-1',
        ];
        $result      = (new MessagesService())->add_kit_message($msg_data);
        if ($result[0] != 'ok') {
            throw new ValidationException(static::$t->_('message_send_error'));
        }
        $this->getDI()->get('db_backyard')->updateAsDict('hr_probation',
            ['result_notification' => HrProbationModel::RESULT_NOTIFICATION_YES], 'id = ' . $probation_data['id']);
        return true;
    }
    
    public function compensateSendStageDoneMessage($staffInfoId)
    {
        $sql_1  = "SELECT * from hr_probation where first_audit_status = 3 and probation_channel_type = 2 and (first_stage_done_msg_id is null or first_stage_done_msg_id = '') and staff_info_id = ".$staffInfoId;
        $conn_1 = $this->getDI()->get('db_backyard');
        $data_1 = $conn_1->fetchAll($sql_1, \Phalcon\Db::FETCH_ASSOC);
        $local['locale'] = 'zh';
        foreach ($data_1 as $v){
            try {
                $this->probationStageDoneMessage($local, ['staff_info_id'=>$v['staff_info_id'],'customize_cur_level'=>1]);
            } catch (\Exception|ValidationException $e) {
                $this->logger->error([
                    'function' => __FUNCTION__,
                    'staff_info_id' => $v['staff_info_id'],
                    'message' => $e->getMessage(),
                ]);
                continue;
            }
        }
        $sql_2  = "SELECT * from hr_probation where second_audit_status = 3 and probation_channel_type = 2 and (second_stage_done_msg_id is null or second_stage_done_msg_id = '') and staff_info_id = ".$staffInfoId;
        $conn_2 = $this->getDI()->get('db_backyard');
        $data_2 = $conn_2->fetchAll($sql_2, \Phalcon\Db::FETCH_ASSOC);
        foreach ($data_2 as $v){
            try {
                $this->probationStageDoneMessage($local, ['staff_info_id'=>$v['staff_info_id'],'customize_cur_level'=>2]);
            } catch (\Exception|ValidationException $e) {
                $this->logger->error([
                    'function' => __FUNCTION__,
                    'staff_info_id' => $v['staff_info_id'],
                    'message' => $e->getMessage(),
                ]);
                continue;
            }
        }
        return true;
    }

    /**
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function downloadAcknowledgement($param): array
    {
        $staff_info_id = $param['staff_info_id'] ?? '';
        if (empty($staff_info_id)){
            throw new ValidationException(static::$t->_('param_error'));
        }
        $probation_data = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
            ],
        ]);
        $probation_data = !empty($probation_data) ? $probation_data->toArray() : [];
        if (empty($probation_data['pdf_path'])){
            throw new ValidationException(static::$t->_('probation_err_2'));
        }
        return [
            'pdf_url' => $probation_data['pdf_path'],
        ];
    }

    /**
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function downloadConfirmationLetter($param): array
    {
        $staff_info_id = $param['staff_info_id'] ?? '';
        if (empty($staff_info_id)){
            throw new ValidationException(static::$t->_('param_error'));
        }
        $message_pdf_data = MessagePdf::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and module_category = :module_category:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'module_category' => MessagePdf::MODULE_CATEGORY_CONFIRMATION_LETTER,
            ],
        ]);
        $message_pdf_data = !empty($message_pdf_data) ? $message_pdf_data->toArray() : [];
        if (empty($message_pdf_data['pdf_url'])){
            throw new ValidationException(static::$t->_('probation_err_2'));
        }
        return [
            'pdf_url' => $message_pdf_data['pdf_url'],
        ];
    }

    /**
     * @param $param
     * @return array
     * @throws ValidationException
     */
    public function downloadNonConfirmationLetter($param): array
    {
        $staff_info_id = $param['staff_info_id'] ?? '';
        if (empty($staff_info_id)){
            throw new ValidationException(static::$t->_('param_error'));
        }
        $resignInfoObj = HrProbationResignModel::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_info_id: AND status = :status: AND is_deleted = :is_deleted:',
                'bind'       => [
                    'staff_info_id'         => $staff_info_id,
                    'is_deleted' => Enums\GlobalEnums::NO_DELETED,
                    'status'     => HrProbationResignModel::STATUS_SUCCESS,
                ],
            ]
        );
        $resignInfoObj = !empty($resignInfoObj) ? $resignInfoObj->toArray() : [];
        if (empty($resignInfoObj['notice_url'])){
            throw new ValidationException(static::$t->_('probation_err_2'));
        }
        return [
            'pdf_url' => $resignInfoObj['notice_url'],
        ];
    }
}