<?php

namespace App\Services;

use App\Library\BaseService;
use App\Models\backyard\HrJobDepartmentRelationModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\HrJobTitleRoleModel;
use App\Models\backyard\HrStaffInfoReadModel;

/**
 *
 */
class HrJobTitleService extends BaseService
{
    //根据网点名称模糊查询
    public function searchJobTitleData(array $columns, array $condition, array $bind)
    {
        $query_info['columns'] = '*';
        if ($columns) {
            $query_info['columns'] = implode(' , ', $columns);
        }
        if ($condition) {
            $query_info['conditions'] = implode(' AND ', $condition);
        }else{
            $query_info['limit']    = 30;
        }
        if ($bind) {
            $query_info['bind'] = $bind;
        }

        return HrJobTitleModel::find($query_info)->toArray();
    }

    //职位列表
    public function getJobTitleList($status = 1)
    {
        $condition['columns'] = 'id, job_name,status';
        if ($status) {
            $condition['conditions'] = 'status=' . $status;
        }
        return HrJobTitleModel::find($condition)->toArray();
    }

    //职位列表
    public function getJobTitleListByDepartmentId($departmentId) {
        if($departmentId <= 0){
            return [];
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('relation.job_id as job_id, hr_job_title.job_name as job_name')
            ->addFrom(HrJobDepartmentRelationModel::class, 'relation')
            ->leftJoin(HrJobTitleModel::class, 'relation.job_id = hr_job_title.id', 'hr_job_title')
            ->where('hr_job_title.status = 1')
            ->andWhere('relation.department_id = :depeartment_id:', ['depeartment_id' => $departmentId]);
        return $builder->getQuery()->execute()->toArray();
    }

    public function getJobTitleIdMap(){
        $data_list = $this->getJobTitleList();
        $ret = [];
        foreach ($data_list as $item){
            $job_name = $item['job_name'];
            $job_name_lower = strtolower(trim($job_name));
            $ret[$job_name_lower] = $item['id'];
        }
        return $ret;
    }

    //根据职位名称模糊查询
    public function searchJobTitleList($search_name, $columns = 'id, job_name', $filter = null)
    {
        if (empty($search_name)) {
            return [];
        }
        $condition      = 'status = :status: and job_name like :name:';
        $bind['status'] = SysStoreModel::STATE_1;
        $bind['name']   = '%' . $search_name . '%';

        if (!empty($filter)) {
            $condition         .= ' and id not in({filter_id:array})';
            $bind['filter_id'] = is_array($filter) ? $filter: [$filter];
        }
        $realJobTitleModel = HrJobTitleModel::findFirst([
            'columns'    => $columns,
            'conditions' => $condition . ' and job_name = :real_job_name:',
            'bind'       => array_merge($bind, ['real_job_name' => $search_name]),
        ]);
        $realJobTitle      = [];
        if ($realJobTitleModel) {
            $realJobTitle[]      = $realJobTitleModel->toArray();
            $condition           .= 'and id != :real_job_id:';
            $bind['real_job_id'] = $realJobTitleModel->id;
        }
        $list =  HrJobTitleModel::find([
            'columns'    => $columns,
            'conditions' => $condition,
            'bind'       => $bind,
            'limit'      => 100,
        ])->toArray();
        return array_merge($realJobTitle,$list);
    }

    /**
     * 获取职位名称
     * @param $job_title_id
     * @param string $columns
     * @return array
     */
    public function getJobTitleByIds($job_title_id, $columns = 'id, job_name'): array
    {
        if (!is_array($job_title_id)) {
            $job_title_id = [$job_title_id];
        }
        $conditions = 'id in({job_title_id:array})';
        return HrJobTitleModel::find([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => [
                'job_title_id' => $job_title_id,
            ],
        ])->toArray();
    }

    //根据职位id获取职位详情
    public function getJobTitleDetail($job_title_id,$is_all = false) {
        if(empty($job_title_id)) {
            return [];
        }
        $condition = ' id = :id:';
        $bind = ['id'=>$job_title_id];

        if(!$is_all){
            $condition .= ' and status = 1';
        }
        $detail = HrJobTitleModel::findFirst([
            'conditions' => $condition,
            'bind' => $bind
        ]);

        return empty($detail) ? [] : $detail->toArray();
    }

    /**
     * 获取职位列表
     */
    public function getJobTitleMapByIds($jobTitleIds){
        if (empty($jobTitleIds)) {
            return [];
        }
        $data = HrJobTitleModel::find(['conditions' => 'id IN ({ids:array})', 'bind' => ['ids' => $jobTitleIds]])->toArray();
        $res = array_column($data, 'job_name', 'id');
        return $res;
    }

    public static function getAllJobTitleByIds ($ids) {
        if (!is_array($ids) || empty($ids)) {
            return [];
        }

        $result = \App\Models\backyard\HrJobTitleModel::find(
            [
                'conditions' => 'status IN ({status:array}) AND id IN ({ids:array})',
                'bind' => [
                    'status'    => \App\Models\backyard\HrJobTitleModel::ALL_STATUS,
                    'ids'       => array_values(array_unique($ids))
                ],
                'columns' => ['id','job_name', 'status']
            ]
        )->toArray();
        return array_column($result,null, 'id');
    }

    /**
     * 获取职位角色关联关系
     * @param $roleId
     * @return mixed
     */
    public function getRoleJobDeptRelateInfo($roleId)
    {
        return HrJobTitleRoleModel::find([
            'columns'    => 'sys_depeartment_id, job_title_id, role_id',
            'conditions' => 'role_id = ?1',
            'bind'       => [
                1 => $roleId,
            ],
        ])->toArray();
    }

    /**
     * @description 获取部门职位角色关系
     * @param $department_id
     * @param $job_title_id
     * @return array
     */
    public function getRolesBySpecDepartmentAndJobTitle($department_id, $job_title_id): array
    {
        $rolesInfo = HrJobTitleRoleModel::find([
            'columns'    => 'role_id',
            'conditions' => 'sys_depeartment_id = :department_id: and job_title_id = :job_title_id:',
            'bind'       => [
                'department_id' => $department_id,
                'job_title_id'  => $job_title_id
            ],
        ])->toArray();
        if (empty($rolesInfo)) {
            return [];
        }
        return array_column($rolesInfo, 'role_id');
    }


    /**
     * 获取根据部门职位获取关联信息
     * @param $params
     * @return mixed
     */
    public function getRoleJobDeptRelate($params)
    {
        $res = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: AND job_id = :job_title:',
            'bind'       => [
                'department_id' => $params['department_id'],
                'job_title'     => $params['job_title'],
            ],
            'columns'    => ['working_day_rest_type', 'job_level'],
        ]);
        return !empty($res) ? $res->toArray() : [];
    }


    //职位列表
    public function getJobTitleListByStaffInfo($ids) {
        if(empty($ids)){
            return [];
        }
        $builder = $this->modelsManager->createBuilder()
            ->columns('hr_staff_info.job_title, hr_job_title.job_name,hr_staff_info.staff_info_id,hr_staff_info.name')
            ->addFrom(HrStaffInfoReadModel::class, 'hr_staff_info')
            ->leftJoin(HrJobTitleModel::class, 'hr_staff_info.job_title = hr_job_title.id', 'hr_job_title')
            ->inWhere('hr_staff_info.staff_info_id',array_values(array_unique(array_filter($ids))));
        $data = $builder->getQuery()->execute()->toArray();
        if(!empty($data)){
            return array_column($data,null,'staff_info_id');
        }
        return [];
    }

    /**
     * @description 获取指定部门下的关联职位
     * @param $department_id
     * @return array
     */
    public function getDepartmentRelateJobTitle($department_id): array
    {
        $jobTitle = HrJobDepartmentRelationModel::find([
            'conditions' => 'department_id = :department_id:',
            'bind' => [
                'department_id' => $department_id
            ],
            'columns' => 'job_id'
        ])->toArray();
        if (empty($jobTitle)) {
            return [];
        }
        $jobTitle = array_column($jobTitle, 'job_id');

        return HrJobTitleModel::find([
            'conditions' => 'id in({ids:array})',
            'bind' => [
                'ids' => $jobTitle
            ],
            'columns' => 'id as value,job_name as label'
        ])->toArray();
    }

}
