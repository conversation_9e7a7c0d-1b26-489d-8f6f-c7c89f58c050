<?php


namespace App\Services;

use App\Library\BaseService;
use App\Library\Enums\RedisEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\StaffPublicHolidayModel;
use App\Models\backyard\ThailandHolidayModel;
use App\Models\backyard\HrStaffInfoModel;
use Exception;

/**
 * 每个国家自己实现自己的法定假日，供其他service调用
 */
class HolidayService extends BaseService
{

    public static $holiday = [];


    public function getHolidayForSvc($params): array
    {
        $year = $params['year'];

        $startDate = $year . '-01-01';
        $endDate   = $year . '-12-31';

        //查询旧数据
        $thailandData = ThailandHolidayModel::find([
            'conditions' => 'day >= :start_date: AND day <= :end_date: AND type IN ({type:array})',
            'bind'       => [
                'start_date' => $startDate,
                'end_date'   => $endDate,
                'type'       => [ThailandHolidayModel::TYPE_DEFAULT, ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6],
            ],
            'columns'    => 'day',
            'order'      => 'day ASC',
        ])->toArray();
        return array_column($thailandData, 'day');
    }


    /**
     * 获取员工维度的法定节假日
     * @param array $params ['staff' => [['id','day_type']] ,'date'] //week_working_day=day_type
     * @return array
     */
    public function getStaffHoliday(array $params = []): array
    {
        $data      = [];
        $condition = '';
        $bind      = [];
        if (isset($params['date'])) {
            $condition    = 'day >=  :date:';
            $bind['date'] = date('Y-m-d', strtotime($params['date']));
        }
        if (!isset($params['staff'])) {
            return $data;
        }
        $holiday          = ThailandHolidayModel::find([
            'conditions' => $condition,
            'columns'    => 'day,type',
            'bind'       => $bind,
        ])->toArray();
        $staffFiveHoliday = [];
        $staffSixHoliday  = [];
        foreach ($holiday as $item) {
            if (0 == $item['type']) {
                $staffFiveHoliday[] = $item['day'];
                $staffSixHoliday[]  = $item['day'];
            }
            if (1 == $item['type']) {
                $staffSixHoliday[] = $item['day'];
            }
            if (2 == $item['type']) {
                $staffFiveHoliday[] = $item['day'];
            }
        }
        if (is_array($params['staff'])) {
            foreach ($params['staff'] as $item) {
                if (isset($item['id'], $item['day_type'])) {
                    if (5 == $item['day_type']) {
                        $data[$item['id']] = $staffFiveHoliday;
                    } else {
                        $data[$item['id']] = $staffSixHoliday;
                    }
                }
                if (isset($item['staff_info_id'], $item['week_working_day'])) {
                    if (5 == $item['week_working_day']) {
                        $data[$item['staff_info_id']] = $staffFiveHoliday;
                    } else {
                        $data[$item['staff_info_id']] = $staffSixHoliday;
                    }
                }
            }
        }

        return $data;
    }

    /**
     * 普通维度节假日(条件没有到员工维度的时候)
     * @param array $params
     * @return array[]
     */
    public function getHoliday(array $params = []): array
    {
        $condition = '';
        $bind      = [];
        if (isset($params['date'])) {
            $condition    = 'day >=  :date:';
            $bind['date'] = $params['date'];
        }
        $holiday          = ThailandHolidayModel::find([
            'conditions' => $condition,
            'columns'    => 'day,type',
            'bind'       => $bind,
        ])->toArray();
        $staffFiveHoliday = $staffSixHoliday = [];
        foreach ($holiday as $item) {
            if (ThailandHolidayModel::TYPE_DEFAULT == $item['type']) {
                $staffFiveHoliday[] = $item['day'];
                $staffSixHoliday[]  = $item['day'];
            }
            if (ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6 == $item['type']) {
                $staffSixHoliday[] = $item['day'];
            }
            if (ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_5 == $item['type']) {
                $staffFiveHoliday[] = $item['day'];
            }
        }
        return [HrStaffInfoModel::WEEK_WORKING_DAY_FIVE =>$staffFiveHoliday , HrStaffInfoModel::WEEK_WORKING_DAY_SIX =>$staffSixHoliday];
    }

    /**
     * 各个国家重写!
     * //根据员工信息 获取 对应时间区间的 系统的公共假期 thailand_holiday
     * @param $staffInfo 员工信息
     * @param $beginDate 查询开始时间
     * @param bool $self 是否获取私人自己的ph (staff_public_holiday)
     * @return array
     */
    public function getHolidayByStaffInfo($staffInfo, $beginDate, $self = false)
    {
        $param['date'] = $beginDate;
        $param['type'] = 7 - $staffInfo['week_working_day'];
        $holidays      = $this->getHoliday($param);
        $holidays      = $holidays[$staffInfo['week_working_day']] ?? [];

        //获取 私人ph
        if ($self) {
            $holidaySelf = StaffPublicHolidayModel::find([
                'conditions' => 'staff_info_id = :staff_id: and date_at >= :date_at: and is_deleted = 0',
                'bind'       => [
                    'staff_id' => $staffInfo['staff_info_id'],
                    'date_at'  => $beginDate,
                ],
            ])->toArray();
            $holidaySelf = empty($holidaySelf) ? [] : array_column($holidaySelf, 'date_at');
            $holidays    = array_merge($holidays, $holidaySelf);
        }
        return array_values(array_unique($holidays));
    }


    /**
     * 公共假期保存
     * @throws Exception
     */
    public function holidaySave($parmas)
    {
        $year = $parmas['year'];
        $data = $parmas['data'];

        //记录初始日志
        $this->logger->write_log('holiday params:'.json_encode($parmas), 'info');

        //过滤验证数据
        $data = $this->filterHolidaySaveData($data, $year);

        //获取操作年所有数据
        $oldData = $this->getYearData($year);

        //格式化数据
        $data = $this->formatHolidayInseartData($oldData, $data);

        if (in_array(get_country_code(),['MY','PH','ID'])) {
            reBuildCountryInstance(new HolidayChangeInfluenceLeaveAndOTService())->setTheDayBeforeFirstOperation();
        }

        //开启事务
        $backyardDb = $this->di->get('db_backyard');
        $backyardDb->begin();

        try {
            //删除
            if (!empty($data['delData'])) {
                ThailandHolidayModel::find([
                    "conditions" => "id in ({ids:array})",
                    "bind"       => ['ids' => array_keys($data['delData'])],
                ])->delete();

                //清理staff_public_holiday数据
                $this->delStaffPublicHoliday($data['delData'],$parmas['operate_id']);
                //清理补的休息日 老挝越南 都删 菲律宾不动 其他国家 按remark
                $this->delOffDay($data['delData'], $parmas['operate_id']);
//                if(isCountry('LA')||isCountry('VN')){
//                    HrStaffWorkDaysModel::find([
//                        "conditions" => "src_date in ({src_date:array})",
//                        "bind"       => ['src_date' => array_values($data['delData'])],
//                    ])->delete();
//                }
            }

            //新增
            if (!empty($data['addData'])) {
                (new ThailandHolidayModel())->batch_insert($data['addData']);
                //新增ph  给固定修员工 加off
                $phDates = array_unique(array_column($data['addData'],'day'));
                $phDates = array_values($phDates);
                $this->addDefaultOff($phDates,['type_map' => $parmas['data'], 'operate_id' => $parmas['operate_id']]);

            }
            //记录操作日志
            $this->logger->write_log('holiday save:'.json_encode($data), 'info');

            return $backyardDb->commit();
        } catch (Exception $exception) {
            $backyardDb->rollback();
            throw $exception;
        }
    }

    /**
     * 过滤数据
     * 1.验证传值错误数据
     * 2.过滤今日之前的数据
     * 3.过滤空数数据
     * @param $parmas
     * @return array
     */
    public function filterHolidaySaveData($data, $year)
    {
        if (empty($data)) {
            return [];
        }

        foreach ($data as $day => $val) {
            //排除不合规数据
            if ($val === '' || $day <= date("Y-m-d")) {
                unset($data[$day]);
            }

            //日期不合法
            if (!$this->checkFormalDate($day, $year)) {
                throw new ValidationException(self::$t->_('holoday.date_input_error'));
            }

            //取值不合法
            if ($val != ThailandHolidayModel::TYPE_DEFAULT) {
                throw new ValidationException(self::$t->_('holoday.date_input_value_error'));
            }
        }

        return $data;
    }

    /**
     * 对比需要处理的数据
     * del[1,2,3,4]
     * add[1,2,3,4]
     * @param $oldData
     * @param $newData
     * @return array
     */
    public function formatHolidayInseartData($oldData, $newData)
    {
        $delData = $addData = [];
        $oldData = array_column($oldData, null, 'day');

        //旧数据清理，删除多余集合
        foreach ($oldData as $oldKey => $oldVal) {
            if (!isset($newData[$oldKey])) {
                $delData[$oldVal['id']] = $oldKey;
            }
        }

        //新数据数据清理，删除多余集合
        foreach ($newData as $newKey => $newVal) {
            if (!isset($oldData[$newKey])) {
                $addData[] = [
                    'day'  => $newKey,
                    'type' => ThailandHolidayModel::TYPE_DEFAULT,
                ];
            }
        }

        return [
            'delData' => $delData,
            'addData' => $addData,
        ];
    }

    /**
     * 获取年度数据
     * @param $year
     * @return mixed
     */
    public function getYearData($year)
    {
        //初始化查询条件
        $startDate = date("Y-m-d", strtotime("+1 day"));

        if ($year != date('Y')) {
            $startDate = $year.'-01-01';
        }

        $endDate = $year.'-12-31';

        //查询旧数据
        return ThailandHolidayModel::find([
            'conditions' => 'day >= :start_date: AND day <= :end_date: AND type IN ({type:array})',
            'bind'       => [
                'start_date' => $startDate,
                'end_date'   => $endDate,
                'type'       => [ThailandHolidayModel::TYPE_DEFAULT, ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6],
            ],
            'columns'    => 'id,day,type,province_code',
        ])->toArray();
    }

    /**
     * 公共假期列表
     * @param $params
     * @return mixed
     */
    public function holidayList($params)
    {
        $year = $params['year'];

        $startDate = $year.'-01-01';
        $endDate   = $year.'-12-31';

        //查询旧数据
        $thailandData = ThailandHolidayModel::find([
            'conditions' => 'day >= :start_date: AND day <= :end_date: AND type IN ({type:array})',
            'bind'       => [
                'start_date' => $startDate,
                'end_date'   => $endDate,
                'type'       => [ThailandHolidayModel::TYPE_DEFAULT, ThailandHolidayModel::TYPE_WEEK_WORKING_DAY_6],
            ],
            'columns'    => 'id,day,type',
            'order'      => 'day ASC',
        ])->toArray();

        $data['dataList'] = $this->formatholidayReturnData($thailandData);
        $data['types']    = new \ArrayObject(ThailandHolidayModel::$common_types);

        return $data;
    }

    /**
     * 公共假期输出格式化
     * @param $data
     * @return mixed
     */
    public function formatholidayReturnData($data,$myProvinceCodes=[])
    {
        $returnData = [];

        if (empty($data)) {
            return $returnData;
        }

        foreach ($data as $v) {
            $returnData[$v['day']] = (string)ThailandHolidayModel::TYPE_DEFAULT;
        }

        return $returnData;
    }

    /**
     * 字符串补位
     */
    public function checkFormalDate($date, $year)
    {
        $dates = explode('-', $date);

        if (empty($dates[0]) || empty($dates[1]) || empty($dates[2])) {
            return false;
        }

        if ($dates[0] != $year) {
            return false;
        }

        return checkdate($dates[1], $dates[2], $dates[0]);
    }

    /**
     * 清理StaffPublicHoliday
     * @param $data
     * @return bool
     */
    public function delStaffPublicHoliday($data,$operate_id)
    {
        //记录清理日志
        $this->logger->write_log('delStaffPublicHoliday:'.json_encode($data), 'info');
        
        if (empty($data)) {
            return false;
        }
        $data = array_values($data);

        $staffPublicHoliday = StaffPublicHolidayModel::find([
            'columns'    => 'date_at',
            'conditions' => "src_date in ({src_date:array}) and is_deleted = 0 ",
            'bind'       => ['src_date' => $data],
        ])->toArray();
        if (!empty($staffPublicHoliday)) {
            $dateList = array_values(array_unique(array_column($staffPublicHoliday, 'date_at')));
            $this->delOffDay($dateList, $operate_id,HrStaffWorkDaysModel::REMARK_FOR_COMPENSATE_OFF);
        }

        $backyardDb                  = $this->di->get('db_backyard');
        $staffPublicHolidayModelName = (new StaffPublicHolidayModel())->getSource();

        $starDate = min($data);

        if (!$starDate) {
            return false;
        }

        $backyardDb->updateAsDict(
            $staffPublicHolidayModelName,
            ['is_deleted' => 1],
            [
                'conditions' => 'date_at >= ?',
                'bind'       => [$starDate],
                'bindTypes'  => [\PDO::PARAM_STR, \PDO::PARAM_STR],
            ]
        );

        return true;
    }

    public function delOffDay($dateList, $operate_id = 0,$remark = HrStaffWorkDaysModel::REMARK_FOR_DEFAULT_HOLIDAY){
        $data = HrStaffWorkDaysModel::find([
            "conditions" => "src_date in ({src_date:array}) and  remark = :remark:",
            "bind"       => ['src_date' => array_values($dateList),'remark' => $remark],
        ]);

        //日志表
        $logServer = new WorkShiftService();
        $logData = $logServer->formatWorkdayModel($data->toArray(), $operate_id);
        $ext['t_key'] = 'workday_cancel_ph';
        $logServer->addShiftLogBatch(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF,$logData, $ext);

        $this->logger->info('ph_delete_off ' . json_encode($dateList) . json_encode(array_column($data->toArray(),'staff_info_id')));
        $data->delete();
    }


    //新增 ph 给固定修员工 增加 off remark 要标记成default_holiday_add
    public function addDefaultOff($phList, $extend = [])
    {
        if (empty($phList)) {
            return true;
        }
        //菲律宾暂时不给默认off
        if(isCountry('PH')){
            return true;
        }
        $staffIds        = $extend['staff_ids'] ?? [];//页面操作公共假期 不传 其他都传
        $phId            = $extend['staff_ph_id'] ?? 0;//关联的补ph 的id
        $specialTypeList = $extend['type_map']??[];//特殊国家
        $server          = reBuildCountryInstance($this);

        //如果 有工号 不查询
        if(empty($staffIds)){
            //固定修 在职 停职 非子账号
            $conditions = '(state in (1,3) or (leave_date >= :leave_date: and state = :leave_state:)) and formal in (1,4) and is_sub_staff = 0 and rest_type = :rest_type: ';
            $bind       = [
                'rest_type'   => HrStaffInfoModel::REST_TYPE_2,
                'leave_state' => HrStaffInfoModel::STATE_RESIGN,
                'leave_date'  => date('Y-m-d 00:00:00', strtotime(' -30 days')),

            ];

            $staffInfos = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,nationality,sex,sys_store_id',
                'conditions' => $conditions,
                'bind'       => $bind,
            ])->toArray();
            $staffIds   = empty($staffInfos) ? [] : array_column($staffInfos, 'staff_info_id');
            $staffInfos = empty($staffInfos) ? [] : array_column($staffInfos, null, 'staff_info_id');
        }


        //马来需要对应所在州
        if (isCountry('MY')) {
            $server->setItemData($staffIds);
        }

        //先看看 有没有数据
        $exist = HrStaffWorkDaysModel::find([
            'columns'    => "staff_info_id ,concat(staff_info_id,'_',date_at) as u_key",
            'conditions' => 'staff_info_id in ({ids:array}) and date_at in ({dates:array})',
            'bind'       => [
                'ids'   => $staffIds,
                'dates' => $phList,

            ],
        ])->toArray();

        $exist = empty($exist) ? [] : array_column($exist, 'staff_info_id', 'u_key');
        $remark = HrStaffWorkDaysModel::REMARK_FOR_DEFAULT_HOLIDAY;
        if(isset($extend['remark'])){
            $remark = $extend['remark'];
        }
        //组装数据 写 workday
        $insert = [];
        foreach ($staffIds as $staffId) {
            foreach ($phList as $date) {
                $k = "{$staffId}_{$date}";
                if (!empty($exist[$k])) {
                    continue;
                }
                //针对 马来 和老挝 修改公共假期页面 其他任务入口进来的不管
                if (empty($extend['staff_ids']) && !empty($specialTypeList) && !empty($staffInfos[$staffId])) {
                    $flag = $server->getConditions($date, $staffInfos[$staffId], $specialTypeList);
                    if (!$flag) {//如果是 false 不符合条件 不操作默认off
                        continue;
                    }
                }

                $row['staff_info_id'] = $staffId;
                $row['month']         = date('Y-m', strtotime($date));
                $row['date_at']       = $date;
                $row['operator']      = 10000;
                $row['remark']        = $remark;
                $row['staff_ph_id']   = $phId;
                $row['src_date']      = $date;
                $row['type']          = HrStaffWorkDaysModel::TYPE_1;
                if(isCountry('MY')){
                    $row['type']          = HrStaffWorkDaysModel::TYPE_2;
                }
                $insert[]             = $row;
            }
        }
        if(!empty($insert)){
            (new HrStaffWorkDaysModel())->batch_insert($insert);

            //日志表
            $logServer = new WorkShiftService();
            $logData = $logServer->formatWorkdayModel($insert, $extend['operate_id'] ?? 10000);
            $ext['t_key'] = 'workday_add_ph';
            $logServer->addShiftLogBatch(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF,$logData, $ext);

            $this->logger->info('addDefaultOff ' . json_encode($phList) . json_encode(array_column($insert,'staff_info_id')));
        }

    }


    //只有 老挝 和马来 特殊 符合条件 true 不符合 false
    public function getConditions($date, $staffInfo, $conditions)
    {
        return true;
    }

}
