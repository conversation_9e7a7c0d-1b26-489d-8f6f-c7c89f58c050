<?php
/**
 * Author: Bruce
 * Date  : 2022-06-21 14:29
 * Description:
 */

namespace App\Services;


use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MessageEnums;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\MessageWarningModel;
use App\Models\backyard\QuestionnaireAnswerModel;
use App\Models\coupon\MessageCourierModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Modules\Id\library\Enums\enums;

class MessageCourierService extends BaseService
{
    /**
     * 获取员工问卷消息
     * @param $param
     * @return array
     */
    public function get_staff_questionnaire($param){
        $remote_msg_id = $param['msg_id'];
        $staff_info_id = $param['staff_info_id'];
        if (empty($staff_info_id) || empty($remote_msg_id)) {
            return [];
        }

        $sql = "SELECT id, title, read_state AS submit_status
                FROM message_courier
                WHERE message_content_id = :msg_id AND staff_info_id = :staff_id 
                    AND category = :category AND category_code = :category_code
                    AND push_state = :push_state AND is_del = :is_del
                LIMIT 1";
        $query_param = [
            'msg_id' => $remote_msg_id,
            'staff_id' => $staff_info_id,
            'category' => 6,
            'category_code' => 3,
            'push_state' => 1,
            'is_del' => 0,
        ];

        $res = $this->getDI()->get('db_message_read')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];
    }

    /**
     * 获取问卷消息详情
     * @param $param
     * @return array
     */
    public function get_questionnaire_detail($param){
        if (empty($param['remote_msg_id'])) {
            return [];
        }

        // 获取问卷消息 和 问卷库内容
        $sql = "SELECT m.title,m.end_time, m.content, m.feedback_setting, lib.qn_lib_content, m.remote_message_id, lib.qn_lib_desc
                FROM message AS m LEFT JOIN questionnaire_lib AS lib 
                ON m.questionnaire_lib_id = lib.id
                WHERE m.remote_message_id = :remote_msg_id LIMIT 1";
        $query_param = [
            'remote_msg_id' => $param['remote_msg_id'],
        ];

        $res = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);


        return $res ? $res : [];
    }

    // 获取该员工已提交的问卷答案
    public function get_staff_answer($param){
        if (empty($param['remote_message_id']) || empty($param['staff_info_id'])) {
            return [];
        }

        // 获取问卷消息 和 问卷库内容
        $sql = "SELECT id,answer_content, submit_time FROM questionnaire_answer 
                WHERE staff_info_id = :staff_id AND remote_message_id = :remote_message_id LIMIT 1";
        $query_param = [
            'staff_id' => $param['staff_info_id'],
            'remote_message_id' => $param['remote_message_id'],
        ];

        $res = $this->getDI()->get('db_rby')->query($sql, $query_param)->fetch(\Phalcon\Db::FETCH_ASSOC);
        return $res ? $res : [];
    }

    // 添加问卷结果-只添加答案数据，没有答案内容
    public function add_answer_question($param){
        if (empty($param)) {
            return false;
        }

        // 添加问卷 并 修改问卷消息提交状态
        $db = $this->getDI()->get('db_backyard');
        try {
            $answer_sql = "INSERT INTO questionnaire_answer
                            (remote_message_id, staff_info_id, create_time)
                            VALUES (?, ?, ?)";
            $answer_data = [
                $param['remote_message_id'],
                $param['staff_info_id'],
                date('Y-m-d H:i:s', time()),
            ];
            $db->execute($answer_sql, $answer_data);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("hcm_svc :questionnaire add_answer_question - : " . $e->getMessage() . 'params=>' . json_encode($param, JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    // 更新问卷结果
    public function update_answer_question($answerId){
        if (empty($answerId)) {
            return false;
        }

        // 添加问卷 并 修改问卷消息提交状态
        $db_bi = $this->getDI()->get('db_backyard');
        try {
            $db_bi->updateAsDict(
                'questionnaire_answer',
                ['create_time' => date('Y-m-d H:i:s', time())],
                'id = ' . $answerId
            );
            return true;
        } catch (\Exception $e) {
            $this->logger->error("hcm_svc :questionnaire add_answer_question - : " . $e->getMessage() . 'params=>' . json_encode($answerId, JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    // 更新问卷结果-答案，提交时间
    public function update_answer($param){
        if (empty($param)) {
            return false;
        }
        $answer_info = $this->get_staff_answer($param);
        if(empty($answer_info)) {
            return false;

        }

        // 添加问卷 并 修改问卷消息提交状态
        $db_by = $this->getDI()->get('db_backyard');
        $db_message = $this->getDI()->get('db_message');

        try {
            $db_by->begin();
            $db_message->begin();

            $answer_data['answer_content'] = $param['answer'];
            $answer_data['submit_time'] = date('Y-m-d H:i:s', time());
            $db_by->updateAsDict('questionnaire_answer', $answer_data, ['conditions' => "id =?", 'bind' => [$answer_info['id']]]);

            $msg_update_sql = "UPDATE message_courier SET read_state = ? WHERE id = ? LIMIT 1";
            $msg_update_param = [1, $param['message_courier_id']];
            $db_message->execute($msg_update_sql, $msg_update_param);

            $db_by->commit();
            $db_message->commit();

            return true;
        } catch (\Exception $e) {
            $db_by->rollback();
            $db_message->rollback();
            $this->logger->error("hcm rpc: questionnaire update_answer - : " . $e->getMessage() . 'params=>' . json_encode($param, JSON_UNESCAPED_UNICODE));

            return false;
        }
    }

    /**
     * 根据 题库id 和工号，查询员工答案
     * @param $params
     * @return array
     */
    public function getStaffAnswer($params)
    {
        $result = QuestionnaireAnswerModel::findFirst([
            'conditions'=>' staff_info_id = :staff_info_id: and lib_id = :lib_id:',
            'bind'=>[
                'staff_info_id' => $params['staff_info_id'],
                'lib_id' => $params['lib_id'],
            ],
        ]);

        return empty($result) ? [] : $result->toArray();
    }

    /**
     * 添加问卷结果-只添加答案数据，没有答案内容
     * @param $param
     * @return bool
     */
    public function addAnswerQuestion($param){
        if (empty($param)) {
            return false;
        }

        // 添加问卷 并 修改问卷消息提交状态
        $db_by = $this->getDI()->get('db_backyard');
        try {
            $answer_sql = "INSERT INTO questionnaire_answer
                            (lib_id, staff_info_id, create_time)
                            VALUES (?, ?, ?)";
            $answer_data = [
                $param['lib_id'],
                $param['staff_info_id'],
                date('Y-m-d H:i:s', time()),
            ];
            $db_by->execute($answer_sql, $answer_data);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("hcm_svc :questionnaire addAnswerQuestion - out part: " . $e->getMessage() . 'params=>' . json_encode($param, JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    /**
     * 更新问卷结果-答案，提交时间
     * @param $param
     * @return bool
     */
    public function updateAnswer($param){
        if (empty($param)) {
            return false;
        }
        $answer_info = $this->getStaffAnswer($param);
        if(empty($answer_info)) {
            return false;

        }
        // 添加问卷 并 修改问卷消息提交状态
        $db_by = $this->getDI()->get('db_backyard');
        try {

            $answer_data['answer_content'] = $param['answer'];
            $answer_data['submit_time'] = date('Y-m-d H:i:s', time());
            $db_by->updateAsDict('questionnaire_answer', $answer_data, ['conditions' => "id =?", 'bind' => [$answer_info['id']]]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error("hcm rpc: questionnaire update_answer - : " . $e->getMessage() . 'params=>' . json_encode($param, JSON_UNESCAPED_UNICODE));

            return false;
        }
    }

    /**
     * 删除by消息
     * @param array $ids
     * @return mixed
     */
    public function delMessageByIds(array $ids)
    {
        if (empty($ids)) {
            return false;
        }

        return $this->getDI()->get('db_message')->updateAsDict(
            'message_courier',
            ['is_del' => GlobalEnums::DELETED],
            ['conditions' => "id in ('" . implode('\',\'', $ids) . "')"]);
    }

    /**
     * 查询员工消息
     * @param $params
     * @param array $columns
     * @return array
     */
    public function getMessageCourierInfo($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind       = [];


        if (!empty($params['staff_info_id'])) {
            $conditions .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params['staff_info_id'];
        }

        if (!empty($params['category'])) {
            $conditions .= ' and category = :category:';
            $bind['category'] = $params['category'];
        }

        if (isset($params['read_state']) && in_array($params['read_state'], [MessageCourierModel::READ_STATE_NO, MessageCourierModel::READ_STATE_YES])) {
            $conditions .= ' and read_state = :read_state:';
            $bind['read_state'] = $params['read_state'];
        }

        $data = MessageCourierModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($data) ? [] : $data->toArray();
    }

    /**
     * 获取发送处罚警告的职位ID列表
     *
     * 该函数返回一个包含职位ID的数组，这些职位需要接收处罚警告。
     * 通常用于在特定场景下筛选出需要发送警告的职位。
     *
     * @return array 返回一个包含职位ID的数组，例如 ['13', '110']
     */
    protected function getSendPenaltyWarningJobTitle(): array
    {
        return ['110','13','452','1015','1930','1844'];
    }

    protected function getSendWarningWarningStoreCategory(): array
    {
        return ['1','2','10','14'];
    }


    /**
     * 20450【TH】虚假类处罚站内信逻辑
     * @param $date_at
     * @return bool
     * @throws \Exception
     */
    public function penaltyWarningMessage($date_at): bool
    {
        $store_ids = array_values(array_unique(array_merge($this->getPenaltyStoreIds($date_at),
            $this->getWarningStoreIds($date_at))));
        if (empty($store_ids)) {
            $this->logger->info('20450【TH】虚假类处罚站内信逻辑：未获取到处罚网点id');
            return true;
        }
        //根据网点获取大区、片区、网点数据

        $store_piece_region_data = $this->getStorePieceRegionData($store_ids);

        //给大区负责人发
        if (!empty($store_piece_region_data['region_manager'])) {
            $this->dealRegionManagerMessage($date_at, $store_piece_region_data['region_manager']);
        }
        //给片区负责人发
        if (!empty($store_piece_region_data['piece_manager'])) {
            $this->dealPieceManagerMessage($date_at, $store_piece_region_data['piece_manager']);
        }
        //给网点负责人发
        if (!empty($store_piece_region_data['store_manager'])) {
            $this->dealStoreManagerMessage($date_at, $store_piece_region_data['store_manager']);
        }
        //给快递员发
        if (!empty($store_piece_region_data['piece_ids'])) {
            $this->dealStaffMessage($date_at, $store_piece_region_data['piece_ids']);
        }
        return true;
    }


    /**
     * @param $date_at
     * @param $region_manager
     * @return bool
     */
    protected function dealRegionManagerMessage($date_at, $region_manager): bool
    {
        if (empty($region_manager)) {
            return true;
        }

        $all_manager = array_keys($region_manager);
        $staffsLang  = (new StaffService())->getStaffEquipmentLanguage($all_manager);
        $messageService = new MessagesService();

        foreach ($region_manager as $manager_id => $item) {
            $content = json_encode([
                'manage_ids' => array_values(array_unique($item)),
                'biz_value'  => '0',
                'biz_type'   => '1',
                'date_at'    => $date_at,
            ], JSON_UNESCAPED_UNICODE);

            $staffLang      = $staffsLang[$manager_id] ?? getCountryDefaultLang();
            $t              = BaseService::getTranslation($staffLang);
            $title          = $t->_('20450_message_title',['date_at'=>date('md', strtotime($date_at))]);
            $message_params = [
                'staff_info_id' => $manager_id,
                'title'         => $title,
                'category'      => MessageEnums::MESSAGE_CATEGORY_PENALTY_WARNING,
                'content'       => $content,
                'sub_category'  => 1,
            ];
            $messageService->sendStaffMessage([], $message_params);
        }
        return true;
    }


    /**
     * @param $date_at
     * @param $piece_manager
     * @return bool
     */
    protected function dealPieceManagerMessage($date_at, $piece_manager): bool
    {
        if (empty($piece_manager)) {
            return true;
        }

        $all_manager = array_keys($piece_manager);
        $staffsLang  = (new StaffService())->getStaffEquipmentLanguage($all_manager);
        $messageService = new MessagesService();

        foreach ($piece_manager as $manager_id => $item) {
            foreach ($item as $region_id => $_piece_ids) {
                $content        = json_encode([
                    'manage_ids' => array_values(array_unique($_piece_ids)),
                    'biz_value'  => strval($region_id),
                    'biz_type'      => '2',
                    'date_at'      => $date_at,

                ], JSON_UNESCAPED_UNICODE);
                $staffLang      = $staffsLang[$manager_id] ?? getCountryDefaultLang();
                $t              = BaseService::getTranslation($staffLang);
                $title          = $t->_('20450_message_title', ['date_at' => date('md', strtotime($date_at))]);
                $message_params = [
                    'staff_info_id' => $manager_id,
                    'title'         => $title,
                    'category'      => MessageEnums::MESSAGE_CATEGORY_PENALTY_WARNING,
                    'content'       => $content,
                    'sub_category'  => 2,
                ];
                $messageService->sendStaffMessage([], $message_params);
            }
        }
        return true;
    }

    /**
     * @param $date_at
     * @param $store_manager
     * @return bool
     */
    protected function dealStoreManagerMessage($date_at, $store_manager): bool
    {
        if (empty($store_manager)) {
            return true;
        }

        $all_manager = array_keys($store_manager);
        $staffsLang  = (new StaffService())->getStaffEquipmentLanguage($all_manager);
        $messageService = new MessagesService();

        foreach ($store_manager as $manager_id => $item) {
            foreach ($item as $piece_id => $_store_ids) {
                $content = json_encode([
                    'manage_ids' => array_values(array_unique($_store_ids)),
                    'biz_value'  => strval($piece_id),
                    'biz_type'   => '3',
                    'date_at'    => $date_at,

                ], JSON_UNESCAPED_UNICODE);
                $staffLang      = $staffsLang[$manager_id] ?? getCountryDefaultLang();
                $t              = BaseService::getTranslation($staffLang);
                $title          = $t->_('20450_message_title', ['date_at' => date('md', strtotime($date_at))]);
                $message_params = [
                    'staff_info_id' => $manager_id,
                    'title'         => $title,
                    'category'      => MessageEnums::MESSAGE_CATEGORY_PENALTY_WARNING,
                    'content'       => $content,
                    'sub_category'  => 3,
                ];
                $messageService->sendStaffMessage([], $message_params);
            }
        }
        return true;
    }

    /**
     * @param $date_at
     * @param $piece_ids
     * @return bool
     */
    protected function dealStaffMessage($date_at, $piece_ids): bool
    {
        if (empty($piece_ids)) {
            return true;
        }

        foreach ($piece_ids as $piece_id) {
            $staff_builder = $this->modelsManager->createBuilder();
            $staff_builder->columns([
                'staff.staff_info_id',
                'staff.sys_store_id',
            ]);
            $staff_builder->from(['staff' => HrStaffInfoModel::class]);
            $staff_builder->innerJoin(SysStoreModel::class, 'store.id = staff.sys_store_id', 'store');
            $staff_builder->where('store.manage_piece = :piece_id:', ['piece_id' => $piece_id]);
            $staff_builder->andWhere('staff.state != :state: and staff.job_title in ({job_title:array}) and staff.formal in ({formal:array}) and staff.hire_type != :hire_type:',
                ['state'     => HrStaffInfoModel::STATE_RESIGN,
                 'job_title' => $this->getSendPenaltyWarningJobTitle(),
                 'formal'    => [HrStaffInfoModel::FORMAL_0, HrStaffInfoModel::FORMAL_1],
                 'hire_type' => HrStaffInfoModel::HIRE_TYPE_UN_PAID,
                ]);
            $staff_data = $staff_builder->getQuery()->execute()->toArray();
            if (empty($staff_data)) {
                continue;
            }
            $staff_ids      = array_column($staff_data, 'staff_info_id');
            $staffsLang     = (new StaffService())->getStaffEquipmentLanguage($staff_ids);
            $messageService = new MessagesService();
            foreach ($staff_data as $staff_datum) {
                $content = json_encode([
                    'manage_ids' => [],
                    'biz_value'  => $piece_id,
                    'biz_type'   => '3',
                    'date_at'    => $date_at,
                    'is_courier' => '1',
                ], JSON_UNESCAPED_UNICODE);
                $staffLang      = $staffsLang[$staff_datum['staff_info_id']] ?? getCountryDefaultLang();
                $t              = BaseService::getTranslation($staffLang);
                $title          = $t->_('20450_message_title', ['date_at' => date('md', strtotime($date_at))]);
                $message_params = [
                    'staff_info_id' => $staff_datum['staff_info_id'],
                    'title'         => $title,
                    'category'      => MessageEnums::MESSAGE_CATEGORY_PENALTY_WARNING,
                    'content'       => $content,
                    'sub_category'  => 4,
                ];
                $messageService->sendStaffMessage([], $message_params);
            }
        }
        return true;
    }


    /**
     * @param $store_ids
     * @return array|array[]
     */
    public function getStorePieceRegionData($store_ids): array
    {

        if (empty($store_ids)) {
            return [];
        }

        $storeList = SysStoreModel::find([
            'conditions' => 'id in ({ids:array}) and category in ({category:array}) and manage_region is not null and  manage_piece is not null',
            'bind'       => [
                'category' => $this->getSendWarningWarningStoreCategory(),
                'ids'      => $store_ids,
            ],
            'columns'=>'id,manage_region,manage_piece'
        ])->toArray();

        if (empty($storeList)) {
            $this->logger->info(['store_filter'=>$store_ids]);
            return [];
        }

        $region = array_values(array_unique(array_column($storeList, 'manage_region')));
        $piece  = array_values(array_unique(array_column($storeList, 'manage_piece')));

        $store_builder = $this->modelsManager->createBuilder();
        $store_builder->columns([
            'store.id store_id',
            'store.manager_id store_manager_id',
            'piece.id piece_id',
            'piece.manager_id piece_manager_id',
            'region.id region_id',
            'region.manager_id region_manager_id',
        ]);
        $store_builder->from(['store' => SysStoreModel::class]);
        $store_builder->leftjoin(SysManagePieceModel::class, 'store.manage_piece = piece.id', 'piece');
        $store_builder->leftjoin(SysManageRegionModel::class, 'store.manage_region = region.id', 'region');
        //$store_builder->inWhere('store.id', $store_ids);
        $store_builder->andWhere('store.category in ({category:array}) and store.manage_region is not null and  store.manage_piece is not null', ['category'=>$this->getSendWarningWarningStoreCategory()]);
        $store_data = $store_builder->getQuery()->execute()->toArray();
        if (empty($store_data)) {
            return [];
        }
        $region_manager = $piece_manager = $store_manager  = [];

        foreach ($store_data as $store_datum) {
            if (!empty($store_datum['region_manager_id'])) {
                $region_manager[$store_datum['region_manager_id']][] = $store_datum['region_id'];
            }
            if (!empty($store_datum['piece_manager_id']) && in_array($store_datum['region_id'],$region)) {
                $piece_manager[$store_datum['piece_manager_id']][$store_datum['region_id']][] = $store_datum['piece_id'];
            }
            if (!empty($store_datum['store_manager_id']) && in_array($store_datum['piece_id'],$piece)) {
                $store_manager[$store_datum['store_manager_id']][$store_datum['piece_id']][] = $store_datum['store_id'];
            }
        }
        //大区数据

        return [
            'region_manager' => $region_manager,
            'piece_manager'  => $piece_manager,
            'store_manager'  => $store_manager,
            'piece_ids'      => $piece,
        ];
    }


    /**
     * 获取处罚网点id
     * @param $date_at
     * @return mixed
     * @throws \Exception
     */
    protected function getPenaltyStoreIds($date_at)
    {

        $apiClient = new ApiClient('ard_api', '', 'abnormal.getMailStoreIdByFakeType');
        $apiClient->withParam(['date_at' => $date_at]);
        $result = $apiClient->execute();
        if (isset($result['code'])) {
            return $result['data']??[];
        }
        throw new \Exception('penalty_store_api_error'.$date_at);
    }

    /**
     * 获取警告网点id
     * @param $date_at
     * @return array
     */
    public function getWarningStoreIds($date_at): array
    {
        $params['start_time'] = zero_time_zone($date_at);
        $params['end_time']   = gmdate('Y-m-d H:i:s', strtotime($date_at) + 86400);
        $builder              = $this->modelsManager->createBuilder();
        $builder->from(['wm' => MessageWarningModel::class]);
        $builder->where('wm.warning_type in ({warning_type:array}) and wm.is_delete = 0 and wm.type_code = :type_code:',
            ['warning_type' => [1, 2, 3], 'type_code' => 't_warning_6']);
        $builder->andWhere('wm.created_at >= :start_time: and wm.created_at < :end_time:',
            ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
        $builder->columns(['wm.store_id']);
        $result = $builder->getQuery()->execute()->toArray();
        if (empty($result)) {
            return [];
        }
        return array_column($result,'store_id');
    }




}