<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\StaffPayrollCompanyInfoModel;
use App\Models\backyard\StaffPayrollReportOperateLogModel;
use App\Modules\My\library\Enums\SalaryEnums;
use Exception;


class StaffPayrollListOperateLogService extends BaseService
{

    //类型
    public static $business_type = [
        StaffPayrollReportOperateLogModel::business_type_all       => 'payroll_report_business_all',
        StaffPayrollReportOperateLogModel::business_type_pcb       => 'payroll_report_business_pcb',
        StaffPayrollReportOperateLogModel::business_type_ea        => 'payroll_report_business_ea',
        StaffPayrollReportOperateLogModel::business_type_cp39      => 'payroll_report_business_cp39',
        StaffPayrollReportOperateLogModel::business_type_socso8    => 'payroll_report_business_socso8',
        StaffPayrollReportOperateLogModel::business_type_eis1      => 'payroll_report_business_esi1',
        StaffPayrollReportOperateLogModel::business_type_kwsp6     => 'payroll_report_business_kwsp6',
        StaffPayrollReportOperateLogModel::business_type_cp22      => 'payroll_report_business_cp22',
        StaffPayrollReportOperateLogModel::business_type_cp21      => 'CP21',
        StaffPayrollReportOperateLogModel::business_type_cp22a     => 'CP22A',
        StaffPayrollReportOperateLogModel::business_type_cp22a_txt => 'CP22A(TXT)',
    ];
    //操作类型 1 批量发送 2 发送单个 3 下载批量 4 下载单个  type 1 需要显示备注
    public static $operate_type = [
        StaffPayrollReportOperateLogModel::operate_type_1  => ['title' => 'payroll_report_history_operate_type_1'],
        StaffPayrollReportOperateLogModel::operate_type_2  => ['title' => 'payroll_report_history_operate_type_2'],
        StaffPayrollReportOperateLogModel::operate_type_3  => ['title' => 'payroll_report_history_operate_type_3'],
        StaffPayrollReportOperateLogModel::operate_type_4  => ['title' => 'payroll_report_history_operate_type_4'],
        StaffPayrollReportOperateLogModel::operate_type_5  => ['title' => 'payroll_report_history_operate_type_5'],
        StaffPayrollReportOperateLogModel::operate_type_6  => ['title' => 'payroll_report_history_operate_type_6'],
        StaffPayrollReportOperateLogModel::operate_type_7  => ['title' => 'payroll_report_history_operate_type_5'],
        StaffPayrollReportOperateLogModel::operate_type_8  => ['title' => 'payroll_report_history_operate_type_6'],
        StaffPayrollReportOperateLogModel::operate_type_9  => ['title' => 'payroll_report_history_operate_type_7'],
        StaffPayrollReportOperateLogModel::operate_type_10 => ['title' => 'payroll_report_history_operate_type_6'],
        StaffPayrollReportOperateLogModel::operate_type_11 => ['title' => 'payroll_report_history_operate_type_5'],
        StaffPayrollReportOperateLogModel::operate_type_12 => ['title' => 'payroll_report_history_operate_type_7'],
        StaffPayrollReportOperateLogModel::operate_type_13 => ['title' => 'payroll_report_history_operate_type_6'],
        StaffPayrollReportOperateLogModel::operate_type_14 => ['title' => 'payroll_report_history_operate_type_5'],
        StaffPayrollReportOperateLogModel::operate_type_15 => ['title' => 'payroll_report_history_operate_type_5'],
        StaffPayrollReportOperateLogModel::operate_type_16 => ['title' => 'payroll_report_history_operate_type_5'],
        StaffPayrollReportOperateLogModel::operate_type_17 => ['title' => 'payroll_report_history_operate_type_6'],
    ];

    /**
     * @description: 获取操作类型枚举
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/7 15:30
     */
    public function getStaticEnums(): array
    {
        $data          = [];
        $business_type = self::$business_type;
        foreach ($business_type as $k => $v) {
            $data[] = ['code' => $k, 'title' => self::$t->_($v)];
        }
        return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => $data];
    }

    /**
     * @description: 获取列表
     * @param null
     * @return array :
     * @throws BusinessException
     * <AUTHOR> L.J
     * @time       : 2022/6/1 16:30
     */
    public function getList($paramIn): array
    {
        $data       = ['list' => [], 'count' => 0];
        $companyIds = (new PayrollManageService())->getStaffManageCompanyIds($paramIn['operate_id'] ?? 0);
        $this->logger->info($companyIds);
        if (empty($companyIds)) {
            throw new BusinessException(static::$t->_('no_data_permission'));
        }
        if (!empty($paramIn['company_id']) && !in_array($paramIn['company_id'], $companyIds)) {
            return $data;
        }
        try {
            $page   = $paramIn['page'] ?? 1;
            $size   = $paramIn['page_size'] ?? 20;
            $offset = ($page - 1) * $size;

            $staff_info_id = $paramIn['staff_info_id'] ?? '';   //员工
            $year          = $paramIn['year'] ?? '';            //年
            $business      = $paramIn['business'] ?? '';        //类型
            $start_date    = $paramIn['start_date'] ?? '';      //操作开始时间
            $end_date      = $paramIn['end_date'] ?? '';        //操作结束时间
            $companyIds    = empty($paramIn['company_id']) ? $companyIds : [$paramIn['company_id']];


            //查询条件
            $conditions = ' 1=1 ';
            $bind       = [];
            //操作者
            if ($staff_info_id) {
                $conditions             .= ' and log.operator_id in ({staff_info_ids:array}) ';
                $bind['staff_info_ids'] = explode(',', $staff_info_id);
            }
            //年月
            if ($year) {
                $conditions   .= ' and log.year = :year: ';
                $bind['year'] = $year;
            }
            //类型
            if ($business) {
                $conditions       .= ' and log.business = :business: ';
                $bind['business'] = $business;
            }
            //发薪公司
            if ($companyIds) {
                $conditions          .= ' and (log.company_id  in ({company_ids:array}) or log.company_id=0) ';
                $bind['company_ids'] = $companyIds;
            } else {
                $conditions .= ' and log.company_id=0';
            }
            //操作开始时间
            if ($start_date) {
                //时间转换
                $start_date         .= ' 00:00:00';
                $conditions         .= ' and log.created_at >= :start_date: ';
                $bind['start_date'] = gmdate('Y-m-d H:i:s', strtotime($start_date)); //时间因为表里是 0 时区 所以需要转换一下
            }
            //操作结束时间
            if ($end_date) {
                //时间转换
                $end_date         .= ' 00:00:00';
                $conditions       .= ' and log.created_at <= :end_date: ';
                $bind['end_date'] = gmdate('Y-m-d H:i:s',
                    strtotime('+1 day', strtotime($end_date))); //时间因为表里是 0 时区 所以需要转换一下
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['log' => StaffPayrollReportOperateLogModel::class]);
            $builder->join(HrStaffInfoReadModel::class, 'log.operator_id  = staff.staff_info_id', 'staff');
            $builder->where($conditions, $bind);
            $builder->columns('count(1) as count');
            $count = $builder->getQuery()->getSingleResult()->toArray();
            $num   = $count['count'] ?? 0;
            $builder->columns([
                'log.id',
                'log.type',
                'log.business',
                'log.year_month',
                'log.created_at',
                'log.remark',
                'log.operator_id',
                'log.company_id',
                'log.staff_info_id',
                'staff.name',
            ]);

            $builder->limit($size, $offset);
            $builder->orderBy('log.id desc');
            $list = $builder->getQuery()->execute()->toArray();

            $add_hour      = $this->config->application->add_hour;
            $business_type = self::$business_type;
            $operate_type  = self::$operate_type;
            //格式化内容
            foreach ($list as $k => &$v) {
                $v['staff_text']    = $v['name'] . "({$v['operator_id']})";
                $v['operator_time'] = date('Y-m-d H:i:s', (strtotime($v['created_at']) + $add_hour * 3600));
                $v['business_text'] = self::$t->_($business_type[$v['business']]);
                if (empty($v['remark'])) {
                    $v['operate_type_text'] = self::$t->_($operate_type[$v['type']]['title']);
                } else {
                    $v['operate_type_text'] = self::$t->_($operate_type[$v['type']]['title']) . ' ' . $v['remark'];
                }
                if (StaffPayrollReportOperateLogModel::operate_type_14 == $v['type']) {
                    if (empty($v['staff_info_id'])) {
                        $v['operate_type_text'] = self::$t->_($operate_type[$v['type']]['title']);
                    } else {
                        $v['operate_type_text'] = self::$t->_($operate_type[$v['type']]['title']) . '-' . $v['staff_info_id'];
                    }
                }
                $v['company_name'] = SalaryEnums::$companyMap[$v['company_id']] ?? '';
            }

            $data['list']  = $list;
            $data['count'] = $num;
        } catch (\Exception $e) {
            $this->logger->error("getList：" . $e->getMessage() . ' paramIn => ' . json_encode($paramIn));
            return ['code' => ErrCode::SYSTEM_ERROR, 'message' => $e->getMessage(), 'data' => $data];
        }
        return ['code' => ErrCode::SUCCESS, 'message' => 'success', 'data' => $data];
    }
}