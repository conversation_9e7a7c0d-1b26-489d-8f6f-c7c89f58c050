<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use app\models\backyard\HrProbationActValuesModel;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\HrProbationTargetBusinessModel;
use App\Models\backyard\HrProbationTargetDetailModel;
use App\Models\backyard\HrProbationTargetMessageModel;
use App\Models\backyard\HrProbationTargetModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\SettingEnvModel;
use App\Repository\HrProbationTargetRepository;
use Exception;
use Mpdf\Tag\Hr;

class ProbationTargetService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取目标设定列表
     * @param $params
     * @param bool $isExport
     * @return array
     */
    public function getList($params, $isExport = false): array
    {
        $pageNum  = $params['page_num'] ?? 1;
        $pageSize = $params['page_size'] ?? 100;
        $offset   = $pageSize * ($pageNum - 1);

        $result = [];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as count');
        $builder->from(['target' => HrProbationTargetModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'target.staff_info_id = staff.staff_info_id', 'staff');
        $builder->leftJoin(HrProbationModel::class, 'target.staff_info_id = probation.staff_info_id', 'probation');
        $builder->leftjoin(HrProbationTargetDetailModel::class,
            " detail.id = (SELECT max( detail.id ) as sa_id FROM ".HrProbationTargetDetailModel::class." detail  WHERE detail.target_id = target.id GROUP BY detail.target_id) ",
            "detail");

        //格式化查询条件
        $this->filterWhere($builder, $params);
        $count           = $builder->getQuery()->getSingleResult();
        $result['total'] = (int)$count->count ?? 0;

        //查询列表
        $builder->columns([
            'target.id as target_id',
            'target.staff_info_id as staff_info_id',
            'target.setting_state as setting_state',
            'target.send_state as send_state',
            'target.sign_state as sign_state',
            'staff.name as staff_name',
            'staff.state as staff_state',
            'staff.node_department_id as department_id',
            'staff.hire_date as hire_date',
            'staff.working_country as working_country',
            'staff.job_title as job_title_id',
            'staff.sys_store_id as store_id',
            'staff.manger as manager_staff_id',
            'staff.wait_leave_state as wait_leave_state',
            'probation.first_audit_status as first_audit_status',
            'probation.second_audit_status as second_audit_status',
            'probation.first_evaluate_start as first_evaluate_start',
            'probation.first_evaluate_end as first_evaluate_end',
            'detail.target_id as detail_target_id',
            'detail.stage as detail_stage',
            'detail.setting_state as detail_setting_state',
        ]);

        $builder->limit($pageSize, $offset);

        $builder->orderBy('staff.hire_date DESC,target.id DESC');
        $list = $builder->getQuery()->execute()->toArray();

        $result['list'] = $this->formatListInfo($list, $isExport);
        return $result;
    }

    /**
     * 获取试用期列表数据权限SQL条件
     * 根据用户身份和选项卡状态生成对应的数据权限过滤条件
     * @param int $userId 用户ID
     * @param int $tabSettingState 选项卡状态（对应HrProbationTargetModel::TAB_SETTING_STATE_*常量）
     * @param bool $isRpc 是否为RPC调用（影响角色权限判断）
     *
     * @return array 返回包含以下键值的数组：
     *      - builder_sql: SQL条件语句数组（使用OR连接）
     *      - builder_bind: SQL绑定参数数组
     *      当无权限时返回空条件，数据为空数组
     */
    public function getProbationAuthSql($userId, $tabSettingState, $isRpc = false): array
    {
        $returnData = [
            'builder_sql'  => [],
            'builder_bind' => [],
        ];

        //没有tab选择默认为空数据
        if (!in_array($tabSettingState, array_keys(HrProbationTargetModel::$tab_setting_state_list))) {
            $returnData['builder_sql'][]                 = "staff.staff_info_id = :staff_info_id:";
            $returnData['builder_bind']['staff_info_id'] = 0;
            return $returnData;
        }

        //是否需要角色设置
        $roleFix = true;
        if ($isRpc) {
            $roleFix = false;
        }

        //获取权限
        $purview = (new StaffPermissionService())->getProbationAuth($userId, $roleFix);

        $this->logger->info([
            'title'  => '试用期列表-数据权限',
            'opt'    => '开始记录日志',
            'func'   => 'getProbationAuthSql',
            'params' => $purview,
        ]);

        if (is_string($purview) && $purview == 'ALL') {
            return $returnData;
        }

        //管辖网点
        if (!empty($purview['stores'])) {
            if (in_array(Enums::ALL_STORE_PERMISSION, $purview['stores'])) {
                $returnData['builder_sql'][]                = " ( staff.sys_store_id NOT IN ({sys_store_id:array}) ) ";
                $returnData['builder_bind']['sys_store_id'] = [Enums\GlobalEnums::HEAD_OFFICE_ID];
            } else {
                //获取最新网点
                $storeIds = array_intersect($purview['stores'], $this->getStaffStoreIds());

                if (empty($storeIds)) {
                    //未匹配搭配 ，还用之前配置的数据
                    $storeIds = $purview['stores'];
                }

                $returnData['builder_sql'][]                = " ( staff.sys_store_id IN ({sys_store_id:array}) ) ";
                $returnData['builder_bind']['sys_store_id'] = array_values($storeIds);
            }
        }

        //部门负责人可以查看自己部门所属及以下的范围
        if (!empty($purview['departments'])) {
            $returnData['builder_sql'][]                      = " ( staff.node_department_id IN ({node_department_id:array}) ) ";
            $returnData['builder_bind']['node_department_id'] = $purview['departments'];
        }

        //管辖部门
        if (!empty($purview['flash_home_departments'])) {
            $returnData['builder_sql'][]                          = " ( staff.node_department_id IN ({flash_home_departments:array}) and staff.sys_store_id = ".Enums\GlobalEnums::HEAD_OFFICE_ID.")";
            $returnData['builder_bind']['flash_home_departments'] = $purview['flash_home_departments'];
        }

        //管辖下级
        if (!empty($purview['staff_ids'])) {
            $returnData['builder_sql'][]             = " ( staff.staff_info_id IN ({staff_ids:array}) )";
            $returnData['builder_bind']['staff_ids'] = $purview['staff_ids'];
        }

        //无权限
        if (empty($returnData['builder_sql'])) {
            $returnData['builder_sql'][]                 = "staff.staff_info_id = :staff_info_id:";
            $returnData['builder_bind']['staff_info_id'] = 0;
        }

        $this->logger->info([
            'title'  => '试用期列表-数据权限',
            'opt'    => '结束日志',
            'func'   => 'getProbationAuthSql',
            'params' => $returnData,
        ]);

        return $returnData;
    }

    /**
     * 获取员工所有的网点
     * @return array
     */
    public function getStaffStoreIds(): array
    {
        $staffInfoList = HrStaffInfoModel::find([
            'columns'    => 'DISTINCT(sys_store_id) as sys_store_id',
            'conditions' => 'formal in ({formals:array}) and is_sub_staff = :is_sub_staff:',
            'bind'       => [
                'formals' => [HrStaffInfoModel::FORMAL_1,HrStaffInfoModel::FORMAL_INTERN],
                'is_sub_staff' => HrStaffInfoModel::IS_SUB_STAFF_NO,
            ],
        ])->toArray();

        return array_column($staffInfoList, 'sys_store_id');
    }


    /**
     * 列表条件处理
     * @param $builder
     * @param $params
     * @return mixed
     */
    protected function filterWhere($builder, $params)
    {
        $tabSettingState = $params['tab_setting_state'] ?? 0;

        //数据权限
        $permissionSql = $this->getProbationAuthSql($params['user_id'], $tabSettingState,
            $params['is_rpc'] ?? false);//列表

        if (!empty($permissionSql['builder_sql']) && !empty($permissionSql['builder_bind'])) {
            $builder->andWhere(implode(' or ', $permissionSql['builder_sql']), $permissionSql['builder_bind']);
        }

        //员工工号
        if (!empty($params['staff_info_id'])) {
            $builder->andWhere("target.staff_info_id = :staff_info_id:", ['staff_info_id' => $params['staff_info_id']]);
        }

        //员工名称
        if (!empty($params['staff_name'])) {
            $builder->andWhere("staff.name LIKE :staff_name:", ['staff_name' => "%{$params['staff_name']}%"]);
        }

        //所属部门
        if (!empty($params['department_id'])) {
            $deptIds = [];
            if (!empty($params['is_sub_department'])) {
                $deptIds = (new SysDepartmentService())->getChildrenListByDepartmentId($params['department_id'], true);
            }

            $deptIds[] = $params['department_id'];

            $builder->andWhere("staff.node_department_id IN ({department_ids:array})", ['department_ids' => $deptIds]);
        }

        //网点
        if (!empty($params['store_id'])) {
            $builder->andWhere("staff.sys_store_id IN ({store_id:array})",
                ['store_id' => array_values($params['store_id'])]);
        }

        //在职状态
        if (!empty($params['staff_state'])) {
            if (in_array(HrStaffInfoModel::STATE_PENDING_RESIGNATION, $params['staff_state'])) {
                $builder->andWhere('(staff.state in ({staff_state:array}) AND staff.wait_leave_state = 0) OR (staff.state = 1 AND staff.wait_leave_state = 1)',
                    ['staff_state' => $params['staff_state']]);
            } else {
                $builder->andWhere('staff.state in ({staff_state:array}) AND  staff.wait_leave_state = 0',
                    ['staff_state' => $params['staff_state']]);
            }
        }

        //职位
        if (!empty($params['job_title_id'])) {
            $builder->andWhere("staff.job_title IN ({job_title:array})",
                ['job_title' => array_values($params['job_title_id'])]);
        }

        //直线上级
        if (!empty($params['manage_staff_id'])) {
            $builder->andWhere("staff.manger = :manage_staff_id:", ['manage_staff_id' => $params['manage_staff_id']]);
        }

        //入职日期
        if (!empty($params['hire_date_start'])) {
            $builder->andWhere("staff.hire_date >= :hire_date_start:",
                ['hire_date_start' => $params['hire_date_start']]);
        }

        //入职日期
        if (!empty($params['hire_date_end'])) {
            $builder->andWhere("staff.hire_date <= :hire_date_end:", ['hire_date_end' => $params['hire_date_end']]);
        }

        //设置状态
        if ($tabSettingState == HrProbationTargetModel::TAB_SETTING_STATE_FINISH) {
            $tabSettingStateArr = [
                HrProbationTargetModel::SETTING_STATE_FINISH,
                HrProbationTargetModel::SETTING_STATE_ADJUST,
            ];

            if (in_array($params['setting_state'], $tabSettingStateArr)) {
                $tabSettingStateArr = [$params['setting_state']];
            }

            $builder->andWhere("target.setting_state IN ({setting_state:array})",
                ['setting_state' => array_values($tabSettingStateArr)]);
        } else {
            $builder->andWhere("target.setting_state = :setting_state:",
                ['setting_state' => HrProbationTargetModel::SETTING_STATE_NOT_START]);
        }

        //发送
        if (!empty($params['send_state'])) {
            $builder->andWhere("target.send_state = :send_state:", ['send_state' => $params['send_state']]);
        }

        //签字
        if (!empty($params['sign_state'])) {
            $builder->andWhere("target.sign_state = :sign_state:", ['sign_state' => $params['sign_state']]);
        }

        //工作所在国家
        if (!empty($params['working_country'])) {
            $builder->andWhere("staff.working_country IN ({working_country:array})",
                ['working_country' => array_values($params['working_country'])]);
        }

        $builder->andWhere("target.is_deleted = :is_deleted:", ['is_deleted' => GlobalEnums::NO_DELETED]);

        return $builder;
    }

    /**
     * 格式化数据
     * @param $list
     * @param bool $isExport
     * @return mixed
     * 目标完成设置时间（导出时最新时间）
     * 目标发送时间（导出时最新时间）
     * 员工签字时间（导出时员工最后一次签字）
     * 上级签字时间（导出时上级最后一次签字）
     * 调整后目标发送时间（导出时最新时间，如没有调整留空）
     * 调整后员工签字时间（导出时员工调整后最后一次签字）
     * 调整后上级签字时间（导出时上级调整后最后一次签字）
     */
    public function formatListInfo($list, $isExport = false)
    {
        $managerIds                  = array_values(array_unique(array_column($list, 'manager_staff_id')));
        $hrProbationTargetRepository = (new HrProbationTargetRepository());
        $probationTargetTools        = (new ProbationTargetToolsService());

        $managerList = [];
        if ($managerIds) {
            $managerList = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,name',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $managerIds],
            ])->toArray();

            $managerList = array_column($managerList, 'name', 'staff_info_id');
        }

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        $targetIds = array_unique(array_column($list, 'target_id'));

        $targetDetailList = $targetBusinessList = [];
        //计算发送日期
        if ($isExport) {
            //导出发送时间，设置完成时间
            $targetDetailList = $hrProbationTargetRepository->getTargetDetailListByTargetIds($targetIds,
                'target_id,stage,setting_time,send_time');
            $targetDetailList = $probationTargetTools->formatDetailKy($targetDetailList);

            //导出签字时间
            $targetBusinessList = $hrProbationTargetRepository->getTargetBusinessListByTargetIds($targetIds,
                'id,target_id,stage,manager_sign_time,staff_state,manager_state,manager_sign_time,staff_sign_time');
            $targetBusinessList = $probationTargetTools->formatBusinessKy($targetBusinessList);
        }

        foreach ($list as & $v) {
            $v['staff_info_id']        = $v['staff_info_id'] ?? '';
            $v['staff_name']           = $v['staff_name'] ?? '';
            $v['department_id']        = $v['department_id'] ?? '0';
            $v['department_name']      = $this->showDepartmentName($v['department_id']);
            $v['job_title_id']         = $v['job_title_id'] ?? '0';
            $v['job_title_name']       = $this->showJobTitleName($v['job_title_id']);
            $v['store_id']             = $v['store_id'] ?? '';
            $v['store_name']           = $this->showStoreName($v['store_id']);
            $v['staff_state_name']     = $probationTargetTools->getStateName($v['staff_state'], $v['wait_leave_state']);
            $v['hire_date']            = formatHrDate($v['hire_date'] ?? '');
            $v['first_evaluate_start'] = $v['first_evaluate_start'] ?? '';
            $v['first_evaluate_end']   = $v['first_evaluate_end'] ?? '';

            //工号
            $v['manager_staff_name'] = $managerList[$v['manager_staff_id']] ?? '';

            //业务状态    1已设置 2未开始 3已调整
            $v['setting_state_name'] = self::$t->_(HrProbationTargetModel::$setting_state_list[$v['setting_state']] ?? '');
            //发送状态  1已发送 2未发送 3调整后已发送
            $v['send_state_name'] = self::$t->_(HrProbationTargetModel::$send_state_list[$v['send_state']] ?? '');
            //签字状态  1已完成 2未开始 3进行中 4调整后未开始 5调整后进行中 6调整后已完成
            $v['sign_state_name'] = self::$t->_(HrProbationTargetModel::$sign_state_list[$v['sign_state']] ?? '');

            //工作所在国家
            $v['working_country']      = $v['working_country'] ?? '';
            $v['working_country_name'] = $workingCountryList[$v['working_country']] ?? '';

            //评估状态
            $v['first_audit_status']  = strval($v['first_audit_status'] ?? HrProbationModel::FIRST_AUDIT_STATUS_WAIT);
            $v['second_audit_status'] = strval($v['second_audit_status'] ?? HrProbationModel::SECOND_AUDIT_STATUS_WAIT);

            //评估状态
            $v['first_audit_status_name']  = self::$t->_('hr_probation_audit_status_'.$v['first_audit_status']);
            $v['second_audit_status_name'] = self::$t->_('hr_probation_audit_status_'.$v['second_audit_status']);

            //设置状态
            $v['detail_stage']         = $v['detail_stage'] ?? (string)HrProbationTargetDetailModel::STAGE_FIRST;
            $v['detail_setting_state'] = $v['detail_setting_state'] ?? (string)HrProbationTargetDetailModel::SETTING_STATE_NOT_START;

            //导出添加时间
            if ($isExport) {
                $v['first_setting_time'] = show_time_zone($targetDetailList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['setting_time'] ?? '');

                $v['first_send_time']          = '';
                $v['first_staff_sign_time']    = '';
                $v['first_manager_sign_time']  = '';
                $v['second_send_time']         = '';
                $v['second_staff_sign_time']   = '';
                $v['second_manager_sign_time'] = '';

                //调整前
                if (in_array($v['send_state'], [HrProbationTargetModel::SEND_STATE_FINISH, HrProbationTargetModel::SEND_STATE_ADJUST_FINISH])) {
                    $v['first_send_time'] = show_time_zone($targetDetailList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['send_time'] ?? '');

                    //验证签字状态
                    if (
                        isset($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['staff_state'])
                        && $targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS
                    ) {
                        $v['first_staff_sign_time'] = show_time_zone($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['staff_sign_time'] ?? '');
                    }

                    if (
                        isset($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['manager_state'])
                        && $targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['manager_state'] == HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS
                    ) {
                        $v['first_manager_sign_time'] = show_time_zone($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_FIRST]['manager_sign_time'] ?? '');
                    }

                    //调整后
                    if ($v['setting_state'] == HrProbationTargetModel::SETTING_STATE_ADJUST) {
                        $v['second_send_time'] = show_time_zone($targetDetailList[$v['target_id']][HrProbationTargetDetailModel::STAGE_SECOND]['send_time'] ?? '');

                        if (
                            isset($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_SECOND]['staff_state'])
                            && $targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_SECOND]['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS
                        ) {
                            $v['second_staff_sign_time'] = show_time_zone($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_SECOND]['staff_sign_time'] ?? '');
                        }

                        if (
                            isset($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_SECOND]['manager_state'])
                            && $targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_SECOND]['manager_state'] == HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS
                        ) {
                            $v['second_manager_sign_time'] = show_time_zone($targetBusinessList[$v['target_id']][HrProbationTargetDetailModel::STAGE_SECOND]['manager_sign_time'] ?? '');
                        }
                    }
                }
            }
        }

        return $list;
    }


    /**
     * 目标导出
     * @param $params
     * @return string
     * @throws \OSS\Core\OssException
     */
    public function exportList($params): string
    {
        $params['page_size'] = 500;
        $params['page_num']  = 1;

        $exportData = [];
        while (true) {
            // 获取数据
            $result = $this->getList($params, true);

            $list = $result['list'];

            if (empty($list)) {
                break;
            }

            foreach ($list as $key => $val) {
                $exportData[] = [
                    $val['staff_info_id'],                       // 员工工号
                    $val['department_name'],                     // 所属部门
                    $val['job_title_name'],                      // 职位
                    $val['staff_state_name'],                    // 在职状态
                    $val['hire_date'],                           // 入职日期
                    $val['manager_staff_id'],                    // 直线上级工号
                    $val['manager_staff_name'],                  // 直线上级姓名
                    $val['setting_state_name'],                  // 目标设定状态
                    $val['sign_state_name'],                     // 签字状态
                    $val['send_state_name'],                     // 发送状态
                    $val['first_setting_time'],                  // 目标完成设置时间
                    $val['first_send_time'],                     // 目标发送时间
                    $val['first_staff_sign_time'],               // 员工签字时间
                    $val['first_manager_sign_time'],             // 上级签字时间
                    $val['second_send_time'],                    // 调整后目标发送时间
                    $val['second_staff_sign_time'],              // 调整后员工签字时间
                    $val['second_manager_sign_time'],            // 调整后上级签字时间
                ];
            }

            ++$params['page_num'];
        }

        //表头
        $head = [
            self::$t->_('staff_id'),                               //工号
            self::$t->_('node_department_name'),                   //所属部门
            self::$t->_('job_title'),                              //职位
            self::$t->_('staff_state'),                            //在职状态
            self::$t->_('hire_date'),                              //入职日期
            self::$t->_('manager_staff_id'),                       //直线上级工号
            self::$t->_('manager_staff_name'),                     //直线上级姓名
            self::$t->_('probation_target_setting_state_name'),    //设置状态
            self::$t->_('probation_target_sign_state_name'),       //签字状态
            self::$t->_('probation_target_send_state_name'),       //发送状态
            self::$t->_('first_setting_time_name'),                //目标完成设置时间
            self::$t->_('first_send_time_name'),                   //目标发送时间
            self::$t->_('first_staff_sign_time_name'),             //员工签字时间
            self::$t->_('first_manager_sign_time_name'),           //上级签字时间
            self::$t->_('second_send_time_name'),                  //调整后目标发送时间
            self::$t->_('second_staff_sign_time_name'),            //调整后员工签字时间
            self::$t->_('second_manager_sign_time_name'),          //调整后上级签字时间
        ];

        // 上传到oss存储
        $fileName  = $params['file_name'];
        $excelFile = $this->exportExcel($head, $exportData, $fileName);

        $flashOss    = new FlashOss();
        $jsonUrlName = env('country_code').'/probation/'.$fileName;
        $flashOss->uploadFile($jsonUrlName, $excelFile['data']);
        return $jsonUrlName;
    }

    /**
     * 保存目标
     * @param $params
     * @return true
     * @throws ValidationException
     * @throws BusinessException
     * @throws Exception
     */
    public function save($params): bool
    {
        $this->logger->info([
            'title'  => '用户设置目标',
            'opt'    => '开始执行',
            'func'   => 'save',
            'params' => $params,
        ]);

        $hrProbationTargetRepository = (new HrProbationTargetRepository());

        //组装数据
        $data = [
            'staff_info_id' => $params['staff_info_id'],
            'stage'         => (int)$params['stage'],
            'duty_info'     => $params['duty_info'] ?? '',
        ];

        if (!empty($params['target_info'])) {
            $widgetList = array_column($params['target_info'], 'weight');
            $sum        = array_sum($widgetList);

            if ($sum > HrProbationTargetModel::WIDGET_FINIS) {
                throw new ValidationException(self::$t->_('probation_weight_sum_error'));
            }

            $data['target_info'] = json_encode($params['target_info'], JSON_UNESCAPED_UNICODE);
        }

        //获取员工数据
        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($params['staff_info_id']);

        //离职校验
        if (empty($staffInfo) || ($staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN)) {
            throw new ValidationException(self::$t->_('staff_data_resign_not_operate'));
        }

        //获取Target
        $targetInfo = $hrProbationTargetRepository->getTargetInfoByStaffId($params['staff_info_id']);

        if (empty($targetInfo)) {
            throw new ValidationException(self::$t->_('target_data_does_no_exist'));
        }

        //用户第一阶段已经发送不可以再次编辑必须撤回
        if (
            ($targetInfo['send_state'] == HrProbationTargetDetailModel::SEND_STATE_FINISH)
            && ($params['stage'] == HrProbationTargetDetailModel::STAGE_FIRST)
        ) {
            throw new ValidationException(self::$t->_('target_data_send_state_finish_not_setting'));
        }

        //第二阶段 - 发送状态为 「已发送」
        if (
            $params['stage'] == HrProbationTargetDetailModel::STAGE_SECOND
            && ($targetInfo['send_state'] != HrProbationTargetDetailModel::SEND_STATE_FINISH)
        ) {
            throw new ValidationException(self::$t->_('target_data_state_no_finish_not_operate'));
        }

        //第二阶段必须目标设定状态为「已完成」
        if (
            $params['stage'] == HrProbationTargetDetailModel::STAGE_SECOND
            && ($targetInfo['setting_state'] != HrProbationTargetDetailModel::SETTING_STATE_FINISH)
        ) {
            throw new ValidationException(self::$t->_('target_data_state_no_finish_not_operate'));
        }

        //转正数据验证
        $info = $hrProbationTargetRepository->getProbationInfoByStaffId($params['staff_info_id']);

        if (!empty($info)) {
            //用户已经转正
            if ($info['status'] == HrProbationModel::STATUS_CORRECTED) {
                throw new ValidationException(self::$t->_('staff_probation_state_corrected_not_operate'));
            }

            //第一阶段评估状态=待发起
            if (
                ($info['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT)
                && ($params['stage'] == HrProbationTargetDetailModel::STAGE_FIRST)
            ) {
                throw new ValidationException(self::$t->_('staff_first_audit_status_wait_not_operate'));
            }

            //第二阶段转正状态=待发起
            if ($params['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                if (($info['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_DONE)) {
                    throw new ValidationException(self::$t->_('target_first_evaluat_state_error_not_setting'));
                }

                //验证必须试用期7天
                $firstEvaluateEnd = $info['first_evaluate_end'];
                $firstEvaluateEnd = strtotime("{$firstEvaluateEnd} + 7 days");

                if (time() >= $firstEvaluateEnd) {
                    throw new ValidationException(self::$t->_('target_data_time_first_evaluat_over_not_setting'));
                }
            }
        }

        $this->logger->info([
            'title'  => '用户设置目标',
            'opt'    => '验证通过',
            'func'   => 'save',
            'parmas' => [
                'staff_info_id' => $params['staff_info_id'],
                'stage'         => $params['stage'],
            ],
        ]);

        $db = $this->getDI()->get("db_backyard");
        $db->begin();

        try {
            //获取最新的数据
            $info = $hrProbationTargetRepository->getTargetDetailInfoByTargetId($targetInfo['id'], $params['stage']);

            //数据已发送不能调整
            if ($info && $info['send_state'] == HrProbationTargetDetailModel::SEND_STATE_FINISH)
            {
                throw new ValidationException(self::$t->_('target_data_state_no_finish_not_operate'));
            }

            //保存详情表数据
            $targetDetailRes = $hrProbationTargetRepository->saveTargetDetail(
                array_merge(
                    [
                        'id'        => $info['id'] ?? 0,
                        'target_id' => $targetInfo['id'],
                    ],
                    $data
                )
            );

            //保存失败返回错误
            if (!$targetDetailRes) {
                throw new Exception('Probation Target Detail Save Error ');
            }

            if (!$db->commit()) {
                throw new Exception('Common Save Error ');
            }

            $this->logger->info([
                'title' => '用户设置目标',
                'opt'   => '保存完成',
                'func'  => 'save',
                'res'   => $targetDetailRes,
            ]);
        } catch (Exception $exception) {
            $db->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * 获取详情数据
     * @throws ValidationException
     */
    public function detail($params): array
    {
        $staffInfoId = $params['staff_info_id'] ?? 0;
        $stage       = $params['stage'] ?? 0;
        $userId      = $params['user_id'] ?? 0;

        //获取员工级别信息
        $staffInfo = (new StaffInfoService())->getStaffInfoOne($staffInfoId);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('operation_data_does_no_exist'));
        }

        //deadlineDate
        $deadlineDate = [
            'first_deadline_date'  => '',
            'second_deadline_date' => '',
        ];

        //试用期相关
        $probationInfo = (new HrProbationTargetRepository())->getProbationInfoByStaffId($staffInfoId);

        if (!empty($probationInfo['first_evaluate_start']) && !empty($probationInfo['first_evaluate_end'])) {
            $deadlineDate['first_deadline_date'] = $probationInfo['first_evaluate_start'].'——'.$probationInfo['first_evaluate_end'];
        }

        if (!empty($probationInfo['second_evaluate_start']) && !empty($probationInfo['second_evaluate_end'])) {
            $deadlineDate['second_deadline_date'] = $probationInfo['second_evaluate_start'].'——'.$probationInfo['second_evaluate_end'];
        }

        //获取目标详情
        $targetDetail = (new HrProbationTargetRepository())->getTargetDetailInfoByStaffId($staffInfoId, $stage);

        //获取价值观版本号
        $actVersion = HrProbationActValuesModel::CURRENT_VERSION;

        //已发送获取固化的，未发送/撤回获取最新的，默认为最新的
        if ($targetDetail['send_state'] == HrProbationTargetDetailModel::SEND_STATE_FINISH) {
            $actVersion = intval($targetDetail['act_version']);
        }

        //目标详情获取
        $info = $this->formatDetail($staffInfo, $deadlineDate, $targetDetail, $actVersion, $stage);

        if (empty($params['is_rpc'])) {
            //处理用户是否有职级白名单权限
            $hr_rpc = (new ApiClient('hris', '', 'has_grade_permission_tab', 'th'));
            $hr_rpc->setParams([["staff_info_id" => $staffInfoId, "fbid" => $userId]]);
            $res = $hr_rpc->execute();

            if (!isset($res['body']) || ($res['body'] != 1)) {
                $info['job_title_grade_v2']   = '';
                $info['job_title_grade_name'] = '';
            }
        }

        //补充数据
        $targetInfo = (new HrProbationTargetRepository())->getTargetInfoByStaffId($staffInfoId);

        //补充发送按钮判断字段逻辑
        $info['setting_state']         = $targetInfo['setting_state'];
        $info['send_state']            = $targetInfo['send_state'];
        $info['sign_state']            = $targetInfo['sign_state'];
        $info['first_evaluate_start']  = $probationInfo['first_evaluate_start'] ?? '';
        $info['first_evaluate_end']    = $probationInfo['first_evaluate_end'] ?? '';
        $info['second_evaluate_start'] = $probationInfo['second_evaluate_start'] ?? '';
        $info['second_evaluate_end']   = $probationInfo['second_evaluate_end'] ?? '';

        return $info;
    }

    /**
     * 格式化目标详情数据
     * @param $staffInfo
     * @param $probationInfo
     * @param $targetDetail
     * @return array
     */
    public function formatDetail(
        $staffInfo,
        $probationInfo,
        $targetDetail,
        $actVersion,
        $stage = HrProbationTargetDetailModel::STAGE_FIRST
    ): array {
        $probationTargetToolsService = (new ProbationTargetToolsService());

        $this->logger->info([
            'title' => '格式化目标详情',
            'opt'   => '开始',
            'func'  => 'formatDetail',
            'res'   => [
                'staffInfo'     => $staffInfo,
                'probationInfo' => $probationInfo,
                'actVersion'    => $actVersion,
            ],
        ]);

        //格式化用户信息
        $returnData = $probationTargetToolsService->formatStaffInfo($staffInfo, $probationInfo);

        $returnData['duty_info'] = $targetDetail['duty_info'] ?? '';

        //获取详情 详情不存在默认为第一阶段
        if (empty($targetDetail)) {
            $returnData['stage'] = $stage ?: HrProbationTargetDetailModel::STAGE_FIRST;
        } else {
            $returnData['stage'] = $targetDetail['stage'] ?? HrProbationTargetDetailModel::STAGE_FIRST;
        }

        $returnData['stage'] = (string) $returnData['stage'];

        $returnData['target_info'] = !empty($targetDetail['target_info']) ? json_decode($targetDetail['target_info'],
            true) : [];

        //插入目标说明 - 不区分版本都存在
        $returnData['probation_target_setting_title'] = self::$t->_('probation_target_setting_title');
        $returnData['probation_target_setting_remark'] = self::$t->_('probation_target_setting_remark');

        //插入价值观详细数据 按照版本获取
        $returnData['probation_concept_data'] = $this->getProbationActValuesForConcept($actVersion) ?: null;

        return $returnData;
    }

    /**
     * 获取试用期价值观配置数据
     *
     * 用于获取当前版本的试用期价值观评估标准，包含：
     * - 核心价值类型
     * - 行为规范分类
     * - 能力评估维度
     *
     * @param int $version 价值观版本号（默认使用HrProbationActValuesModel::CURRENT_VERSION）
     *
     * @return array 返回格式化后的价值观配置数组，结构包含：
     *               - probation_concept_remark: 价值观说明文本
     *               - probation_target_values: 按概念类型分组的价值观列表
     *               - behavior_list: 具体行为规范列表
     */
    public function getProbationActValuesForConcept($version = 1)
    {
        // 如果版本号为空，直接返回空数组
        $list = $this->getProbationActValues($version);

        if (empty($list)) {
            return [];
        }

        // 初始化用于存储格式化后数据的数组
        $conceptData = [];

        // 遍历查询结果，按 concept_type 分类并格式化数据
        foreach ($list as $item) {
            $info = [];

            // 行为类型及行为名称（翻译）
            $info['behavior_type'] = $item['behavior_type'] ?? '';
            $info['grade']         = $item['grade'] ?? '';
            $info['behavior_name'] = self::$t->_('probation_behavior_type_'.$item['behavior_type']);

            // 概念类型及名称（从模型静态变量中获取翻译）
            $conceptTypeKey                               = $item['concept_type'];
            $conceptData[$conceptTypeKey]['concept_type'] = $item['concept_type'] ?? '';
            $conceptData[$conceptTypeKey]['concept_name'] = self::$t->_(HrProbationActValuesModel::$concept_type_list[$item['concept_type']]);

            // 将当前行为加入对应的概念类型下
            $conceptData[$conceptTypeKey]['behavior_list'][] = $info;
        }

        // 返回最终组装的数据结构
        return [
            // 翻译后的模块说明
            'probation_concept_remark' => self::$t->_('probation_concept_remark'),
            // 转换为索引数组以保持顺序一致性
            'probation_target_values'  => array_values($conceptData),
        ];
    }

    /**
     * 获取试用期价值观配置数据
     * 用于获取当前版本的试用期价值观评估标准 - 二维数组
     * - 核心价值类型
     * - 行为规范分类
     * - 能力评估维度
     */
    public function getProbationActValuesForList($version = 1 , $lang = 'th')
    {
        $t    = $this->getTranslation($lang);
        $list = $this->getProbationActValues($version);

        if (empty($list)) {
            return [];
        }

        // 初始化用于存储格式化后数据的数组
        $conceptData = [];

        // 遍历查询结果，按 concept_type 分类并格式化数据
        foreach ($list as $item) {
            $info = [];

            // 行为类型及行为名称（翻译）
            $info['behavior_type'] = $item['behavior_type'] ?? '';
            $info['grade']         = $item['grade'] ?? '';
            $info['behavior_name'] = $t->_('probation_behavior_type_'.$item['behavior_type']);
            $info['concept_type']  = $item['concept_type'] ?? '';
            $info['concept_name']  = $t->_(HrProbationActValuesModel::$concept_type_list[$item['concept_type']]);

            // 将当前行为加入对应的概念类型下
            $conceptData[] = $info;
        }

        return $conceptData;
    }

    /**
     * 获取试用期价值观配置数据
     * @param int $version 价值观版本号（默认使用HrProbationActValuesModel::CURRENT_VERSION）
     * @return array 值返回格式化后的价值观配置数组
     **/
    public function getProbationActValues($version)
    {
        // 如果版本号为空，直接返回空数组
        if (!$version) {
            return [];
        }

        // 查询 HrProbationActValuesModel 中未删除且匹配版本的数据
        return HrProbationActValuesModel::find([
            'columns'    => 'concept_type,behavior_type,id,grade,version',
            'conditions' => 'is_deleted = :is_deleted: AND version = :version:',
            'bind'       => [
                'is_deleted' => GlobalEnums::NO_DELETED,
                'version'    => $version,
            ],
            'order'      => 'id ASC', // 按ID升序排列
        ])->toArray();
    }

    /**
     * 发送报告
     * @return true
     * @throws ValidationException
     * @throws BusinessException
     */
    public function send($params): bool
    {
        $this->logger->info(['title' => '用户发送目标', 'opt' => '开始执行', 'func' => 'send']);

        $staffInfoId                 = $params['staff_info_id'] ?? 0;
        $userId                      = $params['user_id'] ?? 0;
        $hrProbationTargetRepository = (new HrProbationTargetRepository());
        $date                        = gmdate('Y-m-d H:i:s');

        if (!$userId) {
            throw new ValidationException(self::$t->_('login_timeout'));
        }

        //员工信息验证
        $staffInfo = (new StaffInfoService())->getStaffInfoOne($staffInfoId);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('staff_info_does_not_exist'));
        }

        //验证数据
        //目标设定状态为「已完成」且 发送状态为 「未发送」的记录可以点击发送按钮
        $targetInfo = $hrProbationTargetRepository->getTargetInfoByStaffId($staffInfoId);

        if (empty($targetInfo)) {
            throw new ValidationException(self::$t->_('target_data_does_no_exist'));
        }

        //获取最新的
        $targetDetailInfo = $hrProbationTargetRepository->getTargetDetailInfoByTargetId($targetInfo['id']);

        //目标详情不存在
        if (empty($targetDetailInfo)) {
            throw new ValidationException(self::$t->_('target_data_no_setting_error'));
        }

        //目标未设置完成
        if ($targetDetailInfo['setting_state'] != HrProbationTargetModel::SETTING_STATE_FINISH) {
            throw new ValidationException(self::$t->_('target_data_no_setting_finish'));
        }

        //目标已发送
        if ($targetDetailInfo['send_state'] == HrProbationTargetModel::SEND_STATE_FINISH) {
            throw new ValidationException(self::$t->_('target_data_already_send'));
        }

        //转正数据验证
        $info = $hrProbationTargetRepository->getProbationInfoByStaffId($params['staff_info_id']);

        $deadlineDate = [
            'first_deadline_date'  => '',
            'second_deadline_date' => '',
        ];

        if (!empty($info['first_evaluate_start']) && !empty($info['first_evaluate_end'])) {
            $deadlineDate['first_deadline_date'] = $info['first_evaluate_start'].'——'.$info['first_evaluate_end'];
        }

        if (!empty($info['second_evaluate_start']) && !empty($info['second_evaluate_end'])) {
            $deadlineDate['second_deadline_date'] = $info['second_evaluate_start'].'——'.$info['second_evaluate_end'];
        }

        if (!empty($info)) {
            //员工已经转正，不可以发送
            if (($info['status'] == HrProbationModel::STATUS_CORRECTED)) {
                throw new ValidationException(self::$t->_('staff_probation_state_corrected_not_operate'));
            }

            //用户第二阶段已经通过不可以发送
            if (($info['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE)) {
                throw new ValidationException(self::$t->_('staff_second_audit_status_success_not_operate'));
            }

            //用户第二阶段已经通过不可以发送
            if ($targetDetailInfo['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                if (($info['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT)) {
                    throw new ValidationException(self::$t->_('staff_second_audit_status_wait_not_operate'));
                }

                if ($info['first_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_DONE) {
                    throw new ValidationException(self::$t->_('target_first_evaluat_state_error_not_setting'));
                }

                $firstEvaluateEnd = $info['first_evaluate_end'];
                $firstEvaluateEnd = strtotime("{$firstEvaluateEnd} + 7 days");

                if ((time() >= $firstEvaluateEnd)) {
                    throw new ValidationException(self::$t->_('target_data_time_first_evaluat_over_not_send'));
                }
            }

            //用户第一阶段已经发起不可以发送第一阶段目标
            if (
                ($info['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT)
                && ($targetDetailInfo['stage'] == HrProbationTargetDetailModel::STAGE_FIRST)
            ) {
                throw new ValidationException(self::$t->_('staff_first_audit_status_wait_not_operate'));
            }
        }

        $this->logger->info(['title' => '用户发送目标', 'opt' => '验证完成', 'func' => 'send']);

        $db        = $this->getDI()->get("db_backyard");
        $messageDb = $this->getDI()->get('db_message');
        $db->begin();
        $messageDb->begin();

        try {
            //验证是否存在改目标正在执行的业务
            $targetDetailId = $targetDetailInfo['id'];

            $businessInfo = $hrProbationTargetRepository->getTargetBusinessInfo([
                'target_detail_id' => $targetDetailId,
                'state'            => HrProbationTargetBusinessModel::STATE_NORMAL,
            ]);

            //目标已经发送了 重复操作
            if ($businessInfo) {
                throw new ValidationException(self::$t->_('target_data_already_send'));
            }

            //固化基本数据
            $staffData                         = [];
            $staffData['staff_info_id']        = $staffInfo['staff_info_id'] ?? '';
            $staffData['name']                 = $staffInfo['name'] ?? '';
            $staffData['name_en']              = $staffInfo['name_en'] ?? '';
            $staffData['nick_name']            = $staffInfo['nick_name'] ?? '';
            $staffData['job_title_grade_v2']   = $staffInfo['job_title_grade_v2'] ?? '';
            $staffData['department_name']      = $staffInfo['department_name'] ?? '';
            $staffData['node_department_id']   = $staffInfo['node_department_id'] ?? '';
            $staffData['job_title_name']       = $staffInfo['job_title_name'] ?? '';
            $staffData['job_title']            = $staffInfo['job_title'] ?? '';
            $staffData['sys_store_id']         = $staffInfo['sys_store_id'] ?? '';
            $staffData['store_name']           = $staffInfo['store_name'] ?? '';
            $staffData['hire_date']            = $staffInfo['hire_date'] ?? '';
            $staffData['manger']               = $staffInfo['manger'] ?? '';
            $staffData['working_country']      = $staffInfo['working_country'] ?? '';
            $staffData['first_deadline_date']  = $deadlineDate['first_deadline_date'] ?? '';
            $staffData['second_deadline_date'] = $deadlineDate['second_deadline_date'] ?? '';

            $this->logger->info([
                'title'  => '用户发送目标',
                'opt'    => '固化员工数据完成',
                'func'   => 'send',
                'params' => $staffData,
            ]);

            //生成业务表数据
            $targetBusinessId = $hrProbationTargetRepository->saveTargetBusiness([
                'target_id'        => $targetInfo['id'],
                'target_detail_id' => $targetDetailInfo['id'],
                'staff_info_id'    => $staffInfoId,
                'stage'            => $targetDetailInfo['stage'],
                'duty_info'        => $targetDetailInfo['duty_info'],
                'target_info'      => $targetDetailInfo['target_info'],
                'staff_data'       => json_encode($staffData, JSON_UNESCAPED_UNICODE),
                'send_time'        => $date,
                'send_operate_id'  => $userId,
            ]);

            if (!$targetBusinessId) {
                throw new Exception('Probation Business Save Error ');
            }

            //员工消息标题
            $messageTitle = 'probation_target_message_title_send';

            //调整主表信息
            $targetData = [
                'id'         => $targetInfo['id'],
                'send_state' => HrProbationTargetModel::SETTING_STATE_FINISH,
                'sign_state' => HrProbationTargetModel::SETTING_STATE_NOT_START,
            ];

            if ($targetDetailInfo['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                $messageTitle                = 'probation_target_message_title_re_send';
                $targetData['setting_state'] = HrProbationTargetModel::SETTING_STATE_ADJUST;
            }

            //保存目标
            $targetRes = $hrProbationTargetRepository->saveTarget($targetData);

            if (!$targetRes) {
                throw new Exception('Probation Target Save Error ');
            }

            $this->logger->info([
                'title'  => '用户发送目标',
                'opt'    => '保存目标完成',
                'func'   => 'send',
                'params' => $targetData,
            ]);

            //调整详情表数据
            $targetDetailRes = $hrProbationTargetRepository->saveTargetDetail([
                'id'         => $targetDetailInfo['id'],
                'send_state' => HrProbationTargetModel::SETTING_STATE_FINISH,
                'sign_state' => HrProbationTargetModel::SETTING_STATE_NOT_START,
                'send_time'  => $date,
            ]);

            if (!$targetDetailRes) {
                throw new Exception('Probation Target Detail Save Error ');
            }

            //发送消息
            $messageRes = $this->sendTargetMessage([
                'staff_info_id' => $staffInfoId,
                'content'       => json_encode([
                    'business_id' => $targetBusinessId,
                    'type'        => HrProbationTargetMessageModel::TYPE_STAFF,
                ], JSON_UNESCAPED_UNICODE),
                'title'         => $messageTitle,
                'category'      => Enums\EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET'),
            ]);

            if ($messageRes[1] != 1) {
                throw new Exception('Probation Target Message Error ');
            }

            $this->logger->info([
                'title'  => '用户发送目标',
                'opt'    => '发送消息完成',
                'func'   => 'send',
                'params' => $messageRes,
            ]);

            //生成业务表数据
            $messageData = [
                'target_id'          => $targetInfo['id'],
                'target_business_id' => $targetBusinessId,
                'staff_info_id'      => $staffInfoId,
                'msg_id'             => $messageRes[2][0] ?? '',
                'type'               => HrProbationTargetMessageModel::TYPE_STAFF,
                'operate_id'         => $userId,
            ];

            $targetMessageRes = $hrProbationTargetRepository->saveTargetMessage($messageData);

            if (!$targetMessageRes) {
                throw new Exception('Probation Target Message Business Error ');
            }

            $this->logger->info([
                'title'  => '用户发送目标',
                'opt'    => '消息业务表完成',
                'func'   => 'send',
                'params' => $messageData,
            ]);

            if (!$db->commit() || !$messageDb->commit()) {
                throw new BusinessException('Common Save Error ');
            }

            //抄送数据
            $rmq = new RocketMQ('probation-target-msg');
            $rmq->setShardingKey($staffInfoId);

            $sendData = [
                'type'               => RocketMQ::TAG_PROBATION_TARGET_MSG,
                'staff_info_id'      => $staffInfoId,
                'target_business_id' => $targetBusinessId,
                'source_type'        => HrProbationTargetMessageModel::SOURCE_TYPE_SEND,
                'operate_id'         => $userId,
            ];

            $rid = $rmq->sendOrderlyMsg($sendData);
            $this->logger->write_log(
                ['title' => '用户发送目标', 'opt' => 'BP消息队列发送完成', 'func' => 'send', 'params' => $sendData],
                $rid ? 'info' : 'error');
        } catch (Exception $exception) {
            $db->rollback();
            $messageDb->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * 保存消息
     * @param $parmas
     * @return array|false
     */
    public function sendTargetMessage($parmas)
    {
        $title       = $parmas['title'] ?? '';
        $category    = $parmas['category'] ?? '';
        $content     = $parmas['content'] ?? '';
        $titleParams = $parmas['title_params'] ?? [];

        if (!$title || !$category || !$content) {
            return false;
        }

        $staffInfoId = $parmas['staff_info_id'] ?? 0;

        if (!$staffInfoId) {
            return false;
        }

        $staffLang = (new StaffService())->getAcceptLanguage($staffInfoId);
        $t         = BaseService::getTranslation($staffLang);

        $id                              = time().$staffInfoId.rand(1000000, 9999999);
        $kit_param['staff_info_ids_str'] = $staffInfoId;
        $kit_param['staff_users']        = [$staffInfoId];
        $kit_param['message_title']      = $t->_($title, $titleParams);
        $kit_param['message_content']    = $content;
        $kit_param['id']                 = $id;
        $kit_param['category']           = $category;
        return (new MessagesService())->add_kit_message($kit_param);
    }

    /**
     * 取消目标
     * 处理试用期目标的取消操作
     * @param array $params 包含取消操作所需数据的关联数组
     * @return bool 成功取消返回 true
     * @throws ValidationException 参数验证失败时抛出异常
     * @throws BusinessException 业务逻辑处理出错时抛出异常
     */
    public function cancel($params): bool
    {
        $this->logger->info(['title' => '目标取消', 'opt' => '开始执行', 'func' => 'cancel', 'params' => $params]);

        $staffInfoId                 = $params['staff_info_id'] ?? 0;
        $hrProbationTargetRepository = (new HrProbationTargetRepository());

        //获取员工数据
        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);

        if (empty($staffInfo) || ($staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN)) {
            throw new ValidationException(self::$t->_('staff_data_resign_not_operate'));
        }

        //验证Target数据
        $targetInfo = $hrProbationTargetRepository->getTargetInfoByStaffId($staffInfoId);

        if (empty($targetInfo)) {
            throw new ValidationException(self::$t->_('target_data_does_no_exist'));
        }

        //验证是否是已调整和已完成
        if (!in_array($targetInfo['setting_state'],
            [HrProbationTargetModel::SETTING_STATE_FINISH, HrProbationTargetModel::SETTING_STATE_ADJUST])) {
            throw new ValidationException(self::$t->_('target_data_no_setting_finish'));
        }

        //转正数据验证
        $probationInfo = $hrProbationTargetRepository->getProbationInfoByStaffId($params['staff_info_id']);

        //第二阶段已经发起不可以撤销
        if (!empty($probationInfo) && ($probationInfo['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT)) {
            throw new ValidationException(self::$t->_('staff_second_audit_status_wait_not_operate'));
        }

        $this->logger->info(['title' => '目标取消', 'opt' => '验证完成', 'func' => 'cancel']);

        $db = $this->getDI()->get("db_backyard");
        $db->begin();

        try {
            //获取最新的流程中的数据
            $targetBusinessInfo = $hrProbationTargetRepository->getTargetBusinessInfo([
                'target_id' => $targetInfo['id'],
            ], 'id,target_id,target_detail_id,stage,state');

            //不存在或者状态不正确
            if (empty($targetBusinessInfo) || $targetBusinessInfo['state'] != HrProbationTargetBusinessModel::STATE_NORMAL) {
                throw new ValidationException(self::$t->_('target_data_cancel_state_not_normal'));
            }

            //第一阶段验证一阶段评估是否发起
            if ($targetBusinessInfo['stage'] == HrProbationTargetDetailModel::STAGE_FIRST) {
                if (!empty($probationInfo) && ($probationInfo['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT)) {
                    throw new ValidationException(self::$t->_('staff_first_audit_status_wait_not_operate'));
                }
            }

            //获取最新的流程中的数据
            $targetDetailInfo = $hrProbationTargetRepository->getTargetDetailInfo([
                'id' => $targetBusinessInfo['target_detail_id'],
            ], 'id,target_id,stage,send_state'
            );

            //目标未发送
            if (
                empty($targetDetailInfo)
                ||
                $targetDetailInfo['send_state'] != HrProbationTargetModel::SEND_STATE_FINISH
            ) {
                throw new ValidationException(self::$t->_('target_data_send_state_not_finish_not_operate'));
            }

            //调整业务表数据
            $businessRes = $hrProbationTargetRepository->saveTargetBusiness([
                'id'    => $targetBusinessInfo['id'],
                'state' => HrProbationTargetBusinessModel::STATE_CANCEL,
            ]);

            if (!$businessRes) {
                throw new BusinessException('Probation Business Save Error ');
            }

            $targetData = [
                'id'            => $targetInfo['id'],
                'setting_state' => HrProbationTargetModel::SETTING_STATE_NOT_START,
                'send_state'    => HrProbationTargetModel::SEND_STATE_NOT_SEND,
                'sign_state'    => HrProbationTargetModel::SIGN_STATE_NOT_START,
            ];

            //第二阶段
            if ($targetBusinessInfo['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
                $targetData['setting_state'] = HrProbationTargetModel::SETTING_STATE_FINISH;
                $targetData['send_state']    = HrProbationTargetModel::SEND_STATE_FINISH;
                $targetData['sign_state']    = HrProbationTargetModel::SIGN_STATE_FINISH;
            }

            //调整主表信息
            $targetRes = $hrProbationTargetRepository->saveTarget($targetData);

            if (!$targetRes) {
                throw new BusinessException('Probation Target Save Error ');
            }

            $this->logger->info(['title'  => '目标取消', 'opt'    => '调整主表完成', 'func'   => 'cancel', 'params' => $targetData,]);

            //调整详情表信息
            $targetDetailRes = $hrProbationTargetRepository->saveTargetDetail([
                'id'         => $targetBusinessInfo['target_detail_id'],
                'send_state' => HrProbationTargetDetailModel::SEND_STATE_NOT_SEND,
                'sign_state' => HrProbationTargetDetailModel::SIGN_STATE_NOT_START,
            ]);

            if (!$targetDetailRes) {
                throw new BusinessException('Probation Target Detail Save Error ');
            }

            $this->logger->info(['title' => '目标取消', 'opt' => '调整详情表完成', 'func' => 'cancel']);

            //记录员工操作记录日志
            $data = [
                [
                    "operater"      => $params['user_id'],
                    "staff_info_id" => $staffInfoId,
                    "type"          => 'probation_target',
                    "before"        => json_encode([
                        'body' => [
                            'send_state' => HrProbationTargetModel::SEND_STATE_FINISH,
                        ],
                    ], JSON_UNESCAPED_UNICODE),
                    "after"         => json_encode([
                        'body' => [
                            'send_state' => HrProbationTargetModel::SEND_STATE_NOT_SEND,
                        ],
                    ], JSON_UNESCAPED_UNICODE),
                ],
            ];

            $client = new ApiClient('hris', '', 'add_operate_logs');
            $client->setParams($data);
            $res = $client->execute();

            $this->logger->info(['title' => '目标取消', 'opt' => '记录日志完成', 'func' => 'cancel', 'res' => $res]);

            if (empty($res)) {
                $this->logger->error([
                    'title'  => '目标取消-推送hris日志',
                    'func'   => 'cancel',
                    'params' => $data,
                    'res'    => $res,
                ]);
            }

            if (!$db->commit()) {
                throw new Exception('Common Save Error ');
            }
        } catch (Exception $exception) {
            $db->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * 获取发送日志
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function logList($params): array
    {
        //获取发送过的数据
        $staffInfoId                 = $params['staff_info_id'] ?? 0;
        $hrProbationTargetRepository = (new HrProbationTargetRepository());

        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('staff_info_does_not_exist'));
        }

        //验证数据
        $targetInfo = $hrProbationTargetRepository->getTargetInfoByStaffId($staffInfoId);

        if (empty($targetInfo)) {
            throw new ValidationException(self::$t->_('target_data_does_no_exist'));
        }

        //获取业务流
        $data = $hrProbationTargetRepository->getTargetBusinessList([
            'target_id' => $targetInfo['id'],
        ], 'id,staff_info_id,stage,created_at');

        $returnData = [];
        $showName   = self::$t->_('probation_send_record_title');
        foreach ($data as $item) {
            $returnData[] = [
                'target_business_id' => $item['id'],
                'log_text'           => show_time_zone($item['created_at']).' '.$showName,
            ];
        }

        return $returnData;
    }

    /**
     * 变更记录详情
     * @throws Exception
     */
    public function logDetail($params): array
    {
        $targetBusinessId            = $params['target_business_id'] ?? 0;
        $hrProbationTargetRepository = (new HrProbationTargetRepository());
        $targetBusinessInfo          = $hrProbationTargetRepository->getTargetBusinessById($targetBusinessId);
        $userId                      = $params['user_id'] ?? 0;

        if (empty($targetBusinessInfo)) {
            return [];
        }

        $staffData = empty($targetBusinessInfo['staff_data']) ? [] : json_decode($targetBusinessInfo['staff_data'],
            true);

        //历史固化数据没有这个字段取最新的
        if ($staffData && (!isset($staffData['state']) || !isset($staffData['wait_leave_state']))) {
            $staffInfo                     = (new HrStaffInfoModel())->getOneByStaffId($targetBusinessInfo['staff_info_id']);
            $staffData['state']            = $staffInfo['state'] ?? 0;
            $staffData['wait_leave_state'] = $staffInfo['wait_leave_state'] ?? 0;
        }

        $returnData = $this->formatDetail(
            $staffData,
            [
                'first_deadline_date'  => $staffData['first_deadline_date'] ?? '',
                'second_deadline_date' => $staffData['second_deadline_date'] ?? '',
            ],
            $targetBusinessInfo,
            $staffData['act_version'] ?? 0 //版本号存在获取不存在不获取
        );

        //处理用户是否有职级白名单权限
        $hrRpc = (new ApiClient('hris', '', 'has_grade_permission_tab', 'th'));
        $hrRpc->setParams([["staff_info_id" => $targetBusinessInfo['staff_info_id'], "fbid" => $userId]]);
        $res = $hrRpc->execute();

        if (!isset($res['body']) || ($res['body'] != 1)) {
            $returnData['job_title_grade_v2']   = '';
            $returnData['job_title_grade_name'] = '';
        }

        return $returnData;
    }

    /**
     * 发送HR相关消息
     * @return true
     * @throws BusinessException
     * @throws ValidationException
     * @throws Exception
     */
    public function sendHrbpMessage($params): bool
    {
        $this->logger->info([
            'title'  => 'BP发送消息',
            'func'   => 'sendHrbpMessage',
            'opt'    => 'START',
            'params' => $params,
        ]);

        $staffInfoId                 = $params['staff_info_id'] ?? 0;
        $targetBusinessId            = $params['target_business_id'] ?? 0;
        $operateId                   = $params['operate_id'] ?? 0;
        $hrProbationTargetRepository = (new HrProbationTargetRepository());
        $sourceType                  = $params['source_type'] ?? HrProbationTargetMessageModel::SOURCE_TYPE_SEND;
        $date                        = gmdate('Y-m-d H:i:s');

        //参数异常
        if (!$staffInfoId || !$targetBusinessId) {
            throw new ValidationException('Params Error ');
        }

        //员工不存在
        $staffInfo = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);

        if (empty($staffInfo)) {
            throw new BusinessException('staff Info Empty ');
        }

        //给HR发送消息
        $bpData = (new StaffInfoService())->findHRBPToBackyard([
            'department_id' => $staffInfo['node_department_id'],
            'store_id'      => $staffInfo['sys_store_id'],
        ]);

        if (empty($bpData['code']) || $bpData['code'] != 1) {
            throw new Exception('Hrbp Svc Request Error ');
        }

        //没有找到HRBP 不发送消息
        if (empty($bpData['data']['hrbp'])) {
            throw new BusinessException('Hrbp List Empty ');
        }

        $hrbpIds = explode(',', $bpData['data']['hrbp']);
        $hrbpIds = array_values(array_filter(array_unique($hrbpIds)));

        //业务开始，获取业务流程
        $businessInfo = $hrProbationTargetRepository->getTargetBusinessById($targetBusinessId);

        if (empty($businessInfo)) {
            throw new BusinessException('Business Info Empty ');
        }

        //查询员工语言环境
        $staffLang = (new StaffService())->getAcceptLanguage($staffInfoId);

        //生成目标PDF
        $pdfUrl = $this->generateStaffPdf($businessInfo, $staffLang, Enums\StaffEnums::STAFF_TARGET_PDF_OPERATE_STAFF);

        if (!$pdfUrl) {
            throw new Exception('Pdf Generate Error ');
        }

        $this->logger->info([
            'title'  => 'BP发送消息',
            'func'   => 'sendHrbpMessage',
            'opt'    => '生成PDF',
            'params' => $pdfUrl,
        ]);

        $db        = $this->getDI()->get("db_backyard");
        $messageDb = $this->getDI()->get('db_message');
        $db->begin();
        $messageDb->begin();

        try {
            foreach ($hrbpIds as $bpId) {
                $messageRes = $this->sendTargetMessage([
                    'staff_info_id' => $bpId,
                    'content'       => json_encode([
                        'business_id' => $targetBusinessId,
                        'type'        => HrProbationTargetMessageModel::TYPE_BP,
                    ], JSON_UNESCAPED_UNICODE),
                    'title'         => 'probation_target_message_title_bp_send',
                    'title_params'  => [
                        'staff_name'    => $staffInfo['name'] ?? '',
                        'staff_info_id' => $staffInfo['staff_info_id'],
                    ],
                    'category'      => Enums\EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET_BP'),
                ]);

                if ($messageRes[1] != 1) {
                    throw new Exception('Probation Target Message Error ');
                }

                $this->logger->info([
                    'title'  => 'BP发送消息',
                    'func'   => 'sendHrbpMessage',
                    'opt'    => '发送消息完成',
                    'params' => $messageRes,
                ]);

                //生成业务表数据
                $res = $hrProbationTargetRepository->saveTargetMessage([
                    'target_id'          => $businessInfo['target_id'],
                    'target_business_id' => $targetBusinessId,
                    'staff_info_id'      => $bpId,
                    'msg_id'             => $messageRes[2][0] ?? '',
                    'type'               => HrProbationTargetMessageModel::TYPE_BP,
                    'source_type'        => $sourceType,
                    'operate_id'         => $operateId,
                ]);

                if (!$res) {
                    throw new Exception('Probation Target Message Business Error ');
                }
            }

            $langKey = 'pdf_path_'.$this->getLangKey($staffLang);

            $businessData = [
                'id'     => $businessInfo['id'],
                $langKey => $pdfUrl,
            ];

            if ($hrbpIds) {
                $bpIdsStr = implode(',', $hrbpIds);

                if ($sourceType == HrProbationTargetMessageModel::SOURCE_TYPE_SEND) {
                    $businessData['bp_first_staff_ids'] = $bpIdsStr;
                    $businessData['bp_first_send_time'] = $date;
                } else {
                    $businessData['bp_new_staff_ids'] = $bpIdsStr;
                    $businessData['bp_new_send_time'] = $date;
                }
            }

            //生成业务表数据
            $targetBusinessId = $hrProbationTargetRepository->saveTargetBusiness($businessData);

            $this->logger->info([
                'title'  => 'BP发送消息',
                'func'   => 'sendHrbpMessage',
                'opt'    => '更新业务表数据',
                'params' => $businessData,
                'res'    => $targetBusinessId,
            ]);

            if (!$targetBusinessId) {
                throw new Exception('Probation Business Save Error ');
            }

            if (!$db->commit() || !$messageDb->commit()) {
                throw new Exception('Common Save Error ');
            }

            $this->logger->info([
                'title'  => 'BP发送消息',
                'func'   => 'sendHrbpMessage',
                'opt'    => 'BP消息发送成功',
                'params' => $targetBusinessId,
            ]);
        } catch (Exception $exception) {
            $db->rollback();
            $messageDb->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * 目标下载
     * @param $params
     * @return array|array[]|string[]
     * @throws ValidationException
     * @throws Exception
     * 格式：Probation_target_工号_年月日时分秒.pdf
     */
    public function download($params): array
    {
        $this->logger->info([
            'title'  => 'PDF下载',
            'func'   => 'download',
            'opt'    => '开始',
            'params' => $params,
        ]);

        $staffInfoId = $params['staff_info_id'] ?? 0;
        $userId      = $params['user_id'] ?? 0;

        //员工不存在
        $staffInfo                   = (new StaffInfoService())->getStaffInfoOne($staffInfoId);
        $hrProbationTargetRepository = (new HrProbationTargetRepository());

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('staff_info_does_not_exist'));
        }

        //获取最新目标
        $targetInfo = $hrProbationTargetRepository->getTargetInfoByStaffId($staffInfoId);

        if (empty($targetInfo)) {
            throw new ValidationException(self::$t->_('target_data_does_no_exist'));
        }

        //验证状态
        if (
            !in_array($targetInfo['send_state'],
                [
                    HrProbationTargetModel::SEND_STATE_FINISH,
                    HrProbationTargetModel::SEND_STATE_ADJUST_FINISH,
                ]
            )
            ||
            !in_array($targetInfo['setting_state'],
                [
                    HrProbationTargetModel::SETTING_STATE_FINISH,
                    HrProbationTargetModel::SETTING_STATE_ADJUST,
                ]
            )
        ) {
            throw new ValidationException(self::$t->_('target_data_sign_state_not_success'));
        }

        //已发送的就可以下载
        $targetDetailInfo = $hrProbationTargetRepository->getTargetDetailInfo([
            'target_id'     => $targetInfo['id'],
            'setting_state' => HrProbationTargetDetailModel::SETTING_STATE_FINISH,
            'send_state'    => HrProbationTargetDetailModel::SEND_STATE_FINISH,
        ]);

        if (empty($targetDetailInfo)) {
            throw new ValidationException(self::$t->_('target_data_state_no_finish_not_operate'));
        }

        $pdfUrl = $this->getPdfInfoByDetailId($targetDetailInfo, $userId);

        //pdf生成错误
        if (!$pdfUrl) {
            throw new Exception('Pdf Generate Error ');
        }

        //第二阶段
        if ($targetDetailInfo['stage'] == HrProbationTargetDetailModel::STAGE_SECOND) {
            $this->logger->info([
                'title' => 'PDF载',
                'func'  => 'download',
                'opt'   => '调整前下载',
            ]);

            //已发送的就可以下载
            $targetDetailInfo = $hrProbationTargetRepository->getTargetDetailInfo([
                'target_id'     => $targetInfo['id'],
                'stage'         => HrProbationTargetDetailModel::STAGE_FIRST,
                'setting_state' => HrProbationTargetDetailModel::SETTING_STATE_FINISH,
                'send_state'    => HrProbationTargetDetailModel::SEND_STATE_FINISH,
            ]);

            if (empty($targetDetailInfo)) {
                throw new ValidationException(self::$t->_('target_data_state_no_finish_not_operate'));
            }

            //业务开始，获取调整前的url
            $pdfFirstUrl =  $this->getPdfInfoByDetailId($targetDetailInfo ,$userId);

            //pdf生成错误，重新生成
            if (!$pdfFirstUrl) {
                throw new Exception('Pdf Generate Error ');
            }

            //合并URL
            $pdfData = (new FormPdfServer())->getInstance()->mergePdf([
                $pdfFirstUrl,
                $pdfUrl,
            ]);

            $pdfUrl = $pdfData['object_url'] ?? '';

            if (!$pdfUrl) {
                throw new Exception('Pdf Generate Error ');
            }
        }

        return [
            'pdf_path' => $pdfUrl,
        ];
    }

    /**
     * 获取PDF信息
     * @param $targetDetailId
     * @return array|mixed|string
     * @throws ValidationException
     */
    public function getPdfInfoByDetailId($targetDetailInfo, $userId)
    {
        $this->logger->info([
            'title'  => '获取PDF信息',
            'func'   => 'getPdfInfoByDetailId',
            'opt'    => '开始',
            'params' => [
                'target_detail_id' => $targetDetailInfo['id'],
                'user_id'          => $userId,
            ],
        ]);

        if (empty($targetDetailInfo)) {
            throw new ValidationException(self::$t->_('target_data_state_no_finish_not_operate'));
        }

        $hrProbationTargetRepository = new HrProbationTargetRepository();
        $probationTargetToolsService = new ProbationTargetToolsService();

        //业务开始，获取业务流程,撤销的不下载
        $businessInfo = $hrProbationTargetRepository->getTargetBusinessInfo([
            'target_detail_id' => $targetDetailInfo['id'],
            'state'            => HrProbationTargetBusinessModel::STATE_NORMAL,
        ]);

        //无业务流程返回错误
        if (empty($businessInfo)) {
            //导入的数据用旧数据
            $businessInfo = $probationTargetToolsService->assembleTmpTargetBusinessInfo($targetDetailInfo);

            if (empty($businessInfo)) {
                return false;
            }
        }

        $this->logger->info([
            'title' => '获取PDF信息',
            'func'  => 'getPdfInfoByDetailId',
            'opt'   => '验证通过',
        ]);

        //查询员工语言环境
        $businessInfo['user_id'] = $userId;

        //生成目标PDF
        $pdfUrl = $this->generateStaffPdf($businessInfo, self::$language,
            Enums\StaffEnums::STAFF_TARGET_PDF_OPERATE_MANAGER);

        $this->logger->info([
            'title'  => 'PDF下载',
            'func'   => 'download',
            'opt'    => '下载完成',
            'params' => [
                'pdf_url' => $pdfUrl,
                'lang'    => self::$language,
            ],
        ]);

        return $pdfUrl;
    }

    /**
     * 获取消息日志详情
     *
     * 用于获取试用期目标设定相关的完整操作日志记录
     * 包含发送、签字等关键操作的时间线信息
     *
     * @param array $params 请求参数数组，必须包含 staff_info_id 员工ID
     *
     * @return array 返回格式化后的操作日志数据，包括：
     *               - staff_info_id: 员工ID
     *               - staff_name: 员工姓名
     *               - log_list: 操作日志列表（按时间排序）
     *
     * @throws ValidationException 当员工ID不存在时抛出异常
     */
    public function msgLog($params): array
    {
        $this->logger->info([
            'title'  => '签字消息进度详情',
            'func'   => 'msgLog',
            'opt'    => '开始',
            'params' => $params,
        ]);

        $staffInfoId = $params['staff_info_id'] ?? 0;

        //员工验证
        $staffInfo                   = (new HrStaffInfoModel())->getOneByStaffId($staffInfoId);
        $hrProbationTargetRepository = (new HrProbationTargetRepository());

        if (empty($staffInfo)) {
            throw new ValidationException(self::$t->_('staff_info_does_not_exist'));
        }

        //目标验证
        $targetInfo = $hrProbationTargetRepository->getTargetInfoByStaffId($staffInfoId);

        if (empty($targetInfo)) {
            throw new ValidationException(self::$t->_('target_data_does_no_exist'));
        }

        //验证完成
        $this->logger->info([
            'title' => '消息详情',
            'func'  => 'msgLog',
            'opt'   => '验证完成',
        ]);

        //组装返回数据
        $returnData = [
            'staff_info_id' => $staffInfo['staff_info_id'],
            'staff_name'    => $staffInfo['name'] ?? '',
            'staff_en_name' => $staffInfo['name_en'] ?? '',
            'log_list'      => [],
        ];

        //获取最新的发送记录
        $targetBusinessInfo = $hrProbationTargetRepository->getTargetBusinessInfo([
            'target_id' => $targetInfo['id'],
            'state'     => HrProbationTargetBusinessModel::STATE_NORMAL,
        ]);

        //没有发送记录
        if (empty($targetBusinessInfo)) {
            return $returnData;
        }

        //调整前调整后两个阶段
        $firstTargetBusinessInfo = $secondTargetBusinessInfo = [];

        //当前是第二阶段
        if (HrProbationTargetDetailModel::STAGE_SECOND == $targetBusinessInfo['stage']) {
            //查询到第一阶段数据
            $firstTargetBusinessInfo = $hrProbationTargetRepository->getTargetBusinessInfo([
                'target_id' => $targetInfo['id'],
                'state'     => HrProbationTargetBusinessModel::STATE_NORMAL,
                'stage'     => HrProbationTargetDetailModel::STAGE_FIRST,
            ]);

            $secondTargetBusinessInfo = $targetBusinessInfo;
        } else {
            //查询到第一阶段数据
            $firstTargetBusinessInfo = $targetBusinessInfo;
        }

        //获取各个阶段数据
        $firstLogList  = $this->formatMsgLog($firstTargetBusinessInfo);
        $secondLogList = $this->formatMsgLog($secondTargetBusinessInfo);

        //合并两个阶段的日志
        $returnData['log_list'] = array_merge($firstLogList, $secondLogList);

        return $returnData;
    }

    /**
     * 获取指定阶段的操作日志枚举映射
     *
     * 根据试用期评估阶段返回对应的操作类型枚举值，用于匹配
     * 不同阶段（第一阶段/第二阶段）对应的不同操作标识
     * @param int $stage 阶段标识（HrProbationTargetDetailModel::STAGE_*）
     * @return array 返回包含以下键值的数组：
     *               - send: 发送操作枚举值
     *               - first_bp: 初始BP抄送操作枚举值
     *               - staff_sign: 员工签字操作枚举值
     *               - manager_sign: 上级签字操作枚举值
     *               - new_bp: 新增BP抄送操作枚举值
     *
     * 当传入无效阶段参数时返回空数组
     */
    private function getLogEnums($stage)
    {
        $enumsList = [
            HrProbationTargetDetailModel::STAGE_FIRST  => [
                'send'         => (string)HrProbationTargetMessageModel::ACTION_SEND_OPERATE,
                'first_bp'     => (string)HrProbationTargetMessageModel::ACTION_SEND_FIRST_BP,
                'staff_sign'   => (string)HrProbationTargetMessageModel::ACTION_SIGN_STAFF,
                'manager_sign' => (string)HrProbationTargetMessageModel::ACTION_SIGN_MANAGER,
                'new_bp'       => (string)HrProbationTargetMessageModel::ACTION_SIGN_NEW_BP,
            ],
            HrProbationTargetDetailModel::STAGE_SECOND => [
                'send'         => (string)HrProbationTargetMessageModel::ACTION_ADJUST_SEND_OPERATE,
                'first_bp'     => (string)HrProbationTargetMessageModel::ACTION__ADJUST_SEND_FIRST_BP,
                'staff_sign'   => (string)HrProbationTargetMessageModel::ACTION__ADJUST_SIGN_STAFF,
                'manager_sign' => (string)HrProbationTargetMessageModel::ACTION_ADJUST_SIGN_MANAGER,
                'new_bp'       => (string)HrProbationTargetMessageModel::ACTION_ADJUST_SIGN_NEW_BP,
            ],
        ];

        return $enumsList[$stage] ?? [];
    }

    /**
     * 格式化单条数据的详情
     * @param $targetBusinessInfo
     * @return array
     */
    public function formatMsgLog($targetBusinessInfo)
    {
        $this->logger->info([
            'title' => '消息详情',
            'func'  => 'msgLog',
            'opt'   => '组装开始',
            'res'   => [
                'staff_info_id' => $targetBusinessInfo['staff_info_id'] ?? '',
                'stage'         => $targetBusinessInfo['stage'] ?? 0,
            ],
        ]);

        //没有数据直接返回
        if (empty($targetBusinessInfo)) {
            return [];
        }

        //获取标题枚举
        $action = $this->getLogEnums($targetBusinessInfo['stage']);

        if (empty($action)) {
            return [];
        }

        //获取所有的员工信息
        $staffIds = [];

        //发起人
        if (!empty($targetBusinessInfo['send_operate_id'])) {
            $staffIds[] = $targetBusinessInfo['send_operate_id'];
        }

        //员工
        if (!empty($targetBusinessInfo['staff_info_id'])) {
            $staffIds[] = $targetBusinessInfo['staff_info_id'];
        }

        //上级
        if (!empty($targetBusinessInfo['manager_id'])) {
            $staffIds[] = $targetBusinessInfo['manager_id'];
        }

        //抄送人
        $bpFirstStaffIds = empty($targetBusinessInfo['bp_first_staff_ids']) ? [] : explode(',',
            $targetBusinessInfo['bp_first_staff_ids']);

        //最新抄送人
        $bpNewStaffIds = empty($targetBusinessInfo['bp_new_staff_ids']) ? [] : explode(',',
            $targetBusinessInfo['bp_new_staff_ids']);

        //所有员工信息获取
        $staffIds  = array_merge($staffIds, $bpFirstStaffIds, $bpNewStaffIds);
        $staffIds  = array_values(array_filter(array_unique($staffIds)));
        $staffList = (new HrStaffService())->getStaffInfosMap($staffIds);

        //发起人
        $sendOperateInfo = [
            'action'         => $action['send'],
            'action_name'    => self::$t->_(HrProbationTargetMessageModel::$actionList[$action['send']] ?? ''),
            'state'          => '',
            'state_name'     => '',
            'operate_time'   => show_time_zone($targetBusinessInfo['send_time']),
            'staff_info_log' => [
                [
                    'staff_info_id'   => $targetBusinessInfo['send_operate_id'],
                    'staff_name'      => $staffList[$targetBusinessInfo['send_operate_id']]['name'] ?? '',
                    'department_name' => $staffList[$targetBusinessInfo['send_operate_id']]['department_name'] ?? '',
                    'job_title_name'  => $staffList[$targetBusinessInfo['send_operate_id']]['job_name'] ?? '',
                ],
            ],
        ];

        $returnData[] = $sendOperateInfo;

        //第一次抄送BP
        if ($bpFirstStaffIds) {
            $bpFirstStaffInfo = [
                'action'         => $action['first_bp'],
                'action_name'    => self::$t->_(HrProbationTargetMessageModel::$actionList[$action['first_bp']] ?? ''),
                'state'          => '',
                'state_name'     => '',
                'operate_time'   => show_time_zone($targetBusinessInfo['bp_first_send_time']),
                'staff_info_log' => [],
            ];

            foreach ($bpFirstStaffIds as $bpFirstStaffId) {
                $bpFirstStaffInfo['staff_info_log'][] = [
                    'staff_info_id'   => $bpFirstStaffId,
                    'staff_name'      => $staffList[$bpFirstStaffId]['name'] ?? '',
                    'department_name' => $staffList[$bpFirstStaffId]['department_name'] ?? '',
                    'job_title_name'  => $staffList[$bpFirstStaffId]['job_name'] ?? '',
                ];
            }

            $returnData[] = $bpFirstStaffInfo;
        }

        //用户签字
        if (!empty($targetBusinessInfo['staff_info_id'])) {
            $staffSignTime = '';
            if (
                isset($targetBusinessInfo['staff_state'])
                && $targetBusinessInfo['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS
            ) {
                $staffSignTime = show_time_zone($targetBusinessInfo['staff_sign_time']);
            }

            //发送人
            $staffSignInfo = [
                'action'         => $action['staff_sign'],
                'action_name'    => self::$t->_(HrProbationTargetMessageModel::$actionList[$action['staff_sign']] ?? ''),
                'state'          => $targetBusinessInfo['staff_state'],
                'state_name'     => self::$t->_(HrProbationTargetBusinessModel::$staffSignStateList[$targetBusinessInfo['staff_state']] ?? ''),
                'operate_time'   => $staffSignTime,
                'staff_info_log' => [
                    [
                        'staff_info_id'   => $targetBusinessInfo['staff_info_id'],
                        'staff_name'      => $staffList[$targetBusinessInfo['staff_info_id']]['name'] ?? '',
                        'department_name' => $staffList[$targetBusinessInfo['staff_info_id']]['department_name'] ?? '',
                        'job_title_name'  => $staffList[$targetBusinessInfo['staff_info_id']]['job_name'] ?? '',
                    ],
                ],
            ];

            $returnData[] = $staffSignInfo;
        }

        //上级签字
        if (!empty($targetBusinessInfo['manager_id'])) {
            $managerSignTime = '';
            if (
                isset($targetBusinessInfo['manager_state'])
                && $targetBusinessInfo['manager_state'] == HrProbationTargetBusinessModel::MANAGER_SIGN_STATE_SUCCESS
            ) {
                $managerSignTime = show_time_zone($targetBusinessInfo['manager_sign_time']);
            }

            //签字人
            $managerSignInfo = [
                'action'         => $action['manager_sign'],
                'action_name'    => self::$t->_(HrProbationTargetMessageModel::$actionList[$action['manager_sign']] ?? ''),
                'state'          => $targetBusinessInfo['manager_state'],
                'state_name'     => self::$t->_(HrProbationTargetBusinessModel::$managerSignStateList[$targetBusinessInfo['manager_state']] ?? ''),
                'operate_time'   => $managerSignTime,
                'staff_info_log' => [
                    [
                        'staff_info_id'   => $targetBusinessInfo['manager_id'],
                        'staff_name'      => $staffList[$targetBusinessInfo['manager_id']]['name'] ?? '',
                        'department_name' => $staffList[$targetBusinessInfo['manager_id']]['department_name'] ?? '',
                        'job_title_name'  => $staffList[$targetBusinessInfo['manager_id']]['job_name'] ?? '',
                    ],
                ],
            ];

            $returnData[] = $managerSignInfo;
        }

        //最新抄送
        if (!empty($targetBusinessInfo['bp_new_staff_ids'])) {
            $bpNewStaffInfo = [
                'action'         => $action['new_bp'],
                'action_name'    => self::$t->_(HrProbationTargetMessageModel::$actionList[$action['new_bp']] ?? ''),
                'state'          => '',
                'state_name'     => '',
                'operate_time'   => show_time_zone($targetBusinessInfo['bp_new_send_time']),
                'staff_info_log' => [],
            ];

            foreach ($bpNewStaffIds as $bpNewStaffId) {
                $bpNewStaffInfo['staff_info_log'][] = [
                    'staff_info_id'   => $bpNewStaffId,
                    'staff_name'      => $staffList[$bpNewStaffId]['name'] ?? '',
                    'department_name' => $staffList[$bpNewStaffId]['department_name'] ?? '',
                    'job_title_name'  => $staffList[$bpNewStaffId]['job_name'] ?? '',
                ];
            }

            $returnData[] = $bpNewStaffInfo;
        }

        //记录一下返回的审批流详情
        $this->logger->info([
            'title' => '消息详情',
            'func' => 'msgLog',
            'opt' => '组装完成',
            'res' => $returnData,
        ]);

        return $returnData;
    }

    /**
     * 生成员工PDF
     * @param $targetBusInfo
     * @param string $lang
     * @param bool $isSign
     * @return array|mixed
     * @throws Exception
     */
    public function generateStaffPdf(
        $businessInfo,
        $lang = 'th',
        $pdfType = Enums\StaffEnums::STAFF_TARGET_PDF_OPERATE_STAFF
    ) {
        // 规范化语言标识 只有三种模版 ZH EN TH
        $lang = $this->getLangKey($lang);

        $data = $this->getPdfData($businessInfo, $pdfType , $lang);

        //pdf取值
        if ($pdfType == Enums\StaffEnums::STAFF_TARGET_PDF_OPERATE_MANAGER) {
            //处理用户是否有职级白名单权限
            $hrRpc = (new ApiClient('hris', '', 'has_grade_permission_tab', 'th'));
            $hrRpc->setParams([["staff_info_id" => $data['staff_info_id'], "fbid" => $businessInfo['user_id'] ?? 0]]);
            $res = $hrRpc->execute();

            if (!isset($res['body']) || ($res['body'] != 1)) {
                $data['job_title_grade_v2']   = '';
                $data['job_title_grade_name'] = '';
            }
        }

        //pdf模版获取
        $tempUrl = $this->getTargetPdfTemp($lang, $data['act_version']);

        $certificateService = reBuildCountryInstance(new CertificateService());

        //获取员工
        if (isCountry('MY')) {
            $pdfCompanyData = $certificateService->getStaffCompanyInfoByTarget(
                $businessInfo['staff_info_id'],
                true,
                [
                    'is_footer_sign'  => !empty($data['act_version']) ? true : false,
                    'footer_sign_url' => $data['staff_sign_url'] ?? '',
                    'lang'            => $lang,
                ]
            );
        } else {
            $pdfCompanyData = $certificateService->getStaffCompanyInfo(
                $businessInfo['staff_info_id'],
                true,
                [
                    'is_footer_sign'  => !empty($data['act_version']) ? true : false,
                    'footer_sign_url' => $data['staff_sign_url'] ?? '',
                    'lang'            => $lang
                ]
            );
        }

        $signPdfSetting = [
            'format'              => 'a4',
            'displayHeaderFooter' => true,
            'headerTemplate'      => $pdfCompanyData['header_template'],
            'footerTemplate'      => $pdfCompanyData['footer_template'],
        ];

        $pdfImgData = [];

        if (!empty($data['staff_sign_url'])) {
            $pdfImgData[] = ['name' => 'staff_sign_url', 'url' => $data['staff_sign_url']];
        }

        if (!empty($data['manager_sign_url'])) {
            $pdfImgData[] = ['name' => 'manager_sign_url', 'url' => $data['manager_sign_url']];
        }

        $data['duty_info'] = nl2br($data['duty_info']);
        //转换br
        foreach ($data['target_info'] as & $v) {
            $v['name'] = nl2br($v['name']);
            $v['info'] = nl2br($v['info']);
        }

        $res = (new FormPdfServer())->getInstance()->generatePdf($tempUrl, $data, $pdfImgData, $signPdfSetting);

        return $res['object_url'] ?? '';
    }

    /**
     * PDF-取值
     * @param $targetDetailInfo
     * @param $targetBusInfo
     * @return array
     */
    public function getPdfData($businessInfo, $pdfType , $lang = ''): array
    {
        $returnData = [];

        //固化的数据
        $staffInfo     = !empty($businessInfo['staff_data']) ? json_decode($businessInfo['staff_data'], JSON_UNESCAPED_UNICODE) : [];
        $jobTitleGrade = array_column((new SysService())->JobTitleGrade(), 'label', 'value');

        //员工数据
        $returnData['staff_name']           = $staffInfo['name'] ?? '';                               // 员工姓名
        $returnData['staff_info_id']        = $staffInfo['staff_info_id'] ?? '';                      // 员工信息ID
        $returnData['department_name']      = $staffInfo['department_name'] ?? '';                    // 部门名称
        $returnData['job_title']            = $staffInfo['job_title'] ?? '';                          // 职位ID
        $returnData['job_title_name']       = $staffInfo['job_title_name'] ?? '';                     // 职位名称
        $returnData['job_title_grade_v2']   = $staffInfo['job_title_grade_v2'] ?? '';                 // 职级代码
        $returnData['job_title_grade_name'] = $jobTitleGrade[$staffInfo['job_title_grade_v2']] ?? ''; // 职级名称
        $returnData['hire_date']            = $staffInfo['hire_date'] ?? '';                          // 入职日期
        $returnData['manager_id']           = $staffInfo['manger'] ?? '';                             // 直属上级ID
        $returnData['first_deadline_date']  = $staffInfo['first_deadline_date'] ?? '';                // 第一次评估截止日期
        $returnData['second_deadline_date'] = $staffInfo['second_deadline_date'] ?? '';               // 第二次评估截止日期
        $returnData['act_version']          = $staffInfo['act_version'] ?? 0;                         // 价值观版本号

        //试用期目标
        $returnData['duty_info']   = $businessInfo['duty_info'] ?? '';

        //目标
        $returnData['target_info'] = [];
        $returnData['weight_tatol'] = 0;

        if (!empty($businessInfo['target_info'])) {
            $returnData['target_info'] = json_decode($businessInfo['target_info'], JSON_UNESCAPED_UNICODE);
            $returnData['weight_tatol'] = array_sum(array_column($returnData['target_info'], 'weight'));
        }


        //签字相关
        $returnData['staff_sign_url']    = null;
        $returnData['staff_sign_time']   = '';
        $returnData['manager_sign_url']  = null;
        $returnData['manager_sign_time'] = '';

        //如果是上级查看显示签字，员工查看不展示签字
        if ($pdfType == Enums\StaffEnums::STAFF_TARGET_PDF_OPERATE_MANAGER) {
            //签字相关
            if (
                isset($businessInfo['staff_state']) &&
                $businessInfo['staff_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS
            ) {
                $returnData['staff_sign_url']    = $businessInfo['staff_sign_url'] ?? null;
                $returnData['staff_sign_time']   = show_time_zone($businessInfo['staff_sign_time']);
            }

            if (
                isset($businessInfo['manager_state']) &&
                $businessInfo['manager_state'] == HrProbationTargetBusinessModel::STAFF_SIGN_STATE_SUCCESS
            ) {
                $returnData['manager_sign_url']  = $businessInfo['manager_sign_url'] ?? null;
                $returnData['manager_sign_time'] = show_time_zone($businessInfo['manager_sign_time']);
            }
        }

        $t = $this->getTranslation($lang);

        //插入目标说明 - 不区分版本都存在
        $returnData['probation_target_setting_title'] = $t->_('probation_target_setting_title');
        $returnData['probation_target_setting_remark'] = $t->_('probation_target_setting_remark');

        //插入价值观详细数据 按照版本获取
        $returnData['probation_concept_remark'] = $t->_('probation_concept_remark');
        $returnData['probation_target_values'] = $this->getProbationActValuesForList($returnData['act_version'], $lang);

        return $returnData;
    }

    /**
     * 获取试用期目标PDF模板路径
     *
     * 根据指定的语言和活动版本号，构建对应的PDF模板路径，并返回模板内容。
     * 如果未指定活动版本，则使用默认的模板路径。
     *
     * @param string $lang 语言标识符（如 'zh-CN', 'en'），可选，默认为空
     * @param int $actVersion 活动版本号，用于选择对应版本的模板
     * @return mixed 返回PDF模板内容
     * @throws Exception 如果模板文件不存在或读取失败
     */
    public function getTargetPdfTemp($lang = '', $actVersion = 0)
    {
        // 构建PDF模板路径：如果存在活动版本号，则使用带版本号的模板路径
        if ($actVersion) {
            $tmpPath = APP_PATH."/views/probationTarget/target_v{$actVersion}_{$lang}.ftl";
        } else {
            $tmpPath = APP_PATH."/views/probationTarget/target_{$lang}.ftl";
        }

        // 调用父类方法获取PDF模板内容
        return $this->getPdfTemp($tmpPath);
    }

    /**
     * 获取lang的短字符
     * @param $lang
     * @return string
     */
    public function getLangKey($lang = 'th'): string
    {
        $lang = strtolower(substr($lang, 0, 2));
        if (!in_array($lang, ['en', 'zh', 'th'])) {
            if (isCountry()) {
                $lang = 'th';
            } else {
                $lang = 'en';
            }
        }

        return $lang;
    }

    /**
     * 获取PDF链接
     * @param $local
     * @param $param
     */
    public function get_probation_target_business_pdf_path($local, $param)
    {
        $this->logger->info([
            'title'  => 'hcm-pdf接口',
            'action' => '开始执行',
            'func'   => 'get_probation_target_business_pdf_path',
            'params' => $param,
            'local'  => $local,
        ]);

        $targetBusinessId = $param['business_id'];
        $userId           = $param['user_id'];

        $hrProbationTargetRepository = (new HrProbationTargetRepository());

        //业务开始，获取业务流程
        $businessInfo = $hrProbationTargetRepository->getTargetBusinessById($targetBusinessId);

        if (empty($businessInfo)) {
            throw new BusinessException('Business Info Empty ');
        }

        $lang    = $this->getLangKey($local['locale']);
        $langKey = 'pdf_path_'.$lang;

        //生成目标PDF
        $pdfUrl = $this->generateStaffPdf($businessInfo, $lang, Enums\StaffEnums::STAFF_TARGET_PDF_OPERATE_STAFF);

        if (!$pdfUrl) {
            throw new Exception('Pdf Generate Error ');
        }

        //生成业务表数据
        $targetBusinessId = $hrProbationTargetRepository->saveTargetBusiness([
            'id'     => $targetBusinessId,
            $langKey => $pdfUrl,
        ]);

        if (!$targetBusinessId) {
            throw new Exception('Probation Business Save Error ');
        }

        return ['code' => ErrCode::SUCCESS, 'msg' => 'ok', 'data' => ['pdf_path' => $pdfUrl]];
    }

    /**
     * 白名单处理
     * @param $params
     * @return bool
     */
    public function deleteWhiteList($params): bool
    {
        $this->logger->info([
            'title'  => '白名单操作',
            'func'   => 'deleteWhiteList',
            'opt'    => 'START',
            'params' => $params,
        ]);

        $db                 = $this->getDI()->get("db_backyard");
        $probationStaff     = (new SettingEnvService())->getSetVal('probation_staff', ',');
        $probationStaffList = array_values(array_unique($probationStaff));

        if (empty($probationStaffList)) {
            $this->logger->info([
                'title' => '白名单操作',
                'func'  => 'deleteWhiteList',
                'opt'   => '无需处理',
            ]);

            return true;
        }

        //查询员工转正信息
        $HrProbationTargetList = HrProbationTargetModel::find([
            'columns'    => 'id,staff_info_id,is_deleted',
            "conditions" => 'staff_info_id in ({staff_info_ids:array})',
            "bind"       => ['staff_info_ids' => $probationStaffList],
        ])->toArray();

        $hrProbationTargetList = array_column($HrProbationTargetList, null, 'staff_info_id');

        foreach ($probationStaffList as $v) {
            if (isset($hrProbationTargetList[$v]) && $hrProbationTargetList[$v]['is_deleted'] == GlobalEnums::NO_DELETED) {
                //进行删除操作
                $db->updateAsDict(
                    (new HrProbationTargetModel())->getSource(),
                    [
                        'is_deleted' => GlobalEnums::DELETED,
                    ],
                    ["conditions" => 'staff_info_id=?', 'bind' => [$v]]
                );

                $this->logger->info([
                    'title'  => '白名单操作',
                    'func'   => 'deleteWhiteList',
                    'opt'    => '删除员工数据',
                    'params' => $v,
                ]);
            }
        }

        return true;
    }

    /**
     * 红点接口
     * @param $param
     * @return void
     */
    public function redCount($params)
    {
        $this->logger->info([
            'title'  => '红点接口',
            'func'   => 'redCount',
            'opt'    => 'START',
            'params' => $params,
        ]);

        //查询员工转正信息
        $returnData = [
            'setting_start_count'  => 0,
            'setting_finish_count' => 0,
        ];

        if (empty($params['user_id'])) {
            return $returnData;
        }

        $returnData['setting_start_count'] = $this->getRedCountInfo(array_merge([
            'tab_setting_state' => HrProbationTargetModel::TAB_SETTING_STATE_NOT_START,
        ], $params));

        $returnData['setting_finish_count'] = $this->getRedCountInfo(array_merge([
            'tab_setting_state' => HrProbationTargetModel::TAB_SETTING_STATE_FINISH,
        ], $params));

        $this->logger->info([
            'title' => '红点接口',
            'func'  => 'redCount',
            'opt'   => 'END',
            'res'   => $returnData,
        ]);

        return $returnData;
    }

    /**
     * 红点数
     * @param $params
     * @param bool $isExport
     * @return array
     */
    public function getRedCountInfo($params)
    {
        $tabSettingState = $params['tab_setting_state'] ?? 0;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(1) as count');
        $builder->from(['target' => HrProbationTargetModel::class]);
        $builder->join(HrStaffInfoModel::class, 'target.staff_info_id = staff.staff_info_id', 'staff');
        $builder->andWhere('target.is_deleted = :target_is_deleted:', ['target_is_deleted' => GlobalEnums::NO_DELETED]);

        //设置状态
        if ($tabSettingState == HrProbationTargetModel::TAB_SETTING_STATE_NOT_START) {
            //待制定的不包含已离职
            $builder->andWhere('staff.state != :staff_state:', ['staff_state' => HrStaffInfoModel::STATE_RESIGN]);
            $builder->andWhere("target.setting_state = :setting_state:",
                ['setting_state' => HrProbationTargetModel::SETTING_STATE_NOT_START]);
        } else {
            $builder->andWhere("target.setting_state != :setting_state:",
                ['setting_state' => HrProbationTargetModel::SETTING_STATE_NOT_START]);
        }

        //数据权限
        $permissionSql = $this->getProbationAuthSql($params['user_id'], $tabSettingState);//红点

        if (!empty($permissionSql['builder_sql']) && !empty($permissionSql['builder_bind'])) {
            $builder->andWhere(implode(' or ', $permissionSql['builder_sql']), $permissionSql['builder_bind']);
        }

        $count = $builder->getQuery()->getSingleResult();
        return (int)$count->count ?? 0;
    }

    /**
     * 保存并发送目标
     * @param array $params 包含发送报告所需数据的关联数组
     * @return bool 成功发送返回 true
     * @throws ValidationException 参数验证失败时抛出异常
     * @throws BusinessException 业务逻辑处理出错时抛出异常
     */
    public function saveSend($params): bool
    {
        $hrProbationTargetRepository = (new HrProbationTargetRepository());
        $probationTargetToolsService = (new ProbationTargetToolsService());

        //参数获取
        $staffInfoId      = $params['staff_info_id'] ?? 0;
        $userId           = $params['user_id'] ?? 0;
        $date             = gmdate('Y-m-d H:i:s');
        $stage            = intval($params['stage'] ?? 0);
        $dutyInfo         = $params['duty_info'] ?? '';
        $paramsTargetInfo = $params['target_info'] ?? [];

        $this->logger->info([
            'title'  => '保存并发送目标',
            'opt'    => '开始执行',
            'func'   => 'saveSend',
            'params' => [
                'staff_info_id' => $staffInfoId,
                'stage'         => $stage,
            ],
        ]);

        //组装数据
        $data = [
            'staff_info_id' => $staffInfoId,
            'stage'         => $stage,
            'duty_info'     => $dutyInfo,
        ];

        //权重验证
        $widgetList = array_column($paramsTargetInfo, 'weight');
        $sum        = array_sum($widgetList);
        if ($sum != HrProbationTargetModel::WIDGET_FINIS) {
            throw new ValidationException(self::$t->_('probation_weight_sum_error'));
        }

        $data['target_info'] = json_encode($paramsTargetInfo, JSON_UNESCAPED_UNICODE);

        //验证用户相关
        if (!$userId) {
            throw new ValidationException(self::$t->_('login_timeout'));
        }

        $staffInfo = (new StaffInfoService())->getStaffInfoOne($staffInfoId);

        //用户离职
        if (empty($staffInfo) || ($staffInfo['state'] == HrStaffInfoModel::STATE_RESIGN)) {
            throw new ValidationException(self::$t->_('staff_info_does_not_exist'));
        }

        //验证目标数据
        $targetBaseInfo = $hrProbationTargetRepository->getTargetInfoByStaffId($staffInfoId);

        if (empty($targetBaseInfo)) {
            throw new ValidationException(self::$t->_('target_data_does_no_exist'));
        }

        //验证转正数据
        $info = $this->checkSaveSendProbationinfo($staffInfoId, $stage);

        //增试用期相关选项
        $deadlineDate = [
            'first_deadline_date'  => '',
            'second_deadline_date' => '',
        ];

        if (!empty($info['first_evaluate_start']) && !empty($info['first_evaluate_end'])) {
            $deadlineDate['first_deadline_date'] = $info['first_evaluate_start'].'——'.$info['first_evaluate_end'];
        }

        if (!empty($info['second_evaluate_start']) && !empty($info['second_evaluate_end'])) {
            $deadlineDate['second_deadline_date'] = $info['second_evaluate_start'].'——'.$info['second_evaluate_end'];
        }

        $this->logger->info([
            'title' => '保存并发送目标',
            'opt'   => '验证完成',
            'func'  => 'saveSend',
        ]);

        $db        = $this->getDI()->get("db_backyard");
        $messageDb = $this->getDI()->get('db_message');
        $db->begin();
        $messageDb->begin();

        try {
            /** 保存目标详情表相关逻辑 **/
            //验证详情表是否存在该阶段数据
            $targetDetailInfo = $hrProbationTargetRepository->getTargetDetailInfoByTargetId(
                $targetBaseInfo['id'],
                $stage
            );

            //目标已发送拦截
            if (!empty($targetDetailInfo) && ($targetDetailInfo['send_state'] == HrProbationTargetModel::SEND_STATE_FINISH)) {
                throw new ValidationException(self::$t->_('target_data_already_send'));
            }

            //保存目标详情表
            $targetDetaiData = array_merge([
                'id'            => $targetDetailInfo['id'] ?? 0,
                'target_id'     => $targetBaseInfo['id'],
                'send_state'    => HrProbationTargetModel::SETTING_STATE_FINISH,
                'sign_state'    => HrProbationTargetModel::SETTING_STATE_NOT_START,
                'send_time'     => $date,
                'setting_state' => HrProbationTargetDetailModel::SETTING_STATE_FINISH,
                'setting_time'  => $date,
                'act_version'   => HrProbationActValuesModel::CURRENT_VERSION, //当前价值观最新版本
            ], $data);

            $targetDetailId = $hrProbationTargetRepository->saveTargetDetail($targetDetaiData);

            if (!$targetDetailId) {
                throw new Exception('Probation Target Detail Save Error ');
            }

            $this->logger->info([
                'title'  => '保存并发送目标',
                'opt'    => '详情表保存完成',
                'func'   => 'saveSend',
                'params' => [
                    'target_detail_id' => $targetDetailId,
                ],
            ]);

            /** 保存主表相关逻辑 **/

            $targetData = [
                'id'            => $targetBaseInfo['id'],
                'setting_state' => HrProbationTargetModel::SETTING_STATE_FINISH,
                'send_state'    => HrProbationTargetModel::SEND_STATE_FINISH,
                'sign_state'    => HrProbationTargetModel::SIGN_STATE_NOT_START,
            ];

            //第二阶段 设置状态为已调整,发送状态为调整后已发送
            if ($stage == HrProbationTargetDetailModel::STAGE_SECOND) {
                $targetData['setting_state'] = HrProbationTargetModel::SETTING_STATE_ADJUST;
                $targetData['send_state']    = HrProbationTargetModel::SEND_STATE_ADJUST_FINISH;
                $targetData['sign_state']    = HrProbationTargetModel::SIGN_STATE_ADJUST_NOT_START;
            }

            //保存主表数据
            $targetRes = $hrProbationTargetRepository->saveTarget($targetData);

            if (!$targetRes) {
                throw new Exception('Probation Target Save Error ');
            }

            $this->logger->info([
                'title'  => '保存并发送目标',
                'opt'    => '主表保存完成',
                'func'   => 'saveSend',
                'params' => [
                    'target_id' => $targetRes,
                ],
            ]);

            /** 保存业务表相关逻辑 **/
            //验证业务部数据，已发送的只能存在一条
            $businessInfo = $hrProbationTargetRepository->getTargetBusinessInfo([
                'target_detail_id' => $targetDetailId,
                'state'            => HrProbationTargetBusinessModel::STATE_NORMAL,
            ]);

            //拦截重复操作
            if ($businessInfo) {
                throw new ValidationException(self::$t->_('target_data_already_send'));
            }

            //固化基本数据
            $staffData = $probationTargetToolsService->weldingTargetBusinessData($staffInfo, $deadlineDate);

            $this->logger->info([
                'title'  => '保存并发送目标',
                'opt'    => '固化员工数据',
                'func'   => 'saveSend',
                'params' => $staffData,
            ]);

            //保存业务表数据
            $targetBusinessId = $hrProbationTargetRepository->saveTargetBusiness([
                'target_id'        => $targetBaseInfo['id'],
                'target_detail_id' => $targetDetailId,
                'staff_info_id'    => $staffInfoId,
                'stage'            => $targetDetaiData['stage'],
                'duty_info'        => $targetDetaiData['duty_info'],
                'target_info'      => $targetDetaiData['target_info'],
                'staff_data'       => json_encode($staffData, JSON_UNESCAPED_UNICODE),
                'send_time'        => $date,
                'send_operate_id'  => $userId,
            ]);

            if (!$targetBusinessId) {
                throw new Exception('Probation Business Save Error ');
            }

            $this->logger->info([
                'title'  => '保存并发送目标',
                'opt'    => '业务表保存完成',
                'func'   => 'saveSend',
                'params' => [
                    'business_id' => $targetBusinessId,
                ],
            ]);

            /** 保存消息表相关逻辑 **/

            //发送消息
            $messageRes = $this->sendTargetMessage([
                'staff_info_id' => $staffInfoId,
                'content'       => json_encode([
                    'business_id' => $targetBusinessId,
                    'type'        => HrProbationTargetMessageModel::TYPE_STAFF,
                ], JSON_UNESCAPED_UNICODE),
                'title'         => ($stage == HrProbationTargetDetailModel::STAGE_SECOND) ? 'probation_target_message_title_re_send' : 'probation_target_message_title_send',
                'category'      => Enums\EnumSingleton::getInstance()->getEnums('MESSAGE_CATEGORY_PROBATION_TARGET'),
            ]);

            if ($messageRes[1] != 1) {
                throw new Exception('Probation Target Message Error ');
            }

            $this->logger->info([
                'title'  => '保存并发送目标',
                'opt'    => '发送消息完成',
                'func'   => 'saveSend',
                'params' => $messageRes,
            ]);

            //保存消息表数据
            $messageData = [
                'target_id'          => $targetBaseInfo['id'],
                'target_business_id' => $targetBusinessId,
                'staff_info_id'      => $staffInfoId,
                'msg_id'             => $messageRes[2][0] ?? '',
                'type'               => HrProbationTargetMessageModel::TYPE_STAFF,
                'operate_id'         => $userId,
            ];

            $targetMessageRes = $hrProbationTargetRepository->saveTargetMessage($messageData);

            if (!$targetMessageRes) {
                throw new Exception('Probation Target Message Business Error ');
            }

            $this->logger->info([
                'title'  => '保存并发送目标',
                'opt'    => '消息业务表完成',
                'func'   => 'saveSend',
                'params' => $messageData,
            ]);

            if (!$db->commit() || !$messageDb->commit()) {
                throw new BusinessException('Common Save Error ');
            }

            /** 抄送数据相关逻辑 **/

            //抄送BP队列初始化
            $rmq = new RocketMQ('probation-target-msg');
            $rmq->setShardingKey($staffInfoId);

            $sendData = [
                'type'               => RocketMQ::TAG_PROBATION_TARGET_MSG,
                'staff_info_id'      => $staffInfoId,
                'target_business_id' => $targetBusinessId,
                'source_type'        => HrProbationTargetMessageModel::SOURCE_TYPE_SEND,
                'operate_id'         => $userId,
            ];

            $rid = $rmq->sendOrderlyMsg($sendData);

            $this->logger->write_log(
                [
                    'title'  => '保存并发送目标',
                    'opt'    => 'BP消息队列发送完成',
                    'func'   => 'saveSend',
                    'params' => $sendData,
                ],
                $rid ? 'info' : 'error'
            );

            /** END **/
        } catch (Exception $exception) {
            $db->rollback();
            $messageDb->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * 验证目标保存并发送试用期考核相关
     * @param $staffInfoId
     * @return mixed
     * @throws ValidationException
     */
    public function checkSaveSendProbationinfo($staffInfoId , $stage)
    {
        $info = (new HrProbationTargetRepository())->getProbationInfoByStaffId($staffInfoId);

        if (!empty($info)) {
            //员工已经转正，不可以发送
            if (($info['status'] == HrProbationModel::STATUS_CORRECTED)) {
                throw new ValidationException(self::$t->_('staff_probation_state_corrected_not_operate'));
            }

            //用户第二阶段已经通过不可以发送
            if (($info['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE)) {
                throw new ValidationException(self::$t->_('staff_second_audit_status_success_not_operate'));
            }

            //用户第二阶段已经通过不可以发送
            if (HrProbationTargetDetailModel::STAGE_SECOND == $stage) {
                if (($info['second_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_WAIT)) {
                    throw new ValidationException(self::$t->_('staff_second_audit_status_wait_not_operate'));
                }

                if ($info['first_audit_status'] != HrProbationModel::SECOND_AUDIT_STATUS_DONE) {
                    throw new ValidationException(self::$t->_('target_first_evaluat_state_error_not_setting'));
                }

                $firstEvaluateEnd = $info['first_evaluate_end'];
                $firstEvaluateEnd = strtotime("{$firstEvaluateEnd} + 7 days");

                if ((time() >= $firstEvaluateEnd)) {
                    throw new ValidationException(self::$t->_('target_data_time_first_evaluat_over_not_send'));
                }
            }

            //用户第一阶段已经发起不可以发送第一阶段目标
            if (
                ($info['first_audit_status'] != HrProbationModel::FIRST_AUDIT_STATUS_WAIT)
                && (HrProbationTargetDetailModel::STAGE_FIRST == $stage)
            ) {
                throw new ValidationException(self::$t->_('staff_first_audit_status_wait_not_operate'));
            }
        }

        return $info;
    }
}