<?php

namespace App\Services;

use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoReadModel;
use app\models\backyard\HrStaffInvestigationFileModel;
use App\Models\backyard\HrStaffInvestigationModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffAuditModel;
use App\Modules\My\Services\ContractStaffService;
use DateTime;
use App\Library\BiMail;

/**
 * 员工停职调查
 */
class InvestigationService extends BaseService
{
    //pdf 编号 需要递增
    public static $pdf_key = 'investigation_pdf';
    public $support_file_type = ['jpg','jpeg','png','bmp','doc','docx','pdf','xls','csv','xlsx','mp4','avi','mov'];
    public $max_file_size = 200 * 1024 * 1024;//最大文件200m
    public $max_file_count = 9;

    public function initialize()
    {
        parent::initialize();
    }


    public function addRecord($param)
    {
        $this->checkAdd($param);
        $insert['staff_info_id'] = $param['staff_info_id'];
        $insert['start_date']    = $param['start_date'];
        $insert['is_stop']       = $param['is_stop'];
        if ($param['is_stop'] == HrStaffInvestigationModel::IS_STOP_YES) {
            $insert['stop_date'] = $param['stop_date'];
            if (!empty($param['hold_type'])) {
                $insert['hold_type']   = $param['hold_type'];
                $insert['hold_reason'] = $param['hold_reason'] ?? '';
                $insert['month_begin'] = $param['month_begin'] ?? '';
                $insert['month_end']   = $param['month_end'] ?? '';
            }
        }
        $insert['remark']        = $param['remark'];
        $insert['operator']      = $param['user_info']['id'];
        $insert['operator_name'] = $param['user_info']['name'];
        $model                   = new HrStaffInvestigationModel();
        $model->create($insert);

        if (!empty($param['file_url'])) {
            $file_arr = [];
            foreach ($param['file_url'] as $item) {
                $file['origin_id'] = $model->id;
                $file['file_type'] = HrStaffInvestigationFileModel::TYPE_ADD;
                $file['file_url']  = $item['url'];
                $file['file_name'] = $item['name'];
                $file_arr[]        = $file;
            }
            $file_model = new HrStaffInvestigationFileModel();
            $file_model->batch_insert($file_arr);
        }

        //如果停职 需要发消息
        if ($param['is_stop'] == HrStaffInvestigationModel::IS_STOP_YES) {
            $this->send_msg($param);
        }

        return true;
    }

    public function checkAdd($param)
    {
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => [
                'staff_id' => $param['staff_info_id'],
            ],
        ]);
        if (empty($staff_info)) {
            throw new ValidationException('staff id error');
        }
        $staff_info = $staff_info->toArray();

        //是否停职
        if ($staff_info['state'] == HrStaffInfoModel::STATE_RESIGN) {
            throw new ValidationException('have_been_stop');
        }
        //离职
        if ($staff_info['state'] == HrStaffInfoModel::STATE_SUSPEND) {
            throw new ValidationException('have_been_leave');
        }
        //雇佣类型=正式员工&月薪制合同工的工号 不包括：实习生/个人代理/兼职个人代理/外协/子账号
        if ($staff_info['formal'] != HrStaffInfoModel::FORMAL_1 || !in_array($staff_info['hire_type'], [
                HrStaffInfoModel::HIRE_TYPE_1,
                HrStaffInfoModel::HIRE_TYPE_2,
            ]) || $staff_info['is_sub_staff'] == HrStaffInfoModel::IS_SUB_STAFF) {
            throw new ValidationException('investigation_staff_error');
        }

        $exist = HrStaffInvestigationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and state = :state:',
            'bind'       => [
                'staff_id' => $param['staff_info_id'],
                'state'    => HrstaffInvestigationModel::STATE_UN_DO,
            ],
        ]);
        if (!empty($exist)) {
            throw new ValidationException('have_been_investigation');
        }

        //停职日期判断
        if ($param['is_stop'] == HrStaffInvestigationModel::IS_STOP_YES && empty($param['stop_date'])) {
            throw new ValidationException('need_stop_date');
        }
        $today  = date('Y-m-d');
        $date_3 = date('Y-m-d', strtotime('+3 day'));

        if ($param['is_stop'] == HrStaffInvestigationModel::IS_STOP_YES && !empty($param['stop_date'])) {
            //日期只能明天往后3天
            if (strtotime($param['stop_date']) <= strtotime($today)) {
                throw new ValidationException('stop_date_error');
            }
            if (strtotime($param['stop_date']) > strtotime($date_3)) {
                throw new ValidationException('stop_date_error');
            }
        }

        //是否 hold HoldManageService::$hold_reasons
        if ($param['is_hold'] == HoldStaffManageModel::IS_HANDLE_HOLD_YES && empty($param['hold_reason'])) {
            throw new ValidationException('need hold reason');
        }
        $this->check_file($param['file_url']);
        return true;
    }

    public function list($param)
    {
        $size = $param['size'] ?? 100;
        $page = $param['page'] ?? 1;
        //分页数据
        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->from(['itg' => HrStaffInvestigationModel::class]);
        $audit_object->leftJoin(HrStaffInfoReadModel::class, 'itg.staff_info_id = i.staff_info_id', 'i');
        //通用数据权限限定条件
        $audit_object = $this->condition($param, $audit_object);

        $audit_object->columns(
            'i.name as staff_name,i.sys_store_id,i.node_department_id,i.job_title,i.wait_leave_state,i.state as staff_state,
            itg.id,itg.staff_info_id,itg.state,itg.start_date,itg.end_date,itg.is_stop,itg.stop_date,itg.hold_type,itg.hold_reason,itg.remark,
            itg.result_state,itg.result_date,itg.leave_type,itg.leave_reason,itg.leave_scenario,itg.is_send,itg.letter_no,itg.send_date,
            itg.letter_law,itg.operator,itg.operator_name,itg.end_operator,itg.end_operator_name,itg.final_time,itg.created_at,itg.updated_at'
        );
        $offset = $size * ($page - 1);
        $audit_object->limit($size, $offset);
        $audit_object->orderBy('itg.id DESC');
        $list = $audit_object->getQuery()->execute()->toArray();

        if (empty($list)) {
            return [];
        }
        $text_key      = Enums::$hris_working_state;//在职状态
        $is_stop       = HrStaffInvestigationModel::$is_stop_enum;//是否停职
        $hold_type     = HoldStaffManageModel::$hold_type;//hold 类型
        $finish_status = HrStaffInvestigationModel::$finish_status;//调查是否结束
        $result_state  = HrStaffInvestigationModel::$result;//处理状态 离职还是恢复在职还是不处理
        $is_send       = HrStaffInvestigationModel::$is_stop_enum;//是否发了 解释信
        $leaveServer   = new LeaveManagerService();
        //整理数据
        foreach ($list as &$li) {
            $li['store_name']      = $this->showStoreName($li['sys_store_id']);
            $li['department_name'] = $this->showDepartmentName($li['node_department_id']);
            $li['job_name']        = $this->showJobTitleName($li['job_title']);
            $staffState            = $li['staff_state'];
            if ($li['staff_state'] == Enums::HRIS_WORKING_STATE_1 && $li['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {
                $staffState = Enums::HRIS_WORKING_STATE_4;
            }
            $li['staff_state_text'] = empty($text_key[$staffState]) ? '' : static::$t->_($text_key[$staffState]);
            $li['is_stop_text']     = static::$t->_($is_stop[$li['is_stop']]);
            $li['hold_type_text']   = static::$t->_($hold_type[$li['hold_type']]);
            $li['hold_reason']      = static::$t->_($li['hold_reason']);
            $li['created_at']       = show_time_zone($li['created_at']);
            //创建人
            $li['operate_text'] = empty($li['operator']) ? '' : $li['operator_name'] . "({$li['operator']})";
            //如果是调查结束页面 拆分  result_date 为 back_date 和 leave_date
            if ($param['tab'] == HrStaffInvestigationModel::STATE_DONE) {
                $li['back_date'] = $li['leave_date'] = '';
                if ($li['result_state'] == HrStaffInvestigationModel::RESULT_RESTORE) {
                    $li['back_date'] = $li['result_date'];
                }
                if ($li['result_state'] == HrStaffInvestigationModel::RESULT_LEAVE) {
                    $li['leave_date'] = $li['result_date'];
                }

                //调查是否完成
                $state = $li['state'];
                //脚本完成状态替换成调查完成
                $state            = $state == HrStaffInvestigationModel::STATE_FINISH ? HrStaffInvestigationModel::STATE_DONE : $state;
                $li['state_text'] = static::$t->_($finish_status[$state]);

                //是否发解释信
                $li['is_send_text'] = empty($li['is_send']) ? '' : static::$t->_($is_send[$li['is_send']]);

                //调查结果
                $li['result_state_text'] = static::$t->_($result_state[$li['result_state']]);
                //离职类型
                $li['leave_type_text'] = empty($li['leave_type']) ? '' : static::$t->_("hris_leave_type_" . $li['leave_type']);
                //离职原因
                $li['leave_reason_text'] = empty($li['leave_reason']) ? '' : $leaveServer->getReasonKeyByCode($li['leave_reason']);
                //离职场景
                $li['leave_scenario_text'] = empty($li['leave_reason']) ? '' : static::$t->_('leave_scenario_' . $li['leave_scenario']);

                //最后更新人
                $li['end_operate_text'] = empty($li['end_operator']) ? '' : $li['end_operator_name'] . "({$li['end_operator']})";
                //更新时间
                $li['updated_at'] = show_time_zone($li['updated_at']);
            }
        }
        return $list;
    }


    public function condition($param, $audit_object)
    {
        $audit_object->andWhere("itg.is_delete = 0");
        //过滤用户
        if (!empty($param['staff_info_id'])) {
            $audit_object->andWhere("itg.staff_info_id = :staff_id:", ['staff_id' => $param['staff_info_id']]);
        }
        //过滤职位
        if (!empty($param['job_title'])) {
            $audit_object->andWhere("i.job_title = :job_title:", ['job_title' => $param['job_title']]);
        }
        //部门
        if (!empty($param['department_id'])) {
            $department_ids = [$param['department_id']];
            if (!empty($param['is_sub'])) {
                $department_server = new SysDepartmentService();
                $sub               = $department_server->getChildrenListByDepartmentId($param['department_id'], true);
                $department_ids    = array_merge($department_ids, $sub);
            }
            $audit_object->inWhere("i.node_department_id ", $department_ids);
        }
        //过滤网点
        if (!empty($param['store_id'])) {
            $audit_object->andWhere("i.sys_store_id = :store_id:", ['store_id' => $param['store_id']]);
        }

        //开始调查日期
        if (!empty($param['start_time']) && !empty($param['end_time'])) {
            $audit_object->betweenWhere("itg.start_date ", $param['start_time'], $param['end_time']);
        }

        //是否停职
        if (!empty($param['is_stop'])) {
            $audit_object->andWhere("itg.is_stop = :is_stop:", ['is_stop' => $param['is_stop']]);
        }

        //停职日期
        if (!empty($param['stop_start_time']) && !empty($param['stop_end_time'])) {
            $audit_object->betweenWhere("itg.stop_date ", $param['stop_start_time'], $param['stop_end_time']);
        }

        //hold 类型
        if (!empty($param['hold_type']) || (isset($param['hold_type']) && $param['hold_type'] === '0')) {
            $audit_object->andWhere("itg.hold_type = :hold_type:", ['hold_type' => $param['hold_type']]);
        }

        //创建人
        if (!empty($param['operate_id'])) {
            $audit_object->andWhere("itg.operator = :operate_id:", ['operate_id' => $param['operate_id']]);
        }

        //创建时间
        if (!empty($param['create_start_time']) && !empty($param['create_end_time'])) {
            $_submit_start_time = gmdate('Y-m-d H:i:s',
                strtotime($param['create_start_time']) - $this->config->application->add_hour * 3600);
            $_submit_end_time   = gmdate('Y-m-d H:i:s',
                strtotime($param['create_end_time']) + 86399 - $this->config->application->add_hour * 3600);
            $audit_object->betweenWhere("itg.created_at", $_submit_start_time, $_submit_end_time);
        }

        //调查状态
        if ($param['tab'] == HrStaffInvestigationModel::STATE_UN_DO) {
            $states = [HrStaffInvestigationModel::STATE_UN_DO];
        } else {
            $states = [HrStaffInvestigationModel::STATE_DONE, HrStaffInvestigationModel::STATE_FINISH];
        }
        $audit_object->inWhere("itg.state ", $states);

        //调查结束才有的筛选
        if (!empty($param['final_start_time']) && !empty($param['final_end_time'])) {
            $audit_object->betweenWhere("itg.end_date ", $param['final_start_time'], $param['final_end_time']);
        }

        //离职日期
        if (!empty($param['leave_start_time']) && !empty($param['leave_end_time'])) {
            $audit_object->andWhere("itg.result_state = :result_state:",
                ['result_state' => HrStaffInvestigationModel::RESULT_LEAVE]);
            $audit_object->betweenWhere("itg.result_date ", $param['leave_start_time'], $param['leave_end_time']);
        }
        //恢复在职日期
        if (!empty($param['back_start_time']) && !empty($param['back_end_time'])) {
            $audit_object->andWhere("itg.result_state = :result_state:",
                ['result_state' => HrStaffInvestigationModel::RESULT_RESTORE]);
            $audit_object->betweenWhere("itg.result_date ", $param['back_start_time'], $param['back_end_time']);
        }

        //最后操作人
        if (!empty($param['final_operate_id'])) {
            $audit_object->andWhere("itg.end_operator = :end_operator:",
                ['end_operator' => $param['final_operate_id']]);
        }

        //最后更新时间
        if (!empty($param['update_start_time']) && !empty($param['update_end_time'])) {
            $up_start = gmdate('Y-m-d H:i:s',
                strtotime($param['update_start_time']) - $this->config->application->add_hour * 3600);
            $up_end   = gmdate('Y-m-d H:i:s',
                strtotime($param['update_end_time']) + 86399 - $this->config->application->add_hour * 3600);
            $audit_object->betweenWhere("itg.updated_at", $up_start, $up_end);
        }

        //在职状态
        if (!empty($param['staff_state'])) {
            $tmp_sql = [];
            if (in_array(HrStaffInfoModel::STATE_PENDING_RESIGNATION, $param['staff_state'])) {
                $tmp_sql[] = '( i.state = 1 and  i.wait_leave_state  = 1 )';
            }
            if (in_array(HrStaffInfoModel::STATE_ON_JOB, $param['staff_state'])) {
                $tmp_sql[] = '( i.state = ' . HrStaffInfoModel::STATE_ON_JOB . ' and i.wait_leave_state  = 0) ';
            }
            if (in_array(HrStaffInfoModel::STATE_RESIGN, $param['staff_state'])) {
                $tmp_sql[] = 'i.state = ' . HrStaffInfoModel::STATE_RESIGN;
            }
            if (in_array(HrStaffInfoModel::STATE_SUSPEND, $param['staff_state'])) {
                $tmp_sql[] = 'i.state = ' . HrStaffInfoModel::STATE_SUSPEND;
            }
            $_sql = implode(' or ', $tmp_sql);
            $audit_object->andWhere($_sql);
        }

        return $audit_object;
    }

    public function getCount($param)
    {
        $audit_object = $this->modelsManager->createBuilder();
        $audit_object->from(['itg' => HrStaffInvestigationModel::class]);
        $audit_object->leftJoin(HrStaffInfoReadModel::class, 'itg.staff_info_id = i.staff_info_id', 'i');
        $audit_object->columns("count(1) as total");
        $audit_object = $this->condition($param, $audit_object);
        //查总数
        $totalInfo = $audit_object->getQuery()->getSingleResult();
        $count     = intval($totalInfo->total);
        return $count;
    }

    //导出调查中 和 调查结束
    public function export($param)
    {
        $param['page'] = 1;
        $param['size'] = 500;
        $data          = [];
        while (true) {
            $res = $this->list($param);
            if (empty($res)) {
                break;
            }
            $data = array_merge($data, $res);
            $param['page']++;
        }
        //调查中列表
        $is_short = $param['tab'] == HrStaffInvestigationModel::STATE_UN_DO;

        $list = [];
        foreach ($data as $da) {
            $row = [];
            if ($is_short) {
                $row['staff_info_id']    = $da['staff_info_id'];
                $row['staff_name']       = $da['staff_name'];
                $row['job_name']         = $da['job_name'];
                $row['department_name']  = $da['department_name'];
                $row['store_name']       = $da['store_name'];
                $row['staff_state_text'] = $da['staff_state_text'];
                $row['start_date']       = $da['start_date'];
                $row['is_stop_text']     = $da['is_stop_text'];
                $row['stop_date']        = $da['stop_date'];
                $row['hold_type_text']   = $da['hold_type_text'];
                $row['hold_reason']      = $da['hold_reason'];
                $row['operate_text']     = $da['operate_text'];
                $row['created_at']       = $da['created_at'];
            } else {
                $row['staff_info_id']     = $da['staff_info_id'];
                $row['staff_name']        = $da['staff_name'];
                $row['job_name']          = $da['job_name'];
                $row['department_name']   = $da['department_name'];
                $row['store_name']        = $da['store_name'];
                $row['staff_state_text']  = $da['staff_state_text'];
                $row['state_text']        = $da['state_text'];
                $row['start_date']        = $da['start_date'];
                $row['is_stop_text']      = $da['is_stop_text'];
                $row['stop_date']         = $da['stop_date'];
                $row['hold_type_text']    = $da['hold_type_text'];
                $row['hold_reason']       = $da['hold_reason'];
                $row['end_date']          = $da['end_date'];
                $row['result_state_text'] = $da['result_state_text'];
                $row['leave_date']        = $da['leave_date'];
                $row['leave_type']        = $da['leave_type'];
                $row['leave_reason']      = $da['leave_reason'];
                $row['leave_scenario']    = $da['leave_scenario'];
                $row['back_date']         = $da['back_date'];
                $row['operate_text']      = $da['operate_text'];
                $row['created_at']        = $da['created_at'];
                $row['end_operate_text']  = $da['end_operate_text'];
                $row['updated_at']        = $da['updated_at'];
            }
            $list[] = $row;
        }

        $header = $this->format_header($param['tab']);

        return [$header, $list];
    }

    //补发数据
    public function export_attendance($param)
    {
        $header[] = self::$t->_('staff_id');//工号
        $header[] = self::$t->_('stop_duties_date');//停职日期
        $header[] = self::$t->_('back_date');//恢复在职日期
        $header[] = self::$t->_('send_days');//补发天数
        $header[] = self::$t->_('send_dates');//补发日期

        $month = $param['month'];
        $date  = date('Y-m-01', strtotime($month));
        //当前月周期
        $current_cycle = $this->getPeriodInfo(new DateTime($date));
        //上个周期r
        $last_date  = date('Y-m-01', strtotime('-1 month', strtotime($date)));
        $last_cycle = $this->getPeriodInfo(new DateTime($last_date));

        //选取数据 为 停职日期在上个周期，恢复在职日期在 当前周期
        $data = HrStaffInvestigationModel::find([
            'conditions' => 'state in ({state:array}) and result_state = :result_state: and is_stop = :is_stop:
                            and result_date between :current_start: and :current_end: 
                            and stop_date between :last_start: and :last_end:',
            'bind'       => [
                            'state'         => [HrStaffInvestigationModel::STATE_DONE, HrStaffInvestigationModel::STATE_FINISH],
                            'result_state'  => HrStaffInvestigationModel::RESULT_RESTORE,
                            'is_stop'       => HrStaffInvestigationModel::IS_STOP_YES,
                            'current_start' => $current_cycle['periodStart'],
                            'current_end'   => $current_cycle['periodEnd'],
                            'last_start'    => $last_cycle['periodStart'],
                            'last_end'      => $last_cycle['periodEnd'],
            ],
        ])->toArray();
        if(empty($data)){
            return [$header, []];
        }

        //计算出 停职日期 开始 到上个周期结束日期 为补发数据
        $list = [];
        foreach ($data as $da){
            $row['staff_info_id'] = $da['staff_info_id'];
            $row['stop_date'] = $da['stop_date'];
            $row['result_date'] = $da['result_date'];
            //计算日期
            $date_list = DateHelper::DateRange(strtotime($da['stop_date']), strtotime($last_cycle['periodEnd']));
            $row['days_num'] = round(count($date_list) / 2, 1);
            //处理日期 去掉年
            $date_list = array_map(function ($date) {
                return date('m-d', strtotime($date));
            }, $date_list);
            $row['dates'] = implode(',', $date_list);
            $list[] = $row;
        }
        return [$header, $list];
    }



    //导出数据处理
    public function format_header($tab)
    {
        if ($tab == HrStaffInvestigationModel::STATE_UN_DO) {
            $header[] = self::$t->_('staff_id');
            $header[] = self::$t->_('name');
            $header[] = self::$t->_('job_name');
            $header[] = self::$t->_('department');
            $header[] = self::$t->_('sys_store_name');
            $header[] = self::$t->_('staff_state');
            $header[] = self::$t->_('investigation_start_date');
            $header[] = self::$t->_('stop_case');
            $header[] = self::$t->_('stop_duties_date');
            $header[] = self::$t->_('warning_hold_type');
            $header[] = self::$t->_('hold_reason');
            $header[] = self::$t->_('creator');
            $header[] = self::$t->_('created_at');
        } else {
            $header[] = self::$t->_('staff_id');
            $header[] = self::$t->_('name');
            $header[] = self::$t->_('job_name');
            $header[] = self::$t->_('department');
            $header[] = self::$t->_('sys_store_name');
            $header[] = self::$t->_('staff_state');
            $header[] = self::$t->_('investigation_state');
            $header[] = self::$t->_('investigation_start_date');
            $header[] = self::$t->_('stop_case');
            $header[] = self::$t->_('stop_duties_date');
            $header[] = self::$t->_('warning_hold_type');
            $header[] = self::$t->_('hold_reason');
            $header[] = self::$t->_('investigation_end_date');
            $header[] = self::$t->_('investigation_result');
            $header[] = self::$t->_('leave_date');
            $header[] = self::$t->_('resign_type');
            $header[] = self::$t->_('leave_reason');
            $header[] = self::$t->_('leave_scenario');
            $header[] = self::$t->_('back_date');
            $header[] = self::$t->_('creator');
            $header[] = self::$t->_('created_at');
            $header[] = self::$t->_('latest_update_operator');
            $header[] = self::$t->_('latest_update_time');
        }
        return $header;
    }

    public function edit($param)
    {
        $id   = (int)$param['id'];
        $info = HrStaffInvestigationModel::findFirst($id);
        if (empty($info)) {
            throw new ValidationException('id error');
        }
        $this->check_file($param['file_url']);
        //调查中的编辑 只能修改 说明 和 附件
        if ($param['finish_status'] == HrStaffInvestigationModel::STATE_UN_DO) {
            //只是编辑基础信息
            if ($info->remark != $param['remark']) {
                $info->remark = $param['remark'];
                $info->update();
            }
            $this->deal_file($id, $param);
            return true;
        }

        //调查结束操作
        if (empty($param['result_state'])) {// 1 不处理 2 恢复在职  3 离职辞退
            throw new ValidationException('result_state error');
        }
        //调查结束说明
        if (empty($param['result_remark'])) {
            throw new ValidationException('result_remark error');
        }

        //结束调查日期 用户选的
        if (empty($param['end_date'])) {
            throw new ValidationException('end_date error');
        }
        $today   = date('Y-m-d');
        $date_30 = date('Y-m-d', strtotime('-30 days'));
        if (strtotime($param['end_date']) < strtotime($date_30) || strtotime($param['end_date']) > strtotime($today)) {
            throw new ValidationException('end_date select error');
        }
        $info->end_date          = $param['end_date'];//结束日期
        $info->state             = HrStaffInvestigationModel::STATE_DONE;//结束状态 1:调查中 2:调查结束 3:调查结束并且已处理完成
        $info->remark            = $param['remark'];//调查说明
        $info->result_remark     = $param['result_remark'];//调查结束说明
        $info->final_time        = date('Y-m-d H:i:s');//结束操作时间
        $info->result_state      = $param['result_state'];
        $info->end_operator      = $param['user_info']['id'];//操作人
        $info->end_operator_name = $param['user_info']['name'];//操作人
        //无需处理
        if ($param['result_state'] == HrStaffInvestigationModel::RESULT_UN_DO) {
            $info->state = HrStaffInvestigationModel::STATE_FINISH;//无需脚本处理 直接完成
        }

        //恢复在职
        if ($param['result_state'] == HrStaffInvestigationModel::RESULT_RESTORE) {
            $info->result_date = $param['back_date'];//离职日期
            //日期不能在停职日期 之前
            if (!empty($info->stop_date) && strtotime($info->stop_date) >= strtotime($param['back_date'])) {
                throw new ValidationException(static::$t->_("before_stop_date"));
            }
        }

        //离职
        if ($param['result_state'] == HrStaffInvestigationModel::RESULT_LEAVE) {
            $info->result_date = $param['leave_date'];//离职日期
            //日期不能在停职日期 之前
            if (!empty($info->stop_date) && strtotime($info->stop_date) >= strtotime($param['leave_date'])) {
                throw new ValidationException(static::$t->_("before_stop_date"));
            }
            if ($param['is_send'] == HrStaffInvestigationModel::IS_SEND_YES) {
                //发送日期
                if (empty($param['send_date'])) {
                    throw new ValidationException('send_date error');
                }
                //信编号
                if (empty($param['letter_no'])) {
                    throw new ValidationException('letter_no error');
                }
            }
            //违反条文
            if (empty($param['letter_law'])) {
                throw new ValidationException('letter_law error');
            }
            if(mb_strlen($param['letter_law']) > 5000){
                throw new ValidationException('at most 5000 characters');
            }
            //离职类型 int
            $info->leave_type = $param['leave_type'];
            //离职原因 int
            $info->leave_reason = $param['leave_reason'];
            //离职场景
            $info->leave_scenario = $param['leave_scenario'];

            //验证文件 调查结束问阿金
            $this->check_file($param['finish_file_url']);

        }
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $this->deal_file($id, $param);//调查中文件
            if (!empty($param['finish_file_url'])) {
                $this->finish_file($id, $param);//调查结束文件
            }
            $info->update();//主表
            $this->send_msg($param);
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error(['id' => $id, 'investigation_edit' => $e->getMessage()]);
            throw new ValidationException('server error');
        }
        return true;
    }

    //验证文件 类型 大小
    public function check_file($file_arr)
    {
        foreach ($file_arr as $item) {
            if (!in_array(strtolower(pathinfo($item['url'], PATHINFO_EXTENSION)), $this->support_file_type)) {
                throw new ValidationException('System only support file type :' . implode(',',
                        $this->support_file_type));
            }
            $headers = get_headers($item['url'], 1); // 获取响应头
            $size    = isset($headers['Content-Length']) ? (int)$headers['Content-Length'] : 0;
            if ($size > $this->max_file_size) {
                throw new ValidationException('file size over 200M');
            }
        }
        if (count($file_arr) > $this->max_file_count) {
            throw new ValidationException("at most {$this->max_file_count} files");
        }
        return true;
    }

    //处理文件
    public function deal_file($id, $param)
    {
        try {
            //删附件 加附件 不用比对
            $db = $this->getDI()->get('db_backyard');
            $db->begin();
            $sql        = "update hr_staff_investigation_file set is_delete = 1 where origin_id =:id";
            $bind['id'] = $id;
            $db->execute($sql, $bind);

            if (!empty($param['file_url'])) {
                $file_arr = [];
                foreach ($param['file_url'] as $item) {
                    $file['origin_id'] = $id;
                    $file['file_type'] = HrStaffInvestigationFileModel::TYPE_ADD;
                    $file['file_url']  = $item['url'];
                    $file['file_name'] = $item['name'];
                    $file_arr[]        = $file;
                }
                $file_model = new HrStaffInvestigationFileModel();
                $file_model->batch_insert($file_arr);
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error(['id' => $id, 'investigation_deal_file' => $e->getMessage()]);
            throw new ValidationException('server error');
        }
        return true;
    }

    //调查结束文件 新增
    public function finish_file($id, $param)
    {
        $file_arr = [];
        foreach ($param['finish_file_url'] as $item) {
            $file['origin_id'] = $id;
            $file['file_type'] = HrStaffInvestigationFileModel::TYPE_FINISH;
            $file['file_url']  = $item['url'];
            $file['file_name'] = $item['name'];
            $file_arr[]        = $file;
        }
        $file_model = new HrStaffInvestigationFileModel();
        $file_model->batch_insert($file_arr);
    }


    public function cancel($param)
    {

        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try{
            $id   = $param['id'];
            $info = HrStaffInvestigationModel::findFirst($id);
            if (empty($info)) {
                throw new ValidationException('id error');
            }

            //停职状态是否 或者 停职日期 大于 当前日期 才可以撤销
            $today = date('Y-m-d');
            if ($info->is_stop == HrStaffInvestigationModel::IS_STOP_YES && $info->stop_date <= $today) {
                throw new ValidationException('cannot_cancel_investigation');
            }

            $info->is_delete = 1;
            $info->update();
            //附件表
            $file_data = HrStaffInvestigationFileModel::find([
                'origin_id = :id:',
                'bind' => ['id' => $id]
            ])->update(['is_delete' => 1]);

            $db->commit();
        }catch (\Exception $e){
            throw new ValidationException('server error');
            $this->logger->error(['investigation_cancel' => $e->getMessage()]);
            $db->rollback();
        }

        return true;
    }

    public function info($param)
    {
        $id = $param['id'];

        $info = HrStaffInvestigationModel::findFirst($id);
        if (empty($info)) {
            throw new ValidationException('id error');
        }
        $info = $info->toArray();

        $staff_info                    = (new StaffService())->getStaffView($info['staff_info_id']);
        $staff['name']                 = $staff_info['name'];
        $staff['staff_info_id']        = $staff_info['staff_info_id'];
        $staff['job_title_name']       = $staff_info['job_title_name'];
        $staff['node_department_name'] = $staff_info['node_department_name'];
        $staff['store_name']           = $staff_info['store_name'];
        $staff['region_name']          = $staff_info['region_name'];
        $staff['piece_name']           = $staff_info['piece_name'];
        $staff['state_name']           = $staff_info['state_name'];
        $info['staff_info']            = $staff;
        //是否选了hold 周期
        $info['hold_cycle'] = (string)HrStaffInvestigationModel::HOLD_CYCLE_NO;
        if ($info['is_stop'] == HrStaffInvestigationModel::IS_STOP_YES && !empty($info['hold_type']) && !empty($info['month_begin'])) {
            $info['hold_cycle'] = (string)HrStaffInvestigationModel::HOLD_CYCLE_MONTH;
        }
        $info['result_state'] = empty($info['result_state']) ? '' : $info['result_state'];

        //离职类型
        $leaveServer             = new LeaveManagerService();
        $info['leave_type_text'] = empty($info['leave_type']) ? '' : static::$t->_("hris_leave_type_" . $info['leave_type']);
        //离职原因
        $info['leave_reason_text'] = empty($info['leave_reason']) ? '' : $leaveServer->getReasonKeyByCode($info['leave_reason']);
        //离职场景
        $info['leave_scenario_text'] = empty($info['leave_scenario']) ? '' : static::$t->_('leave_scenario_' . $info['leave_scenario']);


        $info['file_url'] = $info['finish_file_url'] = [];
        $file_info        = HrStaffInvestigationFileModel::find([
            'conditions' => 'origin_id = :origin_id:',
            'bind'       => ['origin_id' => $id],
        ])->toArray();
        if (empty($file_info)) {
            return $info;
        }
        foreach ($file_info as $file) {
            $row['url']  = $file['file_url'];
            $row['name'] = $file['file_name'];
            if ($file['file_type'] == HrStaffInvestigationFileModel::TYPE_ADD) {
                $info['file_url'][] = $row;
                continue;
            }
            if ($file['file_type'] == HrStaffInvestigationFileModel::TYPE_FINISH) {
                $info['finish_file_url'][] = $row;
            }
        }
        return $info;
    }

    public function getEnumList()
    {
        $res = [];
        //是否停职 停职/不停职
        $is_stop        = HrStaffInvestigationModel::$is_stop_enum;
        $res['is_stop'] = $this->formatEnum($is_stop);

        //hold 类型 枚举值有 hold工资提成/hold工资/hold提成/不hold
        $hold_type        = HoldStaffManageModel::$hold_type;
        $res['hold_type'] = $this->formatEnum($hold_type);

        //hold 周期 不发放或者 限制月份
        $hold_cycle        = HrStaffInvestigationModel::$hold_cycle;
        $res['hold_cycle'] = $this->formatEnum($hold_cycle);

        //hold 原因
        $hold_server        = new HoldManageService();
        $res['hold_reason'] = $hold_server->getHoldReason();


        //在职状态
        $sys_server         = new SysService();
        $res['staff_state'] = $sys_server->HrisAllWorkingState();

        //调查结束情况
        $finish_status        = HrStaffInvestigationModel::$finish_status;
        $res['finish_status'] = $this->formatEnum($finish_status);

        //调查结果
        $result              = HrStaffInvestigationModel::$result;
        $res['result_state'] = $this->formatEnum($result);

        //离职原因
        $leave_data = $sys_server->getLeaveReason();
        $res['leave_type'] = [];
        if(!empty($leave_data)){
            $res['leave_type'][] = $leave_data[1];//code 1 辞退（不赔偿）
            $res['leave_type'][] = $leave_data[2];//code 2 辞退（赔偿）
        }

        //解释信发送情况
        $is_send        = HrStaffInvestigationModel::$is_send_enum;
        $res['is_send'] = $this->formatEnum($is_send);
        return $res;
    }


    //处理枚举 给前端
    public function formatEnum($list)
    {
        $data = [];
        foreach ($list as $key => $value) {
            $data[] = [
                "value" => strval($key),
                "label" => self::$t->_($value),
            ];
        }
        return $data;
    }

    //新增停职 和 操作离职 恢复在职 要发消息
    public function send_msg($param)
    {
        $title_key = $content_key = '';
        if (!empty($param['is_stop']) && $param['is_stop'] == HrStaffInvestigationModel::IS_STOP_YES) {
            $title_key           = 'investigation_stop_title';
            $content_key         = 'investigation_stop_content';
            $msg_data['date_at'] = $param['stop_date'];
        }
        if (!empty($param['result_state']) && $param['result_state'] == HrStaffInvestigationModel::RESULT_RESTORE) {
            $title_key   = 'investigation_stop_title';
            $content_key = 'investigation_stop_content';
        }

        if (!empty($param['result_state']) && $param['result_state'] == HrStaffInvestigationModel::RESULT_LEAVE) {
            $title_key   = 'investigation_leave_title';
            $content_key = 'investigation_leave_content';
        }
        //其他情况不发消息
        if (empty($title_key)) {
            return true;
        }

        //找大区片区 上级 和 hrbp
        $staff_info  = (new StaffService())->getStaffView($param['staff_info_id']);
        $find_server = new RenewContractBusinessService();
        $find_server = reBuildCountryInstance($find_server);
        $manager     = $find_server->getManager($staff_info);

        // 获取接收人最后一次登入的语言
        $accept_language = (new StaffService())->getAcceptLanguage($param['staff_info_id']);
        $t               = self::getTranslation($accept_language);

        //消息变量
        $msg_data['staff_text'] = "{$staff_info['name']}({$staff_info['staff_info_id']})";
        //职位xxx job_name，所属网点xxx sys_store_name，片区xxx store_district，大区xxx store_area。
        $piece                  = $staff_info['piece_name'] ?: '-';
        $region                 = $staff_info['region_name'] ?: '-';
        $msg_data['staff_info'] = $t->_('job_name') . "{$staff_info['job_title_name']}," . $t->_('sys_store_name') . "{$staff_info['store_name']}," . $t->_('store_district') . "{$piece}," . $t->_('store_area') . "{$region}";
        $msg_data['date_at']    = '';

        $title          = "{$staff_info['staff_info_id']}-{$staff_info['name']}" . $t->_($title_key);
        $content        = $t->_($content_key, $msg_data);
        $send_message   = [
            'staff_users'     => $manager,
            'message_title'   => $title,
            'message_content' => "<p style='font-size: 32px;'>" . $content . "</p>",
            'category'        => -1,
            'push_state'      => 1,
        ];
        $messageService = new MessagesService();
        $messageService->add_kit_message($send_message);
        return true;
    }

    //异步 发送pdf param: staff_info_id lang
    public function send_pdf($param)
    {
        $id        = $param['id'];
        $send_type = $param['send_type'];
        $info      = HrStaffInvestigationModel::findFirst($id);
        $t         = self::getTranslation('en');

        //其他状态 不发送
        if (empty($send_type)) {
            return true;
        }

        $attachment_name = $mail_content = $date_time = '';
        //调查中 并且是 停职 发停职 pdf
        if ($send_type == 'stop') {
            $attachment_name = 'Surat Penggantungan Kerja - ';
            $mail_content    = $t->_('investigation_stop_content');
            $date_time       = $info->stop_date;
        }

        //调查结束并且 恢复在职
        if ($send_type == 'back') {
            $attachment_name = 'Notis Tamat Penggantungan - ';
            $mail_content    = $t->_('investigation_back_content');
            $date_time       = date('Y-m-d', strtotime("{$info->result_date} -1 day"));
        }

        //调查结束 并且是 离职
        if ($send_type == 'leave') {
            $attachment_name = 'Notis Penamatan Kerja - ';
            $mail_content    = $t->_('investigation_leave_content');
            $date_time       = date('Y-m-d', strtotime("{$info->result_date} -1 day"));
        }


        //获取 模板地址 选对应类型的模板
        if (in_array(RUNTIME, ['dev', 'tra'])) {
            $file_path = APP_PATH . '/views/investigation/' . $send_type . '.ftl';
            // 上传OSS
            $upload_result = OssHelper::uploadFile($file_path, OssService::PDF_SPACE_CERTIFICATE);
            $temp_url      = $upload_result['object_url'];
        } else {
            $temp_url = (new SettingEnvService())->getSetVal('investigation_tpl_' . $send_type);
        }
        //员工信息
        $staff_info = (new StaffService())->getStaffView($info->staff_info_id);

        //渲染变量
        $week                        = Enums\GlobalEnums::MY_WEEK_MAP;
        $no                          = $this->get_pdf_no();
        $pdf_data['date_no']         = date('y/d/') . $no;//年月+序号 25/06/001
        $pdf_data['send_date']       = date('d/m/Y');//发送日期
        $pdf_data['staff_name']      = $staff_info['name'];//员工姓名
        $pdf_data['staff_id']        = $staff_info['staff_info_id'];//工号
        $pdf_data['job_title']       = $staff_info['job_title_name'];//职位
        $pdf_data['department_name'] = $staff_info['node_department_name'];//部门
        if ($send_type == 'back') {
            $pdf_data['back_date'] = $this->format_my_date($info->result_date);
            $w                     = date('w');
            $pdf_data['week']      = $week[$w];
            $pdf_data['stop_date'] = $this->format_my_date($info->stop_date);
        }
        if ($send_type == 'leave') {
            //发送过解释信
            if ($info->is_send == HrStaffInvestigationModel::IS_SEND_YES) {
                $pdf_data['letter_date']  = $this->format_my_date($info->send_date);
                $pdf_data['letter_no']  = $info->letter_no;
                $pdf_data['letter_law'] = $info->letter_law;
            }
            //停职 为是
            if ($info->is_stop == HrStaffInvestigationModel::IS_STOP_YES) {
                $pdf_data['stop_date'] = $this->format_my_date($info->stop_date);
                //停职结束日期 离职日期 和停职+14 取小
                //        1. 如果停职管理的离职日期-停职管理的停职日期≤14，显示为离职日期的前一天
                //        2. 如果停职管理的离职日期-停职管理的停职日期>14，显示为停职日期+14天
                $date_14 = date('Y-m-d', strtotime("{$info->stop_date} +14 day"));
                if (strtotime($info->result_date) <= strtotime($date_14)) {
                    $pdf_data['end_stop_date'] = $this->format_my_date($date_time);
                } else {
                    $pdf_data['end_stop_date'] = $this->format_my_date($date_14);
                    $pdf_data['date_15']       = $date_14;
                }
            }
        }

        //获取 logo
        $bll = (reBuildCountryInstance(new CertificateService()));
        /**
         * @see \App\Modules\My\Services\CertificateService::getCompanyConfigInfo()
         */
        $company_info        = $bll->getCompanyConfigInfo(['staff_info_id' => $info->staff_info_id]);
        $pdf_data['date_at'] = $this->format_my_date($date_time);//操作日期  停职日期 离职 或者恢复日期 格式定制马来的 22hb(日期) Oktober（定制马来月份） 2022（年）
        //公司名称 company_name
        $pdf_data['company_name'] = $company_info['company_name'];
        //法人 签字 labor_sign_url
        $pdfImgData[] = ['name' => 'labor_sign_url', 'url' => $company_info['labor_sign_url'] ?? ''];
        //法人名称 labor_name
        $pdf_data['labor_name'] = $company_info['labor_name'];
        //法人 职位 labor_job_title
        $pdf_data['labor_job_title'] = $company_info['labor_job_title'];

        $singPdfSetting = (new ContractStaffService())->getPdfHeaderFooter($company_info);
        $res            = (new FormPdfServer())->getInstance()->generatePdf($temp_url, $pdf_data, $pdfImgData,
            $singPdfSetting);

        if (empty($res['object_url'])) {
            throw new \Exception('object_url Error!');
        }

        $pdf_url = $res['object_url'];

        //为了给文件命名
        $attachment_name .= $staff_info['name'] . "({$staff_info['staff_info_id']})";
        $fileUrl         = $this->fileDownload($pdf_url, '', $attachment_name . '.pdf');

        $sendResult = BiMail::send($staff_info['personal_email'], $attachment_name, $mail_content, [$fileUrl]);
        if (!$sendResult) {
            $this->logger->error('investigation send_pdf Error!');
            throw new ValidationException(self::$t->_('send_email_fail'));
        }
        return true;
    }


    //异步 处理考勤 调查结束的 离职或者恢复在职
    public function handle_attendance($param)
    {
        $id   = $param['id'];
        $info = HrStaffInvestigationModel::findFirst($id);
        if (empty($info)) {
            $this->logger->error('investigation handle_attendance Error! ' . $id);
            return true;
        }
        //非调查结束状态
        if (!in_array($info->state, [HrStaffInvestigationModel::STATE_DONE,HrStaffInvestigationModel::STATE_FINISH])) {
            $this->logger->info(['investigation' => 'investigation state ' . $info->state]);
            return true;
        }
        //无需处理
        if ($info->result_state == HrStaffInvestigationModel::RESULT_UN_DO) {
            $this->logger->info(['investigation' => 'investigation result_state ' . $info->result_state]);
            return true;
        }
        if ($info->is_delete == 1) {
            $this->logger->info(['investigation' => 'investigation deleted ']);
            return true;
        }
        if (empty($info->stop_date) || empty($info->result_date)) {
            $this->logger->info([
                'investigation_date' => [
                    'stop_date'   => $info->stop_date,
                    'result_date' => $info->result_date,
                ],
            ]);
            return true;
        }
        //前一天
        $before_date = date('Y-m-d', strtotime("{$info->result_date} -1 day"));
        $same_period = $this->checkDatePeriod($info->stop_date, $before_date);
        if ($same_period === true) {
            $start = $info->stop_date;
            $end   = $before_date;
        } else {
            $start = $same_period['start'];
            $end   = $same_period['end'];
        }
        $deal_param['staff_id'] = $info->staff_info_id;
        $deal_param['start_date'] = $start;
        $deal_param['end_date'] = $end;
        $deal_param['tab'] = HrStaffInvestigationModel::STATE_DONE;
        $deal_param['result_state'] = $info->result_state;
        $this->deal_attendance($deal_param);
    }

    //获取 编号 如果超过最大值 要增加长度
    public function get_pdf_no($length = 3)
    {
        $num = $this->redis->get(self::$pdf_key);
        $max = str_pad(9, $length, '9', STR_PAD_LEFT);
        if ($num >= $max) {
            $length++;
            $this->get_pdf_no($length);
        }
        $num++;
        $num = str_pad($num, $length, '0', STR_PAD_LEFT);
        $this->redis->incr(self::$pdf_key);
        return $num;
    }

    //马来 特殊定制  日期格式
    public function format_my_date($date)
    {
        if(empty($date)){
            return '-';
        }
        $stamp  = strtotime($date);
        $year  = date('Y', $stamp);
        $day   = date('d', $stamp);
        $month = Enums\GlobalEnums::MY_MONTHS_MAPS[(int)date('m', $stamp)];
        return "{$day}hb {$month} {$year}";
    }

    /**
     * @param $startDate
     * @param $endDate
     * @return mixed
     * @throws \Exception
     */
    function checkDatePeriod($startDate, $endDate)
    {
        // 将日期字符串转换为DateTime对象
        $start = new DateTime($startDate);
        $end   = new DateTime($endDate);

        // 获取开始日期的周期信息
        $startPeriod = $this->getPeriodInfo($start);

        // 获取结束日期的周期信息
        $endPeriod = $this->getPeriodInfo($end);

        // 比较周期信息是否相同
        if ($startPeriod['periodStart'] == $endPeriod['periodStart']
            && $startPeriod['periodEnd'] == $endPeriod['periodEnd']) {
            return true;
        }

        // 不在同一个周期，返回开始日期和它所在周期的结束日期
        return [
            'start' => $endPeriod['periodStart'],
            'end'   => $endDate,
        ];
    }

    /**
     * 获取给定日期的周期信息
     * @param DateTime $date 日期对象
     * @return array 包含周期开始和结束日期的数组
     */
    function getPeriodInfo(DateTime $date)
    {
        $year  = (int)$date->format('Y');
        $month = (int)$date->format('n');
        $day   = (int)$date->format('j');

        // 如果日期是24号或之后，则属于下个月的周期
        if ($day >= 24) {
            $periodMonth = $month + 1;
            $periodYear  = $year;

            // 处理12月的情况
            if ($periodMonth > 12) {
                $periodMonth = 1;
                $periodYear++;
            }
        } else {
            $periodMonth = $month;
            $periodYear  = $year;
        }

        // 计算周期开始和结束日期
        $periodStart = new DateTime("$periodYear-" . str_pad($periodMonth - 1, 2, '0', STR_PAD_LEFT) . "-24");
        $periodEnd   = new DateTime("$periodYear-" . str_pad($periodMonth, 2, '0', STR_PAD_LEFT) . "-23");

        // 处理1月的情况（上个月是去年的12月）
        if ($periodMonth == 1) {
            $periodStart = new DateTime(($periodYear - 1) . "-12-24");
        }

        return [
            'periodStart' => $periodStart->format('Y-m-d'),
            'periodEnd'   => $periodEnd->format('Y-m-d'),
        ];
    }


    public function deal_attendance($param)
    {
        $staff_id   = $param['staff_id'];
        $start_date = $param['start_date'];
        $end_date   = $param['end_date'];
        $date_list  = DateHelper::DateRange(strtotime($start_date), strtotime($end_date));
        //补 rest 如果有 off  或者 rest  就跳过
        $exist_off = HrStaffWorkDaysModel::find([
            'conditions' => "staff_info_id = :staff_id: and date_at between :start: and :end:",
            'bind'       => [
                'staff_id' => $staff_id,
                'start'    => $start_date,
                'end'      => $end_date,
            ],
        ])->toArray();
        $exist_off = array_column($exist_off, 'date_at');
        $this->logger->info(['exist_off' => $exist_off, 'staff_id' => $staff_id]);

        $insert = [];
        foreach ($date_list as $date) {
            if (in_array($date, $exist_off)) {
                continue;
            }
            $row['staff_info_id'] = $staff_id;
            $row['month']         = date('Y-m', strtotime($date));
            $row['date_at']       = $date;
            $row['operator']      = 10000;
            $row['remark']        = 'investigation task add';
            $row['type']          = HrStaffWorkDaysModel::TYPE_2;
            $insert[]             = $row;
        }
        $model = new HrStaffWorkDaysModel();
        $model->batch_insert($insert);
        //如果是 恢复在职 就处理完成了 只有停职 和 离职 才处理请假
        if(!empty($param['result_state']) && $param['result_state'] == HrStaffInvestigationModel::RESULT_RESTORE){
            return true;
        }

        //补请假 区间补半天 如果是调查中 有个 13天的逻辑
        if ($param['tab'] == HrStaffInvestigationModel::STATE_UN_DO) {
            $day_13 = date('Y-m-d', strtotime("{$start_date} +13 day"));
            if(strtotime($end_date) > strtotime($day_13)){
                $end_date = $day_13;
                $date_list = DateHelper::DateRange(strtotime($start_date), strtotime($day_13));
            }
        }

        //查询区间请假 带审批和审核通过
        $leave_server = new LeaveBllService();
        $leave_data   = $leave_server->getLeaveByDate([$staff_id], $date_list);
        $leave_data   = $leave_data[$staff_id] ?? [];
        $is_send      = false;
        if (!empty($leave_data)) {
            foreach ($leave_data as $leave) {
                $leave_start = strtotime($leave['leave_start_time']);
                $leave_end   = strtotime($leave['leave_end_time']);
                //如果有不存在区间内的记录 就发邮件 不处理
                if($leave_start < strtotime($start_date) || $leave_end > strtotime($end_date)){
                    $is_send = true;
                    break;
                }
            }
        }

        if($is_send){
            $this->send_leave_email($staff_id);
            return true;
        }

        //补请假 只补半天 要加多条记录
        $audit_model = new StaffAuditModel();
        $split_data = [];
        foreach ($date_list as $date){
            $audit['staff_info_id'] = $staff_id;
            $audit['audit_type'] = StaffAuditModel::AUDIT_TYPE_LEAVE;
            $audit['leave_type'] = StaffAuditModel::LEAVE_TYPE_12;
            $audit['leave_start_time'] = $date . ' 13:00:00';
            $audit['leave_start_type'] = StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON;
            $audit['leave_end_time'] = $date . ' 18:00:00';
            $audit['leave_end_type'] = StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON;
            $audit['leave_day'] = 0.5;
            $audit['status'] = 2;//审核通过
            $audit['audit_reason'] = 'investigation task add';
            $clone = clone $audit_model;
            $clone->create($audit);

            $split['audit_id'] = $clone->audit_id;
            $split['staff_info_id'] = $staff_id;
            $split['date_at'] = $date;
            $split['type'] = StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON;
            $split['year_at'] = date('Y', strtotime($date));
            $split_data[] = $split;
        }

        $split_model = new StaffAuditLeaveSplitModel();
        $split_model->batch_insert($split_data);

        //撤销所有请假
        if(!empty($leave_data)){
            $audit_ids = array_column($leave_data, 'audit_id');
            $audit_ids = array_values(array_unique($audit_ids));
            $this->logger->info(['audit_ids' => $audit_ids]);
            foreach ($audit_ids as $audit_id){
                //culr api
                $send_params['audit_id'] = $audit_id;
                $send_params['staff_id'] = $staff_id;
                $send_params['operate_id'] = 10000;
                $res = $this->getApiDatass('by', '', 'leave_cancel', self::$language, $send_params);
                if($res['code'] != ErrCode::SUCCESS){
                    $this->logger->error('cancel leave error ' . $audit_id);
                }
            }
        }
        return true;
    }

    public function send_leave_email($staff_id){
        $mail = (new SettingEnvService())->getSetVal('investigation_attendance_email');
        if(empty($mail)){
            return true;
        }
        $title = self::$t->_('investigation_attendance_mail_title');
        $content = self::$t->_('investigation_attendance_mail_content',['staff_id' => $staff_id]);
        $sendResult = BiMail::send($mail, $title, $content);
        if (!$sendResult) {
            $this->logger->error('send_leave_email error');
        }
        return true;
    }


}