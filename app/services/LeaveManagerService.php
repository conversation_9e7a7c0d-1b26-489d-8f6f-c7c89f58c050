<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\ApprovalEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\HttpCurl;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AssetsGoodsModel;
use App\Models\backyard\AssetsHandoverModel;
use App\Models\backyard\AssetsInfoLogModel;
use App\Models\backyard\AssetsInfoModel;
use App\Models\backyard\AssetsInventoryStoresModel;
use App\Models\backyard\AuditApprovalModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\CompanyTerminationContractModel;
use App\Models\backyard\HrProbationResignModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\LeaveManageLogModel;
use App\Models\backyard\MessagePdf;
use App\Models\backyard\MsgAssetModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffLeaveReasonModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HoldStaffManageModel;
use App\Models\backyard\SysAttachmentModel;
use App\Models\backyard\StaffResignModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\LeaveAssetsManagerModel;
use App\Models\backyard\LeaveManagerModel;
use App\Models\backyard\LeaveMoneyManagerModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\SysStoreModel;
use App\Models\fle\StaffInfoModel;
use App\Models\fle\StaffInfoPositionModel;
use App\Models\fle\StoreReceivableBillDetailModel;
use App\Models\fle\StoreRemittanceBillModel;
use App\Models\fle\StoreRemittanceRecordModel;
use App\Models\fle\SysDepartmentModel;
use App\Models\oa\LoanModel;
use App\Models\oa\ReimbursementModel;
use App\Models\oa\ReimbursementRelLoanModel;
use App\Models\oa\ReserveFundApplyModel;
use App\Models\oa\ReserveFundReturnModel;
use App\Models\backyard\HrProbationModel;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;
use Mpdf\Mpdf;
use Phalcon\Db;
use App\Services\ExternalDataService;

class LeaveManagerService extends BaseService
{
    // 区域包含省份编码
    public static $areaProvince = [];

    public static $reasonKeyByCode = [];


    /**
     * 走审批的离职来源
     * @var int[]
     */
    public static $approval_leave_source = [
        self::LEAVE_SOURCE_BACKYARD,
        self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT,
    ];

    /**
     * 离职来源
     */
    const LEAVE_SOURCE_ADD = 1;
    const LEAVE_SOURCE_HRIS = 2;
    const LEAVE_SOURCE_OFF3DAYS = 3;
    const LEAVE_SOURCE_NOTPAY = 4;//未缴纳公款
    const LEAVE_SOURCE_BACKYARD = 5;
    const LEAVE_SOURCE_BATCH = 6;
    const LEAVE_SOURCE_OTHER = -1;
    const LEAVE_SOURCE_CRIMINAL = 7;
    const LEAVE_SOURCE_PROBATION = 8;//试用期未通过员工
    const LEAVE_SOURCE_HRIS_EDIT = 9;
    const LEAVE_SOURCE_GLOBAL = 10;
    const LEAVE_SOURCE_CONTRACT_EXPIRE = 11;//合同到期
    const LEAVE_SOURCE_HIRE_TYPE_CHANGE = 12; //转个人代理
    const LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT = 13; //公司解约个人代理
    const LEAVE_SOURCE_FACE_BLACKLIST = 14; //人脸黑名单

    const LEAVE_SOURCE_DESC = [
        self::LEAVE_SOURCE_ADD       => "add_new_hold_case",
        self::LEAVE_SOURCE_HRIS      => 'HRIS',
        self::LEAVE_SOURCE_OFF3DAYS  => 'stay_away_from_work', //连续旷工
        self::LEAVE_SOURCE_NOTPAY    => 'fail_to_submit_public_funds',
        self::LEAVE_SOURCE_BACKYARD  => 'apply_for_resignation_in_by',
        self::LEAVE_SOURCE_BATCH     => 'leave_source_6',
        self::LEAVE_SOURCE_OTHER     => 'leave_source_-1',
        self::LEAVE_SOURCE_CRIMINAL  => 'criminal_record', // 犯罪记录
        self::LEAVE_SOURCE_PROBATION => 'leave_source_8',//试用期未通过员工
        self::LEAVE_SOURCE_HRIS_EDIT => 'font_leave_source_9',
        self::LEAVE_SOURCE_GLOBAL => 'font_leave_source_10',
        self::LEAVE_SOURCE_CONTRACT_EXPIRE  => 'font_leave_source_11',//合同到期
        self::LEAVE_SOURCE_HIRE_TYPE_CHANGE => 'leave_source_12',
        self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT => 'leave_source_13',
        self::LEAVE_SOURCE_FACE_BLACKLIST => 'leave_source_14',
    ];

    const LEAVE_REASON_HIRE_TYPE_CHANGE = 35; //转个人代理
    const LEAVE_REASON_PROBATION = 31;   //试用期未通过
    const LEAVE_REASON_CONTRACT_EXPIRE = 30; //合同到期

    const LEAVE_TYPE_EXPIRE = 4;// 合同终止
    /**
     * 资产处理状态值
     */
    const ASSETS_STATE_UNPROCESS = 0;
    const ASSETS_STATE_UNPROCESSED = 1;
    const ASSETS_STATE_PROCESSING = 2;
    const ASSETS_STATE_PROCESSED = 3;
    const ASSETS_STATE_UN_DEDUCTED = 4;


    /**
     * 申请离职/解约来源
     */
    CONST LEAVE_SRC_STAFF_REGIN = 1;//员工申请
    CONST LEAVE_SRC_COMPANY_TERMINATION = 2;//公司解约

    /**
     * 资产处理状态映射
     */
    const ASSETS_DESC = [
        self::ASSETS_STATE_UNPROCESS   => '未经处理',
        self::ASSETS_STATE_UNPROCESSED => '未处理',
        self::ASSETS_STATE_PROCESSING  => '处理中',
        self::ASSETS_STATE_UN_DEDUCTED => '待扣款',
        self::ASSETS_STATE_PROCESSED   => '已处理',
    ];

    /**
     * 资产处理状态映射
     */
    const MONEY_DESC = [
        self::ASSETS_STATE_UNPROCESS   => '未经处理',
        self::ASSETS_STATE_UNPROCESSED => '未处理',
        self::ASSETS_STATE_PROCESSING  => '处理中',
//        self::ASSETS_STATE_UN_DEDUCTED => '待扣款',
        self::ASSETS_STATE_PROCESSED   => '已处理',
    ];

    // "总部会计"
    const POSITION_HEADQUARTER_ACCOUNT = 10;
    const ASSETS_MANAGER_STAFFS = [38158, 43403, 30487, 58916, 58295, 19515, 31828];

    const SELECTED_YES = 1;
    const SELECTED_NO = 0;

    /**
     * 资产处理来源
     */
    const ASSETS_SOURCE_USER = 1;
    const ASSETS_SOURCE_MANAGER = 2;

    /**
     * 审批状态
     *
     * 1 待审批 2 已同意 3 已驳回 4 已撤销 5 已超时
     */
    const APPROVAL_STATUS_INIT = 0;
    const APPROVAL_STATUS_WAIT = 1;
    const APPROVAL_STATUS_APPROVED = 2;
    const APPROVAL_STATUS_DISMISSED = 3;
    const APPROVAL_STATUS_REVOKED = 4;
    const APPROVAL_STATUS_TIME_OUT = 5;
    const APPROVALSTATUS_DESC = [
        self::APPROVAL_STATUS_WAIT      => "待审批",
        self::APPROVAL_STATUS_APPROVED  => "已同意",
        self::APPROVAL_STATUS_DISMISSED => "已驳回",
        self::APPROVAL_STATUS_REVOKED   => "已撤销",
        self::APPROVAL_STATUS_TIME_OUT  => "已超时",
    ];

    const TYPE_ASSETS = 'assets';
    const TYPE_MONEY = 'money';

    const SOURCE_TYPE_PUBLIC_ASSETS = 'public_assets'; //来源 公共资产

    public static $has_employ_operater_department = [
        'fulfillment' => 15,
        'flash_money' => 16,
        'flash_home' => 18,
        'flash_logistic' => 28,
    ];


    public $leave_list_columns = [
        'hsi.staff_info_id',
        'hsi.name',
        'hsi.name_en',
        'hsi.mobile',
        'hsi.job_title',
        'hsi.sys_department_id',
        'hsi.node_department_id',
        'hsi.sys_store_id',
        'hsi.hire_date',
        'hsi.state',
        'hsi.wait_leave_state',
        'hsi.leave_date',
        'hsi.leave_type',
        'hsi.leave_reason',
        'hsi.leave_reason_remark',
        'hsi.working_country',
        'hsi.leave_source',
        'hsi.formal',
        'hsi.job_title_grade_v2',
        'hsi.hire_type',
        'hsi.contract_expiry_date',
        'lm.staff_info_id as lm_staff_info_id',
        'lm.assets_operate_version as lm_assets_operate_version',
        'lm.is_has_superior',
        'lm.superior_operate_time as lm_superior_operate_time',
        'lm.superior_operate_version as lm_superior_operate_version',
        'lm.leave_date as lm_leave_date',
        'lm.approval_status',
        'lm.assets_operate_remark',
        'lm.assets_remand_state',
        'lm.money_remand_state',
        'lm.leave_date as order_leave_date',
        'lm.is_new_assets_remand_state',
        'lm.last_work_date',
        'lm.resignation_notice',
        'IF(lm.edit_short_notice is null, lm.short_notice, lm.edit_short_notice) as short_notice',
    ];

    public $export_leave_list_columns = [
        'hsi.staff_info_id',
        'hsi.name',
        'hsi.name_en',
        'hsi.mobile',
        'hsi.job_title',
        'hsi.sys_department_id',
        'hsi.node_department_id',
        'hsi.sys_store_id',
        'hsi.hire_date',
        'hsi.state',
        'hsi.wait_leave_state',
        'hsi.leave_date',
        'hsi.leave_type',
        'hsi.leave_reason',
        'hsi.leave_reason_remark',
        'hsi.working_country',
        'hsi.leave_source',
        'hsi.formal',
        'hsi.job_title_grade_v2',
        'hsi.hire_type',
        'hsi.contract_expiry_date',
        'lm.staff_info_id as lm_staff_info_id',
        'lm.assets_operate_version as lm_assets_operate_version',
        'lm.is_has_superior',
        'lm.superior_operate_time as lm_superior_operate_time',
        'lm.superior_operate_version as lm_superior_operate_version',
        'lm.leave_date as lm_leave_date',
        'lm.approval_status',
        'lm.assets_operate_remark',
        'lm.assets_remand_state',
        'lm.money_remand_state',
        'lm.leave_date as order_leave_date',
        'lm.is_new_assets_remand_state',
        'lm.last_work_date',
        'lm.resignation_notice',
        'IF(lm.edit_short_notice is null, lm.short_notice, lm.edit_short_notice) as short_notice',
    ];

    public function leaveList($params)
    {
        return $this->staffList($params);
    }


    public function leaveListColumns()
    {
        return $this->leave_list_columns;
    }

    public function buildOtherRow($item = [])
    {
        return [];
    }

    /**
     * 根据定制化条件，修改原字段值
     * @param $row
     * @param array $item
     * @return mixed
     */
    public function editRowData($row, $item = [])
    {
        return $row;
    }

    public function exportLeaveListColumns()
    {
        return $this->export_leave_list_columns;
    }

    public function exportBuildOtherRow($item, $datum, $bankList = [])
    {
        return $item;
    }

    /**
     * 获取所属区域信息
     * @param $storeAreas
     * @param $sys_store_id
     * @return string
     */
    public function getExportAreaInfo($storeAreas, $sys_store_id)
    {
        return $storeAreas && isset($storeAreas[$sys_store_id]) ? GlobalEnums::$areas[$storeAreas[$sys_store_id]['manage_geography_code']] ?? '' : '';
    }

    public function import($path, $userInfo)
    {
        $res  = [];
        $flag = $this->ifImport($userInfo['position_category']);
        if (!$flag) {
            return $flag;
        }
        $data = [];
        try {
            $config = ['path' => ''];
            $excel  = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $data = [];
            $excel->openFile($path)
                ->openSheet()
                ->setSkipRows(1);
            while (($row = $excel->nextRow(
                    [
                        \Vtiful\Kernel\Excel::TYPE_INT,
                        \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                        \Vtiful\Kernel\Excel::TYPE_STRING,
                    ]
                )) !== null) {
                $row[1] = date("Y-m-d", $row[1]);
                $data[] = $row;
            }
        } catch (\Exception $e) {
            $this->logger->info("importLeave manager excel file error".$e->getMessage());
            return $res;
        }
        $nowDate = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);
        $len     = count($data);
        for ($i = 0; $i < $len; $i++) {
            $this->logger->info("离职管理-checkExcelItem参数 " . json_encode($data[$i], JSON_UNESCAPED_UNICODE));
            $arr = $this->checkExcelItem($data[$i], $nowDate);
            $staff_info_data = $arr['staff_info_data'] ?? [];
            unset($arr['staff_info_data']);
            //如果校验失败
            if ($arr['result'] !== 'ok') {
                $res[] = $arr;
            } else {
                $arr['operaterId'] = $userInfo['id'];
                //处理离职
                $flag = $this->dealLeave($arr, $nowDate,$staff_info_data);
                //处理离职失败
                if (!$flag) {
                    $arr['result']  = 'fail';
                    $arr['remarks'] = 'deal leave fail';
                    $res[]          = $arr;
                    //此次报错，执行下一个
                    continue;
                }

                //处理hold
                $flag = $this->dealHold($arr, $nowDate);
                if (!$flag) {
                    $arr['result']  = 'fail';
                    $arr['remarks'] = 'add hold fail';
                    $res[]          = $arr;
                    //此次报错，执行下一个
                    continue;
                }


                $leaveInfo = $this->getLeaveInfo($arr['staff_info_id']);

                $this->logger->info("离职管理-修改lm-返回值===".json_encode($leaveInfo,
                        JSON_UNESCAPED_UNICODE)."========arr=".json_encode($arr, JSON_UNESCAPED_UNICODE));


                //如果lm信息，则修改lm信息
                if (!empty($leaveInfo)) {
                    $this->logger->info("离职管理-开始修改lm");

                    $temp               = [];
                    $temp['leave_date']         = $arr['leave_date'];
                    $flag                       = $this->updateLeaveInfo($leaveInfo['id'], $temp);

                    $this->logger->info("离职管理-修改lm结果==".json_encode($flag, JSON_UNESCAPED_UNICODE));

                    if (!$flag) {
                        $arr['result']  = 'fail';
                        $arr['remarks'] = 'update lm error';
                        $res[]          = $arr;
                        //此次报错，执行下一个
                        continue;
                    }
                }
                $res[] = $arr;
            }
        }

        return $res;
    }

    /**
     *校验导入excel每行数据
     * @param $item
     * @return array
     */

    private function checkExcelItem($item, $nowDate)
    {
        $staff_info_id = "";
        $leave_date    = "";
        $leave_reason  = "";


        $tempRes = [
            "staff_info_id" => $staff_info_id,
            "leave_date"    => $leave_date,
            "leave_reason"  => $leave_reason,
            "leave_reason_agent"  => "",
            "result"        => "ok",
            "remarks"       => "",
        ];


        if ((isCountry(['TH','PH','MY']) && count($item) != 4) || (!isCountry(['TH','PH','MY']) && count($item) != 3)) {
            $tempRes['result']  = "fail";
            $tempRes['remarks'] = "param error";
            return $tempRes;
        }

        $staff_info_id = trim($item[0]);
        $leave_date    = trim($item[1]);
        $leave_reason  = $item[2] ?? "";
        $leave_reason_agent  = $item[3] ?? "";


        $tempRes = [
            "staff_info_id" => $staff_info_id,
            "leave_date"    => $leave_date,
            "leave_reason"  => $leave_reason,
            "leave_reason_agent"  => $leave_reason_agent,
            "result"        => "ok",
            "remarks"       => "",
        ];

        if(!is_numeric($staff_info_id)) {
            $tempRes['result']  = "fail";
            $tempRes['remarks'] = self::$t->_('sign_msg_021');
            return $tempRes;
        }

        try {
            $timestamp = strtotime($leave_date);
            if (!$timestamp) {
                throw new ValidationException("leave_date error");
            }

            $new_leave_date        = date("Y-m-d", $timestamp);
            $leave_date            = $new_leave_date;
            $tempRes['leave_date'] = $new_leave_date;
            /*if($new_leave_date!=$leave_date && $tmp_leave_date!=$leave_date ){
                throw new \Exception("leave_date format error");
            }*/


            if ($staff_info_id == 10000) {
                throw new ValidationException("staff_info_id error");
            }

            //返回在职的用户信息
            $staffInfo = $this->getStaffInfo($staff_info_id);
            if (empty($staffInfo)) {
                throw new ValidationException("staff_info_id error");
            }

            // 根据雇佣类型取离职原因
            if (isCountry(['TH','PH','MY']) && $staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID){
                $_leave_reason = $leave_reason_agent;
            }else{
                $_leave_reason = $leave_reason;
            }
            if (empty($_leave_reason)){
                throw new ValidationException("leave_reason error");
            }

            $leave_reason_id = $this->getReasonId($_leave_reason);
            if ($leave_reason_id == -1) {
                throw new ValidationException("leave_reason error");
            }

            //入职时间大于离职时间
            $hire_date = date("Y-m-d", strtotime($staffInfo['hire_date']));
            if ($hire_date > $leave_date) {
                throw new ValidationException("leave_date error.");
            }

            if ($staffInfo['leave_reason'] == $leave_reason_id && date('Ymd',strtotime($staffInfo['leave_date'])) == date('Ymd',strtotime($leave_date))){
                throw new ValidationException("No change");
            }

            $leave_info = $this->getLeaveInfo($staff_info_id);

            //如果没有离职申请信息 且是 待离职或离职则报错。
            if (empty($leave_info) && ($staffInfo['state'] == 2 || $staffInfo['wait_leave_state'] == 1)) {
                throw new ValidationException("already exist");
            }

            //待离职
            if ($nowDate < $leave_date) {
                //待离职，不能是停职，离职，或者是待离职状态
                if ($staffInfo['state'] == 2 || $staffInfo['wait_leave_state'] == 1 || $staffInfo['state'] == 3) {
                    throw new ValidationException("already exist..");
                }
            }


            //如果有离职申请信息，待审批和审批通过的要拦截
//            if (!empty($leave_info) && in_array($leave_info['approval_status'],[self::APPROVAL_STATUS_WAIT,self::APPROVAL_STATUS_APPROVED])) {
//                throw new ValidationException("already exist.");
//            }

            $resign = $this->getResign($staff_info_id);
            $today = date('Y-m-d');
            
            if (!empty($resign) && in_array($resign['status'], [1, 2]) && !empty($resign['leave_date']) && $resign['leave_date'] > $today) {
                throw new ValidationException("already exist...");
            }


            $tempRes['leave_reason_id'] = $leave_reason_id;
            $tempRes['hire_date']       = $staffInfo['hire_date'];
            $tempRes['hire_type']       = $staffInfo['hire_type'];
            $tempRes['staff_info_data'] = $staffInfo;
        } catch (\Exception $e) {
            $tempRes['result']  = "fail";
            $tempRes['remarks'] = $e->getMessage();
        }
        return $tempRes;
    }

    /**
     * 添加到 backyard离职表，用于同步Hold
     * @param $item
     * @return boolean
     */

    private function addResign($item)
    {
        $data = [
            'submitter_id'   => $item['staff_info_id'],
            'hire_date'      => $item['hire_date'],
            'last_work_date' => date("Y-m-d", strtotime("-1 days", strtotime($item['leave_date']))),
            'leave_date'     => $item['leave_date'],
            'reason'         => $item['leave_reason_id'],
            'remark'         => "batch import",
            'status'         => 2,
            'serial_no'      => 'RN'.substr(md5(date("YmdHis").rand(1, 1000).$item['staff_info_id']), 0, 13).rand(1,
                    10),
            'source'         => 1,
            'hire_type'      => $item['hire_type']??0,
        ];
        //
        return $this->getDI()->get("db_backyard")->insertAsDict("staff_resign", $data);
    }

    /**
     * 获得最新一个审批
     * @param $userId
     * @return array
     */
    public function getResign($userId)
    {
        $res = StaffResignModel::find(
            [
                "conditions" => "submitter_id = :userId:",
                "order"      => "resign_id desc",
                "bind"       => [
                    "userId" => $userId,
                ],
            ]
        )->toArray();

        if ($res) {
            return $res['0'];
        } else {
            return [];
        }
    }


    /**
     * 获得最新一个公司解约个人代理的数据
     * @param $staff_info_id
     * @return array
     */
    public function getCompanyTerminationContract($staff_info_id)
    {
        $model = CompanyTerminationContractModel::findFirst(
            [
                "conditions" => "staff_info_id = :staff_info_id:",
                "order"      => "id desc",
                "bind"       => [
                    "staff_info_id" => $staff_info_id,
                ],
            ]
        );
        if ($model) {
            return $model->toArray();
        }
        return [];
    }

    /**
     * 根据当前时间，处理离职
     * @param $item
     * @param $nowDate
     * @return boolean
     */
    private function dealLeave($item, $nowDate,$staff_info_data = [])
    {
        $leaveReason = StaffLeaveReasonModel::findFirst([
            'conditions' => ' code = :code: and deleted = 0',
            'bind'       => [
                'code' => $item['leave_reason_id'],
            ],
        ]);
        $leaveReason = !empty($leaveReason) ? $leaveReason->toArray() : [];
        //如果当前时间大于等于离职时间，已离职
        if ($nowDate >= $item['leave_date']) {
            $data = [
                [
                    "staff_info_id" => $item['staff_info_id'],
                    "type"          => 2,
                    "day"           => $item['leave_date'],
                    'leave_reason'  => $item['leave_reason_id'],
                    'leave_source'  => 6, //批量导入
                    'operaterId'    => $item['operaterId'],
                ],
            ];
            //对于导入之前是离职状态 不更新离职来源
            if (!empty($staff_info_data['state']) && $staff_info_data['state'] == HrStaffInfoModel::STATE_RESIGN){
                unset($data[0]['leave_source']);
                $data[0]['leave_type'] = $leaveReason['type'];
            }
            $ret  = $this->update_staff_state($data);
            $this->getDI()->get('logger')->write_log('update_staff_state . '.json_encode($data, JSON_UNESCAPED_UNICODE)
                ." ".json_encode($ret, JSON_UNESCAPED_UNICODE), 'info');

            if (isset($ret['code']) && $ret['code'] == 0) {
                foreach ($ret['body'] as $k => $v) {
                    if ($v['msg'] == 'ok') {
                        return true;
                    }
                }
            }
            return false;
        } //待离职
        else {
            //提交申请就同步数据到FBI
            $postData = [
                'staff_info_id'   => $item['staff_info_id'],
                'wait_leave_date' => $item['leave_date'],
                'leave_reason'    => $item['leave_reason_id'],
                'leave_source'    => 6,                        //来源
                'operaterId'      => $item['operaterId'],       //操作人
            ];
            //对于导入之前是离职状态 不更新离职来源
            if (!empty($staff_info_data['state']) && $staff_info_data['state'] == HrStaffInfoModel::STATE_RESIGN){
                unset($postData['leave_source']);
                $postData['leave_type'] = $leaveReason['type'];
            }
            return $this->waitLeave($postData);
        }
    }

    /**
     * 根据当前时间，处理是否进入hold
     * @param $item
     * @param $nowDate
     * @return boolean
     */
    protected function dealHold($item, $nowDate)
    {
        //如果当前时间大于等于离职时间，已离职

        //5号之前，同步工资hold,6号执行
        $day_5 = date("Y-m-05", strtotime($nowDate));

        //下一个月5号
        $next_day_5 = date("Y-m-05", strtotime("+1 months", strtotime($nowDate)));

        //15号之前，同步提成hold，16号执行
        $day_15 = date("Y-m-15", strtotime($nowDate));


        $next_day_15 = date("Y-m-15", strtotime("+1 months", strtotime($nowDate)));


        //离职日期在当前日期之前的
        if ($item['leave_date'] < $nowDate) {
            $param = [
                'staff_info_id' => $item['staff_info_id'],//员工id
                'type'          => '1,2',                 //hold类型(1.工资hold,2.提成hold)
                'hold_reason'   => 'incomplete_resignation_procedures1',
                'hold_remark'   => '',                                                        //hold备注
                'hold_time'     => gmdate('Y-m-d H:i:s', time() + ($this->timeOffset) * 3600),//hold时间
            ];
            $res   = (new HoldManageService())->synchronizeHoldStaff($param); // hold管理

        } else {
            //当前时间小于等于离职时间
            //nowdate=06-15,leavedate=06-15
            //都插入Resign表，不过有的没用
            $res = $this->addResign($item);

            //默认需要同步工资hold
            $salary_flag = 1;
            //大于下个月的5号
            if ($item['leave_date'] > $next_day_5) {
                $salary_flag = 0;
            }
            //大于本月的5号，但是当前日期小于等于5号
            if ($item['leave_date'] > $day_5 && $nowDate <= $day_5) {
                $salary_flag = 0;
            }

            //提成hold
            $profit_flag = 1;
            if ($item['leave_date'] > $next_day_15) {
                $profit_flag = 0;
            }
            //大于本月的15号，但是当前日期小于等于15号
            if ($item['leave_date'] > $day_15 && $nowDate <= $day_15) {
                $profit_flag = 0;
            }

            $type = [];
            if (!empty($salary_flag)) {
                $type[] = 1;
            }
            if (!empty($profit_flag)) {
                $type[] = 2;
            }


            $logDay = [
                "nowDate"     => $nowDate,
                'day_5'       => $day_5,
                'next_day_5'  => $next_day_5,
                'day_15'      => $day_15,
                'next_day_15' => $next_day_15,
            ];

            $logArr = ['type' => $type, "salary_flag" => $salary_flag, "profit_flag" => $profit_flag, "day" => $logDay];
            $this->logger->info("离职管理-处理hold-返回值===logArr==".json_encode($logArr, JSON_UNESCAPED_UNICODE));


            if (!empty($type)) {
                $param = [
                    'staff_info_id' => $item['staff_info_id'],
                    //员工id
                    'type'          => '1,2',
                    //hold类型(1.工资hold,2.提成hold)
                    'hold_reason'   => 'incomplete_resignation_procedures1',
                    //多个来源
                    'hold_remark'   => '',
                    //hold备注
                    'hold_time'     => gmdate('Y-m-d H:i:s', time() + ($this->timeOffset) * 3600),
                    //hold时间
                ];

                $param['type'] = implode(",", $type);
                $res           = (new HoldManageService())->synchronizeHoldStaff($param);
            }
        }
        return $res;
    }

    /**
     * 修改当前用户成待离职
     * @param $postData
     * @return bool|mixed|null
     */
    public function waitLeave($postData)
    {
        $client = new ApiClient("hris", "", "hr_staff_wait_leave");
        $client->setParams([$postData]);
        $res = $client->execute();
        if (empty($res)) {
            return false;
        }

        if (isset($res) && $res === true) {
            return true;
        } else {
            $this->logger->info("离职管理-待离职修改失败-返回值===".json_encode($res, JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    public function update_staff_state($data)
    {
        /**
         * 测试环境地址：http://192.168.0.230:8082/v1/
         * training环境地址：http://127.0.0.1:8082/v1/
         * 生产环境地址：http://127.0.0.1:8082/v1/
         * 请求类型: application/json;charset=UTF-8
         * 认证方式 请求参数 from=?&sign=?&time=?. 其中 sign = md5({from}{key}{time})
         * 返回数据为json类型,`{
         * "code": 0,
         * "msg": "",
         * "body": {}
         * }`固定返回3个字段，code为0表示请求正常，其他值异常、msg错误信息、body返回内容，根据接口不同返回内容不同
         *
         * 员工在职状态变更
         * 接口 : /v1/staff-ext/status
         * 请求方式: POST
         * 请求体字段说明
         * 请求示例
         * ```json
         * {
         * "type":1|2|3,  // 1在职 2离职 3停职
         * "staff_info_ids":[21992] // 员工ID
         * }
         * ```
         * 'bi' => 'ZDRkZTExMThkNzQyYjNjZDZjYzI3YWI3ZDNiMmExMjE=',
         */
        $hr_rpc = (new ApiClient('hris', '', 'update_staff_status', 'zh-CN'));
        $hr_rpc->setParamss($data);
        /**
         * array(3) {
         * ["code"]=>
         * int(0)
         * ["body"]=>
         * array(1) {
         * [0]=>
         * array(7) {
         * ["staff_info_id"]=>
         * int(119232)
         * ["type"]=>
         * int(2)
         * ["day"]=>
         * string(19) "2022-07-19 00:00:00"
         * ["leave_reason"]=>
         * int(21)
         * ["leave_source"]=>
         * int(6)
         * ["operaterId"]=>
         * int(10000)
         * ["msg"]=>
         * string(2) "ok"
         * }
         * }
         * ["version"]=>
         * int(190404)
         * }*
         *
         */
        return $hr_rpc->execute();
    }

    /**
     * 获得用户相关信息,必须在编
     * @param $userId
     * @return array
     */
    protected function getStaffInfo($userId)
    {
        $res = HrStaffInfoModel::find(
            [
                "conditions" => "formal in (1, 4) and staff_info_id = :staff_info_id: ",
                "bind"       => [
                    "staff_info_id" => $userId,
                ],
            ]
        )->toArray();
        if ($res) {
            return $res[0];
        } else {
            return [];
        }
    }

    /**
     * 根据当前语言环境检查reason id
     * @param $text
     * @return int
     */

    protected function getReasonId($text)
    {
        //ph做差异化，数据从数据表取
        $reason_id_arr = StaffLeaveReasonModel::find([
            'columns'    => 'code',
            'conditions' => 'deleted = 0',
        ])->toArray();
        $reason_id_arr = array_column($reason_id_arr, 'code');

        static $reason_text_arr = [];
        if (empty($reason_text_arr)) {
            foreach ($reason_id_arr as $reason_id) {
                $key = $this->getReasonKeyByCode($reason_id);
                $reason_text_arr[] = self::$t->_($key);
            }
        }
        $flag = false;

        for ($i = 0; $i < count($reason_text_arr); $i++) {
            if (strtolower($reason_text_arr[$i]) == strtolower($text)) {
                $flag = true;
                break;
            }
        }

        if ($flag) {
            return $reason_id_arr[$i];
        } else {
            return -1;
        }
    }

    /**
     * @description 根据离职Code查找对应的翻译Key
     * @return string
     */
    public function getReasonKeyByCode($code)
    {
        if ($code == 21) {
            return "stay_away_from_work";
        }

        if ($code == 33) {
            return 'th_leave_reason_33';
        }
        if (empty(self::$reasonKeyByCode)) {
            self::$reasonKeyByCode = $this->getStaffLeaveReason(StaffLeaveReasonModel::$agent_leave_reason);
        }
        // 这里以前有两套翻译key 
        if (in_array($code,self::$reasonKeyByCode)){
            return "resign_reason_" . $code;
        }else{
            return "leave_reason_" . $code;
        }
    }

    public function getStaffLeaveReason($group_type)
    {
        $data = StaffLeaveReasonModel::find([
            'conditions' => 'deleted=:deleted: and group_type in ({group_type:array})',
            'bind'       => ['deleted' => 0, 'group_type' => $group_type],
            'columns'    => ['code'],
            'order'      => 'sort,code',
        ])->toArray();
        return array_column($data, 'code');
    }

    /**
     * 判断用户是否能导入
     * @param $userId
     * @return bool
     */
    protected function ifImport($user_roles)
    {
        if (empty($user_roles)) {
            return false;
        }
        $allow_role_ids = SettingEnvModel::get_val("leave_manager_import_staff_ids");
        if (empty($allow_role_ids)) {
            return false;
        }
        $user_roles_ids_arr = explode(",", $user_roles);
        $allow_role_ids_arr = explode(",", $allow_role_ids);
        //取交集，不为空证明当前用户有权限
        if (!empty(array_intersect($user_roles_ids_arr, $allow_role_ids_arr))) {
            return true;
        }
        return false;
    }

    /**
     * 判断用户id是否能撤销
     * @param $userId
     * @return bool
     */
    private function ifCancel($user_roles)
    {
        if (RUNTIME == 'dev') {
            return true;
        }
        if (empty($user_roles)) {
            return false;
        }
        $allow_role_ids = SettingEnvModel::get_val("leave_manager_cancel_staff_ids");
        if (empty($allow_role_ids)) {
            return false;
        }
        $user_roles_ids_arr = explode(",", $user_roles);
        $allow_role_ids_arr = explode(",", $allow_role_ids);
        //取交集，不为空证明当前用户有权限
        if (!empty(array_intersect($user_roles_ids_arr, $allow_role_ids_arr))) {
            return true;
        }
        return false;
    }

    /**
     * 判断用户是否有 修改离职日期按钮权限
     * @param $login_user_roles 当前登录用户角色id 字符串列表
     * @return bool
     */
    private function ifEditLeaveDate($user_roles)
    {
        if (RUNTIME == 'dev') {
            return true;
        }

        if (empty($user_roles)) {
            return false;
        }
        $allow_role_ids = SettingEnvModel::get_val("leave_manage_edit_date_role");
        if (empty($allow_role_ids)) {
            return false;
        }
        $user_roles_ids_arr = explode(",", $user_roles);
        $allow_role_ids_arr = explode(",", $allow_role_ids);
        //取交集，不为空证明当前用户有权限
        if (!empty(array_intersect($user_roles_ids_arr, $allow_role_ids_arr))) {
            return true;
        }
        return false;
    }

    /**
     * 获得权限
     * @param $userId 登录用户工号
     * @param $userId 登录用户角色列表（字符串）
     * @return array
     */

    public function getPermission($userId, $user_roles = '')
    {
        return [
            "cancel"          => $this->ifCancel($user_roles),
            "import"          => $this->ifImport($user_roles),
            "edit_leave_date" => $this->ifEditLeaveDate($user_roles),
        ];
    }

    private function staffList($params)
    {
        $sql = $this->buildSql($params);
        //如果返回空sql，证明找不到数据，直接返回
        $this->logger->info('staffList params : '.json_encode($params, JSON_UNESCAPED_UNICODE)." sql ".$sql);
        if (empty($sql)) {
            return ['page_count' => 0, 'rows' => []];
        }
        $sqlCount = explode("from", $sql);
        $result   = $this->getDI()->get('db_rbi')->prepare("select count(*) as count from ".$sqlCount[1]);
        $result->execute();
        $pageCount = $result->fetchColumn();

        if (!isset($params['page'])) {
            $params['page'] = 1;
        }
        if (!isset($params['size'])) {
            $params['size'] = 50;
        }
        $offset = ($params['page'] - 1) * $params['size'];

        $sql .= " order by ( lm_assets_remand_state + lm_money_remand_state ) asc,order_leave_date asc limit {$offset}, {$params['size']}";


        $data = $this->getDI()->get('db_rbi')->query($sql)->fetchAll(Db::FETCH_ASSOC);

        $jobIds   = array_unique(array_filter(array_column($data, 'job_title')));
        $jobTitle = $jobIds ? $this->getDI()->get('db_rby')->query("select * from hr_job_title where id in (".implode(", ",
                $jobIds).") ")->fetchAll(Db::FETCH_ASSOC) : [];
        $jobTitle = array_column($jobTitle, null, 'id');

        $sysDepartments = SysDepartmentModel::find(
            [
                'conditions' => 'deleted = 0',
                'columns'    => 'id,ancestry,name',
            ]
        )->toArray();
        $sysDepartments = array_column($sysDepartments, 'name', 'id');
        $staffId        = array_unique(array_filter(array_column($data, 'staff_info_id')));

        //员工对应审核状态，[staff_id]=state
        $staffApproveStateArr = $this->getApproveState($staffId);
        $msgAssets            = [];
        if ($staffId) {
            $msgAssets = MsgAssetModel::find([
                'conditions' => ' staff_id in ({staff_ids:array}) ',
                'bind'       => [
                    'staff_ids' => $staffId,
                ],
            ])->toArray();
            $msgAssets = array_column($msgAssets, null, 'staff_id');
        }

        $return        = ['page_count' => intval($pageCount), 'rows' => []];
        $isInventories = [];
        if (isset($params['userinfo']) && $params['userinfo']) {
            // 查询是否是特殊的角色
            $positionsModel = StaffInfoPositionModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ',
                'bind'       => [
                    'staff_id'  => $params['userinfo']['id'],
                    'positions' => [
                        14, // 系统管理员
                        99, // 超级管理员
                        18, // 网点主管
                        21, // 区域经理
//                    49, // 采购经理
//                    50 // 采购专员
                    ],
                ],
            ]);

            // 查询网点是否打开了
            $sysStoreIds    = array_values(array_unique((array_column($data, 'sys_store_id'))));
            $wmsInventories = $sysStoreIds ? AssetsInventoryStoresModel::find([
                'conditions' => ' sys_store_id in ({store_ids:array})',
                'bind'       => ['store_ids' => $sysStoreIds],
            ])->toArray() : [];
            foreach ($wmsInventories as $inventory) {
                $isInventories[$inventory['sys_store_id']] = $inventory;
            }
        }
//        $loanNotReturneds = $this->loanNotReturned($staffId);
//        $reserveFunds = $this->reserveFund($staffId);
        $staffIds   = array_column($data, 'staff_info_id');
        $resignData = [];
        if ($staffIds) {
            $sql        = "select * from staff_resign where resign_id in (select max(resign_id) resign_id from staff_resign where submitter_id in (".implode(",",
                    $staffIds).") group by submitter_id)";
            $resignData = $this->getDI()->get('db_backyard')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $resignData = array_column($resignData, null, 'submitter_id');
        }

        $allPrice = $this->allPrices($staffId);

        $jobIds           = array_column($data, 'job_title');
        $nodeDepartmentId = array_column($data, 'node_department_id');
        $sysDepartmentId  = array_column($data, 'sys_department_id');

        // 拿到所有职位信息
        $allJobTitles = (new \App\Services\HrJobTitleService())->getAllJobTitleByIds($jobIds);
        // 拿到所有部门信息
        $allDepartment = (new \App\Services\SysDepartmentService())->getAllDepartmentByIds($nodeDepartmentId,
            $sysDepartmentId);

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        foreach ($data as $item) {
            $areaInfo = $this->getAreaInfoToList($item['sys_store_id']);
            if ($item['sys_store_id'] == -1) {
                $sys_store_name = GlobalEnums::HEAD_OFFICE;
            } else {
                $sys_store_name = isset($this->temp()[$item['sys_store_id']]) ? $this->temp()[$item['sys_store_id']]['name'] : '';
            }

            $job_title_name = isset($allJobTitles[$item['job_title']]) ? $allJobTitles[$item['job_title']]['job_name'] : '';
            if (!empty($job_title_name) && \App\Models\backyard\HrJobTitleModel::STATUS_2 == $allJobTitles[$item['job_title']]['status']) {
                $job_title_name .= self::$t->_('deleted');
            }

            $department_name = isset($allDepartment[$item['node_department_id']]) ? $allDepartment[$item['node_department_id']]['name'] : (isset($allDepartment[$item['sys_department_id']]) ? $allDepartment[$item['sys_department_id']]['name'] : '');
            if (!empty($department_name) && \App\Models\backyard\SysDepartmentModel::DELETE_1 == $allDepartment[$item['node_department_id']]['deleted']) {
                $department_name .= self::$t->_('deleted');
            }

            $sys_department_name = isset($allDepartment[$item['sys_department_id']]) ? $allDepartment[$item['sys_department_id']]['name'] : '';
            if (!empty($sys_department_name) && \App\Models\backyard\SysDepartmentModel::DELETE_1 == $allDepartment[$item['sys_department_id']]['deleted']) {
                $sys_department_name .= self::$t->_('deleted');
            }

            $row = [
//                'sys_department_id' => $item['sys_department_id'],
//                'node_department_id' => $item['node_department_id'],
//                'job_id' => $item['job_title'],
                'staff_info_id'       => $item['staff_info_id'],
                'name'                => $item['name'],
                'name_en'             => (string)$item['name_en'],
                'state'               => $item['state'],
                'hire_date'           => $item['hire_date'] ? date("Y-m-d", strtotime($item['hire_date'])) : '',
                'leave_date'          => $item['leave_date'] ? date("Y-m-d", strtotime($item['leave_date'])) : '',
                'sys_store_name'      => $sys_store_name,
                'job_title_name'      => $job_title_name,
                //isset($jobTitle[$item['job_title']]) ? $jobTitle[$item['job_title']]['job_name'] : '',
                'sys_department_name' => $sys_department_name,
                //isset($sysDepartments[$item['sys_department_id']]) ? $sysDepartments[$item['sys_department_id']] : '',
                'department_name'     => $department_name,
                //isset($sysDepartments[$item['node_department_id']]) ? $sysDepartments[$item['node_department_id']] : (isset($sysDepartments[$item['sys_department_id']]) ? $sysDepartments[$item['sys_department_id']] : ''),
                'store_area_id'       => $areaInfo['store_area_id'],
                'store_area_text'     => $areaInfo['store_area_text'],
                'working_country'     => $workingCountryList[$item['working_country']] ?? '',
                'state_name'          => $this->getStateName($item['state'], $item['wait_leave_state']),
                'approve_state_text'  => $this->getApproveStateName($staffApproveStateArr[$item['staff_info_id']] ?? 1),
//                'is_inventory' => 1
                // 满足四个角色 (系统管理员 超级管理员 网点主管 区域经理) 的用户并且网点也被打开了 查看编辑
                'is_inventory'        => (isset($positionsModel) && $positionsModel
                    && isset($isInventories[$item['sys_store_id']]) && $isInventories[$item['sys_store_id']]['is_switch'] == 1)
                    ? 1 : 0,
            ];


            if (isCountry('My')) {
                // resignation_notice
                // 离职申请来源  职位 职级 试用期状态展示保存的
                $row['resignation_notice'] = $row['last_work_date'] = $row['short_notice'] = '';
                if ($item['leave_source'] == self::LEAVE_SOURCE_BACKYARD) {
                    if ($resignData && isset($resignData[$item['staff_info_id']]) && in_array($resignData[$item['staff_info_id']]['status'],
                            [
                                self::APPROVAL_STATUS_WAIT,
                                self::APPROVAL_STATUS_APPROVED,
                            ]) && ($item['state'] == 1 && $item['wait_leave_state'] == 1 || $item['state'] == 2)) {
                        $row['resignation_notice'] = $resignData[$item['staff_info_id']]['resignation_notice'];// 离职通知期
                        // last work date
                        // 离职申请来源  最后工作日
                        // 批量导入来源 导入离职的日期-1
                        // 三天未出勤停职，未缴纳公款 犯罪记录， 其他 同上
                        $row['last_work_date'] = $resignData[$item['staff_info_id']]['last_work_date'];        // 最后工作日期

                        // short_notice
                        // 离职申请来源 离职通知期离职时间 - 员工的离职时间
                        // 三天未出勤离职来源 并且 有离职申请 离职通知期-实际离职日期
                        // 三天未出勤停职 未回公款 犯罪记录 离职通知期天数
                        $row['short_notice'] = 0;
                        if (!empty($resignData[$item['staff_info_id']]['resignation_leave_day']) && in_array($resignData[$item['staff_info_id']]['status'],
                                [
                                    self::APPROVAL_STATUS_WAIT,
                                    self::APPROVAL_STATUS_APPROVED,
                                ]) && $resignData[$item['staff_info_id']]['resignation_leave_day'] > $resignData[$item['staff_info_id']]['leave_date']) {
                            $diffDays            =
                                round((strtotime($resignData[$item['staff_info_id']]['resignation_leave_day']) - strtotime($resignData[$item['staff_info_id']]['leave_date'])) / (86400));
                            $row['short_notice'] = $diffDays; //  离职通知期天数
                        }
                    }
                } else {
                    $resignation = $this->resignationNotice($item, $item['staff_info_id']);
                    if (($item['state'] == 1 && $item['wait_leave_state'] == 1 || $item['state'] == 2) && $item['leave_date']) {
                        $row['resignation_notice'] = $resignation ? $resignation['resignation_notice'] : '';
                        $row['last_work_date']     = date('Y-m-d', strtotime($item['leave_date']." -1 days "));
                        $row['short_notice']       = 0;
                        if ($resignation && $resignation['resignation_notice']) {
                            $row['short_notice'] = StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? '';
                        }
                    }

                    if ($item['leave_source'] == self::LEAVE_SOURCE_BATCH) {
                        // 导入离职
                        $hrStaffitem         = HrStaffItemsModel::findFirst([
                            'conditions' => ' staff_info_id = :staff_id: and item = :item:',
                            'bind'       => ['staff_id' => $item['staff_info_id'], 'item' => 'SHORT_NOTICE'],
                        ]);
                        $row['short_notice'] = $hrStaffitem ? $hrStaffitem->value : '';
                    }

                    if ($item['leave_source'] == self::LEAVE_SOURCE_OFF3DAYS && $row['leave_date'] && $resignData && isset($resignData[$item['staff_info_id']])) {
                        // 三天未出勤 离职 并且申请过离职申请
                        $row['short_notice'] = round(((strtotime(explode(' ',
                                        $resignData[$item['staff_info_id']]['created_at'])[0])
                                    - strtotime($row['leave_date'])) / (86400)) + $row['short_notice']);
                    }
                }


                if ($row['resignation_notice']) {
                    $tmp                       = explode(" ", $row['resignation_notice']);
                    $row['resignation_notice'] = $tmp[0].self::$t->_($tmp[1]);
                }
            }
            if ($item['state'] == 2) {
                // 离职 取 实际的离职日期
                $row['leave_date'] = explode(' ', $item['leave_date'])[0];
            } elseif ($item['leave_source'] == self::LEAVE_SOURCE_BACKYARD && $resignData && isset($resignData[$item['staff_info_id']])) {
                $row['leave_date'] = in_array($resignData[$item['staff_info_id']]['status'],
                    [3, 4]) ? "" : $resignData[$item['staff_info_id']]['leave_date'];
            }

            $row['wait_leave_state'] = $item['wait_leave_state'];
            //为归还钱款总额
            $row['all_price']        = $allPrice[$item['staff_info_id']] ? bcdiv($allPrice[$item['staff_info_id']], 100,
                2) : 0;
//            $row['loan_not_return'] = $loanNotReturneds[$item['staff_info_id']] ?? 0  ;
//            $row['reserve_fund'] = $reserveFunds[$item['staff_info_id']] ?? 0  ;
            $row['assets_remand_state'] = (int)$item['lm_assets_remand_state'] ? (int)$item['lm_assets_remand_state'] : self::ASSETS_STATE_UNPROCESSED;
            $row['money_remand_state']  = (int)$item['lm_money_remand_state'] ? (int)$item['lm_money_remand_state'] : self::ASSETS_STATE_UNPROCESSED;
            $row['remand_state']        = $this->leaveStateMaps((int)$item['lm_assets_remand_state'],
                (int)$item['lm_money_remand_state']);
            $row['leave_approval_date'] = $item['lm_leave_date'] && $item['lm_leave_date'] != "0000-00-00 00:00:00" ? date("Y-m-d",
                strtotime($item['lm_leave_date'])) : "";
//            $row['leave_date'] = $item['order_leave_date'] && $item['order_leave_date'] != "0000-00-00 00:00:00" ? date("Y-m-d", strtotime($item['order_leave_date'])) : "";
            $row['approval_status']      = (int)$item['lm_approval_status'] ? (int)$item['lm_approval_status'] : 0;
            $row['approval_status_text'] = (int)$item['lm_approval_status'] ? self::$t->_("by_state_".$item['lm_approval_status']) : "";
            $row['is_has_superior']      = (int)$item['lm_is_has_superior'] ? (int)$item['lm_is_has_superior'] : 0;
            if (($row['state'] == 2 && $row['is_has_superior'] == 0) || !in_array($item['job_title'], [
                    AssetsService::$job_title['bike_courier'],
                    AssetsService::$job_title['van_courier'],
                    AssetsService::$job_title['shop_officer'],
                    AssetsService::$job_title['branch_supervisor'],
                    AssetsService::$job_title['shop_supervisor'],
                ]) || !isset($msgAssets[$item['staff_info_id']])) {
                // 已离职并且主管处理情况为未处理
                // 包含了 历史数据 与 手动改离职
                $row['is_has_superior_text'] = "";
            } else {
                $row['is_has_superior_text'] = (int)$item['lm_is_has_superior'] ? self::$t->_("assets_state_".self::ASSETS_STATE_PROCESSED) : self::$t->_("assets_state_".self::ASSETS_STATE_UNPROCESSED);
            }

            $row['assets_remand_state_text'] = self::$t->_('assets_state_'.(int)$item['lm_assets_remand_state']);
            $row['money_remand_state_text']  = self::$t->_('assets_state_'.(int)$item['lm_money_remand_state']);
            $row['remand_state_text']        = self::$t->_('assets_state_'.$this->leaveStateMaps((int)$item['lm_assets_remand_state'],
                    (int)$item['lm_money_remand_state']));
            $row['is_has_employ_operater']   = !in_array($item['sys_department_id'], [
                AssetsService::$job_title['fulfillment'],
                AssetsService::$job_title['flash_money'],
                AssetsService::$job_title['flash_home'],
                AssetsService::$job_title['flash_logistic'],
            ]) && $item['state'] == 2 ? 1 : 0;

            $row['leave_source']      = $item['leave_source'];
            $row['leave_source_text'] = "";
            if (!empty($item['leave_source'])) {
                $row['leave_source_text'] = $this->getLeaveSourceText($item['leave_source']);
            }


            //新需求 来源非“BY申请离职”以及职位一下职位（Bike courier 13 /van courier 110/Branch Supervisor 16/shop officer 98/ Shop Supervisor 101）不显示某些字段 并且关系
            $row['is_show'] = 0;
            $forbidden      = [13, 110, 16, 98, 101];
            //来源 '0=空，-1其他。6批量导入。其他同hold来源,1.新增,2.hirs,3旷工3天及以上，4未缴纳公款，5backyard提交申请，'
            if ($item['leave_source'] == 5 && in_array($item['job_title'], $forbidden)) {
                $row['is_show'] = 1;
            }
            $return['rows'][] = $row;
        }

        return $return;
    }

    /**
     *
     *
     * @param $grade
     * @param $staffId
     *
     */
    public function resignationNotice($info, $staffId, $date = '')
    {
        $probation = HrProbationModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $staffId],
        ]);
        $probation = $probation ? $probation->toArray() : [];

        //formal\hire_type\job_title_grade_v2\leave_date\job_title\leave_date
        if ($info['formal'] == 4) {
            // 实习生
            return [];
        }
        $add_hour = $this->getDI()['config']['application']['add_hour'];
        $result   = [];
        if ($info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_1) {
            // 正式员工
            if($info['job_title_grade_v2'] == 16) {
                $resignation = '1'.self::$t->_('monthlies');
                $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                $result = $this->getResignationNoticeResult($resignation, $resignation_notice, $date);
                $this->logger->info(['resignationNoticeResult1'=>$result]);
            } else if($info['job_title_grade_v2'] == 17) {
                if ($probation && $probation['status'] == 4 && $info['leave_date'] >= $probation['formal_at']) {
                    // 以转正
                    $resignation = '1.5' .self::$t->_('monthlies');
                    $resignation_notice = StaffResignModel::resignation_notice_1_5_monthly;
                } else {
                    $resignation = '1'.self::$t->_('monthly');
                    $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                }
                $result = $this->getResignationNoticeResult($resignation, $resignation_notice , $date);
                $this->logger->info(['resignationNoticeResult2'=>$result]);
            } else if ($info['job_title_grade_v2'] > 17) {
                if ($probation && $probation['status'] == 4 && $info['leave_date'] >= $probation['formal_at']) {
                    // 以转正
                    $resignation = '2'.self::$t->_('monthlies');
                    $resignation_notice = StaffResignModel::resignation_notice_2_monthlies;
                } else {
                    $resignation = '1'.self::$t->_('monthly');
                    $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                }
                $result = $this->getResignationNoticeResult($resignation, $resignation_notice, $date);
                $this->logger->info(['resignationNoticeResult3'=>$result]);
            } else if ($info['job_title_grade_v2'] && $info['job_title_grade_v2'] <= 15) {
                if ($probation && $probation['status'] == 4 && $info['leave_date'] >= $probation['formal_at']) {
                    // 以转正
                    $resignation = '1'.self::$t->_('monthly');
                    $resignation_notice = StaffResignModel::resignation_notice_1_monthly;
                } else {
                    $resignation = '2'.self::$t->_('weeklies');
                    $resignation_notice = StaffResignModel::resignation_notice_2_weeklies;
                }
                $result = $this->getResignationNoticeResult($resignation, $resignation_notice, $date);
                $this->logger->info(['resignationNoticeResult4'=>$result]);
            }
            $settingEnvServer = new SettingEnvService();

            $jobIds = $settingEnvServer->getSetVal('resignation_notice_first_line_jobs');
            $jobIds = explode(',', $jobIds);

            if (in_array($info['job_title'], $jobIds)) {
                if ($probation && $probation['status'] == 4 && $info['leave_date'] >= $probation['formal_at']) {
                    // 以转正
                    $resignation = '2'.self::$t->_('weeklies');
                    $resignation_notice = StaffResignModel::resignation_notice_2_weeklies;
                } else {
                    $resignation = '1'.self::$t->_('weekly');
                    $resignation_notice = StaffResignModel::resignation_notice_1_weekly;
                }
                $result = $this->getResignationNoticeResult($resignation, $resignation_notice, $date);
                $this->logger->info(['resignationNoticeResult5'=>$result]);

            }
        } elseif ($info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $resignation = StaffResignModel::resignation_notice_15_day.self::$t->_('day');
            $resignation_notice = StaffResignModel::resignation_notice_15_days;
            $result = $this->getResignationNoticeResult($resignation, $resignation_notice, $date);
            $this->logger->info(['resignationNoticeResult13'=>$result]);
        }  elseif ($info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT) {
            $resignation = '';
            $resignation_notice = 0;
            $result = $this->getResignationNoticeResult($resignation, $resignation_notice, $date);
            $this->logger->info(['resignationNoticeResult13'=>$result]);
        }else {
            $resignation = '1'.self::$t->_('weekly');
            $resignation_notice = StaffResignModel::resignation_notice_1_weekly;
            $result = $this->getResignationNoticeResult($resignation, $resignation_notice, $date);
            $this->logger->info(['resignationNoticeResult6'=>$result]);

        }

        return $result;
    }

    public function getResignationNoticeResult($resignation, $resignation_notice , $date = '') {
        $days = StaffResignModel::$short_notice[$resignation_notice]?? 1;

        $returnData = [
            'resignation' => $resignation,
            'resignation_notice' => $resignation_notice,
        ];

        if (!empty($date)) {
            $returnData['leave_day'] = date('Y-m-d', strtotime( "{$date} +{$days} days"));
            $returnData['work_day']  = date('Y-m-d', strtotime("{$date} +{$days} days -1 days"));
        } else {
            $returnData['leave_day'] = date('Y-m-d', strtotime( " +{$days} days"));
            $returnData['work_day']  = date('Y-m-d', strtotime(" +{$days} days -1 days"));
        }

        return $returnData;
    }

    public function calculDay($addDayStr, $testDay = '')
    {
        $add_hour   = $this->getDI()['config']['application']['add_hour'];
        $nextMonth  = gmdate('Y-m',
            strtotime(substr($testDay, 0, -3)."-01 ".$addDayStr) + $add_hour * 3600); // 获取下个月的月份
        $currentDay = gmdate('d', strtotime($testDay) + $add_hour * 3600);            // 当前日期

        $nextDay = gmdate("Y-m-d", strtotime($testDay." ".$addDayStr) + $add_hour * 3600);
        if ($currentDay > gmdate('t', strtotime($nextMonth."-01") + $add_hour * 3600)) {
            // 当前日期 大于 下个月份的月末日期 取月末日期
            $nextDay = $nextMonth."-".gmdate('t', strtotime($nextMonth) + $add_hour * 3600);
        }

        return $nextDay;
    }

    /**
     * 根据网点获取对应 所属区域
     * @param $sysStoreId
     * @return mixed
     */
    public function getAreaInfoToList($sysStoreId)
    {

        $areaInfo = $this->getAreaByStore($sysStoreId);

        $result['store_area_id']   = $areaInfo ? $areaInfo['id'] : 0;
        $result['store_area_text'] = $areaInfo ? GlobalEnums::$areas[$areaInfo['id']] : '';
        return $result;
    }

    protected function buildSql($params)
    {
        $pdo = $this->getDI()->get('db_rby')->prepare("select distinct submitter_id from staff_resign where status = 1 ");
        $pdo->execute();
        $results      = $pdo->fetchAll(Db::FETCH_ASSOC);
        $staffInfoIds = array_filter(array_column($results, 'submitter_id'));
        if ($staffInfoIds) {
            $sql = "
                -- 
                    select 
                        hsi.staff_info_id, 
                        hsi.name, 
                        hsi.name_en, 
                        hsi.mobile, 
                        hsi.job_title, 
                        hsi.sys_department_id, 
                        hsi.node_department_id, 
                        hsi.sys_store_id, 
                        hsi.hire_date, 
                        hsi.state, 
                        hsi.wait_leave_state, 
                        hsi.leave_date, 
                        hsi.leave_type,
                        hsi.leave_reason,
                        hsi.leave_reason_remark,
                        hsi.working_country,
                        lm.staff_info_id as lm_staff_info_id, 
                        lm.assets_operate_version as lm_assets_operate_version, 
                        lm.is_has_superior as lm_is_has_superior, 
                        lm.superior_operate_time as lm_superior_operate_time, 
                        lm.superior_operate_version as lm_superior_operate_version, 
                        lm.leave_date as lm_leave_date, 
                        lm.approval_status as lm_approval_status, 
                        lm.assets_operate_remark as lm_assets_operate_remark, 
                        lm.assets_remand_state as lm_assets_remand_state, 
                         (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end ) lm_assets_remand_state,
                        (case when lm.money_remand_state is null then 1 when lm.money_remand_state = 0 then 1 else lm.money_remand_state end ) lm_money_remand_state, 
                        (case when lm.leave_date is null then hsi.leave_date when lm.leave_date = '0000-00-00 00:00:00' then hsi.leave_date else lm.leave_date end) order_leave_date,
                        ss.category as ss_category,
                        hsi.leave_source, 
                        hsi.formal, 
                        hsi.job_title_grade_v2, 
                        hsi.hire_type 
                    from 
                        hr_staff_info hsi 
                    left join 
                        leave_manager lm  
                    on 
                        hsi.staff_info_id = lm.staff_info_id 
                    left join 
                        sys_store ss on hsi.sys_store_id = ss.id 
                    where  
                        (hsi.staff_info_id in ( ".implode(", ",
                    $staffInfoIds).") or  hsi.state = 1 and  hsi.wait_leave_state = 1 or hsi.state = 2 and hsi.leave_date > '2020-04-17 00:00:00' or lm.approval_status is not null)  and hsi.formal in (1, 4)  ";
        } else {
            $sql = "
                --
                    select 
                        hsi.staff_info_id, 
                        hsi.name, 
                        hsi.name_en, 
                        hsi.mobile, 
                        hsi.job_title, 
                        hsi.sys_department_id, 
                        hsi.node_department_id, 
                        hsi.sys_store_id, 
                        hsi.hire_date, 
                        hsi.state, 
                        hsi.wait_leave_state, 
                        hsi.leave_date, 
                        hsi.leave_type,
                        hsi.leave_reason,
                        hsi.leave_reason_remark,
                        hsi.working_country,
                        lm.staff_info_id as lm_staff_info_id, 
                        lm.assets_operate_version as lm_assets_operate_version, 
                        lm.is_has_superior as lm_is_has_superior, 
                        lm.superior_operate_time as lm_superior_operate_time, 
                        lm.superior_operate_version as lm_superior_operate_version, 
                        lm.leave_date as lm_leave_date, 
                        lm.approval_status as lm_approval_status,
                        lm.assets_operate_remark as lm_assets_operate_remark, 
                         (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end ) lm_assets_remand_state,
                        (case when lm.money_remand_state is null then 1 when lm.money_remand_state = 0 then 1 else lm.money_remand_state end ) lm_money_remand_state, 
                        (case when lm.leave_date is null then hsi.leave_date  when lm.leave_date = '0000-00-00 00:00:00' then hsi.leave_date else lm.leave_date end) order_leave_date,
                        ss.category as ss_category,
                        hsi.leave_source,  
                        hsi.formal, 
                        hsi.job_title_grade_v2, 
                        hsi.hire_type
                    from 
                        hr_staff_info hsi 
                    left join 
                        leave_manager lm  
                    on 
                        hsi.staff_info_id = lm.staff_info_id 
                    left join 
                        sys_store ss 
                    on 
                        hsi.sys_store_id = ss.id 
                    where 
                        (hsi.state = 1 and hsi.wait_leave_state = 1 or hsi.state = 2 and hsi.leave_date > '2020-04-17 00:00:00'  or lm.approval_status is not null)  and hsi.formal in (1, 4)  ";
        }

        $sql .= " and hsi.is_sub_staff = 0 ";

        //增加查询条件
        if (isset($params['member_ids']) && $params['member_ids']) {
            if ($params['isProcess']) {
                $sql .= "and (hsi.staff_info_id in ( ".implode(", ",
                        $params['member_ids']).") and (lm.is_has_superior = 1 and lm.superior_id = ".$params['manager_staff_id'].'))';
            } else {
                $sql .= "and (hsi.staff_info_id in ( ".implode(", ",
                        $params['member_ids']).") and (lm.is_has_superior = 0  or lm.is_has_superior is null))";
            }
        }

        // 工作所在国家
        if (isset($params['working_country']) && $params['working_country']) {
            $sql .= " and hsi.working_country in ( ".implode(",", $params['working_country'])." ) ";
        }

        //搜索条件

        unset($staffInfoIds); //使用后删除
        if (isset($params['name']) && $params['name']) {
            $sql .= " and (hsi.name like \"{$params['name']}\" or hsi.staff_info_id like \"{$params['name']}\"  or hsi.emp_id like \"{$params['name']}\" or hsi.name_en like \"{$params['name']}\" ) ";
        }
        if (isset($params['hire_date_begin']) && $params['hire_date_begin'] != '0000-00-00 00:00:00') {
            $sql .= " and hsi.hire_date >= \"{$params['hire_date_begin']}\" ";
        }
        if (isset($params['hire_date_end']) && $params['hire_date_end'] != '0000-00-00 00:00:00') {
            $sql .= " and hsi.hire_date <= \"{$params['hire_date_end']}\" ";
        }

        if (!empty($params['leave_source'])) {
            $sql .= " and hsi.leave_source = {$params['leave_source']} ";
        }

        if (isset($params['store_area_id']) && $params['store_area_id']) {
            $storeIds = $this->getStoresByArea($params['store_area_id']);
            if ($storeIds) {
                $storeIds = implode('", "', $storeIds);
                $sql      .= " and hsi.sys_store_id in ( \"{$storeIds}\" ) ";
            } else {
                return "";
            }
        }

        if (isset($params['leave_date_begin']) && $params['leave_date_begin'] != '0000-00-00 00:00:00'
            &&
            isset($params['leave_date_end']) && $params['leave_date_end'] != '0000-00-00 00:00:00'
        ) {
            $pdo = $this->getDI()->get('db_backyard')->prepare("
            --
            select 
                distinct submitter_id 
            from 
                staff_resign 
            where 
                leave_date >= '{$params['leave_date_begin']}' and leave_date <= '{$params['leave_date_end']}' and status = 2
            ");

            $pdo->execute();
            $results      = $pdo->fetchAll(Db::FETCH_ASSOC);
            $staffInfoIds = array_filter(array_column($results, 'submitter_id'));

            if ($staffInfoIds) {
                $sql .= " and 
                          (
                            hsi.staff_info_id in (".implode(",", $staffInfoIds).") 
                            or 
                            hsi.leave_date >= '{$params['leave_date_begin']}' 
                            and 
                            hsi.leave_date <= '{$params['leave_date_end']}'
                          ) ";
            } else {
                // 离职申请查不到 查询其他来源的
                $sql .= " and 
                          (
                            hsi.leave_date >= '{$params['leave_date_begin']}' 
                            and 
                            hsi.leave_date <= '{$params['leave_date_end']}'
                          ) ";
            }
        }
        /**
         *
         * 在职状态
         * 1 在职
         * 2 离职
         * 3 停职
         * 4 待离职
         *
         */
        if (isset($params['state']) && $params['state']) {
            if ($params['state'] == 1) {
                $sql .= " and hsi.state = {$params['state']} and hsi.wait_leave_state = 0 ";
            } else {
                if ($params['state'] == 4) {
                    $sql .= " and hsi.state = 1 and hsi.wait_leave_state = 1 ";
                } else {
                    $sql .= " and hsi.state = {$params['state']} ";
                }
            }
        }

        if (isset($params['job_title_id']) && $params['job_title_id']) {
            $sql .= " and hsi.job_title = {$params['job_title_id']}  ";
        }

        if (isset($params['store']) && $params['store']) {
            $sql .= " and hsi.sys_store_id = \"{$params['store']}\" ";
        }
        if (isset($params['stores']) && $params['stores']) {
            $sql .= " and hsi.sys_store_id in ('".implode("','", $params['stores'])."')";
        }

        if (isset($params['department']) && $params['department']) {
            //试用期管理 查询当前部门及自部门
            $sql .= " and ( hsi.sys_department_id = \"{$params['department']}\" or hsi.node_department_id = {$params['department']} ) ";
        }
        if (isset($params['remand_state']) && $params['remand_state']) {
            if ($params['remand_state'] == self::ASSETS_STATE_UNPROCESSED) {
                $sql .= " and  
                          ( 
                            (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end  ) in (0,1)  
                            and 
                            (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) in (0,1)
                          ) ";
            } elseif ($params['remand_state'] == self::ASSETS_STATE_PROCESSING) {
                $sql .= " and  
                          ( 
                            (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end  ) = 2  
                            or 
                            (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) = 2 
                            or 
                            ( 
                                (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end  ) in (0,1)  
                                and 
                                (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) not in (0,1) 
                            ) or ( 
                            (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end  ) not in (0,1)   
                            and 
                            (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) in (0,1) )
                          ) ";
            } elseif ($params['remand_state'] == self::ASSETS_STATE_PROCESSED) {
                $sql .= " and  
                        ( 
                            (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end  ) = 3  
                            and 
                            (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) = 3
                        )";
            }
        }

        if (isset($params['assets_remand_state']) && $params['assets_remand_state']) {
            if ($params['assets_remand_state'] == self::ASSETS_STATE_UNPROCESSED) {
                $sql .= " and (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end  ) in (0,1) ";
            } else {
                if ($params['assets_remand_state'] == self::ASSETS_STATE_PROCESSING) {
                    $sql .= " and (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end  ) = 2 ";
                } else {
                    if ($params['assets_remand_state'] == self::ASSETS_STATE_PROCESSED) {
                        $sql .= " and (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end ) = 3 ";
                    } else {
                        if ($params['assets_remand_state'] == self::ASSETS_STATE_UN_DEDUCTED) {
                            $sql .= " and (case when ss.category in (3 ,6) then 3  when lm.assets_remand_state is  null then 1 when lm.assets_remand_state = 0 then 1 when ss.category is null and hsi.sys_store_id != -1 then 3 else lm.assets_remand_state end )= 4 ";
                        }
                    }
                }
            }
        }

        if (isset($params['money_remand_state']) && $params['money_remand_state']) {
            if ($params['money_remand_state'] == self::ASSETS_STATE_UNPROCESSED) {
                $sql .= "and  (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) in (0,1) ";
            } else {
                if ($params['money_remand_state'] == self::ASSETS_STATE_PROCESSING) {
                    $sql .= "and  (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) = 2 ";
                } else {
                    if ($params['money_remand_state'] == self::ASSETS_STATE_PROCESSED) {
                        $sql .= "and  (case when lm.money_remand_state is null then 1 else lm.money_remand_state end ) = 3 ";
                    }
                }
            }
        }


        if (isset($params['approval_status']) && !empty($params['approval_status'])) {
            $tArr = $params['approval_status'];
            foreach ($tArr as $k => $v) {
                $tArr[$k] = intval($v);
            }
            if (!empty($tArr)) {
                $statusIds = implode('", "', $tArr);
                $sql       .= " and lm.approval_status in ( \"{$statusIds}\" ) ";
            }
        }
        $this->logger->info("leaveassets_sql: ".$sql);
        return $sql;
    }

    /**
     * @param array $staffIds
     * @return array
     */
    public function getApproveState(array $staffIds)
    {
        if (empty($staffIds)) {
            return [];
        }
        $arr = AssetsHandoverModel::find(
            [
                "columns"    => "staff_id,state",
                "conditions" => " staff_id in ({ids:array})",
                "bind"       => [
                    "ids" => $staffIds,
                ],
            ]
        )->toArray();
        //如果不为空，转化下
        if (!empty($arr)) {
            $arr = array_column($arr, "state", "staff_id");
        }
        return $arr;
    }

    // 根据区域获取网点列表
    public function getStoresByArea(...$str)
    {
        $ths = [];
        foreach ($str as $name) {
            if (isset(self::areaProvince()[$name])) {
                $ths = array_merge($ths, self::areaProvince()[$name]);
            }
        }

        $stores    = [];
        $storelist = $this->temp();
        foreach ($storelist as $storeId => $store) {
            if (in_array($store['province_code'], $ths)) {
                $stores[] = $storeId;
            }
        }
        return $stores;
    }

    /**
     * 获取所属区域与省份对应列表
     *
     * @return array
     */
    private function areaProvince()
    {
        if (!self::$areaProvince) {
            $provinces = $this->getDI()->get('db_rby')->query("
            --
            select 
                code, manage_geography_code 
            from 
                sys_province 
            where 
                deleted = 0 
            ")->fetchAll(\PDO::FETCH_ASSOC);

            if ($provinces && is_array($provinces)) {
                foreach ($provinces as $province) {
                    self::$areaProvince[$province['manage_geography_code']][] = $province['code'];
                }
            }
            unset($provinces);
        }

        return self::$areaProvince;
    }


    public function getAreaByStore($storeId)
    {
        $province  = '';
        $storeList = $this->temp();
        foreach ($storeList as $sid => $store) {
            if ($sid == $storeId) {
                $province = $store['province_code'];
                break;
            }
        }

        if (!$province) {
            return '';
        }

        foreach (self::areaProvince() as $id => $provinces) {
            if (empty($id)) {
                continue;
            }
            if (in_array($province, $provinces)) {
                return ['id' => $id, 'name' => GlobalEnums::$areas[$id]];
            }
        }
        return [];
    }

    private static $static_sore_list = [];

    protected function temp()
    {
        if (self::$static_sore_list) {
            return self::$static_sore_list;
        }

        $storelist              = array_column((new SysStoreService())->getStoreList(), null, 'id');
        self::$static_sore_list = $storelist;
        return $storelist;
    }

    /**
     * 获取员工在职状态
     *
     * @param $state
     * @param $waitLeaveState
     * @return string
     */
    public function getStateName($state, $waitLeaveState)
    {
        if ($state == 1 && $waitLeaveState == 1) {
            $stateName = self::$t->_('wait_leave_state_'.$waitLeaveState);
        } else {
            $stateName = self::$t->_('staff_state_'.$state);
        }

        return $stateName;
    }

    /**
     * 获得员工审核状态
     * @param $state
     * @return string
     */
    protected function getApproveStateName($state)
    {
        //如果不存在，默认是未提交
        if (empty($state)) {
            $state = 1;
        }
        return self::$t->_('approve_state_'.$state);
    }

    /**
     * 获取离职员工资产信息被处理状态映射值
     *
     * @param $assetState
     * @param $moneyState
     * @return mixed
     */
    protected function leaveStateMaps($assetState, $moneyState)
    {
        $map[self::ASSETS_STATE_UNPROCESS][self::ASSETS_STATE_UNPROCESS]   = self::ASSETS_STATE_UNPROCESSED;
        $map[self::ASSETS_STATE_UNPROCESS][self::ASSETS_STATE_UNPROCESSED] = self::ASSETS_STATE_UNPROCESSED;
        $map[self::ASSETS_STATE_UNPROCESS][self::ASSETS_STATE_PROCESSING]  = self::ASSETS_STATE_UNPROCESSED;
        $map[self::ASSETS_STATE_UNPROCESS][self::ASSETS_STATE_PROCESSED]   = self::ASSETS_STATE_PROCESSING;

        $map[self::ASSETS_STATE_UNPROCESSED][self::ASSETS_STATE_UNPROCESS]   = self::ASSETS_STATE_UNPROCESSED;
        $map[self::ASSETS_STATE_UNPROCESSED][self::ASSETS_STATE_UNPROCESSED] = self::ASSETS_STATE_UNPROCESSED;
        $map[self::ASSETS_STATE_UNPROCESSED][self::ASSETS_STATE_PROCESSING]  = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_UNPROCESSED][self::ASSETS_STATE_PROCESSED]   = self::ASSETS_STATE_PROCESSING;

        $map[self::ASSETS_STATE_PROCESSING][self::ASSETS_STATE_UNPROCESS]   = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_PROCESSING][self::ASSETS_STATE_UNPROCESSED] = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_PROCESSING][self::ASSETS_STATE_PROCESSING]  = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_PROCESSING][self::ASSETS_STATE_PROCESSED]   = self::ASSETS_STATE_PROCESSING;

        $map[self::ASSETS_STATE_PROCESSED][self::ASSETS_STATE_UNPROCESS]   = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_PROCESSED][self::ASSETS_STATE_UNPROCESSED] = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_PROCESSED][self::ASSETS_STATE_PROCESSING]  = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_PROCESSED][self::ASSETS_STATE_PROCESSED]   = self::ASSETS_STATE_PROCESSED;

        $map[self::ASSETS_STATE_UN_DEDUCTED][self::ASSETS_STATE_UNPROCESS]   = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_UN_DEDUCTED][self::ASSETS_STATE_UNPROCESSED] = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_UN_DEDUCTED][self::ASSETS_STATE_PROCESSING]  = self::ASSETS_STATE_PROCESSING;
        $map[self::ASSETS_STATE_UN_DEDUCTED][self::ASSETS_STATE_PROCESSED]   = self::ASSETS_STATE_PROCESSING;

        return $map[$assetState][$moneyState];
    }


    /**
     * 获得离职来源
     * @param $source_id
     * @return mixed|string|void
     */


    public function getLeaveSourceText($source_id)
    {
        if (empty($source_id)) {
            return "";
        }
        //HRIS基本不会有
        if ($source_id == 2) {
            return self::LEAVE_SOURCE_DESC[2];
        }

        //Backyard添加
        if ($source_id == 1) {
            return self::$t->_("leave_source_1");
        }

        return self::$t->_(self::LEAVE_SOURCE_DESC[$source_id]);
    }

    /**
     * @param $storeIds
     * @return array
     */
    public function storeAreas($storeIds)
    {
        $stores = [];
        if ($storeIds) {
            $stores        = (new SysStoreService())->getStoreList($storeIds);
            $provinceCodes = array_values(array_filter(array_unique(array_column($stores, 'province_code'))));
            if ($provinceCodes) {
                $provinces = SysProvinceModel::find([
                    'columns'    => 'code,name,manage_geography_code',
                    'conditions' => 'code in ({codes:array})',
                    'bind'       => ['codes' => $provinceCodes],
                ])->toArray();
                $provinces = array_column($provinces, null, 'code');
                foreach ($stores as $key => $store) {
                    $stores[$key]['manage_geography_code'] =
                        isset($provinces[$store['province_code']]) && $provinces[$store['province_code']]['manage_geography_code']
                            ? $provinces[$store['province_code']]['manage_geography_code'] : '';
                }
                $stores = array_column($stores, null, 'id');
            }
        }
        return $stores;
    }

    /**
     * 资产信息
     *
     * @param $staffIds
     *
     */
    public function assetsExport($staffIds)
    {
        // 离职管理资产列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('lm.staff_info_id, 
        max(lm.assets_operate_version) as assets_operate_version,
        GROUP_CONCAT(lam.assets_id)  as assets_ids, 
        GROUP_CONCAT(lam.goods_id)  as goods_ids,
        sum(lam.price) as all_price');
        $builder->from(['lm' => LeaveManagerModel::class]);
        $builder->leftJoin(LeaveAssetsManagerModel::class, 'lm.staff_info_id = lam.staff_info_id 
        and lm.assets_operate_version = lam.version', 'lam');
        $builder->inWhere(' lm.staff_info_id ', $staffIds);
        $builder->groupBy('lm.staff_info_id');
        $items    = $builder->getQuery()->execute()->toArray();
        $data     = [];
        $goodsIds = explode(",", implode(",", array_column($items, 'goods_ids')));

        if ($goodsIds && is_array($goodsIds)) {
            $goods = AssetsGoodsModel::find([
                'columns'    => 'id, goods_name_en, goods_name_th, goods_name_zh',
                'conditions' => ' id in ({ids:array})',
                'bind'       => ['ids' => $goodsIds],
            ])->toArray();
            $goods = array_column($goods, null, 'id');
        }

        foreach ($items as $item) {
            if ($item['goods_ids']) {
                $goodsStr = '';
                $ids      = array_count_values(explode(',', $item['goods_ids']));
                foreach ($ids as $id => $count) {
                    $goodsStr .= isset($goods) && isset($goods[$id]) ? $goods[$id]['goods_name_'.strtolower(substr(self::$language,
                            0, 2))]."*".$count." " : "";
                }
                $data[$item['staff_info_id']] = [
                    'all_price' => bcdiv($item['all_price'], 100, 2),
                    'goods'     => $goodsStr,
                ];
            } else {
                if($item['assets_operate_version'] > 0) {
                    $data[$item['staff_info_id']] = [
                        'all_price' => 0,
                        'goods'     => '',
                    ];
                }
            }
        }

        // 离职管理里面没有数据， 取员工本身的资产
        // 个人资产和公共单个资产
        $staffIds = array_diff($staffIds, array_keys($data));
        if ($staffIds) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('ai.staff_info_id, 
        group_concat(ai.id) as assets_ids,
        group_concat(ai.assets_goods_id) as goods_ids,
	    SUM(ag.value_th) as price');
            $builder->from(['ai' => AssetsInfoModel::class]);
            $builder->leftJoin(AssetsGoodsModel::class, 'ai.assets_goods_id = ag.id', 'ag');
            $builder->inWhere('ai.staff_info_id', $staffIds);
            $builder->andWhere('(ag.is_public = 0 or ag.is_public = 1 and ag.is_batch = 0)  and ai.state = 1');
            $builder->groupBy('ai.staff_info_id');
            $items = $builder->getQuery()->execute()->toArray();

            $goodsIds = explode(",", implode(",", array_column($items, 'goods_ids')));
            if ($goodsIds && is_array($goodsIds)) {
                $goods = AssetsGoodsModel::find([
                    'columns'    => 'id, goods_name_en, goods_name_th, goods_name_zh',
                    'conditions' => ' id in ({ids:array})',
                    'bind'       => ['ids' => $goodsIds],
                ])->toArray();
                $goods = array_column($goods, null, 'id');
            }
            foreach ($items as $item) {
                $goodsStr = '';
                $ids      = array_count_values(explode(',', $item['goods_ids']));
                foreach ($ids as $id => $count) {
                    $goodsStr .= isset($goods) && isset($goods[$id]) ? $goods[$id]['goods_name_'.strtolower(substr(self::$language,
                            0, 2))]."*".$count." " : "";
                }
                $data[$item['staff_info_id']] = [
                    'all_price' => $item['price'],
                    'goods'     => $goodsStr,
                ];
            }

            // 员工公共批量资产
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('ag.value_th , ai.staff_info_id, GROUP_CONCAT(ai.assets_goods_id) as goods_ids');
            $builder->from(['ai' => AssetsInfoModel::class]);
            $builder->leftJoin(AssetsGoodsModel::class, 'ai.assets_goods_id = ag.id', 'ag');
            $builder->inWhere('ai.staff_info_id', $staffIds);
            $builder->andWhere('ag.is_public = 1 and  ag.is_batch = 1 and ai.state = 1');
            $builder->groupBy('ai.assets_goods_id');
            $batchItems = $builder->getQuery()->execute()->toArray();
            $goodsIds   = explode(",", implode(",", array_column($batchItems, 'goods_ids')));
            if ($goodsIds && is_array($goodsIds)) {
                $batchGoods = AssetsGoodsModel::find([
                    'columns'    => 'id, goods_name_en, goods_name_th, goods_name_zh',
                    'conditions' => ' id in ({ids:array})',
                    'bind'       => ['ids' => $goodsIds],
                ])->toArray();
                $batchGoods = array_column($batchGoods, null, 'id');
            }

            foreach ($batchItems as $item) {
                $id =
                    explode(",", $item['goods_ids'])[0];
                if (!isset($data[$item['staff_info_id']])) {
                    $data[$item['staff_info_id']] = [
                        'all_price' => $item['value_th'],
                        'goods'     => isset($batchGoods) && isset($batchGoods[$id]) ? $batchGoods[$id]['goods_name_'.strtolower(substr(self::$language,
                                0, 2))]."*".count(explode(",", $item['goods_ids'])) : "",
                    ];

                    continue;
                }

                $data[$item['staff_info_id']]['all_price'] += $item['value_th'];
                $data[$item['staff_info_id']]['goods']     .= isset($batchGoods) && isset($batchGoods[$id]) ? $batchGoods[$id]['goods_name_'.strtolower(substr(self::$language,
                        0, 2))]."*".count(explode(",", $item['goods_ids'])) : "";
            }
        }


        return $data;
    }

    /**
     * 导出钱款
     *
     * @param $stores
     *
     */
    public function moneyExport($stores)
    {
        $moneies = [];
        if ($stores) {
            $tmpStores = $stores;
            $staffIds  = array_values((array_unique(array_keys($stores))));

            $storeReceivableBills = StoreReceivableBillDetailModel::find([
                'columns'    => 'receivable_amount, staff_info_id, store_id',
                'conditions' => ' state in(0,3) and staff_info_id in ({staffIds:array}) and store_id in ({storeIds:array}) ',
                'bind'       => [
                    'staffIds' => $staffIds,
                    'storeIds' => array_values(array_unique($stores)),
                ],
            ])->toArray();
            $courierMoneys        = [];
            foreach ($storeReceivableBills as $storeReceivableBill) {
                if (!isset($courierMoneys[$storeReceivableBill['staff_info_id']][$storeReceivableBill['store_id']])) {
                    $courierMoneys[$storeReceivableBill['staff_info_id']][$storeReceivableBill['store_id']] = 0;
                }
                $courierMoneys[$storeReceivableBill['staff_info_id']][$storeReceivableBill['store_id']]
                    += $storeReceivableBill['receivable_amount'];
            }

            $this->getDI()->get('logger')->info('leave_manage_money store_receivable_bill_detail '.json_encode($courierMoneys,
                    JSON_UNESCAPED_UNICODE));

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('lm.staff_info_id,
            lmm.cashier_money,
            lmm.money
            '
            );
            $builder->from(['lm' => LeaveManagerModel::class]);
            $builder->leftJoin(LeaveMoneyManagerModel::class,
                'lm.staff_info_id = lmm.staff_info_id and lm.money_operate_version = lmm.version', 'lmm');
            $builder->inWhere(' lm.staff_info_id ', $staffIds);
            $items        = $builder->getQuery()->execute()->toArray();
            $cashierMoney = [];
            $money        = array_column($items, 'money', 'staff_info_id');
            foreach ($items as $item) {
                if ($item['cashier_money'] !== null && $item['cashier_money'] != -1) {
                    $cashierMoney[$item['staff_info_id']] = $item['cashier_money'];
                    unset($tmpStores[$item['staff_info_id']]);
                }
            }


            $this->getDI()->get('logger')->info('leave_manage_money leave_money_manage '.json_encode($cashierMoney,
                    JSON_UNESCAPED_UNICODE).' tmpStores:'.json_encode($tmpStores, JSON_UNESCAPED_UNICODE));

            if ($tmpStores) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('b.parcel_amount, b.collector_id, b.store_id');
                $builder->from(['b' => StoreRemittanceBillModel::class]);
                $builder->leftJoin(StoreRemittanceRecordModel::class, 'b.parcel_remittance_record_id = r.id', 'r');
                $builder->Where('(b.parcel_state =0 or r.state != 1) and b.store_id in ({storeIds:array}) and b.collector_id in ({staffIds:array})',
                    [
                        'storeIds' => array_values(array_unique($tmpStores)),
                        'staffIds' => array_values(array_unique(array_keys($tmpStores))),
                    ]);
                $items = $builder->getQuery()->execute()->toArray();
                foreach ($items as $item) {
                    if (!isset($cashierMoney[$item['collector_id']][$item['store_id']])) {
                        $cashierMoney[$item['collector_id']][$item['store_id']] = 0;
                    }
                    $cashierMoney[$item['collector_id']][$item['store_id']] += $item['parcel_amount'];
                }

                $this->getDI()->get('logger')->info('leave_manage_money store_remittance_bill '.json_encode($cashierMoney,
                        JSON_UNESCAPED_UNICODE));

                $builder = $this->modelsManager->createBuilder();
                $builder->columns('b.cod_amount, b.collector_id, b.store_id');
                $builder->from(['b' => StoreRemittanceBillModel::class]);
                $builder->leftJoin(StoreRemittanceRecordModel::class, 'b.cod_remittance_record_id = r.id', 'r');
                $builder->Where('(b.cod_state =0 or r.state != 1) and b.store_id in ({storeIds:array}) and b.collector_id in ({staffIds:array})',
                    [
                        'storeIds' => array_values(array_unique($tmpStores)),
                        'staffIds' => array_values(array_unique(array_keys($tmpStores))),
                    ]);
                $items = $builder->getQuery()->execute()->toArray();
                foreach ($items as $item) {
                    if (!isset($cashierMoney[$item['collector_id']][$item['store_id']])) {
                        $cashierMoney[$item['collector_id']][$item['store_id']] = 0;
                    }
                    $cashierMoney[$item['collector_id']][$item['store_id']] += $item['cod_amount'];
                }
            }

            $this->getDI()->get('logger')->info('leave_manage_money store_remittance_bill '.json_encode($cashierMoney,
                    JSON_UNESCAPED_UNICODE));

            foreach ($stores as $staffId => $store) {
                if (!isset($moneies[$staffId])) {
                    $moneies[$staffId] = 0;
                }
                $moneies[$staffId] += isset($money[$staffId]) && is_numeric($money[$staffId]) ? $money[$staffId] : 0;
                $moneies[$staffId] += isset($cashierMoney[$staffId]) && is_numeric($cashierMoney[$staffId]) ? $cashierMoney[$staffId] : 0;
                $moneies[$staffId] += isset($cashierMoney[$staffId][$store]) && is_numeric($cashierMoney[$staffId][$store]) ? $cashierMoney[$staffId][$store] : 0;
                $moneies[$staffId] += isset($courierMoneys[$staffId][$store]) && is_numeric($courierMoneys[$staffId][$store]) ? $courierMoneys[$staffId][$store] : 0;
            }
        }

        return $moneies;
    }

    /**
     * 最后的离职时间
     * @param $staffIds
     * @return array
     */
    public function lastResign($staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }

        $resignIds = StaffResignModel::find([
            'columns'    => 'max(resign_id) as id, submitter_id',
            'conditions' => 'submitter_id in ({staffIds:array}) and status in ({status:array})',
            'bind'       => [
                'staffIds' => array_values($staffIds),
                'status'   => [
                    ApprovalEnums::APPROVAL_STATUS_APPROVAL,
                    ApprovalEnums::APPROVAL_STATUS_TIMEOUT,
                ],
            ],
            'group'      => 'submitter_id',
        ])->toArray();
        if (empty($resignIds)) {
            return [];
        }
        $resignIds = array_column($resignIds, 'id');

        $createds = StaffResignModel::find([
            'columns'    => "submitter_id, convert_tz(created_at, '+00:00', '{$this->timeZone}') as created_at",
            'conditions' => ' resign_id in ({resign_id:array})',
            'bind'       => [
                'resign_id' => $resignIds,
            ],
        ])->toArray();

        return array_column($createds, 'created_at', 'submitter_id');
    }


    /**
     * 公司解约个人代理
     * @param array $staffIds
     * @return array
     */
    public function companyTerminationContractInfo(array $staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $list = CompanyTerminationContractModel::find([
            'columns'    => 'max(id) as id, staff_info_id',
            'conditions' => 'staff_info_id in ({staffIds:array}) and status in ({status:array})',
            'bind'       => [
                'staffIds' => array_values($staffIds),
                'status'   => [
                    ApprovalEnums::APPROVAL_STATUS_APPROVAL,
                    ApprovalEnums::APPROVAL_STATUS_TIMEOUT,
                ],
            ],
            'group'      => 'staff_info_id',
        ])->toArray();
        if (empty($list)) {
            return [];
        }
        $ids = array_column($list, 'id');

        $result = CompanyTerminationContractModel::find([
            'columns'    => "staff_info_id,termination_type,reason, remark",
            'conditions' => ' id in ({ids:array})',
            'bind'       => [
                'ids' => $ids,
            ],
        ])->toArray();

        return array_column($result, null, 'staff_info_id');
    }


    /**
     * 社保离职日期
     * @param array $staffIds
     * @return array
     */
    public function getStaffSocialSecurityLeaveDate(array $staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $staff_items = HrStaffItemsModel::find([
            'conditions' => "staff_info_id in ({staff_ids:array}) and item = 'SOCIAL_SECURITY_LEAVE_DATE'",
            'bind'       => [
                'staff_ids' => $staffIds,
            ],
        ])->toArray();
        return !empty($staff_items) ? array_column($staff_items, 'value', 'staff_info_id') : [];
    }



    public function getLeaveInfo($staff_info_id)
    {
        $res = LeaveManagerModel::findFirst(
            [
                "conditions" => "staff_info_id = :staff_info_id:",
                "bind"       => [
                    "staff_info_id" => $staff_info_id,
                ],
            ]
        );
        if ($res) {
            $res               = $res->toArray();
            $staff_info_id_res = HrStaffInfoModel::findFirst(
                [
                    "conditions" => "staff_info_id = :staff_info_id:",
                    "bind"       => [
                        "staff_info_id" => $staff_info_id,
                    ],
                ]
            );
            $staff_info_id_res = $staff_info_id_res ? $staff_info_id_res->toArray() : [];
            $res["leave_date"] = $staff_info_id_res["leave_date"] ?? $res["leave_date"];
            $res["leave_source"] = intval($staff_info_id_res["leave_source"]);
            return $res;
        } else {
            return [];
        }
    }

    /**
     * 更新leave_manager
     * @param $leave_id
     * @param $data
     * @return mixed
     */

    protected function updateLeaveInfo($leave_id, $data)
    {
        return $this->getDI()->get("db_backyard")->updateAsDict("leave_manager", $data,
            ['conditions' => "id = {$leave_id}"]);
    }

    /**
     * 获取员工审批的离职详情
     * @param $staffInfoId
     * @param  $resign_id
     * @return array
     * @throws ValidationException
     */
    public function getStaffResignApprovalDetail($staffInfoId,  $resign_id = 0): array
    {
        $staffInfoService = new StaffInfoService();
        //获取申请离职员工离职装态和申请离职来源等信息
        $staff_info = $staffInfoService->getStaffInfoByIdv4($staffInfoId, 'leave_source');
        if (empty($staff_info)) {
            throw new ValidationException("staff_info_id error:");
        }
        if (empty($resign_id) && !in_array($staff_info['leave_source'],[self::LEAVE_SOURCE_BACKYARD,self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT])) {
            throw new ValidationException("leave_source error");
        }
        $staff_info['leave_source'] = self::LEAVE_SOURCE_BACKYARD;
        if (!empty($resign_id)) {
            $resign_info = $this->get_staff_resign_by_id($resign_id, 'resign_id, created_at, hire_type');
        }
        if ($info = $this->getCompanyTerminationContract($staffInfoId)) {
            $staff_info['leave_source'] = self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT;
        }

        switch ($staff_info['leave_source']) {
            case self::LEAVE_SOURCE_BACKYARD:
                if (empty($resign_info)) {
                    $resign_info = $this->get_staff_lasted_resign($staffInfoId, 'resign_id, created_at, hire_type');
                }
                $type   = in_array($resign_info['hire_type'],
                    HrStaffInfoModel::$agentTypeTogether) ? ApprovalEnums::APPROVAL_TYPE_CANCEL_CONTRACT : ApprovalEnums::APPROVAL_TYPE_RN;
                $params = [
                    'id'           => 'resign_' . $resign_info['resign_id'],
                    'staff_id'     => $staffInfoId,
                    'type'         => $type,
                    'date_created' => $resign_info['created_at'] ?? '',//申请提交时间
                    'version'      => AuditApprovalModel::VERSION_4,
                ];
                $result = $this->getAuditDetail($params);
                break;
            case self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT:
                $params = [
                    'id'           => 'company_' . $info['id'],
                    'staff_id'     => $staffInfoId,
                    'type'         => ApprovalEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT,
                    'date_created' => $info['created_at'] ?? '',//申请提交时间
                    'version'      => AuditApprovalModel::VERSION_4,
                ];
                $result = $this->getAuditDetail($params);
                break;
        }
        return $result ?? [];
    }

    /**
     * @throws ValidationException
     */
    protected function getAuditDetail($params)
    {
        $client = new ApiClient("by", "", "getDetailForHcm", self::$language);
        $client->setParams([$params]);
        $res = $client->execute();
        if (isset($res['error'])) {
            throw new ValidationException($res['error']['message']);
        }
        unset($res['data']['extend'], $res['data']['money_process_info'], $res['data']['confirm']);
        return $res['data'];
    }


    /**
     * 获取员工最新的一条离职申请信息
     * @param $staff_id
     */
    public function get_staff_lasted_resign($staffId, $filed = '*')
    {
        $staffId = intval($staffId) > 0 ? intval($staffId) : 0;
        if (empty($staffId)) {
            return [];
        }
        $staff_resign_table = StaffResignModel::class;
        $resignInfo = $this->modelsManager->executeQuery(
            "select {$filed} from {$staff_resign_table} where submitter_id = :submitter_id: order by created_at desc limit 1",
            ['submitter_id' => $staffId])->toArray();

        return !empty($resignInfo) ? $resignInfo[0] : [];

        return $resignInfo;
    }

    public function get_staff_resign_by_id($resign_id, $filed = '*') {
        $staffId = intval($resign_id) > 0 ? intval($resign_id) : 0;
        if (empty($staffId)) {
            return [];
        }

        $staff_resign_table = StaffResignModel::class;
        $resignInfo = $this->modelsManager->executeQuery(
            "select {$filed} from {$staff_resign_table} where resign_id = :resign_id:",
            ['resign_id' => $resign_id])->toArray();

        return !empty($resignInfo) ? $resignInfo[0] : [];
    }

    public function leavePdf($staffId)
    {
        error_reporting(E_ALL & ~E_NOTICE);
        $pdo  = $this->getDI()->get("db_rby");
        $stmt = $pdo->prepare("
        --
            select 
                * 
            from 
                hr_staff_info hsi 
            left join 
                hr_job_title  hjt 
            on 
                hsi.job_title = hjt.id 
            where 
                hsi.staff_info_id = :staff_info_id");
        $stmt->bindValue("staff_info_id", $staffId, \PDO::PARAM_INT);
        $stmt->execute();
        $staffInfo   = $stmt->fetch(\PDO::FETCH_ASSOC);
        $name        = $hireDate = $leaveDate = $jobTitle = $department = $nodeDepartment = $lastWordDate = $remark = $staffSig = $sigDatetime = $shangji = $shangjidate = $shangjishangji = $shangjishangjidate = '';
        $countryCode = strtolower(env('country_code', 'th'));
        if ($countryCode == 'th') {
            $formartDate = "d-m-Y";
            $diffYear    = 543;
        } else {
            $formartDate = "Y-m-d";
            $diffYear    = 0;
        }
        if ($staffInfo) {
            $name     = $staffInfo['name'];
            $hireDate = $staffInfo['hire_date'] ? date($formartDate,
                    strtotime($staffInfo['hire_date']." + ".$diffYear." years"))." (วัน-เดือน-ปี)" : "";
            $jobTitle = $staffInfo['job_name'];

            $departments    = $pdo->query("select * from sys_department where id in (".$staffInfo['sys_department_id'].", ".$staffInfo['node_department_id']." )")->fetchAll(\PDO::FETCH_ASSOC);
            $departments    = array_column($departments, null, 'id');
            $department     = isset($departments[$staffInfo['sys_department_id']]) ? $departments[$staffInfo['sys_department_id']]['name'] : '';
            $nodeDepartment = isset($departments[$staffInfo['node_department_id']]) ? $departments[$staffInfo['node_department_id']]['name'] : '';

            $ext = "(วัน-เดือน-ปี)";
            if (!empty($staffInfo['leave_date'])) {
                $leaveDate = date($formartDate, strtotime($staffInfo['leave_date']." + ".$diffYear." years"));
                $leaveDate .= $ext;
            }

            $pdo        = $this->getDI()->get("db_backyard");
            $resignInfo = $pdo->query("select * from staff_resign where submitter_id = ".$staffId." order by created_at desc ")->fetch(\PDO::FETCH_ASSOC);
            if ($staffInfo['leave_source'] == self::LEAVE_SOURCE_BACKYARD) {
                // by 申请来源

                $lastWordDate = $resignInfo['last_work_date'] ? date($formartDate,
                        strtotime($resignInfo['last_work_date']."+ ".$diffYear." years"))." (วัน-เดือน-ปี)" : "";
                //$remark = $resignInfo['remark'];
                if (empty($resignInfo['source'])) {
                    $remark = $resignInfo['remark'];
                } else {
                    //批量导入的，用reason
                    $key = $this->getReasonKeyByCode($staffInfo['leave_reason']);
                    $remark = self::$t->_($key);
                }
            } else {
                if (!empty($staffInfo['leave_date'])) {
                    $lastWordDate = date($formartDate,
                            strtotime($staffInfo['leave_date']." + ".$diffYear." years -1 days")).$ext;
                }
            }

            $attachment =
                $resignInfo ?
                    $pdo->query("select `object_key`, CONVERT_TZ(created_at, '+00:00', '{$this->timeZone}') as created_at  from sys_attachment where oss_bucket_type=\"RESIGN_AUDIT\" and oss_bucket_key={$resignInfo['resign_id']} order by created_at asc  limit 3")->fetchAll(\PDO::FETCH_ASSOC) : [];
            if ($attachment && isset($attachment[0])) {
                $staffSig    = env("img_prefix",
                        "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/").$attachment[0]['object_key'];
                $sigDatetime = $attachment[0]['created_at'] ? date($formartDate,
                        strtotime($attachment[0]['created_at']." + ".$diffYear." years"))." (วัน-เดือน-ปี)" : "";
            }
            if ($attachment && isset($attachment[1])) {
                $shangji     = env("img_prefix",
                        "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/").$attachment[1]['object_key'];
                $shangjidate = $attachment[1]['created_at'] ? date($formartDate,
                        strtotime($attachment[1]['created_at']." + ".$diffYear." years"))." (วัน-เดือน-ปี)" : "";
            }
            if ($attachment && isset($attachment[2])) {
                $shangjishangji     = env("img_prefix",
                        "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/").$attachment[2]['object_key'];
                $shangjishangjidate = $attachment[2]['created_at'] ? date($formartDate,
                        strtotime($attachment[2]['created_at']." + ".$diffYear." years"))." (วัน-เดือน-ปี)" : "";
            }
            if ($staffInfo['leave_source'] != self::LEAVE_SOURCE_BACKYARD && !empty($staffInfo['leave_reason'])) {
                //批量导入的，用reason
                $key = $this->getReasonKeyByCode($staffInfo['leave_reason']);
                $remark = self::$t->_($key);
            }

            $companyConfigInfo = $this->getCompanyConfigInfo($staffInfo);
        }

        $data['name'] = $name;
        $data['emloyee_no'] = $staffId;
        $data['employment_date'] = $hireDate;
        $data['title'] = $jobTitle;
        $data['divition'] = $department;
        $data['department'] = $nodeDepartment;
        $data['leave_date'] = $leaveDate;
        $data['last_leave_date'] = $lastWordDate;
        $data['leave_reason'] = $remark;
        $pdf_img_data = [];
        if ($staffSig && $sigDatetime) {
            $pdf_img_data[] = ['name' => 'staff_name', 'url' => $staffSig];
            $data['book_date'] = $sigDatetime;
        } else {
            $data['book_date'] = '';
        }

        if ($shangji && $shangjidate) {
            $pdf_img_data[] = ['name' => 'shangji', 'url' => $shangji];
            $data['shangjidate'] = $shangjidate;
        } else {
            $data['shangjidate'] = '';
        }

        if ($shangjishangji && $shangjishangjidate) {
            $pdf_img_data[] = ['name' => 'shangjishangji', 'url' => $shangjishangji];
            $data['shangjishangjidate'] = $shangjishangjidate;
        } else {
            $data['shangjishangjidate'] = '';
        }


        $temp_url = $this->getLeaveNoticePdfTemp();

        $data['company_logo_url_base64'] = $companyConfigInfo['company_logo_url_base64'] ?? '';

        $res = (new FormPdfServer())->getInstance()->generatePdf($temp_url, $data, $pdf_img_data);
        if (isset($res['object_url'])) {
            return [
                'url' => $res['object_url'],
            ];
        }
    }

    public function getCompanyConfigInfo($staffInfo)
    {
        $certificateService = new CertificateService();
        $companyId = $certificateService->getCompanyId($staffInfo['node_department_id'], $staffInfo['nationality']);

        return $certificateService->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);
    }

    /**
     * 获取离职通知书模板
     * @return mixed
     * @throws \Exception
     */
    public function getLeaveNoticePdfTemp()
    {
        $tmpPath = APP_PATH . '/views/leaveManager/leave_notice_pdf.ftl';
        return $this->getPdfTemp($tmpPath);
    }

    /**
     * 根据员工状态判断该离职申请是否可修改离职日期
     * @param $staff_info -- 员工基本信息（状态、入职日期等）
     * @param $edit_leave_date -- 面修改填写的离职日期
     * @throws ValidationException
     */
    public function isCanEditLeaveDate($staff_info, $edit_leave_date)
    {
        $today = date('Y-m-d');//今天
        //在职状态 不可修改离职日期（在职state == 1 包含在职和待离职两种）
        if ($staff_info['state'] == 1 && $staff_info['wait_leave_state'] == 0) {
            throw new ValidationException(self::$t->_('on_job_not_allowed'));
        }

        //"待离职"状态 可选日期范围：入职日期——未来任意日期（不早于入职日期即可）
        if (($staff_info['state'] == 1 && $staff_info['wait_leave_state'] == 1 || $staff_info['state'] == 2) && $edit_leave_date < $staff_info['hire_date']) {
            throw new ValidationException(self::$t->_('leave_date_early_hire_date'));
        }
        //"离职"状态 可选日期范围：入职日期——今天
        if ($staff_info['state'] == 2 && $edit_leave_date > $today) {
            throw new ValidationException(self::$t->_('leave_date_greater_than_today'));
        }
    }


    /**
     * 修改员工离职状态和离职日期
     * @param $userid -- 操作人（登录用户）ID
     * @param $staff_info -- 员工基本信息（ID，状态等）
     * @param $edit_leave_date -- 员工离职日期
     * @return bool
     */

    public function editLeaveDate($userid, $staff_info, $edit_leave_date)
    {
        $today = date('Y-m-d');//今天
        $state = 2;                                                   //1:在职（包含待离职），2：离职
        //"待离职"状态 :“离职日期”>=今天 则改为 待离职
        if ($staff_info['state'] == 1 && $staff_info['wait_leave_state'] == 1 && $edit_leave_date >= $today) {
            $state = 1;
        }
        try {
            $client = new ApiClient("hris", "", "update_leave_date");
            //提交申请就同步数据到FBI
            $postData = [
                'staff_info_id' => $staff_info['staff_info_id'], //员工ID
                'leave_date'    => $edit_leave_date,             //要修改成的离职日期
                'state'         => $state,                       //离职日期修改后状态
                'operater'      => $userid,                       //操作人
            ];
            $client->setParams([$postData]);
            $res = $client->execute();
            if (isset($res['code']) && $res['code'] == 1) {
                $this->logger->info("flash员工离职管理-修改离职日期同步hris成功：".json_encode([
                        'req_param'  => $postData,
                        'ret_result' => $res,
                    ]));
                return true;
            } else {
                $this->logger->error("flash员工离职管理-修改离职日期同步hris失败：".json_encode([
                        'req_param'  => $postData,
                        'ret_result' => $res,
                    ]));
                return false;
            }
        } catch (\Exception $e) {
            $this->logger->error("flash员工离职管理-修改离职日期异常：".$e->getMessage());
            return false;
        }
    }


    /**
     * 修改离职管理表离职日期
     * @param $staff_info -- 员工信息
     * @param $leave_date -- 离职日期
     * @param $before_short_notice -- 修改前 short notice
     * @param $short_notice -- short notice
     * @return bool 是否修改成功
     * @throws ValidationException
     */
    public function edit_leave_manager_date($staff_info, $user_info,$before_leave_date, $before_short_notice,$leave_date,$short_notice): bool
    {
        $is_leave_date_change = $before_leave_date != $leave_date;
        $is_short_notice_change = $before_short_notice != $short_notice;
        $update_field = [];
        $bind['staff_info_id']= $staff_info['staff_info_id'];
        if ($is_leave_date_change) {
            $update_field[] = ' leave_date=:leave_date ';
            $bind['leave_date'] = $leave_date;
        }
        if (isCountry('MY') && $is_short_notice_change) {
            if ($before_short_notice < $short_notice) {
                throw new ValidationException(self::$t->_('short_notice_notice_001'));//Short notice只能变更为小值
            }
            $update_field[]       = ' edit_short_notice =:short_notice ';
            $bind['short_notice'] = $short_notice;
        }

        if (empty($update_field)) {
            throw new ValidationException(self::$t->_('data_not_change'));
        }

        if ($is_leave_date_change) {
            $update_staff = $this->editLeaveDate($user_info['id'], $staff_info, $leave_date);
            if (empty($update_staff)) {
                throw new ValidationException(self::$t->_('server_error'));
            }
        }
        $update_field_str = implode(',',$update_field);
        //离职资产管理表离职日期修改
        $sql  = "update leave_manager  set $update_field_str where staff_info_id=:staff_info_id";
        $res  = $this->_executeSql($sql, 'db_backyard', $bind);

        if($is_leave_date_change){
            //撤销写入log 找出最新一条记录更新撤销原因
            $log_detail = LeaveManageLogModel::findFirst([
                'conditions'=>'staff_info_id = :staff_info_id:',
                'bind'=>[
                    'staff_info_id' => $staff_info['staff_info_id'],
                ],
                'order' => 'id desc',
            ]);
            if($log_detail) {
                $log_detail->leave_date = $leave_date;
                $log_detail->save();
            }
        }
        if (isCountry('MY') && $is_short_notice_change) {
            //记录变更日志
            $before_data = ['short_notice' => $before_short_notice];
            $after_data  = ['short_notice' => $short_notice];
            (new StaffUpdaterService())->saveOperateLogs($user_info['id'], $staff_info['staff_info_id'], $before_data,
                $after_data);
        }

        return $res ;
    }

    /**
     *
     * 后台 离职管理添加 编辑离职时间功能
     * 影响 hold添加逻辑
     * 记录 编辑历史记录
     * @param $operateId -- 操作人ID
     * @param $staffId -- 员工ID
     * @param $leaveDate  -- 离职日期
     */
    public function addLeaveDataByAdm($operateId, $staffId, $leaveDate)
    {
        try {
            $dbBack = $this->getDI()->get("db_backyard");
            $sql    = "-- 取最后员工最后提交的离职申请信息
            select 
                * 
            from 
                `staff_resign`  
            WHERE 
                `resign_id`  in 
                        (select  MAX(resign_id ) as resign_id from `staff_resign`  GROUP BY `submitter_id`) 
                      and submitter_id = :submitterId ";

            $result = $dbBack->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC, [
                "submitterId" => $staffId,
            ], [
                "submitterId" => \Phalcon\Db\Column::BIND_PARAM_INT,
            ]);


            $bi_params = [
                'staff_info_id' => $staffId,
                //员工id
                'type'          => '',
                //hold类型(1.工资hold,2.提成hold,同时就传’1,2’)
                'hold_reason'   => 'incomplete_resignation_procedures',
                //hold原因(BY申请离职)
                'hold_remark'   => $result && isset($result['reason']) ? $result['reason'] : '',
                //hold备注
                'hold_time'     => gmdate('Y-m-d H:i:s', time() + ($this->timeOffset) * 3600),
                //hold时间操作时间
                'hold_source'   => 5,
                //来源->离职管理
            ];

            $this->getDI()->get("logger")->info("leave_date_operate_log ".json_encode($result, JSON_UNESCAPED_UNICODE));
            $CurrentTypes = $this->getLastApprovelHoldType($result);
            $this->getDI()->get("logger")->info("leave_date_operate_log ".json_encode($CurrentTypes,
                    JSON_UNESCAPED_UNICODE));

            // 如果离职日期在下个月5号之前  同步工资hold
            // 如果离职日期在下个月15号之前 同步提成hold
            $leaveDate      = date("Y-m-d", strtotime($leaveDate));
            $nextMonthType1 = date("Y-m-05", strtotime("+1 month"));
            $nextMonthType2 = date("Y-m-15", strtotime("+1 month"));
            $types          = [];
            if ($leaveDate <= $nextMonthType1) {
                $types[] = 1;
                $types[] = 2;
            } else {
                if ($leaveDate <= $nextMonthType2) {
                    $types[] = 2;
                }
            }
            $afterTypes = array_diff($types, $CurrentTypes);

            $bi_params['type'] = $afterTypes ? implode(",", $afterTypes) : '';

            if ($bi_params['type']) {
                (new HoldManageService())->synchronizeHoldStaff($bi_params);
            }

            $this->getDI()->get("logger")->info("leave_date_operate_log ".json_encode(func_get_args())." ".json_encode($bi_params,
                    JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            $this->getDI()->get("logger")->error("leave_date_operate_log ".json_encode(func_get_args())." error:".$e->getTraceAsString());
            return false;
        }
        return true;
    }

    /**
     * 获取最后一条离职信息数据 判断hold type类型
     *
     * @param $approval
     *
     */
    private function getLastApprovelHoldType($approval)
    {
        $type                   = [];
        $approvalDate           = date("Y-m-d", strtotime($approval['leave_date']));
        $approvalNextMonthType1 = date("Y-m-05", strtotime(" +1 month"));
        $approvalNextMonthType2 = date("Y-m-15", strtotime(" +1 month"));
        if ($approvalDate <= $approvalNextMonthType1) {
            $type[] = 1;
            $type[] = 2;
        } else {
            if ($approvalDate <= $approvalNextMonthType2) {
                $type[] = 2;
            }
        }

        return $type;
    }

    /**
     * 修改离职申请表 离职日期、最后工作日
     * @param $staff_info_id  -- 工号
     * @param $leave_date -- 离职日期
     * @return bool 是否修改成功
     */
    public function edit_resign_leave_date($staff_info_id, $leave_date)
    {
        $leave_resign = $this->get_staff_lasted_resign($staff_info_id, 'resign_id');
        if ($leave_resign) {
            $last_work_date = date('Y-m-d', strtotime('-1 day', strtotime($leave_date)));
            //离职申请表离职日期修改
            $sql               = "update staff_resign set leave_date=:leave_date,last_work_date=:last_work_date where resign_id=:resign_id";
            $bind              = [
                'leave_date'     => $leave_date,
                'last_work_date' => $last_work_date,
                'resign_id'      => $leave_resign['resign_id'],
            ];
            $staff_resigin_res = $this->_executeSql($sql, 'db_backyard', $bind);
            return (bool)$staff_resigin_res;
        }
        return false;
    }

    /**
     * 恢复在职验证
     * @param $staffInfo
     * @throws ValidationException
     */
    public function changeToOnJobValidation($staffInfo)
    {
        $limit_job_title = ['16'];
        if (isCountry('TH')) {
            $limit_job_title = ['16', '1290'];
        }

        //网点主管只能一个在职
        if (in_array($staffInfo['job_title'], $limit_job_title )&& $staffInfo['is_sub_staff'] == 0) {
            $store = SysStoreModel::count(
                [
                    'conditions' => 'id = :id: and category in ({category:array}) ',
                    'bind'       => [
                        'id' => $staffInfo['sys_store_id'],
                        'category'=>[Enums::STORE_TYPE_SP,Enums::STORE_TYPE_DC,Enums::STORE_TYPE_BDC,Enums::STORE_TYPE_CDC,Enums::STORE_TYPE_PDC],
                    ],
                ]
            );

            if ($store) {
                $staffs = HrStaffInfoModel::count(
                    [
                        'conditions' => 'staff_info_id != :staff_info_id: and job_title in ({job_title:array}) and sys_store_id  = :sys_store_id: and state = 1 and is_sub_staff = 0 and formal in (1,4) ',
                        'bind'       => [
                            'staff_info_id' => $staffInfo['staff_info_id'],
                            'sys_store_id'  => $staffInfo['sys_store_id'],
                            'job_title'     => $limit_job_title,
                        ],
                    ]
                );
                if ($staffs) {
                    throw new ValidationException(self::$t->_('store_job_title_limit'));
                }
            }
        }
    }

    /**
     * 撤销离职
     * 后续优化这块 需要拆分成小方法
     * @param $params
     * @param $userInfo
     * @return bool|string
     * @throws ValidationException
     * @throws BusinessException
     */

    public function cancel($params, $userInfo)
    {
        $reason = $params['reason'];
        $from = $params['from'] ?? '';
        $is_cancel_staff_resign = $params['is_cancel_staff_resign'] ?? 1;

        if ($from != 'hris_rpc'){
            $flag = $this->ifCancel($userInfo['position_category']);
            if (!$flag) {
                throw new BusinessException(self::$t->_('no_permission'));
            }
        }
        $staffInfo = $this->getStaffInfo($params['staff_info_id']);
        if (empty($staffInfo)) {
            throw new BusinessException(self::$t->_('staff_id_not_found'));
        }
        //恢复在职的公共验证
        $this->changeToOnJobValidation($staffInfo);

        if ($from != 'hris_rpc' && $staffInfo['state'] == 1 && $staffInfo['wait_leave_state'] == 0) {
            throw new BusinessException(self::$t->_('operation_data_does_no_exist'));
        }

        $resign = $this->getResign($params['staff_info_id']);
        if ($is_cancel_staff_resign == 1){
            $today = gmdate('Y-m-d', time() + ($this->timeOffset) * 3600);
            $is_show_cancel_staff_resign = 0;
            if (!empty($resign) && $resign['status'] == 2 && $resign['leave_date'] > $today){
                $is_show_cancel_staff_resign = 1;
            }
            //$resign = 0;
            if (!empty($resign) && in_array($resign['status'],[self::APPROVAL_STATUS_WAIT,self::APPROVAL_STATUS_APPROVED,self::APPROVAL_STATUS_TIME_OUT]) && $is_show_cancel_staff_resign) {
                //有审批流
                if ($resign['source'] == 0) {
                    $data                  = [];
                    $data['staff_info_id'] = $params['staff_info_id'];
                    $data['resign_id']     = $resign['resign_id'];
                    $data['reason']        = $params['reason'];
                    $data['operator_id']   = $userInfo['id'];
                    $data['operator_name'] = $userInfo['name'];
                    $data['type']          = in_array($resign['hire_type'],HrStaffInfoModel::$agentTypeTogether) ? ApprovalEnums::APPROVAL_TYPE_CANCEL_CONTRACT : ApprovalEnums::APPROVAL_TYPE_RN;
                    $res                   = $this->cancelResign($data);
                    if (!empty($res) && $res['code'] == 1) {
                    } else {
                        $this->logger->error(['data' => $data, 'res' => $res]);
                        throw new BusinessException($res['msg']);
                    }
                }
                $this->updateResign($resign['resign_id'],
                    ["status" => self::APPROVAL_STATUS_REVOKED, "cancel_reason" => $params['reason']]);
            }
        }
        //公司解约个人代理

        if (in_array($staffInfo['hire_type'],HrStaffInfoModel::$agentTypeTogether)) {
            $companyTermination = $this->getCompanyTerminationContract($params['staff_info_id']);
            if(!empty($companyTermination) && in_array($companyTermination['status'],
                    [self::APPROVAL_STATUS_WAIT, self::APPROVAL_STATUS_APPROVED, self::APPROVAL_STATUS_TIME_OUT])){
                $data                  = [];
                $data['staff_info_id'] = $params['staff_info_id'];
                $data['id']            = $companyTermination['id'];
                $data['reason']        = $params['reason'];
                $data['operator_id']   = $userInfo['id'];
                $data['operator_name'] = $userInfo['name'];
                $data['type']          = ApprovalEnums::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT;
                $res                   = $this->cancelResign($data);
                if (!empty($res) && $res['code'] == 1) {
                } else {
                    $this->logger->error(['data' => $data, 'res' => $res]);
                    throw new BusinessException($res['msg']);
                }
            }
        }


        // bi 取消离职消息已读
        $msgAssetInfo = MsgAssetModel::find(
            [
                'conditions' => 'staff_id = :staff_id:  and type = :type:',
                'bind'       => ['staff_id' => $params['staff_info_id'], 'type' => '1'],
            ]
        )->toArray();
        if ($msgAssetInfo) {
            foreach ($msgAssetInfo as $info) {
                $rs = $this->has_read_operation($info['msg_id']);
            }
        }
        //// bi 取消离职消息已读
        $now_time = gmdate("Y-m-d H:i:s");

        //没有离职信息，需要添加，用ETC时间
        //撤销后 原逻辑是改成已处理，新逻辑改成未处理状态
        $data                        = [];
        $leaveInfo       = $this->getLeaveInfo($params['staff_info_id']);
        if ($is_cancel_staff_resign == 1){
            $data['staff_info_id']       = $params['staff_info_id'];
            $data['created_at']          = $now_time;
            $data['assets_remand_state'] = self::ASSETS_STATE_UNPROCESSED;
            $data['assets_operator_id']  = $userInfo['id'];
            $data['assets_operate_time'] = $data['created_at'];

            $data['money_remand_state'] = self::ASSETS_STATE_UNPROCESSED;
            $data['money_operator_id']  = $userInfo['id'];
            $data['money_operate_time'] = $data['created_at'];
            $data['approval_time']      = $data['created_at'];
            $data['approval_status']    = self::APPROVAL_STATUS_REVOKED;
            $data['leave_date']         = '0000-00-00 00:00:00';
            //撤销不用管主管是否处理
            //$data['is_has_superior'] = 1;
            //$data['superior_id'] = $userInfo['id'];
            //$data['superior_operate_time'] = $data['created_at'];

            $is_has_superior = 0;
            //直接改，没有插入日志
            if (empty($leaveInfo)) {
                $temp = $this->isNeedDealSuperior($staffInfo, $is_has_superior);
                if ($temp) {
                    $data['is_has_superior']       = 1;
                    $data['superior_id']           = $userInfo['id'];
                    $data['superior_operate_time'] = $data['created_at'];
                }
                $flag = $this->addLeaveInfo($data);
            } else {
                $is_has_superior = intval($leaveInfo['is_has_superior']);
                $temp            = $this->isNeedDealSuperior($staffInfo, $is_has_superior);
                if ($temp) {
                    $data['is_has_superior']       = 1;
                    $data['superior_id']           = $userInfo['id'];
                    $data['superior_operate_time'] = $data['created_at'];
                }
                $flag = $this->updateLeaveInfo($leaveInfo['id'], $data);
            }
            if (!$flag) {
                return 'add or update lm error';
            }
        }

        $leave_source = $staffInfo['leave_source'];
        //如果其他来源，不管hold
        if (!($leave_source == -1 || empty($leave_source))) {
            // 处理释放hold逻辑
            $flag = $this->hold_staff_manage($is_cancel_staff_resign,$userInfo,$leaveInfo,$staffInfo,$resign);
            if (empty($flag)){
                return 'hold_staff_manage error';
            }

            //勾选撤销by离职申请才处理离职手续不全的hold
            if ($is_cancel_staff_resign == 1) {
                //释放离职手续不全的hold
                $releaseHoldParams   = [
                    'staff_info_id' => $params['staff_info_id'],
                    'hold_source'   => 0, //全部hold 来源都需要处理为已处理
                    'hold_reason'   => 'incomplete_resignation_procedures',
                    'handle_hold'   => HoldStaffManageModel::IS_HANDLE_HOLD_YES,
                ];
                (new HoldManageService())->syncReleaseHoldStaff($releaseHoldParams);
            }
        }

        if (isCountry('My')) {
            $this->updateStaffItem($params['staff_info_id'], 'SHORT_NOTICE', '');
        }

        //撤销写入log 找出最新一条记录更新撤销原因
        $log_detail = LeaveManageLogModel::findFirst([
            'conditions'=>'staff_info_id = :staff_info_id:',
            'bind'=>[
                'staff_info_id' => $params['staff_info_id'],
            ],
            'order' => 'id desc',
        ]);
        if($log_detail) {
            $log_detail->approval_status = self::APPROVAL_STATUS_REVOKED;
            $log_detail->cancel_reason = $reason;
            $log_detail->is_cancel_staff_resign = $is_cancel_staff_resign;
//            $log_detail->operate_id = $userInfo['id'];
//            $log_detail->operate_name = $userInfo['name'];
            $log_detail->save();
        }
        if ($is_cancel_staff_resign == 1){
            $data = [
                "staff_info_id"    => $params['staff_info_id'],
                "type"             => 1,
                'leave_date'       => null,
                'operaterId'       => $userInfo['id'],
                'wait_leave_state' => 0,
                'cancel_reason'    => $reason,

            ];
        }else{
            $data = [
                "staff_info_id"    => $params['staff_info_id'],
                "type"             => 1,
                'operaterId'       => $userInfo['id'],
                'wait_leave_state' => 1,
            ];
        }

        if ($from != 'hris_rpc'){
            if ($is_cancel_staff_resign == 2) {
                $data['leave_date']   = $resign['leave_date'] ?? '';
                $data['leave_source'] = LeaveManagerService::LEAVE_SOURCE_BACKYARD;
                $data['leave_reason'] = $resign['reason'] ?? '';
                $data['leave_type']   = 1;
            }
            $ret  = $this->update_staff_state([$data]);
            if ($is_cancel_staff_resign == 2){
                $this->logger->info([
                    'function' => 'update_staff_info',
                    'message'  => '更新离职来源信息',
                    'params'   => $data,
                    'result'   => $ret,
                ]);
                $updateLeaveInfoData['staff_info_id']       = $params['staff_info_id'];
                $updateLeaveInfoData['leave_date']         = $resign['leave_date'] ?? '';
                $leaveInfo       = $this->getLeaveInfo($params['staff_info_id']);
                if ($leaveInfo){
                    $flag = $this->updateLeaveInfo($leaveInfo['id'], $updateLeaveInfoData);
                    if (!$flag) {
                        return 'add or update lm error';
                    }
                }
            }
        }
        if (isset($ret['code']) && $ret['code'] == 0) {
            foreach ($ret['body'] as $k => $v) {
                if ($v['msg'] == 'ok') {
                } else {
                    return 'update staff error';
                }
            }
        }
        return true;
    }
    
    /**
     * 处理释放hold逻辑
     * @param $is_cancel_staff_resign
     * @param $userInfo
     * @param $leaveInfo
     * @param $staffInfo
     * @return mixed
     */
    public function hold_staff_manage($is_cancel_staff_resign,$userInfo,$leaveInfo,$staffInfo,$resign)
    {
        $this->getDI()->get('logger')->info('hold_staff_manage1 '.json_encode([
                'is_cancel_staff_resign' => $is_cancel_staff_resign,
                'leaveInfo' => $leaveInfo,
                'staffInfo' => $staffInfo,
                'userInfo' => $userInfo,
            ], JSON_UNESCAPED_UNICODE));
        $handle_data                  = [];
        $release_data                  = [];
        $staff_info_id = $staffInfo['staff_info_id'];
        $handle_data['update_at'] = date('Y-m-d H:i:s');
        $handle_data['handle_people'] = $userInfo['id'];// 处理人
        $handle_data['handle_progress'] = 3;//已处理
        $leave_source = $staffInfo['leave_source'];
        if ($is_cancel_staff_resign == 1){
            // 同时撤销BY离职申请
            $today = gmdate('Y-m-d', time() + ($this->timeOffset) * 3600);
            if (!empty($resign) && $staffInfo['state'] == Enums::HRIS_WORKING_STATE_2 && $leaveInfo['leave_source'] != self::LEAVE_SOURCE_BACKYARD && $resign['status'] == Enums::audit_status_2 && !empty($resign['leave_date']) && $resign['leave_date'] > $today){
                $hold_source = [$leave_source,self::LEAVE_SOURCE_BACKYARD];
            }else{
                $hold_source = [$leave_source];
            }
            $hold_arr = HoldStaffManageModel::find([
                'conditions' => ' staff_info_id = :staff_id: and hold_source not in ({hold_source:array}) and type in ({type:array}) and is_delete = 0 and handle_progress!=3',
                'bind' => [
                    'staff_id' => $staff_info_id,
                    'hold_source' => $hold_source,
                    'type' => [HoldStaffManageModel::HOLD_TYPE_WAGES,HoldStaffManageModel::HOLD_TYPE_PERCENTAGE],
                ],
            ])->toArray();
            $hold_arr = array_column($hold_arr,null,'type');
            if (!empty($hold_arr[HoldStaffManageModel::HOLD_TYPE_WAGES])){
                // 有工资未处理的
                $hold_type_wages = true;
            }
            if (!empty($hold_arr[HoldStaffManageModel::HOLD_TYPE_PERCENTAGE])){
                // 有提成未处理的
                $hold_type_percentage = true;
            }
            if (empty($hold_type_wages) || empty($hold_type_percentage)){
                $release_data['release_state'] = 2;//释放状态 2=已释放
                $release_data['release_time']  = date('Y-m-d H:i:s');
                $release_data['release_submit_id'] = $userInfo['id'];
                $params   = [
                    'operator_id'       => $userInfo['id'],
                    'type'              => 2,                       //(1 hold ; 2 释放)
                    'staff_info_id'     => $staff_info_id,//员工id
                    'payment_markup'    => '102',
                    'stop_payment_type' => $staffInfo['stop_payment_type'],
                ];
            }
            $stop_payment_type = '';
            if (empty($hold_type_wages) && empty($hold_type_percentage)){
                $stop_payment_type = '1,2';
            }elseif (empty($hold_type_wages)){
                $stop_payment_type = '1';
            }elseif (empty($hold_type_percentage)){
                $stop_payment_type = '2';
            }
            if (!empty($stop_payment_type)){
                $params['stop_payment_type'] = $stop_payment_type;
                $lang     = self::$language;
                $result = $this->getApiDatass('hris', '', 'staff_hold', $lang, $params);
                $this->getDI()->get('logger')->info('hold_staff_manage3 '.json_encode([
                        'result' => $result,
                        'params' => $params,
                    ], JSON_UNESCAPED_UNICODE));
                // 将Hold来源与离职来源一致的工资/提成的Hold变为已处理，同时把Hold是“BY申请离职”的工资或提成的Hold变为已处理。检查是否还存在其他Hold来源的工资或提成的Hold，如果存在则不释放员工工资或提成的Hold，如果不存在则释放员工工资或提成的Hold
                $flag = $this->getDI()->get("db_backyard")->updateAsDict("hold_staff_manage", $release_data, ["conditions" => "staff_info_id = {$staff_info_id} and is_delete = 0 and type in ({$stop_payment_type})"]);
            }
        }
        $flag = $this->getDI()->get("db_backyard")->updateAsDict("hold_staff_manage", $handle_data, ["conditions" => "staff_info_id = {$staff_info_id} and is_delete = 0 and hold_source =".$leave_source]);
        $this->getDI()->get('logger')->info('hold_staff_manage2 '.json_encode([
                'flag' => $flag,
                'is_cancel_staff_resign' => $is_cancel_staff_resign,
                'release_data' => $release_data,
                'handle_data' => $handle_data,
            ], JSON_UNESCAPED_UNICODE));
        return $flag;
    }


    public function updateStaffItem($staffId, $item, $value)
    {
        $hrStaffItem = HrStaffItemsModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: and item = :item: ',
            'bind'       => ['staff_id' => $staffId, 'item' => $item],
        ]);
        if (!$hrStaffItem) {
            $hrStaffItem                = new HrStaffItemsModel();
            $hrStaffItem->staff_info_id = $staffId;
            $hrStaffItem->item          = $item;
        }
        $hrStaffItem->value = $value;

        return $hrStaffItem->save();
    }


    public function cancelResign($data)
    {
        $client = new ApiClient("by", "", "resign_cancel",self::$language);
        $client->setParams([$data]);
        return $client->execute();
    }

    private function updateResign($resign_id, $data)
    {
        return $this->getDI()->get("db_backyard")->updateAsDict("staff_resign", $data,
            ['conditions' => "resign_id = {$resign_id}"]);
    }

    //设置已读消息
    public function has_read_operation($msg_id)
    {
        $sql_query = "update message_courier  set read_state=1 ,updated_at = updated_at  where id= '{$msg_id}' ";
        return $this->getDI()->get('db_message')->execute($sql_query);
    }

    /**
     * 判断是否需要主管处理=
     * @param $staff_info
     * @param $is_has_superior
     * @return bool
     */
    public function isNeedDealSuperior($staff_info, $is_has_superior)
    {
        if (($staff_info['state'] == 2 && $is_has_superior == 0) || !in_array($staff_info['job_title'], [
                AssetsService::$job_title['bike_courier'],
                AssetsService::$job_title['van_courier'],
                AssetsService::$job_title['shop_officer'],
            ])) {
            // 已离职并且主管处理情况为未处理
            // 包含了 历史数据 与 手动改离职
            //$row['is_has_superior_text'] = "";
            return false;
        }
        return true;
    }

    /**
     * 撤回的时候，如果没有，则添加
     * @param $data
     * @return mixed
     */
    private function addLeaveInfo($data)
    {
        return $this->getDI()->get("db_backyard")->insertAsDict('leave_manager', $data);
    }

    /**
     * 获取接口数据 apg
     * @param string $method
     * @param array $url
     * @param array $params
     * @param string $lang
     * @return mixed|string
     */
    public function getApiDatass($sys, $modules, string $method, $lang, ...$params)
    {
        // 验证数据
        if (empty(trim($method))) {
            return '';
        }

        // 实例化jsonRPC接口
        $client = new ApiClient($sys, $modules, $method, $lang);
        $client->setParams($params);
        $result = $client->execute();
        return $result;
    }

    /**
     * 查询当员工后台的名下钱款
     *
     * @param integer $staffInfoId 员工ID
     * @return array
     */
    public function moneyInfo($staffInfoId)
    {
        $staff_service            = new StaffService();
        $result['staff_info']     = $staff_service->staffInfo($staffInfoId);
        $result['process_state']  = self::ASSETS_STATE_UNPROCESSED;
        $result['process_states'] = self::moneyState();


        $result['unpaid_money'] = '';
        //[1]快递员未回款 、出纳应汇款
        [$courier, $cashier] = $this->arrearsMoney($staffInfoId);
        $result['courier_money'] = bcmul($courier, 100);
        $result['cashier_money'] = bcmul($cashier, 100);

        $result['remark']           = '';
        $result['money_object_key'] = [
        ];

        // 判断是否操作过
        $leaveInfo = $this->getLeaveInfo($staffInfoId);

        // 调用oa 获取 借款未归还金额 备用金未归还金额
        $notReturnAmount = (new ExternalDataService())->getUserOutstandingAmount($staffInfoId,100);

        $result['loan_not_return']  = $notReturnAmount['loan_amount']; // 借款未归还金额
        $result['reserve_fund']     = $notReturnAmount['reserve_fund_amount'];    // 备用金未归还金额

        if ($leaveInfo) {
            $img_prefix              = env('img_prefix',
                'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/');
            $result['process_state'] = isset($leaveInfo['money_remand_state']) && $leaveInfo['money_remand_state'] ? $leaveInfo['money_remand_state'] : self::ASSETS_STATE_UNPROCESSED;

            $leaveMoney = $this->getSysMoney($staffInfoId, $leaveInfo['money_operate_version'] ?? 0);
            if ($leaveMoney) {
                $result['unpaid_money'] = $leaveMoney['money'];
                $result['remark']       = $leaveMoney['remark'];
            }

            $attachMents = json_decode((string)$leaveInfo['money_attachments'], true);
            if ($attachMents) {
                foreach ($attachMents as $k => $attachMent) {
                    $attachMents[$k]['url'] = $attachMent['object_url'];
                }
            }
            $result['money_object_key'] = $attachMents;
        }

        $this->getDI()->get('logger')->info('leave_manage_money arrearsmoney '.json_encode([
                'courier_money' => $result['courier_money'],
                'cashier_money' => $result['cashier_money'],
                'unpaid_money'  => $result['unpaid_money'],
            ], JSON_UNESCAPED_UNICODE));

        $arrears = bcadd($result['courier_money'], $result['cashier_money']);

        $result['all_price'] = bcadd($result['unpaid_money'], $arrears);
        $result['all_price'] = bcadd($result['all_price'], $result['loan_not_return']);

        $this->getDI()->get('logger')->info($result);
        return $result;
    }

    private function assetsState()
    {
        $states = [];
        foreach (self::ASSETS_DESC as $k => $v) {
            if ($k) {
                $states[] = ["key" => $k, "value" => self::$t->_("assets_state_".$k)];
            }
        }
        return $states;
    }

    private function moneyState()
    {
        $states = [];
        foreach (self::MONEY_DESC as $k => $v) {
            if ($k) {
                $states[] = ["key" => $k, "value" => self::$t->_("assets_state_".$k)];
            }
        }
        return $states;
    }

    /**
     * 快递员未回款+出纳应汇款
     * @param $staffId
     * @return array
     */
    public function arrearsMoney($staffId)
    {
        $staff_service = new StaffService();
        $staffInfo     = $staff_service->get_fbi_user_info($staffId);
        $pdo           = $this->getDI()->get('db_fle');
        $courierMoney  = $cashierMoney = 0;
        $sql           = "
       --
       SELECT
            IFNULL(sum(`receivable_amount`)/100,0) as money
       FROM
            `store_receivable_bill_detail`
       WHERE
            `state` in (0,3) and  `staff_info_id` = {$staffId} and `store_id` = '{$staffInfo['organization_id']}';
       ";
        $result        = $pdo->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if ($result) {
            $courierMoney = bcdiv(100 * $result['money'], 100, 2);
        }

        $leaveInfo = $this->getLeaveInfo($staffId);
        if ($leaveInfo && isset($leaveInfo['money_operate_version']) && $leaveInfo['money_operate_version']) {
            $money = $this->getSysMoney($staffId, $leaveInfo['money_operate_version']);
            if ($money['cashier_money'] != '-1') {
                $cashierMoney = bcdiv($money['cashier_money'], 100, 2);
                return [$courierMoney, $cashierMoney];
            }
        }

        $sql    = "
        --
        SELECT 
                IFNULL(sum(b.`parcel_amount`)/100,0) as money  
        FROM 
                `store_remittance_bill` b
        LEFT JOIN 
                `store_remittance_record` r 
                on 
                b.`parcel_remittance_record_id` = r.`id` 
        WHERE 
            (b.`parcel_state`  =0 or r.`state` != 1)and b.`store_id` = '{$staffInfo['organization_id']}' and  b.`collector_id` = {$staffId};
       ";
        $result = $pdo->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if ($result) {
            $cashierMoney = bcdiv(100 * $result['money'], 100, 2);
        }
        $sql    = "
        --
        SELECT 
                IFNULL(sum(b.`cod_amount`)/100,0)   as money 
        FROM 
                `store_remittance_bill` b
        LEFT JOIN 
                `store_remittance_record` r 
                on 
                b.`cod_remittance_record_id` = r.`id` 
        WHERE 
                (b.`cod_state` = 0 or r.`state` !=1 ) and b.`store_id` = '{$staffInfo['organization_id']}' and  b.`collector_id` = {$staffId};
        ";
        $result = $pdo->query($sql)->fetch(\PDO::FETCH_ASSOC);
        if ($result) {
            $cashierMoney = bcadd($cashierMoney, $result['money'], 2);
        }

        return [$courierMoney, $cashierMoney];
    }


    /**
     * 获取后台管理员编辑的钱款信息
     *
     * @param $staffInfoId
     * @param $version
     * @return mixed
     */
    private function getSysMoney($staffInfoId, $version)
    {
        $stmt = $this->getDI()->get("db_rby")->prepare("
        --
            select 
                * 
            from 
                leave_money_manager 
            where 
                staff_info_id = :staff_info_id and version = :version ");
        $stmt->bindValue(":staff_info_id", $staffInfoId, \PDO::PARAM_INT);
        $stmt->bindValue(":version", $version, \PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * fbi后台管理远编辑员工资产数据
     *
     * @param integer $staffInfoId
     * @param array $assets
     * @param array $publicAssets
     * @param array $batchAssets
     * @param integer $state
     * @param string $remark
     * @param array $attachMents
     * @param integer $operatorId
     * @throws \Exception
     */
    public function editAssets(
        $staffInfoId,
        $assets,
        $publicAssets,
        $batchAssets,
        $state,
        $remark,
        $attachMents,
        $operatorId
    ) {
        $leaveInfo = $this->getLeaveInfo($staffInfoId);

        $filterAttachs = [];
        foreach ($attachMents as $attachMent) {
            $filterAttachs[] = [
                'file_name'  => $attachMent['file_name'],
                'object_url' => $attachMent['object_url'],
            ];
        }
        $attachMents = json_encode($attachMents, JSON_UNESCAPED_UNICODE);

        $pdo = $this->getDI()->get("db_backyard");
        $pdo->begin();
        try {
            $version = 1;
            if (!$leaveInfo) {
                if (!$this->goodsTypes($staffInfoId)) {
                    return [false, '处理完成，不能再编辑'];
                }
                //没有管理过
                $stmt        = $pdo->prepare("
                --
                insert into 
                    leave_manager (
                        `staff_info_id`, 
                        `assets_remand_state`, 
                        `assets_operate_version`, 
                        `assets_operator_id`, 
                        `assets_operate_time`, 
                        `assets_operate_remark`, 
                        `assets_attachments`,
                        `money_remand_state`
                        )
                    value (
                        :staff_info_id, 
                        :assets_remand_state, 
                        :assets_operate_version, 
                        :assets_operator_id, 
                        :assets_operate_time, 
                        :assets_operate_remark, 
                        :assets_attachments,
                        :money_remand_state)"
                );
                $operateTime = gmdate("Y-m-d H:i:s");
                $asset_state_unprocessed = self::ASSETS_STATE_UNPROCESSED;
                $stmt->bindParam(":staff_info_id", $staffInfoId, \PDO::PARAM_INT);
                $stmt->bindParam(":assets_remand_state", $state, \PDO::PARAM_INT);
                $stmt->bindParam(":assets_operate_version", $version, \PDO::PARAM_INT);
                $stmt->bindParam(":assets_operator_id", $operatorId, \PDO::PARAM_INT);
                $stmt->bindParam(":assets_operate_time", $operateTime, \PDO::PARAM_STR);
                $stmt->bindParam(":assets_operate_remark", $remark, \PDO::PARAM_STR);
                $stmt->bindParam(":assets_attachments", $attachMents, \PDO::PARAM_STR);
                $stmt->bindParam(":money_remand_state", $asset_state_unprocessed, \PDO::PARAM_INT);
                $stmt->execute();

                $leaveManagerId = $pdo->lastInsertId();
                if (!$leaveManagerId) {
                    throw new \Exception("插入失败，请重试");
                }
            } else {
                //曾经管理过
                $stmt = $pdo->prepare("
                --
                select 
                    * 
                from 
                    leave_manager 
                where 
                    staff_info_id = :staff_info_id for update ");
                $stmt->bindValue('staff_info_id', $staffInfoId, \PDO::PARAM_INT);
                $stmt->execute();
                $leaveInfo = $stmt->fetch();
                if ($leaveInfo['assets_remand_state'] == self::ASSETS_STATE_PROCESSED || !$this->goodsTypes($staffInfoId)) {
                    return [false, '处理完成，不能再编辑'];
                }

                $version = $leaveInfo['assets_operate_version'] + 1;

                $stmt = $pdo->prepare("
                --
                    update 
                        `leave_manager` 
                    set 
                        `assets_remand_state`=:assets_remand_state, 
                        `assets_operate_version`=:assets_operate_version, 
                        `assets_operator_id`=:assets_operator_id, 
                        `assets_operate_time`=:assets_operate_time, 
                        `assets_operate_remark`=:assets_operate_remark, 
                        `assets_attachments`= :assets_attachments  
                    where 
                        `id` = :id");
                $stmt->bindParam(":assets_remand_state", $state, \PDO::PARAM_INT);
                $stmt->bindParam(":assets_operate_version", $version, \PDO::PARAM_INT);
                $stmt->bindParam(":assets_operator_id", $operatorId, \PDO::PARAM_INT);
                $operateTime = gmdate("Y-m-d H:i:s");
                $stmt->bindParam(":assets_operate_time", $operateTime, \PDO::PARAM_STR);
                $stmt->bindParam(":assets_operate_remark", $remark, \PDO::PARAM_STR);
                $stmt->bindParam(":assets_attachments", $attachMents, \PDO::PARAM_STR);
                $stmt->bindParam(":id", $leaveInfo['id'], \PDO::PARAM_INT);
                $stmt->execute();
                if (!$stmt->rowCount()) {
                    throw new \Exception("更新失败，请重试");
                }
            }


            //原逻辑调用 $this->insertAssets;
            $this->batchInsertAssets($assets, $version, $staffInfoId, $state, $operatorId);
            $this->batchInsertAssets($publicAssets, $version, $staffInfoId, $state, $operatorId);

            foreach ($batchAssets as $k => $batchAsset) {
                $batchAssets[$k]['is_batch'] = 1;
            }
            $this->batchInsertAssets($batchAssets, $version, $staffInfoId, $state, $operatorId);
        } catch (\Exception $e) {
            $pdo->rollBack();
            return [false, $e->getMessage()];
        }

        $pdo->commit();
        return [true, ''];
    }


    /**
     * fbi 后台管理员编辑员工钱款
     *
     * @param integer $staffInfoId
     * @param integer $money
     * @param string $cashier_money
     * @param integer $state
     * @param string $remark
     * @param array $attachMents
     * @param integer $operatorId
     * @throws \Exception
     */
    public function editMoney($staffInfoId, $money, $cashier_money, $state, $remark, $attachMents, $operatorId)
    {
        $leaveInfo = $this->getLeaveInfo($staffInfoId);

        $t         = self::$t;
        $moneyInfo = $this->moneyInfo($staffInfoId);
        if ($moneyInfo && isset($moneyInfo['all_price']) && $moneyInfo['all_price'] > 0 && $state == self::ASSETS_STATE_PROCESSED) {
            return [false, $t->_('leavemanager_edit_monry_error')];
        }

        $filterAttachs = [];
        foreach ($attachMents as $attachMent) {
            $filterAttachs[] = [
                'name'       => $attachMent['name'],
                'object_url' => $attachMent['object_url'],
            ];
        }
        $attachMents = json_encode($attachMents, JSON_UNESCAPED_UNICODE);
        $asset_state_unprocessed = self::ASSETS_STATE_UNPROCESSED;
        $pdo = $this->getDI()->get("db_backyard");
        $pdo->begin();
        try {
            $version = 1;
            if (!$leaveInfo) {
                $stmt = $pdo->prepare("
                --
                    insert into 
                        leave_manager 
                        (`staff_info_id`, `money_remand_state`, `money_operate_version`, `money_operator_id`, `money_operate_time`, `money_attachments`, `assets_remand_state`) 
                    value 
                        (:staff_info_id, :money_remand_state, :money_operate_version, :money_operator_id, :money_operate_time, :money_attachments, :assets_remand_state)"
                );
                $stmt->bindParam(':staff_info_id', $staffInfoId, \PDO::PARAM_INT);
                $stmt->bindParam(':money_remand_state', $state, \PDO::PARAM_INT);
                $stmt->bindParam(':money_operate_version', $version, \PDO::PARAM_INT);
                $stmt->bindParam(':money_operator_id', $operatorId, \PDO::PARAM_INT);
                $operateTime = gmdate("Y-m-d H:i:s");
                $stmt->bindParam(':money_operate_time', $operateTime, \PDO::PARAM_STR);
                $stmt->bindParam(':money_attachments', $attachMents, \PDO::PARAM_STR);
                $stmt->bindParam(':assets_remand_state', $asset_state_unprocessed, \PDO::PARAM_INT);
                $stmt->execute();

                $leaveManagerId = $pdo->lastInsertId();
                if (!$leaveManagerId) {
                    throw new \Exception("插入失败，请重试");
                }
            } else {
                //曾经管理过
                $stmt = $pdo->prepare("select * from leave_manager where staff_info_id = :staff_info_id for update ");
                $stmt->bindValue('staff_info_id', $staffInfoId, \PDO::PARAM_INT);
                $stmt->execute();
                $leaveInfo = $stmt->fetch();
                if ($leaveInfo['money_remand_state'] == self::ASSETS_STATE_PROCESSED) {
                    return [false, '处理完成，不能再编辑'];
                }

                $version     = $leaveInfo['money_operate_version'] + 1;
                $stmt        = $pdo->prepare("
                --
                    update 
                        `leave_manager` 
                    set 
                        `money_remand_state`=:money_remand_state, 
                        `money_operate_version`= :money_operate_version, 
                        `money_operator_id`=:money_operator_id, 
                        `money_operate_time`=:money_operate_time, 
                        `money_attachments`=:money_attachments 
                    where `id`=:id "
                );
                $operateTime = gmdate("Y-m-d H:i:s");
                $stmt->bindParam(':money_remand_state', $state, \PDO::PARAM_INT);
                $stmt->bindParam(':money_operate_version', $version, \PDO::PARAM_INT);
                $stmt->bindParam(':money_operator_id', $operatorId, \PDO::PARAM_INT);
                $stmt->bindParam(':money_operate_time', $operateTime, \PDO::PARAM_STR);
                $stmt->bindParam(':money_attachments', $attachMents, \PDO::PARAM_STR);
                $stmt->bindParam(':id', $leaveInfo['id'], \PDO::PARAM_INT);
                $stmt->execute();

                if (!$stmt->rowCount()) {
                    throw new \Exception("更新失败，请重试");
                }
            }

            $this->insertMoney($version, $money, $cashier_money, $remark, $state, $staffInfoId, $operatorId);
        } catch (\Exception $e) {
            $pdo->rollBack();
            throw $e;
        }

        $pdo->commit();
        return [true, ''];
    }

    /**
     * 获取用户可选择的商品type
     *    根据用户网点ID
     *
     * @param $staffInfoId
     * @return int|string
     */
    public function goodsTypes($staffInfoId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            "conditions" => " staff_info_id = :staff_info_id: ",
            "columns"    => "staff_info_id, sys_store_id ",
            "bind"       => ["staff_info_id" => $staffInfoId],
        ]);
        if ($staffInfo) {
            $staffInfo = $staffInfo->toArray();
        } else {
            return 0;
        }

        if ($staffInfo && $staffInfo['sys_store_id']) {
            if ($staffInfo['sys_store_id'] == "-1") {
                //主战用户
                return AssetsService::STORE_TYPE_HO;
            }
            $store = SysStoreModel::findFirst([
                "conditions" => " id = :id: ",
                "columns"    => " id, category ",
                "bind"       => ["id" => $staffInfo['sys_store_id']],

            ]);
            if (!$store) {
                return 0;
            }
            $store = $store->toArray();
            if ($store && $store['category']) {
                foreach (AssetsService::STORE_TYPE_CAYTEGORY_MAP as $type => $value) {
                    if (in_array($store['category'], $value)) {
                        return $type;
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 插入后台编辑资产记录
     *  在事物中使用
     *  废弃
     * @param $pdo
     * @param array $assets
     * @param integer $version
     * @param integer $staffInfoId
     * @param integer $state
     * @param integer $operatorId
     * @throws \Exception
     */
    private function insertAssets($pdo, $assets, $version, $staffInfoId, $state, $operatorId)
    {
        $assetGoods = $this->assetsGoods(array_column($assets, 'goods_id'), $staffInfoId);
        $assetGoods = $assetGoods + $this->assetsGoods(array_column($assets, 'goods_id'), $staffInfoId,
                self::SOURCE_TYPE_PUBLIC_ASSETS);
//        $currentStaffAssets = $this->getCurrentStaffAssets($staffInfoId);
//        $currentStaffAssetsIds = array_column($currentStaffAssets, 'assets_id');
//        $assetsIds = array_filter(array_column($assets, 'assets_id'));
//        if (sort($currentStaffAssetsIds) != sort($assetsIds)) {
//            throw new \Exception('员工资产以发生变化，请刷新后重新提交');
//        }

        if ($assets && is_array($assets)) {
            foreach ($assets as $asset) {
                if (isset($assetGoods[$asset['goods_id']]['bar_code'])) {
                    $stmt = $pdo->prepare("
                -- 
                    insert into 
                        leave_assets_manager (
                            `staff_info_id`, 
                            `version`, 
                            `source`, 
                            `assets_id`, 
                            `goods_id`, 
                            `goods_price`, 
                            `goods_bar_code`, 
                            `deduction_reason`, 
                            `price`, 
                            `sn_code`, 
                            `asset_code`, 
                            `operator_id`, 
                            `superior_status`,
                            `superior_batch_num`, 
                            `images`,
                            `is_batch`, 
                            `useing_num`, 
                            `assets_operate_status`, 
                            `operate_time`, 
                            `remark`, 
                            `operate_state`) 
                        value (
                            :staff_info_id, 
                            :version, 
                            :source, 
                            :assets_id, 
                            :goods_id, 
                            :goods_price, 
                            :goods_bar_code, 
                            :deduction_reason, 
                            :price, 
                            :sn_code, 
                            :asset_code, 
                            :operator_id, 
                            :superior_status, 
                            :superior_batch_num, 
                            :images,
                            :is_batch, 
                            :useing_num, 
                            :assets_operate_status, 
                            :operate_time, 
                            :remark, 
                            :operate_state) "
                    );
                    $stmt->bindValue('staff_info_id', $staffInfoId, \PDO::PARAM_INT);
                    $stmt->bindValue('version', $version, \PDO::PARAM_INT);
                    $stmt->bindValue('source',
                        isset($asset['assets_id']) && $asset['assets_id'] ? self::ASSETS_SOURCE_USER : self::ASSETS_SOURCE_MANAGER,
                        \PDO::PARAM_INT);
                    $stmt->bindValue('assets_id',
                        isset($asset['assets_id']) && $asset['assets_id'] ? $asset['assets_id'] : 0, \PDO::PARAM_INT);
                    $stmt->bindValue('goods_price',
                        isset($asset['goods_price']) && $asset['goods_price'] ? $asset['goods_price'] : 0,
                        \PDO::PARAM_INT);
                    $stmt->bindValue('goods_id', $asset['goods_id'], \PDO::PARAM_INT);
                    $stmt->bindValue('deduction_reason',
                        isset($asset['deduction_reason']) && $asset['deduction_reason'] ? (string)$asset['deduction_reason'] : '',
                        \PDO::PARAM_STR);
                    $stmt->bindValue('goods_bar_code', $assetGoods[$asset['goods_id']]['bar_code'], \PDO::PARAM_STR);
                    $stmt->bindValue('price',
                        isset($asset['all_price']) && $asset['all_price'] ? $asset['all_price'] : 0, \PDO::PARAM_INT);
                    $stmt->bindValue('sn_code',
                        isset($asset['sn_code']) && $asset['sn_code'] ? (string)$asset['sn_code'] : "",
                        \PDO::PARAM_STR);
                    $stmt->bindValue('asset_code',
                        isset($asset['asset_code']) && $asset['asset_code'] ? (string)$asset['asset_code'] : "",
                        \PDO::PARAM_STR);
                    $stmt->bindValue('remark',
                        isset($asset['remark']) && $asset['remark'] ? (string)$asset['remark'] : "", \PDO::PARAM_STR);
                    $stmt->bindValue('operate_time', gmdate("Y-m-d H:i:s"), \PDO::PARAM_STR);
                    $stmt->bindValue('operator_id', $operatorId, \PDO::PARAM_INT);
                    $stmt->bindValue('superior_status',
                        isset($asset['superior_status']) && $asset['superior_status'] ? $asset['superior_status'] : 0,
                        \PDO::PARAM_INT);
                    $stmt->bindValue('superior_batch_num', isset($asset['num']) && $asset['num'] ? $asset['num'] : 0,
                        \PDO::PARAM_INT);
                    $stmt->bindValue('is_batch',
                        isset($asset['is_batch']) && $asset['is_batch'] ? $asset['is_batch'] : 0, \PDO::PARAM_INT);
                    $stmt->bindValue('images',
                        isset($asset['images']) && $asset['images'] ? json_encode($asset['images'],
                            JSON_UNESCAPED_UNICODE) : '');
                    $stmt->bindValue('useing_num',
                        isset($asset['usesing_num']) && $asset['usesing_num'] ? $asset['usesing_num'] : 0,
                        \PDO::PARAM_INT);
                    $stmt->bindValue('assets_operate_status',
                        isset($asset['operate_status']) && $asset['operate_status'] ? $asset['operate_status'] : AssetsService::OPERATE_STATUS_USEING,
                        \PDO::PARAM_INT);
                    $stmt->bindValue('operate_state', $state, \PDO::PARAM_INT);
                    $stmt->execute();
                }
            }
        }
    }

    /**
     * 插入后台编辑资产记录
     *  在事物中使用
     *
     * @param array $assets
     * @param integer $version
     * @param integer $staffInfoId
     * @param integer $state
     * @param integer $operatorId
     * @throws \Exception
     */
    private function batchInsertAssets($assets, $version, $staffInfoId, $state, $operatorId)
    {
        //查询-网点可申请资产信息
        $assetGoods = $this->assetsGoods(array_column($assets, 'goods_id'), $staffInfoId);
        $assetGoods = $assetGoods + $this->assetsGoods(array_column($assets, 'goods_id'), $staffInfoId,
                self::SOURCE_TYPE_PUBLIC_ASSETS);

        $allAsset = [];
        if ($assets && is_array($assets)) {
            foreach ($assets as $asset) {
                if (isset($assetGoods[$asset['goods_id']]['bar_code'])) {
                    $oneAsset['staff_info_id']         = $staffInfoId;
                    $oneAsset['version']               = $version;
                    $oneAsset['source']                = isset($asset['assets_id']) && $asset['assets_id'] ? self::ASSETS_SOURCE_USER : self::ASSETS_SOURCE_MANAGER;
                    $oneAsset['assets_id']             = isset($asset['assets_id']) && $asset['assets_id'] ? $asset['assets_id'] : 0;
                    $oneAsset['goods_price']           = isset($asset['goods_price']) && $asset['goods_price'] ? $asset['goods_price'] * 100 : 0;
                    $oneAsset['goods_id']              = $asset['goods_id'];
                    $oneAsset['deduction_reason']      = isset($asset['deduction_reason']) && $asset['deduction_reason'] ? (string)$asset['deduction_reason'] : '';
                    $oneAsset['goods_bar_code']        = $assetGoods[$asset['goods_id']]['bar_code'];
                    $oneAsset['price']                 = isset($asset['all_price']) && $asset['all_price'] ? $asset['all_price'] * 100 : 0;
                    $oneAsset['sn_code']               = isset($asset['sn_code']) && $asset['sn_code'] ? (string)$asset['sn_code'] : "";
                    $oneAsset['asset_code']            = isset($asset['asset_code']) && $asset['asset_code'] ? (string)$asset['asset_code'] : "";
                    $oneAsset['remark']                = isset($asset['remark']) && $asset['remark'] ? (string)$asset['remark'] : "";
                    $oneAsset['operate_time']          = gmdate("Y-m-d H:i:s");
                    $oneAsset['operator_id']           = $operatorId;
                    $oneAsset['superior_status']       = isset($asset['superior_status']) && $asset['superior_status'] ? $asset['superior_status'] : 0;
                    $oneAsset['superior_batch_num']    = isset($asset['num']) && $asset['num'] ? $asset['num'] : 0;
                    $oneAsset['is_batch']              = isset($asset['is_batch']) && $asset['is_batch'] ? $asset['is_batch'] : 0;
                    $oneAsset['images']                = isset($asset['images']) && $asset['images'] ? json_encode($asset['images'],
                        JSON_UNESCAPED_UNICODE) : '';
                    $oneAsset['useing_num']            = isset($asset['usesing_num']) && $asset['usesing_num'] ? $asset['usesing_num'] : 0;
                    $oneAsset['assets_operate_status'] = isset($asset['operate_status']) && $asset['operate_status'] ? $asset['operate_status'] : AssetsService::OPERATE_STATUS_USEING;
                    $oneAsset['operate_state']         = $state;

                    $allAsset[] = $oneAsset;
                }
            }
        }

        if ($allAsset) {
            $batchAssets = array_chunk($allAsset, 1000);
            foreach ($batchAssets as $oneBatch) {
                (new BaseModel())->table_batch_insert(array_values($oneBatch), 'db_backyard', 'leave_assets_manager');
            }
        }
    }

    /**
     * 返回当前用户可选择的商品列表
     *
     * @param $goodsIds
     * @param $staffInfoId
     * @return array
     */
    public function assetsGoods($goodsIds, $staffInfoId, $publicAssets = "")
    {
        $type = $this->goodsTypes($staffInfoId);
        if ($type) {
            if ($goodsIds) {
                $sql = "select * from assets_goods where id in (".implode(", ", $goodsIds).") ";
            } else {
                $sql = "select * from assets_goods where type = ".$type;
            }
            if ($publicAssets && $publicAssets == AssetsService::SOURCE_TYPE_PUBLIC_ASSETS) {
                $sql .= " and is_public = 1 ";
            } else {
                $sql .= " and is_public = 0 ";
            }

            $goods = $this->getDI()->get("db_backyard")->query($sql)->fetchAll(\PDO::FETCH_ASSOC);

            return array_column($goods, null, 'id');
        }
        return [];
    }

    /**
     * 插入后台编辑钱款记录
     *  在事物中使用
     *
     * @param $pdo
     * @param integer $version
     * @param integer $money
     * @param string $remark
     * @param integer $state
     * @param integer $staffInfoId
     * @param integer $operatorId
     */
    private function insertMoney($version, $money, $cashier_money, $remark, $state, $staffInfoId, $operatorId)
    {
        $operateTime = gmdate("Y-m-d H:i:s");
        $model       = new LeaveMoneyManagerModel();
        return $model->insert_record([
            'staff_info_id'  => $staffInfoId,
            'version'        => $version,
            'remark'         => $remark,
            'money'          => $money,
            'cashier_money'  => $cashier_money,
            'operator_state' => $state,
            'operator_id'    => $operatorId,
            'operate_time'   => $operateTime,
        ]);
    }

    /**
     * 查询当前员工后台的名下资产
     * 详情
     * @param integer $staffInfoId 员工ID
     * @return array $result
     */
    public function assetsInfo($staffInfoId)
    {
        $staff_service            = new StaffService();
        $result['staff_info']     = $staff_service->staffInfo($staffInfoId);
        $result['superior_info']  = [
            "superior_id"           => "",
            "superior_phone"        => "",
            "superior_operate_time" => "",
        ];
        $result['process_state']  = self::ASSETS_STATE_UNPROCESSED;
        $result['process_states'] = self::assetsState();
        $result['operate_states'] = self::operateState();
        $result['remark'] = '';
        $result['assets_object_key'] = [];

        // 判断是否操作过
        $leaveInfo = $this->getLeaveInfo($staffInfoId);
        //是否新资产处理状态
        $result['is_new_assets_remand_state'] = empty($leaveInfo) ? (string)LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES : $leaveInfo['is_new_assets_remand_state'];
        if ($leaveInfo && isset($leaveInfo['leave_date'])) {
            $result['staff_info']['leave_date'] = $leaveInfo['leave_date'] != '0000-00-00 00:00:00' ? date('Y-m-d',
                strtotime($leaveInfo['leave_date'])) : '';
        }

        $result['assets'] = $result['public_assets'] = $result['batch_assets'] = [];
        if($result['is_new_assets_remand_state'] == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES) {
            $new_leave_assets = $this->getLeaveAssetsDetail($staffInfoId);
            $result['assets'] = $new_leave_assets['assets'] ?? [];
            $result['public_assets'] = $new_leave_assets['public_assets'] ?? [];
            $result['batch_assets'] = [];
            $result['remark']        = $new_leave_assets['asset_department_remark'];
            $result['process_state'] = $leaveInfo['assets_remand_state'] ?? self::ASSETS_STATE_UNPROCESSED;
            $attachMents             = $new_leave_assets['file'];
            if ($attachMents) {
                foreach ($attachMents as $k => $attachMent) {
                    if (isset($attachMent['object_url'])) {
                        $attachMents[$k]['url'] = $attachMent['object_url'];
                    }
                }
            }
            $result['assets_object_key'] = $attachMents;
        } else {
            $goodsList              = $this->assetsGoods([], $staffInfoId);
            $result['goods_list']   = $this->assetsGoodsList();
            $publicAssets           = $this->assetsGoods([], $staffInfoId, AssetsService::SOURCE_TYPE_PUBLIC_ASSETS);
            $result['public_goods'] = $this->assetsGoodsList(true, false);
            $result['batch_goods']  = $this->assetsGoodsList(true, true);

            if ($leaveInfo && $leaveInfo['assets_operate_version']) {
                if ($leaveInfo['is_has_superior'] && $leaveInfo['superior_id']) {
                    $staff = $this->getDI()->get("db_rby")->query("--
                select * from hr_staff_info where staff_info_id = ".$leaveInfo['superior_id'])->fetch(\PDO::FETCH_ASSOC);
                    if ($staff) {
                        $result['superior_info'] = [
                            "superior_id"           => $staff['staff_info_id'],
                            "superior_phone"        => $staff['mobile'],
                            "superior_operate_time" => date("Y-m-d H:i:s",
                                strtotime($leaveInfo['superior_operate_time']) + ($this->timeOffset) * 3600),
                        ];
                    }
                }
                $img_prefix = env('img_prefix', 'http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/');

                //操作过
                $assets = $this->getSysStaffAssets($staffInfoId, $leaveInfo['assets_operate_version']);

                $result['remark']        = $leaveInfo['assets_operate_remark'];
                $result['process_state'] = $leaveInfo['assets_remand_state'];
                $attachMents             = json_decode((string)$leaveInfo['assets_attachments'], true);
                if ($attachMents) {
                    foreach ($attachMents as $k => $attachMent) {
                        if (isset($attachMent['object_url'])) {
                            $attachMents[$k]['url'] = $attachMent['object_url'];
                        }
                    }
                }
                $result['assets_object_key'] = $attachMents;
            } else {
                // 未操作过 取当前拥有的资产
                $assets                      = $this->getCurrentStaffAssets($staffInfoId);
                $result['remark']            = '';
                $result['assets_object_key'] = [
                ];
            }
            // 如果员工没有可选择的商品
            if ($leaveInfo && isset($leaveInfo['assets_remand_state']) && $leaveInfo['assets_remand_state']) {
                $result['process_state'] = $leaveInfo['assets_remand_state'] ? $leaveInfo['assets_remand_state'] : self::ASSETS_STATE_UNPROCESSED;
            } else {
                if (!$goodsList) {
                    $result['process_state'] = self::ASSETS_STATE_PROCESSED;
                }
            }

            foreach ($assets as $asset) {
                if ($asset['is_public']) {
                    if ($asset['is_batch']) {
                        if (isset($asset['num'])) {
                            $result['batch_assets'][] = [
                                "goods_id"         => $asset['goods_id'],
                                "name"             => $asset['name'],
                                "bar_code"         => $asset['bar_code'],
                                "is_public"        => $asset['is_public'],
                                "is_batch"         => $asset['is_batch'],
                                "num"              => $asset['num'],
                                "usesing_num"      => $asset['useing_num'],
                                "all_price"        => $asset['all_price'],
                                "goods_price"      => $asset['goods_price'],
                                "deduction_reason" => $asset['deduction_reason'],
                                "goods_name_zh"    => $asset['goods_name_zh'] ?? '',
                                "goods_name_th"    => $asset['goods_name_th'] ?? '',
                                "goods_name_en"    => $asset['goods_name_en'] ?? '',
                                "goods_name"       => $asset['goods_name_en'] ?? '',
                                "value_th"         => $asset['value_th'] ?? '',
                            ];
                        } else {
                            if (!isset($result['batch_assets'][$asset['goods_id']]) && $asset['operate_status'] == AssetsService::OPERATE_STATUS_USEING) {
                                $result['batch_assets'][$asset['goods_id']] = [
                                    "goods_id"         => $asset['goods_id'],
                                    "name"             => $asset['name'],
                                    "bar_code"         => $asset['bar_code'],
                                    "is_public"        => $asset['is_public'],
                                    "is_batch"         => $asset['is_batch'],
                                    "num"              => 0,
                                    "usesing_num"      => 0,
                                    "all_price"        => $asset['all_price'],
                                    "goods_price"      => $asset['all_price'],
                                    "deduction_reason" => $asset['deduction_reason'],
                                    "goods_name_zh"    => $asset['goods_name_zh'] ?? '',
                                    "goods_name_th"    => $asset['goods_name_th'] ?? '',
                                    "goods_name_en"    => $asset['goods_name_en'] ?? '',
                                    "goods_name"       => $asset['goods_name_en'] ?? '',
                                    "value_th"         => $asset['value_th'] ?? '',
                                ];
                            }
                            if ($asset['operate_status'] == AssetsService::OPERATE_STATUS_USEING) {
                                $result['batch_assets'][$asset['goods_id']]['usesing_num']++;
//                            $result['batch_assets'][$asset['goods_id']]['goods_price'] += $result['batch_assets'][$asset['goods_id']]['goods_price'];
                            }
                        }
                    } else {
                        $result['public_assets'][] = $asset;
                    }
                } else {
                    $result['assets'][] = $asset;
                }
            }
            $result['batch_assets'] = array_values($result['batch_assets']);
        }


        // 扣除薪资、工资与其他收入赔偿损失同意书
        $result['salary_deduction_url'] = env('pre_url').'Datatmp/deduction_notice?staff_id='.$staffInfoId;
        return $result;
    }

    private function operateState()
    {
        $state = [];
        foreach (AssetsService::OPERATE_DESC as $k => $v) {
            if ($k) {
                $state[] = ["key" => $k, "value" => self::$t->_("operate_status_".$k)];
            }
        }
        return $state;
    }

    /**
     * 获取oa离职资产数据
     * @param $staff_info_id
     * @return array
     */
    public function getLeaveAssetsDetail($staff_info_id): array
    {
        $lang   = self::$language;
        $resultData = $this->getLeaveAssetsListBySvc($staff_info_id, $lang);

        $result['assets'] = $result['public_assets'] = $result['file'] = $result['asset_department_remark'] = [];
        if (isset($resultData['code']) && $resultData['code'] == 1 && $resultData['data']) {
            $assets_list                       = $resultData['data']['assets_list'] ?? [];
            $result['file']                    = $resultData['data']['file'];
            $result['asset_department_remark'] = $resultData['data']['asset_department_remark'];
            foreach ($assets_list as $key => $value) {
                if ($value['use'] == 1) { //个人资产
                    $result['assets'][] = [
                        'goods_name'           => $value['asset_name'],
                        'goods_name_zh'        => $value['asset_name_zh'],
                        'goods_name_en'        => $value['asset_name_en'],
                        'goods_name_local'     => $value['asset_name_local'],
                        'asset_code'           => $value['asset_code'],             //资产编码
                        'sn_code'              => $value['sn_code'],                //sn编码
                        'operate_status'       => $value['asset_status'],           //资产状态
                        'operate_status_text'  => $value['asset_status_text'],      //资产状态文本
                        'remark'               => $value['manager_remark'],         //资产备注
                        'images'               => $value['images'],                 //资产状态照片
                        'goods_price'          => $value['purchase_price'],         //资产价值
                        'all_price'            => $value['deduct_amount'],          //扣费金额
                        'deduction_reason'     => $value['deduct_reason'],          //扣款原因
                        'superior_status'      => $value['leave_asset_state'],      //资产状况
                        'superior_status_text' => $value['leave_asset_state_text'], //资产状况文本
                        'receipted_at'         => $value['receipted_at'],
                    ];
                } else { //公共资产
                    $result['public_assets'][] = [
                        'goods_name'           => $value['asset_name'],
                        'goods_name_zh'        => $value['asset_name_zh'],
                        'goods_name_en'        => $value['asset_name_en'],
                        'goods_name_local'     => $value['asset_name_local'],
                        'asset_code'           => $value['asset_code'],             //资产编码
                        'sn_code'              => $value['sn_code'],                //sn编码
                        'operate_status'       => $value['asset_status'],           //资产状态
                        'operate_status_text'  => $value['asset_status_text'],      //资产状态文本
                        'remark'               => $value['manager_remark'],         //资产备注
                        'images'               => $value['images'],                 //资产状态照片
                        'goods_price'          => $value['purchase_price'],         //资产价值
                        'all_price'            => $value['deduct_amount'],          //扣费金额
                        'deduction_reason'     => $value['deduct_reason'],          //扣款原因
                        'superior_status'      => $value['leave_asset_state'],      //资产状况
                        'superior_status_text' => $value['leave_asset_state_text'], //资产状况文本
                        'receipted_at'         => $value['receipted_at'],
                    ];
                }
            }
        }
        return $result;
    }

    /**
     * 从oa获取离职资产数据
     * @param $staff_info_id
     * @param $lang
     * @return array|bool|mixed|null
     */
    public function getLeaveAssetsListBySvc($staff_info_id, $lang)
    {
        $client = new ApiClient('oa_rpc', '', 'get_leave_assets_detail', $lang);
        $params = [
            'staff_id' => $staff_info_id,
        ];
        $client->setParams([$params]);
        return $client->execute();
    }

    /**
     *
     * 返回资产列表
     *
     * @param $isPublic
     * @param $isBatch
     *
     */
    public function assetsGoodsList($isPublic = false, $isBatch = false)
    {
        $goods = AssetsGoodsModel::find([
            'columns'    => 'id, bar_code, goods_name_en, goods_name_th, goods_name_zh ',
            'conditions' => ' is_public = :is_public: and deleted = 0 and is_batch = :is_batch: ',
            'bind'       => [
                'is_public' => $isPublic ? 1 : 0,
                'is_batch'  => $isBatch ? 1 : 0,
            ],
        ])->toArray();

        foreach ($goods as &$good) {
            $good['name'] = $good['goods_name_'.str_replace('-CN', '', self::$language)];
        }
        return $goods;
    }

    /**
     * 获取后台管理员编辑的资产信息
     *
     * @param $staffInfoId
     * @param $version
     * @return array
     */
    private function getSysStaffAssets($staffInfoId, $version)
    {
        $stmt = $this->getDI()->get("db_rby")->prepare("
        --
            select 
                * 
            from 
                leave_assets_manager 
            where 
                staff_info_id = :staff_info_id and version = :version ");
        $stmt->bindValue(":staff_info_id", $staffInfoId, \PDO::PARAM_INT);
        $stmt->bindValue(":version", $version, \PDO::PARAM_INT);
        $stmt->execute();
        $leaveAssets = $stmt->fetchAll(\PDO::FETCH_ASSOC);

        $result             = [];
        $currentStaffAssets = $this->getCurrentStaffAssets($staffInfoId);
        $currentStaffAssets = array_column($currentStaffAssets, null, 'assets_id');
        if ($leaveAssets) {
            $assetsGoods = $this->assetsGoods(array_column($leaveAssets, 'goods_id'), $staffInfoId);
            $assetsGoods = $assetsGoods + $this->assetsGoods(array_column($leaveAssets, 'goods_id'), $staffInfoId,
                    AssetsService::SOURCE_TYPE_PUBLIC_ASSETS);
            foreach ($leaveAssets as $leaveAsset) {
                if ($leaveAsset['assets_id'] && isset($currentStaffAssets[$leaveAsset['assets_id']])) {
                    $result[] = [
                        'goods_id'             => $leaveAsset['goods_id'],
                        'assets_id'            => $leaveAsset['assets_id'],
                        'name'                 => $assetsGoods[$leaveAsset['goods_id']]['goods_name_'.str_replace('-CN',
                            '', self::$language)],
                        'goods_name_zh'        => $assetsGoods[$leaveAsset['goods_id']]['goods_name_zh'],
                        'goods_name_th'        => $assetsGoods[$leaveAsset['goods_id']]['goods_name_th'],
                        'goods_name_en'        => $assetsGoods[$leaveAsset['goods_id']]['goods_name_en'],
                        'bar_code'             => $assetsGoods[$leaveAsset['goods_id']]['bar_code'],
                        'is_public'            => $assetsGoods[$leaveAsset['goods_id']]['is_public'],
                        'is_batch'             => $assetsGoods[$leaveAsset['goods_id']]['is_batch'],
                        'value_th'             => $assetsGoods[$leaveAsset['goods_id']]['value_th'],
                        'asset_code'           => $currentStaffAssets[$leaveAsset['assets_id']]['asset_code'],
                        'deduction_reason'     => $leaveAsset['deduction_reason'],
                        'sn_code'              => $currentStaffAssets[$leaveAsset['assets_id']]['sn_code'],
                        'created_at'           => $currentStaffAssets[$leaveAsset['assets_id']]['created_at'],
                        'remark'               => $leaveAsset['remark'],
                        'num'                  => $leaveAsset['superior_batch_num'],
                        'useing_num'           => $leaveAsset['useing_num'],
                        'images'               => json_decode(stripslashes($leaveAsset['images']), true),
                        'superior_status'      => $leaveAsset['superior_status'],
                        'superior_status_text' => isset($leaveAsset['superior_status']) && $leaveAsset['superior_status'] ?
                            self::$t->_("superior_status_".$leaveAsset['superior_status']) : "",
                        "operate_status"       => $leaveAsset['assets_operate_status'],
                        "operate_status_text"  => self::$t->_('operate_status_'.$leaveAsset['assets_operate_status']),
                        'all_price'            => bcdiv($leaveAsset['price'], 100, 2),
                        'goods_price'          => bcdiv($leaveAsset['goods_price'], 100, 2),
                        'deduction_amount_th'  => number_format($leaveAsset['price'] / 100, 2),
                    ];
                    unset($currentStaffAssets[$leaveAsset['assets_id']]);
                } else {
                    $result[] = [
                        'goods_id'             => $leaveAsset['goods_id'],
                        'assets_id'            => $leaveAsset['assets_id'],
                        'name'                 => $assetsGoods[$leaveAsset['goods_id']]['goods_name_'.str_replace('-CN',
                            '', self::$language)],
                        'goods_name_zh'        => $assetsGoods[$leaveAsset['goods_id']]['goods_name_zh'],
                        'goods_name_th'        => $assetsGoods[$leaveAsset['goods_id']]['goods_name_th'],
                        'goods_name_en'        => $assetsGoods[$leaveAsset['goods_id']]['goods_name_en'],
                        'bar_code'             => $assetsGoods[$leaveAsset['goods_id']]['bar_code'],
                        'is_public'            => $assetsGoods[$leaveAsset['goods_id']]['is_public'],
                        'is_batch'             => $assetsGoods[$leaveAsset['goods_id']]['is_batch'],
                        'value_th'             => $assetsGoods[$leaveAsset['goods_id']]['value_th'],
                        'asset_code'           => $leaveAsset['asset_code'],
                        'deduction_reason'     => $leaveAsset['deduction_reason'],
                        'sn_code'              => $leaveAsset['sn_code'],
                        'created_at'           => $leaveAsset['operate_time'],
                        'remark'               => $leaveAsset['remark'],
                        'num'                  => $leaveAsset['superior_batch_num'],
                        'useing_num'           => $leaveAsset['useing_num'],
                        'images'               => json_decode(stripslashes($leaveAsset['images']), true),
                        'superior_status'      => (int)$leaveAsset['superior_status'],
                        'superior_status_text' => isset($leaveAsset['superior_status']) && $leaveAsset['superior_status'] ?
                            self::$t->_("superior_status_".$leaveAsset['superior_status']) : "",
                        "operate_status"       => $leaveAsset['assets_operate_status'],
                        "operate_status_text"  => self::$t->_('operate_status_'.$leaveAsset['assets_operate_status']),
                        'all_price'            => bcdiv($leaveAsset['price'], 100, 2),
                        'goods_price'          => bcdiv($leaveAsset['goods_price'], 100, 2),
                        'deduction_amount_th'  => number_format($leaveAsset['price'] / 100, 2),
                    ];
                }
            }
        }
        //过滤出来 批量资产 批量资产不存在assets_id
        foreach ($currentStaffAssets as $k => $currentStaffAsset) {
            if (isset($currentStaffAsset['is_batch']) && $currentStaffAsset['is_batch']) {
                unset($currentStaffAssets[$k]);
            }
        }


        if ($currentStaffAssets) {
            $result = array_merge($result, array_values($currentStaffAssets));
        }
        return $result;
    }

    /**
     * 后台为编辑资产情况下获取员工当前资产信息
     *
     * @param $staffInfoId
     * @return array
     */
    public function getCurrentStaffAssets($staffInfoId)
    {
        $pdo = $this->getDI()->get("db_backyard")->prepare("
        --
            select 
                * 
            from 
                assets_info 
            where 
                staff_info_id = :staff_info_id and state = 1 "
        );
        $pdo->bindValue('staff_info_id', $staffInfoId, \PDO::PARAM_INT);
        $pdo->execute();
        $assetsList    = $pdo->fetchAll(\PDO::FETCH_ASSOC);
        $currentAssets = [];

        if ($assetsList) {
            $goodIds   = array_unique(array_column($assetsList, 'assets_goods_id'));
            $goodsList = $this->getDI()->get("db_backyard")->query("
            --
                select 
                    * 
                from 
                    assets_goods 
                where 
                    id in (".implode(', ', $goodIds).") "
            )->fetchAll(\PDO::FETCH_ASSOC);
            $goodsList = array_column($goodsList, null, 'id');

            foreach ($assetsList as $asset) {
                if (isset($goodsList[$asset['assets_goods_id']])) {
                    $currentAssets[] = [
                        'goods_id'             => $asset['assets_goods_id'],
                        'assets_id'            => $asset['id'],
                        'name'                 => $goodsList[$asset['assets_goods_id']]['goods_name_'.str_replace('-CN',
                            '', self::$language)],
                        'bar_code'             => $goodsList[$asset['assets_goods_id']]['bar_code'],
                        'asset_code'           => $asset['asset_code'],
                        'is_public'            => $goodsList[$asset['assets_goods_id']]['is_public'],
                        'is_batch'             => $goodsList[$asset['assets_goods_id']]['is_batch'],
                        'deduction_reason'     => '',
                        'sn_code'              => $asset['sn_code'],
                        'remark'               => "",
                        'images'               => [],
                        'superior_status'      => 0,
                        'superior_status_text' => "",
                        "operate_status"       => $asset['operate_status'],
                        "operate_status_text"  => self::$t->_('operate_status_'.$asset['operate_status']),
//                        'all_price' => 0,
                        'all_price'            => bccomp($asset['deduction_amount_th'], 0,
                            2) ? $asset['deduction_amount_th'] : $goodsList[$asset['assets_goods_id']]['value_th'],
                        'goods_name_zh'        => $goodsList[$asset['assets_goods_id']]['goods_name_zh'],
                        'goods_name_th'        => $goodsList[$asset['assets_goods_id']]['goods_name_th'],
                        'goods_name_en'        => $goodsList[$asset['assets_goods_id']]['goods_name_en'],
                        'created_at'           => $asset['created_at'],
                        'value_th'             => $goodsList[$asset['assets_goods_id']]['value_th'],
                        'deduction_amount_th'  => $asset['deduction_amount_th'],
                        'goods_price'          =>
                            $goodsList[$asset['assets_goods_id']]['value_th'],
                    ];
                }
            }
        }

        return $currentAssets;
    }

    public function syncHold($staffInfoId, $edits = [])
    {
        $assetsProcessState = self::ASSETS_STATE_UNPROCESSED;
        $leaveInfo          = $this->getLeaveInfo($staffInfoId);
        if ($edits && isset($edits['assets_remand_state'])) {
            $assetsProcessState = $edits['assets_remand_state'];
        } else {
            if ($leaveInfo && $leaveInfo['assets_remand_state']) {
                //操作过
                $assetsProcessState = $leaveInfo['assets_remand_state'];
            }
        }
        if ($edits && isset($edits['money_remand_state'])) {
            $moneyProcessState = $edits['money_remand_state'];
        } else {
            if ($leaveInfo && $leaveInfo['money_remand_state']) {
                $moneyProcessState = $leaveInfo['money_remand_state'];
            } else {
                $moneyProcessState = self::ASSETS_STATE_UNPROCESSED;
            }
        }
        $is_new_assets_remand_state = LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_NO;
        if($leaveInfo) {
            $is_new_assets_remand_state = $leaveInfo['is_new_assets_remand_state'];
        }

        if($is_new_assets_remand_state == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_NO) {
            $goodsList = $this->assetsGoods([], $staffInfoId);
            if (!$goodsList) {
                $assetsProcessState = self::ASSETS_STATE_PROCESSED;
            }
        }

        if ($assetsProcessState == self::ASSETS_STATE_PROCESSED && $moneyProcessState == self::ASSETS_STATE_PROCESSED) {
            $this->getDI()->get('logger')->info("开始同步hold ".$staffInfoId);
            $hold_manage_service = new HoldManageService();
            $result = $hold_manage_service->processHoldAfterMoneyAssetProcessed($staffInfoId);

            $sync_absenteeism_hold_result = $hold_manage_service->syncAbsenteeismHoldHandle([
                "staff_info_id"   => $staffInfoId,
                "handle_progress" => self::ASSETS_STATE_PROCESSED,
            ]);
            $this->logger->info([
                'message'                     => '同步hold处理状态',
                'staff_info_id'               => $staffInfoId,
                'synchronizeHoldProgress'     => $result,
                'syncAbsenteeismHoldProgress' => $sync_absenteeism_hold_result,
            ]);

            $this->getDI()->get('logger')->info("同步hold ".$staffInfoId."结果 ".$result);
        } else {
            $this->getDI()->get('logger')->info("处理状态 ".$staffInfoId." ".$assetsProcessState." ".$moneyProcessState);

            return true;
        }
    }


    /**
     * 借款未归还金额
     * @param $staffIds
     * @return array
     */
    public function loanNotReturned($staffIds)
    {
        $loanNotReturns = [];
        if ($staffIds) {
            $amounts = LoanModel::find([
                'columns'    => 'sum(amount) as all_amount, SUM(re_amount) as re_amount, SUM(back_amount) as back_amount, create_id ',
                'conditions' => '  create_id in ({staff_ids:array}) and (pay_status in (1,2) and status = 3 or status = 1 ) ',
                'bind'       => [
                    'staff_ids' => $staffIds,
                ],
                'group'      => 'create_id',
            ])->toArray();
            $amounts = array_column($amounts, null, 'create_id');

            foreach ($amounts as $createId => $amount) {
                $loanNotReturns[$createId] = $amount['all_amount'] - $amount['re_amount'] - $amount['back_amount'];
                if ($loanNotReturns[$createId] < 0) {
                    $loanNotReturns[$createId] = 0;
                }
                $loanNotReturns[$createId] = bcdiv($loanNotReturns[$createId], 10, 0);
            }
        }
        return $loanNotReturns;
    }

    public function reserveFund($staffIds)
    {
        $reserveFunds = [];
        if ($staffIds) {
            $allAmounts = ReserveFundApplyModel::find([
                'columns'    => ' sum(amount) as all_amount, create_id ',
                'conditions' => ' create_id in ({staff_ids:array}) and  ( status = 3 and pay_status in (1, 2) or status = 1 ) ',
                'bind'       => ['staff_ids' => $staffIds],
                'group'      => 'create_id',
            ])->toArray();
            $allAmounts = array_column($allAmounts, null, 'create_id');

            $amount1s = ReserveFundReturnModel::find([
                'columns'    => ' sum(amount) as amount1, create_id ',
                'conditions' => ' create_id in ({staff_ids:array}) and  status = 3',
                'bind'       => ['staff_ids' => $staffIds],
                'group'      => 'create_id',
            ])->toArray();
            $amount1s = array_column($amount1s, null, 'create_id');

            foreach ($allAmounts as $staffId => $allAmount) {
                $reserveFunds[$staffId] = $allAmount['all_amount'];
                if (isset($amount1s[$staffId])) {
                    $reserveFunds[$staffId] = $reserveFunds[$staffId] - $amount1s[$staffId]['amount1'];
                }
                $reserveFunds[$staffId] = bcdiv($reserveFunds[$staffId], 10);
            }
        }

        return $reserveFunds;
    }

    /**
     * @description 获取[借款未归还金额][备用金未归还金额][资产扣费总额]
     * @param $staffIds
     * @param int $dataType
     * @return array
     * @throws \Exception
     */
    public function allPrices($staffIds, int $dataType = GlobalEnums::DATA_DEFAULT): array
    {
        $loanNotReturns = [];
        $reserveFunds = [];
        $deductAmount = [];
        if ($staffIds) {
            // 1). 调用oa获取[借款未归还金额][备用金未归还金额] by马召猛
            // 2). 2023-12-04追加[资产扣费总额] by 路遥
            // https://flashexpress.feishu.cn/docx/YH4SdAzduo7WnDx3dyRcPYNHnFb
            $notReturnAmount = (new ExternalDataService())->batchGetUserOutstandingAmount($staffIds,100);
            //借款未归还金额
            $loanNotReturns = $notReturnAmount['loan_amount'];
            //备用金
            $reserveFunds  = $notReturnAmount['reserve_fund_amount'];
            //资产扣费总额
            $deductAmount = $notReturnAmount['deduct_amount'];
        }

        //快递员未回款
        $receivableAmounts = $this->receivableAmounts($staffIds);
        //其他和出纳应汇款
        $monies            = $this->getMoneys($staffIds);
        //出纳应汇款
        [$cashier, $cashier2] = $this->getAmounts($staffIds);

        $allPrices = [];
        foreach ($staffIds as $staffId) {
            $allPrices[$staffId] = 0;
            if (isset($loanNotReturns[$staffId])) {
                $allPrices[$staffId] += $loanNotReturns[$staffId];
            }
            if (isset($reserveFunds[$staffId])) {
                $allPrices[$staffId] += $reserveFunds[$staffId];
            }
            if (isset($receivableAmounts[$staffId])) {
                $allPrices[$staffId] += $receivableAmounts[$staffId]['receiveable_amount'];
            }
            if (isset($monies[$staffId]) && $monies[$staffId]['money']) {
                $allPrices[$staffId] += $monies[$staffId]['money'];
            }
            if (isset($monies[$staffId]) && $monies[$staffId]['cashier_money'] != '-1') {
                $allPrices[$staffId] += $monies[$staffId]['cashier_money'];
                continue;
            }
            if (isset($cashier[$staffId])) {
                $allPrices[$staffId] += $cashier[$staffId]['parcel_amount'];
            }
            if (isset($cashier2[$staffId])) {
                $allPrices[$staffId] += $cashier2[$staffId]['cod_amount'];
            }
        }
        if ($dataType == GlobalEnums::DATA_EXTEND) {
            return [$allPrices, $deductAmount];
        } else {
            return $allPrices;
        }

    }

    //快递员未回款
    public function receivableAmounts($staffIds)
    {
        $receivableAmounts = [];

        if ($staffIds) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(' sum(srbd.receivable_amount) as receiveable_amount, srbd.staff_info_id');
            $builder->from(['srbd' => StoreReceivableBillDetailModel::class]);
            $builder->leftJoin(StaffInfoModel::class,
                'srbd.staff_info_id = si.id and srbd.store_id = si.organization_id', 'si');
            $builder->where('srbd.state in (0,3) and  si.id in ({staff_ids:array}) ', ['staff_ids' => $staffIds]);
            $builder->groupBy('srbd.staff_info_id');
            $receivableAmounts = $builder->getQuery()->execute()->toArray();
            $receivableAmounts = array_column($receivableAmounts, null, 'staff_info_id');
        }

        return $receivableAmounts;
    }

    public function getLeaveInfos($staffIds)
    {
        $leaves = [];
        if ($staffIds && is_array($staffIds)) {
            $leaves = LeaveManagerModel::find([
                'conditions' => ' staff_info_id in ({staff_ids:array})',
                'bind'       => [
                    'staff_ids' => $staffIds,
                ],
            ])->toArray();
        }

        return $leaves;
    }

    //其他 和 出纳应汇款
    public function getMoneys($staffIds)
    {
        $monies = [];
        if ($staffIds) {
            $sql = "
        select 
                * 
        from 
                leave_money_manager 
        where id in ( select max(id) as id  from leave_money_manager where staff_info_id in (".implode(', ', $staffIds).") 
        group by staff_info_id);
        ";

            $monies = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $monies = array_column($monies, null, 'staff_info_id');
        }

        return $monies;
    }

    //出纳应汇款
    public function getAmounts($staffIds)
    {
        $amounts = [[], []];
        if ($staffIds) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(' b.collector_id, sum(b.parcel_amount) as parcel_amount ');
            $builder->from(['b' => StoreRemittanceBillModel::class]);
            $builder->leftJoin(StoreRemittanceRecordModel::class, ' b.parcel_remittance_record_id = r.id ', 'r');
            $builder->leftJoin(StaffInfoModel::class, ' b.store_id = si.organization_id and b.collector_id = si.id',
                'si');
            $builder->where('(b.parcel_state  = 0 or r.state != 1) and si.id in ({staff_ids:array})',
                ['staff_ids' => $staffIds]);
            $builder->groupBy('b.collector_id');
            $parcelAmounts = $builder->getQuery()->execute()->toArray();
            $parcelAmounts = array_column($parcelAmounts, null, 'collector_id');

            $builder = $this->modelsManager->createBuilder();
            $builder->columns(' b.collector_id, sum(b.cod_amount) as cod_amount ');
            $builder->from(['b' => StoreRemittanceBillModel::class]);
            $builder->leftJoin(StoreRemittanceRecordModel::class, ' b.cod_remittance_record_id = r.id ', 'r');
            $builder->leftJoin(StaffInfoModel::class, ' b.store_id = si.organization_id and b.collector_id = si.id',
                'si');
            $builder->where('(b.cod_state = 0 or r.state != 1) and si.id in ({staff_ids:array})',
                ['staff_ids' => $staffIds]);
            $builder->groupBy('b.collector_id');
            $codAmounts = $builder->getQuery()->execute()->toArray();
            $codAmounts = array_column($codAmounts, null, 'collector_id');

            $amounts = [$parcelAmounts, $codAmounts];
        }

        $this->logger->info(['getAmounts' => $amounts,'staffIds'=>$staffIds]);

        return $amounts;
    }


    /**
     * @param $staffId
     * @param $type
     * @param $lang
     * @throws \Mpdf\MpdfException
     */
    public function deduction_notice($staffId, $type)
    {
        ini_set("pcre.backtrack_limit", "2000000");

        $lang   = self::$language;
        $client = new ApiClient("hris", "", "staff_view", $lang);
        $params = [
            'staff_info_id' => $staffId,
        ];
        $client->setParams([$params]);
        $resultData = $client->execute();
        if (isset($resultData)
            && isset($resultData['code'])
            && $resultData['code'] == 0
            && isset($resultData['body'])
        ) {
            $body = $resultData['body'];
//            $this->view->setVar('date', date('Y-m-d'));
            $this->view->setVar('name', $body['name']);
            $this->view->setVar('identity', $body['identity']);
            $this->view->setVar('job_title', $body['job_title_name']);
            $this->view->setVar('staff_info_id', $body['staff_info_id']);
            $this->view->setVar('node_department_name', $body['node_department_name']);
            $this->view->setVar('sys_store_name', $body['sys_store_name']);
            $this->view->setVar('register_province_name', $body['register_province_name']);
            $this->view->setVar('register_city_name', $body['register_city_name']);
            $this->view->setVar('register_district_name', $body['register_district_name']);
            $this->view->setVar('register_house_num', $body['register_house_num']);
            $this->view->setVar('register_village_num', $body['register_village_num']);
            $this->view->setVar('register_village', $body['register_village']);
            $this->view->setVar('register_alley', $body['register_alley']);
            $this->view->setVar('register_street', $body['register_street']);
            $this->view->setVar('register_postcodes', $body['register_postcodes']);

            $companyConfigInfo = (new CertificateService())->getCompanyConfigInfo(['staff_info_id' => $staffId]);

            $company_name = 'Flash Express Co., Ltd.';
            $company_name_local = 'Flash Express Co., Ltd.';
            if(!empty($companyConfigInfo)) {
                $company_name = empty($companyConfigInfo['company_name']) ? 'Flash Express Co., Ltd.' : $companyConfigInfo['company_name'];
                $company_name_local = empty($companyConfigInfo['company_name_local']) ? 'Flash Express Co., Ltd.' : $companyConfigInfo['company_name_local'];
            }
            $this->view->setVar('company_name', $company_name);
            $this->view->setVar('company_name_local', $company_name_local);

            $approveInfo = (new ApproveService())->approveInfo($staffId);
            $id_card_pic = '';
            $imgUrl      = '';
            $this->logger->info(['approveInfo' => $approveInfo, 'staff_info_id' => $staffId, 'type' => $type]);
            $approveInfo = (new ApproveService())->approveInfo($staffId);
            $id_card_pic = '';
            $imgUrl      = '';

            $this->logger->info(['approveInfo' => $approveInfo, 'staff_info_id' => $staffId, 'type' => $type]);

            if (!empty($approveInfo['id_card_pic'])) {
                $id_card_pic = "<img style='width: 86mm; height:54mm' src='".str_replace('http://', 'https://',
                        $approveInfo['id_card_pic'])."' >";
            }
            if (!empty($approveInfo['sign_name_pic'])) {
                $imgUrl = "<img style='width: 80px;60px' src='".str_replace('http://', 'https://',
                        $approveInfo['sign_name_pic'])."' >";
            }

            $winhrapproveInfo = (new ApproveService())->winhrapproveInfo($staffId, $body);
            $this->logger->info(['winhrapproveInfo' => $approveInfo, 'staff_info_id' => $staffId]);
            if (empty($id_card_pic) && !empty($winhrapproveInfo['id_card_pic'])) {
                $id_card_pic = "<img style='width: 86mm; height:54mm' src='".str_replace('http://', 'https://',
                        $winhrapproveInfo['id_card_pic'])."' >";
            }
            if (empty($imgUrl) && !empty($winhrapproveInfo['sign_name_pic'])) {
                $imgUrl = "<img style='width: 80px;60px' src='".str_replace('http://', 'https://',
                        $winhrapproveInfo['sign_name_pic'])."' >";
            }
            $this->logger->info(['img_url' => $imgUrl, 'id_card_pic' => $id_card_pic, 'staff_info_id' => $staffId]);

            $this->view->setVar('id_card_pic', $id_card_pic);
            $this->view->setVar('img_url', $imgUrl);

            $leaveInfo     = (new LeaveManagerService())->assetsInfo($staffId);
            $resignInfo    = (new LeaveManagerService())->getResign($staffId);
            $leaveDate     = $resignInfo['leave_date'] ?? '';
            $assets        = $leaveInfo && isset($leaveInfo['assets']) ? $leaveInfo['assets'] : [];
            $public_assets = $leaveInfo && isset($leaveInfo['public_assets']) ? $leaveInfo['public_assets'] : [];
            $batch_assets  = $leaveInfo && isset($leaveInfo['batch_assets']) ? $leaveInfo['batch_assets'] : [];
            $assets        = array_merge($assets, $public_assets, $batch_assets);

            $this->view->setVar('goods_name_zhs', implode(',', array_unique(array_column($assets, 'goods_name_zh'))));
            $this->view->setVar('goods_name_ens', implode(',', array_unique(array_column($assets, 'goods_name_th'))));

            if (empty($body['leave_date'])) {
                $body['leave_date'] = $resignInfo['leave_date'];
            }

            if ($lang == 'th-TH' || $lang == 'th') {
                $this->view->setVar('date', $body['leave_date'] ? date('d-m-Y', strtotime($body['leave_date'])) : '');
                $this->view->setVar('leave_date',
                    $body['leave_date'] ? date('d-m-Y', strtotime($body['leave_date'])) : '');
            } else {
                $this->view->setVar('leave_date',
                    $body['leave_date'] ? date('d-m-Y', strtotime($body['leave_date'])) : '');
                $this->view->setVar('date', $body['leave_date'] ? date('d-m-Y', strtotime($body['leave_date'])) : '');
            }
            $body['leave_date'] = $body['leave_date'] ? date('d-m-Y', strtotime($body['leave_date'])) : '';
            $amount             = 0;
            $items              = [];

            $assetsIds = array_column($assets, 'assets_id');
            $logs      = $assetInfos = [];
            if ($assetsIds) {
                $logs = AssetsInfoLogModel::find([
                    'conditions' => ' assets_id in ({assets_ids:array}) and transfer_staff_id = :staff_id: and transfer_type = 1 ',
                    'bind'       => [
                        'assets_ids' => $assetsIds,
                        'staff_id'   => $staffId,
                    ],
                ])->toArray();
                $logs = array_column($logs, null, 'assets_id');

                $assetInfos = AssetsInfoModel::find([
                    'conditions' => ' id in ({assets_ids:array}) and staff_info_id = :staff_id:  and order_id != 0 ',
                    'bind'       => [
                        'assets_ids' => $assetsIds,
                        'staff_id'   => $staffId,
                    ],
                ])->toArray();

                $assetInfos = array_column($assetInfos, null, 'id');
            }


            foreach ($assets as $asset) {
                $__num = 1;

                // 公共资产多项：数量 = 使用数量 + 丢失数量
                if (isset($asset['is_public']) && isset($asset['is_batch']) && $asset['is_public'] && $asset['is_batch']) {
                    $__num = $asset['num'] + $asset['usesing_num'];
                }

                if($leaveInfo['is_new_assets_remand_state'] == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES) {
                    $goods_name_th = !empty($asset['asset_name_local']) ? $asset['asset_name_local'] : $asset['goods_name'];
                    $created_at = !empty($asset['receipted_at']) ? date('Y-m-d', strtotime($asset['receipted_at'])) : '';
                } else {
                    $goods_name_th = $asset['goods_name_th'];
                    $created_at = isset($asset['assets_id']) && isset($logs[$asset['assets_id']])
                        ?
                        date('Y-m-d', strtotime($logs[$asset['assets_id']]['created_at']))
                        :
                        (isset($asset['assets_id']) && isset($assetInfos[$asset['assets_id']]) && $assetInfos[$asset['assets_id']]['out_time'] ? date('Y-m-d',
                            strtotime($assetInfos[$asset['assets_id']]['out_time'])) : $leaveDate);
                }

                $items[] = [
                    'goods_name_zh'       => $asset['goods_name_zh'],
                    'goods_name_th'       => $goods_name_th,
                    'goods_name_en'       => $asset['goods_name_en'],
                    'num'                 => $__num,
                    'price'               => isset(AssetsService::BARCODE_PRICE_MAPS[$asset['bar_code']]) ? AssetsService::BARCODE_PRICE_MAPS[$asset['bar_code']] : 0,
                    'all_price'           => $asset['all_price'],
                    'goods_price'         => $asset['goods_price'],
                    'deduction_reason'    => $asset['deduction_reason'],
                    'value_th'            => $asset['value_th'] ?? '0.00',
                    'deduction_amount_th' => $asset['deduction_amount_th'] ?? '0.00',
                    'sn_code'             => $asset['sn_code'] ?? '',
                    'asset_code'          => $asset['asset_code'] ?? '',
                    'created_at'          => $created_at,

                ];

                $amount += $asset['all_price'];
            }

            $this->view->setVar('items', $items);
            $staffId = md5($staffId);
            if ($type == 1) {
                if ($lang != 'zh' && $lang != 'zh-CN') {
                    $file_name = "leavemanger_type1_th_{$staffId}.pdf";
//                    $this->view->render('leaveManager','staff_asset_list_th');
                    $this->view->render('leaveManager', 'shenqingzichandan_TH5');
                } else {
                    $file_name = "leavemanger_type1_zh_{$staffId}.pdf";
//                    $this->view->render('leaveManager','staff_asset_list_zh');
                    $this->view->render('leaveManager', 'shenqingzichandan_ZH5');
                }
            } else {
                if ($lang != 'zh' && $lang != 'zh-CN') {
                    $file_name = "leavemanger_type2_th_{$staffId}.pdf";
//                    $this->view->render('leaveManager','staff_apply_asset_list_th');
                    $this->view->render('leaveManager', 'zichanguanlizhongyishu_TH5');
                } else {
                    $file_name = "leavemanger_type2_zh_{$staffId}.pdf";
//                    $this->view->render('leaveManager','staff_apply_asset_list_zh');
                    $this->view->render('leaveManager', 'zichanguanlizhongyishu_ZH5');
                }
            }
            $this->view->finish();
            $content = $this->view->getContent();
//            $path = BASE_PATH . '/public/leavemanger_pdf/';
            $path = '/tmp/';
            {
                $defaultConfig = (new ConfigVariables())->getDefaults();
                $fontDirs      = $defaultConfig['fontDir'];

                $defaultFontConfig = (new FontVariables())->getDefaults();
                $fontData          = $defaultFontConfig['fontdata'];
                if ($lang == 'zh' || $lang == 'zh-CN') {
                    $config = [
                        'fontDir'       => array_merge($fontDirs, [
                            BASE_PATH.'/public/fonts',
                        ]),
                        //设置指定要使用的字体
                        'fontdata'      => $fontData + [
                            ],
                        'format'        => 'A4',
                        'mode'          => 'zh-CN',
                        'margin_left'   => 10,
                        'margin_right'  => 10,
                        'margin_top'    => 47,
                        'margin_bottom' => 67,
                        'margin_header' => 10,
                        'margin_footer' => 10,
                    ];
                } else {
                    //实例化mpdf 扩展字体 由于泰文乱码
                    $config = [
                        'fontDir'       => array_merge($fontDirs, [
                            BASE_PATH.'/public/fonts',
                        ]),
                        'fontdata'      => $fontData + [
                                'th' => [
                                    'R' => 'THSarabunNew.ttf',
                                    'B' => 'THSarabunNew Bold.ttf',
                                    'I' => 'THSarabunNew Italic.ttf',
                                ],

                            ],
                        'mode'          => 'th',
                        'format'        => 'A4',
                        'margin_left'   => 10,
                        'margin_right'  => 10,
                        'margin_top'    => 47,
                        'margin_bottom' => 47,
                        'margin_header' => 10,
                        'margin_footer' => 10,
                    ];
                }


                $mpdf = new Mpdf($config);

                $mpdf->useAdobeCJK = true;
                $mpdf->SetDisplayMode('fullpage');
                $mpdf->defaultheaderline = 0;
                $mpdf->defaultfooterline = 0;
                $data                    = [
                    'img_url'    => $imgUrl,
                    'name'       => $body['name'] ?? '',
                    'leave_date' => $body['leave_date'],
                    'company_name' => $company_name,
                    'company_logo_url_base64' => $companyConfigInfo['company_logo_url_base64'],
                    'company_address' => $companyConfigInfo['company_address'],
                ];
                $res                     = $this->getPdfHeaderAndFooter($type, $lang, $data);
                $mpdf->SetHTMLHeader($res['header']);
                $mpdf->SetHTMLFooter($res['footer']);
                $mpdf->WriteHTML($content);
                $mpdf->Output($path.$file_name, "f");

                $flashOss = new FlashOss();
                //每个国家有每个国家的名字
                $flashOss->uploadFile($file_name, $path.$file_name);
                $url = $flashOss->signUrl($file_name);
                if ($url) {
                    return [
                        'url' => $url,
                    ];
                }
            }
        } else {
            throw new ValidationException('no staff');
        }
    }

    /**
     * 离职资产扣款协议
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function getAssetsDeductionProtocolPdf($params): array
    {
        $staff_info_id = $params['staff_info_id'];

        //员工基本信息
        $staff_service = new StaffService();
        $staff_info    = $staff_service->getHrStaffInfo($staff_info_id);
        if (empty($staff_info)) {
            throw new BusinessException(self::$t->_('assets_deduction_protocol_error_1'));
        }

        if ($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            throw new BusinessException(self::$t->_('export_pdf_personal_agent_cannot_export'));
        }

        $staff_info_item = $staff_service->getHrStaffItems($staff_info_id);

        //部门 职位 网点
        $department = (new SysDepartmentService())->getDepartmentDetail($staff_info['node_department_id']);
        $job_detail = (new HrJobTitleService())->getJobTitleDetail($staff_info['job_title']);
        $store      = (new SysStoreService())->getStoreById($staff_info['sys_store_id']);

        $leave_date = $staff_info['leave_date'] ?? '';
        $sign_date  = !empty($leave_date) ? date('d-m-Y', strtotime($leave_date) - 86400) : '';

        //21 --- Signed 取员工在BY申请离职的时候，签署的离职资产协议须知的签字；如果取不到，取该员工在BY申请离职的签字
        $pdf_img_data = [];
        if ($staff_info['leave_source'] == self::LEAVE_SOURCE_BACKYARD) {
            //查找最新一条离职记录
            $staff_resign = $this->getNewStaffResign($staff_info_id);
            if (!empty($staff_resign)) {
                $sign_date = date('d-m-Y', strtotime($staff_resign['created_at']) + $this->timeOffset * 3600);

                if (!empty($staff_resign['resign_assets_sign_audit'])) {
                    $pdf_img_data[] = ['name' => 'staff_resign_sing', 'url'  => $staff_resign['resign_assets_sign_audit']];
                } else {
                    $pdf_img_data[] = ['name' => 'staff_resign_sing', 'url' => $staff_resign['resign_audit']];
                }
            }
        }

        $staff_register_province_code = $staff_info_item['REGISTER_PROVINCE'] ?? '';
        $staff_register_city_code     = $staff_info_item['REGISTER_CITY'] ?? '';
        $staff_register_district_code = $staff_info_item['REGISTER_DISTRICT'] ?? '';

        $sys_service = new SysService();
        $province    = $sys_service->getProvinceDetail($staff_register_province_code);
        $city        = $sys_service->getCityDetail($staff_register_city_code);
        $district    = $sys_service->getDistrictDetail($staff_register_district_code);

        //paf 需要字段
        //1 --- Made at 取导出该协议导出的时间，按照日-月-年展示，如23-06-2023 改为显示固定值
        //$pdf_data['export_date'] = date('d-m-Y', time());
        //2 --- Date 取该员工在BY申请离职时，签字的日期，按照日-月-年展示，如23-06-2023；如果该员工不是BY申请离职的，取自该员工的最后工作日，如果最后工作日为空，取离职日期
        $pdf_data['sign_date'] = $sign_date;
        //3 --- I (Mr./Mrs./Ms.) 取HCM-flash员工管理-基本信息-姓名字段
        //22 --- 展示员工的姓名字段
        $pdf_data['staff_name'] = $staff_info['name'];
        //4 --- ID card/Passport number 取HCM-flash员工管理-个人信息-身份证/护照
        $pdf_data['identity'] = $staff_info['identity'];
        //5 --- Position 取HCM-flash员工管理-基本信息-职位字段
        $pdf_data['staff_job_title_name'] = $job_detail['job_name'] ?? '';
        //6 --- Employee number 取HCM-flash员工管理-基本信息-工号字段
        $pdf_data['staff_info_id'] = $staff_info_id;
        //7 --- Department 取HCM-flash员工管理-基本信息-部门字段
        $pdf_data['staff_department_name'] = $department['name'] ?? '';
        //8 --- Branch 取HCM-flash员工管理-基本信息-所属网点字段
        $pdf_data['staff_store_name'] = $store['name'] ?? '';
        //9 --- Residing at No. 取HCM-flash员工管理-个人信息-户口所在地地区-门牌号
        $pdf_data['staff_register_village_num'] = $staff_info_item['REGISTER_HOUSE_NUM'] ?? '';
        //10 --- Moo 取HCM-flash员工管理-个人信息--户口所在地地区-村庄
        $pdf_data['staff_register_village'] = $staff_info_item['REGISTER_VILLAGE'] ?? '';
        //11 --- Soi/Road 取HCM-flash员工管理-个人信息--户口所在地地区-街道
        $pdf_data['staff_register_street'] = $staff_info_item['REGISTER_STREET'] ?? '';
        //12 --- Tambol/Sub-district 取HCM-flash员工管理-个人信息-户口所在地地区-乡
        $pdf_data['staff_register_district'] = $district['name'] ?? $staff_register_district_code;
        //13 --- Amphoe/District 取HCM-flash员工管理-个人信息-户口所在地地区-市
        $pdf_data['staff_register_city'] = $city['name'] ?? $staff_register_city_code;
        //14 --- Province 取HCM-flash员工管理-个人信息-户口所在地地区-省
        $pdf_data['staff_register_province'] = $province['name'] ?? $staff_register_province_code;
        //15 --- Postal Code 取HCM-flash员工管理-个人信息-户口所在地地区-邮编
        $pdf_data['staff_register_postcodes'] = $staff_info_item['REGISTER_POSTCODES'] ?? '';

        //资产列表 1. 只展示离职员工名下：未还，已还受损坏，归还缺少零件的资产。如果员工名下的已还完好，或者离职之后转到他人名下的“在他人名下”的资产，不在此协议上展示。
        //17 --- No. 从1开始递加，展示员工在HCM-flash员工离职管理里展示的资产，按照资产的ID正序排列展示。
        //18 --- Device/Item 资产名称
        //19 --- Brand/Model 规格型号字段
        //20 --- Cost 资产的扣费金额字段
        $pdf_first_page_assets_list  = [];
        $pdf_second_page_assets_list = [];
        $language = self::$language;
        if(strtolower(self::$language) != 'th') {
            $language = 'en';
        }
        $pdf_data['total_deduct_amount'] = '0.00';//总计扣费金额
        $assets_list_result = $this->getLeaveAssetsListBySvc($staff_info_id, $language);
        if (isset($assets_list_result['code']) && $assets_list_result['code'] == 1 && $assets_list_result['data']) {
            $assets_list = $assets_list_result['data']['assets_list'];
            $index       = 0;
            foreach ($assets_list as $key => $value) {
                $pdf_data['total_deduct_amount'] = bcadd($pdf_data['total_deduct_amount'], $value['deduct_amount'], 2);
                $index++;
                if ($index <= 5) {
                    $pdf_first_page_assets_list[] = [
                        'index'         => $index,
                        'asset_name'    => $value['asset_name'],    //资产名称
                        'deduct_amount' => number_format($value['deduct_amount'], 2), //扣费金额
                        'asset_model'   => $value['model'],          //规格型号
                    ];
                } else {
                    $pdf_second_page_assets_list[] = [
                        'index'         => $index,
                        'asset_name'    => $value['asset_name'],    //资产名称
                        'deduct_amount' => number_format($value['deduct_amount'], 2), //扣费金额
                        'asset_model'   => $value['model'],          //规格型号
                    ];
                }
            }
            $pdf_data['total_deduct_amount'] = number_format($pdf_data['total_deduct_amount'], 2);
        }

        $pdf_data['first_page_assets_list'] = $pdf_first_page_assets_list;
        if (!empty($pdf_second_page_assets_list)) {
            $pdf_data['second_page_assets_list'] = $pdf_second_page_assets_list;
        }

        $certificateService = new CertificateService();

        $companyId = $certificateService->getCompanyId($staff_info['node_department_id'], $staff_info['nationality']);

        if($staff_info['hire_type'] == HrStaffInfoModel::HIRE_TYPE_UN_PAID) {
            $companyId = Enums\CertificateEnums::COMPANY_FLASH_EXPRESS;
        }

        $companyConfigInfo = $certificateService->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);

        $pdf_data['company_name'] = $companyConfigInfo['company_name'];
        $pdf_data['company_name_local'] = $companyConfigInfo['company_name_local'];
        [$header, $footer] = $this->getDeductionPdfHeaderFooter($companyConfigInfo);

        $pdfOptions = [
            'headerTemplate'      => $header,
            'footerTemplate'      => $footer,
            'displayHeaderFooter' => true,
        ];

        $tmpPath = BASE_PATH . '/app/views/leaveManager/deduction_pdf_th.ftl';
        if (strtolower(self::$language) != 'th') {
            $tmpPath = BASE_PATH . '/app/views/leaveManager/deduction_pdf_en.ftl';
        }

        $temp_url = $this->getPdfTemp($tmpPath);

        $res = (new FormPdfServer())->getInstance()->generatePdf($temp_url, $pdf_data, $pdf_img_data, $pdfOptions, $staff_info_id);
        if (isset($res['object_url'])) {
            return ['url' => $res['object_url']];
        }
        return ['url' => ''];
    }

    /**
     * 查找最新一条离职记录
     * @param $staff_info_id
     * @return array
     */
    public function getNewStaffResign($staff_info_id): array
    {
        $resign_info = StaffResignModel::findFirst([
            'conditions' => "submitter_id = :submitter_id:",
            'bind'       => ['submitter_id' => $staff_info_id],
            'order'      => 'resign_id desc',
        ]);
        if (empty($resign_info)) {
            return [];
        }

        $resign_info = $resign_info->toArray();
        //离职资产协议须知签字
        $attachment  = SysAttachmentModel::findFirst([
            'conditions' => "oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key:",
            'bind'       => [
                'oss_bucket_type' => 'RESIGN_ASSETS_SIGN_AUDIT',
                'oss_bucket_key'  => $resign_info['resign_id'],
            ],
        ]);
        $attachment = !empty($attachment) ? $attachment->toArray() : [];

        //离职申请签名
        $attachment_staff_sing = SysAttachmentModel::findFirst([
            'conditions' => "oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key:",
            'bind'       => [
                'oss_bucket_type' => 'RESIGN_AUDIT',
                'oss_bucket_key'  => $resign_info['resign_id'],
            ],
            'order' => 'created_at asc',
        ]);
        $attachment_staff_sing = !empty($attachment_staff_sing) ? $attachment_staff_sing->toArray() : [];

        $img_prefix = env("img_prefix", "http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/");

        $resign_audit              = $attachment_staff_sing['object_key'] ?? '';
        $resign_assets_sign_audit  = $attachment['object_key'] ?? '';

        $resign_info['resign_audit']             = !empty($resign_audit) ? $img_prefix . $resign_audit : '';
        $resign_info['resign_assets_sign_audit'] = !empty($resign_assets_sign_audit) ? $img_prefix . $resign_assets_sign_audit : '';

        return $resign_info;
    }

    public function assetsByStaffId($staffId, $lang)
    {
        $lang = substr(strtolower($lang), 0, 2);
        $lang = !in_array($lang,['zh','en','th']) ? 'en' : $lang;
        $sql  = "--
                SELECT
                    ai.id,
                    ai.express,
                    ai.express_sn,
                    ai.handover_type,
                    CONVERT_TZ( ai.out_time, '+00:00', '{$this->timeZone}' ) AS out_time,
                    ag.bar_code,
                    ag.goods_name_{$lang} as goods_name,
                    ag.goods_name_en,
                    ag.goods_name_zh,
                    ag.goods_name_th,
                    ag.value_th,
                    ag.is_public,
                    ai.assets_goods_id,
                    ai.asset_code,
                    CONVERT_TZ( ai.transfer_at, '+00:00', '{$this->timeZone}' ) AS transfer_at,
                    ai.transfer_state,
                    ai.transfer_type,
                    ai.staff_info_id,
                    ai.sn_code,
                    ai.pno,
                    ai.operate_status
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.staff_info_id = '{$staffId}' AND ai.state=1";

        $sql .= " AND ag.is_public in(0, 1 )AND ag.is_batch = 0 ";

        $batchsql = "--
                SELECT
                    ai.id,
                    ai.express,
                    ai.express_sn,
                    ai.handover_type,
                    CONVERT_TZ( ai.out_time, '+00:00', '{$this->timeZone}' ) AS out_time,
                    ag.bar_code,
                    ag.goods_name_{$lang} as goods_name,
                    ag.goods_name_en,
                    ag.goods_name_zh,
                    ag.goods_name_th,
                    ag.value_th,
                    ag.is_public,
                    ai.assets_goods_id,
                    ai.asset_code,
                    CONVERT_TZ( ai.transfer_at, '+00:00', '{$this->timeZone}' ) AS transfer_at,
                    ai.transfer_state,
                    ai.transfer_type,
                    ai.staff_info_id,
                    ai.sn_code,
                    ai.pno,
                    ai.operate_status,
                    count(*) as count
                FROM
                    assets_info ai
                left join
                    assets_goods ag
                ON
                    ai.assets_goods_id = ag.id
                WHERE
                    ai.staff_info_id = '{$staffId}' AND ai.state=1";

        $batchsql     .= " AND ag.is_public =1 AND ag.is_batch = 1 group by ai.assets_goods_id ";
        $batch_assets = $this->getDI()->get('db_backyard')->query($batchsql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $assets       = $this->getDI()->get('db_backyard')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

        $results = [];
        if ($batch_assets) {
            foreach ($batch_assets as $item) {
                $results[] = [
                    "bar_code"   => $item['bar_code'],
                    "name"       => $item['goods_name_'.str_replace("-CN", "", $lang)],
                    "asset_code" => $item['asset_code'],
                    "sn_code"    => $item['sn_code'],
                    "price"      => $item['value_th'],
                    "num"        => $item['count'],
                ];
            }
        }

        foreach ($assets as $asset) {
            if ($asset['is_public']) {
                $results[] = [
                    "bar_code"   => $asset['bar_code'],
                    "name"       => $asset['goods_name_'.str_replace("-CN", "", $lang)],
                    "asset_code" => $asset['asset_code'],
                    "sn_code"    => $asset['sn_code'],
                    "price"      => $asset['value_th'],
                    "num"        => 1,
                ];
            } else {
                $results[] = [
                    "bar_code"   => $asset['bar_code'],
                    "name"       => $asset['goods_name_'.str_replace("-CN", "", $lang)],
                    "asset_code" => $asset['asset_code'],
                    "sn_code"    => $asset['sn_code'],
                    "price"      => $asset['value_th'],
                    "num"        => 1,
                ];
            }
        }

        return $results;
    }

    /**
     * 员工资产新版
     * @param $staff_info_id
     * @param $lang
     * @return array|mixed
     */
    public function assetsByStaffIdNew($staff_info_id, $lang)
    {
        $client = new ApiClient('oa_rpc', '', 'get_leave_assets_staff_count', $lang);
        $client->setParamss(['staff_id' => $staff_info_id]);
        $res  = $client->execute();
        $list = [];
        if (isset($res['code']) && $res['code'] == 1) {
            $list = $res['data'];
        } else {
            $this->getDI()->get('logger')->write_log([
                'function'      => 'assetsByStaffIdNew',
                'res'           => $res,
                'staff_info_id' => $staff_info_id,
            ], 'error');
        }
        return $list;
    }


    public function getPdfHeaderAndFooter($type, $lang, $data)
    {
        $img_url    = $data['img_url'] ?? '';
        $name       = $data['name'] ?? '';
        $leave_date = $data['leave_date'] ?? '';
        $company_name = $data['company_name'] ?? '';
        $company_logo_url_base64 = $data['company_logo_url_base64'] ?? '';
        $company_address = $data['company_address'] ?? '';
        switch ($type) {
            case 1:
                if ($lang == 'zh' || $lang == 'zh-CN') {
                    $header = <<<HTML
<div>
    <table style="width: 100%;">
        <tr>
            <td style="text-align: left;">
                <div style="height: 15mm;line-height: 15mm;font-size: 21px;font-weight: bold;">
                    $company_name
                </div>
            </td>
            <td></td>
            <td style="text-align: right;">
                <img style="width: 34mm; height: 11mm;" src="$company_logo_url_base64">
            </td>
        </tr>
    </table>
    <div style="border-bottom: 1mm solid #000;margin-top: 0mm;"></div>
    <p style="text-align: center;margin: 5mm 0 6mm;font-size: 18px;font-weight: 700;font-family: simsun;">申请资产单</p>
</div>
HTML;
                    $footer = <<<HTML
<div>
    <div>
        <table style="border: 0px; margin-left: 80mm;">
                <tr style="height: 8mm;">
                    <td  style="font-size: 16px;width: 33mm;text-align: right;">签字</td>
                    <td style="font-size: 16px;text-align: center;width:100mm">$img_url</td>
                    <td style="font-size: 16px;width: 33mm;text-align: left;">同意 (员工)</td>
                </tr>
                <tr style="height: 8mm;">
                    <td style="font-size: 16px;width: 33mm;text-align: right;">(</td>
                    <td style="font-size: 16px;text-align: center;"><span>$name</span></td>
                    <td style="font-size: 16px;width: 33mm;text-align: left;">)</td>
                </tr>
            </table> 
    </div>
    <p style="text-align: center;margin-top: 6mm; color: #999999;font-size: 16px;font-weight: bold;padding: 1mm 20mm;">
        $company_address
    </p>
</div>
HTML;
                } else {
                    $header = <<<HTML
<div>
    <table style="width: 100%;">
        <tr>
            <td style="text-align: left;">
                <div style="height: 15mm;
                    line-height: 15mm;
                    font-weight: bold;
                    font-size: 28px;">
                    $company_name
                </div>
            </td>
            <td></td>
            <td style="text-align: right;">
                <img style="width: 34mm; height: 11mm;" src="$company_logo_url_base64">
            </td>
        </tr>
    </table>
    <div style="border-bottom: 1mm solid #000;
        margin-top: 0mm;"></div>
    <p style="text-align: center;
        font-weight: 600;
        margin: 5mm 0 10mm;
        font-size: 20px;">หนังสือเบิกอุปกรณ์ประจำตัวพนักงาน</p>
</div>
HTML;
                    $footer = <<<HTML
<div>
    <div>
        <table border="0" style="border: 0px;margin-left: 125mm;width: 90mm;">
            <tr>
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px; text-align: right;">ลงชื่อ</td>
                <td style="text-align: center;">$img_url</td>
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px; text-align: left;">อุปกรณ์ (พนักงาน)</td>
            </tr>
            <tr>
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px;text-align: right;">(</td>
                <td style="font-size: 18px;min-width: 18mm;font-size: 18px; text-align: center;">$name</td>
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px;text-align: left;">)</td>
            </tr>
        </table>
    </div>
    <p style="text-align: center;margin-top: 6mm;font-size: 18px;">
        $company_address
    </p>
</div>
HTML;
                }
                break;
            default:
                if ($lang == 'zh' || $lang == 'zh-CN') {
                    $header = <<<HTML
<div>
    <table style="width: 100%;">
        <tr>
            <td style="text-align: left;">
                <div class="title">
                    $company_name
                </div>
            </td>
            <td></td>
            <td style="text-align: right;">
                <img style="width: 34mm; height: 11mm;" src="$company_logo_url_base64">
            </td>
        </tr>
    </table>
    <div style="border-bottom: 1mm solid #000;
        margin-top: 0mm;"></div>
    <p style="text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin: 5mm 0;">资产管理同意书</p>
</div>
HTML;
                    $footer = <<<HTML
<div>
    <div>
        <table border="0" style="border: 0px; margin-left: 90mm;">
                    <tr>
                        <td  style="font-size: 16px;width: 33mm; height: 8mm;text-align: right">签字</td>
                        <td style="text-align: center;width: 100mm">$img_url</td>
                        <td  style="font-size: 16px;width: 38mm;height: 8mm;text-align: left;">同意者(员工)</td>
                    </tr>
                    <tr>
                        <td style="font-size: 16px;width: 33mm; text-align: right;">(</td>
                        <td style="font-size: 16px;text-align: center;width: 100mm"><span>$name</span></td>
                        <td style="font-size: 16px;width: 38mm;text-align: left;">)</td>
                    </tr>
                    <tr >
                        <td></td>
                        <td style="font-size: 16px;text-align: center;min-width: 83mm">$leave_date</td>
                        <td></td>
                    </tr>
                </table>
    </div>
    <p style="text-align: center;margin-top: 6mm;color: #999;font-size: 16px;padding: 0 20mm;">
        $company_address
    </p>
</div>
HTML;
                } else {
                    $header = <<<HTML
<div>
    <table style="width: 100%;">
        <tr>
            <td style="text-align: left;">
                <div class="title">
                    $company_name
                </div>
            </td>
            <td></td>
            <td style="text-align: right;">
                <img style="width: 34mm; height: 11mm;" src="$company_logo_url_base64">
            </td>
        </tr>
    </table>
    <div style="border-bottom: 1mm solid #000;
        margin-top: 0mm;"></div>
    <p style="text-align: center;
        font-size: 18px;
        font-weight: bold;
        margin: 5mm 0;">หนังสือให้ความยินยอมในการบริหารจัดการทรัพย์สิน</p>
</div>
HTML;
                    $footer = <<<HTML
<div style="margin-bottom: 3mm;">
    <div style=" width: 100mm;margin-left: 120mm;margin-bottom: 3mm;">
        <table border="0" style="width: 100%;">
            <tr style="height: 8mm;">
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px; text-align: right;">ลงชื่อ</td>
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px; text-align: center;">$img_url</td>
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px; text-align: left;">อุปกรณ์(พนักงาน)</td>
            </tr>
            <tr style="height: 8mm;">
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px; text-align: right;">(</td>
                <td style="font-size: 18px;min-width: 18mm;font-size: 18px; text-align: center;">$name</td>
                <td style="font-size: 18px;min-width: 18mm; max-width: 33mm;font-size: 18px;text-align: left;">)</td>
            </tr>
        </table>
    </div>
    <p style="text-align: center;margin-top: 6mm;font-size: 18px;">
        $company_address
    </p>
</div>
HTML;
                }
        }

        return [
            "header" => $header,
            "footer" => $footer,
        ];
    }

    /**
     * 查询员工名下资产与钱款
     * @param $params
     * @return array
     */
    public function userStateInfo($params)
    {
        $staff_info_id = $params['staff_info_id'] ?? 0;
        $hold_id       = $params['hold_id'] ?? 0;
        $result        = [];
        try {
            $result['staff_info']           = (new StaffSearchService())->getStaffInfo($staff_info_id);
            $result['assets_process_state'] = self::$t->_("assets_state_" . self::ASSETS_STATE_UNPROCESSED);
            // 判断是否操作过
            $leaveInfo = $this->getLeaveInfo($staff_info_id);

            //新需求 返回该员工资产是否 被资产部操作过
            $result['assets_remand_state']        = empty($leaveInfo) ? 0 : $leaveInfo['assets_remand_state'];
            $result['is_has_superior']            = empty($leaveInfo) ? 0 : $leaveInfo['is_has_superior'];
            $result['is_new_assets_remand_state'] = empty($leaveInfo) ? (string)LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES : $leaveInfo['is_new_assets_remand_state'];
            if ($result['is_new_assets_remand_state'] == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_NO) {
                if ($leaveInfo && $leaveInfo['assets_operate_version']) {
                    //操作过
                    $result['assets']               = $this->getSysStaffAssets($staff_info_id,
                        $leaveInfo['assets_operate_version']);
                    $result['assets_remark']        = $leaveInfo['assets_operate_remark'];
                    $result['assets_process_state'] = self::$t->_("assets_state_" . $leaveInfo['assets_remand_state']);
                } else {
                    // 未操作过 取当前拥有的资产
                    $result['assets']        = $this->getCurrentStaffAssets($staff_info_id);
                    $result['assets_remark'] = '';
                }

                $goodsList = $this->assetsGoods([], $staff_info_id);
                if (!$goodsList) {
                    $result['assets_process_state'] = self::$t->_("assets_state_" . self::ASSETS_STATE_PROCESSED);
                }
            } else {
                $assets_remand_state            = $leaveInfo['assets_remand_state'] ?? '';
                $result['assets_process_state'] = !empty($assets_remand_state) ? self::$t->_("assets_state_" . $assets_remand_state) : self::$t->_("assets_state_" . self::ASSETS_STATE_PROCESSED);
                //新逻辑 读取oa离职资产管理
                $new_leave_assets = $this->getLeaveAssetsDetail($staff_info_id);
                $assets           = $new_leave_assets['assets'] ?? [];
                $public_assets    = $new_leave_assets['public_assets'] ?? [];

                $new_assets       = array_merge($assets, $public_assets);
                $result['assets'] = [];
                foreach ($new_assets as $key => $value) {
                    $result['assets'] [] = [
                        'name'                => $value['goods_name'],
                        'asset_code'          => $value['asset_code'],
                        'sn_code'             => $value['sn_code'],
                        'operate_status_text' => $value['operate_status_text'],
                        'all_price'           => $value['all_price'],
                    ];
                }
            }

            $result['staff_info']['approve_status_text'] = '';
            //直接取离职管理审批状态
            if ($leaveInfo && isset($leaveInfo['approval_status'])) {
                $result['staff_info']['approve_status_text'] = $leaveInfo['approval_status'] ? self::$t->_("by_state_" . $leaveInfo['approval_status']) : '';
            }

            if ($leaveInfo && $leaveInfo['money_operate_version']) {
                $result['money_process_state'] = self::$t->_("assets_state_" . $leaveInfo['money_remand_state']);
                $leaveMoney                    = $this->getSysMoney($staff_info_id,
                    $leaveInfo['money_operate_version']);
                //$result['unpaid_money'] = $leaveMoney['money'];
                //光辉 - 取合计
                $money_info             = $this->moneyInfo($staff_info_id);
                $all_price              = $money_info['all_price'] ?? '';
                $result['unpaid_money'] = !empty($all_price) ? ($all_price / 100) : '';
                $result['money_remark'] = $leaveMoney['remark'];
            } else {
                $result['money_process_state'] = self::$t->_("assets_state_" . $leaveInfo['money_remand_state'] ?? self::ASSETS_STATE_UNPROCESSED);
                //光辉 - 取合计
                $money_info             = $this->moneyInfo($staff_info_id);
                $all_price              = $money_info['all_price'] ?? '';
                $result['unpaid_money'] = !empty($all_price) ? ($all_price / 100) : '';
                $result['money_remark'] = '';
            }
        } catch (\Exception $e) {
            $this->logger->error([
                'function' => 'LeaveManagerService-userStateInfo',
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'params'   => $params,
            ]);
        }
        return $result;
    }

    public function fixHold($submit_id, $staffInfo)
    {
        $data                  = [];
        $data['release_state'] = 2;
        $data['release_time']  = gmdate("Y-m-d H:i:s", time() + ($this->timeOffset) * 3600);;
        $data['release_submit_id'] = $submit_id;

        $data['handle_progress'] = 3;
        $flag = $this->getDI()->get("db_backyard")->updateAsDict("hold_staff_manage", $data, ["conditions" => "staff_info_id = {$staffInfo['staff_info_id']} " ]);

        if (!$flag) {
            return 'update hold error';
        }
        $params = [
            'operator_id'       => $submit_id,
            'type'              => 2,                          //(1 hold ; 2 释放)
            'staff_info_id'     => $staffInfo['staff_info_id'],//员工id
            'payment_markup'    => '102',
            'stop_payment_type' => $staffInfo['stop_payment_type'],
        ];

        $lang = self::$language;

        return $this->getApiDatass('hris', '', 'staff_hold', $lang, $params);
    }

    /**
     * 离职申请 撤销 驳回
     * 同步hold
     *
     * @param $staffId
     */
    private function syncHoldToCompleted($staffId,$src)
    {
        $di     = $this->getDI();
        if ($src == self::LEAVE_SRC_COMPANY_TERMINATION) {
            //公司解约的，只有在审批通过的才会hold
            return true;
        }

        $model = StaffResignModel::findFirst([
            'columns'    => " leave_date,  CONVERT_TZ(created_at,  '+00:00', '" . $di['config']['application']['timeZoneOfThailand'] . "') as created_at, submitter_id, resign_id",
            'conditions' => ' submitter_id = :submitter_id: ',
            'bind'       => ['submitter_id' => $staffId],
            'order'      => ' resign_id desc ',
        ]);

        if (!empty($model)) {
            $resign         = $model->toArray();
            $nextMonthType1 = date("Y-m-05", strtotime($resign['created_at']." +1 month"));
            $nextMonthType2 = date("Y-m-15", strtotime($resign['created_at']." +1 month"));
            $holdType       = '';
            if ($resign['leave_date'] <= $nextMonthType1) {
                $holdType = '1,2';
            } else {
                if ($resign['leave_date'] <= $nextMonthType2) {
                    $holdType = '2';
                }
            }
            if ($holdType) {
                // 满足同步hold 条件
                $holdStaffManages = HoldStaffManageModel::find([
                    'conditions' => ' staff_info_id = :staff_id: and hold_source = :hold_source: and is_delete = 0',
                    'bind' => [
                        'staff_id' => $staffId,
                        'hold_source' => 5,
                    ],
                ]);

                if ($holdStaffManages) {
                    foreach ($holdStaffManages as $holdStaffManage) {
                        $holdStaffManage->handle_progress = 3;
                        $holdStaffManage->update_at       = gmdate('Y-m-d H:i:s',
                            time() + $this->config->application->add_hour * 3600);
                        $holdStaffManage->save();
                    }
                }

                $holdManageBll = new HoldManageService();
                $holdManageBll->syncReleaseByStaffId($staffId, 1);
                $holdManageBll->syncReleaseByStaffId($staffId, 2);
            }
        }
    }

    /**
     * 根据用户id修改leave_source
     * @param $user_id
     * @param int $leave_source =5 backyard离职申请
     */

    public function updateLeaveSource($user_id, $leave_source = 5)
    {
        $postData = [
            "operater"      => -1,
            "staff_info_id" => $user_id,
            "leave_source"  => $leave_source,
            "src"           => 'HCM',
        ];
        $client   = new ApiClient("hris", "", "update_staff_info");
        $client->setParamss($postData);
        $res = $client->execute();
        if (isset($res['code']) && $res['code'] == 1) {
            $this->getDI()->get('logger')->write_log("backyard提交 修改leave_source=5,user_id=".$user_id, 'info');
            return true;
        } else {
            $this->getDI()->get('logger')->write_log("backyard提交 修改leave_source=5,user_id=".$user_id.' res '.json_encode($res,
                    JSON_UNESCAPED_UNICODE), 'error');
            return false;
        }
    }


    /**
     * ToDo 使用中 该方法仅限by离职申请试用，不允许继续扩展  ！！！
     * @param $staffId
     * @param $approvalStatus
     * @param $leaveDate
     * @param $src
     * @return true
     * @throws \Exception
     */
    public function sycnApprovalStatus($staffId, $approvalStatus, $leaveDate,$src,$applyResignTime)
    {
        if (!isset(self::APPROVALSTATUS_DESC[$approvalStatus])) {
            throw new \Exception(" 审批状态值1-5 ");
        }
        if (in_array($approvalStatus, [self::APPROVAL_STATUS_DISMISSED, self::APPROVAL_STATUS_REVOKED])) {
            $this->syncHoldToCompleted($staffId,$src);
        }

        $db = BackyardBaseModel::beginTransaction($this);
        try {
            $leaveManagerModel       = LeaveManagerModel::findFirst(
                [
                    "conditions" => "staff_info_id = :staff_info_id:",
                    "bind"       => [
                        "staff_info_id" => $staffId,
                    ],
                ]
            );
            $asset_state_unprocessed = self::ASSETS_STATE_UNPROCESSED;

            if (!empty($leaveManagerModel)) {
                $leaveManagerModel->approval_status = $approvalStatus;
                if (!empty($leaveDate)) {
                    $leaveManagerModel->leave_date = $leaveDate;
                }
                $leaveManagerModel->approval_time = gmdate("Y-m-d H:i:s");

                if ($approvalStatus == self::APPROVAL_STATUS_REVOKED) {
                    $leaveManagerModel->is_has_superior          = 0;
                    $leaveManagerModel->superior_id              = 0;
                    $leaveManagerModel->superior_operate_time    = '0000-00-00 00:00:00';
                    $leaveManagerModel->superior_operate_version = 0;
                }
                if (empty($leaveManagerModel->apply_resign_time) && $approvalStatus == self::APPROVAL_STATUS_APPROVED && $src == self::LEAVE_SRC_STAFF_REGIN) {
                    $leaveManagerModel->apply_resign_time = $applyResignTime ?: gmdate("Y-m-d H:i:s");
                }

                if ($src == self::LEAVE_SRC_COMPANY_TERMINATION) {
                    $leaveManagerModel->assets_remand_state = $asset_state_unprocessed;
                    $leaveManagerModel->money_remand_state  = $asset_state_unprocessed;
                }

                if ($approvalStatus == self::APPROVAL_STATUS_WAIT) {
                    $leaveManagerModel->assets_remand_state = $asset_state_unprocessed;
                    $leaveManagerModel->money_remand_state  = $asset_state_unprocessed;
                    $leaveManagerModel->apply_resign_time   = $applyResignTime ?: gmdate("Y-m-d H:i:s");
                }
            } else {
                $leaveManagerModel                      = new LeaveManagerModel();
                $leaveManagerModel->staff_info_id       = $staffId;
                $leaveManagerModel->created_at          = gmdate("Y-m-d H:i:s");
                if ($src == self::LEAVE_SRC_STAFF_REGIN) {
                    $leaveManagerModel->apply_resign_time   = $applyResignTime ?: gmdate("Y-m-d H:i:s");
                }
                $leaveManagerModel->approval_status     = $approvalStatus;
                $leaveManagerModel->leave_date          = $leaveDate;
                $leaveManagerModel->approval_time       = gmdate("Y-m-d H:i:s");
                $leaveManagerModel->assets_remand_state = $asset_state_unprocessed;
                $leaveManagerModel->money_remand_state  = $asset_state_unprocessed;
            }

            $leaveManagerModel->save();

            //更新为离职过 申请就算
            $stmt = $this->getDI()->get('db_backyard')->prepare("update hr_staff_info set is_history_leave = 1 where staff_info_id = :staff_info_id");
            $stmt->bindValue("staff_info_id", $staffId, \PDO::PARAM_INT);
            $stmt->execute();

            if (in_array($approvalStatus, [self::APPROVAL_STATUS_REVOKED, self::APPROVAL_STATUS_DISMISSED])) {
                $this->getDI()->get('logger')->write_log("开始同步hold " . $staffId, 'info');
                $result = (new HoldManageService())->synchronizeHoldProgress([
                    "staff_info_id"   => $staffId,
                    "handle_progress" => self::ASSETS_STATE_PROCESSED,
                ]);
                $this->logger->info([
                    'message'       => '同步hold结果，backyard来源',
                    'staff_info_id' => $staffId,
                    'result'        => $result,
                ]);
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return true;
    }

    //获取最新申请一条离职申请
    public function getStaffResignMaxList($staff_info_ids) {
        $resignData = [];
        if(!empty($staff_info_ids)) {
            //$staff_resign_table = StaffResignModel::class;
            //$resignData = $this->modelsManager->executeQuery(
            //    "select * from {$staff_resign_table} where resign_id in (select max(resign_id) resign_id from {$staff_resign_table} where submitter_id in ({ids:array}) group by submitter_id)",
            //    ['ids' => $staff_info_ids])->toArray();

            $staff_info_ids_str = implode(',', $staff_info_ids);
            $sql = "select * from `staff_resign` where resign_id in(select max(resign_id)  resign_id from `staff_resign` where submitter_id in(". $staff_info_ids_str .") group by submitter_id)";
            $resignData = $this->getDI()->get("db_backyard")->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            $resignData = !empty($resignData) ? array_column($resignData, null, 'submitter_id') : [];
        }
        return $resignData;
    }

    /**
     * 有效的离职申请
     * @param $staff_info_id
     * @param $date_at
     * @return array
     */
    public function getEffectiveStaffResign($staff_info_id, $date_at): array
    {
        $res = StaffResignModel::findFirst(
            [
                "conditions" => "submitter_id = :staff_info_id: and status in ({status:array}) and leave_date >= :leave_date: and source = :source:",
                "bind"       => [
                    "staff_info_id" => $staff_info_id,
                    "leave_date"    => $date_at,
                    "source"        => StaffResignModel::SOURCE_BY,
                    "status"        => [
                        ApprovalEnums::APPROVAL_STATUS_APPROVAL,
                        ApprovalEnums::APPROVAL_STATUS_TIMEOUT,
                    ],
                ],
                "order"      => "resign_id desc",
            ]
        );
        return $res ? $res->toArray() : [];
    }


    public function getEffectiveProbationResign($staff_info_id, $date_at)
    {

        //判断重复
        $res = HrProbationResignModel::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_info_id: AND status = :status: AND  leave_date >= :leave_date: AND is_deleted = :is_deleted:',
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                    'is_deleted'    => Enums\GlobalEnums::NO_DELETED,
                    'status'        => HrProbationResignModel::STATUS_SUCCESS,
                    'leave_date'    => $date_at,
                ],
                'order'=>'id desc',
            ]
        );
        return $res ? $res->toArray() : [];
    }



    public function getEffectiveContractExpire($staff_info_id, $date_at)
    {
        //合同到期日是离职日期的前一天
        $date_at = date('Y-m-d', strtotime($date_at) - 86400);
        //判断重复
        $res = MessagePdf::findFirst(
            [
                'conditions' => 'staff_info_id = :staff_info_id: AND module_category in ({module_category:array}) AND  contract_end_date >= :contract_end_date: ',
                'bind'       => [
                    'staff_info_id'     => $staff_info_id,
                    'contract_end_date' => $date_at,
                    'module_category'   => [MessagePdf::MODULE_CATEGORY_1,MessagePdf::MODULE_CATEGORY_IC_CONTRACT_MESSAGE],
                ],
                'order'=>'id desc',
            ]
        );
        return $res ? $res->toArray() : [];
    }


    /**
     * 获取最新离职数据
     * @param $staffInfoIds
     * @return array
     */
    public function getStaffProbationMaxList($staffInfoIds) {
        $data = [];
        if(!empty($staffInfoIds)) {
            $staffInfoIds = implode(',', $staffInfoIds);
            $sql = "select * from `hr_probation_resign` where `id` in (select max(id) id from `hr_probation_resign` where staff_info_id in(". $staffInfoIds .") and is_deleted=0 group by staff_info_id)";
            $data = $this->getDI()->get("db_backyard")->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $data = !empty($data) ? array_column($data, null, 'staff_info_id') : [];
        }

        return $data;
    }

    //验证工号数据权限范围
    public function getStaffDataPermission($builder, $params) {
        $staff_info_id = $params['staff_info_id'];

        //获取员工角色
        $staffPosition = HrStaffInfoPositionModel::find([
            'staff_info_id = :staff_info_id:',
            'bind' => [
                'staff_info_id' => $staff_info_id,
            ],
            'columns' => 'position_category',

        ])->toArray();
        $staff_position_ids = array_column($staffPosition, 'position_category');

        //获取配置权限角色
        $configureList = SettingEnvModel::getMultiEnvByCode(['leave_manage_data_permission_all', 'leave_manage_data_permission_part']);
        $all_roles = isset($configureList['leave_manage_data_permission_all']) ? explode(',', $configureList['leave_manage_data_permission_all']): [];
        $part_roles = isset($configureList['leave_manage_data_permission_part']) ? explode(',', $configureList['leave_manage_data_permission_part']): [];

        if (env('sa_id') == $staff_info_id) {
            return $builder;
        }
        if (array_intersect($all_roles, $staff_position_ids)) {
            return $builder;
        } else if (array_intersect($part_roles, $staff_position_ids)) {
            $staffService = new StaffService();
            $getStaffData = $staffService->setExpire(60 * 10)->getStaffJurisdictionFromCache($staff_info_id);

            //没有管辖权限
            if (empty($getStaffData['departments']) &&
                empty($getStaffData['stores']) &&
                empty($getStaffData['regions']) &&
                empty($getStaffData['pieces']) &&
                empty($getStaffData['store_categories'])
            ) {
                return false;
            }

            $result = (new AttendanceToolService())->generateBuilder($builder, ['part' => $getStaffData]);
            $this->logger->write_log("[getStaffDataPermission][get sql]" . $result->getPhql(), 'info');
            return $result;
        } else {
            //查看自己所属部门以及子部门的数据
            $staffInfo = \App\Models\backyard\HrStaffInfoModel::findFirst([
                'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                ],
                'columns' => 'staff_info_id,node_department_id',
            ]);
            if (empty($staffInfo)) {
                return false;
            }

            $departmentInfo = \App\Models\backyard\SysDepartmentModel::getSpecifiedDeptAndSubDept($staffInfo->node_department_id);
            if (empty($departmentInfo)) {
                return false;
            }
            $builder->inWhere('hsi.node_department_id', $departmentInfo);
            return $builder;
        }
    }

    public function getLeaveSource($leave_source)
    {
        if ($leave_source == 3) {
            return 'stay_away_from_work';
        } else {
            return 'font_leave_source_'.$leave_source;
        }
    }

    /**
     * 同步新离职资产处理状态
     * @param $params
     * @return true
     * @throws ValidationException
     * @throws \Exception
     */
    public function editNewAssetsState($params): bool
    {
        //验证数据
        $validations = [
            'staff_info_id' => 'Required|Int|>>>:staff_info_id error',
            'state'         => 'Required|Int|>>>:state error',
            'operator_id'   => 'Required|Int|>>>:operator_id error',
        ];
        Validation::validate($params, $validations);
        $staff_info_id = $params['staff_info_id'];
        $state         = $params['state'];
        $operator_id   = $params['operator_id'];

        $staff_info = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "bind"       => ["staff_info_id" => $staff_info_id],
        ]);

        if (empty($staff_info)) {
            throw new ValidationException('未找到工号信息');
        }

        $db = LeaveManagerModel::beginTransaction($this);

        try {
            $leaveManageModel = LeaveManagerModel::findFirst([
                "conditions" => "staff_info_id = :staff_info_id:",
                "bind"       => ["staff_info_id" => $staff_info_id],
            ]);

            if (empty($leaveManageModel)) {
                $leaveManageModel                     = new LeaveManagerModel();
                $leaveManageModel->staff_info_id      = $staff_info_id;
                $leaveManageModel->money_remand_state = self::ASSETS_STATE_UNPROCESSED;
            }
            $leaveManageModel->assets_remand_state        = $state;
            $leaveManageModel->assets_operator_id         = $operator_id;
            $leaveManageModel->assets_operate_time        = gmdate("Y-m-d H:i:s");
            $leaveManageModel->is_new_assets_remand_state = LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES;
            $leaveManageModel->save();
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }

        //同步hold
        $edits = ['assets_remand_state' => $state];
        $this->syncHold($staff_info_id, $edits);
        return true;
    }

    /**
     * 调用oa 离职资产
     * @param $staff_info_ids
     * @return array
     */
    public function getNewAssetsExport($staff_info_ids): array
    {
        $lang   = self::$language;
        $client = new ApiClient('oa_rpc', '', 'get_not_return_assets_list', $lang);
        $params = [
            'staff_ids' => $staff_info_ids,
        ];
        $client->setParams([$params]);
        $resultData        = $client->execute();
        if (isset($resultData['code']) && $resultData['code'] == 1) {
            return array_column($resultData['data'], null, 'staff_id');
        }
        throw new \Exception('getNewAssetsExport get data from oa error');

    }

    /**
     * @description 获取默认币种
     */
    public function getDefaultCurrency(): string
    {
        switch (get_country_code()) {
            case 'PH':
                $currency = 'PHP';
                break;
            case 'MY':
                $currency = 'MYR';
                break;
            case 'LA':
                $currency = 'LAK';
                break;
            case 'VN':
                $currency = 'VND';
                break;
            case 'ID':
                $currency = 'IDR';
                break;
            default:
                $currency = 'THB';
                break;
        }
        return $currency;
    }

    /**
     * 获取 离职资产未还扣款须知协议
     * @param $companyInfo
     * @return array
     */
    public function getDeductionPdfHeaderFooter($companyInfo)
    {
        $header = '<div style="width: 100%; margin: 0 12mm; box-sizing: border-box; border-bottom: 2px solid #000;padding-bottom: 1mm;"><table style=" line-height: 6mm; width: 100%; font-weight: 700; font-size: 6mm; font-family: sans-serif; background-size: 38mm; "><tr><td style="font-size: 5mm; font-weight: 600; ">'. $companyInfo['company_name'] .'</td><td style="width: 30mm; text-align: right"><img style="width: 30mm; height: auto; object-fit: cover" src="'. $companyInfo['company_logo_url_base64'] .'" /></td></tr></table></div>';

        $footer = '<div style=" width: 100%;margin: 0 12mm; box-sizing: border-box; height: 10mm "><div style="width: 100%;text-align: center; font-size: 2.5mm; word-wrap: break-word;">'. $companyInfo['company_address'] .'</div></div>';
        return [$header, $footer];
    }

    /**
     * 获取离职来源和原因翻译
     * @param $local
     * @param $params
     * @return array
     */
    public function getLeaveSourceAndReason($local, $params): array
    {
        $result = [];
        BaseService::setLanguage($local['locale']);
        if (!empty($params['leave_source'])) {
            $result['leave_source_text'] = self::$t->_($this->getLeaveSource($params['leave_source']));
        }
        if (!empty($params['leave_reason'])) {
            $result['leave_reason_text'] = self::$t->_($this->getReasonKeyByCode($params['leave_reason']));
        }
        return $result;
    }

}
