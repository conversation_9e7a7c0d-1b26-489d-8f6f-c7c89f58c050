<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AssetsInventoryStoresModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\LeaveManageLogModel;
use App\Models\backyard\LeaveManagerModel;
use App\Models\backyard\MsgAssetModel;
use app\models\backyard\ResignFileModel;
use App\Models\backyard\StaffResignModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Models\fle\StaffInfoPositionModel;
use Phalcon\Db;
use Phalcon\Mvc\Model\Query\BuilderInterface;

class LeaveManageListService extends LeaveManagerService
{
    public static $validate_leave_list_params = [
        'name'                   => 'StrLenGeLe:1,50|>>>:name error',
        'working_country'        => 'Arr|ArrLenGe:1|>>>:working_country error', //工作所在国家
        'working_country[*]'     => 'Int|>>>:working_country error',
        'hire_date_begin'        => 'Date|>>>:hire_date_begin error', //入职日期 开始
        'hire_date_end'          => 'Date|>>>:hire_date_end error', //入职日期 结束
        'leave_source'           => 'Int|>>>:leave_source error', //离职来源
        'store_area_id'          => 'Int|>>>:store_area_id error', //所属区域id
        'leave_date_begin'       => 'Date|>>>:leave_date_begin error', //离职日期 开始
        'leave_date_end'         => 'Date|>>>:leave_date_end error', //离职日期 结束
        'state'                  => 'Int|>>>:state error', //在职状态
        'job_title_id'           => 'Arr|ArrLenGe:1|>>>:job_title_id error', //职位
        'job_title_id[*]'        => 'Int|>>>:job_title_id error',
        'store'                  => 'Arr|ArrLenGe:1|>>>:store error', //所属网点
        'store[*]'               => 'StrLenGeLe:1,50|>>>:store error',
        'department'             => 'Int|>>>:department error', //部门
        'remand_state'           => 'Int|>>>:remand_state error', //处理进度
        'assets_remand_state'    => 'Int|>>>:assets_remand_state error', //资产处理状态
        'money_remand_state'     => 'Int|>>>:money_remand_state error', //钱款处理状态
        'approval_status'        => 'Arr|ArrLenGe:1|>>>:approval_status error', //离职申请审批状态
        'approval_status[*]'     => 'Int|>>>:approval_status error',
        'is_sub_department' => 'Required|IntIn:0,1|>>>:is_sub_department param error', // 是否包含子部门，0 不包含 1 包含
    ];

    public static $validate_leave_log_list_params = [
        'name'                   => 'StrLenGeLe:1,50|>>>:name error',
        'working_country'        => 'Arr|ArrLenGe:1|>>>:working_country error', //工作所在国家
        'working_country[*]'     => 'Int|>>>:working_country error',
        'hire_date_begin'        => 'Date|>>>:hire_date_begin error', //入职日期 开始
        'hire_date_end'          => 'Date|>>>:hire_date_end error', //入职日期 结束
        'store_area_id'          => 'Int|>>>:store_area_id error', //所属区域id
        'state'                  => 'Arr|ArrLenGe:1|>>>:state error', //在职状态
        'state[*]'               => 'Int|>>>:state error', //在职状态
        'job_title_id'           => 'Arr|ArrLenGe:1|>>>:job_title_id error', //职位
        'job_title_id[*]'        => 'Int|>>>:job_title_id error',
        'store'                  => 'Arr|ArrLenGe:1|>>>:store error', //所属网点
        'store[*]'               => 'StrLenGeLe:1,50|>>>:store error',
        'department'             => 'Int|>>>:department error', //部门
        'is_sub_department'      => 'Required|IntIn:0,1|>>>:is_sub_department param error', // 是否包含子部门，0 不包含 1 包含
    ];

    public $leave_log_detail_columns = [
        'lm.staff_info_id',
        'lm.serial_no',
        'lm.leave_date',
        'lm.leave_source',
        'lm.leave_type',
        'lm.leave_reason',
        'lm.approval_status',
        'lm.by_staff_resign_id',
        'lm.by_approval_status',
        'lm.cancel_reason',
        'lm.operate_id',
        'lm.operate_name',
        'lm.created_at',
        'lm.updated_at',
    ];

    const RENEW_CONTRACT_RESEND_YES = 1;//展示重新发送续约按钮
    const RENEW_CONTRACT_RESEND_NO = 0;//不展示重新发送续约按钮


    /**
     * 离职管理员工列表 build sql
     * @param $params
     */
    public function buildV2Sql($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(LeaveManagerModel::class, 'hsi.staff_info_id = lm.staff_info_id', 'lm');

        $builder = $this->leftJoinModel($builder);

        //正式员工和实习生 非子账号 离职日期>2020-04-17 00:00:00 待离职或离职状态
        $builder->where("hsi.formal in (1, 4) and hsi.is_sub_staff = 0 and hsi.leave_date > '2020-04-17 00:00:00' and ((hsi.state = 1 and hsi.wait_leave_state = 1) or hsi.state = 2)");

        $builder = $this->getStaffDataPermission($builder, ['staff_info_id' => $params['user']['id']]);
        if(false === $builder) {
            return false;
        }

        // 雇佣类型
        if (!empty($params['hire_type'])) {
            $builder->inWhere("hsi.hire_type",
                is_array($params['hire_type']) ? $params['hire_type'] : [$params['hire_type']]);
        }


        //姓名 工号搜索
        if (isset($params['name']) && $params['name']) {
            $builder->andWhere("hsi.name like :name: or hsi.staff_info_id like :name: or hsi.name_en like :name:",
                ['name' => '%' . $params['name'] . '%']);
        }

        // 工作所在国家 多选
        if (isset($params['working_country']) && $params['working_country']) {
            $builder->inWhere("hsi.working_country", $params['working_country']);
        }

        //入职日期 开始 区间
        if (isset($params['hire_date_begin']) && $params['hire_date_begin']) {
            $builder->andWhere("hsi.hire_date >= :hire_date_begin:", ['hire_date_begin' => $params['hire_date_begin']]);
        }

        //入职日期 结束 区间
        if (isset($params['hire_date_end']) && $params['hire_date_end']) {
            $builder->andWhere("hsi.hire_date <= :hire_date_end:", ['hire_date_end' => $params['hire_date_end']]);
        }

        //离职日期 开始 区间
        if (isset($params['leave_date_begin']) && $params['leave_date_begin']) {
            $builder->andWhere("hsi.leave_date >= :leave_date_begin: ", ['leave_date_begin' => $params['leave_date_begin']]);
        }

        //离职日期 结束 区间
        if (isset($params['leave_date_end']) && $params['leave_date_end']) {
            $builder->andWhere("hsi.leave_date <= :leave_date_end:", ['leave_date_end' => $params['leave_date_end']]);
        }

        //离职来源 单选
        if (!empty($params['leave_source'])) {
            $builder->andWhere("hsi.leave_source = :leave_source:", ['leave_source' => $params['leave_source']]);
        }

        //所属区域
        if (isset($params['store_area_id']) && $params['store_area_id']) {
            $storeIds = $this->getStoresByArea($params['store_area_id']);
            if ($storeIds) {
                $builder->inWhere("hsi.sys_store_id", $storeIds);
            }
        }

        //在职状态
        if (isset($params['state']) && $params['state']) {
            if ($params['state'] == Enums::HRIS_WORKING_STATE_4) { //4 待离职
                $builder->andWhere("hsi.state = 1 and hsi.wait_leave_state = 1");
            } else {
                $builder->andWhere("hsi.state = :state:", ['state' => $params['state']]);
            }
        }

        //职位 多选
        if (isset($params['job_title_id']) && $params['job_title_id']) {
            $builder->inWhere('hsi.job_title', $params['job_title_id']);
        }

        //所属网点 多选
        if (isset($params['store']) && $params['store']) {
            $builder->inWhere("hsi.sys_store_id", $params['store']);
        }

        //所属部门 查询当前部门及自部门 单选
        if (isset($params['department']) && $params['department']) {
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere("hsi.node_department_id", $deptIds);
            }
        }

        //离职管理处理状态 单选
        if (isset($params['remand_state']) && $params['remand_state']) {
            switch ($params['remand_state']) {
                case self::ASSETS_STATE_UNPROCESSED: //未处理
                    $builder->andWhere("assets_remand_state = 1 and money_remand_state = 1");
                    break;
                case self::ASSETS_STATE_PROCESSING: //处理中
                    $builder->andWhere("(lm.assets_remand_state = 2 or lm.money_remand_state = 2 or (lm.assets_remand_state = 1 and lm.money_remand_state != 1) or (lm.assets_remand_state != 1 and lm.money_remand_state = 1))");
                    break;
                case self::ASSETS_STATE_PROCESSED: //已处理
                    $builder->andWhere("lm.assets_remand_state = 3 and lm.money_remand_state = 3");
                    break;
            }
        }

        //资产处理状态 单选
        if (isset($params['assets_remand_state']) && $params['assets_remand_state']) {
            $builder->andWhere("lm.assets_remand_state = :assets_remand_state:", ['assets_remand_state' => $params['assets_remand_state']]);
        }

        //钱款处理状态 单选
        if (isset($params['money_remand_state']) && $params['money_remand_state']) {
            $builder->andWhere("lm.money_remand_state = :money_remand_state:", ['money_remand_state' => $params['money_remand_state']]);
        }

        //离职申请审批状态 多选
        if (isset($params['approval_status']) && !empty($params['approval_status'])) {
            $tArr = array_map('intval', $params['approval_status']);
            $builder->inWhere("lm.approval_status", $tArr);
            $builder->andWhere("hsi.leave_source in ({approval_leave_source:array})",
                ['approval_leave_source' => LeaveManagerService::$approval_leave_source]);
        }

        $builder = $this->constructWhere($builder, $params);

        return $builder;
    }

    public function leftJoinModel($builder)
    {
        return $builder;
    }

    /**
     * 构建 查询条件
     * 为差异化使用
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function constructWhere($builder, $params)
    {
        return $builder;
    }

    /**
     * 离职管理列表
     * @param $params
     * @return array
     */
    public function staffListV2($params)
    {
        $offset = ($params['page'] - 1) * $params['size'];
        $list_builder = $this->buildV2Sql($params);
        $return    = ['page_count' => 0, 'rows' => []];

        if(false === $list_builder) {
            return $return;
        }

        $list_builder->columns("count(1) as total");
        $totalInfo = $list_builder->getQuery()->getSingleResult();
        $pageCount = intval($totalInfo->total);
        if(empty($pageCount)){
            return $return;
        }
        $return['page_count'] = $pageCount;

        $builder_columns = $this->leaveListColumns();
        $list_builder->columns($builder_columns);
        $list_builder->orderBy("lm.id desc");
        $list_builder->limit($params['size'], $offset);

        $data = $list_builder->getQuery()->execute()->toArray();

        $staffId = array_column($data,'staff_info_id');

        //员工对应审核状态，[staff_id]=state
        $staffApproveStateArr = $this->getApproveState($staffId);
        $msgAssets            = [];
        if ($staffId) {
            $msgAssets = MsgAssetModel::find([
                'conditions' => ' staff_id in ({staff_ids:array}) ',
                'bind'       => [
                    'staff_ids' => $staffId,
                ],
            ])->toArray();
            $msgAssets = array_column($msgAssets, null, 'staff_id');
        }


        $isInventories = [];
        if (isset($params['userinfo']) && $params['userinfo']) {
            // 查询是$data否是特殊的角色
            $positionsModel = StaffInfoPositionModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ',
                'bind'       => [
                    'staff_id'  => $params['userinfo']['id'],
                    'positions' => [
                        14, // 系统管理员
                        99, // 超级管理员
                        18, // 网点主管
                        21, // 区域经理
                    ],
                ],
            ]);

            // 查询网点是否打开了
            $sysStoreIds    = array_values(array_unique((array_column($data, 'sys_store_id'))));
            $wmsInventories = $sysStoreIds ? AssetsInventoryStoresModel::find([
                'conditions' => ' sys_store_id in ({store_ids:array})',
                'bind'       => ['store_ids' => $sysStoreIds],
            ])->toArray() : [];
            foreach ($wmsInventories as $inventory) {
                $isInventories[$inventory['sys_store_id']] = $inventory;
            }
        }

        $resignData = $this->getStaffResignMaxList($staffId);

        //获取[借款未归还金额][备用金未归还金额][资产扣费总额]
        [$allPrice, $deductAmount] = $this->allPrices($staffId, GlobalEnums::DATA_EXTEND);
        $goodsInfo = !empty($staffId) ? $this->assetsExport($staffId) : [];
        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        //默认币种
        $currency = $this->getDefaultCurrency();

        // 社保离职日期 现在只有th有值 多返回也没事
        $staff_social_security_leave_date = [];
        if ($staffId){
            $staff_items = HrStaffItemsModel::find([
                'conditions' => "staff_info_id in ({staff_ids:array}) and item = 'SOCIAL_SECURITY_LEAVE_DATE'",
                'bind' => [
                    'staff_ids' => $staffId,
                ],
            ])->toArray();
            $staff_social_security_leave_date = !empty($staff_items) ? array_column($staff_items, 'value', 'staff_info_id') : [];
        }


        foreach ($data as $item) {
            $areaInfo        = $this->getAreaInfoToList($item['sys_store_id']);
            $sys_store_name  = $this->showStoreName($item['sys_store_id']);
            $job_title_name  = $this->showJobTitleName($item['job_title'], true);
            $department_name = $this->showDepartmentName($item['node_department_id'], true);
            $row = [
                'staff_info_id'       => $item['staff_info_id'],
                'name'                => $item['name'],
                'name_en'             => (string)$item['name_en'],
                'state'               => $item['state'],
                'hire_date'           => $item['hire_date'] ? date("Y-m-d", strtotime($item['hire_date'])) : '',
                'sys_store_name'      => $sys_store_name,
                'job_title_name'      => $job_title_name,
                'department_name'     => $department_name,
                'store_area_id'       => $areaInfo['store_area_id'],
                'store_area_text'     => $areaInfo['store_area_text'],
                'working_country'     => $workingCountryList[$item['working_country']] ?? '',
                'state_name'          => $this->getStateName($item['state'], $item['wait_leave_state']),
                'approve_state_text'  => $this->getApproveStateName($staffApproveStateArr[$item['staff_info_id']] ?? 1),
                // 满足四个角色 (系统管理员 超级管理员 网点主管 区域经理) 的用户并且网点也被打开了 查看编辑
                'is_inventory'        => (isset($positionsModel) && $positionsModel && isset($isInventories[$item['sys_store_id']]) && $isInventories[$item['sys_store_id']]['is_switch'] == 1) ? 1 : 0,
                'leave_type'          => $item['leave_type'],
                'leave_type_text'     => static::$t->_('hris_leave_type_'.$item['leave_type']),
                'leave_reason'        => $item['leave_reason'],
                'hire_type'           => $item['hire_type'],
                'hire_type_text'      => static::$t->_('hire_type_'.$item['hire_type']),
                'social_security_leave_date' => !empty($staff_social_security_leave_date) ? $staff_social_security_leave_date[$item['staff_info_id']] : '',
            ];
            $key = $this->getReasonKeyByCode($item['leave_reason']);
            $row['leave_reason_text'] = static::$t->_($key);
            $row['leave_date'] = formatHrDate($item['leave_date']);
            $row['wait_leave_state'] = $item['wait_leave_state'];
            //未归还钱款总额
            $row['all_price'] = $allPrice[$item['staff_info_id']] ? bcdiv($allPrice[$item['staff_info_id']], 100, 2) : 0;
            //资产扣费总额
            if ($item['is_new_assets_remand_state'] == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES) {
                $row['deduct_amount'] = isset($deductAmount[$item['staff_info_id']]) ? bcdiv($deductAmount[$item['staff_info_id']], 100, 2) : 0;
            } else {
                $row['deduct_amount'] = $goodsInfo[$item['staff_info_id']] ? $goodsInfo[$item['staff_info_id']]['all_price'] : 0;
            }
            $row['deduct_amount'] = sprintf('%s %s', $row['deduct_amount'], $currency);

            //资产处理进度
            $row['assets_remand_state'] = (int)$item['assets_remand_state'] ? (int)$item['assets_remand_state'] : self::ASSETS_STATE_UNPROCESSED;
            //钱款处理进度
            $row['money_remand_state'] = (int)$item['money_remand_state'] ? (int)$item['money_remand_state'] : self::ASSETS_STATE_UNPROCESSED;
            //处理进度
            $row['remand_state'] = $this->leaveStateMaps((int)$item['assets_remand_state'], (int)$item['money_remand_state']);
            //离职日期
            $row['leave_approval_date'] = $item['lm_leave_date'] && $item['lm_leave_date'] != "0000-00-00 00:00:00" ? date("Y-m-d", strtotime($item['lm_leave_date'])) : "";
            //离职申请审批状态
            if(in_array($item['leave_source'],[self::LEAVE_SOURCE_BACKYARD,self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT])) {
                $row['approval_status']      = (int)$item['approval_status'] ? (int)$item['approval_status'] : 0;
                $row['approval_status_text'] = $item['approval_status'] ? self::$t->_("by_state_".$item['approval_status']) : "";
            } else {
                $row['approval_status']      = 0;
                $row['approval_status_text'] = "";
            }

            //主管是否操作过0未操作 1操作过
            $row['is_has_superior'] = (int)$item['is_has_superior'] ? (int)$item['is_has_superior'] : 0;
            if (($row['state'] == 2 && $row['is_has_superior'] == 0) || !in_array($item['job_title'], [
                    AssetsService::$job_title['bike_courier'],
                    AssetsService::$job_title['van_courier'],
                    AssetsService::$job_title['shop_officer'],
                    AssetsService::$job_title['branch_supervisor'],
                    AssetsService::$job_title['shop_supervisor'],
                ]) || !isset($msgAssets[$item['staff_info_id']])) {
                // 已离职并且主管处理情况为未处理
                // 包含了 历史数据 与 手动改离职
                $row['is_has_superior_text'] = "";
            } else {
                $row['is_has_superior_text'] = (int)$item['is_has_superior'] ? self::$t->_("assets_state_".self::ASSETS_STATE_PROCESSED) : self::$t->_("assets_state_".self::ASSETS_STATE_UNPROCESSED);
            }

            $row['assets_remand_state_text'] = self::$t->_('assets_state_'.(int)$item['assets_remand_state']);
            $row['money_remand_state_text']  = self::$t->_('assets_state_'.(int)$item['money_remand_state']);
            $row['is_has_employ_operater']   = !in_array($item['sys_department_id'], [
                self::$has_employ_operater_department['fulfillment'],
                self::$has_employ_operater_department['flash_money'],
                self::$has_employ_operater_department['flash_home'],
                self::$has_employ_operater_department['flash_logistic'],
            ]) && $item['state'] == 2 ? 1 : 0;

            //离职来源
            $row['leave_source']      = $item['leave_source'];
            $row['leave_source_text'] = "";
            if (!empty($item['leave_source'])) {
                //$row['leave_source_text'] = $this->getLeaveSourceText($item['leave_source']);
                $leaveSourceKey = $this->getLeaveSource($item['leave_source']);
                $row['leave_source_text'] = self::$t->_($leaveSourceKey);
            }

            //新需求 来源非“BY申请离职”以及职位一下职位（Bike courier 13 /van courier 110/Branch Supervisor 16/shop officer 98/ Shop Supervisor 101）不显示某些字段 并且关系
            $row['is_show'] = 0;
            $forbidden      = [13, 110, 16, 98, 101];
            //来源 '0=空，-1其他。6批量导入。其他同hold来源,1.新增,2.hirs,3旷工3天及以上，4未缴纳公款，5backyard提交申请，'
            if ($item['leave_source'] == 5 && in_array($item['job_title'], $forbidden)) {
                $row['is_show'] = 1;
            }
            $row = array_merge($row, $this->buildOtherRow($item));
            $row = $this->editRowData($row, $item);//修改字段值
            $row['remand_state_text']        = self::$t->_('assets_state_'.$row['remand_state']);
            $row['is_show_cancel_staff_resign'] = 0; // 默认不显示
            $row['staff_resign_leave_date'] = !empty($resignData[$item['staff_info_id']]['leave_date']) ? $resignData[$item['staff_info_id']]['leave_date'] : '';
            $today = gmdate('Y-m-d', time() + ($this->timeOffset) * 3600);
            if ($item['state'] == Enums::HRIS_WORKING_STATE_2 && $item['leave_source'] != self::LEAVE_SOURCE_BACKYARD && $item['approval_status'] == 2 && !empty($resignData[$item['staff_info_id']]['leave_date']) && $resignData[$item['staff_info_id']]['leave_date'] > $today){
                // 非BY申请离职 审批状态已同意 申请的离职日期大于当前日期 显示同时撤销by离职申请 和不需要撤销 按钮
                $row['is_show_cancel_staff_resign'] = 1;
            }
            $resendInfo = $this->isResendRenew($item['staff_info_id'], $params['user']['id']);
            $row['is_resend_renew']      = empty($resendInfo['is_resend_renew']) ? 0 : $resendInfo['is_resend_renew']; //是否展示 【重发续约确认】 按钮
            $row['contract_business_id'] = empty($resendInfo['contract_business_id']) ? 0 : $resendInfo['contract_business_id']; //业务id

            $return['rows'][] = $row;
        }

        return $return;
    }

    public function isResendRenew($staffInfoId, $userId)
    {
        return [];
    }


    /**
     * 导出
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function export($params): array
    {
        $list_builder    = $this->buildV2Sql($params);
        if(false === $list_builder) {
            return [];
        }
        $data_s = [];
        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');
        //菲律宾 特殊逻辑
        $unpaidBankList = (new QuitclaimService())->getUnpaidBankList();
        $builder_columns = $this->exportLeaveListColumns();
        $list_builder->columns($builder_columns);
        $i = 1;
        $page_size = 500;
        if (get_runtime() == 'dev') {
            $page_size = 50;
        }
        while (true) {
            $list_builder->orderBy("lm.id desc");
            $offset = ($i - 1) * $page_size;
            $list_builder->limit($page_size, $offset);
            $data = $list_builder->getQuery()->execute()->toArray();
            if (empty($data)) {
                break;
            }
            $i++;
            $this->logger->info(['page'=>$i,'function'=>'export_v2']);

            $staffIds        = array_column($data, 'staff_info_id');
            $staff_store_ids = array_column($data, 'sys_store_id');

            // 调用oa 获取 借款未归还金额 备用金未归还金额
            $notReturnAmount = (new ExternalDataService())->batchGetUserOutstandingAmount($staffIds);
            // 快递员未回款
            $receivableAmounts =  $this->receivableAmounts($staffIds) ;
            // 其他 出纳应汇款
            $monies = $this->getMoneys($staffIds);
            // 出纳应汇款
            [$cashier, $cashier2] = $this->getAmounts($staffIds);
            //所属区域
            $storeAreas  =  $this->storeAreas($staff_store_ids);
            // 资产 钱款用户ID
            $goods     =  $this->assetsExport($staffIds);
            $goods_new = $this->getNewAssetsExport($staffIds);
            // 最后离职时间
            $resignsTime = $this->lastResign($staffIds);
            //公司解约个人代理
            $companyTerminationContractInfo = $this->companyTerminationContractInfo($staffIds);
            // 社保离职日期
            $staff_social_security_leave_date = $this->getStaffSocialSecurityLeaveDate($staffIds);
            foreach ($data as  $datum) {
                $datum['leave_date'] = formatHrDate($datum['leave_date']);
                $sys_store_name      = $this->showStoreName($datum['sys_store_id']);
                $storeAreasBelong    = $this->getExportAreaInfo($storeAreas, $datum['sys_store_id']);
                $job_title_name      = $this->showJobTitleName($datum['job_title'],true);
                // 快递员未回款
                $_receiveable_amount = isset($receivableAmounts[$datum['staff_info_id']]) && $receivableAmounts[$datum['staff_info_id']] ? bcdiv((int)($receivableAmounts[$datum['staff_info_id']]['receiveable_amount']), 100, 2) : 0;
                // 出纳应回款
                if(isset($monies[$datum['staff_info_id']]) && $monies[$datum['staff_info_id']]['cashier_money'] != '-1') {
                    $_parcel_amount_cod_amount = bcdiv((int)$monies[$datum['staff_info_id']]['cashier_money'], 100, 2);
                } else {
                    $cashier_parcel_amount = isset($cashier[$datum['staff_info_id']]) ? (int)$cashier[$datum['staff_info_id']]['parcel_amount'] : 0;
                    $cashier2_cod_amount = isset($cashier2[$datum['staff_info_id']]) ? (int)$cashier2[$datum['staff_info_id']]['cod_amount'] : 0;
                    $_parcel_amount_cod_amount = bcdiv($cashier_parcel_amount + $cashier2_cod_amount, 100, 2);
                }

                // 借款未归还
                $_loanNotReturns = $notReturnAmount['loan_amount'][$datum['staff_info_id']] ?? 0;

                // 网点备用金
                $_revserveFunds = $notReturnAmount['reserve_fund_amount'][$datum['staff_info_id']] ?? 0;

                // 其他
                $_other_money = isset($monies[$datum['staff_info_id']]) && $monies[$datum['staff_info_id']]['money'] ? bcdiv($monies[$datum['staff_info_id']]['money'], 100, 2) : 0;

                $leave_source = !empty($datum['leave_source']) ? $this->getLeaveSourceText($datum['leave_source']) : '';

                $leave_reason_key = $this->getReasonKeyByCode($datum['leave_reason']);

                $assets_operate_remark = '';
                $asset_name            = '';
                $all_price             = 0;
                if ($datum['is_new_assets_remand_state'] == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES) {
                    if (isset($goods_new[$datum['staff_info_id']])) {
                        $asset_name            = $goods_new[$datum['staff_info_id']]['asset_name'];
                        $all_price             = $goods_new[$datum['staff_info_id']]['all_deduct_amount'];
                        $assets_operate_remark = $goods_new[$datum['staff_info_id']]['asset_department_remark'];
                    }
                } else {
                    $assets_operate_remark = $datum['assets_operate_remark'];
                    if (isset($goods[$datum['staff_info_id']])) {
                        $asset_name = $goods[$datum['staff_info_id']]['goods'];
                        $all_price  = $goods[$datum['staff_info_id']]['all_price'];
                    }
                }

                if(mb_strlen($asset_name) > 200) {
                    $asset_name = mb_substr($asset_name, 0, 200) . '......';
                }
                //处理进度
                $datum['remand_state'] = $this->leaveStateMaps((int)$datum['assets_remand_state'], (int)$datum['money_remand_state']);
                $datum = $this->editRowData($datum, $datum);//修改字段值

                $tmpCompanyTerminationContractInfo = $companyTerminationContractInfo[$datum['staff_info_id']] ?? [];
                //展示“解约原因”；“解约原因备注”
                if (!empty($tmpCompanyTerminationContractInfo)) {
                    $datum['leave_reason_remark'] = self::$t->_('termination_reason_' . $tmpCompanyTerminationContractInfo['reason']) . ';' . $tmpCompanyTerminationContractInfo['remark'];
                    $datum['leave_reason_remark'] = trim($datum['leave_reason_remark'], ';');
                }

                $item = [];
                // 工号
                $item[] = $datum['staff_info_id'];
                // 姓名
                $item[] = $datum['name'];
                // 英文名称
                $item[] = $datum['name_en'];
                // 职位
                $item[] = $job_title_name;
                // 部门
                $item[] = $this->showDepartmentName($datum['node_department_id']);
                // 工作所在国家
                $item[] = $workingCountryList[$datum['working_country']] ?? '';
                // 所属网点
                $item[] = $sys_store_name;
                // 所属区域
                $item[] = $storeAreasBelong;
                // 在职状态
                $item[] = $this->getStateName($datum['state'], $datum['wait_leave_state']);
                //雇佣类型
                $item[] = self::$t->_('hire_type_'.$datum['hire_type']);
                // 入职日期
                $item[] = date('Y-m-d', strtotime($datum['hire_date']));;
                // 离职/待离职日期
                $item[] = date('Y-m-d', strtotime($datum['leave_date']));
                // 社保离职日期
                if (isCountry('TH')){
                    $item[] = !empty($staff_social_security_leave_date[$datum['staff_info_id']]) ? date('Y-m-d', strtotime($staff_social_security_leave_date[$datum['staff_info_id']])) : '';    
                }
                // 未还资产
                $item[] = $asset_name;
                // 总价值(资产总扣费金额)
                $item[] = $all_price;
                // 资产处理状态
                $item[] = static::$t->_('assets_state_'.$datum['assets_remand_state']);
                // 未归还钱款(未归还钱款总额)
                $item[] = $_other_money + $_revserveFunds + $_loanNotReturns + $_parcel_amount_cod_amount + $_receiveable_amount;

                $item = $this->exportBuildOtherRow($item, $datum, $unpaidBankList);

                // 快递员未回款
                $item[] = $_receiveable_amount;
                // 出纳应汇款
                $item[] = $_parcel_amount_cod_amount;
                // 借款未归还金额
                $item[] = $_loanNotReturns;
                // 网点备用金未归还金额q
                $item[] = $_revserveFunds;
                // 其他
                $item[] = $_other_money;
                // 钱款处理状态
                $item[] = self::$t->_('assets_state_'.$datum['money_remand_state']);
                // 来源
                $item[] = $leave_source;
                // 离职申请日期
                $item[] = $datum['leave_source'] == self::LEAVE_SOURCE_BACKYARD ? ( $resignsTime[$datum['staff_info_id']] ?? '') :'';
                // 离职类型
                $item[] = empty($datum['leave_type']) ? '' : static::$t->t('resign_type_'.$datum['leave_type']);
                // 离职原因
                $item[] = empty($datum['leave_reason']) ? '' : static::$t->t($leave_reason_key);
                // 离职原因备注
                $item[] = $datum['leave_reason_remark'];
                // 备注
                $item[] = $assets_operate_remark;
                // 处理进度
                $item[] = self::$t->_('assets_state_'.$datum['remand_state']);

                $data_s[] = $item;
            }
        }

        return $data_s;
    }

    /**
     * 离职记录
     * @param $params
     * @return array
     */
    public function leaveLogList($params) {
        $offset = ($params['page'] - 1) * $params['size'];

        $list_builder = $this->leaveLogListBuildSql($params);
        if(false === $list_builder) {
            return ['page_count' => 0, 'rows' => []];
        }
        $list_builder->columns("count(1) as total");
        $totalInfo = $list_builder->getQuery()->getSingleResult();
        $pageCount = intval($totalInfo->total);
        if (empty($pageCount)) {
            return ['page_count' => 0, 'rows' => []];
        }
        $builder_columns = 'hsi.staff_info_id, 
                    hsi.name, 
                    hsi.name_en, 
                    hsi.mobile, 
                    hsi.job_title, 
                    hsi.sys_department_id, 
                    hsi.node_department_id, 
                    hsi.sys_store_id, 
                    hsi.hire_date, 
                    hsi.state, 
                    hsi.wait_leave_state, 
                    hsi.leave_date, 
                    hsi.leave_type,
                    hsi.leave_reason,
                    hsi.leave_reason_remark,
                    hsi.working_country,
                    hsi.leave_source,  
                    hsi.formal, 
                    hsi.job_title_grade_v2, 
                    hsi.hire_type';
        $list_builder->columns($builder_columns);
        $list_builder->orderBy("hsi.hire_date desc");
        $list_builder->limit($params['size'], $offset);

        $data = $list_builder->getQuery()->execute()->toArray();

        $return        = ['page_count' => intval($pageCount), 'rows' => []];

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        foreach ($data as $item) {
            $areaInfo            = $this->getAreaInfoToList($item['sys_store_id']);
            $sys_store_name      = $this->showStoreName($item['sys_store_id']);
            $job_title_name      = $this->showJobTitleName($item['job_title'], true);
            $department_name     = $this->showDepartmentName($item['node_department_id'], true);
            $sys_department_name = $this->showDepartmentName($item['sys_department_id'], true);
            $key                 = $this->getReasonKeyByCode($item['leave_reason']);

            $row = [
                'staff_info_id'       => $item['staff_info_id'],
                'name'                => $item['name'],
                'name_en'             => (string)$item['name_en'],
                'state'               => $item['state'],
                'hire_date'           => $item['hire_date'] ? date("Y-m-d", strtotime($item['hire_date'])) : '',
                'leave_date'          => $item['leave_date'] ? date("Y-m-d", strtotime($item['leave_date'])) : '',
                'sys_store_name'      => $sys_store_name,
                'job_title_name'      => $job_title_name,
                'sys_department_name' => $sys_department_name,
                'department_name'     => $department_name,
                'store_area_id'       => $areaInfo['store_area_id'],
                'store_area_text'     => $areaInfo['store_area_text'],
                'working_country'     => $workingCountryList[$item['working_country']] ?? '',
                'state_name'          => $this->getStateName($item['state'], $item['wait_leave_state']),
                'leave_type'          => $item['leave_type'],
                'leave_type_text'     => static::$t->_('hris_leave_type_'.$item['leave_type']),
                'leave_reason'        => $item['leave_reason'],
                'leave_reason_text'   => static::$t->_($key),
                'hire_type_text'      => static::$t->_('hire_type_'.$item['hire_type']),
            ];

            $return['rows'][] = $row;
        }

        return $return;
    }

    /**
     * 离职记录 build sql
     * @param $params
     */
    public function leaveLogListBuildSql($params) {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'hsi.sys_store_id = ss.id', 'ss');
        $builder->where("hsi.is_history_leave = 1");
        $builder->inWhere('hsi.formal', [1, 4]);
        $builder->andWhere("hsi.is_sub_staff = 0");

        $builder = $this->getStaffDataPermission($builder, ['staff_info_id' => $params['user']['id']]);
        if(false === $builder) {
            return false;
        }

        // 雇佣类型
        if (!empty($params['hire_type'])) {
            $builder->inWhere("hsi.hire_type",
                is_array($params['hire_type']) ? $params['hire_type'] : [$params['hire_type']]);
        }

        //姓名 工号搜索
        if (isset($params['name']) && $params['name']) {
            $builder->andWhere("hsi.name like :name: or hsi.staff_info_id like :name: or hsi.name_en like :name:",
                ['name' => '%' . $params['name'] . '%']);
        }

        //所属部门 查询当前部门及自部门 单选
        if (isset($params['department']) && $params['department']) {
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere("hsi.node_department_id", $deptIds);
            }
        }

        //职位多选
        if(isset($params['job_title_id']) && $params['job_title_id']) {
            $builder->inWhere("hsi.job_title", $params['job_title_id']);
        }

        //所属网点 多选
        if (isset($params['store']) && $params['store']) {
            $builder->inWhere("hsi.sys_store_id", $params['store']);
        }

        //所属区域
        if (isset($params['store_area_id']) && $params['store_area_id']) {
            $storeIds = $this->getStoresByArea($params['store_area_id']);
            if ($storeIds) {
                $builder->inWhere("hsi.sys_store_id", $storeIds);
            }
        }

        // 工作所在国家 多选
        if (isset($params['working_country']) && $params['working_country']) {
            $builder->inWhere("hsi.working_country", $params['working_country']);
        }

        //在职状态
        if (isset($params['state']) && $params['state']) {
            if(in_array(Enums::HRIS_WORKING_STATE_4, $params['state'])) {
                $state_key = array_search(Enums::HRIS_WORKING_STATE_4, $params['state']);
                unset($params['state'][$state_key]);
                if(!empty($params['state'])) {
                    $params['state'] = array_values(array_unique($params['state']));
                    $builder->andWhere('(hsi.state in({state:array}) or hsi.wait_leave_state = 1)', ['state' => $params['state']]);
                } else {
                    $builder->andWhere('(hsi.state = 1 and hsi.wait_leave_state = 1)');
                }

            } else {
                $builder->andWhere('(hsi.state in({state:array}) and hsi.wait_leave_state = 0)', ['state' => $params['state']]);
            }
        }

        //入职日期 开始 区间
        if (isset($params['hire_date_begin']) && $params['hire_date_begin']) {
            $builder->andWhere("hsi.hire_date >= :hire_date_begin:", ['hire_date_begin' => $params['hire_date_begin']]);
        }

        //入职日期 结束 区间
        if (isset($params['hire_date_end']) && $params['hire_date_end']) {
            $builder->andWhere("hsi.hire_date <= :hire_date_end:", ['hire_date_end' => $params['hire_date_end']]);
        }

        return $builder;
    }

    public function getLeaveLogDetailColumns()
    {

        return $this->leave_log_detail_columns;
    }

    public function leaveLogDetailLeftModel($builder)
    {
        return $builder;
    }

    /**
     * 列表行差
     * @param array $value
     * @return array|mixed
     */
    public function buildLeaveLogDetailOtherRow($value = [])
    {
        return [];
    }

    /**
     * 员工离职记录详情
     * @param $params
     * @return array
     */
    public function leaveLogDetail($params) {

        $offset = ($params['page'] - 1) * $params['size'];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['lm' => LeaveManageLogModel::class]);
        $builder = $this->leaveLogDetailLeftModel($builder);
        $builder->where("lm.staff_info_id = :staff_info_id:", ['staff_info_id' => $params['staff_info_id']]);

        $builder->columns("count(1) as total");
        $totalInfo = $builder->getQuery()->getSingleResult();
        $pageCount = intval($totalInfo->total);

        $columns = $this->getLeaveLogDetailColumns();

        $builder->columns($columns);
        $builder->orderBy("lm.id desc");
        $builder->limit($params['size'], $offset);

        $data = $builder->getQuery()->execute()->toArray();
        $return        = ['page_count' => $pageCount];
        foreach ($data as $key => $value) {
            $data[$key]['leave_source_text'] = $this->getLeaveSourceText($value['leave_source']); //离职来源
            $data[$key]['leave_type_text'] = !empty($value['leave_type']) ? static::$t->_('hris_leave_type_'.$value['leave_type']) : '';//离职类型
            //离职原因
            $reasonKey = $this->getReasonKeyByCode($value['leave_reason']);
            $data[$key]['leave_reason_text'] = $reasonKey ? self::$t->_($reasonKey):'';
            $data[$key]['created_at'] = show_time_zone($value['created_at']);
            $data[$key]['updated_at'] = show_time_zone($value['updated_at']);
            $data[$key] = array_merge($data[$key], $this->buildLeaveLogDetailOtherRow($value));
        }
        $return['rows'] = $data;

        return $return;
    }

    /**
     * 申请离职列表
     * @param $params
     * @return array
     */
    public function staffRegionList($params): array
    {
        $offset  = ($params['page'] - 1) * $params['size'];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['sr' => StaffResignModel::class]);
        $builder->where("sr.submitter_id = :staff_info_id:", ['staff_info_id' => $params['staff_info_id']]);
        $builder->andWhere('sr.source = :source:', ['source' => StaffResignModel::SOURCE_BY]);
        $builder->columns("count(1) as total");
        $totalInfo = $builder->getQuery()->getSingleResult();
        $pageCount = intval($totalInfo->total);
        $return['total'] = $pageCount;
        $return['list']  = [];
        if (empty($return['total'])) {
            return $return;
        }
        $builder->columns(['resign_id,created_at,serial_no,leave_date,last_work_date,reason,status,work_handover,reason,remark']);
        $builder->orderBy("sr.resign_id desc");
        $builder->limit($params['size'], $offset);
        $return['list'] = $builder->getQuery()->execute()->toArray();
        foreach ($return['list'] as &$item) {
            $item['created_at']        = show_time_zone($item['created_at']);
            $item['leave_reason_text'] = self::$t->_($this->getReasonKeyByCode($item['reason']));
            $item['status_text']       = self::$t->_('audit_status.' . $item['status']);
        }
        return $return;
    }

    /**
     * @param $params
     * @return array
     */
    public function checkFileList($params)
    {
        if (empty($params['staff_info_id'])) {
            return [];
        }
        $data       = ResignFileModel::find([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => [
                'staff_info_id' => $params['staff_info_id'],
            ],
            'order'      => 'id desc',
        ])->toArray();
        $staff_info = HrStaffInfoModel::findFirst([
            'columns'    => 'email,personal_email',
            'conditions' => 'staff_info_id=:staff_info_id:',
            'bind'       => ['staff_info_id' => $params['staff_info_id']],
        ]);
        $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];
        foreach ($data as $k => $v) {
            $data[$k]['type_text']      = !empty(ResignFileModel::$resign_file_types_text[$v['type']]) ? self::$t->_(ResignFileModel::$resign_file_types_text[$v['type']]) : '';
            $data[$k]['created_at']     = show_time_zone($v['created_at']);
            $data[$k]['personal_email'] = $staff_info['personal_email'] ?? '';
        }
        return $data;
    }

    /**
     * @param $params
     * @return array|true
     * @throws ValidationException
     * @throws \PHPMailer\PHPMailer\Exception
     */
    public function sendEmail($params)
    {
        if (empty($params['id']) || empty($params['email'])) {
            throw new ValidationException('params error');
        }
        $data = ResignFileModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $params['id'],
            ],
        ]);
        if (empty($data)) {
            throw new ValidationException('data error');
        }
        $data = $data->toArray();
        // 发送邮件
        $emial_title            = $data['email_title'] ?? '';
        $emial_content          = $data['email_content'] ?? '';
        $file_name              = $data['file_name'] ?? '';
        $pdfFileName            = str_replace('/', '', $file_name);
        $email_data['file_url'] = $this->fileDownload($data['file_url'], '', $pdfFileName . '.pdf');
        // 获取抄送人 产品说不抄送了
//        $email_data['cc_email'] = (new SettingEnvService())->getSetVal('email_agent_suspension_resign',',') ?: '';
        $sendResult             = \App\Library\BiMail::send($params['email'],
            $emial_title,
            $emial_content, [$email_data['file_url']],
            $email_data['cc_email'] ?? []);
        if (!$sendResult) {
            throw new ValidationException('send_state_fail');
        }
        return true;
    }
}