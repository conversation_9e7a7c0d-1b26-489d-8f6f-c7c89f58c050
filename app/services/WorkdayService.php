<?php
/**
 * Created by <PERSON>pStorm.
 * User: nick
 * Date: 2021/8/4
 * Time: 9:13 PM
 */

namespace App\Services;

use App\Library\ApiClient;
use App\Library\AttendanceEnums;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums;
use App\Library\Enums as BaseEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\RedisEnums;
use App\Library\Enums\SchedulingSuggestionEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Models\backyard\StaffAuditModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\HrOvertimeModel;
use App\Models\fle\StaffAccountModel;
use Phalcon\Mvc\Model\Query\Builder;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Library\Enums\ConditionsRulesEnums;

use App\Library\Enums\ShiftEnums;

use Exception;

class WorkdayService extends BaseService
{

    public  $staff_info_id;
    public  $staff_info_ids;
    public  $date;
    public  $operator;
    public  $is_confirm;
    public  $leaveData;//请假数据
    public  $suggestConfirm;
    public  $staffInfo           = [];
    public  $commonPublicHoliday = [];
    public  $allPublicHoliday    = [];
    public  $setOffDays = [];
    public  $changeDayOff = [];//挪过来的休息日
    public  $isWorkDayRootId = false;
    public  $notSundayCommonPHDays = [];//非周日的PH日期数
    public  $notSaturdayAndSundayCommonPHDays = [];//非周六和周日的PH日期数
    /**
     * 能修改当天的六天班轮休休息日
     * @var bool
     */
    public  $canModifyTodayRest = false;
    public  $staffAllOffDays = [];
    private $src                 = 1;
    const SRC_1 = 1;
    const SRC_2 = 2;

    public $shiftStaffsData;
    //小方块展示 请假缩写 其他的不用 和薪酬那边不一样
    public $salaryLeaveType = ['AL', 'SL', 'CT', 'PL', 'LW', 'USL'];
    public $holidayServer;

    public $shiftData;//班次信息 小方块里面用
    public $liveJobId = [];//主播职位id 数组

    public $allStaffIds;//本次操作轮休的所有员工id
    public $operateId;//操作人id
    public $canEditSelf;//修改自己轮休

    public $isReplace= false;

    public static $staff_week_work_day_diff_off_day = [
        HrStaffInfoModel::WEEK_WORKING_DAY_5 => [6, 7],
        HrStaffInfoModel::WEEK_WORKING_DAY_6 => [7],
    ];

    public function setSrc($src = 1): WorkdayService
    {
        $this->src = $src;
        return $this;
    }

    public function setStaffInfoId($staff_info_id): WorkdayService
    {
        $this->staff_info_id = $staff_info_id;
        return $this;
    }

    public function setDate($date): WorkdayService
    {
        $this->date = $date;
        return $this;
    }

    public function setOperator($operator): WorkdayService
    {
        $this->operator = $operator;
        return $this;
    }

    //是否可以修改自己的轮休
    public function setEditSelf($staff): WorkdayService
    {
        $this->canEditSelf = false;
        if($this->operateId == 10000){
            $this->canEditSelf = true;
            return $this;
        }
        //如果 编辑员工 不是自己 返回true 不去查规则
        if($this->operateId != $staff){
            $this->canEditSelf = true;
            return $this;
        }
        //新增规则配置校验 能否修改自己的休息日
        $ruleParam['staff_info_id'] = $this->operateId;
        $ruleParam['rule_key']      = 'Allow_set_selfOff';
        $rules                      = $this->getConditionRule(self::$language, $ruleParam);
        if (!empty($rules['data']) && $rules['data']['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE) {
            $this->canEditSelf = $rules['data']['response_data'];//产品说配置的是 bool 类型
        }
        return $this;
    }

    public function setConfirm($is_confirm): WorkdayService
    {
        $this->is_confirm = $is_confirm;
        return $this;
    }

    //轮休配置 验证排班建议 二次确认标记
    public function setSuggestConfirm($confirm): WorkdayService
    {
        $this->suggestConfirm = $confirm;
        return $this;
    }

    public function setLeaveData($staffIds,$dateList){
        $leaveServer = new LeaveBllService();
        $leaveData = $leaveServer->getLeaveByDate($staffIds,$dateList);
        $this->leaveData = empty($leaveData) ? [] : array_column($leaveData,'type','u_key');
    }

    public function setIsWorkDayRootId(): WorkdayService
    {
        $sysService = new SysService();
        $sysService->setCurrentStaffId($this->operator);
        $this->isWorkDayRootId = $sysService->isWorkDayRootId();
        return $this;
    }

    //对应国家的 主播职位id
    public function setLiveJob(){
        $liveJobId = (new SettingEnvService())->getSetVal('free_shift_position');
        $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
        $this->liveJobId = $liveJobId;
    }

    /**
     * 能修改当天的六天班轮休休息日
     * @return $this
     */
    public function canModifyTodayRest(): WorkdayService
    {
        $sysService = new SysService();
        $sysService->setCurrentStaffId($this->operator);
        $canModifyTodayRest = $sysService->canModifyTodayRest();
        $this->canModifyTodayRest = $canModifyTodayRest && in_array($this->staffInfo['week_working_day'],HrStaffInfoModel::$weekWorkingDayTogether) &&  $this->staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_1;
        return $this;
    }

    const WORKDAY_OSS_PATH = 'WORK_DAY';

    //判断登陆用户权限 获取列表数据
    public static $permission = [
        ['key' => 'area', 'value' => true],
        ['key' => 'store_id', 'value' => true],
        ['key' => 'job_title', 'value' => true],
        ['key' => 'staff_info_id', 'value' => true],
        ['key' => 'department', 'value' => true],
    ];

    /**
     * 是否是产品
     * @param $staff_info_id
     * @return bool
     */
    public function isProductAdmin($staff_info_id): bool
    {
        $set_val = (new SettingEnvService())->getSetVal('edit_work_day_admin_staff');
        if (empty($set_val)) {
            return false;
        }
        return in_array($staff_info_id, explode(',', $set_val));
    }


    /**
     * 验证
     * @throws BusinessException
     */
    protected function validation()
    {
        if (BaseService::checkLock(RedisEnums::TASK_RUNNING_FLAG_DEFAULT_REST_DAY)) {
            throw new BusinessException(self::$t->_('task_running_notice'));
        }

        //新增验证逻辑 菲律宾 https://flashexpress.feishu.cn/docx/C6SidNLIIoA3OAxXsiKc2fEsnQf
        $flag = $this->checkTruckUpdatePermission($this->allStaffIds, $this->operateId);
        if(!$flag){
            throw new BusinessException(self::$t->_('workday_truck_permission'));
        }

    }

    /**
     * @param $local
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffOffLeave($local, $params): array
    {
        if (empty($params['staff_ids']) || !is_array($params['staff_ids'])) {
            throw new ValidationException('staff_ids must be array');
        }
        if (empty($params['start_date']) || empty($params['end_date'])) {
            throw new ValidationException('start_date and end_date empty!!');
        }

        $staffIds  = array_values(array_unique($params['staff_ids']));
        $startDate = $params['start_date'];
        $endDate   = $params['end_date'];

        $dateList           = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        $staffOffDayService = new StaffOffDayService();
        $allOffDays         = $staffOffDayService->getMultiStaffData($staffIds, $startDate, $endDate, false, false);

        $leaveServer = new LeaveBllService();
        $leaveData   = $leaveServer->getLeaveByDate($staffIds, $dateList);
        $leaveData   = array_column($leaveData, 'type', 'u_key');
        $result      = [];
        $item        = [];
        foreach ($staffIds as $staffId) {
            foreach ($dateList as $date) {
                $item['staff_id'] = $staffId;
                $item['date']     = $date;
                $item['type']     = 0;//默认
                if (in_array($date, $allOffDays[$staffId] ?? [])) {
                    $item['type'] = 1;//休息日
                } elseif (isset($leaveData[$date . '_' . $staffId])) {
                    $item['type'] = 2;//请假
                }
                $result[] = $item;
            }
        }
        return ['code' => ErrCode::SUCCESS, 'message' => 'success!', 'data' => $result];
    }



    /**
     * @throws Exception
     */
    public function handle($staffs, $dates, $operator, $is_confirm = 0): bool
    {
        //统一验证
        $this->allStaffIds = $staffs;
        $this->operateId = $operator;
        $this->validation();

        //验证 是否存在请假 以及 排班建议是否符合条件 只有泰国
        $this->setLeaveData($staffs,$dates);
        $this->holidayServer = new HolidayService();

        $db = HrStaffWorkDaysModel::beginTransaction($this);
        try {
            foreach ($staffs as $staff) {
                foreach ($dates as $date) {
                    $this->setStaffInfoId($staff);
                    $this->setDate($date);
                    $this->setOperator($operator);
                    $this->setEditSelf($staff);
                    $this->setIsWorkDayRootId();
                    $this->setConfirm($is_confirm);
                    $this->initStaffInfo();
                    $this->canModifyTodayRest();
                    $this->leaveValidate();
                    $this->initStaffHolidayAndOffDay();
                    $this->addStaffWorkdays();
                }
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        $batch_message = [];
        foreach ($staffs as $staff) {
            foreach ($dates as $date) {
                if ($this->src == self::SRC_1) {
                    $this->staff_set_work_day_send_message($staff, $date, 2);//轮休发送消息设置
                } else {
                    $tmp['staff_info_id'] = $staff;
                    $tmp['date_at']       = $date;
                    $batch_message[]      = $tmp;
                }
            }
        }

        if ($this->src == self::SRC_2 && $batch_message) {//批量的
            $this->staff_batch_send_message($batch_message);
        }
        //同步 处罚
        $this->syncFbi($staffs,$dates);

        return true;
    }

    //同步 处罚接口 修改过去日期情况 只有泰国
    public function syncFbi($staffs, $dates)
    {
        //泰国 和马来 同步
        if (!isCountry('TH') && !isCountry('MY')) {
            return true;
        }
        try {
            $beforeDates = [];
            $today       = date('Y-m-d');
            foreach ($dates as $date) {
                if ($date < $today) {
                    $beforeDates[] = $date;
                }
            }
            //没有修改过去的日期不操作
            if (empty($beforeDates)) {
                return true;
            }
            $client = new ApiClient('bi_rpc_v2', '', 'abnormal.staff_leave_request', self::$language);
            foreach ($staffs as $id) {
                foreach ($beforeDates as $date) {
                    $param['staff_id']         = $id;
                    $param['leave_start_date'] = $date;
                    $param['leave_end_date']   = $date;
                    $param['type']             = Enums::SYNC_TYPE_OFF;
                    $client->withParam($param)->execute();
                }
            }
            $this->getDI()->get('logger')->write_log("syncFbi off day" . json_encode($beforeDates) . json_encode($staffs),
                'info');
            return true;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->write_log("syncFbi off day error " . $e->getTraceAsString());
            return true;
        }
    }


    /**
     * 初始化 员工信息
     * @return $this
     * @throws BusinessException
     */
    protected function initStaffInfo(): WorkdayService
    {
        //验证员工状态
        $this->staffInfo = (new StaffService())->getHrStaffInfo($this->staff_info_id);
        if (empty($this->staffInfo)) {
            throw new BusinessException(self::$t->_('staff_id_not_found'));
        }
        $this->staffInfo['week_working_day'] = $this->staffInfo['week_working_day'] ?: 6;
        return $this;
    }

    /**
     * 初始化 休息日和假日
     * @return $this
     */
    protected function initStaffHolidayAndOffDay(): WorkdayService
    {
        $this->setOffDays = [];
        $this->notSaturdayAndSundayCommonPHDays = [];
        $this->notSundayCommonPHDays = [];
        //获取公共的休息日
        $holidays = $this->get_holidays([
            'type'      => 7 - $this->staffInfo['week_working_day'],
            'date'      => date('Y-m-d', strtotime($this->date) - 30 * 86400),
            'staff_ids' => [$this->staff_info_id],
        ]);
        if (!empty($holidays)) {
            $this->commonPublicHoliday = $holidays[$this->staffInfo['week_working_day']]??[];
            sort($this->commonPublicHoliday);
        }
        $this->allPublicHoliday = array_merge($this->commonPublicHoliday,
            (new StaffPublicHolidayService())->getStaffData($this->staff_info_id));

        $weekDays = DateHelper::getWeekAllDaysByDate($this->date);
        $staffOffDayService = new StaffOffDayService();
        $_allOffDays = $staffOffDayService->getMultiStaffData([$this->staff_info_id], min($weekDays), '', true);
        $this->staffAllOffDays = $staffAllOffDays = $_allOffDays ? $_allOffDays[$this->staff_info_id] : [];
        foreach ($staffAllOffDays as $staffAllOffDay) {
            if (in_array($staffAllOffDay, $weekDays)) {
                $this->setOffDays[] = $staffAllOffDay;
            }
        }

        foreach ($this->commonPublicHoliday as $holiday) {
            //给5天班的人
            if (in_array($holiday,$weekDays) && in_array($holiday, $this->setOffDays) &&   !in_array(date('N', strtotime($holiday)),self::$staff_week_work_day_diff_off_day[HrStaffInfoModel::WEEK_WORKING_DAY_5])) {
                $this->notSaturdayAndSundayCommonPHDays[] = $holiday;
            }
            //给六天班的人
            if (in_array($holiday,$weekDays) && in_array($holiday, $this->setOffDays) &&  date('N', strtotime($holiday)) != 7) {
                $this->notSundayCommonPHDays[] = $holiday;
            }
        }

        return $this;
    }


    /**
     * 保存轮休数据
     * @throws Exception
     */
    protected function addStaffWorkdays(): bool
    {
        //验证设置日期
        if (!$this->isWorkDayRootId) {
            if ($this->canModifyTodayRest && $this->date < date('Y-m-d')) {
                throw new BusinessException(self::$t->_('cannot_be_set_on_less_than_today'));
            }
            if (!$this->canModifyTodayRest && $this->date <= date('Y-m-d')) {
                throw new BusinessException(self::$t->_('cannot_be_set_on_today_or_less_than_today'));
            }
        }

        //新增规则配置校验 能否修改自己的休息日
        if(!$this->canEditSelf){
            throw new BusinessException(self::$t->_('can_not_edit_self_off'));
        }

        $staffOffDayService = new StaffOffDayService();
        //一周内已经设置的休息日
        $setOffDays = $this->setOffDays;

        $set_limit = 7 - ($this->staffInfo['week_working_day'] ?: 6);
        //已经设置过了
        if (!$this->isWorkDayRootId && in_array($this->date, $setOffDays)) {
            throw new BusinessException(self::$t->_('workdays_already_set',
                ['date' => $this->date, 'staff_info_id' => $this->staff_info_id, 'name' => $this->staffInfo['name']]));
        }

        //轮休 6天班的可以设置 1天休息日，5天班的可以设置 2天休息日 ph不占用额度
        if (!$this->isWorkDayRootId && $this->staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_1 && !in_array($this->date, $this->allPublicHoliday) && count(array_diff($setOffDays, $this->allPublicHoliday)) >= $set_limit) {
            throw new BusinessException(self::$t->_('workdays_set_limit',
                ['num' => $set_limit, 'staff_info_id' => $this->staff_info_id, 'name' => $this->staffInfo['name']]));
        }
        //固定休 6天班的可以设置 1天休息日，5天班的可以设置 2天休息日
        if(!$this->isWorkDayRootId && $this->staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_2){
            $num = count($setOffDays);
            if ($this->staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_6) {
                $num -= count($this->notSundayCommonPHDays);
            } elseif ($this->staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_5) {
                $num -= count($this->notSaturdayAndSundayCommonPHDays);
            }

            if ($num >= $set_limit) {
                throw new BusinessException(self::$t->_('workdays_set_limit',
                    ['num'           => $set_limit,
                     'staff_info_id' => $this->staff_info_id,
                     'name'          => $this->staffInfo['name'],
                    ]));
            }
        }

        //验证 建议排班 人数 只能多 不能少于建议 只有泰国 新增 马来 菲律宾 改成所有国家都有https://flashexpress.feishu.cn/docx/Gsvhd5rtpoKS2AxgWQKc6zTcnYg
        $this->checkWorkDaySuggest([$this->staff_info_id],[$this->date],$this->operator);


        //可以设置休息日
        $insert['staff_info_id'] = $this->staff_info_id;
        $insert['month']         = date('Y-m', strtotime($this->date));
        $insert['date_at']       = $this->date;
        $insert['type']          = HrStaffWorkDaysModel::TYPE_1;
        $insert['operator']      = $this->operator;
        $insert['remark']        = 'api add';
        //处理员工个人的公共假期
        $ph = $this->dealStaffPublicHoliday();
        if ($ph) {
            $insert['staff_ph_id'] = $ph->id;
            //给补的ph 加 off
            $phData                = empty($ph->date_at) ? [] : [$ph->date_at];
            $extend['staff_ids']   = [$this->staff_info_id];
            $extend['staff_ph_id'] = $ph->id;
            $extend['operate_id'] = $this->operator;
            $extend['remark'] = HrStaffWorkDaysModel::REMARK_FOR_COMPENSATE_OFF;
            $this->holidayServer->addDefaultOff($phData, $extend);

        }
        if (!$staffOffDayService->saveData($insert)) {
            throw  new \Exception('eeeeee1');
        }
        //操作日志
        $insert['operate_id'] = $this->operator;
        $logServer = new WorkShiftService();
        $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF,$insert);

        return true;
    }

    /**
     * 固定修
     * 休息日遇到PH 将最近的一个工作日设置为PH
     * 马来、越南重写
     */
    protected function dealStaffPublicHoliday()
    {
        //印尼没有不假
        if (isCountry('ID')) {
            return false;
        }

        //非固定休的，不补
        if ($this->staffInfo['rest_type'] != \App\Models\backyard\HrStaffInfoModel::REST_TYPE_2) {
            return false;
        }

        //要设置的休息日当天不是PH 不需要补
        if (!in_array($this->date, $this->commonPublicHoliday)) {
            return false;
        }

        $week_day = date('N', strtotime($this->date));

        //当天不是周六 或 周六日 staff_week_work_day_diff_off_day
        if (!in_array($week_day, self::$staff_week_work_day_diff_off_day[$this->staffInfo['week_working_day']])) {
            return false;
        }

        $tm = $this->staffInfo['week_working_day'] == \App\Models\backyard\HrStaffInfoModel::WEEK_WORKING_DAY_5 && $week_day == 6 ? 2 : 1;

        //需要补未ph的日子
        $need_public_holiday       = date('Y-m-d', strtotime($this->date) + $tm * 86400);

        //默认 周六 补下周一  周日补下周二 万一周一或二也是PH\off  就再往后挪一天
        while (in_array($need_public_holiday,$this->allPublicHoliday) || in_array($need_public_holiday,$this->staffAllOffDays)) {
            $need_public_holiday = date('Y-m-d', strtotime($need_public_holiday) + 86400);
        }
        return (new StaffPublicHolidayService())->saveData([
            'staff_info_id' => $this->staff_info_id,
            'date_at'       => $need_public_holiday,
            'src_date'      => $this->date,
            'remark'        => $this->date,
            'is_deleted'    => 0,
        ]);
    }

    /**
     * 固定修  la  vn 在用
     * 休息日遇到PH 将最近的一个工作日设置为OFF
     */
    protected function dealStaffOffDay(): bool
    {

        //非固定休的，不补
        if ($this->staffInfo['rest_type'] != HrStaffInfoModel::REST_TYPE_2) {
            return false;
        }

        //要设置的休息日当天不是PH 不需要补
        if (!in_array($this->date, $this->commonPublicHoliday)) {
            return false;
        }

        $week_day = date('N', strtotime($this->date));

        //当天不是周六 或 周六日 staff_week_work_day_diff_off_day
        if (!in_array($week_day, self::$staff_week_work_day_diff_off_day[$this->staffInfo['week_working_day']])) {
            return false;
        }
        //补休跨度。
        $tm = (new StaffPublicHolidayService())->getSupplement($this->staffInfo['week_working_day'],$week_day);

        //需要补Off的日子
        $need_off_day       = date('Y-m-d', strtotime($this->date) + $tm * 86400);

        $staffOffDayService = new StaffOffDayService();

        //默认 周六 补下周一  周日补下周二 万一周一或二也是off \ ph 就再往后挪一天
        while ($staffOffDayService->getStaffDataByDates($this->staff_info_id, [$need_off_day]) || in_array($need_off_day,$this->allPublicHoliday)) {
            $need_off_day = date('Y-m-d', strtotime($need_off_day) + min($tm,1) * 86400);
        }
        return $staffOffDayService->saveData([
            'staff_info_id' => $this->staff_info_id,
            'date_at'       => $need_off_day,
            'month'         => date('Y-m',strtotime($need_off_day)),
            'remark'        => $this->date . ' PH',
            'src_date'      => $this->date,
        ]);
    }

    /** 获取员工轮休班次列表  查询条件  区域 area 网点 store 职位 job_title 工号 staff_id
     * 需求：
     * 表单数据包含所有Flash员工管理和实习生员工管理中的主账号数据；表单默认状态是在职、停职员工数据；不查离职员工。
     * 新增 需求 部门是 ka 客服 和 hub 也可以设置轮休
     * 新增需求  列表增加 260 jobtitle Driver Management
     * 新增需求 列表要加上直属下级 （经排查 只有部门负责人 和区域经理 需要 or连接 直属下级查询）
     * @param $param
     * @param $operatorId
     */
    public function search_staff_workdays($param, $operatorId)
    {
        $store     = empty($param['store_id']) ? [] : $param['store_id'];
        //网点改成多选了 历史数据处理
        if(!is_array($store)){
            $store = [$store];
        }
        $job_title = empty($param['job_title']) ? [] : $param['job_title'];
        $staff_id  = empty($param['staff_info_id']) ? '' : trim($param['staff_info_id']);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['staff' => HrStaffInfoReadModel::class]);
        $builder->join(HrJobTitleModel::class, 'staff.job_title  = job.id', 'job');
        $builder->leftJoin(SysStoreModel::class, 'store.id = staff.sys_store_id', 'store');
        $builder->leftJoin(SysProvinceModel::class, 'pro.code = store.province_code', 'pro');
        $builder->where('staff.formal in(1,4) and staff.is_sub_staff = 0  ');

        //雇佣类型
        if (!empty($param['hire_type'])) {
            $builder->inWhere('staff.hire_type', $param['hire_type']);
        }
        //在职状态
        if (!empty($param['state'])) {
            if (in_array(
                BaseEnums::HRIS_WORKING_STATE_4, $param['state'])) {
                $builder->andWhere("staff.state IN ({states:array}) OR (staff.state=:state: AND staff.wait_leave_state=:wait_leave_state:)",
                    [
                        'states'           => $param['state'],
                        'state'            => BaseEnums::HRIS_WORKING_STATE_1,
                        'wait_leave_state' => BaseEnums::WAIT_LEAVE_STATE,
                    ]);
            } elseif (!in_array(BaseEnums::HRIS_WORKING_STATE_4,
                    $param['state']) && in_array(BaseEnums::HRIS_WORKING_STATE_1, $param['state'])) {
                $builder->andWhere("staff.state IN ({states:array}) AND staff.wait_leave_state != :wait_leave_state:",
                    ['states' => $param['state'], 'wait_leave_state' => BaseEnums::WAIT_LEAVE_STATE]);
            } else {
                $builder->andWhere("staff.state IN ({states:array})", ['states' => $param['state']]);
            }
        }
        if (!empty($param['week_working_day'])) {
            $builder->andWhere('staff.week_working_day = :week_working_day:',
                ['week_working_day' => $param['week_working_day']]);
        }

        if (!empty($param['rest_type'])) {
            $builder->andWhere('staff.rest_type = :rest_type:', ['rest_type' => $param['rest_type']]);
        }

        if (!empty($param['area'])) {
            $builder->inWhere('pro.sorting_no', $param['area']);
        }

        if (!empty($store)) {
            $builder->inWhere('staff.sys_store_id', $store);
        }
        if (!empty($job_title)) {
            $job_title = is_array($job_title) ? $job_title : [$job_title];
            $builder->inWhere('staff.job_title', $job_title);
        }

        if (!empty($staff_id)) {
            $builder->andWhere('(staff.staff_info_id = :staff_info_id: or staff.name like :name:)',
                ['staff_info_id' => $staff_id, 'name' => '%'.$staff_id.'%']);
        }
        //搜索部门 或子部门
        if (!empty($param['search_department'])) {
            $builder->andWhere('(staff.sys_department_id = :sys_department_id: or node_department_id = :node_department_id:)',
                [
                    'sys_department_id'  => $param['search_department'],
                    'node_department_id' => $param['search_department'],
                ]);
        }

        //通用数据权限限定条件
        $condition = (new AttendanceStatisticsService())->buildStaffPurviewData($operatorId, 'staff');
        if ($condition['condition'] && $condition['bind']) {
            $builder->andWhere($condition['condition'], $condition['bind']);
        }

        $builder->columns('count(1) as count');
        $count           = $builder->getQuery()->getSingleResult()->toArray();
        $return['count'] = $count['count'];

        $builder->columns('staff.staff_info_id,staff.name staff_name,staff.sex,staff.week_working_day
                ,job.job_name,sys_store_id id
                ,store.name store_name
                ,pro.sorting_no as area
                ,staff.node_department_id
                ,staff.hire_type
                ,staff.hire_date
                ,staff.state
                ,staff.wait_leave_state
                ,staff.job_title');
        $page  = empty($param['start']) ? 0 : $param['start'] - 1;
        $limit = intval($param['length']);
        $builder->limit($limit, $page * $limit);
        $data = $builder->getQuery()->execute()->toArray();

        $return['data'] = $data;
        return $return;
    }


    public function formatList($param, $data)
    {
        $month = $param['month'] ?? date('Y-m', time());
        //获取公休日 1:6天班的 2:5天班
        $holiday_data = $this->get_holidays(['type' => 7 - $param['week_working_day'], 'date' => $month . '-01']);
        $holidays     = $holiday_data[$param['week_working_day']] ?? [];

        //获取 轮休日期
        $staff_ids   = array_column($data, 'staff_info_id');
        $workDaysOff = $this->get_workdays_by_ids($staff_ids, $month);

        //班次 班次server  过去日期 history表 当天 hr shift 未来 临时中间表和 preset
        $shiftServer     = new HrShiftService();
        $dateList        = getDateByMonth($month);
        //如果 查询开始日期小于当天把上个月的也取出来  主播职位不查班次
        $this->setLiveJob();
        $shiftStaffIds = $this->getShiftStaffIds($data);
        if(date('Y-m-d') < $dateList[0]){
            $lastMonth = date('Y-m',strtotime("{$dateList[0]} -1 month"));
            $dates = array_merge(getDateByMonth($lastMonth),$dateList);
            $this->shiftData = $shiftServer->getShiftInfos($shiftStaffIds, $dates);
        }else{
            $this->shiftData = $shiftServer->getShiftInfos($shiftStaffIds, $dateList);
        }

        //获取 请假
        $leaveServer     = new LeaveBllService();
        $leaveData       = $leaveServer->getLeaveByDate($staff_ids, $dateList);
        $this->leaveData = empty($leaveData) ? [] : array_column($leaveData, null, 'u_key');

        //每个格子的请假班次信息 把请假和班次 拼装到一个list
        $cubeInfo = $this->workdayCube($staff_ids, $dateList);

        //获取员工个人维度的公共假期
        $staffPublicHoliday = (new StaffPublicHolidayService())->getMultiStaffData($staff_ids);
        $defaultRestStaffMap = (new DefaultRestDayService())->getAll($staff_ids);

        foreach ($data as &$v) {
            if ($v['state'] == HrStaffInfoModel::STATE_ON_JOB && $v['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $v['state'] = 999;
            }
            $v['state_name']       = !empty(\App\Library\Enums::$hris_working_state[$v['state']]) ? self::$t->_(\App\Library\Enums::$hris_working_state[$v['state']]) : '';
            //主播职位 不能点击班次
            $v['is_live_master'] = in_array($v['job_title'], $this->liveJobId);
            $v['shift'] = empty($workDaysOff[$v['staff_info_id']][$month]) ? [] : $workDaysOff[$v['staff_info_id']][$month];
            //拼接总部名称
            if (empty($v['store_name']) && $v['id'] == '-1') {
                $v['store_name'] = GlobalEnums::HEAD_OFFICE;
            }
            $v['hire_type_text']        = self::$t->_('hire_type_'.$v['hire_type']);
            //默认轮休日
            $v['default_rest_day_date'] = $defaultRestStaffMap[$v['staff_info_id']] ?? [];

            $v['holiday']['ph'] = array_merge($holidays, $staffPublicHoliday[$v['staff_info_id']] ?? []);
            //方块里面新增的信息
            $v['cube_info'] = $cubeInfo[$v['staff_info_id']] ?? [];
        }
        return $data;
    }

    //获取排除调 主播职位的工号
    public function getShiftStaffIds($data)
    {
        $jobData   = array_column($data, 'job_title', 'staff_info_id');
        $res       = [];
        foreach ($jobData as $staffId => $job) {
            if (!in_array($job, $this->liveJobId)) {
                $res[] = $staffId;
            }
        }
        return $res;
    }

    /**
     * @param $staff_ids //员工工号
     * @param $month //要查询的月份
     * @return array
     */
    public function get_workdays_by_ids($staff_ids, $month = '')
    {
        if (empty($staff_ids)) {
            return [];
        }

        $staffs = array_chunk($staff_ids, 500);
        $data   = [];
        foreach ($staffs as $staff) {
            $str_staff_ids = implode(',', $staff);
            $sql           = "select * from hr_staff_work_days where staff_info_id in ({$str_staff_ids})";
            if (!empty($month)) {
                $sql .= " and month = '{$month}'";
            }

            $res  = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC) ?: [];
            $data = array_merge($data, $res);
        }

        $return = [];
        if (!empty($data)) {
            foreach ($data as $v) {
                $return[$v['staff_info_id']][$v['month']][] = $v['date_at'];
            }
        }

        return $return;
    }


    /**
     * @param $staff_id
     * @param $date
     * @param $operateId
     * @return array
     */
    protected function beforeCancelValidation($staff_id, $date, $operateId): array
    {
        return [];
    }


    protected function afterCancel($staff_id, $date, $operator): bool
    {
        (new HrStaffWorkDaysChangeModel())->insert_record([
            'month'         => date('Y-m', strtotime($date)),
            'staff_info_id' => $staff_id,
            'date_at'       => $date,
            'type'          => HrStaffWorkDaysChangeModel::TYPE_DELETE,
            'operator'      => $operator,
        ]);
        //日志
        $this->logger->info("staff_workdays_cancel {$staff_id} {$date} {$operator}");
        return true;
    }



    /**
     * @param $staff_id
     * @param $date
     * @param $operateId
     * @return true|array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function cancel($staff_id, $date, $operateId = 10000)
    {
        //取消前的验证
        $res =  $this->beforeCancelValidation($staff_id, $date, $operateId);
        if (!empty($res)) {
            return $res;
        }

        $staff_id = intval($staff_id);
        $date     = date('Y-m-d', strtotime($date));
        $db       = HrStaffWorkDaysModel::beginTransaction($this);
        $logDates = [$date];
        try {
            $conditions = 'staff_info_id = :staff_info_id: AND date_at = :start_date: ';
            $bind       = ['staff_info_id' => $staff_id, 'start_date' => $date];
            $data       = HrStaffWorkDaysModel::findFirst([
                'columns'    => 'date_at,staff_info_id,staff_ph_id',
                'conditions' => $conditions,
                'bind'       => $bind,

            ]);
            if (empty($data)) {
                $db->commit();
                return true;
            }
            if ($data->staff_ph_id) {
                (new StaffPublicHolidayService())->saveData(['id' => $data->staff_ph_id, 'is_deleted' => 1]);
                //把 对应的 ph id 的 off 删除
                $defaultOff = HrStaffWorkDaysModel::find([
                    'conditions' => 'staff_info_id = :staff_id: and staff_ph_id = :staff_ph_id:',
                    'bind' => ['staff_id' => $staff_id,'staff_ph_id' => $data->staff_ph_id],
                ]);
                //把对应的ph日期的 off 删除
                if(!empty($defaultOff->toArray())){
                    //有可能多条数据
                    $dates = array_column($defaultOff->toArray(),'date_at');
                    $logDates = array_merge($logDates, $dates);
                    $defaultOff->delete();
                }

            }
            $sql = " delete from hr_staff_work_days where staff_info_id = {$staff_id} and date_at = '{$date}'";
            $this->getDI()->get('db_backyard')->execute($sql);
            $db->commit();
            //加日志
            $logDates = array_unique($logDates);
            foreach ($logDates as $day){
                $logParam['operate_id'] = $operateId;
                $logParam['staff_info_id'] = $staff_id;
                $logParam['date_at'] = $day;
                //业务操作 不记录remark
                $extend = [];
                //任务跑的 记录轮休规则变更
                if($operateId == 10000){
                    $extend['t_key'] = 'workday_role_change';
                }
                $logServer = new WorkShiftService();
                $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF,$logParam);
            }
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
        $this->afterCancel($staff_id, $date, $operateId);
    }

    //根据日期获取轮休配置
    public function get_workdays($staff_id, $str_where)
    {
        $sql = "select staff_info_id,group_concat(date_at) date_at 
                from hr_staff_work_days where staff_info_id  in ({$staff_id}) and date_at in ({$str_where})
                group by staff_info_id
                ";

        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    //获取 该日期 是否有审核通过的休息日 类型的请假
    public function check_leave($staff_id, $date)
    {
        $sql = "select a.audit_id,a.audit_type,a.leave_type
              from staff_audit_leave_split sp
              join staff_audit a on sp.audit_id = a.audit_id
              where sp.staff_info_id = {$staff_id} and sp.date_at = '{$date}' 
              and a.status = 2 and a.audit_type = 2 and a.leave_type = 15 
              
              ";

        return $this->getDI()->get('db_rby')->fetchOne($sql);
    }

    /**
     * @description:获取 该日期 是否有审核通过或者审批中的加班
     *
     * @param $staff_id  int 用户 id
     * @param $start_date  string 开始日期
     * @param $end_date  string 结束日期
     * @return     : array []
     * <AUTHOR> L.J
     * @time       : 2021/12/22 11:52
     */

    public function check_overtime($staff_id, $start_date = '', $end_date = '')
    {
        $result = [];
        if (empty($staff_id) || empty($start_date)) {
            return $result;
        }
        $conditions = "staff_id = :staff_id: and state in (1,2) and date_at >= :start_time:  and date_at  <= :end_time: ";
        $bind       = [
            'staff_id'   => $staff_id,
            'start_time' => $start_date,
            'end_time'   => $start_date,
        ];
        if (!empty($end_date)) {
            $bind['end_time']   = $end_date;
        }

        $overtime_result = HrOvertimeModel::findFirst([
            'conditions' => $conditions,
            'columns'    => 'overtime_id,start_time,end_time,state,date_at',
            'bind'       => $bind,
        ]);
        if ($overtime_result) {
            $result = $overtime_result->toArray();
        }
        return $result;
    }

    /**
     * @description:获取 该日期 是否有审核通过或者审批中的加班
     * @param $staff_id  int 用户 id
     * @param $start_date  string 开始日期
     * @param $end_date  string 结束日期
     * @param $leave_type  int  需要排除的类型
     * @return     : array []
     * <AUTHOR> L.J
     * @time       : 2021/12/22 16:41
     */
    public function check_leave_all($staff_id, $start_date = '', $end_date = '', $leave_type = '')
    {
        $result = [];
        if (empty($staff_id) || empty($start_date)) {
            return $result;
        }
        $conditions = "staff_info_id = :staff_id:  and status in (1,2)  and date_format(leave_start_time,'%Y-%m-%d') <= :start_time:  and date_format(leave_end_time,'%Y-%m-%d') >= :end_time: ";
        $bind       = [
            'staff_id'   => $staff_id,
            'start_time' => $start_date,
            'end_time'   => $start_date,
        ];

        if (!empty($end_date)) {
            $conditions             = "staff_info_id = :staff_id:  and status in (1,2)  and ((date_format(leave_start_time,'%Y-%m-%d') <= :start_time:  and date_format(leave_end_time,'%Y-%m-%d') >= :end_time:) or (date_format(leave_start_time,'%Y-%m-%d') <= :end_date_start:  and date_format(leave_end_time,'%Y-%m-%d') >= :end_date_end:) or (date_format(leave_start_time,'%Y-%m-%d') >= :start_time:  and date_format(leave_end_time,'%Y-%m-%d') <= :end_date_end:) ) ";
            $bind['end_date_start'] = $end_date;
            $bind['end_date_end']   = $end_date;
        }


        if (!empty($leave_type)) {
            $conditions         .= " and leave_type != :leave_type: ";
            $bind['leave_type'] = $leave_type;
        }


        $staff_audit_info = StaffAuditModel::findFirst([
            'conditions' => $conditions,
            'columns'    => 'staff_info_id,leave_start_time,leave_end_time',
            'bind'       => $bind,
        ]);
        if ($staff_audit_info) {
            $result = $staff_audit_info->toArray();
        }
        return $result;
    }


    //设置休息日
    public function set_staff_work_days($data)
    {
        $connection = $this->getDI()->get('db_backyard');
        $table      = 'hr_staff_work_days';
        return $connection->insertAsDict($table, $data);
    }

    //设置休息日
    public function batch_workdays($data)
    {
        $model = new HrStaffWorkDaysModel();
        if (!empty($data)) {
            foreach ($data as $da) {
                $clone = clone $model;
                $row   = [];
                $info  = $model::findFirst("staff_info_id = {$da['staff_info_id']} and month = '{$da['month']}' and date_at = '{$da['date_at']}' ");
                if (empty($info)) {
                    $row['staff_info_id'] = $da['staff_info_id'];
                    $row['month']         = $da['month'];
                    $row['date_at']       = $da['date_at'];
                    $row['remark']        = $da['remark'] ?? '';
                    $row['type']          = $da['type'] ?? HrStaffWorkDaysModel::TYPE_1;
                    $row['operator']      = $da['operator'];
                    $clone->create($row);
                }
                continue;
            }
        }

        return true;
//
//
//        $keys = array_keys(reset($data));
//        $keys = array_map(function ($key) {
//            return "`{$key}`";
//        }, $keys);
//        $keys = implode(',', $keys);
//        $sql = "INSERT INTO hr_staff_work_days ({$keys}) VALUES ";
//
//        foreach ($data as $v) {
//            $v = array_map(function ($value) {
//                if ($value === null) {
//                    return 'NULL';
//                } else {
//                    $value = addslashes($value); //处理特殊符号，如单引号
//                    return "'{$value}'";
//                }
//            }, $v);
//
//            $values = implode(',', array_values($v));
//            $sql .= " ({$values}), ";
//        }
//        $sql = rtrim(trim($sql), ',');
//
//        return $this->getDI()->get('db_backyard')->execute($sql);


    }

    /**
     * 删除 指定月份 未发生的 休息日
     * @param $staff_ids
     * @param $month
     * @param $today
     * @return mixed
     */
    public function delete_for_recover($staff_ids, $month, $today)
    {
        $str_staff = implode(',', $staff_ids);
        $sql       = "delete from hr_staff_work_days where staff_info_id in ({$str_staff}) and `month` = '{$month}' and date_at > '{$today}'";
        return $this->getDI()->get('db_backyard')->execute($sql);
    }

    /**
     * 获取员工 指定周 是否有休息记录
     * @param $staff_id
     * @param $str_where
     */
    public function count_week_restday($staff_id, $str_where)
    {
        $sql = "select count(1) from hr_staff_work_days where staff_info_id = {$staff_id} and date_at in ({$str_where})";

        return $this->getDI()->get('db_rby')->fetchColumn($sql);
    }

    //批量获取 员工 每周是否有休息日
    public function count_restdays($staff_ids, $str_where)
    {
        $sql = "select count(1) `num` from hr_staff_work_days 
                where staff_info_id in ({$staff_ids}) and date_at in ({$str_where})
                group by staff_info_id ";

        return $this->getDI()->get('db_rby')->fetchAll($sql);
    }

    //轮休 新增 直属下级 员工
    public function get_sub_staff($staff_id)
    {
        $sql  = " select `staff_info_id` from hr_staff_items where item = 'MANGER' and `value` = '{$staff_id}'";
        $data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($data)) {
            return array_column($data, 'staff_info_id');
        }
        return $data;
    }

    public function get_week_days($date, $is_array = 0, $holidays)
    {
        $date_num = date('w', strtotime($date));
        if ($date_num == 0) {
            $date_num = 7;
        }
        $week[] = $date;
        $num    = 0;
        for ($i = $date_num; $i > 1; $i--) {
            $num++;
            $week[] = date('Y-m-d', strtotime("{$date} -{$num} day"));
        }

        $num = 0;
        for ($i = $date_num; $i < 7; $i++) {
            $num++;
            $week[] = date('Y-m-d', strtotime("{$date} +{$num} day"));
        }
        sort($week);

        foreach ($week as $k => $d) {
            if (in_array($d, $holidays)) {
                unset($week[$k]);
            }
        }

        if ($is_array) {
            return $week;
        } else {
            $str_where = "'".implode("','", $week)."'";
            return $str_where;
        }
    }


    /**
     * 导出轮休
     * @param $result
     * @param $month
     * @return array
     */
    public function export_rest_workday($result, $month): array
    {
        if (empty($result['data'])) {
            return [];
        }
        $staff_ids     = array_column($result['data'], 'staff_info_id');
        $shift         = $this->get_workdays_by_ids($staff_ids, $month);
        $defaultServer = new DefaultRestDayService();
        $defaultRest   = $defaultServer->getAll($staff_ids);
        $weekArr       = ShiftEnums::$num2Week;

        $returnData = [];
        foreach ($result['data'] as $item) {
            $item['shift'] = empty($shift[$item['staff_info_id']][$month]) ? [] : $shift[$item['staff_info_id']][$month];
            //拼接总部名称
            if (empty($item['store_name']) && $item['id'] == '-1') {
                $item['store_name'] = GlobalEnums::HEAD_OFFICE;
            }
            //拼接 默认休息日
            $strWeekRest = '';
            if (isset($defaultRest[$item['staff_info_id']])) {
                foreach ($defaultRest[$item['staff_info_id']] as $num) {
                    $strWeekRest .= $weekArr[$num] . ' ';
                }
            }
            $item['week_rest'] = $strWeekRest;

            $state = $item['state'];
            if ($item['state'] == HrStaffInfoModel::STATE_ON_JOB && $item['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {
                $state = 999;
            }

            $store_name = $this->showStoreName($item['id']);
            $area_name = $item['area'];
            if (isCountry('PH')) {
                $area_name = $this->showRegionNameByStoreId($item['id']);
            }
            $row          = [];
            $row[]        = $item['staff_info_id'] . "({$item['staff_name']})";
            $row[]        = $area_name;
            $row[]        = $store_name;
            $row[]        = $item['job_name'];
            $row[]        = self::$t->_(Enums::$hris_working_state[$state]);
            $row[]        = self::$t->_('hire_type_' . $item['hire_type']);
            $row[]        = $item['week_rest'] ?? '';
            $row[]        = empty($item['shift']) ? '' : implode(' | ', $item['shift']);
            $returnData[] = $row;
        }
        return $returnData;
    }


    //获取对应 公休日 type 对应类型 0-默认 工作5，6天公用
    // 1 - 6天班员工用
    // 2- 针对泰国法律 5天班员工用
    public function get_holidays($param)
    {
        $holidayService = new HolidayService();
        return $holidayService->getHoliday($param);
    }


    /**
     * @description: 发送消息  这里不用了 用 从 bi  cp 过来的方法
     *
     * @param $staff_ids 需要发送消息的人 数据
     * @param $date 需要发送的日期
     * @param $type 1 是取消休息日 2 是 设置休息日
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/12/22 19:32
     */
    public function send_message($staff_ids = [], $date = [], $type = 1)
    {
        try {
            $staff_ids = array_values($staff_ids);
            if (empty($staff_ids) || empty($date)) {
                throw new \Exception('参数为空!');
            }
            //查询用户信息
            $builder = new  Builder();
            $builder->columns([
                'hsi.staff_info_id,
			                               hsi.name,
                                          hsi.job_title,
                                          job.job_name',
            ])->from(['hsi' => HrStaffInfoModel::class])
                ->leftJoin(HrJobTitleModel::class, 'job.id = hsi.job_title', 'job')
                ->inWhere('hsi.staff_info_id', $staff_ids);
            $staff_data = $builder->getQuery()->execute()->toArray();
            if (!$staff_data) {
                throw new \Exception(' 没有找到用户数据!');
            }
            //查询用户上次登录语言
            $staffAccountList = StaffAccountModel::find([
                'conditions' => ' staff_info_id in ({staff_info_ids:array}) ',
                'bind'       => ['staff_info_ids' => $staff_ids],
                'columns'    => 'staff_info_id,accept_language',
            ])->toArray();

            $staffAccountList = array_column($staffAccountList, 'accept_language', 'staff_info_id');
            //title
            //这里是日期
            $staff_date = implode(',', $date);
            foreach ($staff_data as $k => $v) {
                //定义语言
                $locale = 'en';
                if (isset($staffAccountList[$v['staff_info_id']])) {
                    $locale = in_array($staffAccountList[$v['staff_info_id']],
                        ['th', 'th-CN']) ? 'th' : $staffAccountList[$v['staff_info_id']];
                }
                $getTranslation = self::getTranslation($locale);
                //发送消息  1 是取消 2 是调整
                $title   = $type == 1 ? $getTranslation->_('work_day_title_cancel') : $getTranslation->_('work_day_title_adjustment');
                $content = $type == 1 ? $getTranslation->_('work_day_content_cancel') : $getTranslation->_('work_day_content_adjustment');
                $content = str_replace([
                    "#staff_name#",
                    "#staff_info_id#",
                    "#staff_job_title#",
                    "#staff_date#",
                ], [
                    $v['name'],
                    $v['staff_info_id'],
                    $v['job_name'],
                    $staff_date,
                ], $content);
                //添加消息 必填项
                $message_param['staff_users']        = [$v['staff_info_id']];//数组 多个员工id
                $message_param['message_title']      = $title;
                $message_param['message_content']    = addslashes($content);
                $message_param['staff_info_ids_str'] = $v['staff_info_id'];
                $message_param['id']                 = time().$v['staff_info_id'].rand(10, 9999);
                $message_param['category']           = -1;

                $res = (new MessagesService())->add_kit_message($message_param);

                if ($res[1] == 1) {
                    $this->logger->info(" workday send_message {$v['staff_info_id']}_{$type}_{$staff_date} 写入message成功");
                } else {
                    $this->logger->error(" workday send_message {$v['staff_info_id']}_{$type}_{$staff_date} 写入message失败");
                }
            }
        } catch (\Exception $e) {
            //日志
            $params = ['staff_ids' => $staff_ids, 'date' => $date, 'type' => $type];
            $this->logger->info(" workday send_message params ".json_encode($params)." result=>".$e->getMessage());
            return false;
        }
        return true;
    }


    //轮休设置 设置/取消休息日发送backyard消息
    public function staff_set_work_day_send_message($staff_info_id, $days, $send_type = 1)
    {
        try {
            $log = [
                'staff_info_id' => $staff_info_id,
                'days'          => $days,
                'sen_typ'       => $send_type,
            ];
            $this->getDI()->get('logger')->write_log('staff_cancel_work_day_send_message param: '.json_encode($log),
                'info');

            $locale = (new StaffService())->getAcceptLanguage($staff_info_id);

            $t               = self::getTranslation($locale);
            $days_arr        = $this->message_day_format([$days], $locale);
            $days            = implode("、", $days_arr);
            $staff_info_list = $this->get_staff_info_list([$staff_info_id]);
            $staff_info_name = $staff_info_list[$staff_info_id]['name'] ?? '';
            $job_title_name  = $staff_info_list[$staff_info_id]['job_title_name'] ?? '';
            $str_replace_arr = [
                "#staff_name#",
                "#staff_info_id#",
                "#staff_job_title#",
                "#staff_date#",
            ];

            $str_replace_content = [
                $staff_info_name,
                $staff_info_id,
                $job_title_name,
                $days,
            ];
            switch ($send_type) { //1取消 2设置
                case 1:
                    $by_message_content = $t->_('work_day_content_cancel');
                    $message['title']   = $t->_('work_day_title_cancel');
                    $message['content'] = str_replace($str_replace_arr, $str_replace_content, $by_message_content);
                    break;
                case 2:
                    $by_message_content = $t->_('work_day_content_adjustment');
                    $message['title']   = $t->_('work_day_title_adjustment');
                    $message['content'] = str_replace($str_replace_arr, $str_replace_content, $by_message_content);
                    break;
                default:
                    $message = [];
            }
            $log['message'] = $message;
            $this->getDI()->get('logger')->write_log('staff_cancel_work_day_send_message param: '.json_encode($log),
                'info');
            if (empty($message)) {
                return false;
            }
            $result = $this->staff_work_day_send_message($staff_info_id, $message);

            $log['result'] = $result;
            $this->getDI()->get('logger')->write_log('staff_cancel_work_day_send_message param: '.json_encode($log),
                'info');
            return $result;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('staff_cancel_work_day_send_message error: '.$e->getMessage().';行号：'.$e->getLine());
            return false;
        }
    }

    //轮休设置 批量发送backyard消息
    public function staff_batch_send_message($param)
    {
        try {
            $this->getDI()->get('logger')->write_log('staff_batch_send_message 参数: '.json_encode($param), 'info');
            $staff_info_ids = array_unique(array_column($param, 'staff_info_id'));
            $days_date_at   = array_unique(array_column($param, 'date_at'));
            //$days_str = implode("、",$days);
            $staff_info_list        = $this->get_staff_info_list($staff_info_ids);
            $send_message_result    = [];
            $staff_account          = $this->get_staff_account_list($staff_info_ids);
            $staff_account_lang_arr = array_column($staff_account, 'accept_language', 'staff_info_id');
            foreach ($staff_info_ids as $key => $value) {
                $staff_info_id      = $value;
                $staff_account_lang = $staff_account_lang_arr[$staff_info_id] ?? 'zh-CN';
                if (strpos($staff_account_lang, 'th') === 0) {
                    $locale = 'th';
                } else {
                    if (strpos($staff_account_lang, 'en') === 0) {
                        $locale = 'en';
                    } else {
                        if (strpos($staff_account_lang, 'zh') === 0) {
                            $locale = 'zh-CN';
                        } else {
                            if (strpos($staff_account_lang, 'vn') === 0) {
                                $locale = 'vn';
                            } else {
                                $locale = $staff_account_lang ?? 'th';
                            }
                        }
                    }
                }

                $t                    = self::getTranslation($locale);
                $days                 = $this->message_day_format($days_date_at, $locale);
                $days_str             = implode("、", $days);
                $str_replace_arr      = [
                    "#staff_name#",
                    "#staff_info_id#",
                    "#staff_job_title#",
                    "#staff_date#",
                ];
                $staff_info_name      = $staff_info_list[$value]['name'] ?? '';
                $staff_job_title_name = $staff_info_list[$value]['job_title_name'] ?? '';
                $str_replace_content  = [
                    $staff_info_name,
                    $staff_info_id,
                    $staff_job_title_name,
                    $days_str,
                ];

                $message['title']   = $t->_('work_day_title_adjustment');
                $by_message_content = $t->_('work_day_content_adjustment');

                $message['content']    = str_replace($str_replace_arr, $str_replace_content, $by_message_content);
                $result                = $this->staff_work_day_send_message($staff_info_id, $message);
                $send_message_result[] = [
                    'staff_info_id' => $staff_info_id,
                    'days'          => $days_str,
                    'result'        => $result,
                ];
            }
            $this->getDI()->get('logger')->write_log('staff_batch_send_message 结果: '.json_encode($send_message_result),
                'info');
            return true;
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('staff_batch_send_message error: '.$e->getMessage().';行号：'.$e->getLine());
            return false;
        }
    }

    //格式化日期
    public function message_day_format($day_arr, $lang)
    {
        $message_day = [];
        foreach ($day_arr as $key => $value) {
            if (in_array($lang, ['th', 'th-TH'])) {
                $message_day[] = date('d/m/Y', strtotime("$value +543 year"));
            } else {
                if (in_array($lang, ['en', 'en-US'])) {
                    $message_day[] = date('d/m/Y', strtotime($value));
                } else {
                    if (in_array($lang, ['vn'])) {
                        $message_day[] = date('d/m/Y', strtotime($value));
                    } else {
                        $message_day[] = $value;
                    }
                }
            }
        }

        return !empty($message_day) ? $message_day : $day_arr;
    }


    //获取员工最后登录语言
    public function get_staff_account_list($staff_info_ids)
    {
        try {
            if (empty($staff_info_ids)) {
                return [];
            }
            //查询用户上次登录语言
            return StaffAccountModel::find([
                'conditions' => ' staff_info_id in ({staff_info_ids:array}) and accept_language IS NOT NULL ',
                'bind'       => ['staff_info_ids' => array_values($staff_info_ids)],
                'columns'    => 'staff_info_id,accept_language',
                'order'      => 'updated_at asc',
            ])->toArray();
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('WorkdayBLL get_staff_account_list error: '.$e->getMessage().';行号：'.$e->getLine());
            return [];
        }
    }

    //获取员工信息
    public function get_staff_info_list($staff_info_ids)
    {
        try {
            if (empty($staff_info_ids)) {
                return [];
            }
            $builder = $this->modelsManager->createBuilder();
            $builder->columns("hr_staff.staff_info_id,hr_staff.name,hr_staff.name_en,hr_staff.sys_store_id,hr_staff.mobile,hr_staff.state,hr_staff.email,hr_staff.job_title,job_title.job_name as job_title_name");
            $builder->from(['hr_staff' => HrStaffInfoModel::class]);
            $builder->leftjoin(HrJobTitleModel::class, 'job_title.id = hr_staff.job_title', 'job_title');
            $builder->inWhere('hr_staff.staff_info_id', $staff_info_ids);
            $staff_info_list = $builder->getQuery()->execute()->toArray();

            return array_column($staff_info_list, null, 'staff_info_id');
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('WorkdayBLL get_staff_info_list error: '.$e->getMessage().';行号：'.$e->getLine());
            return [];
        }
    }

    //轮休设置 发送backyard消息
    public function staff_work_day_send_message($staff_info_id, $message)
    {
        try {
            if (empty($staff_info_id) || empty($message)) {
                $this->getDI()->get('logger')->write_log('staff_work_day_send_message empty 参数:'.json_encode($message),
                    'info');
                return false;
            }
            $send_message = [
                'staff_users'        => [$staff_info_id],
                'message_title'      => $message['title'],
                'message_content'    => $message['content'],
                'staff_info_ids_str' => $staff_info_id,
                'category'           => '46',
                'id'                 => time().$staff_info_id.rand(1000000, 9999999),
            ];

            $res = (new MessagesService())->add_kit_message($send_message);
            if ($res[1] == 1) {
                $this->getDI()->get('logger')->write_log('staff_work_day_send_message success 数据返回: '.json_encode($res,
                        JSON_UNESCAPED_UNICODE).'参数:'.json_encode($send_message), 'info');
                return true;
            } else {
                $this->getDI()->get('logger')->write_log('staff_work_day_send_message 数据返回: '.json_encode($res,
                        JSON_UNESCAPED_UNICODE).'参数:'.json_encode($send_message), 'notice');
                return false;
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->write_log('staff_work_day_send_message error: '.$e->getMessage().';行号：'.$e->getLine());
            return false;
        }
    }


    /**
     * @description:请假撤销验证
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/12/23 13:33
     */

    public function by_revoke_check($staff_info_id, $start_date, $end_date, $leave_type, $lang = 'en')
    {
        $data = ['code' => ErrCode::SUCCESS, 'message' => 'SUCCESS'];
        BaseService::setLanguage($lang);
        $t          = BaseService::getTranslation($lang);
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date   = date('Y-m-d', strtotime($end_date));
        //撤销员工的休息日之前，校验休息日期间是不是有审批通过或者待审批的加班申请
        $check_overtime = $this->check_overtime($staff_info_id, $start_date, $end_date);
        if (!empty($check_overtime)) {
            return ['code' => ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR, 'msg' => $t['workdays_check_overtime_alert']];
        }
        //撤销员工的休息日之前，校验当天是不是有审批通过或者待审批的除了“休息日”外的其他类型的请假申请
        $check_leave_all = $this->check_leave_all($staff_info_id, $start_date, $end_date, $leave_type);
        if (!empty($check_leave_all)) {
            return ['code' => ErrCode::WORK_DAY_CHECK_LEAVE_ERROR, 'msg' => $t['workdays_check_leave_alert']];
        }
        return $data;
    }

    //删除休息日

    /**
     * @throws BusinessException
     */
    public function cancelOffDay($staff_id, $date, $operator)
    {
        $staff_id = intval($staff_id);
        $date     = date('Y-m-d', strtotime($date));
        $conditions = 'staff_info_id = :staff_info_id: AND date_at = :start_date: ';
        $bind       = ['staff_info_id' => $staff_id, 'start_date' => $date];
        $data       = HrStaffWorkDaysModel::findFirst([
            'columns'    => 'date_at,staff_info_id,src_date',
            'conditions' => $conditions,
            'bind'       => $bind,

        ]);
        if (empty($data)) {
            return true;
        }
        if ($data->src_date) {
            throw new BusinessException(self::$t->_('cannot_cancel_off_day'));
        }
        $db = BackyardBaseModel::beginTransaction($this);
        try {
            $date_at = $date;

            $srcData = HrStaffWorkDaysModel::find(
                [
                    'conditions' => "staff_info_id = :staff_info_id: AND (date_at = :date_at: OR src_date = :src_date:)",
                    'bind'       => [
                        'staff_info_id' => $staff_id,
                        'date_at'       => $date_at,
                        'src_date'      => $date_at,
                    ],
                ]
            );
            $logDates = [];
            if(!empty($srcData)){
                $logDates = array_column($srcData->toArray(),'date_at');
            }
            $srcData->delete();
            $db->commit();
            if(empty($logDates)){
                return true;
            }
            //加日志
            $logDates = array_unique($logDates);
            foreach ($logDates as $day){
                $logParam['operate_id'] = $operator;
                $logParam['staff_info_id'] = $staff_id;
                $logParam['date_at'] = $day;
                $logServer = new WorkShiftService();
                $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF,$logParam);
            }

        }catch (Exception $e){
            $db->rollBack();
            throw $e;
        }
        $this->afterCancel($staff_id, $date, $operator);
        return  true;
    }

    //如果配置日期 存在休息日不让 配置轮休
    public function leaveValidate()
    {
        $key = $this->date . '_' . $this->staff_info_id;
        if (isset($this->leaveData[$key])) {
            throw new ValidationException(self::$t->_('workday_leave_notice',
                ['staff_id' => $this->staff_info_id, 'staff_name' => $this->staffInfo['name']])
            );
        }
        return $this;
    }


    /**
     * 计算网点 日期区间内的 实际轮休 on 的人数 轮休页面排班建议用用 任务回写排班建议表用
     * 排版建议 分母 数据部门给的，分子 只看对应日期 对应网点的轮休表 其他的请假出差支援等等 都不管 产品定的（石阳）
     * 返回结构
     * $return = [
     * 'dc'      => [
     * '2023-06-01_08:30' => 2,
     * '2023-06-01_11:30' => 3,
     * ],
     * 'courier' => [
     * '2023-06-01' => 4,
     * ],
     * ];
     * @param $storeId
     * @param $startDate
     * @param $endDate
     * @param $suggest
     * @return array
     */
    public function workdaySuggest($storeId, $startDate, $endDate, $suggest)
    {
        //网点所有人 指定参与计算职位的人
        /*
        *   1「仓管员」：包含[37]DC Officer、[451]Assistant Branch Supervisor
           2「快递员」：包含[13]Bike Courier、[110]Van Courier、[452]Boat Courier
        */
        $dcJob      = (new SettingEnvService)->getSetVal('shift_dc_job_title') ?? [];
        $courierJob = (new SettingEnvService)->getSetVal('shift_courier_job_title') ?? [];
        $dcJob      = empty($dcJob) ? [] : explode(',', $dcJob);
        $courierJob = empty($courierJob) ? [] : explode(',', $courierJob);
        $jobs       = array_merge($dcJob, $courierJob);
        if(empty($jobs)){
            return [];
        }

        //对应网点的所有在职员工 指定职位 非子账号 6天班 轮休
        $staffs = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,job_title',
            'conditions' => 'sys_store_id = :store_id: and job_title in ({jobs:array}) and state = 1 and is_sub_staff = 0 and week_working_day = 6 and rest_type = 1
             and formal in (1,4)',//测试要加的
            'bind'       => ['store_id' => $storeId, 'jobs' => $jobs],
        ])->toArray();

        $staffIds  = array_column($staffs, 'staff_info_id');
        if(empty($staffIds)){
            $this->logger->info("{$storeId} 网点没有对应职位的员工数据");
            return [];
        }
        $jobStaffs = array_column($staffs, 'job_title', 'staff_info_id');

        //对应日期 轮休的人 需要排除
        $restStaff = HrStaffWorkDaysModel::find([
            'columns'    => 'date_at,group_concat(staff_info_id) as staff_ids',
            'conditions' => 'staff_info_id in ({staff_ids:array}) and date_at between :start_date: and :end_date:',
            'bind'       => ['staff_ids' => $staffIds, 'start_date' => $startDate, 'end_date' => $endDate],
            'group'      => 'date_at',
        ])->toArray();
        $restStaff = array_column($restStaff, 'staff_ids', 'date_at');

        //获取dc对应日期的班次 快递员没用
        $dcStaffIds = [];
        foreach ($staffIds as $id) {
            if (in_array($jobStaffs[$id], $dcJob)) {
                $dcStaffIds[] = $id;
            }
        }
        $courierStaffIds = array_diff($staffIds, $dcStaffIds);

        $dateList  = DateHelper::DateRange(strtotime($startDate),strtotime($endDate));
        $tomorrow = date('Y-m-d', strtotime('+1 day'));
        if($startDate > $tomorrow){
            $dateList  = DateHelper::DateRange(strtotime($tomorrow),strtotime($endDate));
        }
        $shiftServer = new HrShiftService();
        $shiftServer = reBuildCountryInstance($shiftServer);
        $this->shiftStaffsData = $dcShiftData = $shiftServer->getShiftInfos($dcStaffIds, $dateList);

        $dc = $courier = [];
        //不含当天 计算 日期维度 仓管(对应建议班次)和 快递员 实际轮休 on 的人数
        foreach ($dateList as $step) {
            $rest = !empty($restStaff[$step]) ? explode(',', $restStaff[$step]) : [];
            //没有排班建议
            if (empty($suggest[$step])) {
                continue;
            }

            foreach ($suggest[$step] as $s) {
                //仓管员的人数
                if ($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER) {
                    //新增 不传班次对应人数 只传一个数值情况 shift_date 是空
                    if(!empty($s['shift_date'])){
                        $dcKey      = "{$step}_{$s['shift_date']}";
                        $dc[$dcKey] = 0;//dc 仓管 轮休 on 人数初始化
                        foreach ($dcStaffIds as $staff) {
                            $shiftKey = "{$step}_{$staff}";
                            //如果休息日 下一个
                            if (in_array($staff, $rest)) {
                                continue;
                            }

                            if (!empty($dcShiftData[$shiftKey])
                                && $shiftServer->betweenOneHour($s['shift_date'], $dcShiftData[$shiftKey]['start']))
                            {
                                $dc[$dcKey]++;
                            }
                        }
                    }
                    //算总数
                    $dcOnStaff = array_diff($dcStaffIds, $rest);
                    $dc[$step] = count($dcOnStaff);
                }

                //快递员 取总数 - 轮休on 人数
                if ($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_COURIER) {
                    //剔除 对应日期 休息的人
                    $courierOnStaff = array_diff($courierStaffIds, $rest);
                    $courier[$step] = count($courierOnStaff);
                }
            }
        }

        $return['dc']      = $dc;
        $return['courier'] = $courier;
        $return['restStaff'] = $restStaff;
        $this->logger->write_log("workdaySuggest " . json_encode($return),'info');
        return $return;
    }


    /**
     * @param $staffs //本次配置轮休的工号 list
     * @param $dates //本次配置轮休的日期 list
     * @param $operator //当前操作人
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function checkWorkDaySuggest($staffs, $dates, $operator)
    {
        //日期
        sort($dates);
        $start = $dates[0];
        $end   = end($dates);
        //网点
        $dcJob      = (new SettingEnvService)->getSetVal('shift_dc_job_title') ?? [];
        $courierJob = (new SettingEnvService)->getSetVal('shift_courier_job_title') ?? [];
        $dcJob      = empty($dcJob) ? [] : explode(',', $dcJob);
        $courierJob = empty($courierJob) ? [] : explode(',', $courierJob);
        $jobs       = array_merge($dcJob, $courierJob);
        if(empty($jobs)){
            return true;
        }
        $staffData  = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,job_title,sys_store_id',
            'conditions' => 'staff_info_id in ({staffs:array}) and job_title in ({jobs:array}) and sys_store_id != :head_office: and week_working_day = 6 and rest_type = 1',
            'bind'       => ['staffs' => $staffs, 'jobs' => $jobs, 'head_office' => '-1'],
        ])->toArray();
        //没有符合条件的配置员工
        if (empty($staffData)) {
            return true;
        }

        //把人拆分成 仓管和 快递员 网点纬度
        $dcStaff = $courierStaff = [];
        foreach ($staffData as $staff) {
            if (in_array($staff['job_title'], $dcJob)) {
                $dcStaff[$staff['sys_store_id']][] = $staff['staff_info_id'];
            } else {
                $courierStaff[$staff['sys_store_id']][] = $staff['staff_info_id'];
            }
        }

        $storeIds = array_column($staffData, 'sys_store_id');
        //没有网点员工
        if (empty($storeIds)) {
            return true;
        }

        //操作人是否是主管
        $isSupper = $this->operatorSupervisor($operator);

        $shiftServer = new HrShiftService();
        $suggestServer = new SchedulingSuggestionService();
        $confirm       = false;
        foreach ($storeIds as $storeId) {
            $suggest = $suggestServer->getSchedulingSuggest($storeId, $start, $end);
            if (empty($suggest)) {
                continue;
            }
            //实际出勤人数
            $actData = $this->workdaySuggest($storeId, $start, $end, $suggest);
            //日期维度
            foreach ($dates as $date) {
                if (empty($suggest[$date])) {
                    continue;
                }
                foreach ($suggest[$date] as $s) {
                    //建议人数
                    $suggestNum = $s['suggest_number'];
                    //实际出勤人数
                    $actNum = null;
                    //如果没有建议数 原来是不存库 现在 有可能出现 不限制
                    if($suggestNum <= 0){
                        continue;
                    }
                    $this->logger->write_log("checkWorkDaySuggest {$date} " . json_encode($s),'info');

                    //仓管 人员
                    if ($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER && !empty($dcStaff[$storeId])) {
                        //没有建议班次的情况 取总数
                        if(empty($s['shift_date'])){
                            $actNum = $actData['dc'][$date] ?? 0;
                            //减去本次配置的dc数量
                            $actNum -= count($dcStaff[$storeId]);
                        }else{
                            //每个建议班次 验证人数
                            foreach ($dcStaff[$storeId] as $dc){
                                //非对应班次
                                $shiftKey = "{$date}_{$dc}";
                                if(!empty($this->shiftStaffsData[$shiftKey]['start']) && !$shiftServer->betweenOneHour($s['shift_date'], $this->shiftStaffsData[$shiftKey]['start'])){
                                    continue;
                                }
                                $key        = "{$date}_{$s['shift_date']}";
                                $actNum     = $actData['dc'][$key] ?? 0;
                                //减去本次配置的dc数量
                                $actNum -= count($dcStaff[$storeId]);

                            }
                        }
                        //如果配置之后 小于 建议人数了
                        if (!is_null($actNum) && $actNum < $suggestNum && $isSupper) {//主管操作 直接返回提示不让申请
                            throw new ValidationException(self::$t->_('workday_shift_change_notice'));
                        }
                        if (!is_null($actNum) && $actNum < $suggestNum && !$isSupper) {
                            $confirm = true;
                        }
                    }

                    //快递员
                    if ($s['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_COURIER && !empty($courierStaff[$storeId])) {
                        $actNum     = $actData['courier'][$date] ?? 0;
                        //减去本次配置的dc数量
                        $actNum -= count($courierStaff);

                        //如果配置之后 小于 建议人数了
                        if ($actNum < $suggestNum && $isSupper) {//主管操作 直接返回提示不让申请
                            throw new ValidationException(self::$t->_('workday_shift_change_notice'));
                        }
                        if ($actNum < $suggestNum && !$isSupper) {
                            $confirm = true;
                        }
                    }
                }
            }
        }

        //获取对应日期 实际排班on 人数 非主管操作 且非二次确认后
        if (empty($this->suggestConfirm) && !$isSupper && $confirm) {
            throw new BusinessException(self::$t->_('workday_shift_change_confirm'), 10086);
        }
        return true;
    }

    //操作人 是否是主管
    public function operatorSupervisor($operator)
    {
        $operatorInfo = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'columns'    => 'job_title',
            'bind'       => ['staff_id' => $operator],
        ]);

        if ($operator != 10000 && empty($operatorInfo)) {
            throw new ValidationException('wrong operator');
        }
        $jobTitle = $operatorInfo->job_title;
        $isSuper  = false;
        //如果操作人 不是主管 二次确认弹窗
        $supervisorJob = (new SettingEnvService)->getSetVal('shift_supervisor_job_title', ',') ?? [];
        if (in_array($jobTitle, $supervisorJob)) {
            $isSuper = true;
        }
        return $isSuper;
    }



    //轮休列表方格内新增 班次 和请假信息
    public function workdayCube($staff_ids, $dateList)
    {
        $cubeInfo = [];
        foreach ($staff_ids as $staff) {
            $row = [];
            foreach ($dateList as $date) {
                $item               = [];
                $uKey               = "{$date}_{$staff}";
                $item['date_at']    = $date;
                $item['shift_info'] = $this->shiftData[$uKey]['shift_info'] ?? '';
                $item['leave_info'] = $item['leave_icon'] = '';
                if (!empty($this->leaveData[$uKey])) {
                    $r                  = $this->formatLeave($this->leaveData[$uKey]);
                    $item['leave_info'] = $r['leave_info'];
                    $item['leave_icon'] = $r['leave_icon'];
                }
                $row[] = $item;
            }
            $cubeInfo[$staff] = $row;
        }

        return $cubeInfo;
    }

    //请假 对应日期 请假数据 注意 上下午 两个类型的假期要分别展示
    public function formatLeave($da)
    {
        $leaveType = $da['leave_type_str'];//group concat 字段
        $typeArr   = array_unique(explode(',', $leaveType));
        $return    = [];

        //包含年假AL、病假SL、培训假CT、带薪假PL、不带薪假LW 不带薪 USL）
        $server = reBuildCountryInstance($this);
        $leaveTypeCode = $server->leaveTypeCode();
        if ($da['type'] == 3) {
            //两个半天 两种类型
            if (count($typeArr) == 1) {//两个半天 都是同一个类型
                $enum = $leaveTypeCode[current($typeArr)];
                if (empty($enum) || !in_array($enum, $this->salaryLeaveType)) {
                    return $return;
                }
                $k                    = strtolower($enum) . '_leave';
                $return['leave_info'] = $enum;
                $return['leave_icon'] = self::$t->_('workday_leave_text', ['days' => '1']) . self::$t->_($k);
                return $return;
            }

            //两个半天不一样类型
            $leave_info = $leave_icon = '';
            foreach ($typeArr as $type) {
                $enum = $leaveTypeCode[$type];
                if (empty($enum) || !in_array($enum, $this->salaryLeaveType)) {
                    $leave_info .= '-|';
                    continue;
                }
                $leave_info .= $enum . '|';
                $k          = strtolower($enum) . '_leave';
                $leave_icon .= self::$t->_('workday_leave_text', ['days' => '0.5']) . self::$t->_($k) . '<br/>';
            }
            if (empty($leave_info)) {
                return $return;
            }
            $leave_info           = rtrim($leave_info, '|');
            $return['leave_info'] = $leave_info;
            $return['leave_icon'] = $leave_icon;
            return $return;

        }
        //其余情况都只有一种类型
        $enum = $leaveTypeCode[$leaveType];
        $days = in_array($da['type'], [1, 2]) ? '0.5' : '1';
        if (empty($enum) || !in_array($enum, $this->salaryLeaveType)) {
            $leave_off = '';//原休息日
            //目前只有马来有
            $batch_changing_flag = rtrim(AttendanceToolService::BATCH_ADD_OFF_DAY_REASON, '%s');
            if (strpos($da['audit_reason'], $batch_changing_flag) !== false) {
                $leave_off = self::$t->_('original_rest_day', [
                    'date_at' => trim(str_replace($batch_changing_flag, '', $da['audit_reason'])),
                ]);
            }
            $return['leave_info'] = '';
            $return['leave_icon'] = $leave_off;
            return $return;
        }
        //拼接后面的 ｜- 测试新增需求 上午请假下午请假 要区分开
        $str = '';
        if($days == '0.5'){
            $str = $da['type'] == 1 ? '|-' : '-|';
        }
        $k                    = strtolower($enum) . '_leave';
        $return['leave_info'] = $da['type'] == 1 ? $enum . $str : $str . $enum;
        $return['leave_icon'] = self::$t->_('workday_leave_text', ['days' => $days]) . self::$t->_($k);
        return $return;
    }

    //各个国家不一样
    public function leaveTypeCode(){
        return AttendanceEnums::$leave_type2stat_type;
    }


    //排班建议行 和 点击弹窗
    public function suggestRow($param)
    {
        $this->operator = $param['operate_id'];
        //没有指定网点搜索 并且有 pre id 说明角色是 网点主管或者网点经理 查询管辖网点 看是不是超过1个如果是一个就取 如果超1个返回空
        if(empty($param['store_id']) && !empty($param['pre_store_id'])){
            $masterStore = $this->getMasterStore($param);
            $param['store_id'] = empty($masterStore) ? [] : [$masterStore];
        }
        //没有网点参数 没数据
        if(empty($param['store_id'])){
            return [];
        }
        //有可能搜总部
        $param['store_id'] = array_diff($param['store_id'],['-1']);
        if(empty($param['store_id'])){
            return [];
        }
        //超过2个 没数据
        if(count($param['store_id']) > 1){
            return [];
        }
        //只有一个
        $param['store_id'] = current($param['store_id']);

        $suggestList = [];
        //获取 排班建议 只有 传网点参数 6天班 轮休tab展示
        if (!($param['week_working_day'] == 6 && $param['rest_type'] == 1)) {
            return $suggestList;
        }
        $storeInfo = SysStoreModel::findFirst([
            'conditions' => ' id =  :id: and state = 1',
            'bind'       => [
                'id' => $param['store_id'],
            ],
        ]);
        if (empty($storeInfo)) {
            throw new ValidationException('store id error');
        }
        $month = $param['month'] ?? date('Y-m', time());
        //调建议方法
        $startDate     = $month . '-01';
        $endDate       = date('Y-m-d', strtotime("{$month} last day of"));//每月最后一天
        $suggestServer = new SchedulingSuggestionService();
        $suggest       = $suggestServer->getSchedulingSuggest($param['store_id'], $startDate, $endDate);

        if (empty($suggest)) {
            return $suggestList;
        }

        //计算当天和未来的 实际出勤人数 开始时间从明天开始 是计算的
        $today  = date('Y-m-d');
        $actNum = $this->workdaySuggest($param['store_id'], $today, $endDate, $suggest);

        //职位从翻译改成后端取名称
        $settingJob = (new SettingEnvService)->listByCode(['shift_dc_job_title', 'shift_courier_job_title']) ?? [];
        if (!empty($settingJob)) {
            $settingJob = array_column($settingJob, 'set_val', 'code');
        }
        $dcJob          = empty($settingJob['shift_dc_job_title']) ? [] : explode(',',
            $settingJob['shift_dc_job_title']);
        $courierJob     = empty($settingJob['shift_courier_job_title']) ? [] : explode(',',
            $settingJob['shift_courier_job_title']);
        $dcJobName      = self::$t->_('dc_job_name');
        $courierJobName = self::$t->_('courier_job_name');
        if (!empty($dcJob)) {
            $dcJobName = HrJobTitleModel::findFirst([
                'columns'    => 'group_concat(job_name) as job_name',
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $dcJob],
            ]);
            $dcJobName = empty($dcJobName) ? '' : "({$dcJobName->job_name})";
        }
        if (!empty($courierJob)) {
            $courierJobName = HrJobTitleModel::findFirst([
                'columns'    => 'group_concat(job_name) as job_name',
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $courierJob],
            ]);
            $courierJobName = empty($courierJobName) ? '' : "({$courierJobName->job_name})";
        }

        //处理结构
        $isShow = false;
        foreach ($suggest as $date => $s) {
            $row            = [];
            $row['date_at'] = $date;
            $rowStr         = '';//轮休列表第一行展示的字符串
            $cubeList       = [];//点击对应格子 展示的详情列表页
            foreach ($s as $v) {
                if (empty($v)) {
                    continue;
                }
                $item               = [];//点击格子 弹出来一个详情list 用
                $item['shift_time'] = '';
                $item['job_title']  = '';
                $item['shift_time'] = '';
                $item['num']        = '';
                //要拼接成 xx员（08：00）：3/7
                //没有建议人数 不拼接这行数据
                if($v['suggest_number'] <= 0){
                    $cubeList[] = $item;
                    continue;
                }
                if ($v['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER) {//仓管员 另外一个分之枚举
                    //没有班次配置 跟快递员一样 只展示数量
                    if(empty($v['shift_date'])){
                        $v['actual_number'] = $actNum['dc'][$date] ?? $v['actual_number'];
                        //有建议人数
                        $item['job_title'] = self::$t->_('dc_job_text') . $dcJobName;
                        $item['num']       = self::$t->_('work_day_on', ['num_on' => $v['actual_number']]) .
                            "/" . self::$t->_('work_day_suggest', ['num_suggest' => $v['suggest_number']]);
                        $item['shift_time'] = '-';

                        //拼接上面那行字符串
                        if ($v['actual_number'] < $v['suggest_number']) {
                            $rowStr .= self::$t->_('dc_job_text') . ":<span style='color: #308bf8;'><span style='color:red'>{$v['actual_number']}</span>/{$v['suggest_number']}</span><br/>";
                        } else {
                            $rowStr .= self::$t->_('dc_job_text') . ":<span style='color: #308bf8;'>{$v['actual_number']}/{$v['suggest_number']}</span><br/>";
                        }
                        $cubeList[] = $item;
                        continue;

                    }
                    //有班次配置时间
                    $key                = "{$date}_{$v['shift_date']}";
                    $v['actual_number'] = $actNum['dc'][$key] ?? $v['actual_number'];

                    //新增需求 如果建议为大于0 才展示显示
                    if($v['suggest_number'] > 0){
                        //如果已排 小于 建议 要标记红色
                        if ($v['actual_number'] < $v['suggest_number']) {
                            $rowStr .= self::$t->_('dc_job_text') . "({$v['shift_date']}):<span style='color: #308bf8;'><span style='color:red'>{$v['actual_number']}</span>/{$v['suggest_number']}</span><br/>";
                        } else {
                            $rowStr .= self::$t->_('dc_job_text') . "({$v['shift_date']}):<span style='color: #308bf8;'>{$v['actual_number']}/{$v['suggest_number']}</span><br/>";
                        }
                    }
                    //item 点击方格 弹窗前端用
                    $item['job_title']  = self::$t->_('dc_job_text') . $dcJobName;
                    $item['num']        = self::$t->_('work_day_on', ['num_on' => $v['actual_number']]) . "/" . self::$t->_('work_day_suggest', ['num_suggest' => $v['suggest_number']]);
                    $item['shift_time'] = self::$t->_('more_less_1_hour', ['time' => $v['shift_date']]);
                    $cubeList[] = $item;
                    continue;
                }

                //快递员的
                if ($v['position_type'] == SchedulingSuggestionEnums::POSITION_TYPE_COURIER) {//快递员
                    $v['actual_number'] = $actNum['courier'][$date] ?? $v['actual_number'];

                    if ($v['actual_number'] < $v['suggest_number']) {
                        $rowStr .= self::$t->_('courier_job_text') . ":<span style='color: #308bf8'> <span style='color:red'>{$v['actual_number']}</span>/{$v['suggest_number']}</span><br/>";
                    } else {
                        $rowStr .= self::$t->_('courier_job_text') . ":<span style='color: #308bf8;'>{$v['actual_number']}/{$v['suggest_number']}</span><br/>";
                    }

                    $item['job_title'] = self::$t->_('courier_job_text') . $courierJobName;
                    $item['num']       = self::$t->_('work_day_on', ['num_on' => $v['actual_number']]) . "/" . self::$t->_('work_day_suggest', ['num_suggest' => $v['suggest_number']]);
                    $item['shift_time'] = '-';
                    $cubeList[] = $item;
                }

            }
            //是否显示排班建议行标记
            if(!empty($rowStr)){
                $isShow = true;
            }
            $row['info']   = $rowStr;
            $row['list']   = $cubeList;
            $suggestList[] = $row;
        }
        //时间区间内 一个排班建议都没有 要返回空不显示这一行
        if(!$isShow){
            return [];
        }
        return $suggestList;
    }
    //看管辖网点是不是只有一个
    public function getMasterStore($param){
        $info = HrStaffManageStoreModel::find([
            'columns' => 'store_id',
            'conditions' => 'staff_info_id = :staff_id: and deleted = 0 and type = 1',
            'bind' => ['staff_id' => $this->operator],

        ])->toArray();
        //没有额外的负责网点
        if(empty($info)){
            return $param['pre_store_id'];
        }

        $stores = array_column($info,'store_id');
        $stores = array_diff($stores,['-1',$param['pre_store_id']]);
        //有额外管辖网点 返回空
        if(!empty($stores)){
            return '';
        }

        return $param['pre_store_id'];
    }
    //验证能修改 TruckUpdate 职位的权限 只有菲律宾 重写！！！
    public function checkTruckUpdatePermission($staffIds, $operateId){
        return true;
    }

    //是否能修改自己的轮休
    public function checkSelfEdit($operateId,$staffId){
        if($operateId == 10000){
            return true;
        }
        if($staffId != $operateId){
            return true;
        }
        //新增规则配置校验 能否修改自己的休息日
        $ruleParam['staff_info_id'] = $operateId;
        $ruleParam['rule_key']      = 'Allow_set_selfOff';
        $rules                      = $this->getConditionRule(self::$language, $ruleParam);
        //- N or 空：不允许修改自己，给出如下提示
        if (empty($rules['data']) || $rules['data']['response_type'] != ConditionsRulesEnums::RESPONSE_TYPE_VALUE || !$rules['data']['response_data']) {
            throw new BusinessException(self::$t->_('can_not_edit_self_off'));
        }
        return true;
    }



}