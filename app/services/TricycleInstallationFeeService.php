<?php

namespace App\Services;

use App\Library\BaseService;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\MotorInstallationFeeModel;
use App\Models\backyard\TricycleInstallationFeeModel;
use App\Repository\JobTitleDailySolidRepository;

/**
 * 三轮车安装费用
 * 三轮车安装费用按月批次发放 区别于 HCM-薪酬管理-安装费管理数据
 */
class TricycleInstallationFeeService extends BaseService
{
    public $job_title = [1000];


    public $installationFeeHeader = [
        'Staff id',
        'Branch',
        'Position',
        'Start day',
        'Last working day',
        'Status',
        'Period',
        'Tricycle day',
        'transfer day',
        'Paid Tricycle Installation Fee',
        'Tricycle Installation Fee',
    ];

    /**
     * 三轮车安装费用导出
     * @param $month
     * @return string
     */
    public function tricycleInstallationFeeExport($month): string
    {
        $startDate  = date('Y-m-01', strtotime($month));
        $endDate    = date('Y-m-t', strtotime($month));
        $returnData = [];
        $findFee    = TricycleInstallationFeeModel::find([
            'conditions' => 'excel_month =:month:  and is_deleted=0',
            'bind'       => ['month' => $month],
        ])->toArray();
        foreach ($findFee as $value) {
            $returnData[] = [
                $value['staff_info_id'],
                $this->showStoreName($value['sys_store_id']),
                $this->showJobTitleName($value['job_title']),
                $value['hire_date'],
                $value['last_working_day'],
                static::$t->_('staff_state_' . $value['staff_state']),
                $startDate . '～' . $endDate,
                $value['tricycle_day'],
                $value['transfer_day'],
                $value['history_fee_amount'],
                $value['fee_amount'],
            ];
        }
        $fileName = uniqid(date('Ym', strtotime($month)) . 'Tricycle_Installation Fee') . '.xlsx';
        $return   = $this->exportExcelReturn($this->installationFeeHeader, $returnData,
            $fileName, 'installation_fee/' . date('Y-m-d') . '/');
        return $return['data'] ?? '';
    }

    /**
     * 安装费用计算
     * 注意同一个周期内存在相同的身份证号员工 a,b a先入职离职 b 在入职，
     * 不应该存在单出输入工号 b 的场景 生产都是全量的
     * @return void
     * @throws \Exception
     */
    public function tricycleInstallationFeeCal($month, $inputStaffIds)
    {
        // 日期计算
        $startDate        = date('Y-m-01', strtotime($month));
        $endDate          = date('Y-m-t', strtotime($month));
        $prevMonthLastDay = date('Y-m-t', strtotime($month . ' -1 month'));
        $prevMonth        = date('Y-m', strtotime($month . ' -1 month'));
        $conditions       = 'stat_date>=:start: and stat_date<=:end:  and job_title in ({job_title:array}) and state!=2 and formal=1 and hire_type not in (13,14)';
        $bind             = ['start' => $startDate, 'end' => $endDate, 'job_title' => $this->job_title];
        if ($inputStaffIds) {
            $conditions  .= ' and staff_info_id in ({ids:array})';
            $bind['ids'] = $inputStaffIds;
        }
        $solidData = HrStaffTransferModel::find([
            'columns'    => 'staff_info_id,stat_date',
            'conditions' => $conditions,
            'bind'       => $bind,
            'order'      => 'staff_info_id,stat_date',
        ])->toArray();
        if (empty($solidData)) {
            return;
        }
        $staffIds     = array_values(array_unique(array_column($solidData, 'staff_info_id')));
        $findSolidMap = JobTitleDailySolidRepository::staffMap($staffIds);
        // 处理情况1
        // 1-1. 员工在所选自然月内担任过Tricycle Courier职位，且连续Tricycle Courier职位的第一天在所选自然月
        $staffContinueFirst             = $this->staffsContinueFirst($findSolidMap, $startDate, $endDate);
        $isCurrentMonthContinueStaffIds = array_keys($staffContinueFirst);
        echo '1-1' . json_encode($isCurrentMonthContinueStaffIds) . PHP_EOL;
        //排除相同身份证号入职日期晚的工号
        [
            $isCurrentMonthContinueStaffIds,
            $excludeStaffIds,
        ] = $this->excludeSameIdentity($isCurrentMonthContinueStaffIds);
        echo '1-1 排除相同身份证号入职日期晚的工号后   剩余工号' . json_encode($isCurrentMonthContinueStaffIds) . PHP_EOL;
        // 1-2. 员工工号或者按照身份证号查询到的工号  ( 未在HCM-薪酬管理-安装费管理中 或者 在HCM-薪酬管理-安装费管理中但是否补贴=「否」)
        $isCurrentMonthContinueStaffIds = $this->calCase1_2($isCurrentMonthContinueStaffIds);
        echo '1-2' . json_encode($isCurrentMonthContinueStaffIds) . PHP_EOL;
        //1-3. 工号之前未出现在Tricycle Installation Fee表中
        // 或者 最后出现在Tricycle Installation Fee的月份的次月15号+365≤所选自然月内担任过Tricycle Courier职位，且连续Tricycle Courier职位的第一天
        $excludeIds = $this->calCase1_3($isCurrentMonthContinueStaffIds, $month,
            $staffContinueFirst);
        echo '1-3 排除工号' . json_encode($excludeIds) . PHP_EOL;
        $isCurrentMonthContinueStaffIds = array_values(array_diff($isCurrentMonthContinueStaffIds, $excludeIds));
        //1-4 按照相同身份证号查询到的工号未出现在Tricycle Installation Fee表中  或者 最后出现在Tricycle Installation Fee的月份的次月15号+365 ≤ 条件1中Tricycle职位第一天
        $isCurrentMonthContinueStaffIds = $this->calCase1_4($isCurrentMonthContinueStaffIds, $excludeStaffIds, $month,
            $staffContinueFirst);
        //最终符合情况 1 的所有工号
        $caseOneStaffIds = array_values($isCurrentMonthContinueStaffIds);
        echo '1-4 最终工号' . json_encode($caseOneStaffIds) . PHP_EOL;
        //处理情况 2
        $caseTwoStaffIds = $this->calCase2($month, $prevMonth, $prevMonthLastDay, $startDate, $inputStaffIds);

        $caseTwoStaffSolid = JobTitleDailySolidRepository::staffMap($caseTwoStaffIds, [], 'desc');
        // 获取情况2的员工显示所选月份1号往前倒推连续Tricycle Courier职位的第一天
        $caseTwoStaffContinueFirst = $this->appointDayTricycleCourierIsFirstDay($caseTwoStaffSolid, $startDate);
        $selectIds                 = array_merge($caseOneStaffIds, $caseTwoStaffIds);
        //整理入库
        $this->insertHandle(
            $inputStaffIds,
            $selectIds,
            $month,
            $startDate,
            $endDate,
            $caseOneStaffIds,
            $staffContinueFirst,
            $caseTwoStaffContinueFirst,
            $findSolidMap
        );
    }

    public function insertHandle(
        $inputStaffIds,
        $selectIds,
        $month,
        $startDate,
        $endDate,
        $caseOneStaffIds,
        $staffContinueFirst,
        $caseTwoStaffContinueFirst,
        $findSolidMap
    ) {
        if (empty($selectIds)) {
            echo '没有需要处理的数据' . PHP_EOL;
            return;
        }
        $infoFind    = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,sys_store_id,job_title,date(hire_date) as hire_date,state,wait_leave_state,leave_date',
            'conditions' => 'staff_info_id IN ({ids:array}) ',
            'bind'       => ['ids' => $selectIds],
        ])->toArray();
        $historySum  = $this->getSumTricycleInstallationFee($selectIds, $month);
        $infoFindMap = array_column($infoFind, null, 'staff_info_id');
        $returnData  = [];
        foreach ($selectIds as $staffId) {
            $staff    = $infoFindMap[$staffId];
            $stateK   = ($staff['state'] == HrStaffInfoModel::STATE_ON_JOB && $staff['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) ? '999' : $staff['state'];
            $caseType = in_array($staffId, $caseOneStaffIds) ? 'case_1' : 'case_2';
            //last working day
            $lastWorkDay = $staff['state'] == HrStaffInfoModel::STATE_RESIGN ? date('Y-m-d',
                strtotime($staff['leave_date'] . ' -1 day')) : null;
            if (!($lastWorkDay >= $startDate && $lastWorkDay <= $endDate)) {
                $lastWorkDay = null;
            }
            $tricycle_day = $caseType == 'case_1' ? $staffContinueFirst[$staffId]['start'] : $caseTwoStaffContinueFirst[$staffId]['start'];
            //获取transfer_day
            $transferDay        = $this->calTransferDay($findSolidMap[$staffId] ?? [],
                $tricycle_day, $startDate, $endDate);
            $base               = [
                'staff_info_id'      => $staffId,
                'sys_store_id'       => $staff['sys_store_id'],
                'job_title'          => $staff['job_title'],
                'hire_date'          => $staff['hire_date'],
                'last_working_day'   => $lastWorkDay,
                'staff_state'        => $stateK,
                'excel_month'        => $month,
                'tricycle_day'       => $tricycle_day,
                'transfer_day'       => $transferDay,
                'history_fee_amount' => $caseType == 'case_1' ? 0 : ($historySum[$staffId] ?? 0),
            ];
            $base['fee_amount'] = $this->calFeeAmount($base, $caseType, $endDate);
            $returnData[]       = $base;
        }
        $db = $this->db_backyard;
        try {
            $db->begin();
            $model     = TricycleInstallationFeeModel::class;
            $delBind   = ['m' => $month];
            $deleteSql = "update {$model} set is_deleted=1 where excel_month=:m:";
            if ($inputStaffIds) {
                $deleteSql      .= ' and staff_info_id in ({ids:array})';
                $delBind['ids'] = $inputStaffIds;
            }
            $this->modelsManager->executeQuery($deleteSql, $delBind)->success();
            (new TricycleInstallationFeeModel)->batch_insert($returnData);
            $db->commit();
            echo 'total handle:' . count($returnData) . PHP_EOL;
        } catch (\Throwable $exception) {
            $db->rollback();
            $this->logger->error([$exception->getMessage(), $exception->getTrace()]);
        }
    }

    /**
     * 情况 2
     * @param $month
     * @param $prevMonth
     * @param $prevMonthLastDay
     * @param $startDate
     * @param $inputStaffIds
     * @return array
     */
    public function calCase2($month, $prevMonth, $prevMonthLastDay, $startDate, $inputStaffIds): array
    {
        $prevMonthFirstDay = $prevMonth . '-01';
        $conditions        = 'excel_month =:month:  and is_deleted=0';
        $bind              = ['month' => $prevMonth];
        if ($inputStaffIds) {
            $conditions  .= ' and staff_info_id in ({ids:array})';
            $bind['ids'] = $inputStaffIds;
        }
        //2-1. 员工在出现在所选月份上月的Tricycle Installation Fee表中
        $findPreFee      = TricycleInstallationFeeModel::find([
            'columns'    => 'staff_info_id',
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();
        $caseTwoStaffIds = array_column($findPreFee, 'staff_info_id');
        if (empty($caseTwoStaffIds)) {
            return [];
        }
        echo '2-1' . json_encode($caseTwoStaffIds) . PHP_EOL;
        //2- 2. 员工在所选月份上月的最后1天和所选月份的第一天职位都是Tricycle Courier
        $findSolidMap       = JobTitleDailySolidRepository::staffMap($caseTwoStaffIds);
        $appointDayStaffIds = $this->appointDayIsTricycleCourier($findSolidMap, $prevMonthLastDay, $startDate);
        if (empty($appointDayStaffIds)) {
            return [];
        }
        $findSolidMap       = JobTitleDailySolidRepository::staffMap($caseTwoStaffIds, [], 'desc');
        $excludeStaffIds    = $this->appointRuleIsTricycleCourier($findSolidMap, $prevMonthFirstDay, $prevMonthLastDay);
        $appointDayStaffIds = array_values(array_diff($appointDayStaffIds, $excludeStaffIds));
        if (empty($appointDayStaffIds)) {
            return [];
        }
        echo '2-2' . json_encode($appointDayStaffIds) . PHP_EOL;
        //2-3. 员工此工号出现在所选月份之前7个月月份Tricycle Installation Fee的Tricycle Installation Fee合计<3000
        $appointDayStaffIdsFind       = $this->getSumTricycleInstallationFee($appointDayStaffIds, $month,7);
        $appointDayStaffIdsFindFilter = array_filter($appointDayStaffIdsFind, function ($amount) {
            return $amount < 3000;
        });
        $caseTwoStaffIds2_3           = array_keys($appointDayStaffIdsFindFilter);
        echo '2-3 最终工号' . json_encode($caseTwoStaffIds2_3) . PHP_EOL;
        return $caseTwoStaffIds2_3;
    }

    /**
     * 排除从上月最后一天倒推至上月1号职位不能是三轮车>其他职位>三轮车的情况
     * @param $findSolidMap -- 时间倒序
     * @param $prevMonthFirstDay
     * @param $prevMonthLastDay
     * @return array
     */
    public function appointRuleIsTricycleCourier($findSolidMap, $prevMonthFirstDay, $prevMonthLastDay): array
    {
        $excludeStaffIds = [];
        $judgeArr        = [];
        foreach ($findSolidMap as $staffId => $itemList) {
            $prevMonthLastDayStart = false;
            foreach ($itemList as $item) {
                //从上月最后一天开始
                if (strtotime($prevMonthLastDay) >= strtotime($item['start_time']) && strtotime($prevMonthLastDay) <= strtotime($item['end_time'])) {
                    $prevMonthLastDayStart = true;
                }
                if ($prevMonthLastDayStart) {
                    $judgeArr[$staffId][] = in_array($item['job_title'], $this->job_title) ? 'yes' : 'not';
                }
                //到上月1号结束
                if (strtotime($prevMonthFirstDay) >= strtotime($item['start_time']) && strtotime($prevMonthFirstDay) <= strtotime($item['end_time'])) {
                    break;
                }
            }
        }
        foreach ($judgeArr as $staffId => $value) {
            if (isset($value[0], $value[1], $value[2])
                && $value[0] == 'yes' && $value[1] == 'not' && $value[2] == 'yes') {
                $excludeStaffIds[] = $staffId;
            }
        }
        return $excludeStaffIds;
    }

    public function calCase1_4($staffIds, $excludeStaffIds, $month, $staffContinueFirst): array
    {
        if (empty($staffIds)) {
            return [];
        }
        //查询相同身份证号的工号
        [
            $originIdentityMap,
            $findIdentityStaffIds,
            $findIdentityStaffMap,
        ] = $this->getSameIdentityData($staffIds);
        //1-1 中 如果同一月有相同身份证/护照号的多个工号符合，取入职日期最早的作为有效数据，其余工号排除也无需参与条件4判断
        $findIdentityStaffIds = array_values(array_diff($findIdentityStaffIds, $excludeStaffIds));
        //无相同的直接返回原来的结果
        if (empty($findIdentityStaffIds)) {
            return $staffIds;
        }
        $originIdentityMapFlip = array_flip($originIdentityMap);//身份证-工号
        //组装下查询到工号 -> 原工号
        $selToOriginIdMap = [];
        foreach ($findIdentityStaffIds as $staffId) {
            $selToOriginIdMap[$staffId] = $originIdentityMapFlip[$findIdentityStaffMap[$staffId]];
        }

        $findTricycleInstallationFee  = TricycleInstallationFeeModel::find([
            'columns'    => 'staff_info_id,excel_month',
            'conditions' => 'excel_month <:month: and staff_info_id in ({ids:array})  and is_deleted=0',
            'bind'       => ['ids' => $findIdentityStaffIds, 'month' => $month],
            'order'      => 'staff_info_id,excel_month',
        ])->toArray();
        $excludeIds                   = [];
        $findTricycleInstallationFeeM = array_column($findTricycleInstallationFee, 'excel_month', 'staff_info_id');
        foreach ($findTricycleInstallationFeeM as $staffInfoId => $itemMonth) {
            // 1. 计算次月15号
            $nextMonth15th = date('Y-m-15', strtotime($itemMonth . '-01 +1 month'));
            // 2. 计算次月15号 + 365天
            $nextMonth15thPlus365 = date('Y-m-d', strtotime($nextMonth15th . ' +365 days'));
            if (strtotime($nextMonth15thPlus365) > strtotime($staffContinueFirst[$selToOriginIdMap[$staffInfoId]]['start'])) {
                $excludeIds[] = $selToOriginIdMap[$staffInfoId];
            }
        }
        return array_diff($staffIds, $excludeIds);
    }

    /**
     * 排除相同身份证号入职日期晚的工号
     * @param $staffIds
     * @return array
     */
    public function excludeSameIdentity($staffIds): array
    {
        if (empty($staffIds)) {
            return [[], []];
        }
        // 查询员工表中的身份证号和入职日期信息
        $staffInfo = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id, identity, hire_date',
            'conditions' => 'staff_info_id IN ({ids:array})',
            'bind'       => ['ids' => $staffIds],
        ])->toArray();

        if (empty($staffInfo)) {
            return $staffIds;
        }

        // 按身份证号分组
        $identityGroups = [];
        foreach ($staffInfo as $staff) {
            if (!empty($staff['identity'])) {
                $identityGroups[$staff['identity']][] = [
                    'staff_info_id' => $staff['staff_info_id'],
                    'hire_date'     => $staff['hire_date'],
                ];
            }
        }

        // 排除相同身份证号入职日期晚的工号
        $excludedStaffIds = [];
        foreach ($identityGroups as $identity => $staffList) {
            // 如果同一身份证号只有一个工号，则不需要排除
            if (count($staffList) <= 1) {
                continue;
            }

            // 按入职日期排序
            usort($staffList, function ($a, $b) {
                return strtotime($a['hire_date']) - strtotime($b['hire_date']);
            });

            // 保留入职日期最早的工号，排除其他工号
            for ($i = 1; $i < count($staffList); $i++) {
                $excludedStaffIds[] = $staffList[$i]['staff_info_id'];
            }
        }

        // 返回排除后的工号列表
        return [array_values(array_diff($staffIds, $excludedStaffIds)), $excludedStaffIds];
    }

    /**
     * @param $isCurrentMonthContinueStaffIds
     * @param $month
     * @param $staffContinueFirst
     * @return array'
     */
    public function calCase1_3($isCurrentMonthContinueStaffIds, $month, $staffContinueFirst): array
    {
        if (empty($isCurrentMonthContinueStaffIds)) {
            return [];
        }
        $findTricycleInstallationFee  = TricycleInstallationFeeModel::find([
            'columns'    => 'staff_info_id,excel_month',
            'conditions' => 'excel_month <:month: and staff_info_id in ({ids:array})  and is_deleted=0',
            'bind'       => ['ids' => $isCurrentMonthContinueStaffIds, 'month' => $month],
            'order'      => 'staff_info_id,excel_month',
        ])->toArray();
        $excludeIds                   = [];
        $findTricycleInstallationFeeM = array_column($findTricycleInstallationFee, 'excel_month', 'staff_info_id');
        foreach ($findTricycleInstallationFeeM as $staffInfoId => $itemMonth) {
            // 1. 计算次月15号
            $nextMonth15th = date('Y-m-15', strtotime($itemMonth . '-01 +1 month'));
            // 2. 计算次月15号 + 365天
            $nextMonth15thPlus365 = date('Y-m-d', strtotime($nextMonth15th . ' +365 days'));
            if (strtotime($nextMonth15thPlus365) > strtotime($staffContinueFirst[$staffInfoId]['start'])) {
                $excludeIds[] = $staffInfoId;
            }
        }
        return $excludeIds;
    }

    /**
     * 情况 1-2
     * @param $isCurrentMonthContinueStaffIds
     * @return array
     */
    public function calCase1_2($isCurrentMonthContinueStaffIds): array
    {
        if (empty($isCurrentMonthContinueStaffIds)) {
            return [];
        }
        [
            $originIdentityMap,
            $findIdentityStaffIds,
            $findIdentityStaffMap,
        ] = $this->getSameIdentityData($isCurrentMonthContinueStaffIds);

        $mergeIsCurrentMonthAndIdentityStaffIds = array_merge($isCurrentMonthContinueStaffIds, $findIdentityStaffIds);
        $motorFind                              = MotorInstallationFeeModel::find([
            'columns'    => 'staff_info_id,is_subsidy',
            'conditions' => 'staff_info_id in ({ids:array}) ',
            'bind'       => ['ids' => $mergeIsCurrentMonthAndIdentityStaffIds],
        ])->toArray();
        $motorFindMap                           = array_column($motorFind, 'is_subsidy', 'staff_info_id');

        //符合条件的工号用身份证号相同的工号符合条件后删除员工工号(如果存在)
        $delIdentityStaff = [];
        foreach ($findIdentityStaffMap as $staffId => $identity) {
            if (isset($motorFindMap[$staffId]) && $motorFindMap[$staffId] == MotorInstallationFeeModel::SUBSIDY_YES) {
                $delIdentityStaff[] = $identity;
            }
        }
        foreach ($isCurrentMonthContinueStaffIds as $index => $staffId) {
            if (isset($motorFindMap[$staffId]) && $motorFindMap[$staffId] == MotorInstallationFeeModel::SUBSIDY_YES
                || in_array($originIdentityMap[$staffId], $delIdentityStaff)) {
                unset($isCurrentMonthContinueStaffIds[$index]);
            }
        }
        return array_values($isCurrentMonthContinueStaffIds);
    }

    /**
     * 查询相同身份证号的工号
     * @param $isCurrentMonthContinueStaffIds
     * @return array|array[]
     */
    public function getSameIdentityData($isCurrentMonthContinueStaffIds): array
    {
        if (empty($isCurrentMonthContinueStaffIds)) {
            return [[], [], []];
        }
        $originIdentityMap    = [];//原工号和身份证号
        $findIdentityStaffIds = [];//按照员工工号身份证号查询到的工号
        $findIdentityStaffMap = [];
        $findIdentity         = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,identity',
            'conditions' => 'staff_info_id in ({ids:array}) ',
            'bind'       => ['ids' => $isCurrentMonthContinueStaffIds],
        ])->toArray();
        if ($findIdentity) {
            $originIdentityMap = array_column($findIdentity, 'identity', 'staff_info_id');
            //按照身份证号查询到的工号
            $findIdentityStaff    = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,identity',
                'conditions' => 'identity in ({identity:array}) and staff_info_id not in ({ids:array}) and formal=1 and hire_type not in (13,14) and is_sub_staff=0',
                'bind'       => [
                    'identity' => array_column($findIdentity, 'identity'),
                    'ids'      => $isCurrentMonthContinueStaffIds,
                ],
            ])->toArray();
            $findIdentityStaffIds = array_column($findIdentityStaff, 'staff_info_id');
            $findIdentityStaffMap = array_column($findIdentityStaff, 'identity', 'staff_info_id');
        }
        return [$originIdentityMap, $findIdentityStaffIds, $findIdentityStaffMap];
    }

    /**
     * 计算安装费用
     * @param $base
     * @param $caseType
     * @param $endDate
     * @return float|int|mixed
     */
    public function calFeeAmount($base, $caseType, $endDate)
    {
        if ($caseType == 'case_1') {
            // 情况1的员工
            // 1. Tricycle day=1号，且transfer day和Last wokring day为空，Tricycle Installation Fee=500
            // 2. Tricycle day=1号，且transfer day为空且Last wokring day≥所选月份最后一天，Tricycle Installation Fee=500
            if ($base['tricycle_day'] == $base['excel_month'] . '-01') {
                if (empty($base['transfer_day']) && empty($base['last_working_day'])
                    || empty($base['transfer_day']) && $base['last_working_day'] && $base['last_working_day'] >= $endDate) {
                    echo $base['staff_info_id'] . ' cal fee case1 1,2' . PHP_EOL;
                    return 500;
                }
            }

            // 3. 其他情况
            // 获取周期总天数
            $monthEnd  = date('Y-m-t', strtotime($base['excel_month']));
            $totalDays = date('t', strtotime($base['excel_month']));

            // 3.1 存在transfer day：Tricycle Installation Fee=（transfer day-Tricycle day）/周期总天数*500，四舍五入保留2位小数
            if (!empty($base['transfer_day'])) {
                echo $base['staff_info_id'] . ' cal fee case1 3.1' . PHP_EOL;
                $days = (strtotime($base['transfer_day']) - strtotime($base['tricycle_day'])) / 86400;
                return round(($days / $totalDays) * 500, 2);
            }

            // 3.2 Transfer day为空，Last working day在所选周期：Tricycle Installation Fee=（Last working day-Tricycle day+1）/周期总天数*500
            if (!empty($base['last_working_day'])
                && $base['last_working_day'] >= $base['tricycle_day']
                && $base['last_working_day'] <= $monthEnd) {
                $days = (strtotime($base['last_working_day']) - strtotime($base['tricycle_day']) + 86400) / 86400;
                echo $base['staff_info_id'] . ' cal fee case1 3.2' . PHP_EOL;
                return round(($days / $totalDays) * 500, 2);
            }

            // 3.3 Transfer day和Last working day均为空：Tricycle Installation Fee=（所选月最后1天-Tricycle day+1）/周期总天数*500
            if (empty($base['last_working_day'])) {
                $days = (strtotime($monthEnd) - strtotime($base['tricycle_day']) + 86400) / 86400;
                echo $base['staff_info_id'] . ' cal fee case1 3.3' . PHP_EOL;
                return round(($days / $totalDays) * 500, 2);
            }
        }

        // 情况2的员工逻辑
        if ($caseType == 'case_2') {
            // 计算上限值：用「3000-Paid Tricycle Installation Fee」和500对比，取小值
            $paidAmount = $base['history_fee_amount'] ?? 0;
            $maxLimit   = min(3000 - $paidAmount, 500);

            // 获取周期总天数
            $monthEnd      = date('Y-m-t', strtotime($base['excel_month']));
            $totalDays     = date('t', strtotime($base['excel_month']));
            $monthFirstDay = $base['excel_month'] . '-01';

            // 1. transfer day和Last wokring day为空，Tricycle Installation Fee=上限值
            if (empty($base['transfer_day']) && empty($base['last_working_day'])) {
                echo $base['staff_info_id'] . ' cal fee case2 1' . PHP_EOL;
                return $maxLimit;
            }

            // 2. transfer day为空且Last wokring day≥所选月份最后一天，Tricycle Installation Fee=上限值
            if (empty($base['transfer_day']) && !empty($base['last_working_day']) && $base['last_working_day'] >= $monthEnd) {
                echo $base['staff_info_id'] . ' cal fee case2 2' . PHP_EOL;
                return $maxLimit;
            }

            // 3. 其他情况
            // 3.1 存在transfer day：Tricycle Installation Fee=Min[（transfer day-所选月份1号）/周期总天数*500，上限值]
            if (!empty($base['transfer_day'])) {
                $days = (strtotime($base['transfer_day']) - strtotime($monthFirstDay)) / 86400;
                $fee  = round(($days / $totalDays) * 500, 2);
                echo $base['staff_info_id'] . ' cal fee case2 3.1' . PHP_EOL;
                return min($fee, $maxLimit);
            }

            // 3.2 Transfer day为空，Last working day在所选周期：Tricycle Installation Fee=Min[（Last working day-所选月份1号+1）/周期总天数*500，上限值]
            if (!empty($base['last_working_day'])
                && $base['last_working_day'] >= $monthFirstDay
                && $base['last_working_day'] <= $monthEnd) {
                $days = (strtotime($base['last_working_day']) - strtotime($monthFirstDay) + 86400) / 86400;
                $fee  = round(($days / $totalDays) * 500, 2);
                echo $base['staff_info_id'] . ' cal fee case2 3.2' . PHP_EOL;
                return min($fee, $maxLimit);
            }
        }

        return 0;
    }

    /**
     * 获取历史的安装费用
     * @param $staffIds
     * @param $month
     * @param int $delMonthNum
     * @return array
     */
    public function getSumTricycleInstallationFee($staffIds, $month, int $delMonthNum = 6): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $startMonth = date('Y-m', strtotime("$month -$delMonthNum month"));
        $find       = TricycleInstallationFeeModel::find([
            'columns'    => 'staff_info_id,sum(fee_amount) as fee_amount',
            'conditions' => 'excel_month>=:start: and excel_month <:month: and staff_info_id in ({ids:array})  and is_deleted=0',
            'bind'       => ['ids' => $staffIds, 'start' => $startMonth, 'month' => $month],
            'group'      => 'staff_info_id',
        ])->toArray();
        return array_column($find, 'fee_amount', 'staff_info_id');
    }

    /**
     * 获取 根据Tricycle day往后倒推所选月份最后1天，职位≠Tricycle Courier的第一天
     * @param $staffSolidMap -- 结束时间正序
     * @param $tricycleDay
     * @param $monthStart
     * @param $monthEnd
     * @return void
     */
    public function calTransferDay($staffSolidMap, $tricycleDay, $monthStart, $monthEnd)
    {
        $position           = false;
        $noTricycleFirstDay = null;
        foreach ($staffSolidMap as $keyNumber => $item) {
            if ($tricycleDay >= $item['start_time'] && $tricycleDay <= $item['end_time']) {
                $position = true;
            }
            if ($position
                && isset($staffSolidMap[$keyNumber + 1])
                && $staffSolidMap[$keyNumber + 1]['start_time'] >= $monthStart && $staffSolidMap[$keyNumber + 1]['start_time'] <= $monthEnd
                && !in_array($staffSolidMap[$keyNumber + 1]['job_title'], $this->job_title)
            ) {
                $noTricycleFirstDay = $staffSolidMap[$keyNumber + 1]['start_time'];
                break;
            }
        }
        return $noTricycleFirstDay;
    }

    /**
     * 月份1号往前倒推连续Tricycle Courier职位的第一天
     * @param $findSolidMap
     * @param $monthFirstDay
     * @return array
     */
    public function appointDayTricycleCourierIsFirstDay($findSolidMap, $monthFirstDay): array
    {
        $returnData = [];
        // 遍历每个员工的职位记录
        foreach ($findSolidMap as $staffId => $itemList) {
            $isFirstFound = false;
            $startTime    = null;

            // 首先检查员工在指定日期是否担任三轮车快递员职位
            foreach ($itemList as $keyNumber => $item) {
                if ($monthFirstDay >= $item['start_time'] && $monthFirstDay <= $item['end_time'] && in_array($item['job_title'],
                        $this->job_title)) {
                    $isFirstFound = true;
                    $startTime    = $item['start_time'];
                }
                if ($isFirstFound
                    && isset($itemList[$keyNumber + 1])
                    && date('Y-m-d',
                        strtotime($item['start_time'] . ' last day ')) == $itemList[$keyNumber + 1]['end_time']
                    && in_array($itemList[$keyNumber + 1]['job_title'], $this->job_title)
                ) {
                    $startTime = $itemList[$keyNumber + 1]['start_time'];
                } else {
                    $isFirstFound = false;
                }
            }
            if ($startTime) {
                $returnData[$staffId] = ['start' => $startTime];
            }
        }
        return $returnData;
    }

    /**
     * 指定日期是指定职位的员工
     * @param $findSolidMap
     * @param $prevMonthLastDay
     * @param $monthFirstDay
     * @return array
     */
    public function appointDayIsTricycleCourier($findSolidMap, $prevMonthLastDay, $monthFirstDay): array
    {
        $eligibleStaffIds = [];
        foreach ($findSolidMap as $staffId => $itemList) {
            //指定都符合
            $prevMonthLastDayIs = $startDayIs = false;
            foreach ($itemList as $item) {
                if (in_array($item['job_title'], $this->job_title)) {
                    if (strtotime($prevMonthLastDay) >= strtotime($item['start_time']) && strtotime($prevMonthLastDay) <= strtotime($item['end_time'])) {
                        $prevMonthLastDayIs = true;
                    }
                    if (strtotime($monthFirstDay) >= strtotime($item['start_time']) && strtotime($monthFirstDay) <= strtotime($item['end_time'])) {
                        $startDayIs = true;
                    }
                }
            }
            if ($prevMonthLastDayIs && $startDayIs) {
                $eligibleStaffIds[] = $staffId;
            }
        }
        return $eligibleStaffIds;
    }

    /**
     * 员工在所选自然月内担任过Tricycle Courier职位，且连续Tricycle Courier职位的第一天在所选自然月
     * @param $findSolidMap
     * @param $startDate
     * @param $endDate
     * @return array
     */
    public function staffsContinueFirst($findSolidMap, $startDate, $endDate): array
    {
        $staffContinueFirst = [];
        // 遍历每个员工的职位记录
        foreach ($findSolidMap as $staffId => $itemList) {
            $isFirstFound = false;
            $startTime    = null;

            // 遍历员工的职位记录
            foreach ($itemList as $keyNum => $item) {
                // 检查是否是三轮车快递员职位
                if (in_array($item['job_title'], $this->job_title)) {
                    // 检查职位开始时间是否在指定日期范围内
                    if (strtotime($item['start_time']) >= strtotime($startDate)
                        && strtotime($item['start_time']) <= strtotime($endDate)) {
                        $isFirstFound = true;
                        $startTime    = $item['start_time'];
                        break;
                    }
                }
            }

            // 如果找到了符合条件的职位，添加到结果中
            if ($isFirstFound) {
                $staffContinueFirst[$staffId] = ['start' => $startTime];
            }
        }
        return $staffContinueFirst;
    }
}
