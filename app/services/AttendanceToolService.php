<?php

namespace App\Services;

use App\Library\ApiClient;
use App\Library\ArrayToObject;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\ApprovalEnums;
use App\Library\ErrCode;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\FullAttendanceIgnoreImageModel;
use App\Models\backyard\FullAttendanceIgnoreSettingModel;
use App\Models\backyard\HcmRolePermissionInfoModel;
use App\Models\backyard\HrShiftV2ExtendModel;
use App\Library\Exception\BusinessException;
use App\Library\FlashOss;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AttendanceDataLogModel;
use App\Models\backyard\AttendanceOperateLogModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HcmPermissionModel;
use App\Models\backyard\HcmStaffPermissionInfoModel;
use App\Models\backyard\HrOvertimeModel;
use App\Models\backyard\HrShift;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\HrStaffManageStoreModel;
use App\Models\backyard\HrStaffShiftMiddleDateModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\OutsourcingOvertimeModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\StaffAuditLeaveSplitModel;
use App\Models\backyard\StaffAuditModel;
use App\Models\backyard\StaffAuditReissueForBusinessModel;
use App\Models\backyard\StaffPublicHolidayModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\ToolFileUrlModel;
use App\Models\fle\StaffInfoModel;
use App\Repository\StaffInfoRepository;
use App\Repository\StaffWorkAttendanceRepository;
use OSS\OssClient;
use Exception;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;
use App\Library\Enums\CertificateEnums;
use App\Library\Enums\StaffEnums;
use App\Services\SettingEnvService;

class AttendanceToolService extends BaseService
{
    //导入员工信息
    public $staff_info;
    //操作员工信息
    public $operator_info;
    //1=全部 2=部分
    public $permission_type;
    public const ALL_PERMISSION_ROLES = [41, 99];               //HRIS管理员、超级管理员
    public const PART_PERMISSION_JURISDICTION_ROLES = [
        125,
        21,
        56,
        57,
        77,
    ];                                                          //部分管辖范围（数据管辖范围）HR Generalist、区域经理、大区经理、片区经理、HR service
    public const PART_PERMISSION_STORE_ROLES = [3, 18];         //网点主管、网点经理
    public const PART_PERMISSION_HEAD_OPERATIONS_ROLES = [63];  //运营主管
    public const PART_PERMISSION_OPERATIONS_MANGER_ROLES = [8]; //运营经理

    //马来 批量导入休息日类型的假期备注原因  万万不可修改!!!!
    const BATCH_ADD_OFF_DAY_REASON = 'Batch changing for origin rest day %s';//'Batch changing rest day';

    public $rest_day_leave_type = '15';    //请假类型 休息日

    //如果$permission_type 为部分时，改变量保存导入者管辖的工号
    public $permission_staff_info_id;

    public const ALL_PERMISSION = 1;  //全部权限
    public const PART_PERMISSION = 2; //部分权限
    protected $uniqueKeyMap = [];     //用于校验数据是否重复
    const NOT_ATTENDANCE_START_AT = '2999-12-31';
    const NOT_ATTENDANCE_END_AT = '1970-01-01';
    /**
     * 修改考勤记录
     * 请假 类型
     * @param
     * @return array
     */
    public function dealLeaveTypeForAddLeave($param): array
    {
        $data = [];
        $lang = self::$language;
        $res  = $this->getApiDatass('by', '', 'staffLeaveTypeBook', $lang, $param);
        if ($res) {
            foreach ($res['data'] as $key => $value) {
                $row = ['value' => $value['code'], 'label' => $value['msg']];

                if (!empty($value['template_type'])) {
                    $row['template_type'] = $value['template_type'];
                }
                if (!empty($value['need_img'])) {
                    $row['need_img'] = $value['need_img'];
                }
                $data[] = $row;
            }
            $sort_key = array_column($data, 'value');//var_dump($data);exit;
            array_multisort($sort_key, SORT_ASC, $data);

            //定制工具需求 要展示出来休息日
            $types = array_column($res['data'], 'code');
            if (!in_array(StaffAuditModel::LEAVE_TYPE_15, $types)) {
                $restType = [
                    'value' => '15',
                    'label' => self::$t->_('2017'),
                ];
                $data[]   = $restType;
            }

            //菲律宾定制类型 疫情假还要显示
            if(isCountry('PH')){
                $restType = [
                    'value' => '25',
                    'label' => self::$t->_('leave_25'),
                ];
                $data[]   = $restType;
                $restType = [
                    'value' => '26',
                    'label' => self::$t->_('leave_26'),
                ];
                $data[]   = $restType;
            }

        }
        return $data;
    }


    /**
     * 修改考勤记录
     * 请假 时间区间
     * @return array
     */
    public function timeInterval(): array
    {
        $data = [];
        foreach (Enums::$time_interval as $key => $value) {
            $data[] = ['value' => $key, 'label' => self::$t->_($value)];
        }
        return $data;
    }


    //考勤信息 array
    public $attendance;

    /**
     * 校验工号是否有申请OT的权限
     * @param $params
     * @return bool
     */
    public function applyOtCcheckGrade($params)
    {
        return true;//走新配置项 这接口验证没用

        $info                  = $this->staff_info ? $this->staff_info[$params['staff_id']] : (new StaffInfoService())->getStaffInfoByIdv4($params['staff_id']);
        $job_title_grade_limit = 17;

        if (isCountry('MY')) {
            //实习生没有权限申请OT  https://flashexpress.feishu.cn/docx/YjOGdxokcolp66x88iZcTOf8nCf
            if ($info['formal'] == \App\Models\backyard\HrStaffInfoModel::FORMAL_INTERN) {
                return false;
            }
            $job_title_grade_limit = 16;
        }

        if (isCountry('PH') && date('Y-m-d') >= env('ot_switch_16524', '2023-05-01')) {
            $job_title_grade_limit = 16;
        }

        //泰国 分成 hub 16 非hub 17
        if (isCountry('TH')) {
            //泰国 实习生也没权限了
            if (!empty($info) && $info['formal'] == HrStaffInfoModel::FORMAL_INTERN) {
                //是 ffm 并且 职位是 Internship 需要有申请权限
                $server             = new HcManagerService();
                $ffmDepId           = CertificateEnums::COMPANY_FFL;//ffm 部门 特殊
                $specialJobId       = StaffEnums::$jobTitle['internship'];
                $ffmIds             = $server->getDeptListByPid($ffmDepId);
                $ffmIds             = empty($ffmIds) ? [] : array_column($ffmIds, 'dept_id');
                $ffmIds[]           = $ffmDepId;
                $havePermissionFlag = (in_array($info['sys_department_id'], $ffmIds) || in_array($info['node_department_id'], $ffmIds)) && $info['job_title'] == $specialJobId;
                if (!$havePermissionFlag) {
                    return false;
                }
            }
            $hubDepartmentIds = SettingEnvModel::get_val("hub_department_id");
            $hubDepartmentIds = empty($hubDepartmentIds) ? [] : explode(',', $hubDepartmentIds);
            if (!empty($info) && in_array($info['sys_department_id'], $hubDepartmentIds)) {
                $job_title_grade_limit = 16;
            }
        }

        if (empty($info) || $info['job_title_grade_v2'] >= $job_title_grade_limit) {
            return false;
        }
        return true;
    }

    //OT类型
    public function getOtTypeAndSelectTime($params)
    {
        $res = (new AttendanceToolService())->getApiDatass('by', '', 'getOvertimeTypeList', self::$language, $params);
        if (empty($res['data'])) {
            return [];
        }
        foreach ($res['data'] as $k => $v) {
            $res['data'][$k]['code'] = intval($v['code']);
        }
        return $res['data'];
    }

    /**
     * 新增OT
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws Exception
     */
    public function addOt($params): bool
    {
        $this->logger->info($params);
        if (false === $this->checkOperatorPermission($params)) {
            throw new BusinessException(self::$t->_('permission_denied'));
        }
        //只能选择过去时间
        if (strtotime($params['date_at']) > time()) {
            throw new BusinessException(self::$t->_('add_ot_only_ago'));
        }

        $staffInfo = StaffInfoRepository::getInfoByStaffInfoId($params['staff_info_id'], true);
        if (isCountry('PH') && $staffInfo['formal'] == 0){
            if ($params['type'] == HrOvertimeModel::OVERTIME_99){
                $params['type'] = OutsourcingOvertimeModel::TYPE_ADD_WORK_TIME;
            }
            $res = $this->getApiDatass('by', '', 'addOsOvertime', self::$language, $params);
            if (!isset($res['code']) || $res['code'] != 1) {
                $msg = $res['message'] ?? ($res['msg'] ?? '');
                throw  new BusinessException($msg);
            }
        }else{
            $res = $this->getApiDatass('by', '', 'overtime', self::$language, $params);
            if (!isset($res['code']) || $res['code'] != 1) {
                $msg = $res['message'] ?? ($res['msg'] ?? '');
                throw  new BusinessException($msg);
            }
        }
        //写入DB
        $insert_db = $this->addToDb($params);
        if (!$insert_db) {
            throw new Exception(self::$t->_('data_error'));
        }
        return true;
    }
    
    /**
     * 工具号补卡
     * @param $params
     * @return true
     * @throws Exception
     */
    public function toolMakeUp($params)
    {
        $this->logger->info($params);
        $staffInfoBy = HrStaffInfoModel::findFirst([
            'conditions' => "staff_info_id = :staff_info_id:",
            'bind'       => [
                'staff_info_id' => $params['staff_info_id'],
            ],
        ]);
        if (!empty($staffInfoBy)) {
            throw new BusinessException(self::$t->_('tool_make_up_err_1'));
        }
        $staffInfoFle = StaffInfoModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => [
                'id' => $params['staff_info_id'],
            ],
        ]);
        if (empty($staffInfoFle)) {
            throw new BusinessException(self::$t->_('tool_make_up_err_2'));
        }
        $StaffWorkAttendance = StaffWorkAttendanceModel::findFirst([
            'conditions' => 'attendance_date=:attendance_date: and staff_info_id=:staff_info_id:',
            'bind'       => [
                'attendance_date' => $params['attendance_date'],
                'staff_info_id'   => $params['staff_info_id'],
            ],
        ]);
        $add_hour            = $this->config->application->add_hour;
        $time                = date('Y-m-d H:i:s', strtotime($params['time']) - $add_hour * 3600);
        if (!empty($StaffWorkAttendance)) {
            if ($params['attendance_type'] == StaffWorkAttendanceModel::ATTENDANCE_TYPE_FIRST_UP) {
                $StaffWorkAttendance->started_at     = $time;
                $StaffWorkAttendance->started_state  = 1;
                $StaffWorkAttendance->started_remark = $params['reason'] ?? '';
            } else {
                $StaffWorkAttendance->end_at     = $time;
                $StaffWorkAttendance->end_state  = 1;
                $StaffWorkAttendance->end_remark = $params['reason'] ?? '';
            }
        } else {
            $StaffWorkAttendance = new StaffWorkAttendanceModel();
            $StaffWorkAttendance->staff_info_id     = $params['staff_info_id'];
            $StaffWorkAttendance->attendance_date     = $params['attendance_date'];
            if ($params['attendance_type'] == StaffWorkAttendanceModel::ATTENDANCE_TYPE_FIRST_UP) {
                $StaffWorkAttendance->started_at     = $time;
                $StaffWorkAttendance->started_state  = 1;
                $StaffWorkAttendance->started_remark = $params['reason'] ?? '';
            } else {
                $StaffWorkAttendance->end_at     = $time;
                $StaffWorkAttendance->end_state  = 1;
                $StaffWorkAttendance->end_remark = $params['reason'] ?? '';
            }
        }
        $StaffWorkAttendance->save();
        $params['edit_type'] = Enums::operation_type_make_up;
        $insert_db = $this->addToDb($params);
        if (!$insert_db) {
            throw new BusinessException(self::$t->_('data_error'));
        }
        return true;
    }

    /**
     * 补卡
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function addAttendance($params): bool
    {
        $this->logger->info($params);
        if (false === $this->checkOperatorPermission($params)) {
            throw new BusinessException(self::$t->_('permission_denied'));
        }
        //只能选择过去时间
        if (strtotime($params['attendance_date']) > time()) {
            throw new BusinessException(self::$t->_('add_ot_only_ago'));
        }

        $res = $this->getApiDatass('by', '', 'attendance', self::$language, $params);
        if (!isset($res['code']) || $res['code'] != 1) {
            $msg = $res['message'] ?? ($res['msg'] ?? '');
            throw  new BusinessException($msg);
        }
        //写入DB
        $insert_db = $this->addToDb($params);
        if (!$insert_db) {
            throw new Exception(self::$t->_('data_error'));
        }
        return true;
    }


    /**
     * 请假
     * @param $params
     * @return mixed
     * @throws BusinessException
     * @throws Exception
     */
    public function askForLeave($params)
    {
        $this->logger->info($params);
        if (false === $this->checkOperatorPermission($params)) {
            throw new BusinessException(self::$t->_('permission_denied'));
        }

        $send_params = [
            'staff_id'          => $params['staff_info_id'],
            'staff_info_id'     => $params['staff_info_id'],
            'leave_start_time'  => $params['leave_start_time'],
            'leave_start_type'  => $params['leave_start_type'] ?? StaffAuditModel::LEAVE_TIME_TYPE_MORNING,
            'leave_end_time'    => $params['leave_end_time'],
            'leave_end_type'    => $params['leave_end_type'] ?? StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON,
            'leave_type'        => $params['leave_type'],
            'audit_reason'      => $params['reason'],
            'image_path'        => empty($params['image_path']) ? [] : $params['image_path'],
            'operator'          => $params['operator'],
            'operator_name'     => $params['operator_name'],
            'paid_leave_reason' => !empty($params['paid_leave_reason']) ? $params['paid_leave_reason'] : 0,
        ];
        if (!empty($params['sub_type'])) {
            $send_params['sub_type'] = $params['sub_type'];
        }
        //病假证明 其他分类描述
        if (!empty($params['other_content'])) {
            $send_params['other_content'] = $params['other_content'];
        }

        $res = $this->getApiDatass('by', '', 'leave', self::$language, $send_params);
        if (!isset($res['code']) || $res['code'] != 1) {
            $msg = $res['message'] ?? ($res['msg'] ?? '');
            throw  new BusinessException($msg);
        }
        $params['leave_day'] = $res['data']['leave_day'];
        //写入DB
        $insert_db = $this->addToDb($params);
        if (!$insert_db) {
            throw new Exception(self::$t->_('data_error'));
        }
        return true;
    }

    /**
     * 撤销请假
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws Exception
     */
    public function undoForLeave($params): bool
    {
        $this->logger->info($params);
        //查找记录
        $staff_audit_info = StaffAuditModel::findFirstByAuditId($params['audit_id']);
        //记录不存在
        if (!$staff_audit_info) {
            throw new BusinessException(self::$t->_('operation_data_does_not_exist'));
        }
        $params['staff_info_id'] = $staff_audit_info->staff_info_id;
        if (false === $this->checkOperatorPermission($params)) {
            throw new BusinessException(self::$t->_('permission_denied'));
        }

        //需求https://l8bx01gcjr.feishu.cn/docs/doccnVYgLuyIE6ct8o9ZHrTiHjc
        //撤销员工的休息日之前，校验当天是不是有审批通过或者待审批的加班申请

        if ($staff_audit_info->leave_type == $this->rest_day_leave_type) {
            $start_date  = date('Y-m-d', strtotime($staff_audit_info->leave_start_time));
            $end_date    = date('Y-m-d', strtotime($staff_audit_info->leave_end_time));
            $workday_bll = new WorkdayService();
            //撤销员工的休息日之前，校验休息日期间是不是有审批通过或者待审批的加班申请
            $check_overtime = $workday_bll->check_overtime($staff_audit_info->staff_info_id, $start_date, $end_date);
            if (!empty($check_overtime)) {
                throw new BusinessException(self::$t->_('workdays_check_overtime_alert'),
                    ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR);
            }
            //撤销员工的休息日之前，校验当天是不是有审批通过或者待审批的除了“休息日”外的其他类型的请假申请
            $check_leave_all = $workday_bll->check_leave_all($staff_audit_info->staff_info_id, $start_date, $end_date,
                $this->rest_day_leave_type);
            if (!empty($check_leave_all)) {
                throw new BusinessException(self::$t->_('workdays_check_leave_alert'),
                    ErrCode::WORK_DAY_CHECK_LEAVE_ERROR);
            }
            //当前休息日是被调休后撤销的场景校验
            if ($start_date == $end_date && isCountry('MY')) {
                $find = HrStaffWorkDaysModel::findFirst([
                    'columns'    => 'staff_info_id,date_at,src_date',
                    'conditions' => 'staff_info_id=:staff_id:  and date_at=:date_at: and src_date is not null',
                    'bind'       => ['staff_id' => $staff_audit_info->staff_info_id, 'date_at' => $end_date],
                ]);
                if ($find) {
                    $first = $find->toArray();
                    //当天由休息日%原调休日期%调换而来，%日期A%当天有审批通过或者待审批的OT申请，请撤销OT申请后再调整休息日
                    $check_overtime = $workday_bll->check_overtime($staff_audit_info->staff_info_id, $first['src_date'],
                        $first['src_date']);
                    if (!empty($check_overtime)) {
                        throw new BusinessException(self::$t->_('workdays_check_overtime_alert_src_date',
                            ['src_date' => $first['src_date']]),
                            ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR);
                    }
                    //当天由休息日%原调休日期%调换而来，%原调休日期%当天有审批通过或者待审批的请假申请，请撤销请假申请后再调整休息日
                    $check_leave_all = $workday_bll->check_leave_all($staff_audit_info->staff_info_id,
                        $first['src_date'], $first['src_date'],
                        $this->rest_day_leave_type);
                    if (!empty($check_leave_all)) {
                        throw new BusinessException(self::$t->_('workdays_check_leave_alert_src_date',
                            ['src_date' => $first['src_date']]),
                            ErrCode::WORK_DAY_CHECK_LEAVE_ERROR);
                    }
                }
            }

        }
        $params['leave_type']       = $staff_audit_info->leave_type;
        $params['leave_start_time'] = $staff_audit_info->leave_start_time;
        $params['leave_start_type'] = $staff_audit_info->leave_start_type;
        $params['leave_end_time']   = $staff_audit_info->leave_end_time;
        $params['leave_end_type']   = $staff_audit_info->leave_end_type;
        $params['leave_day']        = $staff_audit_info->leave_day;
        $params['staff_info_id']    = $staff_audit_info->staff_info_id;
        $params['reason']           = 'system cancel';
        //culr api
        $send_params['audit_id'] = $params['audit_id'];
        $send_params['staff_id'] = $staff_audit_info->staff_info_id;
        $send_params['operate_id'] = $params['operator'];

        $res = $this->getApiDatass('by', '', 'leave_cancel', self::$language, $send_params);

        if (!isset($res['code']) || $res['code'] != 1) {
            $msg = $res['message'] ?? ($res['msg'] ?? '');
            throw  new BusinessException($msg);
        }
        //写入DB
        $insert_db = $this->addToDb($params);
        if (!$insert_db) {
            throw new Exception(self::$t->_('data_error'));
        }
        return true;
    }

    /**
     * 写入库
     * @param $params
     * @return mixed
     */
    public function addToDb($params)
    {
        //字段统一
        $attendance_date = $params['date_at'] ?? $params['attendance_date'];

        $bi_db = $this->getDI()->get('db_backyard');//bi数据库
        return $bi_db->insertAsDict(
            'attendance_data_log',
            [
                'staff_info_id'    => $params['staff_info_id'],
                'leave_type'       => $params['leave_type'] ?? 0,
                'leave_start_time' => $params['leave_start_time'] ?? null,
                'leave_end_time'   => $params['leave_end_time'] ?? null,
                'leave_day'        => $params['leave_day'] ?? 0,
                'attendance_type'  => $params['attendance_type'] ?? 0,
                'attendance_date'  => $attendance_date,          //出勤时间
                'attendance_time'  => $params['time'] ?? null,   //补卡时间
                'ot_date'          => $params['date_at'] ?? null,
                'ot_start_time'    => $params['start_time'] ?? null,
                'ot_type'          => $params['type'] ?? 0,
                'duration'         => $params['duration'] ?? 0,
                'log_type'         => $params['edit_type'] ?? 0,
                'operator'         => $params['operator'] ?? '',
                'operator_name'    => $params['operator_name'] ?? '',
                'image_path'       => !empty($params['image_path']) ? implode(',', $params['image_path']) : '',
                'kilometres'       => $params['kilometres'] ?? '',
                'leave_start_type' => $params['leave_start_type'] ?? 0,
                'reason'           => $params['reason'] ?? '',
                'leave_end_type'   => $params['leave_end_type'] ?? 0,
            ]
        );
    }


    /**
     * 获取接口数据 apg
     * @param string $method
     * @param array $url
     * @param array $params
     * @param string $lang
     * @return mixed|string
     */
    public function getApiDatass($sys, $modules, string $method, $lang, ...$params)
    {
        // 验证数据
        if (empty(trim($method))) {
            return '';
        }

        // 实例化jsonRPC接口
        $client = new ApiClient($sys, $modules, $method, $lang);
        $client->setParams($params);
        $result = $client->execute();
        return $result;
    }

    /**
     * OT列表
     * @param $params
     * @return array
     */
    public function attendanceStaffListOT($params)
    {
        $lang = self::$language;
        return $this->getApiDatass('by', '', 'overtimeList', $lang, $params);
    }

    /**
     * 打开列表
     * @param $params
     * @return array
     */
    public function attendanceStaffList($params)
    {
        $data = StaffWorkAttendanceModel::find([
            'columns'    => "id, staff_info_id,attendance_date date_at,started_at,end_at,shift_start,shift_end",
            'conditions' => 'staff_info_id = :staff_id: and attendance_date = :date:',
            'bind'       => [
                'staff_id' => $params['staff_info_id'],
                'date'     => $params['attendance_date'],
            ],
        ])->toArray();
        return $data;
    }

    /**
     * 编辑OT内容
     * @param $params
     * @return array
     */
    public function attendanceOtEdit($params)
    {
        Validation::validate($params, [
            'overtime_id' => 'Required|int|>>>:id”必须是整数',       //记录id'
            'staff_id'    => 'Required|int',                    //员工staff_id'
            'operator'    => 'Required|int',                    //操作人id'
            'duration'    => 'Required|FloatGt:0',              //时长
            'start_time'  => 'Required|Str',                    //开始时间
            'type'        => 'Required|int',                    //ot_type
        ]);

        $this->logger->write_log('attendanceOtEdit param' . json_encode($params), 'info');
        $lang = self::$language;
        return $this->getApiDatass('by', '', 'editOvertime', $lang, $params);
    }

    /**
     * 编辑打卡内容
     * @param $params
     * @return array
     */
    public function attendanceEdit($params)
    {
        Validation::validate($params, [
            'attendance_date' => 'Required|Str',    //时长
            'start_time'      => 'Required|Str',    //开始时间
            'end_time'        => 'Required|Str',    //结束时间
        ]);
        $lang = self::$language;
        return $this->getApiDatass('by', '', 'editAttendance', $lang, $params);
    }

    /**
     * 请假类型
     * @param $params
     * @return array
     */
    public function leaveType($lang = null)
    {
        if (!$lang) {
            $lang = self::$language;
        }
        return $this->getApiDatass('by', '', 'leaveTypeBook', $lang, []);
    }

    /**
     *
     * 根据创建时间 查询记录
     * @param $staff_id
     * @param $type
     * @param $start_date
     * @param $end_date
     */
    public function get_staff_leave($staff_id, $type, $start_date, $end_date)
    {
        //转类型
        $types = [$type];
        //定制 如果是病假 把原来的 带薪病假带上
        if ($type == StaffAuditModel::LEAVE_TYPE_38) {
            $types[] = StaffAuditModel::LEAVE_TYPE_3;
        }

        $data = StaffAuditModel::find([
            'columns'    => 'audit_id, staff_info_id, leave_type,leave_day,leave_start_time,leave_end_time,leave_start_type,leave_end_type,status',
            'conditions' => "staff_info_id = :staff_info_id: and audit_type = 2 
                        and leave_type in ({types:array}) and status in (1,2) 
                        and leave_start_time between  :start_date: and :end_date:
                        and parent_id = 0",
            'bind'       => [
                'staff_info_id' => $staff_id,
                'types'         => $types,
                'start_date'    => $start_date,
                'end_date'      => $end_date,
            ],
        ])->toArray();

        return $data;
    }

    /**
     * 获取hr用户信息
     * @param $user_id
     * @return array
     */
    public function getStaffInfoHr($user_id)
    {
        if (empty($user_id)) {
            return [];
        }
        $sql = " 
            --
            SELECT
                si.staff_info_id as id,
                si.name,
                si.name_en,
                si.sys_store_id,
                si.mobile,
                si.hire_date,
                si.leave_date,
                sip.position_category,
				if(sys_store_id='-1','head office',ss.name) AS store_name,
                si.state,
                si.email,
				si.job_title as job_title,
				sijt.job_name as job_title_name,
				si.sys_department_id as sys_department_id
				,dep.name department_name
				,si.personal_email
				,si.bank_no
				,si.health_status
            FROM
                hr_staff_info AS si
                LEFT JOIN hr_staff_info_position AS sip ON si.id = sip.staff_info_id
                LEFT JOIN sys_store AS ss ON si.sys_store_id = ss.id 
				LEFT JOIN hr_job_title AS sijt ON sijt.id = si.job_title
				left join sys_department dep on si.sys_department_id = dep.id
            WHERE
                si.staff_info_id = %s;
        ";

        $sql = sprintf($sql, $user_id);

        $data = $this->getDI()->get('db_rby')->fetchOne($sql, \Phalcon\Db::FETCH_ASSOC);
        if ($data) {
            return $data;
        } else {
            return [];
        }
    }

    /**
     * 修改历史-导出
     * @param $params
     * @return Resultset|Model
     * @throws ValidationException
     */
    public function operationHistoryExport($params)
    {
        //这里使用 HcmExcelTackModel 里的 空格 进行 分隔
        $action_name = "attendance-tool" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . "export";
        $excelTask   = HcmExcelTackModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id: and  type = :type: and status = 0  and action_name = :action_name: and is_delete = 0',
            'bind'       => [
                'action_name' => $action_name,
                'staff_id'    => $params['operator'],
                'type'        => HcmExcelTackModel::TYPE_ATTENDANCE,
            ],
        ]);
        if ($excelTask) {
            throw new ValidationException('download task is running  pls waiting');
        }

        $excelTask                  = new HcmExcelTackModel();
        $excelTask->executable_path = $this->config->application->appDir . 'cli.php';
        $excelTask->action_name     = $action_name;
        $excelTask->file_name       = $params['file_name'];
        $excelTask->type            = HcmExcelTackModel::TYPE_ATTENDANCE;
        $excelTask->staff_info_id   = $params['operator'];
        $excelTask->args_json       = base64_encode(json_encode($params));
        $excelTask->created_at      = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
        return $excelTask->id;
    }


    /**
     * 修改历史
     * @param $param
     * @return array
     * @throws ValidationException|BusinessException
     */
    public function history_list($param): array
    {
        $return = [];
        //原 操作历史 数据列表
        $log_type = intval($param['log_type']);

        //原来的 四个类型 列表页
        $old_type_arr = [
            Enums::operation_type_ot,
            Enums::operation_type_make_up,
            Enums::operation_type_add_leave,
            Enums::operation_type_leave_cancel,
        ];
        if (in_array($log_type, $old_type_arr)) {
            $return = $this->history_list_before($param);
        }

        //修改考勤记录列表页
        if ($log_type == Enums::operation_type_attendance_edit) {
            $return = $this->history_list_attendance_edit($param);
        }

        //全勤奖不考核日期
        if($log_type == Enums::operation_type_full_press){
            $return = $this->history_list_full_prise($param);
        }

        //出差添加操作日志 只有泰国
        if($log_type == Enums::operation_type_bt){
            $server = new BusinessTripService();
            $return = $server->tripOperateLogList($param);
        }

        return $return;
    }

    //原来的 揉到一起的 操作历史 列表页 操作类型 1，2，3，4  这是 controller 搬过来的

    /**
     * @throws ValidationException
     * @throws BusinessException
     */
    protected function history_list_before($param): array
    {
        $staff_info_id = intval($param['staff_info_id']);
        $log_type      = intval($param['log_type']); //1:oT 2:补卡 3：请假 4：请假撤销
        $start_time    = $param['start_time'];
        $end_time      = $param['end_time'];
        $offset        = $param['page_num'] ?? 1;
        $limit         = $param['page_size'] ?? 10000;


        //!!! log type 必填 这功能没做大列表 只能筛选单个类型
        if (empty($log_type) || $log_type < 0) {
            throw new ValidationException("need param log_type input");
        }

        $params = [
            'staff_info_id' => $param['staff_info_id'],
            'operator'      => $param['operator'],
        ];

        //获取权限
        $operatorPermission = $this->getOperatorPermissionDetail($params);
        if (empty($operatorPermission['all']) && empty($operatorPermission['part'])) {
            throw new BusinessException(self::$t->_('permission_denied'));
        }

        $leave_type = $ot_type = [];
        //获取对应的 枚举类型

        if ($log_type == Enums::operation_type_ot) {//加班
            $ot_res  = $this->getApiDatassFromCache('by', '', 'getApprovalOtType', self::$language, []);
            $ot_type = array_column($ot_res['data'], 'msg', 'code');
        }
        if ($log_type == Enums::operation_type_make_up) {//补卡

        }
        if (in_array($log_type, [Enums::operation_type_add_leave, Enums::operation_type_leave_cancel])) {//请假 请假撤销
            $res_leave_type = $this->getApiDatassFromCache('by', '', 'leaveTypeBookAll', self::$language, []);
            $leave_type     = array_column($res_leave_type['data'], 'msg', 'code');
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['a' => AttendanceDataLogModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'hsi.staff_info_id = a.staff_info_id', 'hsi');
        $builder->where("log_type = :log_type:", ['log_type' => $log_type]);

        if ($staff_info_id > 0) {
            $builder->andWhere('a.staff_info_id = :staff_info_id:', ["staff_info_id" => $staff_info_id]);
        }

        if (!empty($start_time)) {
            $start_time = gmdate('Y-m-d H:i:s', strtotime($start_time));
            $builder->andWhere('a.update_at >= :start_at:', ["start_at" => $start_time]);
        }
        //结束
        if (!empty($end_time)) {
            $end_time = gmdate('Y-m-d H:i:s', strtotime($end_time));
            $builder->andWhere('a.update_at <= :end_at:', ["end_at" => $end_time]);
        }

        $builder    = $this->generateBuilder($builder, $operatorPermission);
        $list_count = $builder->columns("count(1) as count")->getQuery()->getSingleResult()->count;

        $columns = ' a.id,
                  a.leave_type,
                  a.leave_start_time,
                  a.leave_end_time,
                  a.leave_day,
                  a.staff_info_id,
                  a.attendance_type,
                  a.attendance_date,
                  a.attendance_time,
                  a.ot_date,
                  a.ot_start_time,
                  a.ot_type,
                  a.duration,
                  a.log_type,
                  a.create_at,
                  a.update_at,
                  a.operator,
                  a.operator_name,
                  a.image_path,
                  a.kilometres,
                  a.reason,
                  a.leave_start_type,
                  a.leave_end_type,
                  hsi.name as staff_name
                  ';
        $builder->columns($columns);
        $builder->orderBy("update_at desc");

        if (empty($param['is_export'])) {
            $builder->limit($limit, ($offset - 1) * $limit);
        }

        $list = $builder->getQuery()->execute()->toArray();

        $search_result                    = [];
        $search_result['recordsFiltered'] = 0;
        $search_result['recordsTotal']    = 0;
        $search_result['DataList']        = [];
        if (empty($list)) {
            return $search_result;
        }
        $typeArr = Enums::$time_interval;
        if (isCountry('MY')) {
            $typeArr = Enums::$time_half;
        }

        $staff_info_ids = array_values(array_unique(array_column($list,'staff_info_id')));
        $staffFle = [];
        if ($staff_info_ids){
            $staffFle = StaffInfoModel::find([
                'columns'    => 'id,name',
                'conditions' => 'id in ({ids:array})',
                'bind'       => [
                    'ids' => $staff_info_ids,
                ],
            ])->toArray();
            $staffFle = array_column($staffFle,'name','id');
        }
        //整理 列表数据
        foreach ($list as &$value) {
            $staffFleName = $staffFle[$value['staff_info_id']] ?? '';
            $value['name']     = $value['staff_info_id'] . '（' . (empty($value['staff_name']) ? $staffFleName : $value['staff_name']) . '）';
            $value['staff_name']     = (empty($value['staff_name']) ? $staffFleName : $value['staff_name']);
            $value['operator'] = $value['operator'] . ' ' . $value['operator_name'];
            //加班类型
            $value['ot_type_name'] = empty($ot_type[$value['ot_type']]) ? '' : $ot_type[$value['ot_type']];

            $value['attendance_type_name'] = '';
            if ($value['attendance_type'] == 1) {
                $value['attendance_type_name'] = self::$t->_('attendance_started_at');
            }
            if ($value['attendance_type'] == 2) {
                $value['attendance_type_name'] = self::$t->_('attendance_ended_at');
            }
            if ($value['attendance_type'] == 3) {
                $value['attendance_type_name'] = self::$t->_('attendance_started_at_second');
            }
            if ($value['attendance_type'] == 4) {
                $value['attendance_type_name'] = self::$t->_('attendance_ended_at_second');
            }
            //请假类型
            $value['leave_type_name'] = empty($leave_type[$value['leave_type']]) ? '' : $leave_type[$value['leave_type']];
            if ($value['leave_type'] == StaffAuditModel::LEAVE_TYPE_3 && get_country_code() == 'TH') {
                $value['leave_type_name'] = $leave_type[StaffAuditModel::LEAVE_TYPE_38] ?? '';
            }
            $value['update_at'] = show_time_zone($value['update_at']);
            $value['create_at'] = show_time_zone($value['create_at']);

            $value['leave_start_type_name'] = isset($typeArr[$value['leave_start_type']]) ? self::$t[$typeArr[$value['leave_start_type']]] : '';
            $value['leave_end_type_name']   = isset($typeArr[$value['leave_end_type']]) ? self::$t[$typeArr[$value['leave_end_type']]] : '';
            $value['image_path']            = empty($value['image_path']) ? [] : explode(',', $value['image_path']);
        }

        $search_result['recordsFiltered'] = intval($list_count); //过滤后的记录数（如果有接收到前台的过滤条件，则返回的是过滤后的记录数）
        $search_result['recordsTotal']    = intval($list_count); //即没有过滤的记录数（数据库里总共记录数）
        $search_result['DataList']        = $list;

        return $search_result;
    }

    //新增的一个 修改考勤的 历史列表页  5 修改考勤记录（原来跟不卡共用一个类型 拆出来）

    /**
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function history_list_attendance_edit($param)
    {
        $staff_info_id = intval($param['staff_info_id']);
        $start_time    = $param['start_time'];
        $end_time      = $param['end_time'];
        $offset        = $param['page_num'] ?? 1;
        $limit         = $param['page_size'] ?? 20;

        $params = [
            'staff_info_id' => $param['staff_info_id'],
            'operator'      => $param['operator'],
        ];

        //获取权限
        $operatorPermission = $this->getOperatorPermissionDetail($params);
        if (empty($operatorPermission['all']) && empty($operatorPermission['part'])) {
            throw new ValidationException(self::$t->_('permission_denied'));
        }

        $builder = $this->modelsManager->createBuilder();

        $builder->from(["a" => AttendanceOperateLogModel::class]);
        $builder->join(HrStaffInfoModel::class, 'a.staff_info_id = hsi.staff_info_id', 'hsi');
        $builder->leftjoin(HrStaffInfoModel::class, 'a.operate_id = s2.staff_info_id', 's2');

        if (!empty($staff_info_id)) {
            $builder->andWhere("a.staff_info_id = :staff_id:", ['staff_id' => $staff_info_id]);
        }

        if (!empty($start_time)) {
            $start_time = gmdate('Y-m-d H:i:s', strtotime($start_time));
            $builder->andWhere('a.update_at >= :start_at:', ["start_at" => $start_time]);
        }
        //结束
        if (!empty($end_time)) {
            $end_time = gmdate('Y-m-d H:i:s', strtotime($end_time));
            $builder->andWhere('a.update_at <= :end_at:', ["end_at" => $end_time]);
        }

        $builder      = $this->generateBuilder($builder, $operatorPermission);
        $count_column = "count(1) count";
        $builder->columns($count_column);

        $page_count = $builder->getQuery()->getSingleResult()->toArray();
        $page_count = !empty($page_count['count']) ? $page_count['count'] : 0;

        if (!empty($param['is_export']) && $page_count > 50000) {
            throw new BusinessException(self::$t->_('file_download_limit', ['num' => 50000]));
        }


        $search_result['recordsFiltered'] = 0; //过滤后的记录数（如果有接收到前台的过滤条件，则返回的是过滤后的记录数）
        $search_result['recordsTotal']    = 0; //即没有过滤的记录数（数据库里总共记录数）
        $search_result['DataList']        = [];
        if ($page_count == 0) {
            return $search_result;
        }
        //我不知道 为啥 弄两个参数
        $search_result['recordsFiltered'] = $search_result['recordsTotal'] = $page_count;

        $data_column = "a.staff_info_id,a.attendance_date,a.shift_start_before,a.shift_end_before,a.shift_start_after
                        ,a.shift_end_after,a.started_at_before,a.end_at_before,a.started_at_after
                        ,a.end_at_after,a.operate_id,a.create_at,a.update_at
                        ,hsi.name staff_name,s2.name operate_name";

        $builder->columns($data_column);

        if (empty($param['is_export'])) {
            $builder->limit($limit, ($offset - 1) * $limit);
        }

        $list = $builder->getQuery()->execute()->toArray();

        if (!empty($list)) {
            foreach ($list as &$li) {
                $li['staff_text']        = "{$li['staff_info_id']}({$li['staff_name']})";
                $li['operate_text']      = "{$li['operate_id']}({$li['operate_name']})";
                $li['before_shift_text'] = "{$li['shift_start_before']}-{$li['shift_end_before']}";
                $li['after_shift_text']  = "{$li['shift_start_after']}-{$li['shift_end_after']}";
                //如果 没操作班次修改 用之前的
                if (empty($li['shift_start_after'])) {
                    $li['after_shift_text'] = $li['before_shift_text'];
                }

                //时区转换
                $li['create_at'] = show_time_zone($li['create_at']);
                $li['update_at'] = show_time_zone($li['update_at']);
            }
        }

        $search_result['DataList'] = $list;
        return $search_result;
    }


    /**
     * @param $param
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function history_list_full_prise($param)
    {
        $menuId = [90022];//权限id
        $staff_info_id = intval($param['staff_info_id']);
        $start_time    = $param['start_time'];
        $end_time      = $param['end_time'];
        $offset        = $param['page_num'] ?? 1;
        $limit         = $param['page_size'] ?? 20;
//        $operator      = $param['operator'];

//        //获取当前登陆人的角色
//        $staffPosition    = HrStaffInfoPositionModel::find([
//            'staff_info_id = :staff_info_id:',
//            'bind'    => [
//                'staff_info_id' => $operator,
//            ],
//            'columns' => 'position_category',
//        ])->toArray();
//        $staffPosition = array_column($staffPosition, 'position_category');
//        //角色权限
//        $rolePermission = HcmRolePermissionInfoModel::find([
//            'conditions' => 'role_id in ({roles:array}) and permission_id in ({menu_id:array}) and is_deleted = 0',
//            'bind' => ['roles' => $staffPosition, 'menu_id' => $menuId]
//        ])->toArray();
//        $staffPermission = HcmStaffPermissionInfoModel::find([
//            "staff_info_id = :staff_info_id: and permission_id IN ({permission:array}) and is_deleted = 0",
//            "bind" => [
//                "staff_info_id" => $operator,
//                "permission"    => $menuId,
//            ],
//        ])->toArray();
//
//        if(empty($rolePermission) && empty($staffPermission)){
//            throw new ValidationException(self::$t->_('permission_denied'));
//        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(["f" => FullAttendanceIgnoreSettingModel::class]);

        if (!empty($staff_info_id)) {
            $builder->andWhere("f.staff_info_id = :staff_id:", ['staff_id' => $staff_info_id]);
        }

        if (!empty($start_time)) {
            $start_time = gmdate('Y-m-d H:i:s', strtotime($start_time));
            $builder->andWhere('f.updated_at >= :start_at:', ["start_at" => $start_time]);
        }
        //结束
        if (!empty($end_time)) {
            $end_time = gmdate('Y-m-d H:i:s', strtotime($end_time));
            $builder->andWhere('f.updated_at <= :end_at:', ["end_at" => $end_time]);
        }
        $count_column = "count(1) count";
        $builder->columns($count_column);

        $page_count = $builder->getQuery()->getSingleResult()->toArray();
        $page_count = !empty($page_count['count']) ? $page_count['count'] : 0;

        if (!empty($param['is_export']) && $page_count > 50000) {
            throw new BusinessException(self::$t->_('file_download_limit', ['num' => 50000]));
        }

        $search_result['recordsFiltered'] = 0; //过滤后的记录数（如果有接收到前台的过滤条件，则返回的是过滤后的记录数）
        $search_result['recordsTotal']    = 0; //即没有过滤的记录数（数据库里总共记录数）
        $search_result['DataList']        = [];
        if ($page_count == 0) {
            return $search_result;
        }
        //我不知道 为啥 弄两个参数
        $search_result['recordsFiltered'] = $search_result['recordsTotal'] = $page_count;
        $data_column = "id,staff_info_id,name,date_at,operate_id,operate_name,reason,created_at,updated_at";
        $builder->columns($data_column);
        if (empty($param['is_export'])) {
            $builder->limit($limit, ($offset - 1) * $limit);
        }

        $list = $builder->getQuery()->execute()->toArray();
        if(empty($list)){
            return $search_result;
        }
        $ids = array_column($list, 'id');
        $imgData = FullAttendanceIgnoreImageModel::find([
            'conditions' => 'f_id in ({f_id:array})',
            'bind' => ['f_id' => $ids]
        ])->toArray();
        $imgPath = [];
        if(!empty($imgData)){
            foreach ($imgData as $img){
                $imgPath[$img['f_id']][] = $img['image_path'];
            }
        }

        foreach ($list as &$li) {
            $li['image_path'] = $imgPath[$li['id']] ?? [];
            $li['created_at'] = date('Y-m-d H:i:s', strtotime($li['created_at']) + ($this->timeOffset) * 3600);
            $li['updated_at'] = date('Y-m-d H:i:s', strtotime($li['updated_at']) + ($this->timeOffset) * 3600);
            $li['log_type']   = Enums::operation_type_full_press;
        }
        $search_result['DataList'] = $list;
        return $search_result;
    }

    //这个也是 controller 搬过来的
    public function tool_save($param)
    {
        $id      = intval($param['id']);                       //各个业务的主键
        $date_at = date('Y-m-d', strtotime($param['date_at']));//修改哪一天的数据
        $type    = intval($param['type']);                     //1 ot  2 修改考勤

        if (empty($type) || !in_array($type, [Enums::operation_type_ot, Enums::operation_type_make_up])) {
            throw new ValidationException("wrong params for type");
        }

        if (empty($id) || $id < 0) {
            throw new ValidationException("wrong params for id");
        }

        //只能 修改 上个月 和当月的数据
        $start_month = date('Y-m-01');
        $limit       = date('Y-m-d', strtotime("{$start_month} -1 month"));
        if ($date_at < $limit) {
            throw new ValidationException(self::$t->_('two_month_limit'));
        }


        $log_data = [];
        $flag     = false;
        switch ($type) {
            case 1://修改OT
                if(!is_integer($param['ot_type']) || empty($param['ot_type'])){
                    throw new ValidationException('have no permission');
                }
                $ot_type     = intval($param['ot_type']);
                $start_time  = trim($param['start_time']);
                $duration    = floatval($param['duration']);
                $staff_id    = intval($param['staff_id']);//加班操作 传的是 这个
                $send_params = [
                    'overtime_id' => $id,
                    'staff_id'    => $staff_id,
                    'operator'    => $param['user_info']['id'] ?? 0,
                    'type'        => $ot_type,
                    'start_time'  => $start_time,
                    'duration'    => $duration,
                    'date_at'     => $date_at,
                ];
                //by rpc 接口
                $result = $this->attendanceOtEdit($send_params);

                if ($result['code'] != 1) {
                    $msg = $result['msg'] ?? "error";
                    throw new ValidationException($msg);
                }

                $log_data = [
                    'staff_info_id' => $staff_id,
                    'ot_date'       => $date_at,
                    'ot_start_time' => $start_time,
                    'ot_type'       => $ot_type,
                    'duration'      => $duration,
                    'log_type'      => $type,
                    'operator'      => $param['user_info']["id"] ?? 0,
                    'operator_name' => $param['user_info']["name"] ?? '',
                ];
                break;
            case 2://修改打卡 原来跟补卡类型 一样 新需求拆分出来 枚举改成5 操作记录 存新的日志表 attendance_operate_log
                $staff_info_id = intval($param['staff_info_id']);
                $shift_id      = intval($param['shift_value']);
                $started_at    = trim($param['started_at']);
                $end_at        = trim($param['end_at']);
                $staffServer = new StaffService();
                $staffInfo = $staffServer->getHrStaffInfo($staff_info_id);
                if(empty($staffInfo)){
                    throw new ValidationException('staff id error');
                }

                //不能修改当天和以后的日期
                $current_date = date('Y-m-d');
                if (RUNTIME == 'pro' && $date_at >= $current_date) {
                    throw new ValidationException(self::$t->_('today_future_limit'));
                }

                //查询 要修改的信息记录 把原数据 存下来 要在 调rpc 之前
                $attendance_info = StaffWorkAttendanceModel::findFirst(intval($id));
                if (empty($attendance_info)) {
                    throw new ValidationException("wrong params for id");
                }
                $attendance_info = $attendance_info->toArray();

                $send_params = [
                    'id'              => $id,
                    'staff_info_id'   => $staff_info_id,
                    'attendance_date' => $date_at,
                    'start_time'      => $started_at,
                    'end_time'        => $end_at,
                    //新增 参数
                    'shift_id'        => $shift_id,
                ];
                //主播不能 修改班次
                $liveJobId = (new SettingEnvService())->getSetVal('free_shift_position');
                $liveJobId = empty($liveJobId) ? [] : explode(',', $liveJobId);
                if (in_array($staffInfo['job_title'], $liveJobId) && $shift_id > 0) {
                    throw new ValidationException(self::$t->_('reissue_live_notice'));
                }
                $result = $this->attendanceEdit($send_params);
                if ($result['code'] != 1) {
                    $msg = $result['msg'] ?? "error";
                    throw new ValidationException($msg);
                }

                if (!empty($shift_id) && $shift_id > 0) {//操作人没有修改班次 不需要查询对应的班次 也不需要修改数据
                    $shift_info  = HrShift::findFirst(intval($shift_id));
                    $shift_start = empty($shift_info) ? '' : $shift_info->start;
                    $shift_end   = empty($shift_info) ? '' : $shift_info->end;

                    //原班次id
                    $oldShift = HrStaffShiftMiddleDateModel::findFirst([
                        'conditions' => 'staff_info_id = :staff_id: and shift_date = :date_at:',
                        'bind'       => [
                            'staff_id' => $param['staff_info_id'],
                            'date_at'  => $attendance_info['attendance_date'],
                        ],
                    ]);
                    //修改班次 日志
                    $logParam['staff_info_id'] = $staff_info_id;
                    $logParam['date_at']       = $attendance_info['attendance_date'];
                    $logParam['operate_id']   = $staff_info_id;
                    $extend['before']          = empty($oldShift) ? '' : "{$oldShift->shift_start}-{$oldShift->shift_end}";
                    $extend['after']           = "{$shift_start}-{$shift_end}";
                    $logServer                 = new WorkShiftService();
                    $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CURRENT_SHIFT, $logParam, $extend);

                }


                $add_hour                       = $this->config->application->add_hour;
                $log_data['staff_info_id']      = $attendance_info['staff_info_id'];
                $log_data['attendance_date']    = $attendance_info['attendance_date'];
                $log_data['shift_start_before'] = $attendance_info['shift_start'];
                $log_data['shift_end_before']   = $attendance_info['shift_end'];
                $log_data['shift_start_after']  = empty($shift_start) ? '' : $shift_start;
                $log_data['shift_end_after']    = empty($shift_end) ? '' : $shift_end;
                $log_data['started_at_before']  = date('Y-m-d H:i:s',
                    strtotime($attendance_info['started_at']) + $add_hour * 3600);
                $log_data['end_at_before']      = date('Y-m-d H:i:s',
                    strtotime($attendance_info['end_at']) + $add_hour * 3600);
                $log_data['started_at_after']   = empty($started_at) ? '' : $started_at;
                $log_data['end_at_after']       = empty($end_at) ? '' : $end_at;
                $log_data['operate_id']         = $param['user_info']['id'] ?? 0;


                $log_model = new AttendanceOperateLogModel();
                $flag      = $log_model->save($log_data);

                return $flag;
                break;
        }


        if (!empty($log_data)) {//不为空 并且 是 历史的 4个类型 就保存原来的 操作历史表
            $before_log_model = new AttendanceDataLogModel();
            $flag             = $before_log_model->create($log_data);
        }

        $logger = $this->getDI()->get('logger');
        $log    = [
            'method'   => 'editBackyardAction',
            'params'   => $param,
            'operator' => $param['user_info'],
            'result'   => $flag,

        ];
        $logger->info($log);

        return true;
    }


    public function get_shift_enum()
    {
        //concat(start,'-',end) 不好使 不知道咋写
        $data = HrShift::find([
            'conditions' => 'shift_group = :shift_group:',
            'bind'       => [
                'shift_group' => HrShift::SHIFT_GROUP_FULL_DAY_SHIFT,
            ],
        ])->toArray();

        $return = [];
        if (!empty($data)) {
            foreach ($data as $v) {
                $row['code']  = $v['id'];
                $row['value'] = "{$v['start']}-{$v['end']}";
                $return[]     = $row;
            }
        }

        return $return;
    }

    /**
     * 校验当前登陆人查看数据权限
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function checkOperatorPermission($params): bool
    {
        $staffInfoId = $params['staff_info_id'];
        $result      = $this->getOperatorPermissionDetail($params);
        if (!empty($staffInfoId) && empty($this->checkStaffPermission($params, $result))) {
            return false;
        }
        return !empty($result['all']) || !empty($result['part']);
    }

    /**
     * @description 获取权限详情
     * @return array
     * @throws \Exception
     */
    public function getOperatorPermissionDetail($params)
    {
        $operatorInfoId = $params['operator'];      //当前登陆人
        if (empty($params)) {
            throw new ValidationException("miss args");
        }

        if (in_array($operatorInfoId, explode(',', env('sa_id')))) {
            return [
                "all"  => true,
                "part" => false,
            ];
        }
        //获取当前登陆人的角色
        $staffPosition    = HrStaffInfoPositionModel::find([
            'staff_info_id = :staff_info_id:',
            'bind'    => [
                'staff_info_id' => $operatorInfoId,
            ],
            'columns' => 'position_category',

        ])->toArray();
        $staffPositionIds = array_column($staffPosition, 'position_category');

        //获取配置权限角色
        $probationRolesAll  = [41, 99];
        $probationRolesPart = [125];

        $permissionIds = HcmPermissionModel::find([
            "t_key IN ({keys:array})",
            "bind"    => [
                "keys" => ['modify_attendance_record'],
            ],
            "columns" => "id",
        ])->toArray();
        $permissionIds = array_column($permissionIds, "id");

        $permissionInfo = HcmStaffPermissionInfoModel::find([
            "staff_info_id = :staff_info_id: and permission_id IN ({permission:array}) and is_deleted = 0",
            "bind" => [
                "staff_info_id" => $operatorInfoId,
                "permission"    => $permissionIds,
            ],
        ])->toArray();


        //全部权限 角色：HRIS管理员[41]、超级管理员[99]
        //部分权限 角色：HR Generalist
        //其余人 没有权限
        if (array_intersect($probationRolesAll, $staffPositionIds) || !empty($permissionInfo)) {
            return [
                "all"  => true,
                "part" => false,
            ];
        } else {
            if (array_intersect($probationRolesPart, $staffPositionIds)) {
                $staffService = new StaffService();
                $getStaffData = $staffService->setExpire(60 * 10)->getStaffJurisdiction($operatorInfoId);

                //没有管辖权限
                if (empty($getStaffData['departments']) &&
                    empty($getStaffData['stores']) &&
                    empty($getStaffData['regions']) &&
                    empty($getStaffData['pieces']) &&
                    empty($getStaffData['store_categories'])
                ) {
                    return [
                        "all"  => false,
                        "part" => false,
                    ];
                }

                return [
                    "all"  => false,
                    "part" => $getStaffData,
                ];
            } else {
                return [
                    "all"  => false,
                    "part" => false,
                ];
            }
        }
    }

    /**
     * @param $params
     * @param array $result
     * @return bool
     * @throws ValidationException
     */
    private function checkStaffPermission($params, array $data)
    {
        $staffInfoId = $params['staff_info_id']; //搜索或被申请人
        if (empty($staffInfoId)) {
            return false;
        }

        if ($data['all']) {
            return true;
        }
        $getStaffData = $data['part'];

        //没有管辖权限
        if (empty($getStaffData['departments']) &&
            empty($getStaffData['stores']) &&
            empty($getStaffData['regions']) &&
            empty($getStaffData['pieces']) &&
            empty($getStaffData['store_categories'])
        ) {
            return false;
        }

        //获取要查询的人的个人信息
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("staff_info_id,node_department_id,sys_store_id");
        $builder->from(['hsi' => \App\Models\backyard\HrStaffInfoModel::class]);
        $builder->where("staff_info_id = :staff_info_id:", ['staff_info_id' => $staffInfoId]);

        $builder   = $this->generateBuilder($builder, $data);
        $staffInfo = $builder->getQuery()->execute()->toArray();

        return !empty($staffInfo);
    }

    /**
     * @description 处理管辖范围
     *
     * @param $builder
     * @param $operatorPermission
     * @return mixed
     */
    public function generateBuilder($builder, $operatorPermission)
    {
        if (!empty($operatorPermission['part'])) {
            $getStaffData = $operatorPermission['part'];
            $staffService = new StaffService();

            //管辖网点
            //如果存在 -2 就代表要查询所有网点
            if (in_array(HrStaffManageStoreModel::$all_id, $getStaffData['stores'])) {
                //全部网点 + 总部部门管辖权限
                if (!empty($getStaffData['departments'])) {
                    $builder->andWhere("(hsi.node_department_id IN({department_id:array}) and hsi.sys_store_id = '-1') or hsi.sys_store_id != '-1'",
                        [
                            'department_id' => $getStaffData['departments'],
                        ]);
                } else { //全部网点管辖权限
                    $builder->andWhere("hsi.sys_store_id != '-1'");
                }
            } else {
                $storeIds = $staffService->setExpire(60 * 10)->getStaffJurisdictionStoreFromCache($getStaffData);
                if (!empty($storeIds) && !empty($getStaffData['departments'])) {
                    $builder->andWhere("hsi.sys_store_id IN({store_id:array}) or (hsi.node_department_id IN({department_id:array}) and hsi.sys_store_id = '-1')",
                        [
                            'store_id'      => $storeIds,
                            'department_id' => $getStaffData['departments'],
                        ]);
                } else {
                    if (!empty($storeIds)) {
                        $builder->andWhere("hsi.sys_store_id IN({store_id:array})", [
                            'store_id' => $storeIds,
                        ]);
                    } else {
                        if (!empty($getStaffData['departments'])) {
                            $builder->andWhere("hsi.node_department_id IN({department_id:array}) and hsi.sys_store_id = '-1'",
                                [
                                    'department_id' => $getStaffData['departments'],
                                ]);
                        }
                    }
                }
            }
        }
        return $builder;
    }

    public function getPaidLeaveReason()
    {
        $ac = new ApiClient('by', '', 'getPaidLeaveReason', self::$language);
        $ac->setParams([[]]);
        $data = $ac->execute();

        return $data['data'] ?? [];
    }


    public function getSettingDate()
    {
        $setting = (new SettingEnvService())->getSetVal('PLKN_Date');
        if (empty($setting)) {
            return [];
        }
        $setting = trim($setting);
        $setting = str_replace('，', ',', $setting);
        $setting = explode(',', $setting);

        $data = [];
        foreach ($setting as $item) {
            [$start, $end] = explode('-', $item);
            $startDate         = str_replace('/', '-', $start);
            $endDate           = str_replace('/', '-', $end);
            $row['start_date'] = date('Y-m-d', strtotime($startDate));
            $row['end_date']   = date('Y-m-d', strtotime($endDate));
            $row['days']       = (strtotime($endDate) - strtotime($startDate)) / (24 * 3600) + 1;
            $data[]            = $row;
        }
        return $data;
    }

    /**
     * @description 获取下载模版
     * @return array
     */
    public function getTemplate(): array
    {
        $templateKey = sprintf('batch_upload_template_for_leave_%s', substr(self::$language, 0, 2));

        // 读取setting_env表中配置
        $settingEvnRet = (new SettingEnvService())->getSetVal($templateKey);
        // 如果不存在，则返回空
        if (empty($settingEvnRet)) {
            return [];
        }
        return [
            'template' => $settingEvnRet,
        ];
    }

    /**
     * @description 获取下载模版
     * @return array
     */
    public function getTemplateCompensatoryOff(): array
    {
        $templateKey = sprintf('batch_upload_template_compensatory_off_%s', substr(self::$language, 0, 2));

        // 读取setting_env表中配置
        $settingEvnRet = (new SettingEnvService())->getSetVal($templateKey);
        // 如果不存在，则返回空
        if (empty($settingEvnRet)) {
            return [];
        }
        return [
            'template' => $settingEvnRet,
        ];
    }

    /**
     * @description 上传校验
     * @param $importPath
     * @return void
     * @throws BusinessException
     */
    public function uploadCheck($importPath)
    {
        $tmpDir      = sys_get_temp_dir();                // 获取系统的临时目录路径
        $fileName    = basename($importPath);             // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;         // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($importPath))) {
            throw new BusinessException('System error');
        }
        $config       = ['path' => dirname($tmpFilePath)];
        $fileRealName = basename($tmpFilePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet();
        $excelData = $excel->getSheetData();
        if (!isset($excelData[1])) {
            throw new BusinessException('Please set the date header of the second row');
        }

        if ($this->checkIsImportDataBeyondMaxLineCount($excelData, 5000, 2)) {
            throw new BusinessException(static::$t->_('schedule_suggest_error_number'));
        }
        @unlink($tmpFilePath);;
    }

    /**
     * @description 获取导入数据
     * @param $filePath
     * @param $setColumnType
     * @return array
     */
    public function getExcelData($filePath, $setColumnType = []): array
    {
        $config       = ['path' => dirname($filePath)];
        $fileRealName = basename($filePath);
        $excel        = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($fileRealName)->openSheet();
        if (!empty($setColumnType)) {
            $excel->setType($setColumnType);
        }
        return $excel->getSheetData();
    }

    protected function init($operatorId, $staff_info_ids)
    {
        $this->operator_info = (new StaffService())->getHrStaffInfo($operatorId);
        $this->staff_info    = $this->initStaffInfo($staff_info_ids);
        $this->initPermission($operatorId);
    }


    protected function initStaffInfo($staff_info_ids)
    {
        $staffInfo = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({staff_info_id:array})',
            'bind'       => [
                'staff_info_id' => $staff_info_ids,
            ],
            'columns'    => 'staff_info_id,leave_date,state,week_working_day,sys_store_id,rest_type,wait_leave_state,job_title_grade_v2,formal,sys_department_id,is_sub_staff ',
        ])->toArray();
        return array_column($staffInfo, null, 'staff_info_id');
    }

    /**
     * @description 初始化权限数据
     * @param $operatorId
     * @return void
     * @throws \Exception
     */
    public function initPermission($operatorId)
    {
        $this->logger->info(['管辖范围 start' => $operatorId]);
        //获取当前操作人的角色
        $staffPosition    = HrStaffInfoPositionModel::find([
            'staff_info_id = :staff_info_id:',
            'bind'    => [
                'staff_info_id' => $operatorId,
            ],
            'columns' => 'position_category',

        ])->toArray();
        $staffPositionIds = array_column($staffPosition, 'position_category');

        if (array_intersect(self::ALL_PERMISSION_ROLES, $staffPositionIds)) { //全部权限
            $this->permission_type          = self::ALL_PERMISSION;
            $this->permission_staff_info_id = [];
        } else { //部分权限
            $this->permission_type = self::PART_PERMISSION;

            //初始化
            $jurisdictionStaffIds      = [];
            $storeStaffIds             = [];
            $headOperationsStaffIds    = [];
            $operationsManagerStaffIds = [];

            //获取操作人信息
            $staffService = new StaffService();
            $operatorInfo = $staffService->getHrStaffInfo($operatorId);

            //管辖范围
            if (array_intersect(self::PART_PERMISSION_JURISDICTION_ROLES, $staffPositionIds)) {
                $managerStoreList          = (new AttendanceLocationSettingService())->setExpire(60 * 10)->getManagerStore($operatorId);
                $managerStoreIds           = $managerStoreList ? array_column($managerStoreList, 'store_id') : [];
                $jurisdictionStoreStaffIds = $this->findStaffIds(['store_id' => $managerStoreIds]);

                $this->logger->info(['AttendanceTool 管辖范围 manage store' => $managerStoreIds]);

                $manageDepartmentsList = $staffService->getStaffManageDepartment($operatorId,
                    HrStaffManageDepartmentModel::TYPE_STAFF);
                $this->logger->info(['AttendanceTool 管辖范围 manage department' => $manageDepartmentsList]);

                $jurisdictionDepartmentStaffIds = $this->findStaffIds([
                    'node_department_id'          => $manageDepartmentsList,
                    'is_department_contain_store' => true,
                ]);
                $jurisdictionStaffIds           = array_merge($jurisdictionDepartmentStaffIds,
                    $jurisdictionStoreStaffIds);
            }

            //所在网点范围
            if (array_intersect(self::PART_PERMISSION_STORE_ROLES, $staffPositionIds)) {
                $storeStaffIds = $this->findStaffIds(['store_id' => [$operatorInfo['sys_store_id']]]);

                $this->logger->info(['AttendanceTool 所属网点范围' => $operatorInfo['sys_store_id']]);
            }

            //组织架构负责人
            $organizationStaffIds = $this->getManageOrganization($operatorId);

            //运营主管
            if (array_intersect(self::PART_PERMISSION_HEAD_OPERATIONS_ROLES, $staffPositionIds)) {
                $headOperationsDepartmentId = (new SysDepartmentService())->SearchSysDeptId($operatorInfo['node_department_id']);
                $allOperationsDepartmentIds = SysDepartmentModel::getSpecifiedDeptAndSubDept($headOperationsDepartmentId);
                $headOperationsStaffIds     = $this->findStaffIds(['node_department_id' => $allOperationsDepartmentIds]);
                $this->logger->info([
                    'AttendanceTool 运营主管 manage headOperationsDepartmentId' => $headOperationsDepartmentId,
                    'allOperationsDepartmentId'                             => $allOperationsDepartmentIds,
                ]);
            }

            //运营经理
            if (array_intersect(self::PART_PERMISSION_OPERATIONS_MANGER_ROLES, $staffPositionIds)) {
                $operationsManagerDepartmentIds = SysDepartmentModel::getSpecifiedDeptAndSubDept($operatorInfo['node_department_id']);
                $operationsManagerStaffIds      = $this->findStaffIds(['node_department_id' => $operationsManagerDepartmentIds]);
                $this->logger->info(['AttendanceTool 运营经理 manage department' => $operationsManagerDepartmentIds]);
            }

            //下级员工
            $subordinateStaffIds            = $staffService->getSubordinateStaff($operatorId);
            $this->permission_staff_info_id = array_merge($jurisdictionStaffIds, $storeStaffIds,
                $headOperationsStaffIds,
                $organizationStaffIds, $operationsManagerStaffIds, $subordinateStaffIds);
            $this->permission_staff_info_id = array_map('intval', $this->permission_staff_info_id);
            $this->logger->info([
                'operatorId'                         => $operatorId,
                'AttendanceTool initPermission type' => $this->permission_type,
                '共计'                                 => count($this->permission_staff_info_id),
                '员工ID'                               => $this->permission_staff_info_id,
            ]);
        }

        $this->logger->info(['管辖范围' => $operatorId]);
    }

    /**
     * @description 获取组织架构
     * @param $staff_info_id
     * @return array
     */
    public function getManageOrganization($staff_info_id): array
    {
        $dataPermissionService = new DataPermissionService();

        $manageDepartmentIds = $dataPermissionService->getManageDepartmentIds($staff_info_id);
        $departmentStaffIds  = $this->findStaffIds(['node_department_id' => $manageDepartmentIds]);
        $this->logger->info(sprintf('AttendanceTool 组织架构 manage department: %s', json_encode($departmentStaffIds)));

        $manageStoreIds = $dataPermissionService->getSelfManagerStoreIds($staff_info_id);
        $this->logger->info(sprintf('AttendanceTool 组织架构 manage store: %s', json_encode($manageStoreIds)));

        if (empty($manageStoreIds)) {
            $storeStaffIds = [];
        } else {
            $storeStaffIds = $this->findStaffIds(['store_id' => $manageStoreIds]);
        }

        return array_merge($storeStaffIds, $departmentStaffIds);
    }

    /**
     * @description 获取员工ID
     * @param array $params
     * @return array
     */
    public function findStaffIds(array $params = []): array
    {
        $nodeDepartmentIds        = $params['node_department_id'] ?? [];
        $storeIds                 = $params['store_id'] ?? [];
        $isDepartmentContainStore = $params['is_department_contain_store'] ?? false; //部门下是否包含网点员工，默认获取全部
        $isAllStore               = $params['is_all_store'] ?? false;                //是否获取全部网点员工，默认获取指定网点员工

        if (empty($nodeDepartmentIds) && empty($storeIds)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id');
        $builder->from(HrStaffInfoModel::class);
        $builder->where('state in ({state:array}) and formal in ({formal:array}) and is_sub_staff = 0',
            [
                'state'  => [
                    HrStaffInfoModel::STATE_ON_JOB,
                    HrStaffInfoModel::STATE_SUSPEND,
                    HrStaffInfoModel::STATE_RESIGN,
                ],
                'formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN],
            ]);

        if (!empty($nodeDepartmentIds)) {
            $builder->inWhere('node_department_id', $nodeDepartmentIds);

            //只取总部员工
            if ($isDepartmentContainStore) {
                $builder->andWhere('sys_store_id = :sys_store_id:', ['sys_store_id' => '-1']);
            }
        }
        if (!empty($storeIds)) {
            if (empty($isAllStore)) {
                $builder->inWhere('sys_store_id', $storeIds);
            } else { //获取全部网点员工
                $builder->andWhere('sys_store_id != :sys_store_id:', ['sys_store_id' => -1]);
            }
        }
        $staffInfo = $builder->getQuery()->execute()->toArray();

        return array_column($staffInfo, 'staff_info_id');
    }


    /**
     * @description 上传调休
     * @param $filePath
     * @param $operatorId
     * @param $fileName
     * @return array
     * @throws Exception
     */
    public function uploadImportRestDay($filePath, $operatorId, $fileName, $taskCreatTime): array
    {
        //获取Excel数据
        $excelData = $this->getExcelData($filePath,
            [3 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP, 4 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP]);
        //去重 保留行号大的数据
        $data = $fileData = [];
        foreach (static::yieldData()($excelData) as $key => $datum) {
            //表头
            if ($key == 0) {
                $fileData[] = $datum;
                continue;
            }
            //去除多余空行
            if (empty($datum[0]) && empty($datum[1])) {
                continue;
            }
            //导出使用
            if ($key == 1) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        //追加头
                $datum[5]   = static::$t->_('store_material_008');                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        //导入结果
                $datum[6] = static::$t->_('reason_error');                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              //错误原因
                $fileData[] = $datum;
                continue;
            }
            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);
        if (!empty($data)) {
            //初始化员工相关数据
            $staffInfoIds = array_column($data, 1);
            $this->init($operatorId, $staffInfoIds);
            foreach (static::yieldData()($data) as $item) {
                //校验
                [$item, $haveError] = $this->checkItem($item, $taskCreatTime);
                $fileData[] = $item;
                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }

        $url = $this->makeReturnReport($fileData, $fileName);
        return ['url' => $url, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }


    /**
     * @description 处理数据
     * @param $restDayData
     * @return void
     * @throws Exception
     */
    protected function processData($restDayData, $operatorInfo)
    {
        //保存轮休
        $hrStaffWorkDaysModel                      = new HrStaffWorkDaysModel();
        $hrStaffWorkDaysModel->staff_info_id       = $restDayData['staff_info_id'];
        $hrStaffWorkDaysModel->month               = date('Y-m', strtotime($restDayData['dst_date']));
        $hrStaffWorkDaysModel->date_at             = $restDayData['dst_date'];
        $hrStaffWorkDaysModel->operator            = $operatorInfo['staff_info_id'];
        $hrStaffWorkDaysModel->src_week            = date('oW', strtotime($restDayData['src_date']));
        $hrStaffWorkDaysModel->src_date            = $restDayData['del_src_date'] ? $restDayData['src_date'] : null;
        $hrStaffWorkDaysModel->type                = HrStaffWorkDaysModel::TYPE_2;
        $hrStaffWorkDaysModel->remark              = HrStaffWorkDaysModel::REMARK_BATCH_IMPORT; //千万不能动 有用
        $hrStaffWorkDaysModel->save();

        //添加轮休日志
        $logParam['staff_info_id'] = $restDayData['staff_info_id'];
        $logParam['date_at']       = $restDayData['dst_date'];
        $logParam['operate_id']   = $operatorInfo['staff_info_id'];
        $logServer                 = new WorkShiftService();
        $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF, $logParam, ['t_key' => 'workday_add_batch_tool']);


        (new HrStaffWorkDaysChangeModel())->insert_record([
            'month'         => date('Y-m', strtotime($restDayData['dst_date'])),
            'staff_info_id' => $restDayData['staff_info_id'],
            'date_at'       => $restDayData['src_date'],
            'type'          => HrStaffWorkDaysChangeModel::TYPE_ADJUST_OFF,
            'operator'      => $operatorInfo['staff_info_id'],
        ]);
        //同步处罚
        $server = new WorkdayService();
        $server->syncFbi([$restDayData['staff_info_id']],[$restDayData['dst_date']]);

        if ($restDayData['del_src_date']) {
            $hrStaffWorkDaysTableName = $hrStaffWorkDaysModel->getSource();
            $this->db_backyard->execute("delete from {$hrStaffWorkDaysTableName}  where staff_info_id=:id and date_at=:date_at",
                ['id' => $restDayData['staff_info_id'], 'date_at' => $restDayData['src_date']]);
            //取消轮休 添加轮休日志
            $logParam['staff_info_id'] = $restDayData['staff_info_id'];
            $logParam['date_at']       = $restDayData['src_date'];
            $logParam['operate_id']    = $operatorInfo['staff_info_id'];
            $logServer                 = new WorkShiftService();
            $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_CANCEL_OFF, $logParam, ['t_key' => 'workday_cancel_batch_tool']);

            //找到补的ph 删除 确定了 只有一条 不能有多条
            $staffHoliday = StaffPublicHolidayModel::findFirst([
                'conditions' => 'staff_info_id = :staff_id: and src_date = :src_date:',
                'bind' => ['staff_id' => $restDayData['staff_info_id'], 'src_date' => $restDayData['src_date']],
            ]);
            if($staffHoliday){
                $staffHoliday->update([
                    'is_deleted' => 1,
                    'remark' => 'compensatory off',
                ]);
                //删除对应的off
                $staffOff = HrStaffWorkDaysModel::findFirst([
                    'conditions' => 'staff_ph_id = :ph_id: and remark = :remark:',
                    'bind' => ['ph_id' => $staffHoliday->id, 'remark' => HrStaffWorkDaysModel::REMARK_FOR_COMPENSATE_OFF],
                ]);
                if($staffOff){
                    $staffOff->delete();
                }
            }

//            $staffPHModel = (new StaffPublicHolidayModel())->getSource();
//            $this->db_backyard->execute("update {$staffPHModel} set is_deleted=1,remark='compensatory off' where staff_info_id=:staff_id and src_date=:src_date",['staff_id'=>$restDayData['staff_info_id'],'src_date'=>$restDayData['src_date']]);

            $staffAuditModel = (new StaffAuditModel())->getSource();
            $this->db_backyard->execute("update {$staffAuditModel}  set status=:s,audit_reason=concat(audit_reason,'|compensatory off')  where staff_info_id=:staff_info_id and status=2 and leave_type=15 and  date(leave_start_time)=:start and date(leave_end_time)=:end",
                ['s' => StaffAuditModel::STATUS_CANCEL,'staff_info_id' => $restDayData['staff_info_id'],'start' => $restDayData['src_date'],'end'=>$restDayData['src_date']]);
            $this->logger->info([$restDayData['staff_info_id'], $restDayData['src_date'], $restDayData['dst_date']]);
        }
        //保存请假
        $staffAuditModel                   = new StaffAuditModel();
        $staffAuditModel->staff_info_id    = $restDayData['staff_info_id'];
        $staffAuditModel->audit_type       = StaffAuditModel::AUDIT_TYPE_LEAVE;
        $staffAuditModel->leave_type       = StaffAuditModel::LEAVE_TYPE_15;
        $staffAuditModel->leave_start_time = date('Y-m-d 09:00:00', strtotime($restDayData['dst_date']));
        $staffAuditModel->leave_start_type = StaffAuditModel::LEAVE_TIME_TYPE_MORNING;
        $staffAuditModel->leave_end_time   = date('Y-m-d 18:00:00', strtotime($restDayData['dst_date']));
        $staffAuditModel->leave_end_type   = StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON;
        $staffAuditModel->leave_day        = 1.0;
        $staffAuditModel->status           = StaffAuditModel::STATUS_APPROVED;
        $staffAuditModel->audit_reason     = sprintf(self::BATCH_ADD_OFF_DAY_REASON, $restDayData['src_date']);// todo 这里不能修改，有其他用途
        $staffAuditModel->save();

        //请假分割
        $staffAuditLeaveSplitModel                = new StaffAuditLeaveSplitModel();
        $staffAuditLeaveSplitModel->audit_id      = $staffAuditModel->audit_id;
        $staffAuditLeaveSplitModel->staff_info_id = $restDayData['staff_info_id'];
        $staffAuditLeaveSplitModel->date_at       = date('Y-m-d', strtotime($restDayData['dst_date']));
        $staffAuditLeaveSplitModel->save();

        $params = [
            'staff_info_id'    => $restDayData['staff_info_id'],
            'leave_start_time' => date('Y-m-d 09:00:00', strtotime($restDayData['dst_date'])),
            'leave_start_type' => StaffAuditModel::LEAVE_TIME_TYPE_MORNING,
            'leave_end_time'   => date('Y-m-d 18:00:00', strtotime($restDayData['dst_date'])),
            'leave_end_type'   => StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON,
            'leave_type'       => StaffAuditModel::LEAVE_TYPE_15,
            'leave_day'        => 1.0,
            'reason'           => sprintf(self::BATCH_ADD_OFF_DAY_REASON, $restDayData['src_date']),
            'operator'         => $operatorInfo['staff_info_id'],
            'operator_name'    => $operatorInfo['name'],
            'edit_type'        => 3,
        ];
        $this->addToDb($params);
        //发送消息
        $staffInfo = (new StaffInfoService())->getStaffInfo_hr($restDayData['staff_info_id']);
        //获取语言
        $lang      = (new StaffService())->getAcceptLanguage($restDayData['staff_info_id']);
        $t             = self::getTranslation($lang);
        $title         = $t->_('work_day_title_adjustment');//您的休息日已调整!
        $content       = $t->_('my_work_day_content_adjustment', [
            'staff_name'      => $staffInfo['name'],
            'staff_info_id'   => $restDayData['staff_info_id'],
            'staff_job_title' => $staffInfo['job_title_name'],
            'before_date'     => $restDayData['src_date'],
            'after_date'      => $restDayData['dst_date'],
        ]);
        $content   = addslashes("<div style='font-size: 30px'>".$content.'</div>');

        $message_param = [
            'id'              => time() . $restDayData['staff_info_id'] . rand(1000000, 9999999),
            'staff_users'     => [$restDayData['staff_info_id']],
            'message_title'   => $title,
            'message_content' => $content,
            'add_userid'      => 10000,
            'category'        => -1,
        ];
        (new MessagesService())->add_kit_message($message_param);
    }


    /**
     * 导入操作返回结果报告&导出结果报告
     * @param $fileData
     * @param $fileName
     * @param int $operateType
     * @return \OSS\Http\ResponseCore|string
     * @throws \Exception
     */
    public function makeReturnReport(
        $fileData,
        $fileName,
        $rowRange = 'A1:G1',
        $cellHeight = 170,
        $columnRange = 'A:G',
        $width = 16
    ) {
        $excel     = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
        $excel = $excel->fileName($fileName);
        $format = new \Vtiful\Kernel\Format($excel->getHandle());
        $wrapStyle = $format->wrap()->toResource();
        foreach ($fileData as $row => $rowItem) {
            foreach ($rowItem as $column => $colItem) {
                if ($row == 0) {
                    if ($column == 0) {
                        $excel->mergeCells($rowRange, $colItem);
                        $excel->setRow($rowRange, $cellHeight, $wrapStyle);
                        $excel->setColumn($columnRange, $width);
                    } else {
                        break;
                    }
                } else {
                    $excel->insertText($row, $column, $colItem);
                }
            }
        }
        $filePath = $excel->output();

        $flashOss = new FlashOss();
        $object   = 'REST_DAY_IMPORT/' . date('Ymd') . '/' . $fileName;
        $flashOss->uploadFile($object, $filePath, [
            OssClient::OSS_CONTENT_DISPOSTION => 'attachment',
            OssClient::OSS_CONTENT_TYPE       => OssClient::DEFAULT_CONTENT_TYPE,
        ]);
        return $object;
    }

    /**
     * @description 校验每一个item (调休)
     * @param $item
     * @return array
     */
    public function checkItem($item, $taskCreatTime): array
    {
        $haveError   = false;
        $errMsg    = '';
        $staffInfoId = $item[1];                        //员工工号
        $srcDate     = date('Y-m-d', $item[3]);         //原调休日
        $dstDate     = date('Y-m-d', $item[4]);         //新调休日
        $srcDateWeek = date('oW', strtotime($srcDate)); //原调休日所在周
        $dstDateWeek = date('oW', strtotime($dstDate)); //新调休日所在周
        $uniqueKey   = $staffInfoId . $srcDate . $dstDate;
        $delSrcDate = false;

        //原调休日所在周周一
        $dayOfWeek         = date('N', strtotime($srcDate));
        $srcDateWeekMonday = date('Y-m-d', strtotime('-' . ($dayOfWeek - 1) . ' days', strtotime($srcDate)));

        //原调休日所在周周日
        $dayOfWeek         = date('N', strtotime($dstDate));
        $dstDateWeekSunday = date('Y-m-d', strtotime('+' . (7 - $dayOfWeek) . ' days', strtotime($dstDate)));
        if (date('d', strtotime($taskCreatTime)) >= 24) {
            $salaryCurrentPeriodStartDate = date('Y-m-24', strtotime($taskCreatTime));
            $salaryPeriodStartDate        = date('Y-m-24', strtotime($taskCreatTime . ' -1 month')); //上一发薪周期开始日期
        } else {
            $salaryCurrentPeriodStartDate = date('Y-m-24', strtotime($taskCreatTime . ' -1 month'));
            $salaryPeriodStartDate        = date('Y-m-24', strtotime($taskCreatTime . ' -2 month')); //上上一发薪周期开始日期
        }
        //获取原调休日当天OT申请记录
        $db = BackyardBaseModel::beginTransaction($this);

        try {
            do {
                //校验日期时间格式
                if (empty($item[3]) || !is_numeric($item[3]) || strlen($item[3]) != 10 || !preg_match("/\d{4}[\-\/]*\d{2}[\-\/]*\d{2}/",
                        $srcDate)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_date_formatter_src_rest'); //原调休日期格式错误
                    break;
                }
                $item[3] = $srcDate;

                if (empty($item[4]) || !is_numeric($item[4]) || strlen($item[4]) != 10 || !preg_match("/\d{4}[\-\/]*\d{2}[\-\/]*\d{2}/",
                        $dstDate)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_date_formatter_dst_rest'); //新调休日期格式错误
                    break;
                }

                $item[4] = $dstDate;

                if (in_array($uniqueKey, $this->uniqueKeyMap, true)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_date_repeat'); //上传数据重复
                    break;
                }
                $this->uniqueKeyMap[] = $uniqueKey;

                if (!isset($this->staff_info[$staffInfoId])) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_staff_id'); //员工ID错误
                    break;
                }

                //校验权限
                if ($this->permission_type != self::ALL_PERMISSION && !in_array($staffInfoId,
                        $this->permission_staff_info_id)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_no_permission'); //无权限修改
                    break;
                }

                if ($this->staff_info[$staffInfoId]['week_working_day'] != Enums\StaffEnums::WEEK_WORKING_DAY_SIX) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_staff_work_days_not_six'); //员工不是6天班员工
                    break;
                }

                if (($this->staff_info[$staffInfoId]['state'] == HrStaffInfoModel::STATE_RESIGN
                        || $this->staff_info[$staffInfoId]['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES
                    ) && !empty($this->staff_info[$staffInfoId]['leave_date']) &&
                    strtotime($this->staff_info[$staffInfoId]['leave_date']) <= strtotime($dstDate)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_1'); //新调休日期需在离职日期之前
                    break;
                }

                //获取员工公共假期
                $parameters = [
                    'staff' => [$this->staff_info[$staffInfoId]],
                    'date'  => $srcDateWeekMonday,
                ];

                //获取员工公共假期 + 法定节假日
                $staffPublicHoliday = (new StaffPublicHolidayService())->getStaffData($staffInfoId, $srcDateWeekMonday,
                    $dstDateWeekSunday);
                $staffHoliday       = reBuildCountryInstance(new HolidayService())->getStaffHoliday($parameters);
                $staffHolidayList   = array_merge($staffHoliday[$staffInfoId], $staffPublicHoliday);
                $this->logger->info(sprintf('checkItem {%d}, 员工公共假期 : %s , 法定节假日: %s', $staffInfoId,
                    json_encode($staffPublicHoliday), json_encode($staffHoliday)));

                if (!empty($staffHolidayList) && in_array($dstDate, $staffHolidayList)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_2'); //新调休日当天不能是法定假日
                    break;
                }
                $taskCreatTimeYm    = date('Y-m', strtotime($taskCreatTime));
                $taskCreatTimeJudge = strtotime($taskCreatTime) >= strtotime($taskCreatTimeYm . '-24 00:00:00') && strtotime($taskCreatTime) <= strtotime($taskCreatTimeYm . '-25 09:00:00');
                //获取原调休日期所在周的全部休息日
                //固定休: 休息日于PH重合需要算休息日
                //轮休: 休息日于PH重合不算休息日，即 开始日所在周的休息日排掉PH后，还有休息日则已设置休息日
                $srcRestDay      = $this->getRestDaysBySpecDate($staffInfoId, $srcDate);
                $srcRestDateList = array_column($srcRestDay, 'date_at');
                $restDayMap = array_column($srcRestDay, 'src_week','date_at');
                $srcRestDateList = $this->staff_info[$staffInfoId]['rest_type'] == HrStaffInfoModel::REST_TYPE_2 ? $srcRestDateList : array_diff($srcRestDateList,
                    $staffHolidayList);
                if ($srcRestDateList) {
                    if (!in_array($srcDate, $srcRestDateList)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('err_msg_validate_src_rest_check_5'); //原调休日所在周有休息日且原调休日不是轮休管理设置的休息日
                        break;
                    } else {
                        //原调休日已经是被调休过来的休息日 & 原调休日不能是通过调休设置的休息日
                        if ($restDayMap[$srcDate]) {
                            $haveError = true;
                            $errMsg    = static::$t->_('err_msg_validate_src_rest_check_10');
                            break;
                        }
                        $delSrcDate = true;
                    }
                    if ($taskCreatTimeJudge && strtotime($srcDate) < strtotime($salaryPeriodStartDate)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('err_msg_validate_src_rest_check_6'); //原调休日需在上个薪资开始日期及之后
                        break;
                    }
                    if (!$taskCreatTimeJudge && strtotime($srcDate) < strtotime($salaryCurrentPeriodStartDate)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('err_msg_validate_src_rest_check_7'); //原调休日需在当前薪资周期内
                        break;
                    }
                    if ($this->checkStaffOtByDate($staffInfoId, $srcDate)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('err_msg_validate_src_rest_check_8'); //原调休日当天已有OT申请记录
                        break;
                    }
                    if ($this->checkStaffAttendanceTime($staffInfoId, $srcDate)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('err_msg_validate_src_rest_check_9'); //原调休日当天有缺勤
                        break;
                    }
                } else {
                    //原调休日所在自然周原先未设置休息日
                    if (strtotime($srcDate) < strtotime($salaryPeriodStartDate)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('err_msg_validate_src_rest_check_1'); //原调休日需在上个发薪周期的开始日期及之后
                        break;
                    }
                }
                if ($dstDateWeek < $srcDateWeek) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_src_rest_check_2'); //原调休日所在自然周需在新调休日所在自然周之前
                    break;
                }
                //获取原调休日所在周的休息日
                $srcRestDay = $this->getRestDayByWeek($staffInfoId, $srcDate);
                if (!empty($srcRestDay)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_src_rest_check_3',
                        ['date_at' => $srcRestDay['date_at']]); //原调休日所在周已被调休
                    break;
                }
                if ($taskCreatTimeJudge && strtotime($dstDate) < strtotime($salaryPeriodStartDate)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_7'); //新调休日需在上个薪资开始日期及之后
                    break;
                }
                if (!$taskCreatTimeJudge && strtotime($dstDate) < strtotime($salaryCurrentPeriodStartDate)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_8'); //新调休日需在当前薪资周期内
                    break;
                }
                //获取新调休日期所在周的全部休息日
                $dstRestDay = $this->getRestDaysBySpecDate($staffInfoId, $dstDate);
                if (empty($dstRestDay)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_3'); //新调休日期当周未设置休息日
                    break;
                }
                $dstRestDateList = array_column($dstRestDay, 'date_at');

                if (in_array($dstDate, $dstRestDateList)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_4'); //新调休日当天不能是休息日
                    break;
                }

                //如果是轮休 & 排除掉与PH重合的休息日后，新调休日所在周不存在休息日，则提示“新调休日期当周未设置休息日”
                //如果是固定休，则不需要排掉PH
                $dstRestDateListExclusionPh = array_diff($dstRestDateList, $staffHolidayList);
                if ($this->staff_info[$staffInfoId]['rest_type'] == HrStaffInfoModel::REST_TYPE_1 && count($dstRestDateListExclusionPh) == 0 ||
                    $this->staff_info[$staffInfoId]['rest_type'] == HrStaffInfoModel::REST_TYPE_2 && count($dstRestDateList) == 0
                ) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_3'); //新调休日期当周未设置休息日
                    break;
                }

                if ($this->staff_info[$staffInfoId]['rest_type'] == HrStaffInfoModel::REST_TYPE_1 && count($dstRestDateListExclusionPh) >= 2 ||
                    $this->staff_info[$staffInfoId]['rest_type'] == HrStaffInfoModel::REST_TYPE_2 && count($dstRestDateList) >= 2
                ) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_6'); //新休息日所在自然周最多2个休息日
                    break;
                }

                $dstLeaveList = $this->getLeaveByDate($staffInfoId, $dstDate);
                if (!empty($dstLeaveList)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_dst_rest_check_5'); //新休息日当天已有请假记录
                    break;
                }
            } while (0);

            $item[6] = $errMsg;
            if ($haveError) {
                $db->rollback();
                $item[5] = 'failure';
                return [$item, true];
            }
            $operator_info = $this->operator_info;
            $item[5]       = 'success';
            $restDayData   = [
                'staff_info_id' => $staffInfoId,
                'src_date'      => $srcDate,
                'dst_date'      => $dstDate,
                'del_src_date'      => $delSrcDate,
            ];
            $this->processData($restDayData, $operator_info);
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $item[5] = 'failure';
            $item[6] = $e->getMessage();
            return [$item, true];
        }

        return [$item, false];
    }

    /**
     * 原休息日是否有缺勤
     * @param $staffInfoId
     * @param $statDate
     * @return mixed
     * @throws Exception
     */
    public function checkStaffAttendanceTime($staffInfoId, $statDate)
    {
        $unique = $staffInfoId . '-' . $statDate;
        //打卡
        $shiftInfo = reBuildCountryInstance(new StaffShiftService())->getDailyShiftInfo([$staffInfoId],$statDate, $statDate);
        if (!isset($shiftInfo[$unique])) {
            return true;
        }
        //出勤
        $attendance_data = reBuildCountryInstance(new BackyardAttendanceService())->getAttendanceData($statDate,
            $statDate, " and staff_info_id in ({ids:array})", ['ids' => [$staffInfoId]]);
        if (!isset($attendance_data[$unique])) {
            return true;
        }
        $shiftInfo      = new ArrayToObject($shiftInfo[$unique]);
        $attendanceData = $attendance_data[$unique];
        if (isset($attendanceData[0])) {
            $attendance_started_at = $attendanceData[0]['started_at'] ?: self::NOT_ATTENDANCE_START_AT;
            $attendance_end_at     = $attendanceData[0]['end_at'] ?: self::NOT_ATTENDANCE_END_AT;
        }
        if (isset($attendanceData[1])) {
            $first_attendance_started_at = $attendanceData[1]['started_at'] ?: self::NOT_ATTENDANCE_START_AT;
            $first_attendance_end_at     = $attendanceData[1]['end_at'] ?: self::NOT_ATTENDANCE_END_AT;
            $attendance_started_at       = $attendanceData[1]['started_at'] ?: self::NOT_ATTENDANCE_START_AT;
            $attendance_end_at           = $attendanceData[1]['end_at'] ?: self::NOT_ATTENDANCE_END_AT;
        }
        if (isset($attendanceData[2])) {
            $second_attendance_started_at = $attendanceData[2]['started_at'] ?: self::NOT_ATTENDANCE_START_AT;
            $second_attendance_end_at     = $attendanceData[2]['end_at'] ?: self::NOT_ATTENDANCE_END_AT;
        }
        if($shiftInfo->shift_type == HrShiftV2ExtendModel::SHIFT_TYPE_TWICE){
            //第一个班次上班打卡时间
            $first_attendance_started_at = strtotime($first_attendance_started_at ?? self::NOT_ATTENDANCE_START_AT);
            //第一个班次下班打卡时间
            $first_attendance_end_at     = strtotime($first_attendance_end_at ?? self::NOT_ATTENDANCE_END_AT);
            //第一个班次的班次开始时间
            $first_shift_start_at        = strtotime($shiftInfo->first_shift_start_time);
            //第一个班次的班次截止时间
            $first_shift_end_at          = strtotime($shiftInfo->first_shift_end_time);
            //第一个班次的时长
            $first_shift_duration        = $first_shift_end_at - $first_shift_start_at;//时长

            $first_late_times            = max(min($first_attendance_started_at - $first_shift_start_at, $first_shift_duration),
                0);
            $first_leave_early_times     = max(min($first_shift_end_at - $first_attendance_end_at, $first_shift_duration), 0);

            //第二个班次上班打卡时间
            $second_attendance_started_at = strtotime($second_attendance_started_at ?? self::NOT_ATTENDANCE_START_AT);
            //第二个班次下班打卡时间
            $second_attendance_end_at     = strtotime($second_attendance_end_at ?? self::NOT_ATTENDANCE_END_AT);
            //第二个班次的班次开始时间
            $second_shift_start_at        = strtotime($shiftInfo->second_shift_start_time);
            //第二个班次的班次截止时间
            $second_shift_end_at          = strtotime($shiftInfo->second_shift_end_time);
            //第二个班次的时长
            $second_shift_duration        = $second_shift_end_at - $second_shift_start_at;
            $second_late_times            = max(min($second_attendance_started_at - $second_shift_start_at,
                $second_shift_duration), 0);
            $second_leave_early_times     = max(min($second_shift_end_at - $second_attendance_end_at, $second_shift_duration),
                0);
            //第一个班次迟到
            $first_late_times = $first_late_times / 60;
            //第一个班次早退
            $first_leave_early_times = $first_leave_early_times / 60;

            //第二个班次迟到
            $second_late_times        = $second_late_times / 60;
            //第二个班次早退
            $second_leave_early_times = $second_leave_early_times / 60;
            if ($first_late_times < floatval($shiftInfo->first_late)) {
                $first_late_times = 0;
            }
            if ($first_leave_early_times < floatval($shiftInfo->first_leave_early)) {
                $first_leave_early_times = 0;
            }

            if ($second_late_times < floatval($shiftInfo->second_late)) {
                $second_late_times = 0;
            }
            if ($second_leave_early_times < floatval($shiftInfo->second_leave_early)) {
                $second_leave_early_times = 0;
            }
            $first_late_and_leave_early_times_minutes = min($first_late_times + $first_leave_early_times,
                $first_shift_duration / 60);

            $second_late_and_leave_early_times_minutes = min($second_late_times + $second_leave_early_times,
                $second_shift_duration / 60);
          return  $this->extracted(
                $first_late_and_leave_early_times_minutes,
                $second_late_and_leave_early_times_minutes,
                $shiftInfo->ab5
            );
        }
        //上班打卡时间
        $attendance_started_at = strtotime($attendance_started_at ?? self::NOT_ATTENDANCE_START_AT);
        //下班打卡时间
        $attendance_end_at = strtotime($attendance_end_at ?? self::NOT_ATTENDANCE_END_AT);
        //班次开始时间
        $stat_date_shift_start = strtotime($shiftInfo->shift_start_time);
        //班次截止时间
        $stat_date_shift_end = strtotime($shiftInfo->shift_end_time);
        $late_times = max($attendance_started_at - $stat_date_shift_start,0);
        $leave_early_times = max($stat_date_shift_end - $attendance_end_at,0);
        //分钟向下取整
        $save_late_times                    = $late_times / 60;
        $save_leave_early_times             = $leave_early_times / 60;

        if($save_late_times < floatval($shiftInfo->first_late)){
            $save_late_times = 0;
        }
        if($save_leave_early_times < floatval($shiftInfo->first_leave_early)){
            $save_leave_early_times = 0;
        }
        //班次的分钟数，班次结束时间-班次开始时间 - 中间休息时间
        $shift_duration = ($stat_date_shift_end-$stat_date_shift_start - (strtotime($shiftInfo->down_leave_shift_start_time) - strtotime($shiftInfo->up_leave_shift_end_time) ))/60;
        $late_and_leave_early_times_minutes = min($save_late_times + $save_leave_early_times,$shift_duration);
        return $this->extracted($late_and_leave_early_times_minutes,0, $shiftInfo->ab5);
    }

    /**
     * 是否缺勤  return true 代表 缺勤
     * @param $first_late_and_leave_early_times_minutes
     * @param $second_late_and_leave_early_times_minutes
     * @param $ab5
     * @return bool
     */
    public function extracted(
        $first_late_and_leave_early_times_minutes,
        $second_late_and_leave_early_times_minutes,
        $ab5
    ): bool {
        $late_and_leave_early_times_minutes = $first_late_and_leave_early_times_minutes + $second_late_and_leave_early_times_minutes;

        if ($late_and_leave_early_times_minutes < floatval($ab5)) {
            return false;
        }
        return true;
    }
    /**
     * @description 根据指定工号、日期获取指定日期所在周休息日
     * @param $staff_info_id int 员工工号
     * @param $date string 指定日期
     * @return array
     */
    protected function getRestDaysBySpecDate($staff_info_id, $date): array
    {
        // 获取指定日期是星期几
        $dayOfWeek = date('N', strtotime($date));

        // 计算出指定日期所在周的周一和周日的日期
        $monday = date('Y-m-d', strtotime('-' . ($dayOfWeek - 1) . ' days', strtotime($date)));
        $sunday = date('Y-m-d', strtotime('+' . (7 - $dayOfWeek) . ' days', strtotime($date)));

        return HrStaffWorkDaysModel::find([
            'conditions' => 'staff_info_id = :staff_info_id: and date_at >= :monday: and date_at <= :sunday:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'monday'        => $monday,
                'sunday'        => $sunday,
            ],
            'columns'    => 'staff_info_id,date_at,src_week,src_date',
        ])->toArray();
    }

    /**
     * @description 根据指定工号、日期获取指定日期所在周休息日是否被移走
     * @param $staff_info_id int 员工工号
     * @param $date string 指定日期
     * @return array
     */
    protected function getRestDayByWeek($staff_info_id, $date): array
    {
        $restDayDate = HrStaffWorkDaysModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: and src_week = :src_week:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'src_week'      => date('oW', strtotime($date)),
            ],
            'columns'    => 'staff_info_id,date_at,src_week',
        ]);
        return !empty($restDayDate) ? $restDayDate->toArray() : [];
    }

    /**
     * @description 获取请假
     * @param $staff_info_id
     * @param $date
     * @param string $operateType
     * @return array
     */
    protected function getLeaveByDate($staff_info_id, $date,$operateType=''): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('sa.audit_id');
        $builder->from(['sa' => StaffAuditModel::class]);
        $builder->leftJoin(StaffAuditLeaveSplitModel::class, 'sa.audit_id = sals.audit_id', 'sals');
        $builder->where('sa.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        if ($operateType == 'compensatoryOff') {
            $builder->andWhere('sa.leave_type != :leave_type:', ['leave_type' => StaffAuditModel::LEAVE_TYPE_15]);
        }
        $builder->inWhere('sa.status',
            [Enums\ApprovalEnums::APPROVAL_STATUS_PENDING, Enums\ApprovalEnums::APPROVAL_STATUS_APPROVAL]);
        $builder->andWhere('sals.date_at = :date_at:', ['date_at' => $date]);
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 验证是否申请过OT
     * @param $staffId
     * @param $date_at
     * @param array $type
     * @return mixed
     */
    protected function checkStaffOtByDate($staff_info_id, $date_at, array $type = [])
    {
        $conditions = "state in (1,2) and staff_id = :staff_info_id:  and date_at = :date_at: ";
        if ($type) {
            $conditions .= " and type not in ({type:array})";
        }
        return HrOvertimeModel::count(
            [
                'conditions' => $conditions,
                'bind'       => [
                    'staff_info_id' => $staff_info_id,
                    'date_at'       => $date_at,
                    'type'          => $type,
                ],
            ]
        );
    }


    /**
     * @description 上传补休-OT
     * @param $filePath
     * @param $operatorId
     * @param $fileName
     * @return array
     * @throws Exception
     */
    public function uploadImportCompensatoryOff($filePath, $operatorId, $fileName): array
    {
        //获取Excel数据
        $excelData = $this->getExcelData($filePath,
            [3 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP]);

        $data = $fileData = [];
        //去重 保留行号大的数据
        foreach ($excelData as $key => $datum) {
            //表头
            if ($key == 0) {
                $fileData[] = $datum;
                continue;
            }
            //去除多余空行
            if (empty($datum[0]) && empty($datum[1])) {
                continue;
            }
            //导出使用
            if ($key == 1) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      //追加头
                $datum[5]   = static::$t->_('store_material_008');                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      //导入结果
                $datum[6] = static::$t->_('reason_error');                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            //错误原因
                $fileData[] = $datum;
                continue;
            }
            $data[] = $datum;
        }
        $errorNumber = 0;
        $total       = count($data);
        if (!empty($data)) {
            //初始化员工相关数据
            $staffInfoIds = array_column($data, 1);
            $this->init($operatorId, $staffInfoIds);
            foreach ($data as $item) {
                //校验
                [$item, $haveError] = $this->dealCompensatoryOffItem($item);
                $fileData[] = $item;
                if ($haveError) {
                    ++$errorNumber;
                }
            }
        }

        $url = $this->makeReturnReport($fileData, $fileName, 'A1:E1', 170, 'A:E');
        return ['url' => $url, 'fail_number' => $errorNumber, 'success_number' => max($total - $errorNumber, 0)];
    }

    /**
     * 获取发薪周期开始时间
     * @return false|string
     */
    public function getSalaryPeriodStartDate()
    {
        if (date('d') >= 24) {
            $salaryPeriodStartDate = date('Y-m-24', strtotime('-1 month')); //上一发薪周期开始日期
        } else {
            $salaryPeriodStartDate = date('Y-m-24', strtotime('-2 month')); //上上一发薪周期开始日期
        }
        return $salaryPeriodStartDate;
    }

    /**
     * @description 校验每一个item （补休）
     * @param $item
     * @param $operatorId
     * @return array
     * @throws Exception
     */
    public function dealCompensatoryOffItem($item): array
    {
        $haveError   = false;
        $errMsg    = '';
        $staffInfoId = $item[1];                                //员工工号
        $offDate     = $item[3] ? date('Y-m-d', $item[3]) : ''; //补休日
        $otDuration  = $item[4];                                //ot时长
        $otStart     = null;
        $otEnd       = null;
        $onlyAddOt   = false;
        $uniqueKey   = $staffInfoId . $offDate;

        $salaryPeriodStartDate = $this->getSalaryPeriodStartDate();

        $db = BackyardBaseModel::beginTransaction($this);
        try {
            do {
                //校验日期时间格式
                if (empty($item[3]) || !is_numeric($item[3]) || strlen($item[3]) != 10 || !preg_match("/\d{4}[\-\/]*\d{2}[\-\/]*\d{2}/",
                        $offDate)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('off_day_ot_error_1'); //休息日日期格式错误
                    break;
                }
                $item[3] = $offDate;

                //员工ID错误
                if (!isset($this->staff_info[$staffInfoId])) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_staff_id');
                    break;
                }
                if ($this->staff_info[$staffInfoId]['is_sub_staff'] != 0) {
                    $haveError = true;
                    $errMsg    = static::$t->_('apply_ot_check_grade'); //子账号无权限
                    break;
                }

                //校验权限
                if ($this->permission_type != self::ALL_PERMISSION && !in_array($staffInfoId,
                        $this->permission_staff_info_id)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_no_permission'); //无权限修改
                    break;
                }

                if (in_array($uniqueKey, $this->uniqueKeyMap, true)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_date_repeat'); //上传数据重复
                    break;
                }
                $this->uniqueKeyMap[] = $uniqueKey;

                if (!in_array($this->staff_info[$staffInfoId]['week_working_day'], [HrStaffInfoModel::WEEK_WORKING_DAY_6, HrStaffInfoModel::WEEK_WORKING_DAY_FREE])) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_staff_work_days_not_six'); //员工不是6天班员工
                    break;
                }


                if (strtotime($offDate) < strtotime($salaryPeriodStartDate)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('err_msg_validate_src_rest_check_1'); //休息日需在上个发薪周期的开始日期及之后
                    break;
                }

                //获取员工公共假期
                $parameters         = [
                    'staff' => [$this->staff_info[$staffInfoId]],
                    'date'  => $salaryPeriodStartDate,
                ];
                $sysHoliday = reBuildCountryInstance(new HolidayService())->getStaffHoliday($parameters);
                $staffPublicHoliday = (new StaffPublicHolidayService())->getStaffData($staffInfoId,
                    $salaryPeriodStartDate);
                $allHoliday         = array_merge($sysHoliday[$staffInfoId], $staffPublicHoliday);

                //休息日当天已有请假记录
                $dstLeaveList = $this->getLeaveByDate($staffInfoId, $offDate,'compensatoryOff');
                if (!empty($dstLeaveList)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('off_day_ot_error_4');
                    break;
                }
                $otType = $this->getOffAndAlreadyOtTypes();
                if ($this->checkStaffOtByDate($staffInfoId, $offDate, $otType)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('off_day_ot_error_3'); //休息日当天已有OT记录
                    break;
                }

                // 导入的休息日所在周无休息日1.3倍OT记录（同意+待审批）。若导入的休息日所在周已有休息日1.3倍OT记录（同意+待审批），错误文案提示：“当周已申请过休息日1.3倍OT”
                if($this->checkWeekOt($staffInfoId, $offDate)){
                    $haveError = true;
                    $errMsg    = static::$t->_('off_day_ot_error_13'); //当周已申请过休息日1.3倍OT
                    break;
                }

                //是否申请过OT
                if (!empty($otDuration)) {
                    //打卡信息
                    $attendanceInfo = StaffWorkAttendanceRepository::getInfo('started_at,end_at',
                        'staff_info_id = :staff_info_id: and attendance_date = :attendance_date:',
                        ['staff_info_id' => $staffInfoId, 'attendance_date' => $offDate]);
                    if (empty($attendanceInfo)) {
                        $attendanceInfo = StaffAuditReissueForBusinessModel::findFirst([
                            'conditions' => 'staff_info_id = :staff_info_id: and attendance_date =:attendance_date: and status in ({status:array})',
                            'bind'       => [
                                'staff_info_id'   => $staffInfoId,
                                'attendance_date' => $offDate,
                                'status'          => [
                                    ApprovalEnums::APPROVAL_STATUS_PENDING,
                                    ApprovalEnums::APPROVAL_STATUS_APPROVAL,
                                ],
                            ],
                            'columns'    => 'start_time as started_at,end_time as end_at',
                        ]);
                    }

                    if (empty($attendanceInfo) || empty($attendanceInfo->started_at)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('off_day_ot_error_5'); //休息日当天无上班打卡时间
                        break;
                    }

                    if (empty($attendanceInfo->end_at)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('off_day_ot_error_6'); //休息日当天无下班打卡时间
                        break;
                    }
                    $checkJobGrade = $this->applyOtCcheckGrade(['staff_id' => $staffInfoId]);
                    if (!$checkJobGrade) {
                        $haveError = true;
                        $errMsg    = static::$t->_('apply_ot_check_grade'); //员工没有申请权限
                        break;
                    }
                    //todo
                    $typeBook = $this->getOtTypeAndSelectTime(['staff_id' => $staffInfoId]);
                    $typeBook = array_column($typeBook, 'code');
                    if (!in_array(4, $typeBook)) {
                        $haveError = true;
                        $errMsg    = static::$t->_('apply_ot_check_grade'); //员工没有申请权限
                        break;
                    }

                    if (!in_array($otDuration, [4, 8])) {
                        $haveError = true;
                        $errMsg    = static::$t->_('off_day_ot_error_9'); //OT时长有误
                        break;
                    }
                    $otStart = $this->getOtStartTime(show_time_zone($attendanceInfo->started_at));
                    $otEnd   = date('Y-m-d H:i:s', strtotime($otStart) + $otDuration * 3600);
                }else{
                    if (isCountry('PH') &&  $this->checkStaffOtByDate($staffInfoId, $offDate, [3])) {
                        $haveError = true;
                        $errMsg    = static::$t->_('off_day_ot_error_3'); //休息日当天已有OT记录
                        break;
                    }
                }
                //要设置的调休日当天不能是法定假日
                if($this->checkTodayIsPHDay($offDate, $allHoliday)){
                    $haveError = true;
                    $errMsg    = static::$t->_('off_day_ot_error_2');
                    break;
                }
                //要设置的休息日所在自然周已设置休息日
                /**
                 * 固定休: 休息日于PH重合需要算休息日
                 * 轮休: 休息日于PH重合不算休息日，即 开始日所在周的休息日排掉PH后，还有休息日则已设置休息日
                 */
                $srcRestDay      = $this->getRestDaysBySpecDate($staffInfoId, $offDate);
                $srcRestDateList = array_column($srcRestDay, 'date_at');
                $srcRestDateList = $this->staff_info[$staffInfoId]['rest_type'] == HrStaffInfoModel::REST_TYPE_1 ? array_diff($srcRestDateList,
                    $allHoliday) : $srcRestDateList;
                if ($srcRestDateList) {
                    $setOffTodayJudge = isCountry('PH') ? in_array($offDate, $srcRestDateList) : in_array($offDate,
                            $srcRestDateList) || in_array($offDate, $allHoliday);
                    //要设置的休息日当天是休息日或PH
                    if ($setOffTodayJudge) {
                        if (empty($otDuration)) {
                            $haveError = true;
                            $errMsg    = static::$t->_('off_day_ot_error_10');
                            break;
                        }
                        $onlyAddOt = true;
                    } else {
                        $haveError = true;
                        $errMsg    = isCountry() ? static::$t->_('off_day_ot_error_11') : static::$t->_('off_day_ot_error_12');
                        break;
                    }
                } else {
                    if (in_array($offDate, $allHoliday) && isCountry()) {
                        $haveError = true;
                        $errMsg    = static::$t->_('off_day_ot_error_2'); //要设置的调休日当天不能是法定假日
                        break;
                    }
                }
                //休息日所在自然周的休息日已经被调整到%date_at%
                $srcRestDay = $this->getRestDayByWeek($staffInfoId, $offDate);
                if (!empty($srcRestDay)) {
                    $haveError = true;
                    $errMsg    = static::$t->_('off_day_ot_error_8', ['date_at' => $srcRestDay['date_at']]);
                    break;
                }
            } while (0);
            if ($haveError) {
                $db->rollback();
                $item[5] = 'failure';
                $item[6] = $errMsg;
                return [$item, true];
            }
            $data    = [
                'staff_info_id' => $staffInfoId,
                'off_date'      => $offDate,
                'ot_duration'   => $otDuration,
                'ot_start_time' => $otStart,
                'ot_end_time'   => $otEnd,
                'only_add_ot'   => $onlyAddOt,
            ];
            $item[5] = 'success';
            $this->processCompensatoryOffData($data);
            $db->commit();
        } catch (Exception $e) {
            $this->logger->info([
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
                'trace'   => $e->getTraceAsString()]);
            $db->rollback();
            $item[5] = 'failure';
            $item[6] = $e->getMessage();
            return [$item, true];
        }
        return [$item, false];
    }


    /**
     * 处理补休数据落库
     * @param $data
     * @return void
     * @throws Exception
     */
    protected function processCompensatoryOffData($data)
    {
        if (!$data['only_add_ot']) {
            //保存轮休
            $hrStaffWorkDaysModel                = new HrStaffWorkDaysModel();
            $hrStaffWorkDaysModel->staff_info_id = $data['staff_info_id'];
            $hrStaffWorkDaysModel->month         = date('Y-m', strtotime($data['off_date']));
            $hrStaffWorkDaysModel->date_at       = $data['off_date'];
            $hrStaffWorkDaysModel->operator      = $this->operator_info['staff_info_id'];
            $hrStaffWorkDaysModel->remark        = HrStaffWorkDaysModel::REMARK_BATCH_ADD_COMPENSATORY;
            $hrStaffWorkDaysModel->save();

            //加log 日志
            $logParam['staff_info_id'] = $data['staff_info_id'];
            $logParam['date_at']       = $data['off_date'];
            $logParam['operate_id']    = $this->operator_info['staff_info_id'];
            $logServer                 = new WorkShiftService();
            $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF, $logParam, ['t_key' => 'workday_add_batch_tool']);

            //保存请假
            $staffAuditModel                   = new StaffAuditModel();
            $staffAuditModel->staff_info_id = $data['staff_info_id'];
            $staffAuditModel->audit_type    = StaffAuditModel::AUDIT_TYPE_LEAVE;
            $staffAuditModel->leave_type    = StaffAuditModel::LEAVE_TYPE_15;
            $staffAuditModel->leave_start_time = date('Y-m-d 09:00:00', strtotime($data['off_date']));
            $staffAuditModel->leave_start_type = StaffAuditModel::LEAVE_TIME_TYPE_MORNING;
            $staffAuditModel->leave_end_time   = date('Y-m-d 18:00:00', strtotime($data['off_date']));
            $staffAuditModel->leave_end_type   = StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON;
            $staffAuditModel->leave_day        = 1.0;
            $staffAuditModel->status           = StaffAuditModel::STATUS_APPROVED;
            $staffAuditModel->audit_reason     = HrStaffWorkDaysModel::REMARK_BATCH_ADD_COMPENSATORY;
            $staffAuditModel->save();

            //请假分割
            $staffAuditLeaveSplitModel                = new StaffAuditLeaveSplitModel();
            $staffAuditLeaveSplitModel->audit_id = $staffAuditModel->audit_id;
            $staffAuditLeaveSplitModel->staff_info_id = $data['staff_info_id'];
            $staffAuditLeaveSplitModel->date_at       = $data['off_date'];
            $staffAuditLeaveSplitModel->save();

            $this->addToDb([
                'staff_info_id'    => $data['staff_info_id'],
                'leave_start_time' => date('Y-m-d 09:00:00', strtotime($data['off_date'])),
                'leave_start_type' => StaffAuditModel::LEAVE_TIME_TYPE_MORNING,
                'leave_end_time'   => date('Y-m-d 18:00:00', strtotime($data['off_date'])),
                'leave_end_type'   => StaffAuditModel::LEAVE_TIME_TYPE_AFTERNOON,
                'leave_type'       => StaffAuditModel::LEAVE_TYPE_15,
                'leave_day'        => 1.0,
                'reason'           => 'Batch adding compensatory off-day',
                'operator'         => $this->operator_info['staff_info_id'],
                'operator_name'    => $this->operator_info['name'],
                'edit_type'        => Enums::operation_type_add_leave,
            ]);
        }
        $editFlag = false;
        if ($data['ot_duration'] > 0) {
            //菲律宾 将工作日1.25倍日薪OT类型改为休息日超时加班1.69倍日薪
            $editFlag = $this->editOtType($data['staff_info_id'],$data['off_date']);

            $staffInfoHistory = HrStaffTransferModel::findFirst([
                'columns'    => 'job_title,store_id,hire_type',
                'conditions' => 'staff_info_id = :staff_info_id: and stat_date = :stat_date:',
                'bind'       => ['staff_info_id' => $data['staff_info_id'], 'stat_date' => $data['off_date']],
            ]);
            if ($staffInfoHistory) {
                $staff_hire_type = $staffInfoHistory->hire_type;
                $staff_job_title = $staffInfoHistory->job_title;
                $staff_store_id  = $staffInfoHistory->store_id;
            } else {
                $staffInfo       = (new StaffService())->getStaffInfoSimplify(['staff_info_id' => $data['staff_info_id']]);
                $staff_hire_type = $staffInfo['hire_type'];
                $staff_job_title = $staffInfo['job_title'];
                $staff_store_id  = $staffInfo['sys_store_id'];
            }

            //新增休息日1.3倍OT
            $hrOvertime               = new HrOvertimeModel();
            $hrOvertime->staff_id     = $data['staff_info_id'];
            $hrOvertime->type         = $this->getOffOtType();
            $hrOvertime->start_time   = $data['ot_start_time'];
            $hrOvertime->end_time     = $data['ot_end_time'];
            $hrOvertime->reason       = 'Batch adding off-day OT';
            $hrOvertime->state        = ApprovalEnums::APPROVAL_STATUS_APPROVAL;
            $hrOvertime->duration     = $data['ot_duration'];
            $hrOvertime->date_at      = $data['off_date'];
            $hrOvertime->wf_role      = 'ot_new';
            $hrOvertime->hire_type    = $staff_hire_type;
            $hrOvertime->job_title    = $staff_job_title;
            $hrOvertime->sys_store_id = $staff_store_id;
            $hrOvertime->save();

            $this->addToDb([
                'edit_type'     => Enums::operation_type_ot,
                'staff_info_id' => $data['staff_info_id'],
                'staff_id'      => $data['staff_info_id'],
                'date_at'       => $data['off_date'],
                'start_time'    => $data['ot_start_time'],
                'duration'      => $data['ot_duration'],
                'type'          => $this->getOffOtType(),
                'reason'        => 'Batch adding off-day OT',
                'operator'      => $this->operator_info['staff_info_id'],
                'operator_name' => $this->operator_info['name'],
            ]);
        }
        $this->afterExecute($data['staff_info_id'],$data,$editFlag);
    }


    /**
     * 获取要新增的ot类型
     * @return int
     */
    public function getOffOtType(): int
    {
        //休息日1倍
        return 4;
    }

    /**
     * 获取OT的开始时间
     * @param $startTime
     * @return false|string
     */
    protected function getOtStartTime($startTime)
    {
        return $startTime;
    }

    /**
     * 获取补休 休息日当天已有OT记录 的 不校验的 ot类型
     */
    public function getOffAndAlreadyOtTypes(): array
    {
        //休息日和假期超时加班3倍
        return [2];
    }


    protected function afterExecute($staff_info_id,$data,$editFlag): bool
    {
        return true;
    }

    /**
     * 验证当周是否存在休息日OT
     * @param $staff_info_id
     * @param $date_at
     * @return bool
     */
    protected function checkWeekOt($staff_info_id, $date_at): bool
    {
        return false;
    }

    /**
     * 验证休息日是否是ph
     * @param $offDate
     * @param $allHoliday
     * @return bool
     */
    protected function checkTodayIsPHDay($offDate, $allHoliday): bool
    {
        return false;
    }

    /**
     * 修改已存在OT类型
     * @param $staff_info_id
     * @param $date_at
     * @return bool
     */
    protected function editOtType($staff_info_id, $date_at): bool
    {
        return false;
    }


    //修改考勤工具 批量导入 加班补卡请假
    public function import($param)
    {
        //重复记录
        $exist = AsyncImportTaskModel::findFirst([
            'conditions' => 'import_path = :url: and operator_id = :staff_id:',
            'bind'       => [
                'url'      => $param['file_url'],
                'staff_id' => $param['user_info']['id'],
            ],
        ]);
        if ($exist) {
            throw new ValidationException('Duplicate files');
        }
        $userId = $param['user_info']['id'];
        //文件类型
        $fileName  = basename($param['file_url']);
        $fileInfo  = pathinfo($fileName);
        $localName = date('Y-m-d') . '_' . time() . $userId . '.' . $fileInfo['extension'];
        if ($fileInfo['extension'] !== "xlsx") {
            throw  new ValidationException(self::$t->_('only_xls_xlsx'));
        }
        $filePath = sys_get_temp_dir() . '/' . $localName;

        //下载到本地
        $this->downloadFile($param['file_url'], $filePath);
        $config = ['path' => dirname($filePath)];
        $excel  = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($localName)->openSheet();
        //跳过文件头
        $data = $excel->getSheetData();
        //前两行是标题
        unset($data[0]);
        $data = array_values($data);
        //不能超过 1000
        if(count($data) > 1000){
            throw new ValidationException(self::$t->_('excel_limit_num'));
        }

        $json = ['lang' => self::$language];
        $fileServer = new AsyncImportTaskService();
        $resultName = $fileServer->getResultFileName($param['file_type'], $param['user_info']['id']);
        //写数据库 async_import_task
        $model                      = new AsyncImportTaskModel();
        $insert['operator_id']      = $param['user_info']['id'];
        $insert['import_type']      = $param['file_type'];
        $insert['import_path']      = $param['file_url'];
        $insert['result_file_name'] = $resultName;
        $insert['args_json']        = json_encode($json);
        return $model->create($insert);
    }

    //获取 env 配置模板地址
    public function getImportTemplate($type){
        $key = 'batch_import_template_' . $type;
        $url = (new SettingEnvService())->getSetVal($key);
        return $url ?: '';
    }


    //生成链接数据列表
    public function getFileList($param)
    {
        $userId          = $param['user_info']['id'];
        $page            = $param['page'];
        $size            = $param['size'];
        $return['list']  = [];
        $return['count'] = 0;
        //取总数
        $countData = ToolFileUrlModel::find([
            'columns'    => 'operate_version',
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $userId],
            'order'      => 'id desc',
            'group'   => 'staff_info_id,operate_version',
        ])->toArray();
        $countData = count($countData);

        //id 倒序
        $data = ToolFileUrlModel::find([
            'columns' => 'staff_info_id, operate_version, group_concat(file_name) as file_name,group_concat(file_url) as file_url,max(created_at) as created_at',
            'conditions' => 'staff_info_id = :staff_id:',
            'bind'       => ['staff_id' => $userId],
            'offset'     => ($page - 1) * $size,
            'limit'      => $size,
            'order'      => 'id desc',
            'group'   => 'staff_info_id, operate_version',
        ])->toArray();

        if (empty($data)) {
            return $return;
        }
        $addHour         = $this->config->application->add_hour;
        foreach ($data as &$da) {
            $da['created_at'] = date('Y-m-d H:i:s', strtotime($da['created_at']) + $addHour * 3600);
        }
        $return['list']  = $data;
        $return['count'] = $countData;
        return $return;
    }


    //保存生成链接数据
    public function saveFileList($param){
        $data = $param['file_list'];
        $userId = $param['user_info']['id'];
        if(empty($data)){
            throw new ValidationException('need file input');
        }
        $model = new ToolFileUrlModel();
        $code = time() . $userId . rand(1,999999);
        foreach ($data as $da){
            $row['staff_info_id'] = $userId;
            $row['operate_version'] = $code;
            $row['file_name'] = $da['file_name'];
            $row['file_url'] = $da['file_url'];
            $clone = clone $model;
            $clone->create($row);
        }

        return true;
    }


}