<?php
/**
 * Author: Bruce
 * Date  : 2021-12-29 14:45
 * Description:
 */

use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\FlashOss;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\QuestionnaireAnswerModel;
use App\Services\BllService;
use App\Services\ExcelService;
use App\Services\MessageCourierService;
use App\Services\MessagesService;
use App\Services\QuestionnaireLibService;
use App\Library\Enums\MessageEnums;
use App\Models\backyard\MessageModel;
use App\Services\StaffInfoService;
use App\Services\StaffService;

class MessageTask extends BaseTask
{
    const LIMIT_LENGTH = 1000;
    const OLD_DATE = 4;

    /**
     * 定时发送消息 任务 定时发送消息只能指定 整点
     */
    public function set_time_msg_sendAction()
    {
        $message_db = $this->getDI()->get('db_message');
        $backyard_db      = $this->getDI()->get('db_backyard');


        //当前时间  泰国时间 因为 表里定时字段 是泰国时间

        $time       = date('Y-m-d H:i:s');
        $start_time = date('Y-m-d H:i:s', time() - (3 * 24 * 3600));


        //状态为挂起 并且 已经到了 定时发送时间的消息 只取当天的 包括 当天0点 需要发送的
        // 追加条件，且 是审批已通过的, 兼容老数据
        $sql         = " select * from message 
                      where set_time > '{$start_time}' and set_time < '{$time}'
                      and  publish_status = 9 and isdel = 9 and audit_status in (0, 2)";
        $source_data = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($source_data)) {
            echo '暂时数据';
            return false;
        }

        $msgService = new MessagesService();
        $questionnaireLibService = new QuestionnaireLibService();
        $questionnaire_answer = new QuestionnaireAnswerModel();

        foreach ($source_data as $msg_info) {
            // 非数字非逗号的字符，替换为英文逗号, 处理录入时不符遵守规则的数字 [该逻辑在录入环节也需同步加入]
            $msg_info['staff_info_ids'] = preg_replace('/[^\d]/', ',', trim($msg_info['staff_info_ids']));
            if (empty($msg_info['staff_info_ids']))//没有要发送的人 跳过
            {
                continue;
            }

            try {
                $backyard_db->begin();
                $message_db->begin();

                $top               = ($msg_info['top_status'] == 1) ? $msg_info['top_status'] : 0;
                $staff_users       = explode(',', $msg_info['staff_info_ids']);
                $remote_message_id = $msg_info['remote_message_id'];

                $this->echoInfoLog("set_time_msg_send 发送定时消息 remote_message_id {$remote_message_id} ");

                $content = addslashes($msg_info['content']);

                $default_category      = $msg_info['category'];
                $default_category_code = 0;

                // 问卷消息 category 与 code 值
                if ($msg_info['category'] == 6 && $msg_info['questionnaire_lib_id']) {
                    $default_category_code = 3;

                    // 如果过了截止时间，无需发布
                    if (!empty($msg_info['end_time']) && ($msg_info['end_time'] < date('Y-m-d H:i:s', time()))) {
                        throw new ValidationException('过了截止时间，无需发布');
                    }

                    // 生成远端消息内容
                    $content = $msgService->make_qn_msg_remote_tpl($remote_message_id);
                }

                // 答题消息 - 题库
                $lib_content = '';
                if ($msg_info['category'] == 34 && $msg_info['questionnaire_lib_id']) {
                    // 获取题库
                    $lib_param = [
                        'lib_id' => $msg_info['questionnaire_lib_id'],
                    ];

                    $lib_info = $questionnaireLibService->read_lib($lib_param);
                    if ($lib_info['qn_lib_type'] != 3 || $lib_info['is_del']) {
                        throw new ValidationException('答题消息 - 题库 已删除');
                    }

                    $lib_content = $lib_info['question'];

                    // 生成远端消息内容
                    $content = $msgService->make_qn_msg_remote_tpl($remote_message_id);
                }

                $title = addslashes($msg_info['title']);

                $staffs = array_chunk($staff_users, 2000);

                // 普通消息、签字消息 按模板发送 需要传递正确的category_code
                if ($msg_info['send_type'] == 11) {
                    $default_category_code = $msg_info['category_code'];
                }

                //分批插入
                foreach ($staffs as $batchStaffs) {
                    $message_courier_data = '';
                    $questionAnswerData   = '';
                    $insert_staff_questions = [];
                    foreach ($batchStaffs as $staff_id) {
                        $id                   = time() . $staff_id . rand(1000000, 9999999);
                        $message_courier_data .= '(';
                        $message_courier_data .= $id;                         //id
                        $message_courier_data .= ',' . $staff_id;             //工号
                        $message_courier_data .= ',' . "'{$title}'";          //title
                        $message_courier_data .= ',' . $default_category;     //category
                        $message_courier_data .= ',' . $default_category_code;//category_code
                        $message_courier_data .= ',' . $top;                  //top_state
                        $message_courier_data .= ',' . 1;                     //push_state

                        $message_courier_data .= ',' . 1;                 //source_type
                        $message_courier_data .= ',' . $id;               //mns_message_id
                        $message_courier_data .= ',' . $remote_message_id;//message_content_id
                        $message_courier_data .= '),';

                        // 拼接答题消息员工端题库内容
                        if ($msg_info['category'] == 34 && !empty($lib_content)) {
                            $lib_content              = $msgService->shuffleQuestionLibOption($lib_content);
                            $insert_staff_questions[] = [
                                'remote_message_id' => $remote_message_id,
                                'question_content'  => $lib_content,
                                'staff_info_id'     => $staff_id,
                                'create_time'       => date('Y-m-d H:i:s', time()),
                            ];
                        }

                        if ($default_category == 6 && $default_category_code == 3) {//问卷消息
                            $questionAnswerData .= '(';
                            $questionAnswerData .= $remote_message_id;
                            $questionAnswerData .= ',' . $staff_id;
                            $questionAnswerData .= '),';
                        }
                    }

                    if (!empty($insert_staff_questions)) {
                        $questionnaire_answer->batch_insert($insert_staff_questions);
                    }

                    if (!empty($questionAnswerData)) {//问卷消息，发布时创建回答记录
                        $questionAnswerData             = rtrim($questionAnswerData, ',');
                        $backyard_db_questionnaire_answer_sql = "
                            -- 消息分发: insert questionnaire_answer
                            INSERT INTO 
                                questionnaire_answer 
                                (remote_message_id, staff_info_id) 
                            VALUES 
                                $questionAnswerData ;";
                        $result                         = $backyard_db->execute($backyard_db_questionnaire_answer_sql);
                        if (!$result) {
                            throw new \Exception('消息分发,问卷消息, questionnaire_answer 写入失败, insert sql = ' . $backyard_db_questionnaire_answer_sql);
                        }
                    }

                    $message_courier_data              = rtrim($message_courier_data, ',');
                    $message_db_message_courier_sql    = "INSERT INTO message_courier (id,staff_info_id,title,category,category_code,top_state,push_state,source_type,mns_message_id,message_content_id) 
                                                          VALUES {$message_courier_data}";
                    $message_db->execute($message_db_message_courier_sql);
                }

                $messageContent          = new \App\Models\coupon\MessageContentModel();
                $messageContent->id      = $remote_message_id;
                $messageContent->message = $content;

                //定时发送状态更改为 已发送
                $up_sql                = "update message set publish_status = 6 where id = {$msg_info['id']} ";
                $msg_status_update_res = $backyard_db->execute($up_sql);

                if ($messageContent->save() && $msg_status_update_res) {
                    $this->echoInfoLog("set_time_msg_send 发送定时消息：{$remote_message_id} 成功");
                } else {
                    $this->echoErrorLog("set_time_msg_send 发送定时消息：{$remote_message_id} 失败");
                }

                $backyard_db->commit();
                $message_db->commit();
            } catch (\Exception $e) {
                $backyard_db->rollback();
                $message_db->rollback();
                echo $e->getMessage();
                $this->logger->error(['debugInfo'=>[$e->getMessage(),$e->getTraceAsString()]]);

            }
            sleep(10);
        }
        echo 'end';
        return true;
    }

    /**
     * 每天处理指定4个消息
     * @return bool
     */
    public function top4Action()
    {
        $by_db = $this->getDI()->get('db_backyard');
        // message_content & message_courier 表 迁移到 db_message
        $message_db = $this->getDI()->get('db_message');
        $connection = ['db' => $by_db, 'db_message' => $message_db];
        $msgService              = new MessagesService();
        return $msgService->topMessage4($connection);
    }


    /**
     * message 归档任务 默认将31天前，当天的数据进行归档。
     * @param array $params
     */
    public function backup_message_archiveAction($params = [])
    {
        //新增类型 容易疏漏，先关闭
        exit;
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: '.$process_name.' ------ 系统所在国家: '.get_country_code().' ----- '.PHP_EOL;
        $log          .= '开始时间: '.date('Y-m-d H:i:s').PHP_EOL;

        //默认获取 31天前的日期。
        $start_time = $end_time = date('Y-m-d', strtotime('-'.self::OLD_DATE.' day'));
        //处理31天前 指定日期的数据
        if (!empty($params[0])) {
            $start_time = $params[0];
            if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $start_time)) {
                die('wrong parameter :input date style like  Y-m-d');
            }
        }

        if (!empty($params[1])) {
            $end_time = $params[1];
            if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $end_time)) {
                echo 'wrong parameter :input date style like  Y-m-d';
                exit;
            }
        }

        $_start_time = strtotime($start_time);//开始时间
        $_end_time   = strtotime($end_time);  //结束时间

        $len = strtotime(date('Y-m-d')) - $_end_time;
        if ($len / 86400 < self::OLD_DATE) {//判断是否是，31天前
            echo 'end time too early';
            exit;
        }
        while ($_start_time <= $_end_time) {
            $today = date('Y-m-d', $_start_time);
            try {
                $this->fix_message_data($today);
                $_start_time = strtotime('+1 day', $_start_time);
                $log         .= '【FIX-DATA-SUCCESS】:'.$today.PHP_EOL;
                echo '【FIX-'.$today.':SUCCESS】'.PHP_EOL;
            } catch (Exception $e) {
                echo '【FIX-'.$today.':FAIL】'.PHP_EOL;
                $this->echoErrorLog($e->getMessage());
                $log .= '【FIX-DATA-FAIL】:'.$today.PHP_EOL;
                $log .= $e->getMessage().PHP_EOL;
                break;
            }
        }

        $log .= "结束时间: ".date('Y-m-d H:i:s').PHP_EOL;
        $this->getDI()->get('logger')->info("message_archive".$log);
        exit($log);
    }

    /**
     * 获取message信息
     * @param $source_db
     * @param $start_time
     * @param $end_time
     * @param $table
     * @return mixed
     */
    public function getMessageInfo($source_db, $start_time, $end_time, $table)
    {
        $length   = self::LIMIT_LENGTH;
        $data_sql = "select * from {$table} where category not in (0, 6, 7, 33, 34, 36,144) and created_at >= '{$start_time}' and created_at <= '{$end_time}' limit {$length} ";
        return $source_db->query($data_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    /**
     * 处理数据，将message数据插入至归档表，并删除源数据
     * @param $date
     */
    public function fix_message_data($date)
    {
        //bi 主库
        $source_db = $this->getDI()->get('db_backyard');
        //备份库
        $backup_db = $this->getDI()->get('db_backup');
        //数据源 bi 库 message表
        $m_source_name = 'message';

        $year  = date('Y', strtotime($date));
        $month = date('m', strtotime($date));

        $add_hour = $this->config->application->add_hour;
        //根据日期 判断当前创建时间区间
        $start_time = date('Y-m-d H:i:s', strtotime("{$date} - $add_hour Hours"));
        $end_time   = date('Y-m-d H:i:s', strtotime("{$start_time} + 24 Hours"));


        //即将创建的表名
        $m_table = 'message_'.$year;

        try {
            //检验表是否存在 创建
            $exist = "show tables like '{$m_table}'";
            $info  = $backup_db->query($exist)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "CREATE TABLE {$m_table} LIKE {$m_source_name}";
                if ($backup_db->execute($create_table) === false) {
                    throw new Exception("message backup create table {$m_table} fail！");
                }
            }
        } catch (Exception $e) {
            $this->echoErrorLog("message backup create table Exception:".$e->getMessage());
            return;
        }

        $model = new BaseModel();
        //取出一天数据 再分批插入
        while ($m_source_data = $this->getMessageInfo($source_db, $start_time, $end_time, $m_source_name)) {
            try {
                $source_db->begin();
                $backup_db->begin();

                $flag = $model->table_batch_insert($m_source_data, 'db_backup', $m_table);
                if ($flag === false) {
                    throw new Exception("message backup the data from bi {$m_source_name} to backup {$m_table} fail ！");
                }
                $ids = [];
                foreach ($m_source_data as $datum) {
                    $ids[] = $datum['id'];
                }
                $ids_str          = implode(",", $ids);
                $del_m_source_sql = "delete from {$m_source_name} where id in ({$ids_str}) ";
                if ($source_db->execute($del_m_source_sql) === false) {
                    throw new Exception("message backup delete {$m_source_name} data fail ！");
                }

                $source_db->commit();
                $backup_db->commit();
            } catch (Exception $e) {
                $source_db->rollback();
                $backup_db->rollback();
                $this->echoErrorLog("message backup insert data Exception:".$e->getMessage());
            }
        }
    }


    public function backupAction()
    {
        try {
            $sql         = "select * from message_courier limit 1";
            $source_data = $this->getDI()->get('db_backup')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            var_dump($source_data);
        } catch (Exception $exception) {
            var_dump($exception->getMessage());
        }
    }

    /**
     * 处理历史数据
     * @param $params
     */
    public function backup_oldAction($params)
    {
        if (empty($params[0])) {
            echo 'need start time';
            exit;
        }
        $start_time = $params[0];
        if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $start_time)) {
            die('wrong parameter :input date style like  Y-m-d');
        }
        if (empty($params[1])) {
            echo 'need end time';
            exit;
        }
        $end_time = $params[1];
        if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $end_time)) {
            echo 'wrong parameter :input date style like  Y-m-d';
            exit;
        }
        if (empty($params[2])) {
            echo 'need type';
            exit;
        }
        $type = $params[2];

        $_start_time = strtotime($start_time);
        $_end_time   = strtotime($end_time);
        $len         = strtotime(date('Y-m-d')) - $_end_time;
        if ($len / 86400 < 30) {
            echo 'end time too early';
            exit;
        }

        while ($_start_time <= $_end_time) {
            $today = date('Y-m-d', $_start_time);
            try {
                $this->backup_message_courier_and_contentAction([0 => $type ,1=>$today]);
                $_start_time = strtotime('+1 day', $_start_time);
            } catch (Exception $e) {
                $this->echoLog($e->getMessage(), 'error');
            }
        }
    }

    /**
     * 同时备份 courier 和 content 表的数据
     * @param $type
     * @param $input_date_time
     */
    public function backup_message_courier_and_contentAction($params)
    {

        if(!isset($params[0])){
            exit('输入类型');
        }

        if (isset($params[1])) {
            $input_date_time = $params[1];
            $this->echoLog('message backup start date:'.$input_date_time);
        }


        //hr相关消息
        if($params[0] == 1){
            $_where = "  and category not in (7,33,145) and read_state = 1";
            $date  = date('Y-m-d', strtotime("-90 day"));
            $month = date('m', strtotime("-90 day"));
            $year  = date('Y', strtotime("-90 day"));
            $add_hour = $this->config->application->add_hour;
            //根据日期 判断当前创建时间区间
            $start_time = date('Y-m-d H:i:s', strtotime("{$date} - $add_hour Hours"));
            $end_time   = date('Y-m-d H:i:s', strtotime("{$start_time} + 24 Hours"));

        }elseif($params[0] == 2){
            $_where = "  and (category  in (1,2,3,4,10,132) or is_del = 1)";
            $date  = date('Y-m-d', strtotime("-20 day"));
            $year  = date('Y', strtotime("-20 day"));
            $add_hour = $this->config->application->add_hour;
            //根据日期 判断当前创建时间区间
            $start_time = date('Y-m-d H:i:s', strtotime("{$date} - $add_hour Hours"));
            $end_time   = date('Y-m-d H:i:s', strtotime("{$start_time} + 24 Hours"));

        } elseif ($params[0] == 3) {
            $_where = "";//全部消息 最多保留1年
            $date   = date('Y-m-d', strtotime("-1 year"));
            $year   = date('Y', strtotime("-1 year"));
            $add_hour = $this->config->application->add_hour;
            //根据日期 判断当前创建时间区间
            $start_time     = '2019-01-01 00:00:00';
            $tmp_start_time = date('Y-m-d H:i:s', strtotime("{$date} - $add_hour Hours"));
            $end_time       = date('Y-m-d H:i:s', strtotime("{$tmp_start_time} + 24 Hours"));
        } else {
           throw new ValidationException('need start time and end time');
        }



        if(!empty($input_date_time)){
            if (!preg_match("/\d{4}\-\d{2}\-\d{2}/", $input_date_time)) {
                die('wrong parameter :input date style like  Y-m-d');
            }
            $date  = $input_date_time;
            $year  = date('Y', strtotime("{$date}"));
            $add_hour = $this->config->application->add_hour;
            //根据日期 判断当前创建时间区间
            $start_time = date('Y-m-d H:i:s', strtotime("{$date} - $add_hour Hours"));
            $end_time   = date('Y-m-d H:i:s', strtotime("{$start_time} + 24 Hours"));
        }



        //源数据库
        $source_db = $this->getDI()->get('db_message');
        //备份库

        $backup_db    = $this->getDI()->get('db_backup');
        $back_db_name = 'db_backup';
        //数据源
        $m_source_name = 'message_courier';
        $c_source_name = 'message_content';


        $m_table = 'message_courier_'.$year;
        $c_table = 'message_content_'.$year;
        //根据日期 判断当前创建时间区间



        try {
            //检验表是否存在 创建
            $exist = "show tables like '{$m_table}'";
            $info  = $backup_db->query($exist)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "CREATE TABLE {$m_table} LIKE {$m_source_name}";
                if ($backup_db->execute($create_table) === false) {
                    throw new Exception("message backup create table {$m_table} fail！");
                }
            }
            //检验表是否存在 创建
            $exist = "show tables like '{$c_table}'";
            $info  = $backup_db->query($exist)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "CREATE TABLE {$c_table} LIKE {$c_source_name}";
                if ($backup_db->execute($create_table) === false) {
                    throw new Exception("message backup create table {$c_table} fail！");
                }
            }
        } catch (\Exception $e) {
            $this->echoLog("message backup create table Exception:".$e->getMessage());
            return;
        }


        $model = new BaseModel();
        //每次 500条 插入
        $length = 500;
        while (true) {
            try {
                //一次性取出一天数据 再分批插入
                $data_sql = "select * from {$m_source_name} where created_at >= '{$start_time}' and created_at <= '{$end_time}' {$_where} limit {$length} ";
                $m_source_data = $source_db->query($data_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (empty($m_source_data)) {
                    $this->echoLog("没有数据了");
                    break;
                }

                $backup_db->begin();
                $source_db->begin();
                //插入到备份库
                $flag = $model->table_batch_insert($m_source_data, $back_db_name, $m_table,true);
                if ($flag === false) {
                    throw new Exception("message backup the data from {$m_source_name} to  {$m_table} fail ！");
                }
                $message_content_ids = $ids = [];
                foreach ($m_source_data as $datum) {
                    $ids[]                 = $datum['id'];
                    $message_content_ids[] = $datum['message_content_id'];
                }
                $ids_str          = "'".implode("','", $ids)."'";
                $del_m_source_sql = "delete from {$m_source_name} where id in ({$ids_str}) ";
                if ($source_db->execute($del_m_source_sql) === false) {
                    throw new Exception("message backup delete {$m_source_name} data fail ！");
                }
                //之所以不用时间区间取数据，是因为有点分类的消息要一致展示。
                $contend_ids  = "'".implode("','", $message_content_ids)."'";
                $data_sql     = "select * from {$c_source_name} where  id in ($contend_ids)";
                $content_data = $source_db->query($data_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($content_data)) {
                    $flag = $model->table_batch_insert($content_data, $back_db_name, $c_table,true);
                    if ($flag === false) {
                        throw new Exception("message backup the data from {$c_source_name} to {$c_table} fail！");
                    }

                    $del_content_source_sql = "delete from {$c_source_name} where id in ({$contend_ids}) ";
                    if ($source_db->execute($del_content_source_sql) === false) {
                        throw new Exception("message backup delete {$c_source_name} data fail ！");
                    }
                }
                $backup_db->commit();
                $source_db->commit();
            } catch (\Exception $e) {
                $this->logger->error($e->getMessage());
                $backup_db->rollback();
                $source_db->rollback();
                break;
            }
        }
        $this->echoLog('message backup end !');
    }

    public function echoLog($content, $level = 'info')
    {
        $logger = $this->getDI()->get('logger');
        echo $content.PHP_EOL;
        $logger->write_log("handle message :".$content, $level);
    }

    public function backup_leave_staff_message($params)
    {
        $input_date_time = $params['date_at'];
        $staff_info_ids = $params['staff_info_ids'];
        //源数据库
        $source_db = $this->getDI()->get('db_message');
        //备份库

        $backup_db    = $this->getDI()->get('db_backup');
        $back_db_name = 'db_backup';
        //数据源
        $m_source_name = 'message_courier';
        $c_source_name = 'message_content';

        $date  = $input_date_time;
        $year  = date('Y', strtotime("{$date}"));
        $month = date('m', strtotime("{$date}"));

        $m_table = 'message_courier_'.$year;
        $c_table = 'message_content_'.$year;

        $add_hour = $this->config->application->add_hour;
        //根据日期 判断当前创建时间区间
        $start_time = date('Y-m-d H:i:s', strtotime("{$date} - $add_hour Hours"));
        $end_time   = date('Y-m-d H:i:s', strtotime("{$start_time} + 24 Hours"));

        try {
            //检验表是否存在 创建
            $exist = "show tables like '{$m_table}'";
            $info  = $backup_db->query($exist)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "CREATE TABLE {$m_table} LIKE {$m_source_name}";
                if ($backup_db->execute($create_table) === false) {
                    throw new Exception("message backup create table {$m_table} fail！");
                }
            }
            //检验表是否存在 创建
            $exist = "show tables like '{$c_table}'";
            $info  = $backup_db->query($exist)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (empty($info)) {
                $create_table = "CREATE TABLE {$c_table} LIKE {$c_source_name}";
                if ($backup_db->execute($create_table) === false) {
                    throw new Exception("message backup create table {$c_table} fail！");
                }
            }
        } catch (\Exception $e) {
            $this->echoLog("message backup create table Exception:".$e->getMessage());
            return;
        }


        $model = new BaseModel();
        //每次 500条 插入
        $length = 500;
        $is_has_data = false;
        while (true) {
            try {
                //一次性取出一天数据 再分批插入
                $data_sql = "select * from {$m_source_name} where staff_info_id in ($staff_info_ids) and category not in (0,6,33,34) and  created_at >= '{$start_time}' and created_at < '{$end_time}'  limit {$length} ";

                $m_source_data = $source_db->query($data_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (empty($m_source_data)) {
                    break;
                }
                $is_has_data = true;
                $backup_db->begin();
                $source_db->begin();
                //插入到备份库
                $flag = $model->table_batch_insert($m_source_data, $back_db_name, $m_table,true);
                if ($flag === false) {
                    throw new Exception("message backup the data from {$m_source_name} to  {$m_table} fail ！");
                }
                $message_content_ids = $ids = [];
                foreach ($m_source_data as $datum) {
                    $ids[]                 = $datum['id'];
                    $message_content_ids[] = $datum['message_content_id'];
                }
                $ids_str          = "'".implode("','", $ids)."'";
                $del_m_source_sql = "delete from {$m_source_name} where id in ({$ids_str}) ";
                if ($source_db->execute($del_m_source_sql) === false) {
                    throw new Exception("message backup delete {$m_source_name} data fail ！");
                }

                //之所以不用时间区间取数据，是因为有点分类的消息要一致展示。
                $contend_ids  = "'".implode("','", $message_content_ids)."'";


                $del_content_source_sql = "delete from {$c_table} where id in ({$contend_ids}) ";
                if ($backup_db->execute($del_content_source_sql) === false) {
                    throw new Exception("message backup delete {$c_table} data fail ！");
                }

                $data_sql     = "select * from {$c_source_name} where  id in ($contend_ids)";
                $content_data = $source_db->query($data_sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                if (!empty($content_data)) {
                    $flag = $model->table_batch_insert($content_data, $back_db_name, $c_table,true);
                    if ($flag === false) {
                        throw new Exception("message backup the data from {$c_source_name} to {$c_table} fail！");
                    }

                    $del_content_source_sql = "delete from {$c_source_name} where id in ({$contend_ids}) ";
                    if ($source_db->execute($del_content_source_sql) === false) {
                        throw new Exception("message backup delete {$c_source_name} data fail ！");
                    }
                }

                $backup_db->commit();
                $source_db->commit();
                echo $start_time.'  '.$end_time.'success'.PHP_EOL;
            } catch (\Exception $e) {
                $this->echoLog("message backup Exception:".$e->getMessage());
                $backup_db->rollback();
                $source_db->rollback();
                break;
            }
        }
        $this->echoLog( $staff_info_ids . ' ' .$input_date_time . ' is_has_data '. $is_has_data. ' message backup end !');
    }

    /**
     * 处理历史数据
     * @param $params
     */
    public function backup_leave_staff_message_courier_and_contentAction($params)
    {
        $leave_end_at   = date('Y-m-d 00:00:00', strtotime("-60 day"));
        $leave_start_at = empty($input_date_time)? date('Y-m-d 00:00:00', strtotime("-65 day")) : $input_date_time.' 00:00:00';

        if(isset($params[0])){
            $leave_start_at = $params[0].' 00:00:00';
        }
        if(isset($params[1])){
            $leave_end_at = $params[1].' 00:00:00';
        }

        $staff_info = HrStaffInfoModel::find([
            'conditions' => 'state = 2 and leave_date >= :leave_start_at: and leave_date <= :leave_end_at:  ',
            'bind'       => ['leave_start_at' => $leave_start_at, 'leave_end_at' => $leave_end_at],
            'columns'    => 'staff_info_id , hire_date',
        ])->toArray();
        if (empty($staff_info)) {
            return $this->echoLog('无离职员工');
        }

        $_end_time = strtotime($leave_end_at);

        foreach ($staff_info as $item) {
            $_start_time = max(strtotime($item['hire_date']),strtotime('-1 years'));
            while ($_start_time <= $_end_time) {
                $today = date('Y-m-d', $_start_time);
                try {
                    $this->backup_leave_staff_message(['date_at'        => $today,
                                                       'staff_info_ids' => $item['staff_info_id'],
                    ]);
                    $_start_time = strtotime('+1 day', $_start_time);
                } catch (Exception $e) {
                    $this->echoLog($e->getMessage(), 'error');
                    break;
                }
            }
        }

        echo 'end';
    }

    /**
     * 格式化参数，按组织，按部门
     * @param $params
     */
    public function format_create_paramsAction($params)
    {
        if (empty($params[0])) {
            echo 'need msg id';
            exit;
        }

        $msg_ids = explode(',', $params[0]);

        $msgInfo    = \App\Models\backyard\MessageModel::find([
            'conditions' => ' id in ({ids:array})',
            'bind'       => ['ids' => $msg_ids],
            'columns'    => 'id, send_type, to_group',
        ])->toArray();
        $relateInfo = (new MessagesService())->getDepartmentJobRelateAll();

        //部门
        $sysDepartment = \App\Models\backyard\SysDepartmentModel::find([
            'columns' => ['id', 'name'],
        ])->toArray();

        //大区
        $sysManageRegion = \App\Models\backyard\SysManageRegionModel::find([
            'columns' => ['id', 'name'],
        ])->toArray();

        //片区
        $sysManagePiece = \App\Models\backyard\SysManagePieceModel::find([
            'columns' => ['id', 'name'],
        ])->toArray();

        //网点
        $sysStore = \App\Models\backyard\SysStoreModel::find([
            'columns' => ['id', 'name'],
        ])->toArray();

        $sysDepartmentToId   = array_column($sysDepartment, 'id', 'name');
        $sysManageRegionToId = array_column($sysManageRegion, 'id', 'name');
        $sysManagePieceToId  = array_column($sysManagePiece, 'id', 'name');
        $sysStoreToId        = array_column($sysStore, 'id', 'name');

        $db = $this->getDI()->get('db_backyard');

        foreach ($msgInfo as $oneMsg) {
            if (!in_array($oneMsg['send_type'], [MessageEnums::SEND_TYPE_JOB, MessageEnums::SEND_TYPE_ORG])) {
                continue;
            }
            $create_params = [];
            //按职位
            if ($oneMsg['send_type'] == MessageEnums::SEND_TYPE_JOB) {
                $jobs          = explode(',', $oneMsg['to_group']);
                $create_params = [];
                foreach ($jobs as $oneJob) {
                    if (!isset($relateInfo[$oneJob])) {
                        continue;
                    }
                    $create_params[] = $relateInfo[$oneJob];
                }
            }

            //按组织
            if ($oneMsg['send_type'] == MessageEnums::SEND_TYPE_ORG) {
                $orgInfoArr = explode(';', $oneMsg['to_group']);
                $orgInfo    = explode(',', $orgInfoArr[0]);;
                $create_params['select_organizations'] = [];
                foreach ($orgInfo as $oneOrg) {
                    $orgData = [];
                    if (isset($sysDepartmentToId[$oneOrg])) {
                        $orgData['organization_id'] = $sysDepartmentToId[$oneOrg];
                        $orgData['name'] = $oneOrg;
                        $orgData['current_type'] = 'department';
                        $orgData['isSelect'] = true;
                        $create_params['select_organizations'][] = $orgData;
                        continue;
                    }

                    if (isset($sysManageRegionToId[$oneOrg])) {
                        $orgData['organization_id'] = $sysManageRegionToId[$oneOrg];
                        $orgData['name'] = $oneOrg;
                        $orgData['current_type'] = 'region';
                        $orgData['isSelect'] = true;
                        $create_params['select_organizations'][] = $orgData;
                        continue;
                    }

                    if (isset($sysManagePieceToId[$oneOrg])) {
                        $orgData['organization_id'] = $sysManagePieceToId[$oneOrg];
                        $orgData['name'] = $oneOrg;
                        $orgData['current_type'] = 'piece';
                        $orgData['isSelect'] = true;
                        $create_params['select_organizations'][] = $orgData;
                        continue;
                    }

                    if (isset($sysStoreToId[$oneOrg])) {
                        $orgData['organization_id'] = $sysStoreToId[$oneOrg];
                        $orgData['name'] = $oneOrg;
                        $orgData['current_type'] = 'store';
                        $orgData['isSelect'] = true;
                        $create_params['select_organizations'][] = $orgData;
                        continue;
                    }
                }
            }
            $update_data['create_params'] = json_encode($create_params);
            $db->updateAsDict('message', $update_data, ["conditions" => "id = ?", 'bind' => $oneMsg['id']]);
            $log = '参数格式化成功msg:' . $oneMsg['id'] . 'data:' . $update_data['create_params'];
            $this->echoLog($log);
        }
    }

    /**
     * 签字消息下载
     * @param $input
     * @return true|null
     * @throws Exception
     */
    public function sign_downloadAction($input)
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);
        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;
        $params         = json_decode($input[0], true);

        BaseService::setLanguage($params['lang']);
        $messageService   =  reBuildCountryInstance(new MessagesService());
        $messageInfo      = $messageService->getMessage(intval($params['msg_id']));
        $staffIds         = explode(',', $messageInfo['staff_info_ids']);
        $staffChuck       = array_chunk($staffIds, 1000);
        $userInfo         = (new StaffService())->get_fbi_user_info($params['staff_id']);
        $t                = BaseService::getTranslation($params['lang']);
        $staffInfoService = new StaffInfoService();
        $allData          = [];
        foreach ($staffChuck as $staffIds) {

            $staffsInfo = $staffInfoService->getStaffList(array_values($staffIds));
            $staffsInfo = array_column($staffsInfo,null,'staff_info_id');
            foreach ($staffIds as $staff_id) {
                $where['staff_info_id']     = $staff_id;
                $where['remote_message_id'] = $messageInfo['remote_message_id'];
                $where['user_info']         = $userInfo;
                $result                     = $messageService->makeSignMsgPdf($where);
                $ondData                    = [];
                $ondData[]                  = $staff_id;
                $ondData[]                  = $staffsInfo[$staff_id]['name'];
                $ondData[]                  = $staffsInfo[$staff_id]['department_name'];
                $ondData[]                  = $staffsInfo[$staff_id]['job_name'];
                if (isset($result['code']) && $result['code'] == ErrCode::SUCCESS) {
                    $ondData[] = $result['data']['file_url'];
                    $ondData[] = $t['signed'];
                } else {
                    $ondData[] = '';
                    $ondData[] = $t['unsigned'];
                }
                $allData[] = $ondData;
            }
        }

        $title = [
            $t['staff_info_id'],                      //(工号)
            $t['name'],                               //姓名
            $t['department'],                         //部门
            $t['staff_job_title'],                    //职位
            $t['pdf_url'],                            //PDF Url
            $t['status'],                             //状态
        ];

        $updatePath = 'message-download/'.date('Y-m-d').'/';
        $result     = (new BllService())->exportExcels($title, $allData, $updatePath, $file_name);
        return  (new ExcelService())->updateTask($task_id, $file_name, $result['url']);
    }

    public function fixOldMessageAction($params)
    {
        $params['msg_id'] = $params[0];
        if (empty($params['msg_id'])) {
            echo "请输入 message 消息主键 id";return;
        }
        $messageService   =  reBuildCountryInstance(new MessagesService());
        $messageInfo      = $messageService->getMessage(intval($params['msg_id']));
        $staffIds         = array_reverse(explode(',', $messageInfo['staff_info_ids']));
        $staffChuck       = array_chunk($staffIds, 1000);
        foreach ($staffChuck as $staffIds) {
            foreach ($staffIds as $staff_id) {
                $where['staff_info_id']     = $staff_id;
                $where['remote_message_id'] = $messageInfo['remote_message_id'];
                $messageService->makeMessageSignPdfPushRedis(['locale' => getCountryDefaultLang()], $where);
            }
        }
    }

    /**
     * 指定签字消息id 指定工号 生成 pdf， 导出 excel
     * @param $params
     * @throws Exception
     */
    public function sign_download_by_idAction($params)
    {
        if(empty($params[0]) || empty($params[1])) {
            echo "请输入 message 消息主键 id,  工号";
        }

        $messageIds = explode(',', $params[0]);
        $staffInfoIds = explode(',', $params[1]);


        BaseService::setLanguage('en');
        $t                = BaseService::getTranslation('en');

        $messageService   =  reBuildCountryInstance(new MessagesService());
        $staffInfoService = new StaffInfoService();
        $messageService->makeSignPdfPermission = false;

        $allData = [];
        foreach ($messageIds as $oneMessageId) {
            $messageInfo      = $messageService->getMessage(intval($oneMessageId));
            $staffsInfo = $staffInfoService->getStaffList(array_values($staffInfoIds));
            $staffsInfo = array_column($staffsInfo,null,'staff_info_id');

            $sendTime  = show_time_zone($messageInfo['real_send_time']);

            foreach ($staffInfoIds as $staff_id) {
                $where['staff_info_id']     = $staff_id;
                $where['remote_message_id'] = $messageInfo['remote_message_id'];
                $result                     = $messageService->makeSignMsgPdf($where);
                $ondData                    = [];
                $ondData[]                  = $messageInfo['id'];
                $ondData[]                  = $messageInfo['title'];
                $ondData[]                  = $staff_id;
                $ondData[]                  = $staffsInfo[$staff_id]['name'];
                $ondData[]                  = $staffsInfo[$staff_id]['department_name'];
                $ondData[]                  = $staffsInfo[$staff_id]['job_name'];
                $ondData[]                  = $staffsInfo[$staff_id]['hire_date'];
                $ondData[]                  = $staffsInfo[$staff_id]['leave_date'];
                $ondData[]                  = $sendTime;
                if (isset($result['code']) && $result['code'] == ErrCode::SUCCESS) {
                    $ondData[] = $result['data']['file_url'];
                    $ondData[] = $t['signed'];
                } else {
                    $ondData[] = '';
                    $ondData[] = $t['unsigned'];
                }
                $allData[] = $ondData;

            }
        }

        $title = [
            'Message ID',                            //消息标题id
            $t['msg_title_01'],                      //消息标题
            $t['staff_info_id'],                      //(工号)
            $t['name'],                               //姓名
            $t['department'],                         //部门
            $t['staff_job_title'],                    //职位
            $t['hire_date'],                          //入职日期
            $t['leave_date'],                         //离职日期
            $t['warning_time'],                       //发送时间
            $t['pdf_url'],                            //PDF Url
            $t['status'],                             //状态
        ];

        $updatePath = 'message-download/'.date('Y-m-d').'/';
        $result     = (new BllService())->exportExcels($title, $allData, $updatePath, uniqid('sign_message_pdf_') . '.xlsx');

        echo $result['request_uri'];
        return true;
    }

    /**
     * * 消费 签字消息，生成pdf
     */
    public function consumer_make_sign_pdfAction()
    {
        BaseService::setLanguage(getCountryDefaultLang());
        reBuildCountryInstance(new MessagesService())->consumerMakeSignPdf();
    }

    //指定签字消息，导出签字
    public function export_signAction()
    {
        $data[0] = '1659608,1674550,1909747,2309304,2875192,2896850,2905537,2913974,2932498,2940697,2949877,2959587,2969927,2979197,2998059,3007442,3017799,3027209,3036377,3045335,3061534,3070063,3078032,3086841,3094969,3103102,3118443,3126842,3134365,3142150,3150282,3157248,3171314,3178642,3185521,3191638,3197689,3204279,3217626,3224523,3230524,3237537,3244273,3250774,3263151,3270072,3277634,3284915,3292676,3299626,3312495,3312496,3319039,3319044,3331407,3331409,3349363,3349365,3356982,3357016';
        $data[1] = '193016,193288,193620,193687,193779,193791,193955,194035,194045,194147,194258,194464,194596,194674,194802,194967,194999,195189,195346,195452,195505,195516,195597,195689,195789,195850,195873,195925,195959,195980,195981,195994,196296,196335,196385,196422,196498,196566,196632,196650,196654,196697,196716,196770,197067,197109,197156,197239,197287,197313,197328,197343,197390,197492,197547,197617,197893,197950,198027,198045,198057,198078,198124,198156,198273,198305,198362,198391,198497,198841,198935,198936,198994,199005,199026,199091,199111,199160,199354,199393,199428,199471,199472,199481,199504,199536,199541,199547,199583,199630,199653,199657,199819,199890,199919,199991,199993,200082,200107,200115,200124,200261,200268,200323,200375,200377,200403,200441,200610,200677,200758,200806,200876,200889,201156,201160,201222,201291,201294,201326,201346,201462,201517,201524,201608,201726,201881,201895,201949,201952,202074,202159,202174,202314,202416,202495,202526,202627,202715,202766,202857,202962,202987,203230,203276,203303,203392,203442,203485,203595,203665,203679,203721,203740,203824,203856,203868,203881,203949,203976,204050,204068,204138,204347,204394,204405,204517,204599,204614,204920,205013,205035,205471,205633,205649,205658,205756,205796,205809,205834,205837,206036,206037,206079,206196,206216,206300,206321,206375,206378,206455,206475,206547,206664,206668,206834,206840,206875,207067,207164,207195,207337,207408,207445,207637,207700,207803,207900,207904,208008,208110,208119,208166,208219,208390,208513,208538,208553,208557,208573,208726,208812,208873,208979,209093,209110,209236,209359,209466,209527,209552,209819,209922,210077,210181,210204,210300,210322,210362,210416,210483,210498,210513,210620,210744,210792,210875,211160,211180,211301,211340,211414,211488,211592,211637,211641,211734,213341,213363,213418,213434,213436,213490,213614,213690,213797,213860,213982,214057,214078,214250,214267,214361,214596,214674,214763,214804,215323,215326,215353,215622,215824,215826,215852,216049,216122,216280,216334,216845,216915,216932,216941,217143,217300,217386,217431,217555,217642,217734,217840,217846,218243,218411,218622,218824,219009,219173,219232,219348,219406,219429,219498,219961,219964,220010,220051,220074,220848,220897,220995,221023,221401,221404,221454,221537,221620,221717,222159,222169,222171,222176,222299,222324,222378,222519,222545,222698,223194,223213,223460,223465,223538,223612,223667,223825,224104,224210,224288,224322,224519,224687,224778,224795,224832,225384,225459,225460,225663,226050,226447,226525,226562,226647,226699,226752,227320,227363,227432,227471,227637,227722,227794,227908,228792,229285,230136,230521,230868,231037,231376,231514,231655,231709,231740,232765,234665,2000127,2000157,2000177,2000200,2000255,2000325,2000343,2000509';

        $this->sign_download_by_idAction($data);
    }


    public function oldPdfAction($params)
    {
        $end = date('Y-m-d 00:00:00',strtotime($params[0]));
        $start = date('Y-m-d 00:00:00',strtotime($end . ' -1 month'));
        $server = new MessagesService();
        $locale['local'] = getCountryDefaultLang();

        $sql = " select r.staff_info_id ,r.remote_message_id   from `sign_msg_result` as r inner join message as m on r.remote_message_id = m.remote_message_id  WHERE r.created_at  <= '$end' and r.created_at  >= '$start' and m.category = 33 and m.category_code in (0,4)  and r.sign_url != '' and r.sign_pdf_url = ''";

        $list =  $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        foreach ($list as $item) {
            $server->makeMessageSignPdfPushRedis($locale, $item);
        }


    }

    /**
     * 20450【TH】虚假类处罚站内信逻辑
     * 处罚警告消息
     * @return void
     * @throws Exception
     */
    public function penaltyWarningMessageAction($params)
    {
        $service = new MessageCourierService();
        $date_at = $params[0] ?? date('Y-m-d', strtotime('-1 day'));
        $service->penaltyWarningMessage($date_at);
    }




}