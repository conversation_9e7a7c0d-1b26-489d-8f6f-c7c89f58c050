<?php

use App\Library\BaseService;
use App\Models\backyard\AsyncImportTaskModel;
use App\Services\AsyncImportTaskService;
use App\Services\SuspensionService;

class StaffSuspensionTask extends BaseTask
{
    /**
     *
     * @Single
     * php app/cli.php StaffSuspension awol_send_email
     * 每日AWOL发送情况统计 PH 02:00 发送前一天数据 PH
     * @throws Exception
     */
    public function awol_send_emailAction() {
        echo 'begin' . PHP_EOL;
        $current_date = date('Y-m-d');
        echo '当前日期:' . $current_date . PHP_EOL;
        BaseService::setLanguage('en');
        $begin_date = gmdate('Y-m-d H:i:s',strtotime($current_date . '-1 days'));
        $end_date = gmdate('Y-m-d H:i:s', strtotime($current_date));
        $r = reBuildCountryInstance(new SuspensionService())->awolStatisticsSendEmail(['begin_date' => $begin_date, 'end_date' => $end_date]);
        echo '发送结果:' . json_encode($r) . PHP_EOL;
        echo 'end' . PHP_EOL;
    }

    /**
     * 导入停职申请
     * @throws Exception
     */
    public function import_auditAction()
    {
        $task = (new AsyncImportTaskService())->getTask(AsyncImportTaskModel::SUSPENSION_AUDIT);
        if (empty($task)) {
            echo 'not task ' . PHP_EOL;
            return;
        }
        $args        = $task->args_json ? json_decode($task->args_json, true) : [];
        $fileUrl     = $task->import_path;         // 文件的远程URL
        $tmpDir      = sys_get_temp_dir();         // 获取系统的临时目录路径
        $fileName    = basename($fileUrl);         // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;  // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            $task->status = AsyncImportTaskModel::STATE_EXECUTED;
            $task->save();
            $this->echoErrorLog(__METHOD__ . '读取文件失败 task id:' . $task->id);
        }

        BaseService::setLanguage($args['lang'] ?? 'en');
        $server         = new SuspensionService();

        $result         = $server->dealImportData($tmpFilePath, $task->operator_id, $task->result_file_name, $task->id);
        if (empty($result['url'])) {
            $this->echoErrorLog(__METHOD__ . 'import error' . $task->id);
        }
        echo '上传结果：' . json_encode($result, 256) . PHP_EOL;
        $task->result_path    = $result['url'];
        $task->fail_number    = $result['fail_number'] ?? 0;
        $task->success_number = $result['success_number'] ?? 0;
        $task->status         = AsyncImportTaskModel::STATE_EXECUTED;
        $task->save();
        @unlink($tmpFilePath);
    }

    /**
     * 凌晨
     * 待离职->离职前执行
     * @Single
     * @param $param
     */
    public function executeAction($param)
    {
        $date = !empty($param[0]) ? $param[0] : date("Y-m-d");
        $staff_id = !empty($param[1]) ? $param[1] : 0;
        echo 'begin' . PHP_EOL;
        echo '当前日期:' . $date . PHP_EOL;
        BaseService::setLanguage('en');
        (new SuspensionService())->executeStaff(['date' => $date, 'staff_id' => $staff_id]);
        echo 'end' . PHP_EOL;
    }

    /**
     * @Single
     * 将发送停职失败的 发送给ER
     */
    public function sendRemindAction($param)
    {
        $date = !empty($param[0]) ? $param[0] : date("Y-m-d");
        echo 'begin' . PHP_EOL;
        echo '当前日期:' . $date . PHP_EOL;
        BaseService::setLanguage('en');
        (new SuspensionService())->sendRemind($date);
        echo 'end' . PHP_EOL;
    }
}