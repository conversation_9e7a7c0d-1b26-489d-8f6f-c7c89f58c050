<?php

use App\Library\BaseService;
use App\Library\FlashOss;
use App\Models\backyard\AsyncImportTaskModel;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\AsyncImportTaskService;
use App\Services\ExcelService;
use App\Services\VehicleInspectionService;

/**
 * Author: Bruce
 * Date  : 2025-04-28 16:23
 * Description:
 */

class VehicleInspectionTask extends BaseTask
{

    /**
     * 快递员车辆稽查-导出
     * @param $input
     * @throws \OSS\Core\OssException
     */
    public function courier_exportAction($input)
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('VehicleInspectionTask-courierExport start', true);

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog('VehicleInspectionTask-courierExport params :'.json_encode($params, JSON_UNESCAPED_UNICODE), true);

        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }

        $vehicleInspectionService = new VehicleInspectionService();
        $file_data = $vehicleInspectionService->exportTask($params, $excelTask->file_name);

        $flashOss  = new FlashOss();
        $ossObject = 'vehicle_inspection_courier/'.date('Ymd').'/'.$excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        if (!empty($params['From']) && in_array($params['From'], ['fbi'])) {
            $flashOss          = new FlashOss();
            $url               = $flashOss->signUrl($ossObject, 20 * 24 * 60 * 60);
            $dowLoadTaskServer = new \App\Services\DownLoadTaskService();
            $dowLoadTaskServer->updateExportManageInfo('hcm_vehicle_inspection_courier_export_' . $task_id, $url, 3,$task_id);
            $excelTask->is_delete = 1;
        }

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('VehicleInspectionTask-courierExport end', true);
    }

    /**
     * 导入停职申请
     * @throws Exception
     */
    public function courier_importAction()
    {
        $task = (new AsyncImportTaskService())->getTask(AsyncImportTaskModel::COURIER_VEHICLE_INSPECTION);
        if (empty($task)) {
            echo 'not task ' . PHP_EOL;
            return;
        }
        $args        = $task->args_json ? json_decode($task->args_json, true) : [];
        $fileUrl     = $task->import_path;         // 文件的远程URL
        $tmpDir      = sys_get_temp_dir();         // 获取系统的临时目录路径
        $fileName    = basename($fileUrl);         // 提取文件名
        $tmpFilePath = $tmpDir . '/' . $fileName;  // 构建临时文件路径
        if (!file_put_contents($tmpFilePath, file_get_contents($fileUrl))) {
            $task->status = AsyncImportTaskModel::STATE_EXECUTED;
            $task->save();
            $this->echoErrorLog(__METHOD__ . '读取文件失败 task id:' . $task->id);
        }

        BaseService::setLanguage($args['lang'] ?? 'en');
        $server         = new VehicleInspectionService();

        $result         = $server->dealImportData($tmpFilePath, $task->operator_id, $task->result_file_name, $task->id);
        if (empty($result['url'])) {
            $this->echoErrorLog(__METHOD__ . 'import error' . $task->id);
        }
        echo '上传结果：' . json_encode($result, 256) . PHP_EOL;
        $task->result_path    = $result['url'];
        $task->fail_number    = $result['fail_number'] ?? 0;
        $task->success_number = $result['success_number'] ?? 0;
        $task->status         = AsyncImportTaskModel::STATE_EXECUTED;
        $task->save();
        @unlink($tmpFilePath);
    }
}