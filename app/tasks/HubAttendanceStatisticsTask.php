<?php
/**
 * Author: Bruce
 * Date  : 2022-12-27 22:27
 * Description:外协考勤数据
 */

use App\Library\BaseModel;
use App\Library\BaseService;
use App\Library\DateHelper;
use App\Library\Enums\HubOutSourcingStaffEnums;
use App\Models\backyard\AttendanceHikDataModel;
use App\Models\backyard\HrOutsourcingOrderDetailModel;
use App\Models\backyard\HrOutsourcingOrderModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\StaffHikvisionModel;
use App\Models\backyard\OutsourcingHubStaffAttendanceModel;
use App\Repository\StaffHikvisionRepository;
use App\Repository\SysStoreRepository;
use App\Services\HolidayService;
use App\Services\HrShiftService;
use App\Services\HubOsAttendanceService;
use App\Models\backyard\HubOutsourcingOvertimeModel;
use App\Models\backyard\HubOutsourcingOvertimeDetailModel;
use App\Models\backyard\TaskDataLogModel;
use App\Repository\HubOutsourcingOvertimeRepository;


class HubAttendanceStatisticsTask extends BaseTask
{

    //工号分组处理，每组数量
    const GROUP_NUM = 500;
    //下班打卡时间 buffer
    const OUT_BUFFER = 1;

    public $allStaffOrder = [];//考勤日所有订单
    public $staffOrder = [];//考勤日配置人员的订单
    public $shift_info = [];//班次信息
    public $noInOrderStaffIds = [];//不在订单的工号
    public $staffOrderIds = [];//存在订单的工号

    public $otOrderInfo = [];//存在加班的订单信息
    public $addOtOrderDetailInfo = [];//存入加班详情的数据（工号）

    const OFFSET_TIME = 60 * 60;//1 小时

    /**
     * 任务执行日期:假如为01-03，则处理的是 01-01考勤日的 考勤数据。
     * 1. 01-01考勤日的最早班次->上班时间：01-01 13:00:00,下班时间：01-01 22:00:00
     * 2. 01-01考勤日的最晚班次->上班时间：01-02 11:00:00,下班时间：01-02 20:00:00
     *
     * 申请工单时，是否去掉 12点，11点半的上班班次
     * @param $param
     * @return bool
     */
    public function mainAction($param)
    {
        $this->handel($param);
    }

    public function handel($param)
    {
        BaseService::setLanguage('zh');

        $process_name = str_replace('Class', '', __CLASS__);
        $log          = '任务名称: '.$process_name.' ------ 系统所在国家: '.get_country_code().' ----- '.PHP_EOL;
        $log          .= '开始时间: '.date('Y-m-d H:i:s').PHP_EOL;

        $startDate = !empty($param[0]) ? date("Y-m-d",strtotime("-2 days", strtotime($param[0]))) : date("Y-m-d", strtotime('-2 days'));
        $endDate = !empty($param[0]) ? date("Y-m-d",strtotime("-1 days", strtotime($param[0]))) : date("Y-m-d", strtotime('-1 days'));

        //看是否执行过 任务顺序大于前一个任务序号
        if(RUNTIME == 'pro') {
            $taskDate = date('Y-m-d',strtotime("{$endDate} +1 day"));
            $code = TaskDataLogModel::CODE_HIK_DATA;
            $info = TaskDataLogModel::findFirst("date_at = '{$taskDate}' and code = '{$code}'");
            if(!empty($info) && $info->state > TaskDataLogModel::TASK_ORDER_2){
                die($endDate . "已经跑过了");
            }
            if($info === false || $info->state != TaskDataLogModel::TASK_ORDER_2){
                die($endDate . "没有跑前置任务");
            }
        }

        $params['start_date'] = $startDate;
        $params['end_date']   = $endDate;
        $log          .= '执行日期，开始：'. $params['start_date'] . ', 结束：' . $params['end_date'] . PHP_EOL;

        $params['is_all'] = 0;
        //历史日期数据，跑所有员工
        if(!empty($param[0]) && $param[0]!= date('Y-m-d')) {
            $params['is_all'] = 1;
        }


            $hoaService = reBuildCountryInstance(new HubOsAttendanceService());
            //班次
            $shift = (new HrShiftService())->getList(['is_all' => true]);
            //班次信息
            $this->shift_info = array_column($shift, null, 'id');

            //公共假期

            $holidays = $hoaService->getHoliday();


            //工号，逗号隔开
            if (!empty($param[1])) {
                $params['staff_info_ids'] = explode(',', $param[1]);
            }

            if(isCountry('TH')) {
                //去除 pdc 网点的员工。
                $storeInfo = (new SysStoreRepository())->getPdcHikStore();
                $params['pdc_store_ids'] = empty($storeInfo) ? [] : array_column($storeInfo, 'id');
                $params['is_pdc'] = HubOsAttendanceService::IS_PDC_NO;
            }
            $hubStaffList = (new StaffHikvisionRepository())->getHubStaffInfo($params);
            if (empty($hubStaffList)) {
                $log .= "hub hik data is empty! ".PHP_EOL;
                $log .= "结束时间: ".date('Y-m-d H:i:s').PHP_EOL;
                $this->getDI()->get('logger')->info("HubAttendanceStatisticsTask main:".$log);
                return true;
            }
            $groupHubStaffList = array_chunk($hubStaffList, self::GROUP_NUM);

            //不能影响查询全部订单，把 $params['staff_info_ids'] 赋值给 $staff_info_ids;
            $staff_info_ids = [];
            if(!empty($params['staff_info_ids'])) {//不为空时，是指定工号跑数据
                $staff_info_ids = $params['staff_info_ids'];
                unset($params['staff_info_ids']);
            }
            $this->allStaffOrder = (new \App\Repository\HrOutsourcingOrderRepository())->getHubOrderInfoByAttendanceInfo($params);
            //针对 雇佣天数 大于1 的 需要 变更 雇佣日期 为 考勤周期内的 雇佣日期。
            $this->allStaffOrder = $hoaService->changeEmploymentDate($this->allStaffOrder, $startDate, $this->shift_info, $params);
            $this->allStaffOrder = $hoaService->getOrderAscData($this->allStaffOrder);

            $params['staff_info_ids'] =  $staff_info_ids;

            foreach ($groupHubStaffList as $oneGroupHubStaff) {
                $hubStaffIds              = array_column($oneGroupHubStaff, 'staff_info_id');
                foreach($oneGroupHubStaff as $oneStaff) {
                    if($oneStaff['job_title'] == HubOutSourcingStaffEnums::SECURITY_OUTSOURCE) {
                        $hubStaffIds[] = '10' . $oneStaff['staff_info_id'];
                    }
                }

                $params['staff_info_ids'] = $hubStaffIds;
                //查询部分员工订单
                $this->staffOrder = (new \App\Repository\HrOutsourcingOrderRepository())->getHubStaffOrderInfoByAttendanceInfo($params);
                //针对 雇佣天数 大于1 的 需要 变更 雇佣日期 为 考勤周期内的 雇佣日期。
                $this->staffOrder = $hoaService->changeEmploymentDate($this->staffOrder, $startDate, $this->shift_info, $params);
                $this->staffOrder = array_column($this->staffOrder, NULL, 'staff_info_id');

                [$staffInAttendance, $staffOutAttendance] = $this->getFixHikAttendanceData($params);
                $staffInfos = array_column($oneGroupHubStaff, 'job_title', 'staff_info_id');
                $this->formatWorkAttendance($this->staffOrder, $staffInAttendance, $staffOutAttendance, $this->shift_info, $staffInfos, $holidays);
            }
            //写入 加班详情表，才能关联上订单，记为加班
            if(!empty($this->addOtOrderDetailInfo)) {
                $addOtOrderDetailInfo = array_values($this->addOtOrderDetailInfo);
                $baseServer = new BaseModel();
                $baseServer->table_batch_insert($addOtOrderDetailInfo, 'db_backyard', 'hub_outsourcing_overtime_detail');
            }

            $log .= "结束时间: ".date('Y-m-d H:i:s').PHP_EOL;
            $this->getDI()->get('logger')->info("HubAttendanceStatisticsTask main:".$log);

            //生成补卡数据
            $hoaService->genCorrection($startDate,$hubStaffList);

            //发送补卡push--传参的 重新跑的数据，不发push.
            if(empty($param[0])) {
                $hoaService->sendAttendanceCorrectionNotification($startDate);
            }

            if(RUNTIME == 'pro') {
                //写任务顺序日志
                $info->state = TaskDataLogModel::TASK_ORDER_3;
                $info->update();
            }

            echo $log;
            return true;
       
    }

    /**
     * 插入，更新 考勤正式表
     * @param $staffOrder --外协订单信息
     * @param $staffInAttendance --上班卡
     * @param $staffOutAttendance --下班卡
     * @param $shift_info --班次信息
     * @param $staffInfos --员工信息
     * @param $holidays   --节假日
     * @throws Exception
     */
    public function formatWorkAttendance($staffOrder, $staffInAttendance, $staffOutAttendance, $shift_info, $staffInfos, $holidays)
    {
        $db         = $this->getDI()->get("db_backyard");
        $baseServer = new BaseModel();
        $hubOsAttendanceService = reBuildCountryInstance(new HubOsAttendanceService());

        $inStaffId  = array_keys($staffInAttendance);
        $outStaffId = array_keys($staffOutAttendance);
        //可能只有上班卡，或者只有下班卡。
        $allStaffIds     = array_unique(array_merge($inStaffId, $outStaffId));

        try {
            $allStaffWorkAttendance = $allAbsenceData = [];
            foreach ($allStaffIds as $oneStaffId) {
                //下班打卡网点
                $store_id = isset($staffOutAttendance[$oneStaffId]) ? $staffOutAttendance[$oneStaffId]['attendance_store_id'] : null;
                //上班打卡网点
                $store_id = isset($staffInAttendance[$oneStaffId]) ? $staffInAttendance[$oneStaffId]['attendance_store_id'] : $store_id;

                $shift_id        = isset($staffOrder[$oneStaffId]) ? $staffOrder[$oneStaffId]['shift_id'] : 0;
                $attendance_date = isset($staffOrder[$oneStaffId]) ? $staffOrder[$oneStaffId]['employment_date'] : null;

                $order_serial_no = isset($staffOrder[$oneStaffId]) ? $staffOrder[$oneStaffId]['order_serial_no'] : null;

                $is_not_order = isset($staffOrder[$oneStaffId]) ? (empty($staffOrder[$oneStaffId]['is_not_order']) ? OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO : OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES) : OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO;

                if(empty($staffInAttendance[$oneStaffId]['started_at']) && empty($staffOutAttendance[$oneStaffId]['end_at'])) {
                    continue;
                }

                $oneStaffWorkAttendance['staff_info_id']       = $oneStaffId;
                $oneStaffWorkAttendance['job_title']           = $staffInfos[$oneStaffId] ?? 0;
                $oneStaffWorkAttendance['clocking_date']       = $attendance_date;//实际雇拥日期：这样才能结合 班次，进行判断迟到早退。
                $oneStaffWorkAttendance['order_serial_no']     = $order_serial_no;//匹配外协订单编号
                $oneStaffWorkAttendance['shift_id']            = $shift_id;
                $oneStaffWorkAttendance['shift_start']         = isset($shift_info[$shift_id]) ? $shift_info[$shift_id]['start'] : null;
                $oneStaffWorkAttendance['shift_end']           = isset($shift_info[$shift_id]) ? $shift_info[$shift_id]['end'] : null;
                $oneStaffWorkAttendance['started_at']          = !empty($staffInAttendance[$oneStaffId]['started_at']) ? gmdate('Y-m-d H:i:s', strtotime($staffInAttendance[$oneStaffId]['started_at'])) : null;
                $oneStaffWorkAttendance['end_at']              = !empty($staffOutAttendance[$oneStaffId]['end_at']) ? gmdate('Y-m-d H:i:s', strtotime($staffOutAttendance[$oneStaffId]['end_at'])) : null;
                $oneStaffWorkAttendance['attendance_store_id'] = $store_id;
                $oneStaffWorkAttendance['clocking_time']       = !empty($oneStaffWorkAttendance['started_at']) ? $oneStaffWorkAttendance['started_at'] : $oneStaffWorkAttendance['end_at'];

                $started_at_time = !empty($staffInAttendance[$oneStaffId]['started_at']) ? $staffInAttendance[$oneStaffId]['started_at'] : null;
                $end_at_time     = !empty($staffOutAttendance[$oneStaffId]['end_at']) ? $staffOutAttendance[$oneStaffId]['end_at'] : null;

                [$hub_attendance_date,$is_late,$late_times,$is_leave_early,$leave_early_times,$working_hours] =
                    $hubOsAttendanceService->caclAttData($started_at_time,$end_at_time,$oneStaffWorkAttendance);

                $isHoliday = $hubOsAttendanceService->getIsHoliday($started_at_time, $oneStaffWorkAttendance['clocking_date'], $holidays, $store_id);

                $oneStaffWorkAttendance['is_late']                = $is_late;//是否迟到
                $oneStaffWorkAttendance['late_times']             = $late_times;//迟到分钟数
                $oneStaffWorkAttendance['is_leave_early']         = $is_leave_early;//是否早退
                $oneStaffWorkAttendance['leave_early_times']      = $leave_early_times;//早退分钟数
                $oneStaffWorkAttendance['working_hours']          = $working_hours;//工作时长 （入库，是乘10后的结果，没有小数点）
                $oneStaffWorkAttendance['is_holiday_vacations']   = $isHoliday;//是否节假日
                $oneStaffWorkAttendance['hub_attendance_date']    = $hub_attendance_date;//hub 考勤规则的考勤日期
                $oneStaffWorkAttendance['settlement_coefficient'] = $hubOsAttendanceService->getSettlementCoefficient($working_hours,
                        $isHoliday) * 100;//入库乘100操作，没有小数点。
                $oneStaffWorkAttendance['is_not_order']           = $is_not_order;//是否在 外协订单中。

                //存在考勤数据
                $allAbsenceData[] = $oneStaffWorkAttendance;
                $attendanceIdInfo = $this->getStaffAttendanceInfo($oneStaffWorkAttendance);
                if (!empty($attendanceIdInfo)) {
                    $this->getDi()->get('logger')->info('[HubAttendanceStatisticsTask-update-data]' . json_encode($attendanceIdInfo, JSON_UNESCAPED_UNICODE));
                    $db->updateAsDict('outsourcing_hub_staff_attendance', $oneStaffWorkAttendance, ["conditions" => "id = ?", 'bind' => $attendanceIdInfo['id']]);
                    continue;
                }
                $allStaffWorkAttendance[] = $oneStaffWorkAttendance;
            }

            $absenceData = $this->getAbsenceData($staffOrder, $allAbsenceData, $staffInfos);
            if(!empty($absenceData)) {
                $allStaffWorkAttendance = array_merge($allStaffWorkAttendance, $absenceData);
            }
//            print_r($allStaffWorkAttendance);die;

            if (!empty($allStaffWorkAttendance)) {
                $baseServer->table_batch_insert($allStaffWorkAttendance, 'db_backyard', 'outsourcing_hub_staff_attendance');
            }
        } catch (Exception $e) {
            $this->getDi()->get('logger')->notice('出现异常！HubAttendanceStatisticsTask-》海康考勤统计出现异常，'.'message:'.$e->getMessage());
        }
    }

    /**
     * 获取缺勤数据
     * 上线后就不能重跑数据了。(原因会将补卡的数据覆盖掉，注意回写 补卡数据)
     * @param $staffOrder
     * @param $allStaffWorkAttendance
     * @param $staffInfos
     * @param $shift_info
     * @return array
     */
    public function getAbsenceData($staffOrder, $allStaffWorkAttendance, $staffInfos)
    {
        $allStaffWorkAttendanceToStaff = array_column($allStaffWorkAttendance, NULL,'staff_info_id');
        $db         = $this->getDI()->get("db_backyard");
        $allStaffAbsenceData = [];
        foreach ($staffOrder as $oneOrder) {
            if(isset($allStaffWorkAttendanceToStaff[$oneOrder['staff_info_id']])) {
                continue;
            }
            if(empty($oneOrder['staff_info_id'])) {
                continue;
            }

            $start = isset($this->shift_info[$oneOrder['shift_id']]) ? $this->shift_info[$oneOrder['shift_id']]['start'] : null;
            $end   = isset($this->shift_info[$oneOrder['shift_id']]) ? $this->shift_info[$oneOrder['shift_id']]['end'] : null;

            $attendance_date = !empty($oneOrder['employment_date']) ? $oneOrder['employment_date'] : null;
            $order_serial_no = !empty($oneOrder['order_serial_no']) ? $oneOrder['order_serial_no'] : null;

            $hub_attendance_date = $attendance_date;

            if(strtotime($attendance_date . ' 00:00:00') <= strtotime($attendance_date . ' ' . $start . ':00') && strtotime($attendance_date . ' ' . $start . ':00') <= strtotime($attendance_date . ' 11:30:00')) {
                $hub_attendance_date = date('Y-m-d', strtotime($attendance_date . '-1 days'));
            }

            $oneStaffWorkAttendance['staff_info_id']       = $oneOrder['staff_info_id'];
            $oneStaffWorkAttendance['job_title']           = $staffInfos[$oneOrder['staff_info_id']] ?? 0;
            $oneStaffWorkAttendance['clocking_date']       = $attendance_date;//实际雇拥日期
            $oneStaffWorkAttendance['order_serial_no']     = $order_serial_no;//匹配外协订单编号
            $oneStaffWorkAttendance['shift_id']            = $oneOrder['shift_id'];

            $oneStaffWorkAttendance['shift_start']         = $start;
            $oneStaffWorkAttendance['shift_end']           = $end;

            $oneStaffWorkAttendance['started_at']          = null;
            $oneStaffWorkAttendance['end_at']              = null;
            $oneStaffWorkAttendance['attendance_store_id'] = $oneOrder['store_id'];
            $oneStaffWorkAttendance['clocking_time']       = '0000-00-00 00:00:00';

            $oneStaffWorkAttendance['is_late']    = 0;//是否迟到
            $oneStaffWorkAttendance['late_times'] = 0;//迟到分钟数

            $oneStaffWorkAttendance['is_leave_early']    = 0;//是否早退
            $oneStaffWorkAttendance['leave_early_times'] = 0;//早退分钟数

            $oneStaffWorkAttendance['working_hours']          = 0;//工作时长 （入库，是乘10后的结果，没有小数点）
            $oneStaffWorkAttendance['is_holiday_vacations']   = 0;//是否节假日
            $oneStaffWorkAttendance['hub_attendance_date']    = $hub_attendance_date;//hub 考勤规则的考勤日期
            $oneStaffWorkAttendance['settlement_coefficient'] = 0;
            $oneStaffWorkAttendance['is_not_order']           = OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_NO;//是否在 外协订单中。


            $attendanceIdInfo = $this->getStaffAttendanceInfo($oneStaffWorkAttendance);
            if (!empty($attendanceIdInfo)) {
                $this->getDi()->get('logger')->info('[HubAttendanceStatisticsTask-update-data]' . json_encode($attendanceIdInfo, JSON_UNESCAPED_UNICODE));
                $db->updateAsDict('outsourcing_hub_staff_attendance', $oneStaffWorkAttendance, ["conditions" => "id = ?", 'bind' => $attendanceIdInfo['id']]);
                continue;
            }

            $allStaffAbsenceData[] = $oneStaffWorkAttendance;
        }

        return $allStaffAbsenceData;
    }

    /**
     * 处理hik 打卡数据
     * 整来出每个人 考勤日的上下班打卡时间，班次，网点。
     * 匹配班次，从订单中获取
     * @param $params
     * @param $staffOrder
     * @param $shiftList
     * @param $allStaffOrder
     * @return array
     */
    public function getFixHikAttendanceData($params)
    {
        /**
         * 海康拉取上班卡的数据
         * 考勤周期是当日中午12:00 - 次日中午12点
         * 开始是时间：12:00 -1 小时 作为获取海康数据的开始时间
         * 结束时间：
         *  次日中午11：:59打卡 + 9小时也是当前的考勤周期，11:59 + 9小时为 为次日21点
         */
        $startTime = $params['start_date'].' 11:00:00';
        $endTime   = $params['end_date'].' 21:00:00';


        /**
         * 举例 8月8号 - 8月9号的考勤数据
         * 海康拉取下班卡的数据
         * 考勤周期是当日中午12:00 - 次日中午12点
         * 开始时间：
         *  第一个班次的下班时间为 当日12:00 + 9小时 为 21点 最为开始时间
         *
         * 结束时间：
         *  次日中午11：59上班 + 9小时 = 21 + 5个小时的加班时间 = 凌晨2点 + 1小时 = 8月9的凌晨3点作为结束时间
         */
        //下班卡--分开查，因为，按时间区间，会把第二个考勤日的上班卡也会查出来。
        $params['start_time'] = $startTime;
        //$params['end_time']   = date("Y-m-d H:i:s",strtotime('+9 hour',strtotime($params['end_date'].' 21:00:00')));//最晚下班时间，考勤日期的第二天的20点-21点，有1个小时的下班打卡时间, 在往后+9小时包含加班打卡
        $params['end_time']   = date('Y-m-d 03:00:00', strtotime("{$params['end_date']} +1 day"));
        //18084 订单配置内的 员工，不区分上下班卡。
        $allHikAttendanceData = $this->getHikAttendanceData($params);

        //存在订单的工号
        $this->staffOrderIds = !empty($this->staffOrder) ? array_column($this->staffOrder, 'staff_info_id') : [];

        //不存在订单的工号
        $this->noInOrderStaffIds = [];

        //获取 订单内员工上班卡数据。
        [$staffInAttendance, $staffInAllAttendance] = $this->getStaffInAttendance($allHikAttendanceData, $startTime, $endTime);
        //获取 订单内员工下班卡数据。
        [$staffOutAttendance, $staffOutAllAttendance] = $this->getStaffOutAttendance($allHikAttendanceData, $staffInAllAttendance, $staffInAttendance);

        //获取 订单内员工上 下 班卡数据。
        [$staffInAttendanceNoOrder, $staffOutAttendanceNoOrder] = $this->getStaffNoOrderAttendance($staffInAllAttendance, $staffOutAllAttendance , $startTime, $endTime);

        if(!empty($staffInAttendanceNoOrder)) {
            foreach ($staffInAttendanceNoOrder as $oneInAttendanceNoOrder) {
                $staffInAttendance[$oneInAttendanceNoOrder['staff_info_id']] = $oneInAttendanceNoOrder;
            }
        }

        if(!empty($staffOutAttendanceNoOrder)) {
            foreach ($staffOutAttendanceNoOrder as $oneOutAttendanceNoOrder) {
                $staffOutAttendance[$oneOutAttendanceNoOrder['staff_info_id']] = $oneOutAttendanceNoOrder;
            }
        }

        return [$staffInAttendance, $staffOutAttendance];
    }

    //获取 订单内员工上班卡数据。
    public function getStaffInAttendance($allHikAttendanceData, $startTime, $endTime)
    {
        //取员工最早的上班卡
        $staffInAttendance = $staffInAllAttendance = [];
        foreach ($allHikAttendanceData as $oneInHikAttendance) {
            //职位：SECURITY_OUTSOURCE 去掉 10 前缀。
            if (substr($oneInHikAttendance['staff_info_id'], 0, 2) == 10) {
                $oneInHikAttendance['staff_info_id'] = substr($oneInHikAttendance['staff_info_id'], 2);
            }

            //获取员工所有上班卡记录
            if($oneInHikAttendance['direction'] == AttendanceHikDataModel::DIRECTION_IN || $oneInHikAttendance['direction'] == AttendanceHikDataModel::DIRECTION_ZERO) {
                $hikAttendanceInfo['event_time'] = $oneInHikAttendance['event_time'];
                $hikAttendanceInfo['store_id'] = $oneInHikAttendance['flash_store_name'];
                $staffInAllAttendance[$oneInHikAttendance['staff_info_id']][] = $hikAttendanceInfo;

            }

            //不在考勤日上班卡范围内，跳过
            if (!($oneInHikAttendance['event_time'] >= $startTime && $oneInHikAttendance['event_time'] <= $endTime)) {
                continue;
            }

            //如果工号不存在在订单里
            if (!in_array($oneInHikAttendance['staff_info_id'], $this->staffOrderIds)) {
                $this->noInOrderStaffIds[] = $oneInHikAttendance['staff_info_id'];
                continue;
            }

            // 只是处理存在订单中的数据
            $staffInAttendance[$oneInHikAttendance['staff_info_id']]['staff_info_id']       = $oneInHikAttendance['staff_info_id'];
            $staffInAttendance[$oneInHikAttendance['staff_info_id']]['attendance_store_id'] = $oneInHikAttendance['flash_store_name'];
            $staffInAttendance[$oneInHikAttendance['staff_info_id']]['direction']           = AttendanceHikDataModel::DIRECTION_IN;

            /**
             * 存在外协订单的一定存在班次，
             * 此需求只是处理有班次的情况
             * 原规则：1. 上班打卡时间：取订单班次起始时间向前一小时内最近的上班打卡记录，如未取到则查找班次起始时间向后至班次结束时间内最近的一条上班打卡记录。
             * 例：班次起始时间为15点-0点，则上班时间取14点至15点间的上班打卡记录，如果没有，则取15点以后至班次结束时间（0点）内最早的一条上班打卡记录，如也没有，则为空。
             * 新规则：上班打卡区间：班次开始时间-1小时 <= x < 班次结束时间, 如果存在多条是则获取第一条时间最早的那条
             */

            // th正式环境数据存在shift_begin_time 或者 shift_end_time 为空的的数据，如果为空 则使用班次获取时间
            $shiftInfo = $this->shift_info[$this->staffOrder[$oneInHikAttendance['staff_info_id']]['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];
            // 当前order对应的班次结束时间
            $shift_end_time = $shiftInfo['end'];

            $employment_date = $this->staffOrder[$oneInHikAttendance['staff_info_id']]['employment_date'];
            $start_time      = $employment_date.' '.$shift_start_time.':00';
            // 当前order对应班次起始时间减去一个小时
            $start_time      = date('Y-m-d H:i:s', strtotime('-1 hour', strtotime($start_time)));

            $end_time        = $employment_date.' '.$shift_end_time.':00';
            if ($shift_end_time < $shift_start_time) {
                // 跨天
                $end_time = date('Y-m-d H:i:s', strtotime("{$end_time} +1 day"));
            }
            // 海康打卡时间
            $oneInHikAttendance_event_time = $oneInHikAttendance['event_time'];
            if (strtotime($start_time) <= strtotime($oneInHikAttendance_event_time) && strtotime($oneInHikAttendance_event_time) < strtotime($end_time)) {
                // 取首次打卡时间，如果存在则不在更新该时间
                if (isset($staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'])) {
                    if ($staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'] > $oneInHikAttendance_event_time) {
                        $staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'] = $oneInHikAttendance_event_time;
                    }
                } else {
                    $staffInAttendance[$oneInHikAttendance['staff_info_id']]['started_at'] = $oneInHikAttendance_event_time;
                }
            }
        }

        return [$staffInAttendance, $staffInAllAttendance];
    }

    /**
     * 获取 未在订单中配置的员工，
     * @param $staffInAllAttendance-所有上班卡数据
     * @param $staffOutAllAttendance - 所有下班卡数据
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getStaffNoOrderAttendance($staffInAllAttendance, $staffOutAllAttendance , $startTime, $endTime)
    {
        $staffInAttendance = $staffOutAttendance = [];
        $this->noInOrderStaffIds = array_values(array_unique($this->noInOrderStaffIds));
        foreach ($this->noInOrderStaffIds as $oneStaffId) {
            //没找到 员工上班卡记录，跳过
            if(!isset($staffInAllAttendance[$oneStaffId]) || !isset($staffOutAllAttendance[$oneStaffId])) {
                continue;
            }
            //找出上班卡，匹配的订单
            $inOneOrder = [];
            foreach ($staffInAllAttendance[$oneStaffId] as $oneStaffInAttendance) {
                //不在考勤日上班卡范围内，跳过
                if (!(strtotime($oneStaffInAttendance['event_time']) >= strtotime($startTime) && strtotime($oneStaffInAttendance['event_time']) <= strtotime($endTime))) {
                    continue;
                }
                $orders = $this->getMateOrderIn($oneStaffInAttendance['event_time'], $oneStaffInAttendance['store_id']);
                if(empty($orders)) {
                    continue;
                }
                //由于 打卡时间 正序，所以 多个打卡时间 取最早。同一个订单 就是要取最早打卡时间
                foreach ($orders as $order) {
                    if(isset($inOneOrder[$order['id']])) {
                        if(strtotime($inOneOrder[$order['id']]) > strtotime($oneStaffInAttendance['event_time'])) {
                            $inOneOrder[$order['id']] = $oneStaffInAttendance['event_time'];
                        }
                    } else {
                        $inOneOrder[$order['id']] = $oneStaffInAttendance['event_time'];
                    }
                }

            }

            //找出下班卡，匹配的订单
            $outOneOrder = [];
            foreach ($staffOutAllAttendance[$oneStaffId] as $oneStaffOutAttendance) {
                //不在考勤日上班卡范围内，跳过
                if (!(strtotime($oneStaffOutAttendance['event_time']) >= strtotime($startTime) && strtotime($oneStaffOutAttendance['event_time']) <= strtotime($endTime))) {
                    continue;
                }

                $orders = $this->getMateOrderOut($oneStaffOutAttendance['event_time'], $oneStaffOutAttendance['store_id']);
                if(empty($orders)) {
                    continue;
                }
                //由于 打卡时间 正序，所以 多个打卡时间 取最早。同一个订单 多个打卡，取最晚的一个卡。
                foreach ($orders as $order) {
                    if(isset($outOneOrder[$order['id']])) {
                        if(strtotime($outOneOrder[$order['id']]) < strtotime($oneStaffOutAttendance['event_time'])) {
                            $outOneOrder[$order['id']] = $oneStaffOutAttendance['event_time'];
                        }
                    } else {
                        $outOneOrder[$order['id']] = $oneStaffOutAttendance['event_time'];
                    }
                }
            }

            //上班卡匹配的订单，下班卡匹配的订单有一个为空，则为 缺卡
            if(empty($inOneOrder) || empty($outOneOrder)) {
                continue;
            }

            $inOneOrderIds  = array_keys($inOneOrder);
            $outOneOrderIds = array_keys($outOneOrder);
            $orderIds       = array_values(array_unique(array_intersect($inOneOrderIds, $outOneOrderIds)));
            //没有交集，则为 不是同一个订单。跳过。
            if(empty($orderIds)) {
                continue;
            }

            //取最早班次的一个订单。
            $mateOrder = [];
            foreach ($this->allStaffOrder as $oneOrder) {
                if(in_array($oneOrder['id'], $orderIds)) {
                    $mateOrder = $oneOrder;
                    break;
                }
            }

            //原订单，存在这个工号，跳过。这里是 处理未在订单内的。
            //未找到 匹配订单的 跳过。
            if(isset($this->staffOrder[$oneStaffId]) || empty($mateOrder)) {
                continue;
            }

            //将 没在订单配置的员工，匹配进到订单里。（实际没在订单里）
            $mateOrder['staff_info_id']    = $oneStaffId;
            $mateOrder['is_not_order']     = OutsourcingHubStaffAttendanceModel::IS_NOT_ORDER_YES;
            $this->staffOrder[$oneStaffId] = $mateOrder;

            //员工上班卡
            $staffInAttendance[$oneStaffId]['staff_info_id']       = $oneStaffId;
            $staffInAttendance[$oneStaffId]['attendance_store_id'] = $mateOrder['store_id'];
            $staffInAttendance[$oneStaffId]['direction']           = AttendanceHikDataModel::DIRECTION_IN;
            $staffInAttendance[$oneStaffId]['started_at']          = $inOneOrder[$mateOrder['id']];

            //员工下班卡
            $staffOutAttendance[$oneStaffId]['staff_info_id']       = $oneStaffId;
            $staffOutAttendance[$oneStaffId]['attendance_store_id'] = $mateOrder['store_id'];
            $staffOutAttendance[$oneStaffId]['direction']           = AttendanceHikDataModel::DIRECTION_OUT;
            $staffOutAttendance[$oneStaffId]['end_at']              = $outOneOrder[$mateOrder['id']];
        }

        return [$staffInAttendance, $staffOutAttendance];
    }

    //获取 订单内员工下班卡数据。
    public function getStaffOutAttendance($allHikAttendanceData, $staffInAllAttendance, $staffInAttendance)
    {
        $staffHikvisionRepository = new StaffHikvisionRepository();
        $hubOsAttendanceService = new HubOsAttendanceService();

        //去除 pdc 网点的员工。
        $storeInfo = (new SysStoreRepository())->getPdcHikStore();
        $params['pdc_store_ids'] = empty($storeInfo) ? [] : array_column($storeInfo, 'id');
        $params['is_pdc'] = HubOsAttendanceService::IS_PDC_NO;

        //取员工最早的下班卡，排除休息的的下班卡。
        $staffOutAttendance = $staffOutAllAttendance = [];
        foreach ($allHikAttendanceData as $oneOutHikAttendance) {
            //获取员工所有下班卡记录
            if($oneOutHikAttendance['direction'] == AttendanceHikDataModel::DIRECTION_OUT || $oneOutHikAttendance['direction'] == AttendanceHikDataModel::DIRECTION_ZERO) {
                $hikAttendanceInfo['event_time'] = $oneOutHikAttendance['event_time'];
                $hikAttendanceInfo['store_id']   = $oneOutHikAttendance['flash_store_name'];
                $staffOutAllAttendance[$oneOutHikAttendance['staff_info_id']][] = $hikAttendanceInfo;
            }
            //如果当前下班卡时间 小于 员工上班卡时间 则跳过。
            if (!empty($staffInAttendance[$oneOutHikAttendance['staff_info_id']]['started_at']) && $staffInAttendance[$oneOutHikAttendance['staff_info_id']]['started_at'] >= $oneOutHikAttendance['event_time']) {
                continue;
            }

            //判断 下班卡是否为，休息下班卡，如果是则跳过
            if (isset($staffInAllAttendance[$oneOutHikAttendance['staff_info_id']]) && $hubOsAttendanceService->isRestOut($oneOutHikAttendance['event_time'],
                    $staffInAllAttendance[$oneOutHikAttendance['staff_info_id']])) {
                continue;
            }

            //如果工号不存在订单里 下班时间需要单独处理
            if (!in_array($oneOutHikAttendance['staff_info_id'], $this->staffOrderIds)) {
                $this->noInOrderStaffIds[] = $oneOutHikAttendance['staff_info_id'];
                continue;
            }

            /**
             * 2. 下班打卡时间：取订单班次开始时间向后的最早一条非休息下班打卡记录。
             * 例：班次起始时间15点，则取15点以后最早的一条非休息（下班打卡时间后90分钟内无上班打卡记录，泰国吃饭时间需要打上下班卡）下班打卡记录。
             * 2. 下班打卡区间：班次开始时间< x <=班次结束时间+1小时
             */
            $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['staff_info_id']       = $oneOutHikAttendance['staff_info_id'];
            $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['attendance_store_id'] = $oneOutHikAttendance['flash_store_name'];
            $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['direction']           = AttendanceHikDataModel::DIRECTION_OUT;

            //只是处理在订单中的数据
            // 班次信息
            $shiftInfo = $this->shift_info[$this->staffOrder[$oneOutHikAttendance['staff_info_id']]['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];
            // 当前order对应的班次结束时间
            $shift_end_time = $shiftInfo['end'];
            // 当前order对应结束班次 加上1小时
            $forward_time = date('H:i', strtotime('+1 hour', strtotime($shift_end_time)));

            $employment_date = $this->staffOrder[$oneOutHikAttendance['staff_info_id']]['employment_date'];
            $start_time      = $employment_date.' '.$shift_start_time.':00';
            $end_time        = $employment_date.' '.$forward_time.':00';
            if ($end_time < $start_time) {
                // 存在跨天 结束时间需要 + 1天
                $end_time = date('Y-m-d H:i:s', strtotime("{$end_time} +1 day"));
                //$start_time     = $yesterday_date.' '.$shift_start_time.':00';
            }
            $serial_no = $this->staffOrder[$oneOutHikAttendance['staff_info_id']]['order_serial_no'];
            $otInfo = $hubOsAttendanceService->getOvertimeDuration([
                'serial_no' => $serial_no,
                'staff_id'  => $oneOutHikAttendance['staff_info_id'],
                'pdc_store_ids'  => $params['pdc_store_ids'],
                'is_pdc'  => $params['is_pdc'],
            ], $oneOutHikAttendance['staff_info_id'], $end_time);

            //如何知道这个人加班。$otInfo['is_staff']
            //结束时间 晚于 外协订单结束时间。并且是非 加班订单里的人。匹配
            $outEndTime = strtotime($otInfo['end_time']) > strtotime($end_time) ? $otInfo['end_time'] : $end_time;
            if(!$otInfo['is_staff'] && !isset($this->otOrderInfo[$otInfo['ot_serial_no']])) {
                $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_serial_no'] = $otInfo['ot_serial_no'];//加班订单编号
                $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_demand_num'] = $otInfo['ot_demand_num'];//申请加班人数
                $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_residue_demand_num'] = $otInfo['ot_demand_num'];//申请加班剩余人数
            }

            // 数据按照时间从小排序，取最晚的一条
            if (strtotime($start_time) < strtotime($oneOutHikAttendance['event_time']) && strtotime($oneOutHikAttendance['event_time']) <= strtotime($outEndTime)) {
                if (isset($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'])) {
                    if ($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] < $oneOutHikAttendance['event_time']) {
                        $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] = $oneOutHikAttendance['event_time'];
                    }
                } else {
                    $staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] = $oneOutHikAttendance['event_time'];
                }

                //如果 下班卡超出 外协订单的 下班卡 打卡区间， 且 当前人所在订单为加班订单  则 为加班
                if($staffOutAttendance[$oneOutHikAttendance['staff_info_id']]['end_at'] > $end_time && !$otInfo['is_staff']) {
                    //加班人数 -1 操作。
                    if (!isset($this->addOtOrderDetailInfo[$oneOutHikAttendance['staff_info_id']])) {
                        if ($this->otOrderInfo[$otInfo['ot_serial_no']]['ot_residue_demand_num'] > 0) {
                            $this->otOrderInfo[$otInfo['ot_serial_no']]['ot_residue_demand_num']--;
                        }

                        $hubStaffList = $staffHikvisionRepository->getHubStaffInfo(['staff_info_ids' => [$oneOutHikAttendance['staff_info_id']]]);
                        $company_id = empty($hubStaffList) ? 0 : $hubStaffList[0]['outsourcing_company_id'];

                        $addOtOrderDetailInfo['staff_id']                                  = $oneOutHikAttendance['staff_info_id'];
                        $addOtOrderDetailInfo['hub_outsourcing_overtime_id']               = $otInfo['ot_id'];
                        $addOtOrderDetailInfo['serial_no']                                 = $otInfo['ot_serial_no'];
                        $addOtOrderDetailInfo['company_id']                                = $company_id;
                        $addOtOrderDetailInfo['outsourcing_order_serial_no']               = $serial_no;
                        $this->addOtOrderDetailInfo[$oneOutHikAttendance['staff_info_id']] = $addOtOrderDetailInfo;
                    }
                }
            }
        }

        return [$staffOutAttendance, $staffOutAllAttendance];
    }

    /**
     * 用 上班卡 匹配 订单。
     * @param $clock_time
     * @param $storeId
     * @return array|mixed
     */
    public function getMateOrderIn($clock_time, $storeId)
    {
        $allOrder = [];
        foreach ($this->allStaffOrder as $oneOrder) {
            $shiftInfo = $this->shift_info[$oneOrder['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];

            $employment_date = $oneOrder['employment_date'];
            $begin_time      = strtotime($employment_date . ' ' . $shift_start_time . ':00');
            $startTime  = $begin_time - self::OFFSET_TIME;
            $endTime    = $begin_time + self::OFFSET_TIME;
            if (strtotime($clock_time) >= $startTime && strtotime($clock_time) <= $endTime && $oneOrder['store_id'] == $storeId) {
                $allOrder[] = $oneOrder;
            }
        }

        return $allOrder;
    }

    /**
     * 用 下班卡 匹配 订单。
     * @param $clock_time
     * @param $storeId
     * @return array|mixed
     */
    public function getMateOrderOut($clock_time, $storeId)
    {
        $allOrder = [];
        foreach ($this->allStaffOrder as $oneOrder) {
            $shiftInfo = $this->shift_info[$oneOrder['shift_id']];
            // 当前order对应的班次开始时间
            $shift_start_time = $shiftInfo['start'];
            // 当前order对应的班次结束时间
            $shift_end_time = $shiftInfo['end'];

            $employment_date = $oneOrder['employment_date'];
            $start_time      = $employment_date . ' ' . $shift_start_time . ':00';
            $end_time        = $employment_date . ' ' . $shift_end_time . ':00';
            if (strtotime($end_time) < strtotime($start_time)) {
                // 存在跨天 结束时间需要 + 1天
                $end_time = date('Y-m-d H:i:s', strtotime("{$end_time} +1 day"));
            }

            $startTime = strtotime($end_time) - self::OFFSET_TIME;
            $endTime   = strtotime($end_time) + self::OFFSET_TIME;
            if (strtotime($clock_time) >= $startTime && strtotime($clock_time) <= $endTime && $oneOrder['store_id'] == $storeId) {
                $allOrder[] = $oneOrder;
            }
        }

        return $allOrder;
    }

    /**
     * 判断 下班卡是否为休息时打的。
     * @param $event_time
     * @param $staffInAllAttendance
     * @return bool
     */
    public function isRestOut($event_time, $staffInAllAttendance)
    {
        foreach ($staffInAllAttendance as $oneAttendance) {
            if(empty($oneAttendance['event_time'])) {
                continue;
            }
            $startEventTime = strtotime($event_time);
            $endEventTime = strtotime($event_time) + 90 * 60;
            $inAttendance = strtotime($oneAttendance['event_time']);
            if($startEventTime < $inAttendance && $inAttendance < $endEventTime) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取打卡数据
     * @param $params
     * @return mixed
     */
    public function getHikAttendanceData($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['staff_info_id', 'date_at', 'event_time', 'direction', 'flash_store_name']);
        $builder->from(AttendanceHikDataModel::class);
        $builder->andWhere('staff_info_id in ({staff_info_ids:array})',
            ['staff_info_ids' => $params['staff_info_ids']]);

        if(isset($params['direction'])){
            $builder->andWhere('direction = :direction:', ['direction' => $params['direction']]);
        }

        $builder->andWhere('event_time >= :start_time: and event_time < :end_time:',
            ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
        $builder->orderBy('staff_info_id ASC, event_time ASC');

        //兼容是否是pdc
        $sql = '';
        if(!empty($params['is_pdc']) && $params['is_pdc'] == HubOsAttendanceService::IS_PDC_NO) {
            $sql = 'flash_store_name not in ({pdc_store_ids:array})';
        }

        if(!empty($params['is_pdc']) && $params['is_pdc'] == HubOsAttendanceService::IS_PDC_YES) {
            $sql = 'flash_store_name in ({pdc_store_ids:array})';
        }

        if(!empty($sql) && !empty($params['pdc_store_ids'])) {
            $builder->andWhere($sql, ['pdc_store_ids' => $params['pdc_store_ids']]);
        }

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取员工信息
     * @param $params
     * @return array
     */
    public function getStaffInfo($params)
    {
        $staffInfos = HrStaffInfoModel::find([
            'conditions' => '  staff_info_id in ({staff_info_ids:array}) ',
            'bind'       => [
                'staff_info_ids' => $params['staff_info_ids'],
            ],
            'columns'    => 'staff_info_id, job_title',
        ])->toArray();

        return array_column($staffInfos, 'job_title', 'staff_info_id');
    }

    /**
     * 获取员工 考勤日 打卡数据。
     * @param $params
     * @return array
     */
    public function getStaffAttendanceInfo($params)
    {
        $staffAttendance = OutsourcingHubStaffAttendanceModel::findFirst([
            'conditions' => '  staff_info_id = :staff_id: and hub_attendance_date = :hub_attendance_date: ',
            'bind'       => [
                'staff_id'        => $params['staff_info_id'],
                'hub_attendance_date' => $params['hub_attendance_date'],
            ],
            'columns'    => 'id',
        ]);

        return !empty($staffAttendance) ? $staffAttendance->toArray() : [];
    }

    /**
     * 跑历史数据
     * @param array $params
     */
    public function rush_historyAction($params = [])
    {
        if(empty($params[0])) {
            echo '未传日期区间';
            return false;
        }
        $params[1] = empty($params[1]) ? $params[0] : $params[1];
        $historyDates = DateHelper::DateRange(strtotime($params[0]), strtotime($params[1]));
        $logger = $this->getDi()->get('logger');
        foreach ($historyDates as $oneDate) {
            $res = $this->mainAction([0 => $oneDate]);
            if($res) {
                $logger->info('[HubAttendanceStatisticsTask] rush_historyAction:' . $oneDate . ":success");
                echo $oneDate . ":success" . PHP_EOL;
            } else {
                $logger->info('[HubAttendanceStatisticsTask] rush_historyAction:' . $oneDate . ":fail");
                echo $oneDate . ":fail" . PHP_EOL;
            }
        }
    }

    /**
     * 删除 重复 hik 加班详情
     */
    public function fix_ot_detailAction()
    {
        $detailList = HubOutsourcingOvertimeDetailModel::find([
            'columns'    => 'id,hub_outsourcing_overtime_id,staff_id',
            'conditions' => "company_id = 0 and outsourcing_order_serial_no != '' ",
        ])->toArray();

        $issetStaff = [];
        $isDelete = [];
        $isSave = [];
        foreach ($detailList as $one) {
            if(isset($issetStaff[$one['hub_outsourcing_overtime_id'] . '_' . $one['staff_id']])) {
                $isDelete[] = $one['id'];
            } else {
                $issetStaff[$one['hub_outsourcing_overtime_id'] . '_' . $one['staff_id']] = $one['id'];
                $isSave[$one['staff_id']] = $one['id'];
            }
        }

        $isDeleteBatch = array_chunk($isDelete, 500);

        $db = $this->getDI()->get('db_backyard');
        foreach ($isDeleteBatch as $oneBatch) {
            $oneBatchString = implode(',', $oneBatch);

            $sql = "DELETE FROM hub_outsourcing_overtime_detail WHERE id in ({$oneBatchString})";
            $db->execute($sql);

            $this->logger->info(['fix_ot_detail_delete' => $oneBatchString]);
        }

        echo "success";
    }

    /**
     * PDC 拉取 hik 打卡数据
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function get_hik_dataAction($params)
    {
        /**
         * 如果传参数的话，参数规则：
         * php app/cli.php outsourcing_company_hikvision_sync get_hub_data 2025-05-01 12:12:12 2025-05-02 13:13:13
         * 第一个时间参数是 结束时间， 第二个为开始时间。
         * todo 上线前 算好拉取的区间数据。
         */

        $hubOsAttendanceService = new HubOsAttendanceService();
        $currentTime = !empty($params[0]) && !empty($params[1]) ? $params[0] . ' ' . $params[1] : date('Y-m-d H:i:s');
        //格式化时间，分钟数小于 30分钟，则分钟为 00，分钟数大于 30分钟，则分钟为 30，
        $currentTime = $hubOsAttendanceService->formatTime($currentTime);

        $startTime = date('Y-m-d H:i:s', strtotime("{$currentTime}-32 minutes"));
        $endTime = $currentTime;

        if(!empty($params[2]) && !empty($params[3])) {
            $startTime = $params[2] . ' ' . $params[3];
        }

        if (strtotime($startTime) > strtotime($endTime)) {
            echo "开始时间 不能大于 结束时间" . PHP_EOL;
            return false;
        }

        $paramsIn['start_time'] = $startTime;
        $paramsIn['end_time'] = $endTime;

        $res = $hubOsAttendanceService->getAttendanceData($paramsIn);
        $res['execute_time'] = $paramsIn['end_time'];
        $hubOsAttendanceService->addRecordExecuteLog($res);
    }

    /**
     * 监测 拉取失败的任务，再次执行。
     * @throws Exception
     */
    public function get_hik_data_monitorAction()
    {
        $hubOsAttendanceService = new HubOsAttendanceService();

        $logList = $hubOsAttendanceService->getAttendanceHikRecordLog();
        if(empty($logList)) {
            return false;
        }
        foreach ($logList as $oneData) {
            if(empty($oneData['execute_time'])) {
                continue;
            }

            $oneData['execute_time'] = date('Y-m-d', strtotime("{$oneData['execute_time']}+2 minutes"));
            $data = explode(' ', $oneData['execute_time']);
            $this->get_hik_dataAction($data);
        }
        return true;
    }


    /**
     * pdc 计算考勤数据
     * @param $params
     *
     * @throws Exception
     */
    public function executeAction($params)
    {
        $hubOsAttendanceService = new HubOsAttendanceService();
        $currentTime = !empty($params[0]) && !empty($params[1]) ? $params[0] . ' ' . $params[1] : date('Y-m-d H:i:s');
        //格式化时间，分钟数小于 30分钟，则分钟为 00，分钟数大于 30分钟，则分钟为 30，
        $currentTime = $hubOsAttendanceService->formatTime($currentTime);

        //取当前时间 前两小时的班次
        $currentTime = date('Y-m-d H:i:s', strtotime("{$currentTime}-2 hours"));

        $paramsIn['current_time'] = $currentTime;

        $res = $hubOsAttendanceService->statistics($paramsIn);
        $this->getDi()->get('logger')->write_log(['hik-executeAction-statistics' => $res], 'notice');
    }

    /**
     * pdc 补卡数据
     * 一天执行一次。
     * @param $params
     */
    public function card_replacementAction($params)
    {
        $currentDay = !empty($params[0]) ? $params[0] : date('Y-m-d', strtotime("-2 days"));
        $hubOsAttendanceService = new HubOsAttendanceService();
        $hubOsAttendanceService->cardReplacementStatistics($currentDay);
    }

    /**
     * 刷pdc近一个考勤周期的数据（历史数据）
     * @param $params
     * @return bool
     * @throws Exception
     */
    public function rush_executeAction($params)
    {
        if(empty($params[0]) || empty($params[1]) || empty($params[2]) || empty($params[3])) {
            echo "请输入 开始时间 结束时间";
            return false;
        }
        // 定义开始和结束时间
        $start = new DateTime($params[0] . ' ' . $params[1]);
        $end = new DateTime($params[2] . ' ' . $params[3]);

        // 初始化结果数组
        $timePoints = [];

        // 从开始时间开始，每次增加1小时，直到结束时间
        $current = clone $start;
        while ($current <= $end) {
            // 添加当前小时的05分时间点
            $timePoint1 = clone $current;
            $timePoint1->setTime($current->format('H'), 05, 00);
            if ($timePoint1 >= $start && $timePoint1 <= $end) {
                $timePoints[] = $timePoint1->format('Y-m-d H:i:s');
            }

            // 添加当前小时的35分时间点
            $timePoint2 = clone $current;
            $timePoint2->setTime($current->format('H'), 35, 00);
            if ($timePoint2 >= $start && $timePoint2 <= $end) {
                $timePoints[] = $timePoint2->format('Y-m-d H:i:s');
            }

            // 增加1小时
            $current->modify('+1 hour');
        }

        // 输出结果
        foreach ($timePoints as $time) {
            echo $time . PHP_EOL;
            $date = explode(' ', $time);
            $this->executeAction($date);
        }
        echo 'success!!!' . PHP_EOL;
    }

}