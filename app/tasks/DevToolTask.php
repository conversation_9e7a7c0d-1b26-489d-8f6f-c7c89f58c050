<?php

use App\Library\BaseModel;
use App\Library\DateHelper;
use App\Models\backyard\HrStaffInfoExtendModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffShiftModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\HrStaffTransferModel;


class DevToolTask extends BaseTask {


    public function backupTableAction()
    {
        if (!in_array(RUNTIME, ['dev', 'tra'])) {
            exit('测试工具，不要乱搞');
        }
        $tableList   = [
            'setting_env',
        ];
        $db_backyard = $this->getDI()->get('db_backyard');

        foreach ($tableList as $table) {
            $db_backyard->begin();
            $table_back_up = $table . '_backup';
            $show_create_table      = "SHOW TABLES LIKE '%$table_back_up%' ;";
            $show_create_table_info = $db_backyard->query($show_create_table)->fetch(\Phalcon\Db::FETCH_ASSOC);
            if (empty($show_create_table_info)) {

                $show_create_table      = "show create table {$table} ;";
                $show_create_table_info = $db_backyard->query($show_create_table)->fetch(\Phalcon\Db::FETCH_ASSOC);
                $create_table           = str_replace("`{$table}`", "`{$table_back_up}`",
                    $show_create_table_info['Create Table']);
                $db_backyard->execute($create_table);
            } else {
                $db_backyard->execute("delete from {$table_back_up}");
            }


            $dataList = $db_backyard->query("select * from $table")->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            $this->logger->info([$table_back_up=>$dataList]);
            (new BaseModel())->table_batch_insert($dataList, 'db_backyard', $table_back_up);
            $db_backyard->commit();
        }
    }


    /**
     * 每天凌晨对齐sys表数据
     * @return void
     * @throws Exception
     */
    public function syncSysTableAction()
    {
        if (!in_array(RUNTIME, ['dev', 'tra'])) {
            exit('测试工具，不要乱搞');
        }
        $tableList   = [
            'sys_store',
            'sys_department',
            'sys_manage_piece',
            'sys_manage_region',
            'sys_fleet_van_type',
        ];
        $db_backyard = $this->getDI()->get('db_backyard');
        $db_bi = $this->getDI()->get('db_wbi');
        $db_fle      = $this->getDI()->get('db_fle');

        foreach ($tableList as $table) {
            $db_backyard->begin();
            $db_bi->begin();

            $up_sql = "drop table if exists $table ;";
            $db_backyard->execute($up_sql);
            $db_bi->execute($up_sql);

            $show_create_table      = "show create table {$table} ;";
            $show_create_table_info = $db_fle->query($show_create_table)->fetch(\Phalcon\Db::FETCH_ASSOC);
            $db_backyard->execute($show_create_table_info['Create Table']);
            $db_bi->execute($show_create_table_info['Create Table']);

            $dataList = $db_fle->query("select * from $table")->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            (new BaseModel())->table_batch_insert($dataList, 'db_backyard', $table);
            (new BaseModel())->table_batch_insert($dataList, 'db_wbi', $table);
            $db_backyard->commit();
            $db_bi->commit();
        }
    }

    public function filterStaffInfoAction(){
        if(!in_array(RUNTIME,['dev','tra'])){
            exit('测试工具，不要乱搞');
        }
        $db      = $this->getDI()->get('db_backyard');
        //更新员工表信息
        $up_sql                = "update hr_staff_info set personal_email = '<EMAIL>' ,email = '<EMAIL>' ";
        $re = $db->execute($up_sql);
        var_dump($re);
    }


    public function syncStaffAction($param){
        if(!in_array(RUNTIME,['pro','dev','tra'])){
            exit('测试工具，不要乱搞');
        }
        
        if(!isset($param[0]) ){
            exit('请输入绝对路径的文件名');
        }

        $file_name  = $param[0];
        if(!file_exists($file_name)){
            $s = file_get_contents($file_name);
            if(empty( $s )){
                exit('文件不存在');
            }
            $file_name = "/tmp/" . pathinfo($file_name)['basename'];
            file_put_contents($file_name,$s) ;
        }
        if(!is_file($file_name)){
            exit('不是文件');
        }
        
        $config = ['path' => dirname($file_name)];
        $file_real_name = basename($file_name);
        $excel = new \Vtiful\Kernel\Excel($config);
        $excel->openFile($file_real_name)
            ->openSheet('Sheet1', \Vtiful\Kernel\Excel::SKIP_EMPTY_ROW);

        //跳过字段第一行
        $excel->setSkipRows(0);
        while ($data = $excel->nextRow()) {

            try {
                if(empty($data[0])){
                    break;
                }
                $params['staff_info_id'] = $data[0];  //用户 id
                $params['department_id'] = $data[1]; //部门
                $params['job_title_id'] = $data[2];  //职位
                $params['manger'] = $data[3];  //上级
                $params['working_country'] = $data[4];      //工作所在国家
                $params['nationality'] = $data[5];          // 国籍
                $params['job_title_grade'] = (int)$data[6]; //职级
                $params['shift_id'] = $data[7]; //班次 id
                $params['is_work_ignore'] = $data[8]; //转正白名单
                $params['is_whitelist'] = $data[9]; //考勤白名单
                $params['working_day_rest_type'] = $data[10]; //休息类型 52
                $params['roles'] = $data[11]; //角色 ','
                $params['email'] = $data[12]; //邮箱
                $params['operator'] = 10000;
                $staffServer = new \App\Services\StaffService();
                $staffServer->syncHrStaffInfo($params);

            }catch (Exception $e){
                $this->echoInfoLog('staff_id:'.$params['staff_info_id'].' errorInfo:'.$e->getMessage());

            }

        }

    }


    /**
     * 打卡
     * php cli.php dev_tool createAttendance 118995 2022-01-14 2022-01-14 08:00,19:00
     */
    public function createAttendanceAction($params)
    {
        if(!in_array(RUNTIME,['dev','tra'])){
            //exit('测试工具，不要乱搞');
        }

        if(empty($params[0])){
            exit('请输入工号 多个工号,分割');
        }
        $staff_info_ids  = explode(',',$params[0]);
        $start_day = empty($params[1])?date('Y-m-d'):$params[1];
        $end_day = empty($params[2])?$start_day:$params[2];
        $date_range = DateHelper::DateRange(strtotime($start_day), strtotime($end_day));


        if(!empty($params[3])){
            [$start_at,$end_at] = explode(',',$params[3]);
        }



        foreach ($staff_info_ids as $staff_info_id) {

            $shiftInfo  = HrStaffShiftModel::findFirst([
                                                                                'conditions'=>'  staff_info_id = :staff_info_id: ',
                                                                                'bind'=>[
                                                                                    'staff_info_id'=>$staff_info_id,

                                                                                ],
                                                                            ]);
            if(!empty($shiftInfo)){
                $shift_start = $shiftInfo->start?:'08:00';
                $shift_end = $shiftInfo->end?:'17:00';
            }else{
                $shift_start = '08:00';
                $shift_end  = '17:00';
            }
            foreach ($date_range as $item) {
                $staff_attendance = StaffWorkAttendanceModel::findFirst( [
                                                                                       'conditions'=>'  staff_info_id = :staff_info_id: and attendance_date = :attendance_date: ',
                                                                                       'bind'=>[
                                                                                           'staff_info_id'=>$staff_info_id,
                                                                                           'attendance_date'=>$item,
                                                                                       ],
                                                                                   ]);
                if(!$staff_attendance){
                    $staff_attendance = new StaffWorkAttendanceModel();
                }

                $staff_attendance->staff_info_id=$staff_info_id;
                $staff_attendance->started_state=5;
                $staff_attendance->end_state=5;
                $staff_attendance->attendance_date=$item;
                $staff_attendance->started_at=gmdate('Y-m-d H:i:s',strtotime($item.' '.($start_at ?? $shift_start)));
                $staff_attendance->end_at=gmdate('Y-m-d H:i:s',strtotime($item.' '.($end_at?? $shift_end)));
                if($staff_attendance->started_at >    $staff_attendance->end_at){
                    $staff_attendance->end_at = gmdate('Y-m-d H:i:s',strtotime($item.' '.($end_at ?? $shift_end))+86400);
                }
                $staff_attendance->shift_start=$shift_start;
                $staff_attendance->shift_end=$shift_end;
                $staff_attendance->save();
            }
        }
        echo 'success';
    }

    /**
     * 修改班次
     * php cli.php dev_tool modifyShift 118995 08:00,19:00
     */
    public function modifyShiftAction($params)
    {
        if(!in_array(RUNTIME,['dev','tra'])){
            exit('测试工具，不要乱搞');
        }

        if(empty($params[0])){
            exit('请输入工号 多个工号,分割');
        }
        if(empty($params[1])){
            exit('请输入班次 用,分割 如 08:00,19:00');
        }
        foreach (explode(',',$params[0]) as $staff_info_id) {
            $shiftInfo  = HrStaffShiftModel::findFirst([
                                                           'conditions'=>'  staff_info_id = :staff_info_id: ',
                                                           'bind'=>[
                                                               'staff_info_id'=>$staff_info_id,

                                                           ],
                                                       ]);
            if(!$shiftInfo){
                $shiftInfo = new HrStaffShiftModel();
            }
            [$start_at,$end_at] = explode(',',$params[1]);
            $shiftInfo->start = $start_at;
            $shiftInfo->end = $end_at;
            $shiftInfo->save();
        }

        echo 'success';
    }

    /**
     * 固化信息
     * php cli.php dev_tool solidifyStaffInfo 118995,232323 2022-01-14 2022-01-14
     */
    public function solidifyStaffInfoAction($params)
    {
        if(!in_array(RUNTIME,['dev','tra'])){
            exit('测试工具，不要乱搞');
        }

        if(empty($params[0])){
            exit('请输入工号 多个工号,分割');
        }
        $staff_info_ids = explode(',',$params[0]);
        $start_day = empty($params[1])?date('Y-m-d'):$params[1];
        $end_day = empty($params[2])?date('Y-m-d'):$params[2];
        $date_range = DateHelper::DateRange(strtotime($start_day), strtotime($end_day));

        foreach ($staff_info_ids as $staff_info_id) {

            $shiftInfo  = HrStaffShiftModel::findFirst([
                'conditions'=>'  staff_info_id = :staff_info_id: ',
                'bind'=>[
                    'staff_info_id'=>$staff_info_id,

                ],
            ]);
            if(!empty($shiftInfo)){
                $shift_start = $shiftInfo->start?:'08:00';
                $shift_end = $shiftInfo->end?:'17:00';
            }else{
                $shift_start = '08:00';
                $shift_end  = '17:00';
            }

            $staffInfo = HrStaffInfoModel::findFirstByStaffInfoId($staff_info_id);

            $staffItem= HrStaffItemsModel::findFirst([
                'conditions'=>'  staff_info_id = :staff_info_id: and item = :item: ',
                'bind'=>[
                    'staff_info_id'=>$staff_info_id,
                    'item'=>'STAFF_PROVINCE_CODE',
                ]]);
            //工作地所在州

            $staffPosition = \App\Models\backyard\HrStaffInfoPositionModel::find(
                [
                    'conditions'=>'  staff_info_id = :staff_info_id: ',
                    'bind'=>[
                        'staff_info_id'=>$staff_info_id,
                    ]]
            );

            $extend['role'] = array_column($staffPosition->toArray(),'position_category');

           $staffExtend = \App\Models\backyard\HrStaffInfoExtendModel::findFirst(
                [
                    'conditions'=>'  staff_info_id = :staff_info_id:',
                    'bind'=>[
                        'staff_info_id'=>$staff_info_id,

                    ],
                ]
            );
            if ($staffExtend) {
                $extend['project_num'] = $staffExtend->project_num;
            }
            foreach ($date_range as $day) {

                $hr_staff_transfer  = HrStaffTransferModel::findFirst(
                    [
                        'conditions'=>'  staff_info_id = :staff_info_id: and stat_date = :stat_date:',
                        'bind'=>[
                            'staff_info_id'=>$staff_info_id,
                            'stat_date'=>$day,

                        ],
                    ]
                );

                if(!$hr_staff_transfer){
                    $hr_staff_transfer = new HrStaffTransferModel();
                }

                $hr_staff_transfer->staff_info_id=$staff_info_id;
                $hr_staff_transfer->stat_date=$day;
                $hr_staff_transfer->shift_start=$shift_start;
                $hr_staff_transfer->shift_end=$shift_end;
                $hr_staff_transfer->week_working_day= $staffInfo->week_working_day;
                $hr_staff_transfer->sys_department_id= $staffInfo->sys_department_id;
                $hr_staff_transfer->node_department_id= $staffInfo->node_department_id;
                $hr_staff_transfer->store_id= $staffInfo->sys_store_id;
                $hr_staff_transfer->formal= $staffInfo->formal;
                $hr_staff_transfer->job_title= $staffInfo->job_title;
                $hr_staff_transfer->state= $staffInfo->state;
                $hr_staff_transfer->province_code = $staffItem?$staffItem->value:'';
                $hr_staff_transfer->extend = json_encode($extend);
                $hr_staff_transfer->save();
            }

        }



        echo 'success';
    }

    /**
     * 打卡
     * php cli.php dev_tool createAttendance 118995 2022-01-14 2022-01-14
     */
    public function createOffDayAction($params)
    {
        if(!in_array(RUNTIME,['dev','tra'])){
            exit('测试工具，不要乱搞');
        }

        if(empty($params[0])){
            exit('请输入工号 多个工号,分割');
        }
        $staff_info_ids  = explode(',',$params[0]);
        $start_day = empty($params[1])?date('Y-m-d'):$params[1];
        $end_day = empty($params[2])?date('Y-m-d'):$params[2];
        $date_range = DateHelper::DateRange(strtotime($start_day), strtotime($end_day));


        foreach ($staff_info_ids as $staff_info_id) {

            foreach ($date_range as $item) {
                $staffWorkDay = HrStaffWorkDaysModel::findFirst( [
                    'conditions'=>'  staff_info_id = :staff_info_id: and date_at = :date_at: ',
                    'bind'=>[
                        'staff_info_id'=>$staff_info_id,
                        'date_at'=>$item,
                    ],
                ]);
                if(!$staffWorkDay){
                    $staffWorkDay = new HrStaffWorkDaysModel();
                }
                $staffWorkDay->staff_info_id=$staff_info_id;
                $staffWorkDay->date_at=$item;
                $staffWorkDay->month=date('Y-m',strtotime($item));
                $staffWorkDay->remark='tool add';
                $staffWorkDay->operator=0;
                $staffWorkDay->save();
            }
        }
        echo 'success';
    }


    /**
     * 生产同步数据导tra
     * @return void
     * @throws Exception
     */
    public function proToTraAction()
    {
        /**
         * 同步by表
         */
        $db_backyard_pro = $this->getDI()->get('db_rby');
        $db_backyard_tra = $this->getDI()->get('db_by_training');
        $tables_by = [
            'hr_staff_info_extend',
            'hr_staff_info',
            'hr_staff_info_position',
            'hr_staff_shift_v2',
            'hr_shift_v2',
            'hr_shift_v2_extend',
        ];

        foreach ($tables_by as $item) {
            $db_backyard_tra->begin();
            $db_backyard_tra->execute("delete from $item");
            $max_id = 0;
            while (true){
                $data = $this->getData($db_backyard_pro,$item,$max_id);
                if(empty($data)){
                    break;
                }
                $max_id = max(array_column($data,'id'));
                $this->batch_insert($db_backyard_tra,$item,$data);
            }
            if($item == 'hr_staff_info'){
                $update_staff = "
                    update hr_staff_info
                    set
                      job_title_grade_v2 = null,
                      job_grade_effective_date = null,
                      mobile = concat('1234', staff_info_id),
                      mobile_company = null,
                      identity = concat('ID12345', staff_info_id),
                      email = concat( id,'<EMAIL>'),
                      name = concat('tra_test001', staff_info_id),
                      name_en = concat('en_tra_test001', staff_info_id),
                      bank_no = concat('4321', staff_info_id),
                      nick_name = concat('nick_name', staff_info_id),
                      personal_email = null;
                ";
                $db_backyard_tra->execute($update_staff);
            }
            $db_backyard_tra->commit();
        }

        /**
         * 同步bi表
         */

        $db_bi_pro = $this->getDI()->get('db_rbi');
        $db_bi_tra = $this->getDI()->get('db_bi_training');
        $tables_bi = [
            'hr_job_title_role',
        ];
        foreach ($tables_bi as $table) {
            $db_bi_tra->begin();
            $db_bi_tra->execute("delete from $table");
            $max_id = 0;
            while (true){
                $data = $this->getData($db_bi_pro,$table,$max_id);
                if(empty($data)){
                    break;
                }
                $max_id = max(array_column($data,'id'));
                $this->batch_insert($db_bi_tra,$table,$data);
            }
            $db_bi_tra->commit();
        }
    }


    protected function getData($db,$table, int $max_id = 0)
    {
        $limit = 1000;
        if (RUNTIME == 'dev') {
            $limit = 2;
        }
        $sql = "select * from $table where id > $max_id order by id asc limit $limit";
        return $db->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    private function batch_insert($db, $table, array $data)
    {
        if (!is_array($data) || count($data) == 0) {
            return false;
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql  = "INSERT INTO " . $table . " ({$keys}) VALUES ";


        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);

            $values = implode(',', array_values($v));
            $sql    .= " ({$values}), ";
        }
        $sql    = rtrim(trim($sql), ',');
        $di     = $this->getDI();
        $logger = $di->get('logger');
        try {
            $result = $db->execute($sql);
        } catch (Exception $e) {
            $logger->write_log($table . '执行批量导入操作,数据库执行结果：失败，原因是：' . $e->getMessage() . ' sql:' . $sql,
                'error');
            throw $e;
        }
        return $result;
    }

}








