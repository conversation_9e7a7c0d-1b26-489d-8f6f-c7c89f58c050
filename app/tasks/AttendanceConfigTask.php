<?php
/**
 * Author: Bruce
 * Date  : 2022-12-19 22:42
 * Description:
 */


use App\Library\BaseService;
use App\Library\FlashOss;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\ExcelService;
use App\Services\AttendanceConfigService;
use App\Models\backyard\HcmStaffPermissionInfoModel;
use App\Services\SettingEnvService;
use App\Models\backyard\AttendanceTemporaryCoordinateModel;
use App\Models\backyard\AttendanceTemporaryCoordinateStaffModel;
use App\Models\backyard\AttendanceTemporaryCoordinateLogModel;
use App\Library\Enums\AttendanceEnums;

class AttendanceConfigTask extends BaseTask
{

    /**
     * 外勤打卡配置- 按工号导出
     * @throws Exception
     */
    public function staffAction()
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('AttendanceConfigTask staff start', true);
        //TODO 整理参数
        ini_set('memory_limit', '-1');
        set_time_limit(0);      // 设置脚本最大执行时间 为0 永不过期

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog('AttendanceConfigTask staff params:'.json_encode($params, JSON_UNESCAPED_UNICODE), true);

        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;

        $hubOsAttendance = new AttendanceConfigService();
        $data            = $hubOsAttendance->getFieldPersonnelStaffList($params);

        $list = !empty($data['data']) ? $data['data'] : [];

        //设置语言
        $t = BaseService::getTranslation($params['lang']);

        $new_data = [];
        $header   = [
            $t['staff_info_id'], //工号
            $t['name'], //姓名
            $t['job_title'], //职位
            $t['department'],  //部门
        ];

        foreach ($list as $key => $value) {
            $new_data[] = [
                $value['staff_info_id'],
                $value['name'],
                $value['job_title_name'],
                $value['department_name'],
            ];
        }

        $file_data = $hubOsAttendance->exportExcel($header, $new_data, $file_name);

        $flashOss  = new FlashOss();
        $ossObject = 'attendance_config/'.date('Ymd').'/'.$excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('AttendanceConfigTask staff end', true);
    }

    /**
     * /**
     * 外勤打卡配置- 按工号导出
     * @throws Exception
     */
    public function departmentAction()
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('AttendanceConfigTask department start', true);
        //TODO 整理参数
        ini_set('memory_limit', '-1');
        set_time_limit(0);      // 设置脚本最大执行时间 为0 永不过期

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog('AttendanceConfigTask department params:'.json_encode($params, JSON_UNESCAPED_UNICODE),
            true);

        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;

        $hubOsAttendance = new AttendanceConfigService();
        $data            = $hubOsAttendance->getFieldPersonnelDepartmentList($params);

        $list = !empty($data['data']) ? $data['data'] : [];

        //设置语言
        $t = BaseService::getTranslation($params['lang']);

        $new_data = [];
        $header   = [
            $t['department'], //部门
            $t['department_id'], //部门id
            $t['job_title'], //职位
            $t['job_title_id'],  //职位id
        ];

        foreach ($list as $key => $value) {
            $new_data[] = [
                $value['department_name'],
                $value['department_id'],
                $value['job_title_name'],
                $value['job_title_id'],
            ];
        }

        $file_data = $hubOsAttendance->exportExcel($header, $new_data, $file_name);

        $flashOss  = new FlashOss();
        $ossObject = 'attendance_config/'.date('Ymd').'/'.$excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('AttendanceConfigTask department end', true);
    }

    /**
     * 增加编辑权限
     * 所有国家，仅执行1次。
     * @throws Exception
     */
    public function rushPermissionAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: '.$process_name.' ------ 系统所在国家: '.get_country_code().' ----- '.PHP_EOL;
        $log          .= '开始时间: '.date('Y-m-d H:i:s').PHP_EOL;

        $permissionList = HcmStaffPermissionInfoModel::find([
            'conditions' => 'permission_id in (1362,1363)',
        ])->toArray();

        $insertDataAll = [];
        foreach ($permissionList as $onePermission) {
            if ($onePermission['permission_id'] == 1362) {//按照部门/职位-编辑
                $insertData['permission_id'] = 4083;
            }

            if ($onePermission['permission_id'] == 1363) {//按照工号-编辑
                $insertData['permission_id'] = 4084;
            }

            $insertData['staff_info_id'] = $onePermission['staff_info_id'];
            $insertData['operator_id']   = 0;
            $log                         .= '数据：工号-'.$onePermission['staff_info_id'].'>>权限id-'.$insertData['permission_id'].PHP_EOL;
            $insertDataAll[]             = $insertData;
        }
        $baseServer = new \App\Library\BaseModel();
        $baseServer->table_batch_insert($insertDataAll, 'db_backyard', 'hcm_staff_permission_info');

        $log .= "结束时间: ".date('Y-m-d H:i:s').PHP_EOL;
        $this->getDI()->get('logger')->info("rushPermissionAction:".$log);
        exit($log);
    }


    //初始化数据 跑一次就行 临时坐标 原配置数据 刷到新表里 store_temporary_coordinate_new
    public function initCoordinateAction()
    {
        $code        = 'store_temporary_coordinate_new';
        $settingData = (new SettingEnvService())->getSetVal($code);//100.61783584202168,13.841675958441565,TH01510322|100.6440373392699,13.672388094587653,TH01510322

        if (empty($settingData)) {
            die('没配置数据');
        }

        $settingData = explode('|', $settingData);
        $model       = new AttendanceTemporaryCoordinateModel();
        $staffModel  = new AttendanceTemporaryCoordinateStaffModel();
        foreach ($settingData as $k => $li) {
            [$lng, $lat, $storeId] = explode(',', $li);


            //导入 主表
            $insert['store_id'] = $storeId;
            $insert['lat'] = formatCoordinate($lat);
            $insert['lng'] = formatCoordinate($lng);
            $insert['remark'] = '系统初始化数据';
            $clone            = clone $model;
            $clone->create($insert);

            //关联员工表 加个 -1
            $staffRow['coordinate_id'] = $clone->id;
            $staffRow['staff_info_id'] = -1;
            $staffClone                = clone $staffModel;
            $staffClone->create($staffRow);
        }
        die('跑完了');
    }


    //导出临时坐标配置信息任务 下载中心
    public function tmpCoordinateExportAction()
    {
        //获取任务参数
        $input = (new ExcelService())->getHcmExcelTaskArgv();
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :' . $input[1], true);
        }

        $params = json_decode(base64_decode($input[0]), true);
        //设置语言
        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :' . $task_id, true);
        }
        $file_name = $excelTask->file_name;

        $hubOsAttendance = new AttendanceConfigService();
        [$header, $list] = $hubOsAttendance->coordinateExportList($params);
        $file_data = $hubOsAttendance->exportExcel($header, $list, $file_name);
        $flashOss  = new FlashOss();
        $ossObject = 'attendance_config/' . date('Ymd') . '/' . $excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();
    }



}