<?php

use App\Library\DateHelper;
use App\Library\Enums\RedisListEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Library\BaseModel;
use App\Models\backyard\HrOutsourcingOrderDetailModel;
use App\Models\backyard\HrOutsourcingOrderModel;
use App\Models\backyard\HrStaffAnnexInfoModel;
use App\Models\backyard\HrStaffDepartmentStoreRegionsPieceModel;
use App\Models\backyard\HrStaffDepartmentStoreRegionsPieceCategoryModel;
use App\Models\backyard\HrStaffInfoExtendModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Models\backyard\HrStaffManageDepartmentModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\OsStaffInfoExtendModel;
use app\models\backyard\PersistentOutsourceStaffInfoModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Models\backyard\VehicleInfoModel;
use App\Modules\My\library\Enums\SalaryEnums;
use App\Modules\My\Services\PayrollService;
use App\Repository\HrOutsourcingOrderRepository;
use App\Repository\HrStaffInfoPositionRepository;
use App\Repository\HrStaffAnnexInfoRepository;
use App\Services\StaffSupportStoreServer;
use App\Services\StaffService;


class StaffTask extends BaseTask
{

    /**
     * 员工状态检查告警
     * @return true
     */
    public function stateCheckAction()
    {
        $t = \App\Library\BaseService::getTranslation('zh');

        $sql    = "SELECT staff_info_id FROM `hr_staff_info` WHERE state = 1 and wait_leave_state = 0  and `leave_date` is not null and formal in (1, 4) and is_sub_staff = 0 ";
        $check1 = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check1)) {
            $this->logger->alert(['以下员工在职-有离职日期' => $check1]);
        }

        $sql    = "SELECT staff_info_id FROM `hr_staff_info` WHERE state = 2  and `leave_date` is  null and formal in (1, 4) and is_sub_staff = 0  ";
        $check2 = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check2)) {
            $this->logger->alert(['以下员工离职-没有离职日期' => $check2]);
        }

        $sql    = "SELECT staff_info_id  FROM `hr_staff_info` WHERE state = 1 and wait_leave_state = 1  and `leave_date` is  null and formal in (1, 4) and is_sub_staff = 0  ";
        $check3 = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check3)) {
            $this->logger->alert(['以下员工待离职-没有离职日期' => $check3]);
        }

        $sql    = "SELECT i.staff_info_id,i.`manger` , it.`value` FROM `hr_staff_info` as i inner join `hr_staff_items` as it on i.`staff_info_id` = it.`staff_info_id` and it.`item` = 'MANGER' WHERE i.`manger` != it.`value`  and `formal` in (1,4)";
        $check4 = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check4)) {
            $this->logger->alert(['以下员工-上级字段不对齐' => $check4,'staff_info_id' => array_column($check4,'staff_info_id')]);
        }
        $sql    = "SELECT `staff_info_id` ,formal ,hire_type  ,`stop_duties_date` ,`stop_duty_reason` , datediff(CURRENT_DATE(),stop_duties_date) as diff_days FROM `hr_staff_info` WHERE state = 3   and formal in (1, 4) and is_sub_staff = 0 and datediff(CURRENT_DATE(),stop_duties_date) > 7  ";
        $check5 = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check5)) {
            $EHS_stop_days = 7;
            if (isCountry('MY')) {
                $EHS_stop_days = 14;
            }
            foreach ($check5 as $key => &$v) {
                //调查中 15天恢复在职
                //EHS调查 个人代理一直停职
                //EHS调查 正式员工 动态 $EHS_stop_days
                if (($v['stop_duty_reason'] == 4 && $v['diff_days'] <= 15) || ($v['stop_duty_reason'] == 8 && in_array($v['hire_type'],
                            HrStaffInfoModel::$agentTypeTogether)) || ($v['stop_duty_reason'] == 8 && $v['diff_days'] < $EHS_stop_days)) {
                    unset($check5[$key]);
                }
                $v['停职原因'] = $t->_('stop_duty_reason_' . $v['stop_duty_reason']);
            }
            //排查
            $t_staff           = array_column($check5, 'staff_info_id');
            if($t_staff){
                $staff_info_id     = implode(',', $t_staff);
                $now               = date('Y-m-d H:i:s');
                $sql               = "SELECT staff_info_id FROM reinstatement_request where staff_info_id  in ($staff_info_id) and state = 2 and effective_date >= '$now' and handled = 0 ";
                $reinstatementInfo = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                $error_staff       = array_diff($t_staff, array_column($reinstatementInfo, 'staff_info_id'));
                //SELECT * FROM reinstatement_request WHERE staff_info_id =
                !empty($error_staff) && $this->logger->alert([
                    '以下员工停职超过7天' => $error_staff,
                    'staff_info_id'       => $error_staff,
                ]);
            }
        }
        //验证班次

        $shift_table = isCountry('MY') ? 'hr_staff_shift_v2' : 'hr_staff_shift_middle_date';
        $date        = date('Y-m-d', strtotime(" +2 day"));
        $sql         = "SELECT i.staff_info_id FROM
          `hr_staff_info` as i
          left join {$shift_table} as md on i.`staff_info_id` = md.`staff_info_id`
          and md.`shift_date` = '{$date}' where i.formal in  (1, 4)  and i.state in (1,3) and i.is_sub_staff = 0 
           and md.id is null";
        $check6      = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check6)) {
            $this->logger->alert(['没有班次' => $date, 'staff_info_id' => array_column($check6, 'staff_info_id')]);
        }

        // 检测外协黑名单异常数据
        $sql = "SELECT id,identity,reason_code,status,remove_staff_id FROM hr_out_sourcing_blacklist WHERE reason_code IS NULL OR identity IS NULL OR (`status`=2 AND (remove_staff_id IS NULL OR remove_date IS NULL)) and created_at >='2025-03-06 07:00:00'";
        $check7      = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check7)) {
            $this->logger->alert(['外协黑名单表数据异常' => $check7]);
        }

        if (isCountry('MY')) {
            $sql    = "select staff_info_id,node_department_id from hr_staff_info where formal in (1,4) and state != 2 and is_sub_staff = 0 and contract_company_id = 0";
            $check8 = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if (!empty($check8)) {
                $this->logger->alert(['以下员工合同公司为空' => $check8]);
            }
            $sql    = "select i.`staff_info_id` ,i.contract_company_id , d.`company_id`  from  hr_staff_info as i inner join sys_department as d on i.`node_department_id` = d.`id`  where d.`company_id` in ('15006','20001','60001','15050') and i.formal in (1,4) and i.contract_company_id != d.`company_id` ;";
            $check9 = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);

            if (!empty($check9)) {
                $real_map =  SalaryEnums::$salaryCompanyCodeMap;
                $setting_env_map =  SalaryEnums::$specialStaffTypeMap;
                $config = (new PayrollService())->specialStaffList();
                foreach ($check9 as $item) {
                    $config_key  = $setting_env_map[$real_map[$item['contract_company_id']]];
                    if(!in_array($item['staff_info_id'],explode(',',$config[$config_key]))) {
                        $this->logger->alert(['以下员工合同公司异常' => $item]);
                    }
                }
            }
        }
        $sql   = "select i.staff_info_id from  hr_staff_info as i left join `leave_manage_log`  as l on i.`staff_info_id` = l.`staff_info_id` left join `leave_manager` as m on i.`staff_info_id` = m.`staff_info_id`   where i.`is_history_leave` = 1 and l.`id` is null and m.`id` is null  and `formal` in (1,4) and `is_sub_staff` = 0  ";
        $check = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check)) {
            $this->logger->alert(['以下员工未离职过却被无标记为历史离职' => array_column($check, 'staff_info_id')]);
        }
        $sql   = "select i.staff_info_id from  hr_staff_info as i left join `leave_manage_log`  as l on i.`staff_info_id` = l.`staff_info_id` left join `leave_manager` as m on i.`staff_info_id` = m.`staff_info_id`   where i.`is_history_leave` = 0 and (l.`id` > 0  or m.`id`  > 0 )  and `formal` in (1,4) and `is_sub_staff` = 0  ";
        $check = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (!empty($check)) {
            $this->logger->alert(['以下员工离职过却未标记为历史离职' => array_column($check, 'staff_info_id')]);
        }
        $today = date('Y-m-d');
        $sql = "select id from staff_leave_remaining_days where task_date = '{$today}' and  leave_type  = 1";
        $check = $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        if (empty($check)) {
            $this->logger->alert(['年假发放失败 !!! 尽快处理。'=>$today]);
            $this->logger->alert(['年假发放失败 !!! 尽快处理。'=>$today]);
            $this->logger->alert(['年假发放失败 !!! 尽快处理。'=>$today]);
            $this->logger->alert(['年假发放失败 !!! 尽快处理。'=>$today]);

        }

        return true;
    }


    /**
     * 重置密码发送邮件
     * @return false|int
     */
    public function resetPasswordMailAction()
    {
        $service = new \App\Services\StaffService();
        return $service->doSendSmsAndMail();
    }

    /**
     * @Single
     * 固化员工信息
     * @return true
     * @throws Exception
     */
    public function hrStaffTransferAction($params): bool
    {
        //获取员工信息
        $today = date('Y-m-d');
        $formal = [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN];
        $conditions = 'formal in ({formal:array}) and ( state in ({state_1_3:array}) or (state = :state_2: and leave_date >= :before_30_day:)) and is_sub_staff = 0';
        $bind       = [
            'formal'        => $formal,
            'state_1_3'     => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
            'state_2'       => HrStaffInfoModel::STATE_RESIGN,
            'before_30_day' => date('Y-m-d 00:00:00', strtotime('-30 day')),
        ];

        $clearSql = "delete from  hr_staff_transfer where stat_date = ?";
        $clearBind = [$today];
        if (isset($params[0])) {
            $conditions            .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params[0];
            $clearSql             .= ' and staff_info_id = ?';
            $clearBind[]          = $params[0];
        }

        $staffList = HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        $staffListChunk = array_chunk($staffList,500);
        $db  = HrStaffTransferModel::beginTransaction($this);
        //清理
        $this->getDI()->get('db_backyard')->execute($clearSql,$clearBind);

        $carTypeList = array_flip(VehicleInfoModel::$vehicleTypeCategoryMY);

        foreach ($staffListChunk as $chunk) {
            $staffIds = array_column($chunk, 'staff_info_id');
            if (isCountry('MY')) {
                //工作所在州
                $staffProvinceCodeList = HrStaffItemsModel::find([
                    'conditions' => 'staff_info_id in ({staff_info_id:array}) and item = :item:',
                    'bind'       => ['staff_info_id' => $staffIds, 'item' => 'STAFF_PROVINCE_CODE'],
                ])->toArray();
                $staffProvinceCodeList = array_column($staffProvinceCodeList, 'value', 'staff_info_id');
                //车类型
                $vehicleTypeCategoryList = HrStaffItemsModel::find([
                    'conditions' => 'staff_info_id in ({staff_info_id:array}) and item = :item:',
                    'bind'       => ['staff_info_id' => $staffIds, 'item' => 'VEHICLE_TYPE_CATEGORY'],
                ])->toArray();
                $vehicleTypeCategoryList = array_column($vehicleTypeCategoryList, 'value', 'staff_info_id');
            }

            $staffInfoExtendData = HrStaffInfoExtendModel::find([
                'conditions' => 'staff_info_id in ({staff_info_id:array})',
                'bind'       => ['staff_info_id' => $staffIds,],
            ])->toArray();
            $staffInfoExtendData = array_column($staffInfoExtendData, null, 'staff_info_id');


            //角色
            $staffPositionList = HrStaffInfoPositionRepository::getStaffsBatch($staffIds);

            $staff = [];
            foreach ($chunk as $value) {
                $positionCategory                = $staffPositionList[$value['staff_info_id']] ?? '';
                $extend['role']                  = !empty($positionCategory) ? explode(',', $positionCategory) : [];
                $extend['job_title_grade_v2']    = $value['job_title_grade_v2'];
                $extend['rest_type']             = $value['rest_type'];
                $extend['manger']                = $value['manger'];
                $extend['project_num']           = $staffInfoExtendData[$value['staff_info_id']]['project_num'] ?? null;
                isCountry('MY') && $extend['vehicle_type_category'] = intval($carTypeList[$vehicleTypeCategoryList[$value['staff_info_id']] ?? '']);
                $staff[] = [
                    'stat_date'          => $today,
                    'staff_info_id'      => $value['staff_info_id'],
                    'state'              => $value['state'],
                    'wait_leave_state'   => $value['wait_leave_state'],
                    'job_title'          => $value['job_title'],
                    'sys_department_id'  => $value['sys_department_id'],
                    'node_department_id' => $value['node_department_id'],
                    'store_id'           => $value['sys_store_id'],
                    'formal'             => $value['formal'],
                    'leave_date'         => $value['leave_date'],
                    'week_working_day'   => $value['week_working_day'] ?: 0,
                    'province_code'      => $staffProvinceCodeList[$value['staff_info_id']] ?? '',
                    'extend'             => json_encode($extend),
                    'hire_type'          => $value['hire_type'],
                ];
            }
            (new HrStaffTransferModel())->batch_insert($staff);
        }
        $db->commit();
        return true;
    }

    /**
     * @Single
     * 固化外协员工信息
     * @return true
     * @throws Exception
     * php app/cli.php staff persistOutsourceStaffInfo
     */
    public function persistOutsourceStaffInfoAction($params): bool
    {
        //获取员工信息
        $today = date('Y-m-d');
        $formal = [HrStaffInfoModel::FORMAL_0];
        $conditions = 'formal in ({formal:array}) and ( state in ({state_1_3:array}) or (state = :state_2: and leave_date >= :before_30_day:)) and is_sub_staff = 0 and staff_type in(1,2,3)';
        $bind       = [
            'formal'        => $formal,
            'state_1_3'     => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
            'state_2'       => HrStaffInfoModel::STATE_RESIGN,
            'before_30_day' => date('Y-m-d 00:00:00', strtotime('-30 day')),
        ];

        $clearSql = "delete from persistent_outsource_staff_info where stat_date = ?";
        $clearBind = [$today];
        if (isset($params[0])) {
            $conditions            .= ' and staff_info_id = :staff_info_id:';
            $bind['staff_info_id'] = $params[0];
            $clearSql             .= ' and staff_info_id = ?';
            $clearBind[]          = $params[0];
        }

        $staffList = HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        $staffListChunk = array_chunk($staffList,500);
        $db  = HrStaffTransferModel::beginTransaction($this);
        //清理
        $this->getDI()->get('db_backyard')->execute($clearSql,$clearBind);

        foreach ($staffListChunk as $chunk) {
            $staffIds = array_column($chunk, 'staff_info_id');
            if (isCountry()) {
                $staffInfoExtendData = OsStaffInfoExtendModel::find([
                    'conditions' => 'staff_info_id in ({staff_info_id:array})',
                    'bind'       => ['staff_info_id' => $staffIds],
                ])->toArray();
                $staffInfoExtendData = array_column($staffInfoExtendData, null, 'staff_info_id');
            }

            //角色
            $staffPositionList = HrStaffInfoPositionRepository::getStaffsBatch($staffIds);

            $staff = [];
            foreach ($chunk as $value) {
                $positionCategory             = $staffPositionList[$value['staff_info_id']] ?? '';
                $extend['role']               = !empty($positionCategory) ? explode(',', $positionCategory) : [];

                $staff[] = [
                    'staff_info_id'      => $value['staff_info_id'],
                    'state'              => $value['state'],
                    'job_title'          => $value['job_title'],
                    'sys_department_id'  => $value['sys_department_id'],
                    'node_department_id' => $value['node_department_id'],
                    'store_id'           => $value['sys_store_id'],
                    'formal'             => $value['formal'],
                    'wait_leave_state'   => $value['wait_leave_state'],
                    'hire_type'          => $value['hire_type'],
                    'leave_date'         => $value['leave_date'],
                    'stat_date'          => $today,
                    'staff_type'         => $value['staff_type'],
                    'company_id'         => $staffInfoExtendData[$value['staff_info_id']]['company_item_id'] ?? null,
                    'company_name_ef'    => $value['company_name_ef'] ?? null,
                    'extend'             => json_encode($extend),
                ];
            }
            (new PersistentOutsourceStaffInfoModel())->batch_insert($staff);
        }
        $db->commit();
        echo "end!";
        return true;
    }

    /**
     * 初始化外协员工固化数据，仅执行一次
     * @return true
     * @throws Exception
     * php app/cli.php staff initOutsourceStaffInfo
     */
    public function initOutsourceStaffInfoAction()
    {
        //获取员工信息
        $startDate = '2025-01-01';
        $endDate = date('Y-m-d', strtotime('-1 day'));

        $dateRange = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
        $formal = [HrStaffInfoModel::FORMAL_0];
        $conditions = 'formal in ({formal:array}) and ( state in ({state_1_3:array}) or (state = :state_2: and leave_date >= :before_30_day:)) and is_sub_staff = 0';
        $bind       = [
            'formal'        => $formal,
            'state_1_3'     => [HrStaffInfoModel::STATE_ON_JOB, HrStaffInfoModel::STATE_SUSPEND],
            'state_2'       => HrStaffInfoModel::STATE_RESIGN,
            'before_30_day' => date('Y-m-d 00:00:00', strtotime('-30 day')),
        ];

        $staffList = HrStaffInfoModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
        ])->toArray();

        $staffListChunk = array_chunk($staffList, 500);
        $repo           = new App\Repository\HrOutsourcingOrderRepository();

        foreach (static::yieldData()($staffListChunk) as $chunk) {
            $db       = HrStaffTransferModel::beginTransaction($this);
            $staffIds = array_column($chunk, 'staff_info_id');
            if (empty($staffIds)) {
                continue;
            }

            //获取角色
            $staffPositionList = HrStaffInfoPositionRepository::getStaffsBatch($staffIds);

            //获取打卡数据
            $attendanceInfo = StaffWorkAttendanceModel::find([
                'conditions' => 'staff_info_id in({staff_ids:array}) and attendance_date in({date:array})',
                'bind'       => [
                    'staff_ids' => $staffIds,
                    'date'      => $dateRange,
                ],
                'columns'    => "concat(staff_info_id,'-',attendance_date) unique_key,company_name_ef",
            ])->toArray();
            $attendanceInfo = array_column($attendanceInfo, 'company_name_ef', 'unique_key');

            //外协工单
            $outsourcingOrder = $repo->getOutsourcingStaffOrder($staffIds, $startDate, $endDate);

            foreach (static::yieldData()($chunk) as $value) {
                $positionCategory = $staffPositionList[$value['staff_info_id']] ?? '';
                $extend['role']   = !empty($positionCategory) ? explode(',', $positionCategory) : [];

                $staff = [];
                foreach ($dateRange as $day) {
                    $uniqueKey   = sprintf('%s-%s', $value['staff_info_id'], $day);
                    $companyInfo = $attendanceInfo[$uniqueKey] ?? $outsourcingOrder[$value['staff_info_id']][$day] ?? 0;

                    $staff[] = [
                        'staff_info_id'      => $value['staff_info_id'],
                        'state'              => $value['state'],
                        'job_title'          => $value['job_title'],
                        'sys_department_id'  => $value['sys_department_id'],
                        'node_department_id' => $value['node_department_id'],
                        'store_id'           => $value['sys_store_id'],
                        'formal'             => $value['formal'],
                        'wait_leave_state'   => $value['wait_leave_state'],
                        'hire_type'          => $value['hire_type'],
                        'leave_date'         => $value['leave_date'],
                        'stat_date'          => $day,
                        'staff_type'         => $value['staff_type'],
                        'company_id'         => isCountry() ? $companyInfo : null,
                        'company_name_ef'    => isCountry() ? null : $companyInfo,
                        'extend'             => json_encode($extend),
                    ];
                }
                (new PersistentOutsourceStaffInfoModel())->batch_insert($staff);
            }
            $db->commit();
        }

        echo "end!";
        return true;
    }

    /**
     * @Single
     * 计算司龄
     * @return void
     */
    public function averageTenureAction()
    {
        //更新职级
        $average_tenure_sql = "UPDATE hr_staff_info SET average_tenure = TRUNCATE(TIMESTAMPDIFF(DAY, hire_date, NOW())/365,1) WHERE state IN (1,3) AND formal IN (1,4) AND is_sub_staff = 0 and hire_date is not null;";
        $db                 = $this->getDI()->get('db_backyard');
        return $db->execute($average_tenure_sql);
    }




    /**
     * 因为员工的工作制和轮休规则变化，需要重置员工的休息日和补的PH
     * @return void
     * @throws BusinessException
     */
    public function resetStaffOffDayAndPublicHolidayAction($param)
    {
        $staffService = new \App\Services\StaffService();
        if(!empty(intval($param[0]))){
            $staffService->restStaffOffDayPublicHolidayDay(intval($param[0]));
            die('跑完了 测试工号 ' . $param[0]);
        }

        try{
            while (true){
                $staffService->restStaffOffDayPublicHolidayDay();
            }
        }catch (BusinessException $e){
            echo $e->getMessage();
        }catch (ValidationException $ve){
            echo $ve->getMessage();
        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * 同步 staff items 项 到 hr_staff_info 表
     * @param $params
     *
     */
    public function sync_staff_itemsAction($params)
    {
        try {
            $num = $params[0] ?? 3;
            $items = [
                'NATIONALITY', 'WORKING_COUNTRY', 'MANGER', 'EDUCATION', // 国籍 工作所在国家 上级 最高学历
            ];

            $page = 1;
            $size = 50;
            do {
                $conditions = ' created_at >= :created_at: and working_country = 0 and formal = 1 and is_sub_staff  = 0 and state = 1 ';
                $bind = ['created_at' => date('Y-m-d H:i:s',strtotime('-'.$num.' days')),];

                if(isset($params[1])){
                    $staff_info_id = explode(',',$params[1]);
                    $conditions .= " and staff_info_id in ({staff_info_id:array})";
                    $bind = ['created_at' => date('Y-m-d H:i:s',strtotime('-'.$num.' days')),'staff_info_id'=>$staff_info_id];
                }
                $staffModels = HrStaffInfoModel::find([
                    'conditions' => $conditions,
                    'bind' => $bind,
                    'offset' => ($page - 1) * $size,
                    'limit' => $size,
                    'order' => 'staff_info_id desc',
                ]);

                $staffs = $staffModels->toArray();
                if ($staffs) {
                    $staffIds = array_column($staffs, 'staff_info_id');
                    $staffItems = HrStaffItemsModel::find([
                        'conditions' => ' staff_info_id in ({staff_ids:array}) and item in ({items:array})',
                        'bind' => ['staff_ids' => $staffIds, 'items' => $items],
                    ])->toArray();
                    $staffItemsGrpByStaffId = [];
                    foreach ($staffItems as $staffItem) {
                        $staffItemsGrpByStaffId[$staffItem['staff_info_id']][$staffItem['item']] = $staffItem['value'];
                    }

                    foreach ($staffModels as $staffModel) {
                        try {

                            $staffModel->working_country = $staffItemsGrpByStaffId[$staffModel->staff_info_id]['WORKING_COUNTRY'] ?? 0;
                            $staffModel->manger =  $staffItemsGrpByStaffId[$staffModel->staff_info_id]['MANGER'] ?? 0;
                            $staffModel->nationality = $staffItemsGrpByStaffId[$staffModel->staff_info_id]['NATIONALITY'] ?? 0;
                            $staffModel->education = $staffItemsGrpByStaffId[$staffModel->staff_info_id]['EDUCATION'] ?? 0;
                            $staffModel->save();
                            $this->echoInfoLog($staffModel->id . ' sync ' . $staffModel->staff_info_id . ' items ' .  json_encode([
                                    'working_country' => $staffItemsGrpByStaffId[$staffModel->staff_info_id]['WORKING_COUNTRY'] ?? 0,
                                    'manger' => $staffItemsGrpByStaffId[$staffModel->staff_info_id]['MANGER'] ?? 0,
                                    'education' => $staffItemsGrpByStaffId[$staffModel->staff_info_id]['EDUCATION'] ?? 0,
                                    'nationality' => $staffItemsGrpByStaffId[$staffModel->staff_info_id]['NATIONALITY'] ?? 0,
                                ], JSON_UNESCAPED_UNICODE));
                        } catch (\Exception $e) {

                            $this->echoErrorLog(json_encode(['Error_msg' => $e->getMessage(), 'Error_file' => $e->getFile(), 'Error_line' => $e->getLine()]));
                        }
                    }
                }
                $page++;
            } while ($staffs);

        } catch (\Exception $e) {
            $this->echoErrorLog(json_encode(['Error_msg' => $e->getMessage(), 'Error_file' => $e->getFile(), 'Error_line' => $e->getLine()]));
        }

    }


    public function create_sub_staffAction() {
        $current_day = date('Y-m-d');
        $support_list = HrStaffApplySupportStoreModel::find([
            'conditions' => 'status = 2 and sub_staff_info_id = 0 and  employment_end_date >= :employment_end_date:',
            'bind' => [
                'employment_end_date' => $current_day,
            ],
        ])->toArray();
        if(!empty($support_list)) {
            $staff_info_ids = array_column($support_list, 'staff_info_id');
            $staff_list = HrStaffInfoModel::find([
                'conditions' => "formal = 1 and is_sub_staff = 0 and staff_info_id in ({staff_ids:array})",
                'bind' => [
                    'staff_ids' => $staff_info_ids,
                ],
                'columns' => 'staff_info_id,name,sex,identity,mobile,personal_email,job_title,sys_store_id,sys_department_id,node_department_id,state,week_working_day,rest_type',
            ])->toArray();
            $staff_list = !empty($staff_list) ? array_column($staff_list, null, 'staff_info_id') : [];

            $staff_items = HrStaffItemsModel::find([
                'conditions' => "staff_info_id in ({staff_ids:array}) and item = 'MANGER'",
                'bind' => [
                    'staff_ids' => $staff_info_ids,
                ],
            ])->toArray();
            $staff_manager = !empty($staff_items) ? array_column($staff_items, 'value', 'staff_info_id') : [];

            foreach ($support_list as $key => $value) {
                $staff_info_id = $value['staff_info_id'];

                $staff_info = $staff_list[$staff_info_id];
                $staff_info['manager'] = $staff_manager[$staff_info_id] ?? '';
                echo "---------------------" . PHP_EOL;
                echo "申请信息 工号:{$staff_info_id},支援开始日期:{$value['employment_begin_date']},支援结束日日期:{$value['employment_end_date']}" . PHP_EOL;

                if($current_day > date('Y-m-d', strtotime($value['employment_begin_date']))) {
                    $value['employment_begin_date'] = $current_day;
                }
                $sub_staff_info_id = (new StaffSupportStoreServer())->createSubStaff($staff_info,
                    ['job_title' => $value['job_title_id'], 'store_id' => $value['store_id'], 'employment_begin_date' => $value['employment_begin_date']]);
                echo "主账号:" . $staff_info_id . PHP_EOL;
                echo "子账号:" . $sub_staff_info_id . PHP_EOL;


                if(!empty($sub_staff_info_id)) {
                    $this->getDI()->get('db_backyard')->updateAsDict(
                        'hr_staff_apply_support_store',
                        ['sub_staff_info_id' => $sub_staff_info_id],
                        'id = ' . $value['id']
                    );

                    $sync_ms_params[] = [
                        'id' => $value['id'],
                        'staff_id' => $sub_staff_info_id,
                        'master_staff' => $staff_info_id,
                        'begin_at' => strtotime($value['employment_begin_date']),
                        'end_at' => strtotime($value['employment_end_date']) + 86399,
                    ];

                    $result = (new StaffSupportStoreServer())->syncMsSupportApply($sync_ms_params);
                    echo '同步ms子账号结果:' . $result . PHP_EOL;
                }

                echo "---------------------" . PHP_EOL;
            }
        }

    }


    /**
     * @description:同步 hr_staff_department_store_regions_piece  到 hr_staff_manage 相关表  只需要执行一次就好了
     * @param null
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/10/9 11:06
     */
    public function sync_staff_manageAction($params)
    {
        //查询 hr_staff_department_store_regions_piece 表数据  大概 7w 数据
        $relations = HrStaffDepartmentStoreRegionsPieceModel::find()->toArray();
        //插入数据
        $departments = [];
        $stores      = [];
        $regions     = [];
        $pieces      = [];
        foreach ($relations as $v) {
            if (!empty($v['department'])) {
                $departments[$v['staff_info_id'].'-'.$v['department']] = [
                    'department_id'  => $v['department'],
                    'staff_info_id'  => $v['staff_info_id'],
                    'is_include_sub' => 0,
                    'created_staff'  => $v['creater'],
                    'type'           => 1,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,
                ];
            }

            if (!empty($v['store_id'])) {
                $stores[$v['staff_info_id'].'-'.$v['store_id']] = [
                    'store_id'      => $v['store_id'],
                    'staff_info_id' => $v['staff_info_id'],
                    'created_staff' => $v['creater'],
                    'type'          => 1,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,

                ];
            }
            if (!empty($v['region_id'])) {
                $regions[$v['staff_info_id'].'-'.$v['region_id']] = [
                    'region_id'     => $v['region_id'],
                    'staff_info_id' => $v['staff_info_id'],
                    'created_staff' => $v['creater'],
                    'type'          => 1,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,

                ];
            }
            if (!empty($v['piece_id'])) {
                $pieces[$v['staff_info_id'].'-'.$v['piece_id']] = [
                    'piece_id'      => $v['piece_id'],
                    'staff_info_id' => $v['staff_info_id'],
                    'created_staff' => $v['creater'],
                    'type'          => 1,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,

                ];
            }
        }
        $model = new BaseModel();

        $departments = array_chunk($departments, 1000);
        foreach ($departments as $k => $departments_data) {
            $info = $model->table_batch_insert($departments_data, 'db_backyard', 'hr_staff_manage_department');
            $this->echoInfoLog('部门 第'.$k.'次 插入 '.count($departments_data).' 状态 '.$info);
        }
        $stores = array_chunk($stores, 1000);
        foreach ($stores as $k => $stores_data) {
            $stores = $model->table_batch_insert($stores_data, 'db_backyard', 'hr_staff_manage_store');
            $this->echoInfoLog('网点 第'.$k.'次 插入 '.count($stores_data).' 状态 '.$info);

        }
        $regions = array_chunk($regions, 1000);
        foreach ($regions as $k => $regions_data) {
            $info = $model->table_batch_insert($regions_data, 'db_backyard', 'hr_staff_manage_region');
            $this->echoInfoLog('大区 第'.$k.'次 插入 '.count($regions_data).' 状态 '.$info);
        }
        $pieces = array_chunk($pieces, 1000);
        foreach ($pieces as $k => $pieces_data) {
            $info = $model->table_batch_insert($pieces_data, 'db_backyard', 'hr_staff_manage_piece');
            $this->echoInfoLog('片区 第'.$k.'次 插入 '.count($pieces_data).' 状态 '.$info);
        }
        $this->echoInfoLog('完毕!');
    }

    /**
     * @description:同步 hr_staff_department_store_regions_piece_category  到 hr_staff_manage 相关表  只需要执行一次就好了
     * @author: L.J
     * @time: 2022/10/14 10:46
     */
    public function sync_staff_manage_categoryAction($params)
    {
        //查询 hr_staff_department_store_regions_piece 表数据  大概 8500 数据
        $relations = HrStaffDepartmentStoreRegionsPieceCategoryModel::find()->toArray();
        //插入数据
        $departments      = [];
        $stores           = [];
        $regions          = [];
        $pieces           = [];
        $store_categories = [];
        foreach ($relations as $v) {
            if (!empty($v['department'])) {
                $departments[$v['staff_info_id'].'-'.$v['department']] = [
                    'department_id'  => $v['department'],
                    'staff_info_id'  => $v['staff_info_id'],
                    'is_include_sub' => 0,
                    'created_staff'  => $v['creater'],
                    'type'           => 2,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,
                ];
            }

            if (!empty($v['store_id'])) {
                $stores[$v['staff_info_id'].'-'.$v['store_id']] = [
                    'store_id'      => $v['store_id'],
                    'staff_info_id' => $v['staff_info_id'],
                    'created_staff' => $v['creater'],
                    'type'          => 2,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,
                ];
            }
            if (!empty($v['region_id'])) {
                $regions[$v['staff_info_id'].'-'.$v['region_id']] = [
                    'region_id'     => $v['region_id'],
                    'staff_info_id' => $v['staff_info_id'],
                    'created_staff' => $v['creater'],
                    'type'          => 2,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,
                ];
            }
            if (!empty($v['piece_id'])) {
                $pieces[$v['staff_info_id'].'-'.$v['piece_id']] = [
                    'piece_id'      => $v['piece_id'],
                    'staff_info_id' => $v['staff_info_id'],
                    'created_staff' => $v['creater'],
                    'type'          => 2,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,
                ];
            }
            if (!empty($v['store_category'])) {
                $store_categories[$v['staff_info_id'].'-'.$v['store_category']] = [
                    'store_category' => $v['store_category'],
                    'staff_info_id'  => $v['staff_info_id'],
                    'created_staff'  => $v['creater'],
                    'type'           => 2,
                    'created_at'     =>$v['created_at'],
                    'updated_staff'  =>10000,
                ];
            }
        }
        $model = new BaseModel();

        $departments = array_chunk($departments, 1000);
        foreach ($departments as $k => $departments_data) {
            $info = $model->table_batch_insert($departments_data, 'db_backyard', 'hr_staff_manage_department');
            $this->echoInfoLog('部门 第'.$k.'次 插入 '.count($departments_data).' 状态 '.$info);
        }
        $stores = array_chunk($stores, 1000);
        foreach ($stores as $k => $stores_data) {
            $stores = $model->table_batch_insert($stores_data, 'db_backyard', 'hr_staff_manage_store');
            $this->echoInfoLog('网点 第'.$k.'次 插入 '.count($stores_data).' 状态 '.$info);

        }
        $regions = array_chunk($regions, 1000);
        foreach ($regions as $k => $regions_data) {
            $info = $model->table_batch_insert($regions_data, 'db_backyard', 'hr_staff_manage_region');
            $this->echoInfoLog('大区 第'.$k.'次 插入 '.count($regions_data).' 状态 '.$info);
        }
        $pieces = array_chunk($pieces, 1000);
        foreach ($pieces as $k => $pieces_data) {
            $info = $model->table_batch_insert($pieces_data, 'db_backyard', 'hr_staff_manage_piece');
            $this->echoInfoLog('片区 第'.$k.'次 插入 '.count($pieces_data).' 状态 '.$info);
        }
        $this->echoInfoLog('完毕!');
    }


    /**
     * 一次性脚本 th 刷社保离职日期
     * @return void
     */
    public function one_staff_social_security_leave_dateAction()
    {
        $log = 'staff_social_security_leave_dateAction , 系统所在国家: ' . strtoupper(env('country_code',
                'TH')) . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:i:s') . PHP_EOL;
        $data = (new \App\Services\StaffService())->staff_social_security_leave_date_logic();
        $log .= ",总数 : " .$data['count_num'] . PHP_EOL;
        $log .= ",成功数 : " .$data['success_num'] . PHP_EOL;
        $log .= "结束时间: " . date('Y-m-d H:i:i:s') . PHP_EOL;
        $this->echoInfoLog($log);
    }

    /**
     * 添加社保离职日期 一秒一次
     * @return void
     */
    public function staff_social_security_leave_dateAction()
    {
        try {
            $redis                      = $this->getDI()->get("redis");
            $staffService = new StaffService();
            while ($redis_data = $redis->rpop(RedisListEnums::STAFF_SOCIAL_SECURITY_LEAVE_DATE)) {
                if (!empty($redis_data)) {
                    $redis_data = json_decode($redis_data, true);
                    if (!$staffService->staffSocialSecurityLeaveDate($redis_data['staff_info_id'],$redis_data['leave_date'])) {
                        continue;
                    }
                }
            }
            $this->echoInfoLog('success');
        } catch (Exception $e) {
            $this->echoErrorLog('staff_social_security_leave_dateAction Error '.$e->getMessage().$e->getTraceAsString());
        }
    }

    /**
     * 户口簿，历史数据
     * @throws Exception
     */
    public function residence_bookletAction()
    {
        $page = 1;
        $page_size = 1000;
        $batch_count = 1;//批次

        $staffInfoRepository = new \App\Repository\StaffInfoRepository();

        $staffInfoList = $staffInfoRepository->getStaffInfoList($page, $page_size, ['formal' => [HrStaffInfoModel::FORMAL_1, HrStaffInfoModel::FORMAL_INTERN]], ['staff_info_id']);

        $log =  PHP_EOL . "处理批次[batch=$batch_count]: start = $batch_count, per_length = $page_size-$page " . PHP_EOL;
        echo $log;
        $this->logger->info($log);

        while(!empty($staffInfoList)){

            $insertData = [];
            foreach ($staffInfoList as $oneData) {
                $data['staff_info_id'] = $oneData['staff_info_id'];
                $data['annex_path_front'] = 'https://tc-static-asset-internal.flashexpress.com/workOrder/1710408820-d3c007f92b47411eaa4f09352234a866.jpeg';
                $data['annex_path_rear'] = 'https://tc-static-asset-internal.flashexpress.com/workOrder/1710408820-d3c007f92b47411eaa4f09352234a866.jpeg';
                $data['type'] = \App\Models\backyard\HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD;
                $data['audit_state'] = \App\Models\backyard\HrStaffAnnexInfoModel::AUDIT_STATE_PASSED;

                $conditions            = 'staff_info_id = :staff_info_id: and type = :type:';
                $bind['staff_info_id'] = $oneData['staff_info_id'];
                $bind['type']          = HrStaffAnnexInfoModel::TYPE_RESIDENCE_BOOKLET_CARD;
                $residenceBookletInfo  = HrStaffAnnexInfoRepository::getStaffAnnexInfoInfo('*', $conditions, $bind);
                if(empty($residenceBookletInfo)) {
                    $insertData[] = $data;
                }
            }
            if(!empty($insertData)) {
                (new \App\Models\backyard\HrStaffAnnexInfoModel())->batch_insert($insertData);
            }

            // 取下一批次同步员工
            $batch_count++;
            $page++;
            $staffInfoList =  $staffInfoRepository->getStaffInfoList($page, $page_size);
            $log = PHP_EOL . "处理批次[batch=$batch_count]: start = $batch_count, per_length = $page_size-$page " . PHP_EOL;
            echo $log;
            $this->logger->info($log);
        }
    }
    
    /**
     * 发现异常以后的补偿数据脚本 默认不开启
     * my 个人代理 停职发送pdf邮件
     * @param $params
     * @return bool
     */
    public function suspensionAgentSendPdfAction($params)
    {
        if (!isCountry('MY') || empty($params[0])){
            return false;
        }
        $log_id = $params[0];
        $suspensionService = (new \App\Modules\My\Services\SuspensionService());
        $suspensionInfo = \App\Models\backyard\SuspensionManageLogModel::findFirst([
            'conditions' => 'id = :log_id:',
            'bind'       => [
                'log_id' => $log_id,
            ],
        ]);
        $suspensionInfo = $suspensionInfo->toArray();
        $staffInfo = $suspensionService->getStaffInfo($suspensionInfo['staff_info_id']);
        $suspensionService->agentCreatPdf($suspensionInfo, $staffInfo,$staffInfo['stop_duty_reason'],'');
        return true;
    }

    /**
     * 发现异常以后的补偿数据脚本 默认不开启
     * my 个人代理 离职发送pdf邮件
     * @param $params
     * @return bool
     */
    public function leaveAgentSendPdfAction($params)
    {
        if (!isCountry('MY') || empty($params[0])){
            return false;
        }
        $staff_info_id = $params[0];
        $stop_duties_date = $params[1];
        $staffLeaveService = new \App\Services\StaffLeaveService();
        $staffLeaveService->agentCreatPdf([
            'staff_info_id'=>$staff_info_id,
            'operate_id'=>'10000',
            'stop_duties_date'=>$stop_duties_date,
        ]);
        return true;
    }

    public function autoCreateEmailAction($params)
    {
        $date = empty($params[0]) ? date('Y-m-d', strtotime('-1 days')) : $params[0];
        (new \App\Services\StaffInfoService())->autoCreateEmailSend($date);
    }



}