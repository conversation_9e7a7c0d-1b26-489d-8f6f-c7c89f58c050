<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 6/14/23
 * Time: 10:38 AM
 */


use App\Library\FlashOss;
use App\Models\backyard\SchedulingSuggestionDaysModel;
use App\Services\WorkdayService;
use \App\Services\SchedulingSuggestionService;
use \App\Library\Enums\SchedulingSuggestionEnums;
use App\Models\backyard\AsyncImportTaskModel;
use App\Library\BaseService;
use App\Services\WorkDayImportService;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\WorkShiftService;
use App\Services\BllService;
use App\Models\backyard\HcmStaffPermissionInfoModel;
use App\Models\backyard\HcmRolePermissionInfoModel;
use App\Services\ExcelService;
use App\Services\WorkdaySettingService;

class WorkdayTask extends BaseTask
{
    public static $listExportAction = 'workday list_export';

    //轮休页面 每天跑当天的实际轮休 on 人数 到排班建议表 需要在hris 离职任务之后跑

    /**
     * @Single
     * @param $param
     * @return true|void
     */
    public function actSuggestNumAction($param){
        $date = date('Y-m-d');
        if(!empty($param[0])){
            $date = $param['0'];
        }

        //取出今天 配置建议的网点
        $suggestData = SchedulingSuggestionDaysModel::find([
            'conditions' => "stat_date = :date_at: and is_deleted = 0",
            'bind' => ['date_at' => $date]
        ]);

        if(empty($suggestData)){
            return true;
        }
        BaseService::setLanguage('en');

        $suggestServer = new SchedulingSuggestionService();
        $workdayServer = new WorkdayService();
        //获取对应网点的 实际轮休on 人数
        foreach ($suggestData as $suggestDatum) {
            $storeId = $suggestDatum->store_id;
            //获取建议数据
            $suggest = $suggestServer->getSchedulingSuggest($storeId,$date,$date);
            if(empty($suggest[$date])){
                echo "数据异常 {$storeId} {$date} 没建议数据";
            }

            $actNum = $workdayServer->workdaySuggest($storeId,$date,$date,$suggest);
            if($suggestDatum->position_type == SchedulingSuggestionEnums::POSITION_TYPE_DC_OFFICER){
                //仓管
                $key = "{$date}_{$suggestDatum->shift_date}";
                if(empty($suggestDatum->shift_date)){
                    $key = $date;
                }
                $num = $actNum['dc'][$key] ?? 0;
                $suggestDatum->actual_number = $num;
                $suggestDatum->update();

            }elseif($suggestDatum->position_type == SchedulingSuggestionEnums::POSITION_TYPE_COURIER){
                //快递员
                $num = $actNum['courier'][$date] ?? 0;
                $suggestDatum->actual_number = $num;
                $suggestDatum->update();
            }
        }
        echo '跑完了';
    }


    //轮休页面 导入操作
    public function workShiftImportAction(){
        $excelTask = AsyncImportTaskModel::findFirst([
            'conditions' => 'import_type = :type: and status = 1 and is_deleted = 0  ',
            'bind' => ['type' => AsyncImportTaskModel::BATCH_IMPORT_WORKDAY_ROLE]
        ]);

        if(empty($excelTask)){
            die('没有要执行的任务');
        }

        $argv = json_decode($excelTask->args_json,true);
        BaseService::setLanguage($argv['lang']);

        $param = $excelTask->toArray();
        $server = new WorkDayImportService();
        $server->taskRuleSave($param, $excelTask->operator_id);

        $this->echoInfoLog('workShiftImport end ', true);
    }

    /**
     * 列表导出
     * @return string
     * @throws \OSS\Core\OssException
     * @throws Exception
     */
    public function list_exportAction(): string
    {
        [$taskObj, $params] = $this->getHcmExcelTack(static::$listExportAction);
        if (empty($taskObj)) {
            return 'not task';
        }
        $service = reBuildCountryInstance(new WorkdayService());
        $service::setLanguage($params['lang'] ?? 'en');
        $returnData = [];
        $start      = 1;
        $sizeLen    = 500;
        while ($start) {
            $params['start']  = $start;
            $params['length'] = $sizeLen;
            $result           = $service->search_staff_workdays($params, $params['operator']);
            $data             = $service->export_rest_workday($result, $params['month']);
            if (empty($data)) {
                break;
            }
            $this->echoInfoLog("handing $start $sizeLen");
            ++$start;
            $returnData = array_merge($returnData, $data);
        }
        $this->echoInfoLog("total" . count($returnData));
        $header = [
            'emp.ID',
            'area',
            'branch',
            'position',
            'State',
            'Hire type',
            'Default rest day',
            'rest day',
        ];

        $return = $service->exportExcel($header, $returnData, $taskObj->file_name);
        if (empty($return['data'])) {
            return 'error';
        }
        $flashOss      = new FlashOss();
        $object = WorkdayService::WORKDAY_OSS_PATH . '/' . date('ymd') . '/' . $taskObj->file_name;
        $flashOss->uploadFile($object, $return['data']);
        $taskObj->path = $object;
        $taskObj->update();
        echo 'END' . PHP_EOL;
        return 'success';
    }
    //轮休页面 操作日志 导出任务 下载中心
    public function exportOperateLogAction()
    {
        $actionName = "workday" . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'exportOperateLog';
        [$taskObj, $params] = $this->getHcmExcelTack($actionName);
        if (empty($taskObj)) {
            return '';
        }

        $service = new WorkShiftService();
        $service::setLanguage($params['lang']);
        [$header, $export] = $service->logList($params, true);

        $updatePath = 'backyard/' . date('Y-m-d') . '/';
        $result     = (new BllService())->exportExcels($header, $export, $updatePath, $params['file_name']);

        if ($result['code']) {
            $taskObj->file_name = $params['file_name'];
            $taskObj->path      = $result['url'];
            $taskObj->status    = HcmExcelTackModel::TASK_STATUS_OVER;
            $taskObj->finish_at = gmdate("Y-m-d H:i:s", time() + $this->config->application->add_hour * 3600);
            $taskObj->update();
            $this->getDI()->get('logger')->info([
                'type' => 'down',
                'msg'  => json_encode([
                    "生成下载文件成功 hcm_excel_task id:" . $taskObj->id,
                    $params,
                    $result,
                ], JSON_UNESCAPED_UNICODE),
            ]);
        } else {
            $this->getDI()->get('logger')->info([
                'type' => 'down error',
                'msg'  => json_encode(["生成下载文件失败 hcm_excel_task id:" . $taskObj->id], JSON_UNESCAPED_UNICODE),
            ]);
        }
        return;
    }

    //上线刷一次权限数据
    public function workdayPermissionAction(){
        $staffData = HcmStaffPermissionInfoModel::find([
            'columns' => 'staff_info_id',
            'conditions' => 'permission_id = 43 and is_deleted = 0',
        ])->toArray();

        if(!empty($staffData)){
            $staffData = array_column($staffData, 'staff_info_id');
            $model = new HcmStaffPermissionInfoModel();
            $insert = [];
            foreach ($staffData as $staffId){
                $row['staff_info_id'] = $staffId;
                $row['permission_id'] = 4330;//查看权限
                $row['operator_id'] = 10000;
                $insert[] = $row;
                $row['staff_info_id'] = $staffId;
                $row['permission_id'] = 4331;//编辑权限
                $row['operator_id'] = 10000;
                $insert[] = $row;
            }
            $model->batch_insert($insert);
        }

        $roleData = HcmRolePermissionInfoModel::find([
            'columns' => 'role_id',
            'conditions' => 'permission_id = 43 and is_deleted = 0',
        ])->toArray();

        if(empty($roleData)){
            die('role 没数据');
        }
        $roleData = array_column($roleData, 'role_id');
        $model = new HcmRolePermissionInfoModel();
        $roleInsert = [];
        foreach ($roleData as $id){
            $row = [];
            $row['role_id'] = $id;
            $row['permission_id'] = 4330;//查看权限
            $row['operator_id'] = 10000;
            $roleInsert[] = $row;
            $row['role_id'] = $id;
            $row['permission_id'] = 4331;//编辑权限
            $row['operator_id'] = 10000;
            $roleInsert[] = $row;
        }
        $model->batch_insert($roleInsert);

        echo '跑完了';
    }

    //导入轮休配置 自由轮休天数 异步任务
    public function importWorkdaySettingAction(){

        $excelTask = AsyncImportTaskModel::findFirst([
            'conditions' => 'import_type = :type: and status = 1 and is_deleted = 0  ',
            'bind' => ['type' => AsyncImportTaskModel::WORKDAY_SETTING]
        ]);
        if(RUNTIME == 'dev'){
           // $excelTask = AsyncImportTaskModel::findFirst(3000465); //测试
        }
        if(empty($excelTask)){
            die('没有要执行的任务');
        }

        $argv = json_decode($excelTask->args_json,true);
        BaseService::setLanguage($argv['lang']);
        $param = $excelTask->toArray();
        (new WorkdaySettingService())->importResult($param, $excelTask->operator_id);
        $this->echoInfoLog('importWorkdaySetting', true);
    }



    //下载中心任务 轮休配置休息天数 导出
    public function workdaySettingExportAction($params)
    {
        $excelServer = new ExcelService();
        $params      = !isset($params[0]) ? $excelServer->getHcmExcelTaskArgv() : $params;
        if (empty($params[1])) {
            return $this->echoErrorLog('task not isset :' . $params[1], true);
        }
        $taskId = $params[1];
        $args   = json_decode(base64_decode($params[0]), true);
        BaseService::setLanguage($args['lang']);
        $service      = new WorkdaySettingService();
        $page         = 1;
        $args['size'] = 1000;
        $export       = [];
        while (true) {
            $args['page'] = $page;
            $service->param = $args;
            $data         = $service->getList();
            $data = $service->formatExport($data);
            if (empty($data)) {
                break;
            }
            $export = array_merge($export, $data);
            $page++;
        }

        $header     = $service->exportHeader();
        $updatePath = 'backyard/' . date('Y-m-d') . '/';
        $result     = (new BllService())->exportExcels($header, $export, $updatePath, $args['file_name']);

        $res = $excelServer->updateTask($taskId, $args['file_name'], $result['url']);
        $this->logger->info('VanCourierExport 导出结果' . $res);
    }





}