<?php

/**
 *
 * 根据外协考勤数据，跑外协加班数据考勤日期
 * 获取数据的范围：T-2 到 T-4的数据
 * 脚本命令：
 *  php app/cli.php hub_ot_attendance_statistics main
 *
 * 运行周期：
 * 每天外协考勤脚本跑完后
 * 暂定四点
 *
 */

use App\Library\DateHelper;
use app\models\backyard\HubOutsourcingOvertimeModel;
use app\models\backyard\OutsourcingHubStaffAttendanceModel;
use App\Repository\HrOutsourcingOrderRepository;
use App\Repository\HubOutsourcingOvertimeDetailRepository;
use App\Repository\SysStoreRepository;
use App\Services\HrShiftService;
use App\Services\HubOsAttendanceService;

class HubOtAttendanceStatisticsTask extends BaseTask
{
    /**
     * 脚本入口
     */
    public function mainAction($params = [])
    {
        if (!empty($params[0])) {
            if (!preg_match("/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/", $params[0])) {
                echo "请传递日期：格式为Y-m-d";
                return false;
            }
            if (!preg_match("/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/", $params[1])) {
                echo "请传递日期：格式为Y-m-d";
                return false;
            }
            $beginDate =  $params[0];
            $endDate = $params[1];
        } else {
            $beginDate = date("Y-m-d", strtotime('-2 day'));
            $endDate   = date("Y-m-d", strtotime('-1 day'));
        }

        $storeInfo = (new SysStoreRepository())->getPdcHikStore();
        $params['pdc_store_ids'] = empty($storeInfo) ? [] : array_column($storeInfo, 'id');
        $params['is_pdc'] = HubOsAttendanceService::IS_PDC_NO;
        // 获取符合条件的外协加班数据
        $otList = $this->getOtData($beginDate, $endDate);
        if (empty($otList)) {
            $emptyLog = "==========日期：{$beginDate}-{$endDate} 没有加班数据更新==============\n";
            echo $emptyLog;
            $this->getDI()->get('logger')->write_log($emptyLog, 'info');
            return false;
        }

        $dataLog = "=========== 数据共计{".count($otList)."}条 ==============\n";
        echo $dataLog;
        $this->getDI()->get('logger')->write_log($dataLog, 'info');

        $sql = "UPDATE hub_outsourcing_overtime SET attendance_date = CASE id";
        // 数据处理
        $ids        = [];
        $allIds     = array_column($otList, 'id');
        $shift_list = (new HrShiftService())->getList(['is_all' => true]);
        $shift_list = array_column($shift_list, null, 'id');

        $otStaffList = [];

        foreach ($otList as $key => $item) {
            // 加班所属班次的开始时间
            $shift_start_time = $item['ot_date'].' '.$shift_list[$item['shift_id']]['start'].':00';

            $cur_time_stamp = $item['ot_date'];
            if (!empty($params[0])) {
                $cur_time_stamp = $params[0];
            }
            // 当日12点
            $cur_date_time = date('Y-m-d 12:00:00', strtotime($cur_time_stamp));

            // 加班所属班次的开始时间 >= 当日的12点 考勤日属于第二天
            if (strtotime($shift_start_time) >= strtotime($cur_date_time)) {
                $attendanceDate = date('Y-m-d', strtotime($cur_date_time));
                $sql            .= " WHEN {$item['id']} THEN '{$attendanceDate}'";
                $ids[]          = $item['id'];

                $otStaff['attendance_date'] = $attendanceDate;
                $otStaff['ot_id']           = $item['id'];

                $otStaffList[] = $otStaff;
            }

            // 加班所属班次的开始时间 < 当日的12点，考勤日属于前一天
            if (strtotime($shift_start_time) < strtotime($cur_date_time)) {
                $attendanceDate = date('Y-m-d', strtotime('-1 day', strtotime($cur_date_time)));
                $sql            .= " WHEN {$item['id']} THEN '{$attendanceDate}'";
                $ids[]          = $item['id'];

                $otStaff['attendance_date'] = $attendanceDate;
                $otStaff['ot_id']           = $item['id'];

                $otStaffList[] = $otStaff;
            }
        }

        // 经过计算 未匹配到数据的情况
        if (empty($ids)) {
            $emptyAttLog = "==========日期：{$beginDate}-{$endDate} 未匹配到需要更新的数据==============\n";
            echo $emptyAttLog;
            $this->getDI()->get('logger')->write_log($emptyAttLog, 'info');
            return false;
        }

        $diffId = array_diff($allIds, $ids);
        if (!empty($diffId)) {
            $noRuleLog = "============未匹配到规则的数据id为：".implode(',', $diffId)."==================\n";
            echo $noRuleLog;
            $this->getDI()->get('logger')->write_log($dataLog, 'info');
        }

        $sql .= " END WHERE id IN (".implode(',', $ids).")";

        echo $sql."\n";
        $this->getDI()->get('logger')->write_log($sql."\n", 'info');

        $db = $this->getDI()->get('db_backyard');

        $db->begin();

        $result = $db->execute($sql);
        if (!$result) {
            $db->rollback();
            $this->getDI()->get('logger')->write_log(['HubOtAttendanceStatisticsTask' => $sql], 'error');
            return false;
        }

        foreach ($otStaffList as $oneData) {
            $detailList = HubOutsourcingOvertimeDetailRepository::getHubOtDetailList($oneData['ot_id']);
            if(empty($detailList)) {
                continue;
            }
            foreach ($detailList as $oneStaff) {
                $res = $db->updateAsDict("outsourcing_hub_staff_attendance", ['is_ot' => OutsourcingHubStaffAttendanceModel::IS_OT_YES, 'ot_id' => $oneData['ot_id']], ["conditions" => 'staff_info_id = ? and hub_attendance_date = ? and clocking_time != ? and is_ot = ?', 'bind'=> [$oneStaff['staff_id'], $oneData['attendance_date'], '0000-00-00 00:00:00', OutsourcingHubStaffAttendanceModel::IS_OT_NO]]);
                if(!$res){
                    $db->rollback();
                    $this->getDI()->get('logger')->write_log(['HubOtAttendanceStatisticsTask_staff_data' => $oneData], 'error');
                    return false;
                }
            }

        }

        $db->commit();

        $overLog = "============脚本运行完毕==============\n";
        echo $overLog;
        $this->getDI()->get('logger')->write_log($overLog."\n", 'info');
        return true;
    }

    /**
     * 获取符合t-2 到 t-1 的外协加班数据
     * @param $beginDate
     * @param $endDate
     * @return Array
     */
    private function getOtData($beginDate, $endDate): array
    {
        $builder = $this->modelsManager->createBuilder();
        $colum   = "ot.id, ot.ot_date, ot.attendance_date, ot.start_time, ot.end_time, ot.apply_state,ot.shift_id";
        $builder->from(['ot' => HubOutsourcingOvertimeModel::class]);
        $builder->andWhere("ot.ot_date >= :b_time:", ['b_time' => $beginDate]);
        $builder->andWhere("ot.ot_date <= :e_time:", ['e_time' => $endDate]);
        $builder->andWhere("ot.attendance_date IS NULL OR ot.attendance_date = ''");
        $builder->andWhere("ot.apply_state = :apply_state:", ['apply_state' => 2]);
        $builder->andWhere("ot.shift_id > :shift_id:", ['shift_id' => 0]);
        $builder->orderBy('ot.id DESC');
        $builder->columns($colum);

        //兼容是否是pdc
        $sql = '';
        if(!empty($params['is_pdc']) && $params['is_pdc'] == HubOsAttendanceService::IS_PDC_NO) {
            $sql = 'ot.store_id not in ({pdc_store_ids:array})';
        }

        if(!empty($params['is_pdc']) && $params['is_pdc'] == HubOsAttendanceService::IS_PDC_YES) {
            $sql = 'ot.store_id in ({pdc_store_ids:array})';
        }

        if(!empty($sql) && !empty($params['pdc_store_ids'])) {
            $builder->andWhere($sql, ['pdc_store_ids' => $params['pdc_store_ids']]);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据加班时间去捞考勤数据
     * @param array $otDateList
     * @param array $staffIds
     * @return array
     */
    private function getAttendanceData(array $otDateList, array $staffIds)
    {
        $list = [];
        if (empty($otDateList) || empty($staffIds)) {
            return $list;
        }
        $builder = $this->modelsManager->createBuilder();
        $colum   = "oa.id,
        oa.staff_info_id,
        oa.hub_attendance_date,
        oa.clocking_date,
        CONVERT_TZ(oa.clocking_time, '+00:00', '".get_sys_timezone()."' ) AS clocking_time,
        CONVERT_TZ(oa.started_at, '+00:00', '".get_sys_timezone()."' ) AS started_at,
        CONVERT_TZ(oa.end_at, '+00:00', '".get_sys_timezone()."' ) AS end_at";
        $builder->from(['oa' => OutsourcingHubStaffAttendanceModel::class]);
        $builder->andWhere("oa.hub_attendance_date IN ({hub_attendance_date:array})",
            ['hub_attendance_date' => $otDateList]);
        $builder->andWhere("oa.staff_info_id IN ({staff_info_ids:array})", ['staff_info_ids' => $staffIds]);
        $builder->orderBy('oa.id DESC');
        $builder->columns($colum);
        $data = $builder->getQuery()->execute()->toArray();
        if (empty($data)) {
            return $list;
        }
        foreach ($data as $key => $val) {
            $list[$val['hub_attendance_date']][$val['staff_info_id']] = $val;
        }
        return $list;
    }


    /**
     * 跑历史数据
     * @param array $params
     */
    public function rush_historyAction($params = [])
    {
        if(empty($params[0])) {
            echo '未传日期区间';
            return false;
        }
        $params[1] = empty($params[1]) ? $params[0] : $params[1];
        $historyDates = DateHelper::DateRange(strtotime($params[0]), strtotime($params[1]));
        $logger = $this->getDi()->get('logger');
        foreach ($historyDates as $oneDate) {
            $res = $this->mainAction([0 => $oneDate]);
            if($res) {
                $logger->info('[HubOtAttendanceStatisticsTask] rush_historyAction:' . $oneDate . ":success");
                echo $oneDate . ":success" . PHP_EOL;
            } else {
                $logger->info('[HubOtAttendanceStatisticsTask] rush_historyAction:' . $oneDate . ":fail");
                echo $oneDate . ":fail" . PHP_EOL;
            }
        }
    }

    /**
     * 修复 hub 加班 加班日期。
     * @param array $params
     * @return bool
     */
    public function fix_ot_dateAction($params = [])
    {
        $startTime = !empty($params[0]) ? $params[0] : '2023-11-29 19:00:00';
        $otList = HubOutsourcingOvertimeModel::find([
            'conditions' => 'created_at >= :created_at:',
            'bind'       => [
                'created_at' => $startTime,
            ],
        ])->toArray();

        if(empty($otList)) {
            return false;
        }
        $db_by  = $this->getDI()->get('db_backyard');

        $log = 'fix_ot_dateAction' . PHP_EOL;
        foreach ($otList as $oneData) {
            $detail = HubOutsourcingOvertimeDetailRepository::getHubOtDetailInfo($oneData['id']);
            if(empty($detail)) {
                continue;
            }

            $order = HrOutsourcingOrderRepository::getHubOrderInfo($detail['outsourcing_order_serial_no']);
            if(empty($order)) {
                continue;
            }
            $update_data['ot_date'] = $order['employment_date'];
            $flag = $db_by->updateAsDict('hub_outsourcing_overtime', $update_data, 'id = ' . $oneData['id']);
            if (!$flag) {
                $log .= "更新失败: ot_id = {$oneData['id']}, ot_date = " . $order['employment_date'] . PHP_EOL;
            }
            $log .= "更新成功: ot_id = {$oneData['id']}, ot_date_after = " . $order['employment_date'] . ', ot_date_before = ' . $oneData['ot_date']  . PHP_EOL;

        }
        $this->logger->info($log);

        echo "success";
    }


    /**
     * 将 历史 hub 加班数据 跑入 hub 考勤表
     */
    public function rush_otAction()
    {
        $params['page'] = 0;
        $params['page_size'] = 10;

        $db = $this->getDI()->get('db_backyard');

        $hubOutsourcingOvertimeRepository = new \App\Repository\HubOutsourcingOvertimeRepository();
        do {
            $params['page'] = $params['page'] + 1;

            $list = $hubOutsourcingOvertimeRepository->getOrderList($params);
            if(empty($list)) {
                break;
            }

            foreach ($list as $key => $oneData) {
                if(empty($oneData['attendance_date'])) {
                    continue;
                }
                if(empty($oneData['staff_id'])) {
                    continue;
                }

                $db->updateAsDict("outsourcing_hub_staff_attendance", ['is_ot' => OutsourcingHubStaffAttendanceModel::IS_OT_YES, 'ot_id' => $oneData['id']], ["conditions" => 'staff_info_id = ? and hub_attendance_date = ? and clocking_time != ? ', 'bind'=> [$oneData['staff_id'], $oneData['attendance_date'], '0000-00-00 00:00:00']]);
            }
        } while (1);

        echo 'success';
    }

    /**
     * pdc 计算加班
     * @param $params
     */
    public function executeAction($params)
    {
        $date = !empty($params[0]) ? $params[0] : '';
        (new \App\Services\HubOsAttendanceService())->statisticsOt($date);
    }

}