<?php

use App\Library\BaseService;
use App\Library\FlashOss;
use App\Models\backyard\HrOperateLogsModel;
use App\Models\backyard\StaffLeaveQuestionnaireModel;
use App\Services\ExcelService;
use App\Services\HrStaffService;
use App\Services\LeaveManagerService;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\LeaveManagerModel;
use App\Models\backyard\SysStoreModel;
use App\Services\LeaveManageListService;
use App\Models\backyard\LeaveManageLogModel;
use App\Models\backyard\StaffResignModel;

class LeavemanagerTask extends BaseTask
{

    public function export_v2Action($params)
    {
        //获取任务参数
        $params = !isset($params[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $params;
        if (empty($params[1])) {
            return $this->echoErrorLog('task not isset :' . $params[1], true);
        }

        $this->logger->info('leavemanager_export_v2 ' . json_encode([$params[0] . $params[1]], JSON_UNESCAPED_UNICODE));
        $taskId = $params[1];
        $args   = json_decode(base64_decode($params[0]), true);
        BaseService::setLanguage($args['lang']);
        $t = BaseService::getTranslation($args['lang']);

        $header = [
            $t['staff_info_id'],                                //工号
            $t['name'],                                         //姓名
            $t['en_name'],                                      //英文名称
            $t['staff_job_title'],                              //职位
            $t['department'],                                   //部门
            $t['working_country'],                              //工作所在国家
            $t['hr_probation_field_sys_store_name'],            //所属网点
            $t['hr_probation_field_store_area_text'],           //所属区域
            $t['staff_state'],                                  //在职状态
            $t['hire_type'],                                    //雇佣类型
            $t['hire_date'],                                    //入职日期
            $t['staff_leave_date'],                             //离职/待离职日期
            $t['social_security_leave_date'],                   //社保离职日期 其他国家都有差异化 只有th走公共
            $t['unreturned_assets'],                            //未还资产
            $t['value_of_assets'] . "(THB)",                    //资产总扣费金额 原名 总价值
            $t['t_report_status'],                              //资产处理状态
            $t['unreturned_money'] . "(THB)",                   //未归还钱款总额+单位 原名 未归还钱款
            $t['unreceived_payment_by_courier'] . "(THB)",      //快递员未回款
            $t['cashier_should_remit'] . "(THB)",               //出纳应汇款
            $t['unreturned_loan'] . "(THB)",                    //借款未归还金额
            $t['unreturned_petty_cash'] . "(THB)",              //网点备用金未归还金额
            $t['other_noney'] . "(THB)",                        //其他
            $t['money_remand_state'],                           //钱款处理状态
            $t['sscourt_task_source'],                          //来源
            $t['resignation_application_date'],                 //离职申请日期
            $t['resign_type'],                                  //离职类型
            $t['leave_reason_old'],                             //离职原因(旧)
            $t['leave_reason'],                                 //离职原因
            $t['leave_scenario'],                               //离职场景
            $t['leave_reason_remark'],                          //离职原因备注
            $t['task_remark'],                                  //备注
            $t['leave_manage_remand_state'],                    //处理进度
        ];

        $leaveManagerService = new LeaveManageListService();
        $data                = $leaveManagerService->export($args);

        $file_name = $args['file_name'] . date('YmdHis') . rand(10000,99999) . '.xlsx';
        $result    = $leaveManagerService->exportExcel($header, $data, $file_name);
        if ($result['code']) {
            $flashOss = new FlashOss();
            //每个国家有每个国家的名字
            $json_url_name = 'leave_manage/' . date('Ymd') . '/' . $file_name;
            $flashOss->uploadFile($json_url_name, $result['data']);
            if (!empty($args['From']) && $args['From'] == 'fbi') {
                $dowLoadTaskServer = new \App\Services\DownLoadTaskService();
                $dowLoadTaskServer->updateExportManageInfo('hcm_leavemanager_export_' . $taskId,
                    $flashOss->signUrl($json_url_name, 20 * 86400), 3,$taskId);
            }
            $eb = new ExcelService();
            $eb->updateTask($taskId, $file_name, $json_url_name, HcmExcelTackModel::TYPE_LEAVE_MANAGER_EXPORT);
        }
    }

    public function fixAction($param)
    {
        $operaterId    = $param[0];
        $staff_info_id = $param[1];
        $leaveManage   = new LeaveManagerService();
        $data          = [
            [
                "staff_info_id"    => $staff_info_id,
                "type"             => 1,
                'leave_date'       => null,
                'operaterId'       => $operaterId,
                'wait_leave_state' => 0,
            ],

        ];
        $ret           = $leaveManage->update_staff_state($data);
        var_dump($ret);
    }


    //手动执行任务
    public function ImportStaffAction()
    {
        $this->echo('begin');
        $this->echo('=================hr_staff_info表离职员工数据导入 leave_manager');
        //写入leave_manager/leave_manage_log 员工表条件：离职或者待离职 正式员工/实习生 非子账号 离职日期大于2020-04-17
        $hr_staff_leave_count = HrStaffInfoModel::count([
            'conditions' => "(state = 2 or (state = 1 and wait_leave_state = 1)) and formal IN (1,4) and is_sub_staff = 0 and leave_date > '2020-04-17 00:00:00'",
        ]);

        $page_size   = 100;//每页多少条
        $total_count = $hr_staff_leave_count;
        $page_count  = ceil($total_count / $page_size);//总页数
        $this->echo('每页查询条数：', $page_size);
        $this->echo('总条数：', $total_count);
        $this->echo('总页数', $page_count);

        $leave_manager_model    = new LeaveManagerModel();
        $leave_manage_log_model = new LeaveManageLogModel();
        for ($page = 1; $page <= $page_count; $page++) {
            $this->echo('页码：', $page);
            $hr_staff_leave_list = HrStaffInfoModel::find([
                'conditions' => "(state = 2 or (state = 1 and wait_leave_state = 1)) and formal IN (1,4) and is_sub_staff = 0 and leave_date > '2020-04-17 00:00:00'",
                'limit'      => $page_size,
                'offset'     => ($page - 1) * $page_size,
                'order'      => 'staff_info_id desc',
            ])->toArray();

            $staff_info_ids  = array_column($hr_staff_leave_list, 'staff_info_id');
            $staff_store_ids = array_column($hr_staff_leave_list, 'sys_store_id');
            $this->echo(json_encode($staff_info_ids));

            $store_list = SysStoreModel::find([
                'columns'    => 'id,name,category',
                'conditions' => "id in ({store_ids:array})",
                'bind'       => [
                    'store_ids' => $staff_store_ids,
                ],
            ])->toArray();
            $store_list = array_column($store_list, null, 'id');

            $leave_manager_list = LeaveManagerModel::find([
                'conditions' => 'staff_info_id in ({staff_info_ids:array})',
                'bind'       => [
                    'staff_info_ids' => $staff_info_ids,
                ],
            ])->toArray();
            $leave_manager_list = array_column($leave_manager_list, null, 'staff_info_id');

            $batch_insert_params = [];
            $batch_insert_log    = [];
            foreach ($hr_staff_leave_list as $key => $value) {
                //leave_manager表操作
                if (!isset($leave_manager_list[$value['staff_info_id']])) {
                    $this->echo($value['staff_info_id'], '不在离职管理', '离职日期：',
                        date('Y-m-d', strtotime($value['leave_date'])));
                    $assets_remand_state = 1;
                    //网点类型 是 3，6 处理状态是已处理 总部员工也是已处理
                    if ((isset($store_list[$value['sys_store_id']]) && in_array($store_list[$value['sys_store_id']]['category'],
                                [3, 6])) || $value['sys_store_id'] == '-1') {
                        $assets_remand_state = 3;
                    }

                    $batch_insert_params[] = [
                        'staff_info_id'       => $value['staff_info_id'],
                        'assets_remand_state' => $assets_remand_state,
                        'leave_date'          => $value['leave_date'],
                        'money_remand_state'  => 1, //钱款处理状态 默认1未处理
                    ];
                } else {
                    $this->echo($value['staff_info_id'], '已经进入离职管理', '离职日期：',
                        date('Y-m-d', strtotime($value['leave_date'])));
                }

                //leave_manage_log 表操作
                $batch_insert_log[] = [
                    'staff_info_id'  => $value['staff_info_id'],
                    'serial_no'      => date('YmdHis') . rand(10000, 99999),
                    'leave_date'     => date('Y-m-d', strtotime($value['leave_date'])),
                    'leave_source'   => $value['leave_source'],
                    'leave_type'     => $value['leave_type'],
                    'leave_reason'   => $value['leave_reason'],
                    'leave_scenario' => $value['leave_scenario'],
                    'operate_id'     => -1,
                    'operate_name'   => 'system auto',
                ];
            }
            //leave_manager表操作
            if (!empty($batch_insert_params)) {
                $batch_insert_result = $leave_manager_model->batch_insert($batch_insert_params);
                $this->echo('leave_manager批量插入', '结果', $batch_insert_result);
            }

            //leave_manage_log 表操作
            if (!empty($batch_insert_log)) {
                $batch_insert_log_result = $leave_manage_log_model->batch_insert($batch_insert_log);
                $this->echo('leave_manage_log', '结果', $batch_insert_log_result);
            }
        }

        //assets_remand_state
        $this->echo('=================begin leave_manager表数据清洗 assets_remand_state=0的改成1');
        $update_result = $this->getDI()->get("db_backyard")->updateAsDict("leave_manager", ['assets_remand_state' => 1],
            ['conditions' => "assets_remand_state = 0"]);
        $this->echo('结果', $update_result);
        $this->echo('=================end leave_manager表数据清洗 assets_remand_state=0的改成1');

        //money_remand_state
        $this->echo('=================begin leave_manager表数据清洗 money_remand_state=0的改成1');
        $update_result = $this->getDI()->get("db_backyard")->updateAsDict("leave_manager", ['money_remand_state' => 1],
            ['conditions' => "money_remand_state = 0"]);
        $this->echo('结果', $update_result);
        $this->echo('=================end leave_manager表数据清洗 money_remand_state=0的改成1');

        $this->echo('end');
    }

    //手动执行任务
    public function ImportStaffResignAction()
    {
        $this->echo('=================begin 处理staff_resign待审批状态导入leave_manage_log');
        $leave_manage_log_model = new LeaveManageLogModel();
        $staff_resign_list      = StaffResignModel::find()->toArray();
        if (!empty($staff_resign_list)) {
            $batch_insert = [];
            foreach ($staff_resign_list as $key => $value) {
                $batch_insert[] = [
                    'staff_info_id' => $value['submitter_id'],
                    'serial_no'     => date('YmdHis').rand(10000, 99999),
                    'leave_date'    => date('Y-m-d', strtotime($value['leave_date'])),
                    'leave_source'  => 5,
                    'leave_type'    => 1,
                    'leave_reason'  => $value['reason'],
                    'by_staff_resign_id' => $value['resign_id'],
                    'by_approval_status' => $value['status'],
                    'cancel_reason' => $value['cancel_reason'],
                    'operate_id'    => -1,
                    'operate_name'  => 'system auto',
                ];
            }

            $batch_insert_log_result = $leave_manage_log_model->batch_insert($batch_insert);
            $this->echo('leave_manage_log', '结果', $batch_insert_log_result);

            //更新员工表
            $this->echo("更新员工表 is_history_leave");
            $staff_info_ids     = array_column($staff_resign_list, 'submitter_id');
            $staff_info_ids_str = implode(',', $staff_info_ids);
            $this->echo('工号:', $staff_info_ids_str);
            $update_result = $this->getDI()->get("db_backyard")->updateAsDict(
                "hr_staff_info",
                ['is_history_leave' => 1],
                ['conditions' => "staff_info_id in($staff_info_ids_str)"]
            );
            $this->echo("更新员工表 is_history_leave,结果:".$update_result);
        } else {
            $this->echo('未找到需要导入的离职申请待审批数据');
        }

        $this->echo('=================end 处理staff_resign待审批状态导入leave_manage_log');
    }

    public function echo(...$str)
    {
        $str = implode(' ', $str).PHP_EOL;
        echo $str;
    }


    /**
     * 获取员工数据
     * @param int $staff_info_id
     * @param int $max_id
     * @return mixed
     */
    protected function getStaffList(int $staff_info_id = 0, int $max_id = 0)
    {
        $limit = 500;
        if (RUNTIME == 'dev') {
            $limit = 2;
        }
        $sql = "select staff_info_id from hr_staff_info where staff_info_id > {$max_id} ";
        if ($staff_info_id) {
            $sql .= " and staff_info_id = $staff_info_id";
        }
        $sql .= " and  formal in (1,4) and is_sub_staff = 0 order by staff_info_id asc limit $limit";
        return $this->getDI()->get('db_rby')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
    }

    public function fixLogAction($params)
    {
        $staff_info_id          = $params[0] ?? 0;
        $max_id                 = 0;
        $leave_manage_log_model = new LeaveManageLogModel();
        $i                      = 1;
        while (true) {
            $staff_list = $this->getStaffList(intval($staff_info_id), intval($max_id));
            if (empty($staff_list)) {
                break;
            }
            $staff_info_ids = array_column($staff_list, 'staff_info_id');
            //by申请的有效离职
            $staff_resign_list = StaffResignModel::find([
                'columns'    => 'leave_date,submitter_id',
                'conditions' => 'submitter_id in ({submitter_id:array}) and status in (2,5) and source = 0',
                'bind'       => [
                    'submitter_id' => $staff_info_ids,
                ],
            ])->toArray();
            $resign_data       = [];
            if ($staff_resign_list) {
                foreach ($staff_resign_list as $item) {
                    $resign_data[$item['submitter_id']][] = $item['leave_date'];
                }
            }
            //员工操作记录
            $hr_operate_logs = HrOperateLogsModel::find([
                'columns'    => 'before,after,staff_info_id,created_at',
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and type = :type:',
                'bind'       => [
                    'staff_info_ids' => $staff_info_ids,
                    'type'           => 'staff',
                ],
                'order'      => 'id asc',
            ])->toArray();

            foreach ($hr_operate_logs as $hr_operate_log) {
                $hr_operate_logs[$hr_operate_log['staff_info_id']][] = $hr_operate_log;
            }
            $max_id = max($staff_info_ids);
            $str    = '第' . $i . '批 max_staff_id ' . $max_id . PHP_EOL;
            $this->logger->info($str);

            $sql = "delete from  leave_manage_log  where staff_info_id in (" . implode(',', $staff_info_ids) . ") ";
            $this->getDI()->get('db_backyard')->execute($sql);

            $insertData = [];
            foreach ($staff_list as $item) {
                $staff_logs = $hr_operate_logs[$item['staff_info_id']] ?? [];
                if (empty($staff_logs)) {
                    continue;
                }

                foreach ($staff_logs as $staff_log) {
                    $before_info = json_decode($staff_log['before'], true)['body'] ?? [];
                    $after_info  = json_decode($staff_log['after'], true)['body'] ?? [];
                    if (isset($before_info['state']) && isset($after_info['state']) && $before_info['state'] != $after_info['state'] && $after_info['state'] == 2) {
                        $leave_source                = $after_info['leave_source'] == 5 && !in_array(substr($after_info['leave_date'], 0, 10),
                            $resign_data[$item['staff_info_id']] ?? [])  ? '-1' : $after_info['leave_source'];
                        $insertItem['staff_info_id'] = $item['staff_info_id'];
                        $insertItem['leave_date']    = $after_info['leave_date'];
                        $insertItem['leave_source']  = $leave_source;
                        $insertItem['leave_reason']  = $after_info['leave_reason'];
                        $insertItem['leave_scenario']  = $after_info['leave_scenario']??null;
                        $insertItem['leave_type']    = $after_info['leave_type'];
                        $insertItem['created_at']    = $staff_log['created_at'];
                        $insertItem['updated_at']    = $staff_log['created_at'];
                        $insertItem['operate_id']    = -1;
                        $insertItem['operate_name']  = 'system auto';
                        $insertData[]                = $insertItem;
                    }
                }
            }
            if ($insertData) {
                $leave_manage_log_model->batch_insert($insertData);
            }
            $i++;
        }
    }

    public function exportQuestionnaireAction()
    {

        //获取任务参数
        $params = !isset($params[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $params;
        if (empty($params[1])) {
            return $this->echoErrorLog('task not isset :' . $params[1], true);
        }

        $taskId = $params[1];
        $args   = json_decode($params[0], true);
        BaseService::setLanguage($args['lang']);
        $t = BaseService::getTranslation($args['lang']);

        $header = [
            $t['staff_info_id'],                 //工号
            $t['name'],                          //姓名
            $t['en_name'],                       //英文名称
            $t['resignation_application_date'],  //离职申请日期
            $t['last_work_date'],                //最后工作日
            $t['leave_date'],                    //待离职日期
            $t['work_handover'],                 //工作交接人
            $t['pass_state'],                    //审批状态
            $t['leave_reason'],                  //离职原因
            $t['leave_reason_remark'],           //离职原因备注
            'GroupCEO',                          //GroupCEO
            $t['c_level'],                       //CLevel
            $t['bu'],                            //BU
            $t['dept_level_1'],                  //一级部门
            $t['dept_level_2'],                  //二级部门
            $t['dept_level_3'],                  //三级部门
            $t['dept_level_4'],                  //四级部门
            $t['store_area'],                    //大区
            $t['store_district'],                //片区
            $t['sys_store_name'],                //工作网点
            $t['position_type'],                 //职位性质
            $t['staff_job_title'],               //职位
            $t['job_title_grade'],               //职级
            $t['work_days'],                     //工作天数
            $t['staff_state'],                   //在职状态
            $t['hire_type'],                     //雇佣类型
            $t['formal_label'],                  //员工属性
            $t['hire_date'],                     //入职日期
            $t['hr_probation_field_manage_staff_id'],  //直接上级工号
            $t['direct_supervisor_name'],        //直接上级姓名
        ];
        $service = new LeaveManageListService();
        $questionnaire = $service->leaveQuestionnaireDataMap();
        ksort($questionnaire);

        $header = array_merge($header, array_values($questionnaire));

        $args['page'] = 1;
        $args['size'] = 500;
        $data = [];
        $hidden_job_grade_staffs = (new HrStaffService())->getCEmployee();
        while (true) {
            $data_tmp = $service->staffQuestionnaireList($args);
            if (empty($data_tmp)) {
                break;
            }
            $this->logger->info(['page'=>$args['page']]);
            foreach ($data_tmp as $key => $value) {
                $questionnaire_data = [];
                if (!empty($value['detail'])) {
                    $questionnaire_data = json_decode($value['detail'], true);
                    $questionnaire_data = array_column($questionnaire_data, 'value', 'leave_questionnaire_id');
                    ksort($questionnaire_data);
                }
                $solid_data = [];
                if(!empty($value['solid_data'])){
                    $solid_data = json_decode($value['solid_data'], true);
                }
                $t1 = [
                    $value['staff_info_id'],
                    $value['name'],
                    $value['name_en'],
                    $value['apply_time'],
                    $value['last_work_date'],
                    $value['log_leave_date'],
                    $value['show_work_handover'],
                    $value['audit_state_text'],
                    $value['leave_reason_text'],
                    $value['remark'],
                    $solid_data ? $solid_data['group_ceo'] : '',
                    $solid_data ? $solid_data['c_level'] : '',
                    $solid_data ? $solid_data['bu'] : '',
                    $solid_data ? $solid_data['dept_level_1'] : '',
                    $solid_data ? $solid_data['dept_level_2'] : '',
                    $solid_data ? $solid_data['dept_level_3'] : '',
                    $solid_data ? $solid_data['dept_level_4'] : '',
                    $solid_data ? $solid_data['region_name'] : '',
                    $solid_data ? $solid_data['piece_name'] : '',
                    $solid_data ? $solid_data['store_name'] : '',
                    $solid_data ? $t->_('position_type_' . $solid_data['position_type']) : '',
                    $solid_data ? $solid_data['job_title_name'] : '',
                    in_array($value['staff_info_id'],$hidden_job_grade_staffs) ? '-' : ( $solid_data ? 'F' . $solid_data['job_title_grade_v2'] : ''),
                    $solid_data ? $t['working_day_rest_type_' . $solid_data['week_working_day'] . $solid_data['rest_type']] : '',
                    $value['state_name'] ,
                    $solid_data ? $t['hire_type_'.$solid_data['hire_type']] : '',
                    $solid_data ? $t['formal_' . $solid_data['formal']] : '',
                    $value['hire_date'],
                    $solid_data ? $solid_data['manager_id'] : '',
                    $solid_data ? $solid_data['manager_name'] : '',
                ];
                $t2 = array_values($questionnaire_data);
                $data[] = array_merge($t1, $t2);
            }

            $args['page']++;
        }

        $file_name = $args['file_name'];
        $result    = $service->exportExcel($header, $data, $file_name);
        if ($result['code']) {
            $flashOss = new FlashOss();
            //每个国家有每个国家的名字
            $json_url_name = 'leave_questionnaire/' . date('Ymd') . '/' . $file_name;
            $flashOss->uploadFile($json_url_name, $result['data']);
            if (!empty($args['From']) && $args['From'] == 'fbi') {
                $dowLoadTaskServer = new \App\Services\DownLoadTaskService();
                $dowLoadTaskServer->updateExportManageInfo('hcm_leavemanager_export_' . $taskId,
                    $flashOss->signUrl($json_url_name, 20 * 86400), 3,$taskId);
            }
            $eb = new ExcelService();
            $eb->updateTask($taskId, $file_name, $json_url_name);
        }
    }


}