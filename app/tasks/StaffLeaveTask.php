<?php


use App\Library\BaseService;
use App\Services\StaffLeaveService;
use App\Services\SuspensionService;

class StaffLeaveTask extends BaseTask
{
    /**
     * @Single
     * 未回公款 停职变离职
     * @param $params
     * @return void
     * @throws Exception
     */
    public function unRefundedAction($params)
    {
        BaseService::setLanguage(getCountryDefaultLang());
        $staff_ids = [];
        if (!empty($params[0])) {
            $staff_ids = explode(',', $params[0]);
        }
        $service = new StaffLeaveService();
        $service = reBuildCountryInstance($service);
        $service->handleUnRefunded($staff_ids);
    }

    public function aaAction(){
        $waitReinstatementStaff = (new SuspensionService)->getWaitReinstatementList();
        dd($waitReinstatementStaff);
    }

}