<?php

use App\Library\BaseService;
use App\Library\FlashOss;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\SuspensionService;
use App\Services\ExcelService;

/**
 * Author: Bruce
 * Date  : 2023-06-08 18:02
 * Description:停职管理导出
 */
class StaffSuspensionExportTask extends BaseTask
{
    public function mainAction($input)
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('StaffSuspensionExportTask start', true);

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog('StaffSuspensionExportTask params :'.json_encode($params, JSON_UNESCAPED_UNICODE), true);

        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;

        $suspensionService = new SuspensionService();
        //获取数据
        $list = [];
        $params['page_num'] = 1;
        $params['page_size'] = 500;
        while (true) {
            $data_tmp = $suspensionService->getList($params);
            if (empty($data_tmp['list'])) {
                break;
            }
            $list = array_merge($list, $data_tmp['list']);
            $params['page_num']++;
        }

        //设置语言
        $t = BaseService::getTranslation($params['lang']);

        $new_data = [];
        $header   = [
            $t['staff_info_id'], //工号
            $t['name'], //姓名
            $t['hire_type'], //雇佣类型
            $t['job_title'], //职位
            $t['node_department_name'],  //所属部门
            $t['sys_store_name'], //所属网点
            $t['regional'], //所属大区
            $t['area'], //所属片区
            $t['stop_duties_date'], //停职日期
            $t['stop_duty_reason'], //停职原因
            $t['audit_state_text'], //恢复在职申请审批
            $t['staff_state'], //在职状态
            $t['hire_date'], //在职状态
            $t['stop_duties_count'], //停职次数
        ];

        foreach ($list as $key => $value) {
            $new_data[] = [
                $value['staff_info_id'],
                $value['name'],
                $value['hire_type_text'], //雇佣类型
                $value['job_title_name'],
                $value['department_name'],
                $value['store_name'],
                $value['region_name'],
                $value['piece_name'],
                $value['suspension_date'],
                $value['reason'],
                $value['audit_state_text'],
                $value['state_text'],
                $value['hire_date'],
                $value['suspension_number'],
            ];
        }

        $file_data = $suspensionService->exportExcel($header, $new_data, $file_name);

        $flashOss  = new FlashOss();
        $ossObject = 'staff_suspension/'.date('Ymd').'/'.$excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('StaffSuspensionExportTask end', true);
    }


    //停职记录明细导出 各国表头不一样
    public function export_detailAction(){
        //获取任务参数
        $input = (new ExcelService())->getHcmExcelTaskArgv();
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('export_detail start', true);

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog('export_detail params :'.json_encode($params, JSON_UNESCAPED_UNICODE), true);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;

        $suspensionService = new SuspensionService();
        $suspensionService = reBuildCountryInstance($suspensionService);
        $suspensionService::setLanguage($params['lang']);
        [$list,$header]  = $suspensionService->getDetailListHeader($params);

        $file_data = $suspensionService->exportExcel($header, $list, $file_name);

        $flashOss  = new FlashOss();
        $ossObject = 'staff_suspension/'.date('Ymd').'/'.$excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('export_detail end', true);

    }

    /**
     * 停职申请-导出
     * @param $input
     * @throws \OSS\Core\OssException
     */
    public function audit_listAction($input)
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('StaffSuspensionExportTask start', true);

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog('StaffSuspensionExportTask params :'.json_encode($params, JSON_UNESCAPED_UNICODE), true);

        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;

        $suspensionService = new SuspensionService();
        //获取数据
        $list = [];
        $params['page_num'] = 1;
        $params['page_size'] = 500;
        while (true) {
            $data_tmp = $suspensionService->getAuditListQuery($params);
            if (empty($data_tmp)) {
                break;
            }
            $list = array_merge($list, $data_tmp);
            $params['page_num']++;
        }

        //设置语言
        $t = BaseService::getTranslation($params['lang']);

        $new_data = [];
        $header   = [
            $t['title_serial_no'], //审批编号
            $t['staff_info_id'], //工号
            $t['name'], //姓名
            $t['job_title'], //职位
            $t['node_department_name'],  //所属部门
            $t['sys_store_name'], //所属网点
            $t['staff_state'], //在职状态
            $t['by_leave_status'], //审批状态
            $t['ot_applicant'], //申请人
            $t['by_apply_time'], //申请时间
            $t['handle_type'], //处理状态
            $t['stop_duties_date'], //停职日期
            $t['last_audit_name'], //最后审批人
            $t['last_audit_time'], //最后审批日期
        ];

        foreach ($list as $key => $value) {
            $new_data[] = [
                $value['serial_no'],
                $value['staff_info_id'],
                $value['name'],
                $value['job_title_name'],
                $value['department_name'],
                $value['store_name'],
                $value['state_text'],
                $value['audit_status_text'],
                $value['apply_staff_name'],
                $value['created_at'],
                $value['fix_status_text'],
                $value['stop_duties_date'],
                $value['last_audit_name'],
                $value['audit_time'],//最后审批日期
            ];
        }

        $file_data = $suspensionService->exportExcel($header, $new_data, $file_name);

        $flashOss  = new FlashOss();
        $ossObject = 'staff_suspension/'.date('Ymd').'/'.$excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('StaffSuspensionExportTask end', true);
    }


}