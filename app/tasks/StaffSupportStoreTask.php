<?php

/**
 * 支援需求管理导出excel 【没间隔五分中运行一次】
 * php app/cli.php staff_support_store exportStaffSupport
 *
 */

use App\Library\BaseService;
use App\Models\backyard\HcmExcelTackModel;
use App\Models\backyard\HrImportStaffSupportDetailModel;
use App\Models\backyard\HrImportStaffSupportExcelModel;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrStaffApplySupportStoreOperateLogsModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\HrStoreApplySupportModel;
use App\Models\backyard\SysStoreModel;
use App\Services\ExcelService;
use App\Services\StaffContractService;
use App\Services\StaffNoticeService;
use App\Services\StaffService;
use App\Services\StaffSupportStoreServer;
use App\Services\SysStoreService;
use App\Library\FlashOss;
use App\Library\DateHelper;
use App\Services\StaffShiftService;

class StaffSupportStoreTask extends BaseTask
{
    /**
     * type = 1：更新员工生效，审批通过 每天00:05 并且支援开始时间是今天
     * type = 2：更新员工支援状态生效 审批通过 每天23:55 并且支援结束时间是今天
     */
    public function updateSupportStatusAction($params)
    {
        echo "begin:" . date("Y-m-d h:i:s") . PHP_EOL;
        $logger = $this->getDi()->get('logger');
        try {
            $type  = $params[0] ?? 0;
            $today = date("Y-m-d");
            if (isset($params[1])) {
                $today = $params[1];
            }

            if (isset($params[2])) {
                $staff_info_ids = explode(',', $params[2]);
            }


            echo "type = 1：更新员工生效，审批通过 每天00:05 并且支援开始时间是今天" . PHP_EOL;
            echo "type = 2：更新员工支援状态生效 审批通过 每天23:55 并且支援结束时间是今天" . PHP_EOL;
            echo '执行类型 type:' . $type;
            echo "执行日期：" . $today . PHP_EOL;
            $result = (new StaffSupportStoreServer())->updateSupportStatusTask([
                'today'          => $today,
                'type'           => $type,
                'staff_info_ids' => $staff_info_ids ?? [],
            ]);
            echo "执行结果:" . $result . PHP_EOL;
        } catch (Exception $e) {
            $logger->error([
                'function' => 'updateSupportStatusAction',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
            ]);
        }
        echo "end:" . date("Y-m-d h:i:s") . PHP_EOL;
    }

    /**
     * 导入支援定时任务 每10秒执行一次，每次只取一条
     */
    public function importStaffSupportAction()
    {
        echo "begin:" . date("Y-m-d h:i:s") . PHP_EOL;
        $logger = $this->getDi()->get('logger');
        try {
            $limit = 1;
            $list  = HrImportStaffSupportExcelModel::find([
                'conditions' => 'type = :type: AND is_del = 0 AND status = 1 ',
                'limit'      => $limit,
                'order'      => 'id asc',
                'bind'       => [
                    'type' => HrImportStaffSupportExcelModel::TYPE_SUPPORT_STAFF,
                ],
            ])->toArray();
            echo '需要执行的任务:' . json_encode($list) . PHP_EOL;
            if (count($list) > 0) {
                $server = new StaffSupportStoreServer();
                foreach ($list as $key => $value) {
                    $data_list = HrImportStaffSupportDetailModel::find([
                        'conditions' => 'import_excel_id = :excel_id: and is_del = 0',
                        'bind'       => [
                            'excel_id' => $value['id'],
                        ],
                    ])->toArray();

                    $excel_data = [];
                    foreach ($data_list as $k => $v) {
                        $excel_data[] = json_decode($v['row_content'], true);
                    }
                    $params = [
                        'excel_data'      => $excel_data,
                        'import_excel_id' => $value['id'],
                        'operator_id'     => $value['operator_id'],
                    ];
                    $result = $server->ImportStaffSupport($params);
                    echo '执行结果:' . json_encode($result) . PHP_EOL;
                }
            } else {
                echo "没有要执行的任务" . PHP_EOL;
            }
        } catch (Exception $e) {
            echo $e->getMessage();
            $logger->error([
                'function' => 'ImportStaffSupportAction',
                'message'  => $e->getMessage(),
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
            ]);
            echo "error:" . $e->getMessage();
        }
        echo "end:" . date("Y-m-d h:i:s") . PHP_EOL;
    }

    /**
     * 支援员工管理中的导出excel由同步修改为异步
     * @return void|null
     * @throws Exception
     */
    public function exportStaffSupportAction()
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :' . $input[1], true);
        }

        $this->printLog('exportStaffSupportAction start');

        $params = json_decode(base64_decode($input[0]), true);
        $this->printLog(__CLASS__ . ' params :' . json_encode($params, JSON_UNESCAPED_UNICODE));

        BaseService::setLanguage($params['lang']);
        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);
        if (empty($excelTask)) {
            $this->printLog('task not isset :' . $task_id);
            exit();
        }

        $staffSupportStoreServerObj = reBuildCountryInstance(new StaffSupportStoreServer());
        $ossObject                  = $staffSupportStoreServerObj->exportStaffSupport($params);

        if (!empty($params['From']) && $params['From'] == 'fbi') {
            $flashOss          = new FlashOss();
            $url               = $flashOss->signUrl($ossObject, 20 * 24 * 60 * 60);
            $dowLoadTaskServer = new \App\Services\DownLoadTaskService();
            $dowLoadTaskServer->updateExportManageInfo('staff_support_store_exportStaffSupport_' . $task_id,
                $url, 3,$task_id);
            $excelTask->is_delete = 1;
        }

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->printLog('exportStaffSupportAction end');
    }

    /**
     * 输出日志
     * @param string $logInfo
     * @return void
     */
    public function printLog(string $logInfo)
    {
        $logInfo .= PHP_EOL;
        $this->logger->info($logInfo);
        echo $logInfo;
    }

    /**
     * 定时任务 每天00:10 创建支援子账号
     */
    public function createSubStaffAction($params)
    {
        echo "begin:" . date("Y-m-d h:i:s") . PHP_EOL;
        $logger = $this->getDi()->get('logger');

        $today = date("Y-m-d");
        if (isset($params[0])) {
            $today = $params[0];
        }
        $list = HrStaffApplySupportStoreModel::find([
            'conditions' => 'status = 2 and sub_staff_info_id = 0 and employment_begin_date = :employment_begin_date: and support_status in (1,2)',
            'bind'       => [
                'employment_begin_date' => $today,
            ],
        ])->toArray();
        echo '当日申请支援总数量:' . count($list) . PHP_EOL;

        $create_sub_staff_result = [];
        if (empty($list)) {
            $this->logger->info(['params'=>$today,'createSubStaff'=>'empty']);
            return true;
        }

        $ids = array_column($list, 'id');
        echo '当日申请支援id:' . json_encode($ids) . PHP_EOL;

        $server         = new StaffSupportStoreServer();
        $staff_info_ids = array_column($list, 'staff_info_id');
        $staff_list     = $server->getStaffInfoByIds($staff_info_ids);

        $staff_items    = HrStaffItemsModel::find([
            'conditions' => "staff_info_id in ({staff_ids:array}) and item = 'MANGER'",
            'bind'       => [
                'staff_ids' => $staff_info_ids,
            ],
        ])->toArray();
        $staff_manager  = !empty($staff_items) ? array_column($staff_items, 'value', 'staff_info_id') : [];
        $db             = $this->getDI()->get("db_backyard");

        $job_title_config = (new StaffSupportStoreServer())->getSupportStaffJobTitleConfig();//setting_env 配置
        $support_job_title_role_config = $job_title_config['support_job_title_role_config'];//支援职位对应角色id

        foreach ($list as  $value) {
            $staff_info_id         = $value['staff_info_id'];
            $staff_info            = $staff_list[$staff_info_id];
            $staff_info['manager'] = $staff_manager[$staff_info_id] ?? '';

            $sub_staff_info_id = $server->createSubStaff(
                $staff_info,
                [
                    'job_title'             => $value['job_title_id'],
                    'store_id'              => $value['store_id'],
                    'employment_begin_date' => $value['employment_begin_date'],
                    'position_category'     => $support_job_title_role_config[$value['job_title_id']] ?? [],
                ]
            );
            if (empty($sub_staff_info_id)) {
                $this->logger->error(['params'=>$value,'createSubStaff'=>'error']);
                continue;
            }
            $sync_ms_params = [[
                'id'       => $value['id'],
                'staff_id' => $sub_staff_info_id,
                'master_staff' => $staff_info_id,
                'begin_at' => strtotime($value['employment_begin_date']),
                'end_at'   => strtotime($value['employment_end_date']) + 86399,
            ]];

            //同步ms工号信息
            $sync_ms = $server->syncMsSupportApply($sync_ms_params);
            if (!$sync_ms) {
                $this->logger->error(['params'=>$sync_ms_params,'syncMsSupportApply'=>'error']);
                continue;
            }

            $result = $db->updateAsDict(
                'hr_staff_apply_support_store',
                [
                    'sub_staff_info_id' => $sub_staff_info_id,
                ],
                'id = ' . $value['id']
            );
            //子账号发送合同消息
            $staffContractService = new StaffContractService();
            $staffContractService->sendContractMsgToSubStaff($staff_info_id, $sub_staff_info_id);
            //给子账号 发送简历完善通知 消息
            $sendResult = (new StaffNoticeService())->sendResumeMessage($staff_info_id, $sub_staff_info_id, $today);
            $this->getDI()->get('logger')->write_log([
                'function' => 'StaffSupportStoreServer-createSubStaffAction-sendResumeMessage',
                'params' => [$staff_info_id, $sub_staff_info_id, $today],
                'result' => $sendResult
            ], 'info');

            //生成班次信息
            $createShiftInfo = [
                'staff_info_id'     => $staff_info_id,
                'sub_staff_info_id' => $sub_staff_info_id,
                'shift_start_date'  => $value['employment_begin_date'],
                'shift_end_date'    => $value['employment_end_date'],
                'shift_id'          => $value['shift_id'],
                'shift_type'        => $value['shift_type'],
                'shift_start'       => $value['shift_start'],
                'shift_end'         => $value['shift_end'],
            ];
            $server->createSupportStaffShift($createShiftInfo);

            $create_sub_staff_result[] = [
                'staff_info_id'     => $value['staff_info_id'],
                'sub_staff_info_id' => $sub_staff_info_id,
                'result'            => $result,
            ];
            //echo '主账号:' . $value['staff_info_id'] . '-----子账号:' . $sub_staff_info_id . '-----结果:' . $result . PHP_EOL;
        }

        $logger->notice([
            'function'                => 'createSubStaffTask',
            '主账号'                  => $staff_info_ids,
            '支援工单id'              => $ids,
            'create_sub_staff_result' => $create_sub_staff_result,
        ]);

        //发送支援员工信息 原网点主管和仓管职位员工发送支援信息/支援网点网点主管和仓管员工发送支援信息
        //$this->sendStaffSupportMessageAction();
        return true;
    }


    /**
     * 邮件补支援
     * @param $params
     */
    public function emailFixAction($params)
    {
        //工号
        $staff_info_ids = explode(',', $params[0] ?? '');
        //班次开始时间
        $shift_start = $params[1];
        //网点名称
        $store_name = $params[2] ?? '';
        //支援开始时间
        $start_date = $params[3];
        //支援结束时间
        $end_date = $params[4];

        if (empty($staff_info_ids)) {
            exit('工号为空');
        }

        $supportStoreInfo = \App\Models\backyard\SysStoreModel::findFirstByName($store_name);
        if (empty($supportStoreInfo)) {
            exit('支援网点不存在');
        }

        $shiftInfo = \App\Models\backyard\HrShift::findFirstByStart($shift_start);
        if (empty($shiftInfo)) {
            exit('班次不存在');
        }

        if (count($staff_info_ids) > 0) {
            foreach ($staff_info_ids as $staff_info_id) {
                $staffInfo = \App\Models\backyard\HrStaffInfoModel::findFirstByStaffInfoId($staff_info_id);
                if ($staffInfo) {
                    $model                        = new HrStaffApplySupportStoreModel();
                    $model->staff_info_id         = $staffInfo->staff_info_id;
                    $model->job_title_id          = $staffInfo->job_title;
                    $model->store_id              = $supportStoreInfo->id;
                    $model->store_name            = $supportStoreInfo->name;
                    $model->employment_begin_date = $start_date;
                    $model->employment_end_date   = $end_date;
                    $model->employment_days       = (strtotime($end_date) - strtotime($start_date)) / 86400 + 1;
                    $model->shift_id              = $shiftInfo->id;
                    $model->shift_type            = $shiftInfo->type;
                    $model->shift_start           = $shiftInfo->start;
                    $model->shift_end             = $shiftInfo->end;
                    $model->status                = 2;
                    $model->staff_store_id        = $staffInfo->sys_store_id;
                    $model->staff_store_id        = $staffInfo->sys_store_id;
                    $model->support_status        = 2;
                    $model->actual_begin_date     = $start_date;
                    if ($end_date < date('Y-m-d')) {
                        $model->actual_end_date = $end_date;
                        $model->support_status  = 3;
                    }
                    var_dump($model->save(), $staff_info_id);
                }
            }
        }
    }

    /**
     * 发送消息提醒
     * @return void
     */
    public function sendMessageNoticeAction()
    {
        $time = date("Y-m-d H:i:s", time() - 43200);

        $data = HrStaffApplySupportStoreOperateLogsModel::find([
            'conditions' => 'created_at > :created_at: and is_push = 0',
            'bind'       => [
                'created_at' => $time,
            ],
            'order'      => 'id asc',
        ]);
        if (empty($data)) {
            echo "无数据";
            return;
        }

        $server = new StaffSupportStoreServer();
        foreach ($data as $item) {
            $staff_info_id = $item->staff_info_id;

            $detail = HrStaffApplySupportStoreModel::findFirst("id = " . $item->pid);
            if (empty($detail)) {
                echo "pid = {$item->pid} empty, and continue.", PHP_EOL;
                continue;
            }
            //取消支援通知发送网点主管和网点仓管
            //支援网点 网点主管和网点仓管
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: ',
                'bind'       => [
                    'staff_id' => $staff_info_id,
                ],
                'columns'    => 'staff_info_id,name',
            ]);
            if (empty($staffInfo)) {
                throw new \Exception("Staff Not Exist");
            }

            $storeInfo = SysStoreModel::find([
                'conditions' => "id in ({store_id:array})",
                'bind'       => [
                    'store_id' => [$detail->store_id, $detail->staff_store_id],
                ],
                'columns'    => 'id,name',
            ])->toArray();
            $storeInfo = array_column($storeInfo, 'name', 'id');

            if ($item->action == 'edit') {
                //支援网点
                $store_message_params = [
                    'store_id'     => $detail->store_id,
                    'job_title'    => [
                        StaffSupportStoreServer::$support_job_title['branch_supervisor'],
                        StaffSupportStoreServer::$support_job_title['dc_officer'],
                    ],
                    'title_key'    => 'staff_support_store_edit_message_title_2',
                    'title_bind'   => [
                        'staff_info_id' => $staff_info_id,
                    ],
                    'content_key'  => 'staff_support_store_edit_message_content_2',
                    'content_bind' => [
                        'staff_info_id'      => $staff_info_id,
                        'staff_info_id_1'    => $staff_info_id,
                        'staff_name'         => $staffInfo->name ?? "",
                        'support_store_name' => $storeInfo[$detail->store_id] ?? "",
                        'support_time'       => $detail->employment_begin_date . '-' . $detail->employment_end_date,
                    ],
                ];
                $server->sendPush($store_message_params);

                //所属网点
                $store_message_params = [
                    'store_id'     => $detail->staff_store_id,
                    'job_title'    => [
                        StaffSupportStoreServer::$support_job_title['branch_supervisor'],
                        StaffSupportStoreServer::$support_job_title['dc_officer'],
                    ],
                    'title_key'    => 'staff_support_store_edit_message_title_2',
                    'title_bind'   => [
                        'staff_info_id' => $staff_info_id,
                    ],
                    'content_key'  => 'staff_support_store_edit_message_content_2',
                    'content_bind' => [
                        'staff_info_id'      => $staff_info_id,
                        'staff_info_id_1'    => $staff_info_id,
                        'staff_name'         => $staffInfo->name ?? "",
                        'support_store_name' => $storeInfo[$detail->store_id] ?? "",
                        'support_time'       => $detail->employment_begin_date . '-' . $detail->employment_end_date,
                    ],
                ];
                $server->sendPush($store_message_params);
            }

            if ($item->action == 'cancel') {
                //支援网点
                $store_message_params = [
                    'store_id'     => $detail->store_id,
                    'job_title'    => [
                        StaffSupportStoreServer::$support_job_title['branch_supervisor'],
                        StaffSupportStoreServer::$support_job_title['dc_officer'],
                    ],
                    'title_key'    => 'staff_support_store_edit_message_title_3',
                    'title_bind'   => [
                        'staff_info_id' => $staff_info_id,
                    ],
                    'content_key'  => 'staff_support_store_edit_message_content_4',
                    'content_bind' => [
                        'staff_info_id' => $staff_info_id,
                    ],
                ];
                $server->sendPush($store_message_params);

                //原网点
                $store_message_params = [
                    'store_id'     => $detail->staff_store_id,
                    'job_title'    => [
                        StaffSupportStoreServer::$support_job_title['branch_supervisor'],
                        StaffSupportStoreServer::$support_job_title['dc_officer'],
                    ],
                    'title_key'    => 'staff_support_store_edit_message_title_3',
                    'title_bind'   => [
                        'staff_info_id' => $staff_info_id,
                    ],
                    'content_key'  => 'staff_support_store_edit_message_content_3',
                    'content_bind' => [
                        'staff_info_id' => $staff_info_id,
                    ],
                ];
                $server->sendPush($store_message_params);
            }

            $item->is_push = 1;
            $item->save();
        }
    }

    //发送支援员工信息 原网点主管和仓管职位员工发送支援信息/支援网点网点主管和仓管员工发送支援信息

    /**
     * @Single
     * @return true|void
     */
    public function sendStaffSupportMessageAction()
    {

        $today  = date("Y-m-d");
        $logger = $this->getDi()->get('logger');

        $staff_support_list = HrStaffApplySupportStoreModel::find([
            'conditions' => 'status = 2 and employment_begin_date = :employment_begin_date: and support_status = 2',
            'bind'       => [
                'employment_begin_date' => $today,
            ],
        ])->toArray();
        if (empty($staff_support_list)) {
            $logger->info(['sendStaffSupportMessage' => 'no data']);
            return true;
        }

        //网点信息
        $this_store_ids  = array_values(array_unique(array_column($staff_support_list, 'staff_store_id')));
        $this_store_list = SysStoreModel::find([
            'columns'    => 'id,name',
            'conditions' => 'id in ({store_ids:array})',
            'bind'       => [
                'store_ids' => $this_store_ids,
            ],
        ])->toArray();
        $this_store_list = array_column($this_store_list, 'name', 'id');

        //支援网点id
        $support_store_ids = array_unique(array_column($staff_support_list, 'store_id'));

        //员工信息
        $staff_info_ids  = array_column($staff_support_list, 'staff_info_id');
        $staff_info_list = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,name,mobile,mobile_company',
            'conditions' => 'staff_info_id in ({staff_info_ids:array})',
            'bind'       => [
                'staff_info_ids' => $staff_info_ids,
            ],
        ])->toArray();
        $staff_info_list = !empty($staff_info_list) ? array_column($staff_info_list, null,
            'staff_info_id') : [];

        $this_store_staff_list    = [];//原网点员工数据
        $support_store_staff_list = [];//支援网点员工数据
        foreach ($staff_support_list as $key => $value) {
            $this_store_staff_list[$value['staff_store_id']][] = $value;
            $support_store_staff_list[$value['store_id']][]    = $value;
        }

        $job_title_ids = [
            StaffSupportStoreServer::$support_job_title['branch_supervisor'],
            StaffSupportStoreServer::$support_job_title['dc_officer'],
        ];
        //原网点主管和仓管职位员工发送支援信息
        foreach ($this_store_ids as $store_id) {
            //原网点员工信息
            $store_staff_list = $this_store_staff_list[$store_id] ?? [];
            $message_staff    = "<br/>";
            foreach ($store_staff_list as $staff_key => $staff_value) {
                $employment_date = $staff_value['employment_begin_date'] . '~' . $staff_value['employment_end_date'];//支援日期
                $staff_name      = $staff_info_list[$staff_value['staff_info_id']]['name'] ?? '';
                //员工工号 | 员工姓名 | 支援网点 | 支援日期
                $message_staff .= "{$staff_value['staff_info_id']} | {$staff_name} | {$staff_value['store_name']} | {$employment_date} <br/>";
            }
            //send_this_store__message_title send_this_store__message_content
            $send_message_params = [
                'job_title_ids'         => $job_title_ids,
                'store_id'              => $store_id,
                'message_title_key'     => 'staff_support_store_this_store_message_title',
                //'当天本网点员工支援其他网点',
                'message_content_key'   => 'staff_support_store_this_store_message_content',
                //'您的网点以下员工今天将在其他网点进行支援',
                'message_content_value' => $message_staff,
            ];
            $result_1 = $this->sendSupervisorDCofferMessage($send_message_params);
        }

        //支援网点网点主管和仓管员工发送支援信息
        foreach ($support_store_ids as $support_store_id) {
            $store_staff_list = $support_store_staff_list[$support_store_id] ?? [];
            $message_staff    = '<br/>';
            foreach ($store_staff_list as $staff_key => $staff_value) {
                $staff_name   = $staff_info_list[$staff_value['staff_info_id']]['name'] ?? '';  //员工姓名
                $staff_mobile = $staff_info_list[$staff_value['staff_info_id']]['mobile'] ?? '';//员工手机号

                $this_store_name = $this_store_list[$support_store_id] ?? '';//原网点名称

                $support_shift   = $staff_value['shift_start'] . '~' . $staff_value['shift_end'];                    //支援班次
                $employment_date = $staff_value['employment_begin_date'] . '~' . $staff_value['employment_end_date'];//支援日期
                //员工工号 | 员工姓名 | 员工电话 | 原网点 | 支援班次 | 员工支援日期
                $message_staff .= " {$staff_value['staff_info_id']} | {$staff_name} | {$staff_mobile} | {$this_store_name} | {$support_shift} | {$employment_date}  <br/>";
            }
            //send_support_store__message_title send_support_store_message_content
            $send_message_params = [
                'job_title_ids'         => $job_title_ids,
                'store_id'              => $support_store_id,
                'message_title_key'     => 'staff_support_store_support_store_message_title',
                //'当天到本网点支援的员工信息',
                'message_content_key'   => 'staff_support_store_support_store_message_content',
                //'以下员工今天将到您的网点进行支援',
                'message_content_value' => $message_staff,
            ];

            $result_2 = $this->sendSupervisorDCofferMessage($send_message_params);
        }
        $logger->info(['sendStaffSupportMessage'=>'end']);
    }

    //员工信息 根据网点和职位
    public function sendSupervisorDCofferMessage($params)
    {
        $job_title_ids         = $params['job_title_ids'];
        $store_id              = $params['store_id'];
        $message_title_key     = $params['message_title_key'];
        $message_content_key   = $params['message_content_key'];
        $message_content_value = $params['message_content_value'];
        $staff_info = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,name,mobile,mobile_company',
            'conditions' => 'job_title in ({job_title:array}) and sys_store_id = :store_id: and state = 1 and formal = 1 and is_sub_staff = 0',
            'bind'       => [
                'job_title' => $job_title_ids,
                'store_id'  => $store_id,
            ],
        ])->toArray();
        if (empty($staff_info)) {
            return true;
        }
        $staff_support_store_service = new StaffSupportStoreServer();
        foreach ($staff_info as $key => $value) {
            $lang = (new StaffService())->getAcceptLanguage($value['staff_info_id']);
            self::setLang($lang);
            $message_params = [
                'staff_info_id'   => $value['staff_info_id'],
                'message_title'   => self::getTranslation()->_($message_title_key),
                'message_content' => self::getTranslation()->_($message_content_key) . $message_content_value,
                'source'          => 'support_store_supervisor_staff_support',
            ];
            $staff_support_store_service->sendStaffBackyardMessage($message_params);
        }
        return true;
    }


    public function fixStoreSupportShiftAction()
    {
        echo 'start!!' . PHP_EOL;
        $today              = date('Y-m-d');
        $store_apply_detail = HrStoreApplySupportModel::find([
            'conditions' => " employment_end_date >= :employment_end_date: and status in (1,2) and shift_extend_id is null ",
            'bind'       => [
                'employment_end_date' => $today,
            ],
        ]);

        if (empty($store_apply_detail)) {
            exit('no data');
        }

        $shiftInfo = (new \App\Modules\My\Services\StaffShiftService())->getSupportShiftList();

        $shiftInfo = array_column($shiftInfo, 'shift_extend_id', 'id');

        foreach ($store_apply_detail as $item) {
            if (!isset($shiftInfo[$item->shift_id])) {
                echo 'HrStoreApplySupportModel id ' . $item->id . ' error_shift_id ' . $item->shift_id . PHP_EOL;
                $this->logger->error([
                    'function' => 'fixSupportShiftAction',
                    'id'       => $item->id,
                    'error'    => 'shift id not support : ' . $item->shift_id,
                ]);
                continue;
            }
            $item->shift_extend_id = $shiftInfo[$item->shift_id];
            $res                   = $item->save();
        }

        echo ' del HrStaffApplySupportStoreModel  start !!' . PHP_EOL;

        $staff_apply_detail = HrStaffApplySupportStoreModel::find([
            'conditions' => " employment_begin_date >= '2022-11-24' and shift_extend_id is null ",
        ]);

        if (empty($staff_apply_detail)) {
            exit('no data');
        }

        foreach ($staff_apply_detail as $item) {
            if (!isset($shiftInfo[$item->shift_id])) {
                echo 'HrStaffApplySupportStoreModel id ' . $item->id . ' error_shift_id ' . $item->shift_id . PHP_EOL;
                $this->logger->error([
                    'function' => 'fixSupportShiftAction',
                    'id'       => $item->id,
                    'error'    => 'shift id not support : ' . $item->shift_id,
                ]);
                continue;
            }
            $item->shift_extend_id = $shiftInfo[$item->shift_id];
            $res                   = $item->save();
        }
        echo ' end!!';
    }


    public function fixStaffSupportShiftAction()
    {
        echo 'start!!' . PHP_EOL;
        //1获取支援进行中的数据
        $staff_support_list = HrStaffApplySupportStoreModel::find([
            'conditions' => "status = 2  and  support_status in (2,3) and employment_begin_date >= '2022-11-24' ",

        ]);
        if (empty($staff_support_list)) {
            exit('no data');
        }

        foreach ($staff_support_list as $item) {
            echo 'deal id ' . $item->id;
            $createShiftInfo = [
                'staff_info_id'     => $item->staff_info_id,
                'sub_staff_info_id' => $item->sub_staff_info_id,
                'shift_start_date'  => $item->employment_begin_date,
                'shift_end_date'    => $item->employment_end_date,
                'shift_id'          => $item->shift_id,
                'shift_extend_id'   => $item->shift_extend_id,
            ];
            $re              = (new \App\Modules\My\Services\StaffSupportStoreServer())->createSupportStaffShift($createShiftInfo);
            var_dump($re, $item->id);
        }

        echo ' end!!';
    }


    public function fixStaffSupportShiftV2Action()
    {
        echo 'start!!' . PHP_EOL;
        //1获取支援进行中的数据
        $staff_support_list = HrStaffApplySupportStoreModel::find([
            'conditions' => "status = 2  and  support_status in (2,3) and employment_begin_date >= '2022-11-24' ",

        ]);
        if (empty($staff_support_list)) {
            exit('no data');
        }
        foreach ($staff_support_list as $item) {
            $shift_id        = $item->shift_id;
            $shift_extend_id = $item->shift_extend_id;
            $condition       = ["conditions" => " is_operator_edit = 2 and (staff_info_id = " . $item->staff_info_id . " or staff_info_id = " . $item->sub_staff_info_id . " ) and shift_date >= '2022-12-30' and shift_date <=  '{$item->employment_end_date}'"];
            $updateData      = ['shift_id' => $shift_id, 'shift_extend_id' => $shift_extend_id];
            $update_result   = $this->getDI()->get("db_backyard")->updateAsDict("hr_staff_shift_v2", $updateData,
                $condition);
            var_dump($update_result);
        }

        echo ' end!!';
    }

    //支援刷主账号班次 上线跑一次 只跑泰国 菲律宾
    public function initSupportShiftAction()
    {
        //当前生效支援数据
        $list = HrStaffApplySupportStoreModel::find([
            'conditions' => 'status = 2 and support_status in (1,2)',
        ])->toArray();

        if (empty($list)) {
            die('没有支援数据');
        }
        $server = new StaffShiftService();
        foreach ($list as $li) {
            try {
                $dateList = DateHelper::DateRange(strtotime($li['employment_begin_date']),
                    strtotime($li['employment_end_date']));
                $server->shiftBackup($li, $dateList);
            } catch (\Exception $e) {
                $this->logger->write_log("initSupportShift " . json_encode($li) . $e->getTraceAsString());
            }
        }
        die('跑完了');
    }

    /**
     * 一次性脚本，初始化两个网点经纬度的距离
     * th my ph 执行
     * @return void
     */
    public function initSupportApartAction()
    {
        $this->echoInfoLog('initSupportApartAction start', true);
        $page_num  = 1;
        $page_size = 1000;
        while (true) {
            try {
                $offset  = $page_size * ($page_num - 1);
                $builder = $this->modelsManager->createBuilder();
                $builder->columns(['id', 'store_id', 'staff_store_id']);
                $builder->from(HrStaffApplySupportStoreModel::class);
                $builder->andWhere('created_at >= :created_at:', ['created_at' => '2023-06-01 00:00:00']);
                $builder->andWhere("support_status != 4");
                $builder->orderBy('id ASC');
                $builder->limit($page_size, $offset);
                $list = $builder->getQuery()->execute()->toArray();
            } catch (Exception $e) {
                $this->logger->write_log("initSupportApartAction update error " . $e->getMessage(), 'error');
                break;
            }
            if (empty($list)) {
                break;
            }
            foreach ($list as $k => $v) {
                // 计算两个网点经纬度之间的距离
                $apart = (new SysStoreService())->calculateDistanceStore($v['store_id'], $v['staff_store_id']);
                if ($apart) {
                    $result = $this->getDI()->get("db_backyard")->updateAsDict(
                        'hr_staff_apply_support_store',
                        [
                            'apart' => $apart,
                        ],
                        'id = ' . $v['id']
                    );
                    if (!$result) {
                        $this->logger->write_log("initSupportApartAction update error id: " . $v['id'], 'error');
                    }
                } else {
                    $this->logger->write_log("initSupportApartAction apart is null id: " . $v['id'], 'info');
                }
            }
            $page_num++;
        }
        $this->echoInfoLog('initSupportApartAction end', true);
    }


}
