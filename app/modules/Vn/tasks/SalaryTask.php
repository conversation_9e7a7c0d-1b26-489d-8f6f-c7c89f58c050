<?php

namespace App\Modules\Vn\Tasks;



use App\Library\BaseService;
use App\Models\backyard\FileOssUrlModel;
use App\Models\fle\StaffAccountModel;
use App\Modules\Vn\Services\SalaryAttendanceAllowanceService;
use App\Modules\Vn\Services\SalaryService;

class SalaryTask extends \BaseTask
{

    //发送工资条 任务
    public function send_allAction(){
        $di = $this->getDI();
        $logger    = $di->get('logger');
        $server = new SalaryService();
        $redis = $di->get('redis');
        BaseService::setLanguage('vi');
        //设备语言 用于发消息
        $staffAccountList = StaffAccountModel::find([
            'columns'    => 'staff_info_id,accept_language',
             'conditions' => 'equipment_type=:equipment_type:',
            'bind' => ['equipment_type' => StaffAccountModel::$equipment_type['backyard']],
        ])->toArray();
      $staffLang  = array_column($staffAccountList, 'accept_language', 'staff_info_id');
        //
//        $language = ['en'=>1,'zh'=>2,'zh-CN'=>2,'vi' =>3];
        $language = FileOssUrlModel::$lang_list;
        foreach($staffLang as $staff_info_id => $lang){
            if (in_array($lang,['en-GB','en-US','en'])) {
                $lang = 'en';
            }
            if (in_array($lang,['vi','vi-VN'])) {
                $lang = 'vi';
            }
            $staffLang[$staff_info_id] = $language[$lang] ?? $language['en'];
        }
        SalaryService::$equipmentLang = $staffLang;
        while($redis_data = $redis->rpop(SalaryService::$list_name)) {
            try {
                $this->echoInfoLog($redis_data);
                [$staff_id,$month] = explode('_', $redis_data);
                $server->send_salary($staff_id,$month);
            } catch (\Exception $e) {
                $logger->info(__CLASS__.__METHOD__ .' exception:'.$e->getMessage());
                echo date('Y-m-d H:i:s').__METHOD__.$e->getMessage().PHP_EOL;
                break;
            }
        }
    }

    /**
     * 计算出勤津贴
     * @param $params
     * @return void
     */
    public function calAttendanceAllowanceAction($params)
    {
        $month    = date('Y-m', strtotime('last month'));
        $staffIds = [];
        if (!empty($params[0])) {
            $month = $params[0];
        }
        if (!empty($params[1])) {
            $staffIds = explode(',', $params[1]);
        }
        (new SalaryAttendanceAllowanceService())->calculate($month, $staffIds);
    }
}
