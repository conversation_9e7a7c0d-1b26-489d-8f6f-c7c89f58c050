<?php
/**
 * Author: Bruce
 * Date  : 2022-03-25 17:18
 * Description:
 */

namespace App\Modules\Vn\Services\message;


use App\Library\ApiClient;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\MessageModel;
use App\Modules\Vn\library\Enums\MessageEnums;
use App\Modules\Vn\Services\MessagesService;
use App\Services\message\BaseMessageService as GlobalBaseMessageService;

use Exception;

abstract class BaseMessageService extends GlobalBaseMessageService
{
    abstract protected function verifyOrganizationMessage(array $fromData);
    public $insertData =[];
    public $send_type = 0;
    public $organizationMessageValidateRule = [
        'title' => 'Required|StrLenGe:1|>>>:title param error',
        'content' => 'Required|StrLenGe:1|>>>:content param error',
        'top' => 'Required|IntIn:1,3|>>>:top param error',
        'category' => 'Required|IntIn:0,6,33,34,144|>>>:category param error',
    ];


    /**
     * 公共校验
     * @return $this
     * @throws Exception
     */
    public function commonVerify()
    {
        $insertData = $this->insertData;
        //发送按钮白名单校验

        // 发送对象权限验证: 消息页 btn 权限
        $btn_permission = (new MessagesService())->getMessagePageBtnPermission();
        if ($btn_permission[9] == false) {
            throw  new ValidationException(self::$t->_('send_message_no_permissions'));
        }


        if(empty($insertData['select_send_list'])){
            throw new ValidationException('recipient staffs empty');
        }

        $validate_rule = [];
        if (!empty($insertData['feedback_setting'])) {
            $validate_rule['feedback_setting'] = 'Required|IntIn:0,1|>>>:feedback_setting param error';
        }
        if (!empty($insertData['by_show_type'])) {
            $validate_rule['by_show_type'] = 'Required|IntIn:0,1|>>>:by_show_type param error';
        }
        if (!empty($insertData['category_code'])) {
            $validate_rule['category_code'] = 'Required|IntIn:0,3|>>>:category_code param error';
        }
        if ($validate_rule) {
            Validation::validate($insertData, $validate_rule);
        }

        if (!empty($insertData['set_time'])) {
            if (strtotime($insertData['set_time']) <= strtotime(date('Y-m-d H:00'))) {
                throw new ValidationException(self::$t->_('limit_set_time'));
            }
            $insertData['set_time'] = date('Y-m-d H:00:00', strtotime($insertData['set_time']));
        }
        if ($insertData['set_time'] == '1970-01-01 00:00:00') {
            throw new ValidationException('set time error');
        }

        if ($insertData['set_time'] >= date('Y-m-d 23:01:01', strtotime('+2 month'))) {
            throw new ValidationException('CAN NOT OVER 2 MONTH');
        }

        // 截止时间不可以早于发送时间
        if (!empty($insertData['end_time']) && !empty($insertData['set_time']) && $insertData['set_time'] > $insertData['end_time']) {
            throw new ValidationException(self::$t->_('msg_qn_056'));
        }
        return $this;
    }

    /**
     * 创建消息
     * @return bool
     * @throws Exception
     */
    public function save()
    {
        try {

            if (!$sendStaffS = BuildMessageService::init()->getStaff()) {
                throw  new ValidationException(self::$t->_('no_recipient'));
            }
            //保存按组织创建条件
            $create_params['select_staffs']        = !empty($this->insertData['select_staffs']) ? $this->insertData['select_staffs'] : [];
            $create_params['select_organizations'] = !empty($this->insertData['select_organizations']) ? $this->insertData['select_organizations'] : [];
            $create_params['select_send_list']     = !empty($this->insertData['select_send_list']) ? $this->insertData['select_send_list'] : [];

            $msg_param['send_type']          = $this->send_type;
            $msg_param['to_group']           = BuildMessageService::init()->getSendOrganizationAndStaffInfo();
            $msg_param['staff_users']        = $sendStaffS;
            $msg_param['message_title']      = $this->insertData['title'];
            $msg_param['message_content']    = $this->insertData['content'];
            $msg_param['staff_info_ids_str'] = implode(',', $sendStaffS);

            $msg_param['add_userid']    = $this->insertData['user_info']['id'];
            $msg_param['top']           = $this->insertData['top'];
            $msg_param['category']      = empty($this->insertData['category']) ? 0 : intval($this->insertData['category']);
            $msg_param['category_code'] = empty($this->insertData['category_code']) ? 0 : intval($this->insertData['category_code']);
            $msg_param['set_time']      = $this->insertData['set_time'] ?? null;

            $msg_param['questionnaire_lib_id'] = $this->insertData['questionnaire_lib_id'] ?? 0;
            $msg_param['feedback_setting']     = in_array($this->insertData['feedback_setting'],
                [0, 1]) ? $this->insertData['feedback_setting'] : 0;
            $msg_param['by_show_type']         = in_array($this->insertData['by_show_type'],
                [0, 1]) ? $this->insertData['by_show_type'] : 0;
            $msg_param['end_time']             = $this->insertData['end_time'] ?? null;
            $msg_param['visible']              = $this->insertData['visible'] ?? 0;
            $msg_param['create_params']        = json_encode($create_params, JSON_UNESCAPED_UNICODE);
            $msg_param['extend'] = $this->insertData['extend'] ?? null;
            $audit_info = BuildMessageService::init()->getExamineAndApproveData();

            if (!empty($audit_info['node_auditor'])) {
                $msg_param['approver_id'] = implode(',', $audit_info['node_auditor']);
            }

            //是否同步子账号:1否，2是
            $msg_param['is_sync_sub']         = empty($this->insertData['is_sync_sub']) || (!empty($this->insertData['is_sync_sub']) && $this->insertData['is_sync_sub'] == MessageModel::IS_SYNC_SUB_NO) ? MessageModel::IS_SYNC_SUB_NO : MessageModel::IS_SYNC_SUB_YES;

            // 验证审批类型 和 提取节点审批人
            $messageBLL = new MessagesService();
            $messageBLL->setHireTypeList($this->insertData['hire_type_list']);
            $messageBLL->setJobTitleList($this->insertData['job_title_list']);
            $messageBLL->setStoreCategoryList($this->insertData['store_category_list']);
            $messageBLL->setHireDateList($this->insertData['hire_date_list']);

            $messageBLL->setContractCompanyIds($this->insertData['contract_company_ids']);
            $messageBLL->setPositionTypes($this->insertData['position_types']);
            // 创建消息
            $add_result = $messageBLL->createMessage($msg_param);
            if ($add_result === false) {
                throw new ValidationException('message creation failed');
            }

            //审批人可空 直接发送
            if ($audit_info === true) {
                //调用 bi  rpc 发送
                $rpcParam['message_id']   = $add_result;
                $rpcParam['audit_status'] = 2;
                $this->logger->write_log("bi_rpc_res_send_message: post_data: ".json_encode($rpcParam,
                        JSON_UNESCAPED_UNICODE), 'info');

                $messagesService = new MessagesService();
                if($messagesService->syncAuditStatus($rpcParam)){
                    return true;
                } else {
                    throw new ValidationException('message created successfully send fail message_id: '.$add_result);
                }
            }

            // 创建审批流
            $create_audit_flow_res = $messageBLL->syncCreateMsgAuditFlow($add_result, $audit_info);
            if ($create_audit_flow_res['code'] == 1) {
                return true;
            } else {
                throw new ValidationException('message created successfully, '.$create_audit_flow_res['msg']);
            }
        } catch (Exception $e) {
            throw $e;
        }
        //处理入口逻辑
    }
}