<?php

namespace App\Modules\Vn\Services;

use App\Library\Enums\StaffEnums;
use App\Library\FlashOss;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrProbationAuditModel;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Services\DictionaryService;
use App\Services\ProbationService as BaseProbationService;
use App\Services\SysStoreService;
use Mpdf\Mpdf;
use Phalcon\Mvc\Model\Query\Builder;

class ProbationService extends BaseProbationService
{
	
	
	
	/**
	 * @throws \Exception
	 */
	public function getList($params)
	{

		//todo::迁移参考：BI：app/BLL/StaffSearchBLL.php method:staffList
		$data = [];
		$builder = new Builder();
		$builder->from(['hsi' => HrStaffInfoReadModel::class])
                ->columns("count(1) as total")
		        ->leftJoin(HrProbationModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hp')
				->leftJoin(SysStoreModel::class, 'hsi.sys_store_id = st.id', 'st')
		        ->andWhere("hsi.formal=1 and hsi.hire_date>='2020-06-13' and hsi.is_sub_staff = 0  and  ( hsi.hire_type = 1 or hsi.hire_type in (3,4) and hsi.hire_times >= 365 or hsi.hire_type in (2) and hsi.hire_times >= 12)");
		
		/**
		 * 1. 正式员工：默认均进入试用期
		 * 2. 实习生：默认不进入试用期
		 * 3. 特殊合同工类型：以雇佣期间来判断是否需要进试用期
		 * - 若雇佣期间大于等于12个月或大于等于365天需要进入试用期
		 */


        //区域 使用新的所属区域
        if (isset($params['store_area_id']) && $params['store_area_id']) {
            $builder->inWhere('st.sorting_no', $params['store_area_id']);
        }
        $builder  = $this->getQueryConditions($builder, $params);
        $builder = $this->checkStaffPermission($builder, $params);
        if (false === $builder) {
            return ['page_count' => 0, 'rows' => []];
        }

        //查总数
        $totalInfo = $builder->getQuery()->getSingleResult();
        $pageCount = intval($totalInfo->total);
        if ($pageCount === 0) {
            return ['page_count' => 0, 'rows' => []];
        }
        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.name_en',
            'hsi.job_title',
            'hsi.sys_department_id',
            'hsi.node_department_id',
            'hsi.sys_store_id',
            'hsi.hire_date',
            'hsi.state',
            'hsi.wait_leave_state',
            'hsi.leave_date',
            'hsi.job_title_grade_v2',
            'hsi.identity',
            'hsi.mobile_company',
            'hsi.manger as manage_staff_id',
            'hp.status as probation_status',
            'hp.first_score',
            'hp.second_score',
            'hp.formal_at',
            'hp.remark',
            'hp.cur_level',
            'hp.is_delay',
            'hp.mark',
            'st.sorting_no',
            'hsi.working_country',
            'hp.first_audit_status',
            'hp.first_status',
            'hp.second_audit_status',
            'hp.second_status',
        ]);
		$builder->orderBy('hsi.hire_date');
		
        $params['page'] = $params['page'] ?? 1;
        $params['pagesize'] = $params['pagesize'] ?? 50;
        $offset = ($params['page'] - 1) * $params['pagesize'];
        $builder->limit($params['pagesize'], $offset);
        
		$data = $builder->getQuery()->execute()->toArray();
        if ($data) {
            $jobIds = array_values(array_unique(array_filter(array_column($data, 'job_title'))));
            $jobIds = array_map(function ($item) {
                return intval($item);
            }, $jobIds);
            $jobTitle = $jobIds ? HrJobTitleModel::query()->inWhere('id', $jobIds)->execute()->toArray() : [];
            $jobTitle = array_column($jobTitle, null, 'id');
            //$sysDepartments = $this->sysDepartmentList();
            // 拿到所有部门信息
            $nodeDepartmentId = array_column($data,'node_department_id');
            $sysDepartmentId = array_column($data,'sys_department_id');
            $sysDepartments = (new \App\Services\SysDepartmentService())->getAllDepartmentByIds($nodeDepartmentId, $sysDepartmentId);
            $staffId = array_column($data, 'staff_info_id');
            $manage_staffs         = [];
            $manage_high_staff_map = [];
            $manage_ids            = array_column($data, 'manage_staff_id');
            //如果导出的话+上上级电话
            if ($manage_ids && isset($params['is_export'])) {
                $t_arr = HrStaffInfoModel::query()->columns(['manger', 'staff_info_id'])->inWhere('staff_info_id', array_values($manage_ids))->execute()->toArray();
                $t_ids = array_column($t_arr, 'manger');

                //[manager_id] =>上级id
                $manage_high_staff_map = array_column($t_arr, 'manger', 'staff_info_id');
                $manage_ids            = array_merge($manage_ids, $t_ids);
            }
            if ($manage_ids) {
                $manage_staffs = HrStaffInfoReadModel::query()->columns(['staff_info_id', 'name', 'mobile'])->inWhere('staff_info_id', array_values($manage_ids))->execute()->toArray();
                $manage_staffs = array_column($manage_staffs, null, 'staff_info_id');
            }


            $store_ids = array_values(array_unique(array_filter(array_column($data, 'sys_store_id'))));

            $pieceArr = [];
            if ($store_ids) {
                $storeArr = (new SysStoreService())->getStoreListByIdsFromCache($store_ids);
                $pieceArr = array_column($storeArr, 'piece_name', 'id');
            }
        }
		$return = ['page_count' => intval($pageCount), 'rows' => []];

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

		foreach ($data as $item) {

            $job_title_name = isset($jobTitle[$item['job_title']]) ? $jobTitle[$item['job_title']]['job_name'] : '';
            if (!empty($job_title_name) && HrJobTitleModel::STATUS_2 == $jobTitle[$item['job_title']]['status']) {
                $job_title_name .= self::$t->_('deleted');
            }

            $department_name = $sysDepartments[$item['node_department_id']]['name'] ?? "";
            if (!empty($department_name) && SysDepartmentModel::DELETE_1 == $sysDepartments[$item['node_department_id']]['deleted']) {
                $department_name .= self::$t->_('deleted');
            }

            if (empty($department_name)) {
                $department_name = $sysDepartments[$item['sys_department_id']]['name'] ?? '';
                if (!empty($department_name) &&  SysDepartmentModel::DELETE_1 == $sysDepartments[$item['sys_department_id']]['deleted']) {
                    $department_name .= self::$t->_('deleted');
                }
            }

            $sys_department_name = $sysDepartments[$item['sys_department_id']]['name'] ?? '';
            if (!empty($sys_department_name) && SysDepartmentModel::DELETE_1 == $sysDepartments[$item['sys_department_id']]['deleted']) {
                $sys_department_name .= self::$t->_('deleted');
            }

			$row = [
				'staff_info_id' => $item['staff_info_id'],
				'name' => $item['name'],
				'name_en' => (string)$item['name_en'],
				'mobile' => !empty($item['mobile']) ? (string)$item['mobile'] : '',
				'state' => $item['state'],
				'leave_date' => $item['leave_date'] ? date("Y-m-d", strtotime($item['leave_date'])) : '',
				'sys_store_name' => isset($this->temp()[$item['sys_store_id']]) ? $this->temp()[$item['sys_store_id']]['name'] : '',
				'job_title_name' => $job_title_name,
				'sys_department_name' => $sys_department_name,
				'department_name' => $department_name,
				'store_area_id' => '',
				'store_area_text' => $item['sorting_no'],
				'sorting_no' => $item['sorting_no'],
				'state_name' => $this->getStateName($item['state'], $item['wait_leave_state']),
                'working_country' => $workingCountryList[$item['working_country']] ?? '', // 工作所在国家
                'first_audit_status' => $item['first_audit_status'],
                'first_status'=> $item['first_status'],
                'second_audit_status'=> $item['second_audit_status'],
                'second_status'=> $item['second_status'],
                'first_audit_status_name'  => static::$t->_('hr_probation_audit_status_' . (empty($item['first_audit_status']) ? HrProbationModel::FIRST_AUDIT_STATUS_WAIT : $item['first_audit_status'])),
                'first_status_name'        => $item['first_audit_status'] == HrProbationModel::FIRST_AUDIT_STATUS_DONE ? (static::$t->_('hr_probation_status_' . (empty($item['first_status']) ? HrProbationModel::FIRST_STATUS_NOT_PASS : $item['first_status']))) : '',
                'second_audit_status_name' => static::$t->_('hr_probation_audit_status_' . (empty($item['second_audit_status']) ? HrProbationModel::SECOND_AUDIT_STATUS_WAIT : $item['second_audit_status'])),
                'second_status_name'       => $item['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE ? (static::$t->_('hr_probation_status_' . (empty($item['second_status']) ? HrProbationModel::SECOND_STATUS_NOT_PASS : $item['second_status']))) : '',

            ];
			////整体代码过长 留着按照源代码 定位下位置，防止难找，没问题后可删除
			if (true) {
				$now_time = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);
				//转正评估不需要leave_source
				$item['leave_source'] = 5;
				
				$row['hire_date'] = $item['hire_date'] ? date("Y-m-d", strtotime($item['hire_date'])) : '';
				$row['job_title_level'] = !empty($item['job_title_level']) ? $item['job_title_level'] : 1;

				$row['manage_staff_id'] = $item['manage_staff_id'] ?? '';
				$row['manage_staff_name'] = '';
				$row['manage_staff_mobile'] = '';
				
				$row['manage_higher_staff_id'] = "";
				$row['manage_higher_staff_name'] = "";
				$row['manage_higher_staff_mobile'] = "";
				
				if (!empty($row['manage_staff_id']) && !empty($manage_staffs[$row['manage_staff_id']])) {
					$row['manage_staff_name'] = $manage_staffs[$row['manage_staff_id']]['name'] ?? '';
					$row['manage_staff_mobile'] = $manage_staffs[$row['manage_staff_id']]['mobile'] ?? '';
					
					$row['manage_higher_staff_id'] = $manage_high_staff_map[$row['manage_staff_id']] ?? '';
				}
				
				if (isset($params['is_export']) && !empty($row['manage_higher_staff_id'])) {
					$row['manage_higher_staff_name'] = $manage_staffs[$row['manage_higher_staff_id']]['name'] ?? '';
					$row['manage_higher_staff_mobile'] = $manage_staffs[$row['manage_higher_staff_id']]['mobile'] ?? '';
				}
				
				$row['first_deadline_date'] = "";
				$row['second_deadline_date'] = "";
				
				$row['first_deadline_date_start'] = "";
				$row['first_deadline_date_end'] = "";
				
				$row['second_deadline_date_start'] = "";
				$row['second_deadline_date_end'] = "";
				
				$row['days_90'] = "";
				$row['is_delay'] = $item['is_delay'];
				
				if (!empty($row['hire_date'])) {
                    //x 级一下
                    if ($item['job_title_grade_v2'] <= $this->job_grade) {
                        $row['first_deadline_date_start']  = $this->getDateByDays($row['hire_date'], $this->probation_first_deadline_start, 1);
                        $row['first_deadline_date_end']    = $this->getDateByDays($row['hire_date'], $this->probation_first_deadline_end, 1);
                        $row['second_deadline_date_start'] = $this->getDateByDays($row['hire_date'], $this->probation_second_deadline_start, 1);
                        $row['second_deadline_date_end']   = $this->getDateByDays($row['hire_date'], $this->probation_second_deadline_end, 1);

                    } else {
                        //x 级以上
                        $row['first_deadline_date_start']  = $this->getDateByDays($row['hire_date'], $this->probation_first_deadline_start_two, 1);
                        $row['first_deadline_date_end']    = $this->getDateByDays($row['hire_date'], $this->probation_first_deadline_end_two, 1);
                        $row['second_deadline_date_start'] = $this->getDateByDays($row['hire_date'], $this->probation_second_deadline_start_two, 1);
                        $row['second_deadline_date_end']   = $this->getDateByDays($row['hire_date'], $this->probation_second_deadline_end_two, 1);

                    }
                    $row['first_deadline_date']  = $row['first_deadline_date_start'] . '——' . $row['first_deadline_date_end'];
                    $row['second_deadline_date'] = $row['second_deadline_date_start'] . '——' . $row['second_deadline_date_end'];
				}
				
				$row['status'] = $item['probation_status'] ?? '1';
				$row['status_text'] = (static::$t)->_("probation_status_" . $row['status']);
				$row['first_score'] = $item['first_score'] ? bcdiv($item['first_score'], 1000, 2) : '0';
				$row['second_score'] = $item['second_score'] ? bcdiv($item['second_score'], 1000, 2) : '0';
				$row['formal_at'] = $item['formal_at'] ?? $this->getDateByDays($row['hire_date'], $this->formal_days, 1);
				
				if (empty($item['hire_date'])) {
					$row['days'] = 0;
				} else {
					$datetime_start = new \DateTime($item['hire_date']);
					$datetime_end = new \DateTime($now_time);
					$row['days'] = $datetime_start->diff($datetime_end)->days;
				}
				
				$row['identity'] = $item['identity'] ?? "";
				$row['mobile_company'] = $item['mobile_company'] ?? "";
				$row['node_department_name'] = $this->showDepartmentName($item['node_department_id']);
				
				//试用期状态按钮是否变红
				$row['is_not_pass'] = '0';
				//编辑按钮是否展示
				$row['is_edit_show'] = $this->isCanEdit($row);
				
				//试用期状态为未通过 或者 已过第二次评估期限但状态为试用期中
				if ($row['status'] == StaffEnums::STATUS_NOT_PASS || ($now_time > $row['second_deadline_date_end'] && $row['status'] == StaffEnums::STATUS_PROBATION)) {
					$row['is_not_pass'] = '1';
				}
				$row['cur_level'] = $item['cur_level'];
				
				//审批意见
				$row['remark'] = $item['remark'] ?? '';
				
				//试用期转正时候的备注
				$row['mark'] = $item['mark'] ?? '';
				
				$row['piece_name'] = $pieceArr[$item['sys_store_id']] ?? "";
                $row['is_show_download'] = true; // 下载按钮除权限外的展示逻辑
			}
			$return['rows'][] = $row;
		}

		return $return;
	}
	

	
	
  
   
    /**
     * 下载
     */

    public function download($staff_info_id)
    {
        $data = [];
        $builder = new  Builder();
        $execute = $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.job_title_level',
            'hsi.job_title_grade_v2',
            'hsi.hire_date',
            'job.job_name',
            'd.name   as department_name',
            'n.name as node_department_name',
            'hp.id as probation_id',
            'hp.formal_at',
            'hp.is_delay'
        ])->from(['hsi' => HrStaffInfoReadModel::class])
            ->leftJoin(HrProbationModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hp')
            ->leftJoin(HrJobTitleModel::class, 'job.id = hsi.job_title', 'job')
            ->leftJoin(SysDepartmentModel::class, 'd.id = hsi.sys_department_id', 'd')
            ->leftJoin(SysDepartmentModel::class, 'n.id = hsi.node_department_id', 'n')
            ->where('hsi.staff_info_id =:id:', ['id' => intval($staff_info_id)])->getQuery()->getSingleResult();
        if ($execute) {
            $data = $execute->toArray();
        }


        $data['first_deadline_date'] = "";
        $data['second_deadline_date'] = "";
        if (!empty($data['hire_date'])) {
            $data['hire_date'] = $this->getDateByDays($data['hire_date'], 0, 1);
            $data['first_deadline_date'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays($data['hire_date'], $this->probation_first_deadline_end, 1) : $this->getDateByDays(
                $data['hire_date'],
                $this->probation_first_deadline_end_two,
                1
            );
            $data['second_deadline_date'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays($data['hire_date'], $this->probation_second_deadline_end, 1) : $this->getDateByDays(
                $data['hire_date'],
                $this->probation_second_deadline_end_two,
                1
            );

            if (empty($data['formal_at'])) {
                $data['formal_at'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays($data['hire_date'], $this->formal_days, 1) : $this->getDateByDays(
                    $data['hire_date'],
                    $this->formal_days_two,
                    1
                );
            }
        }

        $data['comment'] = "";

        $data['probation_id'] = $data['probation_id'] ?? 0;
        $data['job_title_level'] = !empty($data['job_title_level']) ? $data['job_title_level'] : 1;
        $data['job_title_level_text'] = StaffEnums::JOB_TITLE_LEVEL_DESC[$data['job_title_level']];

        $data['job_title_grade_v2'] = $data['job_title_grade_v2'] ?? 0;
        $data['job_title_grade_v2_text'] = 'F' . $data['job_title_grade_v2'];


        if (!empty($data['probation_id'])) {
            $data['comment'] = $this->getProbationLogs($data['probation_id'])['comment'];
        }
        $data['rules'] = [];
        for ($i = 1; $i <= 6; $i++) {
            $data['rules'][] = (static::$t)->_("hr_probation_rule_" . $i);
        }

        $item = HrProbationAuditModel::findFirst([
            'conditions' => 'probation_id = ?1',
            'order'      => 'id desc',
            'bind'       => [
                1 => $data['probation_id'],
            ]
        ]);
        if ($item) {
            $item = $item->toArray();
            $tpl_id = $item['tpl_id'];
            $score = $item['score'];
        } else {
            $tpl_id = $this->getTplIdByJobTitleGradeV2($data['job_title_grade_v2']);
            $score = null;
        }
        $tpl = $this->getTplItem($tpl_id, $score);
        if (is_string($tpl)) {
            throw new \Exception($tpl);
        }
        $data['score'] = $tpl['score'];
        $data['question_num'] = $tpl['question_num'];
        $data['score_rule_text'] = $tpl['score_rule_text'];
        $data['job_content'] = $item['job_content'] ?? '';
        //试用期时间从-到-
        $data['probation_per_iod'] = (static::$t)->_("hr_probation_field_probation_period_dt");
        $data['probation_per_iod'] = sprintf($data['probation_per_iod'], $data['hire_date'], $data['formal_at']);

        $data['score']['score_text'] = $this->getScoreGrade($data['score']['score']);
        $data['score']['second_score_text'] = $this->getScoreGrade($data['score']['second_score']);

        $data['field'] = [];

        $fieldArr = [
            "name",
            "staff_info_id",
            "job_title",
            "job_title_level",
            "department",
            "hire_date",
            "first_deadline_date",
            "second_deadline_date",
            "rule",
            "info",
            "weight",
            "first_score",
            "second_score",
            "comment",
            "attendance",
            'attendance_info',
            "first_check",
            "second_check",
            "late",
            "sick",
            "casual",
            "lack",
            "alert",
            "first_alert",
            "second_alert",
            "from",
            "all_info",
            "company_vn",
            "grade_vn",
            "probation_per_iod",//试用期时间
            "work_responsibilities",//工作内容
        ];

        foreach ($fieldArr as $field) {
            $data['field'][$field] = (static::$t)->_("hr_probation_field_" . $field);
        }
        $data['field']['tpl_name'] = (static::$t)->_("hr_probation_field_title_vn_" . $tpl_id);
        $data['attendance'] = $this->getAttendance($staff_info_id);
        $data['alert'] = $this->getAlert($staff_info_id, $data['hire_date'], $data['formal_at']);

        $this->outPdf($staff_info_id, 'staff-vn', $data);
    }


    /**
     * 导出
     * @param $params
     * @throws \Exception
     */
    public function export($params)
    {
        $params['is_export'] = 1;
        $params['page']     = 1;
        $params['pagesize'] = 1000;
        $data['rows'] = [];
        while (true) {
            $_data = $this->getList($params);
            if (empty($_data['rows'])) {
                break;
            }
            $data['rows'] = array_values(array_merge($data['rows'],$_data['rows']));
            $params['page']++;
        }

        $fieldArr = [
            "staff_info_id",
            "name",
            "name_en",
            "identity",
            "mobile_company",
            "state_name",
            "manage_staff_name",
            "manage_staff_mobile",
            "manage_staff_id",
            "manage_higher_staff_name",
            "manage_higher_staff_mobile",
            "manage_higher_staff_id",
            'status_text',
            'remark',
            "hire_date",
            "formal_at",
            "days",
            "job_title_name",
            "sys_department_name",
            "node_department_name",
            "sys_store_name",
//            "store_area_text",
//            "piece_name",
            "first_deadline_date",
            "first_audit_status_name",
            "first_status_name",
            "second_deadline_date",
            "second_audit_status_name",
            "second_status_name",
            "first_score",
            "second_score",
        ];

        $langArr = [
            "staff_info_id",
            "name",
            "name_en",
            "identity",
            "mobile_company",
            "state",
            "manage_staff_name",
            "manage_staff_mobile",
            "manage_staff_id",
            "manage_higher_staff_name",
            "manage_higher_staff_mobile",
            "manage_higher_staff_id",
            'status_text',
            'remark',
            "hire_date",
            "formal_at",
            "days",
            "job_title",
            "sys_department_name",
            "node_department_name",
            "sys_store_name",
//            "store_area_text",
//            "piece_name",
            "excel_first_deadline_date",
            "first_status",//第一次评估状态
            "first_name",//第一次评估结果
            "excel_second_deadline_date",
            "second_status", //第二次评估状态
            "second_name",//第二次评估结果
            "excel_first_score",
            "excel_second_score",
        ];

        $header = [];
        foreach ($langArr as $k => $v) {
            $header[] = (static::$t)["hr_probation_field_".$v];
        }

        $data_list = [];
        foreach ($data['rows'] as $k => $v) {
            foreach ($fieldArr as $kk => $vv) {
                $data_list[$k][$vv] = $v[$vv];
            }
            $data_list[$k] = array_values($data_list[$k]);
        }

        //3.生成Excel URL
        $file_name = $params['file_name'];
        $file_data = $this->exportExcel($header, $data_list ?? [], $file_name);
        $flashOss  = new FlashOss();
        $ossObject = env('country_code').'/probation/'.$file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);
        return $ossObject;
    }
}