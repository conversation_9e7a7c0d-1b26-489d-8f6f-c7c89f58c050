<?php

namespace app\modules\Th\tasks;

use App\Library\BaseService;
use App\Library\FlashOss;
use App\Models\backyard\HcmExcelTackModel;
use App\Modules\Th\Services\SuspensionService;
use App\Services\ExcelService;
use StaffSuspensionExportTask as BaseStaffSuspensionExportTask;

class StaffSuspensionExportTask extends BaseStaffSuspensionExportTask
{
    public function mainAction($input)
    {
        //获取任务参数
        $input = !isset($input[0]) ? (new ExcelService())->getHcmExcelTaskArgv() : $input;
        if (empty($input[1])) {
            return $this->echoErrorLog('task not isset :'.$input[1], true);
        }

        $this->echoInfoLog('StaffSuspensionExportTask start', true);

        $params = json_decode(base64_decode($input[0]), true);
        $this->echoInfoLog('StaffSuspensionExportTask params :'.json_encode($params, JSON_UNESCAPED_UNICODE), true);

        BaseService::setLanguage($params['lang']);

        $task_id   = $input[1];
        $excelTask = HcmExcelTackModel::findFirst($task_id);

        if (empty($excelTask)) {
            return $this->echoErrorLog('task not isset :'.$task_id, true);
        }
        $file_name = $excelTask->file_name;

        $params['is_download']   = 1;
        $suspensionService = new SuspensionService();
        $data = $suspensionService->getList($params);

        $list = !empty($data['list']) ? $data['list'] : [];

        //设置语言
        $t = BaseService::getTranslation($params['lang']);

        $new_data = [];
        $header   = [
            $t['staff_info_id'], //工号
            $t['name'], //姓名
            $t['hire_type'], //雇佣类型
            $t['job_title'], //职位
            $t['node_department_name'],  //所属部门
            $t['sys_store_name'], //所属网点
            $t['regional'], //所属大区
            $t['area'], //所属片区
            $t['stop_duties_date'], //停职日期
            $t['stop_duty_reason'], //停职原因
            $t['audit_state_text'], //恢复在职申请审批
            $t['staff_state'], //在职状态
            $t['hire_date'], //在职状态
            $t['stop_duties_count'], //停职次数
        ];

        foreach ($list as $key => $value) {
            $new_data[] = [
                $value['staff_info_id'],
                $value['name'],
                $value['hire_type_text'], //雇佣类型
                $value['job_title_name'],
                $value['department_name'],
                $value['store_name'],
                $value['region_name'],
                $value['piece_name'],
                $value['suspension_date'],
                $value['reason'],
                $value['audit_state_text'],
                $value['state_text'],
                $value['hire_date'],
                $value['suspension_number'],
            ];
        }

        $file_data = $suspensionService->exportExcel($header, $new_data, $file_name);

        $flashOss  = new FlashOss();
        $ossObject = 'staff_suspension/'.date('Ymd').'/'.$excelTask->file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);

        $excelTask->path      = $ossObject;
        $excelTask->status    = 1;
        $excelTask->finish_at = gmdate('Y-m-d H:i:s', time() + $this->config->application->add_hour * 3600);
        $excelTask->save();

        $this->echoInfoLog('StaffSuspensionExportTask end', true);
    }
}