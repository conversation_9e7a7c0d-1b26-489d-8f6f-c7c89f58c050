<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 4/17/24
 * Time: 2:09 PM
 */


namespace App\Modules\Th\Services;

use App\Models\backyard\StaffAuditModel;
use App\Services\LeaveBllService as GlobalServer;

class LeaveBllService extends GlobalServer
{

    public function leaveTypeToCode($str)
    {
        $list = [
            'annual leave'                       => StaffAuditModel::LEAVE_TYPE_1,
            'personal leave'                     => StaffAuditModel::LEAVE_TYPE_2,
            'sick leave'                         => StaffAuditModel::LEAVE_TYPE_38,
            'maternity leave'                    => StaffAuditModel::LEAVE_TYPE_4,
            'paternity leave'                    => StaffAuditModel::LEAVE_TYPE_5,
            'prenatal check-up'                  => StaffAuditModel::LEAVE_TYPE_17,
            'sterilization leave'                => StaffAuditModel::LEAVE_TYPE_8,
            'personal trained leave'             => StaffAuditModel::LEAVE_TYPE_9,
            'pabbajja leave'                     => StaffAuditModel::LEAVE_TYPE_11,
            'marriage leave'                     => StaffAuditModel::LEAVE_TYPE_10,
            'national military training holiday' => StaffAuditModel::LEAVE_TYPE_6,
            'personal leave(unpaid)'             => StaffAuditModel::LEAVE_TYPE_12,
            'funeral leave'                      => StaffAuditModel::LEAVE_TYPE_7,
            'international family leave'         => StaffAuditModel::LEAVE_TYPE_19,
            'flash training leave'               => StaffAuditModel::LEAVE_TYPE_16,
            'day off'                            => StaffAuditModel::LEAVE_TYPE_15,
            'reject task notice'                 => StaffAuditModel::LEAVE_TYPE_40,

        ];
        $str = strtolower($str);
        return $list[$str] ?? '';
    }

}