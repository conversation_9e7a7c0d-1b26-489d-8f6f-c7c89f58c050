<?php

namespace  App\Modules\Th\library\Enums;


use App\Models\backyard\StaffWarningMessage;

final class MessWarningEnums
{
    //HOLD类型
    const HOLD_TYPE_NO = 1;
    const HOLD_TYPE_SALARY = 2;
    const HOLD_TYPE_INCENTIVE = 3;
    const HOLD_TYPE_BOTH = 4;
    static $hold_type = [
        self::HOLD_TYPE_INCENTIVE => 'warning_hold_type_3',
        self::HOLD_TYPE_NO => 'warning_hold_type_1',
//        self::HOLD_TYPE_SALARY => 'warning_hold_type_2',
        self::HOLD_TYPE_BOTH => 'warning_hold_type_4',
    ];

    //在枚举中不展示的举报原因
    static $menuReportReasonNo = [
        1 => 't_warning_1',
        3 => 't_warning_3',
        7 => 't_warning_7',
        14 =>'t_warning_14'
    ];

    //举报类型
    static $reportType = [
        '1' => 't_report_type_investigate',
        '2' => 't_report_type_warning',
        '3' => 't_report_type_3', // 开除
    ];

    //举报处理状态
    const REPORT_WARNING_STATUS_UN_PROCESS = 1;//待处理
    const REPORT_WARNING_STATUS_WARNING    = 2;//已警告
    const REPORT_WARNING_STATUS_UN_WARNING = 3;//不警告
    const REPORT_WARNING_STATUS_NO_PROCESS = 4;//无需处理
    static $reportStatus = [
        '1' => 't_report_status_un_process',
        '2' => 't_report_status_warning',
        '3' => 't_report_status_un_warning',
        '4' => 't_report_status_no_process',//无需处理
    ];

    //qaqc专员
    const QAQC_SPECIALIST = 52;

    //安全监督员
    const SECURITY_MANAGER = 30;

    //警告状态
    const NO_WARNING_STATUS = 3;
    const NO_WARNING_TYPE = 0;
    const YES_WARNING_STATUS = 2;

    // 前端显示网点分类
    const STORE_TYPE_ALL = 0;
    const STORE_TYPE_DC_SP = 1;
    const STORE_TYPE_HUB = 2;
    const STORE_TYPE_SHOP = 3;
    const STORE_TYPE_HO = 4;
    const STORE_TYPE_OS = 5;
    const STORE_TYPE_BDC = 6;
    const STORE_TYPE_B_HUB = 7;
    const STORE_TYPE_FFM = 8;
    const STORE_TYPE_CDC = 13;
    static $store_type_map = [
        self::STORE_TYPE_ALL => 'ALL',
        self::STORE_TYPE_DC_SP => 'DC/SP',
        self::STORE_TYPE_HUB => 'HUB',
        self::STORE_TYPE_SHOP => 'SHOP',
        self::STORE_TYPE_HO => 'HO',

        self::STORE_TYPE_OS => 'OS',
        self::STORE_TYPE_BDC => 'BDC',
        self::STORE_TYPE_B_HUB => 'B-HUB',
        self::STORE_TYPE_FFM => 'FFM',
        self::STORE_TYPE_CDC => 'CDC'
    ];

    //签字状态
    const SIGN_STATE_1 = 1;
    const SIGN_STATE_2 = 2;
    const SIGN_STATE_3 = 3;
    static $sign_state_desc = [
        self::SIGN_STATE_1 => 'sign_state_1',
        self::SIGN_STATE_2 => 'sign_state_2',
        self::SIGN_STATE_3 => 'sign_state_3',
    ];
    //警告类型，上面那个其实是警告原因
    const WARNING_TYPE_1 = 1;
    const WARNING_TYPE_2 = 2;
    const WARNING_TYPE_3 = 3;
    public static $warning_types = [
        self::WARNING_TYPE_1 => 'warning_type_1',
        self::WARNING_TYPE_2 => 'warning_type_2',
        self::WARNING_TYPE_3 => 'warning_type_3',
    ];

    const WARNING_TYPE_NUMBERING = [
        self::WARNING_TYPE_1 => "V",
        self::WARNING_TYPE_2 => "WL",
        self::WARNING_TYPE_3 => "WL",
    ];

    const WARNING_ITEMS_V2 = 'warning_times_v2';
    const WARNING_ITEMS_V3 = 'warning_times_v3';

    //审批状态
    static $approvalStatus = [
        1 => 'by_state_1',
        2 => 'by_state_2',
        3 => 'by_state_3',
        4 => 'by_state_4',
        5 => 'by_state_5',
    ];

    //data_fix_status：最终处理意见列表
    public static $dataFixStatusList = [
        StaffWarningMessage::DATA_FIX_STATUS_1,
        StaffWarningMessage::DATA_FIX_STATUS_2,
        StaffWarningMessage::DATA_FIX_STATUS_3,
        StaffWarningMessage::DATA_FIX_STATUS_4,
    ];
    //data_fix_status：最终处理意见列表翻译key
    public static $dataFixStatusListKey = [
        StaffWarningMessage::DATA_FIX_STATUS_1 => 'data_fix_status_1',
        StaffWarningMessage::DATA_FIX_STATUS_2 => 'data_fix_status_2',
        StaffWarningMessage::DATA_FIX_STATUS_3 => 'data_fix_status_3',
        StaffWarningMessage::DATA_FIX_STATUS_4 => 'data_fix_status_4',
    ];
}

