<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/8/4
 * Time: 2:43 PM
 */

namespace App\Modules\La\Controllers;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\Validation;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Services\ProbationService;
use App\Modules\La\library\Enums\enums;
use App\Modules\La\Services\WorkdayService;
use App\Services\StaffPublicHolidayService;
use App\Controllers\WorkdayController as BaseController;
use App\Services\StaffService;
use App\Services\SysService;

class WorkdayController extends BaseController
{

    /**
     * @Token
     * @Permission(action='work_day_view')
     */
    public function staff_workdays_listAction()
    {
        $param['store_id']      = empty($this->paramIn['store_id']) ? [] : $this->paramIn['store_id'];
        $param['state']      = empty($this->paramIn['state']) ? [] : $this->paramIn['state'];
        $param['area']          = empty($this->paramIn['area']) ? '' : $this->paramIn['area'];
        $param['job_title']     = empty($this->paramIn['job_title']) ? '' : $this->paramIn['job_title'];
        $param['staff_info_id'] = empty($this->paramIn['staff_info_id']) ? '' : $this->paramIn['staff_info_id'];
        //部门页面搜索
        $param['search_department'] = empty($this->paramIn['search_department']) ? '' : $this->paramIn['search_department'];
        //分页
        $param['start']  = empty($this->paramIn['start']) ? 0 : $this->paramIn['start'];
        $param['length'] = empty($this->paramIn['length']) ? 30 : $this->paramIn['length'];

        $work_type                 = empty($this->paramIn['work_type']) ? '0_0' : $this->paramIn['work_type'];
        $param['week_working_day'] = explode('_', $work_type)[0];
        $param['rest_type']        = explode('_', $work_type)[1];
        $param['hire_type']        = $this->paramIn['hire_type']??[];

        //员工信息
        $staff_bll         = new WorkdayService();
        $staff_id          = $this->userinfo['id'];
        $param['operator'] = $staff_id;

        //获符合筛选条件和登录用户可视范围数据权限的员工
        $result = $staff_bll->search_staff_workdays($param, $this->userinfo['id']);

        if (!empty($result['data'])) {
            $param['month'] = $this->paramIn['month'] ?? '';
            $result['data'] = $staff_bll->formatList($param, $result['data']);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * @Token
     */
    public function area_listAction()
    {
        $parcel_model = enums::$store_sorting_no;
        $return       = [];
        foreach ($parcel_model as $k => $v) {
            $row['code']  = $k;
            $row['value'] = $v;
            $return[]     = $row;
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $return);
    }


    /**
     * @Token
     * 单个人 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_workdaysAction(){
        $params['staff_info_id'] =  $this->paramIn['staff_info_id'];
        $params['date'] =  $this->paramIn['date'];
        $validation = [
            'staff_info_id'=> 'Required|int|>>>:staff_info_id error',
            'date' => 'Required|Date|>>>:date error',
        ];
        Validation::validate($params, $validation);

        $workdayService = new WorkdayService();
        $workdayService->handle([$params['staff_info_id']],[$params['date']],$this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }


    /**
     * @Token
     * 批量 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_batch_workdaysAction(){
        $params['staff_info_id'] =  $this->paramIn['staff_info_id'];
        $params['date'] =  $this->paramIn['date'];
        $validation = [
            'staff_info_id'=> 'Required|Arr|>>>:staff_info_id error',
            'date' => 'Required|Arr|>>>:date error',
        ];
        Validation::validate($params, $validation);

        $workdayService = new WorkdayService();
        $workdayService->setSrc(2);
        $workdayService->handle($params['staff_info_id'],$params['date'],$this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }

    /**
     * @Token
     * @Permission(action='work_day_setting')
     */
    public function cancel_shiftAction(){
        $staff_id = $this->paramIn['staff_info_id'];
        $date =$this->paramIn['date'];
        $operator = $this->paramIn['operator'];

        if(empty($staff_id) || empty($date)){
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'wrong param', []);
        }

        //能不能修改自己的轮休
        $workday_bll = new WorkdayService();
        $workday_bll->checkSelfEdit($operator, $staff_id);
        $flag = $workday_bll->checkTruckUpdatePermission([$staff_id], $operator);
        if(!$flag){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR,$this->t->_('workday_truck_permission'), []);
        }
        //验证员工
        $staffInfo = (new StaffService())->getHrStaffInfo($staff_id);
        if(empty($staffInfo)){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_LEAVE_ERROR,$this->t->_('staff_id_not_found'), []);
        }
        //只能取消当天之后的
        $today = date('Y-m-d');
        $sysService = new SysService;
        $isWorkDayRootId =$sysService->setCurrentStaffId($this->user['id'])->isWorkDayRootId();
        $canModifyTodayRest = $sysService->setCurrentStaffId($this->user['id'])->canModifyTodayRest()
            && $staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_SIX &&  $staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_1;
        if (!$isWorkDayRootId) {
            if ($canModifyTodayRest && $date < $today || !$canModifyTodayRest && $date <= $today) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'can not cancel,wrong date chosen', []);
            }
        }
        //当天有审批通过或者待审批的加班申请，请撤销加班申请后再调整休息日
        $check_overtime = $workday_bll->check_overtime($staff_id,$date);
        if(!empty($check_overtime)){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR,$this->t->_('workdays_check_overtime_alert_v2',['staff_info_id'=>$staff_id,'name'=>$staffInfo['name']]), []);
        }
        //当天有请假申请，限制不能取消休息日
        $check_leave_all =  $workday_bll->check_leave_all($staff_id,$date);
        if(!empty($check_leave_all)){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_LEAVE_ERROR,$this->t->_('workdays_check_leave_alert_v2',['staff_info_id'=>$staff_id,'name'=>$staffInfo['name']]), []);
        }

        $workday_bll->cancelOffDay($staff_id,$date, $operator);

        //日志
        $this->logger->info("staff_workdays_cancel {$staff_id} {$date} {$operator}");
        //发送消息 取消
        $workday_bll->staff_set_work_day_send_message($staff_id, $date, 1);//轮休发送消息取消
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }


}