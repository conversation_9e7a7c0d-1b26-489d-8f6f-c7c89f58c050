<?php

namespace App\Modules\La\Services;

use App\Library\AttendanceEnums;
use App\Library\AttendanceUtil;
use App\Library\DateHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Modules\La\library\Enums\enums;
use App\Services\AttendanceStatisticsService as BaseAttendanceStatisticsService;
use App\Services\StaffPublicHolidayService;
use App\Services\StaffSupportStoreServer;
use App\Services\SysStoreService;

class AttendanceStatisticsService extends BaseAttendanceStatisticsService
{

    /**
     * 查询attendance_data_v2的字段
     * @var string[]
     */
    protected $solidify_attendance_data_v2_query_field = [
        'leave_type',
        'attendance_started_at',
        'attendance_end_at',
        'stat_date',
        'staff_info_id',
        'shift_start',
        'shift_end',
        'late_times',       //迟到
        'leave_early_times',//早退
        'OFF',
        'BT',
        'AB',
        'PH',
        'job_title',
    ];

    protected $staff_sex = [];

    /**
     * 获取员工考勤数据
     * @param $params
     */
    public function getStaffsAttendanceInfo($params)
    {
        if (empty($params['start_date'])) {
            throw new ValidationException('start_date empty');
        }

        if (empty($params['end_date'])) {
            throw new ValidationException('end_date empty');
        }

        $returnData = [
            'total' => 0,
            'list'  => [],
        ];
        //获取员工信息
        if ($this->query_type == 2) {
            $staffs = $this->getHarderOfficeStaffInfo($params);
        } else {
            $staffs = $this->getStoreStaffInfo($params);
        }

        if (!$staffs['list']) {
            return $returnData;
        }

        [$returnData['total'], $list] = [$staffs['total'], $staffs['list']];
        //获取考勤信息

        $params['staff_info_ids'] = array_column($list, 'id');

        //获取考勤信息
        $attendanceData = $this->getAttendanceData($params);

        //获取请假信息
        $apply_info = $this->getStaffAuditInfo($params);

        //获取出差打卡数据
        $reissue_info = $this->getStaffAuditReissueInfo($params);
        $backyard_attendance_bll = new BackyardAttendanceService();

        //出差
        $trip_where = " and apply_user in ({apply_user:array}) ";
        $trip_where_params['apply_user'] = $params['staff_info_ids'] ?? [0];

        $trip_data = $backyard_attendance_bll->getTripData(
            $params['start_date'] ?? '',
            $params['end_date'] ?? '',
            $trip_where,
            $trip_where_params
        );


        //用固化表的数据是最好的
        $this->getStaffWeekWorkingDay($params);

        //获取固化的员工班次信息
        $staff_shift_history_data = $this->getShiftByDate($params);

        $staff_week_working_data = array_column($list, 'week_working_day', 'id');
        $this->staff_sex = array_column($list, 'sex', 'id');
        //构建考勤数据
        $attendanceData = $attendanceData ? $this->handleAttendanceDataToViewV2(
            $params,
            $attendanceData,
            $staff_week_working_data,
            $apply_info,
            $reissue_info,
            $trip_data,
            $staff_shift_history_data
        ) : [];

        $returnData['list'] = $this->handelAttendanceOtherInfo($list, $attendanceData, $params);
        return $returnData;
    }

    /**
     * 整理员工信息
     * @param $staff_list
     * @param $attendanceData
     * @param $params
     * @return mixed
     */
    protected function handelAttendanceOtherInfo($staff_list, $attendanceData, $params)
    {
        $sysService = new SysService();
        $region_info = array_column($sysService->getRegionListFromCache(), 'name', 'id');
        $piece_info = array_column($sysService->getPieceListFromCache(), 'name', 'id');
        $store_info = array_column($sysService->getStoreListFromCache(), 'name', 'id');
        //网点区域
        $sorting_no_map =  array_column( $sysService->storeGeographyCodeFromCache(),'label','value');
        //网点类型
        $store_category_map =  SysStoreService::$category;
        $sorting_no_map = array_flip($sorting_no_map);

        foreach ($staff_list as &$item) {
            $item['attend_data'] = $attendanceData[$item['id']] ?? $this->getEmptyAttendance($params);
            $item['manage_region'] = $item['manage_region'] ? $region_info[$item['manage_region']] : '';
            $item['manage_piece'] = $item['manage_piece'] ? $piece_info[$item['manage_piece']] : '';

            $item['state'] = $item['wait_leave_state'] == 1 && in_array($item['state'], [1, 2]) ? self::$t->_(
                'wait_leave_state_1'
            ) : self::$t->_('staff_state_' . $item['state']);
            $item['store_name'] = $this->query_type == 1 ? ($store_info[$item['organization_id']] ?? '') : '';
            //统计旷工
            $item['absenteeism_num'] = array_sum(array_column($item['attend_data'], 'absenteeism_num'));
            //迟到统计
            $item['late_num'] = array_sum(array_column($item['attend_data'], 'late_num'));
            //迟到分钟数统计
            $item['late_time'] = array_sum(array_column($item['attend_data'], 'late_time'));
            //早退统计
            $item['leave_early_num'] = array_sum(array_column($item['attend_data'], 'leave_early_num'));
            //早退分钟数
            $item['leave_early_time'] = array_sum(array_column($item['attend_data'], 'leave_early_time'));
            //网点区域
            $item['manage_geography_code'] = isset($item['sorting_no']) ? ($sorting_no_map[$item['sorting_no']] ?? '') : '';
            $item['manage_geography_code_name'] = isset($item['sorting_no']) ? (isset($sorting_no_map[$item['sorting_no']]) ? $item['sorting_no'] : '') : '';
            //网点类型             s.category as store_category,
            $item['store_category_name'] = isset($item['store_category']) ? ($store_category_map[$item['store_category']] ?? '') : '';
            unset($item['sorting_no']);
        }
        return $staff_list;
    }
    /**
     * 与泰国有差异化
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getStoreStaffRegion($builder, $params)
    {
        if (!empty($params['geography_code'])) { // 区域
            $sorting_no = [];
            foreach ($params['geography_code'] as $oneCode) {
                $sorting_no[] = enums::$store_sorting_no_select[$oneCode];
            }
            $builder->andWhere(
                's.sorting_no in ({sorting_no:array})',
                ['sorting_no' => $sorting_no]
            );
        }
        return $builder;
    }


    /**
     * 构建考勤数据
     * @param array $params 日期等参数
     * @param array $attendanceDataAll 考勤信息
     * @param array $staff_week_working_data 员工工作制信息
     * @param array $apply_info 请假信息
     * @param array $reissue_info 出差打卡数据
     * @param array $trip_data 出差数据
     * @param array $staff_shift_history_data 历史班次信息
     * @return array
     */
    protected function handleAttendanceDataToViewV2(
        $params,
        array $attendanceDataAll,
        array $staff_week_working_data,
        array $apply_info,
        array $reissue_info = [],
        array $trip_data = [],
        array $staff_shift_history_data = [],
        array $staticStaffInfoData = []
    ) {
        $returnData = [];
        if (empty($attendanceDataAll)) {
            return $returnData;
        }

        $date_range = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));

        //请假数据
        $apply_info = !empty($apply_info) ? $this->handleAuditDataUseToSelf($apply_info) : [];

        //出差打卡数据

        $reissue_info = !empty($reissue_info) ? $this->handleAuditReissueDataUseToSelf($reissue_info) : [];

        $trap_data2date = AttendanceUtil::btSection2Date($date_range, $trip_data);

        //历史班次信息
        $staff_shift_history_data = !empty($staff_shift_history_data) ? $this->handleShiftByDate($staff_shift_history_data) : [];

        //获取现在员工班次
        $hr_staff_shift_data = $this->getStaffShift($params);

        //获取员工所属国家
        $country_info = $this->getStaffCountryInfo($params['staff_info_ids'] ?? []);
        //获取法定假日
        $holidayParams = [];
        foreach ($params['staff_info_ids'] as $staff_info_id) {
            $holidayParams[] = [
                'staff_info_id'    => $staff_info_id,
                'sex'              => $this->staff_sex[$staff_info_id] ?? HrStaffInfoModel::SEX_MALE,
                'country_id'       => $country_info[$staff_info_id] ?? HrStaffItemsModel::NATIONALITY_1,
                'week_working_day' => $staff_week_working_data[$staff_info_id] ?? HrStaffInfoModel::WEEK_WORKING_DAY_6,
            ];
        }
        $staffHoliday = (new HolidayService())->getStaffHoliday(['staff' => $holidayParams,'date'=>$params['start_date']]);
        //员工休息日
        $offDay = $this->getStaffOffDayData(
            $params['start_date'],
            $params['end_date'],
            $params['staff_info_ids']
        );

        //员工个人补的ph
        $staffAddPublicHoliday = (new StaffPublicHolidayService())->getMultiStaffData($params['staff_info_ids']);


        //支援信息
        $supportServer = new StaffSupportStoreServer();
        $supportData = $supportServer->getSupportDataBetween($params['staff_info_ids'], $params['start_date'], $params['end_date']);
        $supportData = $supportServer->formatSupportData($supportData, $params['start_date'], $params['end_date']);
        $supportText = self::$t->_("on_support");

        //请假枚举
        $leave_type_config = AttendanceEnums::$leave_type;

        $attendanceDataAll = array_chunk($attendanceDataAll,500);

        foreach ($attendanceDataAll as $attendanceData) {
            foreach ($attendanceData as $value) {
                $type = "";
                $reason = "";
                $image_path = "";
                $background = 0;
                $start_at = self::$t->_("lack_of_work_card");//默认缺卡
                $end_at = self::$t->_("lack_of_work_card");  //默认缺卡
                $leave_type = [];
                $_leave_type = [];
                $holiday_str = '';
                $is_punch_in = 0;
                $is_punch_out = 0;

                //计算旷工天数
                $absenteeism_num = 0;
                //计算迟到次数
                $late_num = 0;
                //计算迟到时长
                $late_time = 0;
                //计算早退次数
                $leave_early_num = 0;
                //计算早退时长
                $leave_early_time = 0;
                //出差状态拼接
                $business_travel = '';

                //当前员工的ph
                $currentStaffHoliday = array_merge( $staffHoliday[$value['staff_info_id']] ?? [],$staffAddPublicHoliday[$value['staff_info_id']]??[]);


                if (!empty($value['shift_start']) && strlen($value['shift_start']) < 5) {
                    $value['shift_start'] = '0'.$value['shift_start'];
                }
                if (!empty($value['shift_end']) && strlen($value['shift_end']) < 5) {
                    $value['shift_end'] = '0'.$value['shift_end'];
                }
                //如果当v 2 表里班次信息不存在 则尝试获取固化班次信息
                $value['shift_start'] = empty($value['shift_start']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['start']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['start'] : $value['shift_start'];
                $value['shift_end']   = empty($value['shift_end']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['end']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['end'] : $value['shift_end'];
                $value['shift_type']  = empty($value['shift_type']) && isset($staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_type']) ? $staff_shift_history_data[$value['staff_info_id']][$value['stat_date']]['shift_type'] : $value['shift_type'];
                //如果固化表也没有获取 hr_staff_shift 表班次
                $value['shift_start'] = empty($value['shift_start']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_start'] : $value['shift_start'];
                $value['shift_end']   = empty($value['shift_end']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_end'] : $value['shift_end'];
                $value['shift_type']  = empty($value['shift_type']) && isset($hr_staff_shift_data[$value['staff_info_id']]) ? $hr_staff_shift_data[$value['staff_info_id']]['shift_type'] : $value['shift_type'];


                //出差打卡 重新赋值打卡时间和班次
                $value = $this->assignReissueAttendanceData($reissue_info, $value);
                $business_travel = $value['business_travel'] ?? '';

                $s1 = !empty($value['shift_start']) ? $value['shift_start'] : '08:00';       //上班班次
                $s2 = !empty($value['shift_end']) ? $value['shift_end'] : '17:00';           //下班班次
                $check_start = date('Y-m-d H:i', strtotime($value['attendance_started_at']));//上班打卡时间
                $shift_start = "{$value['stat_date']} {$s1}";
                $shift_start = date('Y-m-d H:i', strtotime($shift_start));             //班次开始时间
                $check_end = date('Y-m-d H:i', strtotime($value['attendance_end_at']));//下班打卡时间
                $shift_end = "{$value['stat_date']} {$s2}";
                $shift_end = date('Y-m-d H:i', strtotime($shift_end));
                if ($s1 > $s2) {
                    $shift_end = date('Y-m-d H:i', strtotime("{$shift_end} +1 day"));
                }


                //$background 0 白色  1 灰色 2 黄色  3 红色





                //上班打卡
                if (!empty($value['attendance_started_at'])) {
                    //迟到
                    $is_punch_in = 10;
                    //打卡时间大于班次时间 是迟到
                    if ($check_start > $shift_start) {
                        $background = 2; //黄色
                        $is_punch_in = 5;
                    }

                    $start_at = date('H:i', strtotime($value['attendance_started_at'])).$business_travel;
                }

                if (!empty($value['attendance_end_at'])) {
                    //早退
                    $is_punch_out = 10;
                    if ($check_end < $shift_end) {
                        $background = 2;
                        $is_punch_out = 5;
                    }

                    $end_at = date('H:i', strtotime($value['attendance_end_at'])).$business_travel;
                }

                //正常打卡
                if ($is_punch_out == 10 && $is_punch_in == 10) {
                    $background = 0;
                }

                //优先使用固化表的工作制
                $week_working_day = $this->static_staff_week_working_data[$value['staff_info_id']][$value['stat_date']] ?? $staff_week_working_data[$value['staff_info_id']];

                //打卡时间
                //			$punch_info = "in:{$start_at} out:{$end_at}";


                // AB
                if (empty($is_punch_out) && empty($is_punch_in) && !empty($value['AB'])) {
                    $type = self::$t->_("absenteeism");//这是旷工;
                    $background = 3;                   //给红色
                    $absenteeism_num = 1;              //记录一天旷工
                }

                //班次为空 给红色
                if (($is_punch_out || $is_punch_in) && (empty($value['shift_start']) || empty($value['shift_end']))) {
                    $background = 3;
                }

                //如果没有上班卡 或者没有下班卡 就是缺卡 要显示红色
                if (empty($is_punch_out) || empty($is_punch_in)) {
                    $background = 3;   //缺卡
                }


                //6天班公共假期
                if ((in_array($value['stat_date'], $currentStaffHoliday) && $week_working_day == 6)) {
                    $holiday_str = "PH";
                    $background = 1;
                    $absenteeism_num = 0;//旷工天数
                    //不是休息日 又没打卡
                    if (!isset($offDay[$value['staff_info_id'].'-'.$value['stat_date']]) && ($is_punch_out == 0 && $is_punch_in == 0)) {
                        $background = 3;
                        $type = $is_punch_out || $is_punch_in ? $type : self::$t->_(
                            "absenteeism_working_day_6"
                        );//这是安排加班,未出勤;
                        $absenteeism_num = 1;
                    }
                }

                //5天班公共假期
                if ((in_array($value['stat_date'], $currentStaffHoliday) && $week_working_day != 6)) {
                    $holiday_str = "PH";
                    $background = 1;
                    $absenteeism_num = 0;//旷工天数
                }

                if (isset($offDay[$value['staff_info_id'].'-'.$value['stat_date']])) {
                    $absenteeism_num = 0;//旷工天数
                    $background = 1;     //休息日
                    $type = self::$t->_('2017');
                }
                // 注意从这里开始 type 是.=
                //判断是否请假
                //请假图片
                //如果请假了
                if (isset($apply_info[$value['staff_info_id']]) && isset($apply_info[$value['staff_info_id']][$value['stat_date']])) {
                    //看当前日期请的是什么假
                    $leave_type_arr = [];
                    foreach ($apply_info[$value['staff_info_id']][$value['stat_date']]['leave_arr'] as $leave) {
                        // 假期类型  -- 请假天数 -- (如果在审批中 则显示审批中 否则不显示)
                        $_leave_type[] = self::$t->_(
                                $leave_type_config[(int)$leave['leave_type']] ?? 'undefined leave type - '.(int)$leave['leave_type']
                            ). //假期类型
                            self::$t->_('leave_day_'.(int)$leave['type']). //请假天数
                            ($leave['status'] == $this->leave_status_1 ? self::$t->_(
                                'leave_status_'.(int)$leave['status']
                            ) : ''); //  (如果在审批中 则显示审批中 否则不显示)
                        //看请假是否申请通过
                        //如果通过
                        if ($leave['status'] == $this->leave_status_2) {
                            $leave_type_arr[] = $leave['type'];
                            $absenteeism_num = 0;                             //旷工天数
                            $background = $background == 2 ? 0 : $background; //如果是黄色 迟到早退 重新计算颜色
                            //如果是全天假 或者请了一上午和一下午
                            if ($leave['type'] == $this->leave_type_0 || empty(
                                array_diff(
                                    [$this->leave_type_1, $this->leave_type_2],
                                    $leave_type_arr
                                )
                                )) {
                                $background = 1; // 考勤变灰
                                $type = '';
                                //如果是上午半天假 && 并且打了上班卡和下班卡
                            } elseif ($leave['type'] == $this->leave_type_1 && !empty($is_punch_in) && !empty($is_punch_out)) {
                                //需要判断下午上班打卡是否正常
                                //首先把上午打卡时间 推后 5 小时
                                $shift_start = date('Y-m-d H:i', (strtotime($shift_start) + $this->leave_half_time));
                                $is_punch_in = 10;
                                $is_punch_out = 10;
                                //然后比较
                                //打卡时间大于班次时间 是迟到
                                if ($check_start > $shift_start) {
                                    $background = 2; //黄色
                                    $is_punch_in = 5;
                                }
                                //打卡时间小于班次时间 是早退
                                if ($check_end < $shift_end) {
                                    $background = 2;
                                    $is_punch_out = 5;
                                }
                                //如果是下午半天假 && 并且打了上班卡和下班卡
                            } elseif ($leave['type'] == $this->leave_type_2 && !empty($is_punch_in) && !empty($is_punch_out)) {
                                //需要判断上午下班打卡是否正常
                                //首先把下班打卡时间提前五小时
                                $shift_end = date('Y-m-d H:i', (strtotime($shift_end) - $this->leave_half_time));
                                $is_punch_in = 10;
                                $is_punch_out = 10;
                                //然后比较
                                //打卡时间小于班次时间 是早退
                                if ($check_end < $shift_end) {
                                    $background = 2;
                                    $is_punch_out = 5;
                                }
                                //打卡时间大于班次时间 是迟到
                                if ($check_start > $shift_start) {
                                    $background = 2; //黄色
                                    $is_punch_in = 5;
                                }
                            }
                        }
                        //如果未通过 则按照考勤展示
                        if (isset($offDay[$value['staff_info_id'].'-'.$value['stat_date']])) {
                            $absenteeism_num = 0;//旷工天数
                            $background = 1;     //休息日
                            $type = self::$t->_('2017');
                        }
                    }
                    $type .= '  '.implode(' ', $_leave_type);
                    $reason = $apply_info[$value['staff_info_id']][$value['stat_date']]['reason'] ?: '';        //请假的原因
                    $image_path = $apply_info[$value['staff_info_id']][$value['stat_date']]['image_path'] ?: '';//图片
                    if (!empty($image_path)) {
                        $imgData    = explode(',', $image_path);
                        $imgData    = array_unique($imgData);
                        $image_path = implode(',', $imgData);
                    }
                }


                //判断是否出差
                if (isset($trap_data2date[$value['staff_info_id'].'-'.$value['stat_date']])) {
                    $type .= " ".self::$t->_('business_trip');
                }
                //出差打卡审批中
                if (!empty($business_travel)) {
                    $background = 3;//红色
                }
                //计算迟到时长
                if ($is_punch_in == 5 && in_array($background, [2,3])) {
                    $late_num = 1;                                           //迟到1 次
                    $late_time = $this->diffTime($check_start, $shift_start);//计算迟到时长
                }
                //计算早退
                if ($is_punch_out == 5 && in_array($background, [2,3])) {
                    $leave_early_num = 1;                                       //早退1 次
                    $leave_early_time = $this->diffTime($check_end, $shift_end);//计算早退时长
                }


                //			//判断是否出差
                //			if(!empty($value['BT'])){
                //				$type .=  " " . self::$t->_('business_trip');
                //			}
//
                //			//判断是否出差
                //			if(!empty($value['BT_Y'] )){
                //				$type .=  " " . self::$t->_('business_trip');
                //			}

                //打卡时间
                $punch_info = "in:{$start_at} out:{$end_at}";
                if($value['stat_date'] > date('Y-m-d')){
                    $punch_info       = '';
                    $background       = 0;
                    $type             = 0;
                    $absenteeism_num  = 0;
                    $leave_early_num  = 0;
                    $late_num         = 0;
                    $late_time        = 0;
                    $leave_early_time = 0;
                }

                //新增 支援显示文案
                $supportKey = $value['staff_info_id'] . '-' . $value['stat_date'];
                $isSupport = empty($supportData[$supportKey]) ? '' : $supportText;

                $returnData[$value['staff_info_id']][] = [
                    'stat_date'        => $value['stat_date'],
                    'value'            => ($is_punch_out || $is_punch_in) ? "{$punch_info} {$type} {$isSupport}" : "{$type} {$isSupport}",
                    'background'       => $background,
                    'reason'           => $reason,
                    'image_path'       => $image_path,
                    'shift_time'       => $value['shift_start'] ? $value['shift_start'].'-'.$value['shift_end'] : '',
                    'shift_type'       => $value['shift_type'] ? self::$t->_(strtolower($value['shift_type'])) : '',
                    'holiday'          => $holiday_str,  //假期
                    'absenteeism_num'  => $absenteeism_num,
                    'late_num'         => $late_num,
                    'late_time'        => $late_time,
                    'leave_early_num'  => $leave_early_num,
                    'leave_early_time' => $leave_early_time,
                ];
            }
        }



        return $this->handelVacancyData($returnData, $date_range);
    }


    /**
     * 获取员工所属国家
     * @param $staffIds
     * @return array
     */
    public function getStaffCountryInfo($staffIds): array
    {
        $model = HrStaffItemsModel::class;
        $sql = "SELECT staff_info_id,value FROM {$model} WHERE staff_info_id IN ({staff_info_id:array}) and item='NATIONALITY'";
        $data = $this->modelsManager->executeQuery($sql, ['staff_info_id' => $staffIds])->toArray();
        return array_column($data, 'value', 'staff_info_id');
    }
}
