<?php
/**
 * Author: Bruce
 * Date  : 2023-04-26 10:48
 * Description:
 */

namespace app\modules\Ph\services;

use App\Library\Enums\ApprovalEnums;
use App\Models\backyard\HrStaffContractBusinessApplyModel;
use App\Models\backyard\HrStaffContractModel;
use App\Models\backyard\LeaveManageLogModel;
use App\Models\backyard\LeaveQuitclaimInfoModel;
use App\Models\backyard\StaffResignModel;
use App\Repository\HrStaffContractBusinessApplyRepository;
use App\Repository\HrStaffContractRepository;
use App\Services\LeaveManageListService as BaseLeaveManageListService;
use App\Services\SettingEnvService;

class LeaveManageListService extends BaseLeaveManageListService
{



    /**
     * 关联 quitclaim info 表
     * @param $builder
     * @return mixed
     */
    public function leftJoinModel($builder)
    {
        return $builder->leftJoin(LeaveQuitclaimInfoModel::class,
            'lqi.staff_info_id = hsi.staff_info_id and lqi.is_new = 1', 'lqi');
    }

    /**
     * 构建 查询条件
     * 为差异化使用
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function constructWhere($builder, $params)
    {
        if (isset($params['quitclaim_status']) && $params['quitclaim_status'] != '') {
            if($params['quitclaim_status'] == LeaveQuitclaimInfoModel::STATUS_99) {
                $builder->andWhere('lqi.reply_status = :replay_status:', ['replay_status' => LeaveQuitclaimInfoModel::REPLY_TIMEOUT]);
            } else{
                $builder->andWhere('lqi.status = :quitclaim_status: and lqi.reply_status != :replay_status:', ['quitclaim_status' => $params['quitclaim_status'], 'replay_status' => LeaveQuitclaimInfoModel::REPLY_TIMEOUT]);
            }
        }

        return $builder;
    }

    /**
     * 字段差异
     * @return array
     */
    public function leaveListColumns()
    {
        $other_columns = [
            'lqi.id as quitclaim_id',
            'lqi.status as quitclaim_status',
            'lqi.file_url',
            'lqi.reply_status as quitclaim_reply_status',
        ];
        return array_merge($this->leave_list_columns, $other_columns);
    }

    /**
     * 列表行差
     * @param array $item
     * @return array|mixed
     */
    public function buildOtherRow($item = [])
    {
        if($item['quitclaim_reply_status'] == LeaveQuitclaimInfoModel::REPLY_TIMEOUT) {
            $item['quitclaim_status'] = LeaveQuitclaimInfoModel::STATUS_99;
        }
        $row['quitclaim_status_name'] = isset(LeaveQuitclaimInfoModel::STATUS_LIST[$item['quitclaim_status']]) ? self::$t->_(LeaveQuitclaimInfoModel::STATUS_LIST[$item['quitclaim_status']]) : '';
        $row['quitclaim_status']      = $item['quitclaim_status'];
        $row['quitclaim_pdf_url']     = $item['quitclaim_status'] == LeaveQuitclaimInfoModel::STATUS_2 ? $item['file_url'] : '';
        $row['quitclaim_id']          = $item['quitclaim_id'];
        return $row;
    }

    /**
     * 导出 内容差异
     * @param $item
     * @param $datum
     * @return array
     */
    public function exportBuildOtherRow($item, $datum, $bankList = [])
    {
        if($datum['quitclaim_reply_status'] == LeaveQuitclaimInfoModel::REPLY_TIMEOUT) {
            $datum['quitclaim_status'] = LeaveQuitclaimInfoModel::STATUS_99;
        }

        $item[] = $datum['quitclaim_status'] == LeaveQuitclaimInfoModel::STATUS_2 ? $datum['file_url'] : '';
        $item[] = isset(LeaveQuitclaimInfoModel::STATUS_LIST[$datum['quitclaim_status']]) ? self::$t->_(LeaveQuitclaimInfoModel::STATUS_LIST[$datum['quitclaim_status']]) : '';
        $item[] = isset(LeaveQuitclaimInfoModel::PAYMENT_METHOD[$datum['payment_method']]) ? self::$t->_(LeaveQuitclaimInfoModel::PAYMENT_METHOD[$datum['payment_method']]) : ($bankList[$datum['payment_method']] ?? '');
        $item[] = $datum['bank_account_name'];
        $item[] = $datum['bank_account_number'];

        return $item;
    }

    /**
     * 导出字段差异
     * @return array
     */
    public function exportLeaveListColumns()
    {
        $other_columns = [
            'lqi.status as quitclaim_status',
            'lqi.file_url',
            'lqi.payment_method',
            'lqi.bank_account_name',
            'lqi.bank_account_number',
            'lqi.reply_status as quitclaim_reply_status',
        ];
        return array_merge($this->export_leave_list_columns, $other_columns);
    }

    /**
     * 离职日志详情 Columns 其他
     * @return array
     */
    public function getLeaveLogDetailColumns()
    {
        $other_columns = [
            'lqi.id as quitclaim_id',
            'lqi.status as quitclaim_status',
            'lqi.file_url',
        ];
        return array_merge($this->leave_log_detail_columns, $other_columns);
    }

    public function leaveLogDetailLeftModel($builder)
    {
        return $builder->leftJoin(LeaveQuitclaimInfoModel::class, 'lqi.leave_manager_log_id = lm.id', 'lqi');
    }

    /**
     * 离职详情列表
     * @param array $value
     * @return array|mixed
     */
    public function buildLeaveLogDetailOtherRow($value = [])
    {
        $data['quitclaim_id']          = $value['quitclaim_id'];
        $data['quitclaim_pdf_url']     = $value['quitclaim_status'] == LeaveQuitclaimInfoModel::STATUS_2 ? $value['file_url'] : '';
        $data['quitclaim_status_name'] = isset(LeaveQuitclaimInfoModel::STATUS_LIST[$value['quitclaim_status']]) ? self::$t->_(LeaveQuitclaimInfoModel::STATUS_LIST[$value['quitclaim_status']]) : '';
        $data['quitclaim_status']      = $value['quitclaim_status'];
        return $data;
    }

    /**
     * 根据定制化条件，修改原字段值
     * @param $row
     * @param array $item
     * @return mixed
     */
    public function editRowData($row, $item = [])
    {
        //处理状态：已处理的标准变为：“资产归还情况”和“钱款归还情况”都是“已处理”状态，并且“Quitclaim处理进度”是“已通过”或者是“回复超时”
        if($row['remand_state'] == self::ASSETS_STATE_PROCESSED) {
            $row['remand_state'] = self::ASSETS_STATE_PROCESSING;
            if($item['quitclaim_status'] == LeaveQuitclaimInfoModel::STATUS_2 || $item['quitclaim_reply_status'] == LeaveQuitclaimInfoModel::REPLY_TIMEOUT) {
                $row['remand_state'] = self::ASSETS_STATE_PROCESSED;
            }
        }
        return $row;
    }

    /**
     * 个人代理重发续约确认按钮权限
     * @param $staffInfoId
     * @param $userId
     * @return array|mixed
     */
    public function isResendRenew($staffInfoId, $userId)
    {
        $result['is_resend_renew'] = self::RENEW_CONTRACT_RESEND_NO;//不展示 按钮
        $result['contract_business_id'] = 0;

        $permissionStaffId = (new SettingEnvService())->getSetVal('renew_contract_send_button', ',');

        //没有权限
        if(!in_array($userId, $permissionStaffId)) {
            return $result;
        }
        $where['staff_info_id'] = $staffInfoId;
        $data = HrStaffContractBusinessApplyRepository::getLastOneByStaffId($where);

        if(empty($data)) {
            return $result;
        }

        //未选择 续不续约
        if($data['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_NO_SELECT) {
            return $result;
        }

        $contractInfo = HrStaffContractRepository::getOne(['id' => $data['contract_id']], ['contract_status']);

        if(empty($contractInfo)) {
            return $result;
        }

        //超出合同到期日 不展示
        if(strtotime($data['contract_end_date']) < strtotime(date('Y-m-d'))) {
            return $result;
        }
        //本次合同到期日对应的“待审核、已同意”状态的续约申请, 不展示
        if(
            $data['business_type'] == HrStaffContractBusinessApplyModel::BUSINESS_TYPE_NOT_FIRST
            && $data['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE
            && in_array($data['status'], [ApprovalEnums::APPROVAL_STATUS_NO_CREATE, ApprovalEnums::APPROVAL_STATUS_PENDING,  ApprovalEnums::APPROVAL_STATUS_APPROVAL])
        ) {
            return $result;
        }

        if($data['business_type'] == HrStaffContractBusinessApplyModel::BUSINESS_TYPE_FIRST && $data['business_status'] == HrStaffContractBusinessApplyModel::BUSINESS_STATUS_AGREE) {
            return $result;
        }

        //winhr 合同状态 非待续约 不展示
        if($contractInfo['contract_status'] != HrStaffContractModel::CONTRACT_STATUS_TO_BE_RENEWED) {
            return $result;
        }

        $result['is_resend_renew'] = self::RENEW_CONTRACT_RESEND_YES;//展示 按钮
        $result['contract_business_id'] = $data['id'];
        return $result;
    }
}