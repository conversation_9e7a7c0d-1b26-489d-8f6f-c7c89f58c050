<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/9/16
 * Time: 3:54 PM
 */


namespace App\Modules\Ph\Services;

use App\Library\BaseService;
use App\Library\BiMail;
use App\Library\Mailer;
use App\Library\Validation\ValidationException;
use App\Models\backyard\CertificateCodeModel;
use App\Models\backyard\FileOssUrlModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SettingEnvModel;
use App\Modules\Ph\library\Enums\CertificateEnums;
use App\Repository\HrStaffInfoRepository;
use App\Services\FormPdfServer;
use App\Services\OssService;
use App\Services\SettingEnvService;
use App\Services\StaffService;
use App\Services\SysDepartmentService;
use App\Services\CertificateService as BaseCertificateService;

class CertificateService extends BaseCertificateService
{

    public static $month_en_list = [
        1  => 'January',
        2  => 'February',
        3  => 'March',
        4  => 'April',
        5  => 'May',
        6  => 'June',
        7  => 'July',
        8  => 'August',
        9  => 'September',
        10 => 'October',
        11 => 'November',
        12 => 'December',
    ];

    public static $certificate_type = [
        1 => 'salary',
        2 => 'on_job',
        3 => 'leave_job',
        4 => 'payroll',
        5 => 'salary',
        6 => 'salary',
    ];
    //证明 用途  1 Loan Application, 2 Auto loan, 3 Housing loan, 4 Credit card Application, 5 Visa Application 6 Other
    public static $use_type = [
        1 => 'Loan Application',
        2 => 'Auto loan',
        3 => 'Housing loan',
        4 => 'Credit card Application',
        5 => 'Visa Application',
        6 => 'Other',
    ];

    public $v_param = [
        'id'                           => 'Required|Int|IntGt:0|>>>:[id] params error',
        'company_logo_url'             => 'Required|ArrLenGe:1|>>>:[company_logo_url] params error',//公司logo
        'company_seal_url'             => 'Required|ArrLenGe:1|>>>:[company_seal_url] params error',//公司印章
        'company_name'                 => 'Required|StrLenGeLe:1,200|>>>:[company_name] params error',//公司名称
        'company_short_name'           => 'Required|StrLenGeLe:1,200|>>>:[company_short_name] params error',//公司简称
        'company_registration_no'      => 'Required|StrLenGeLe:1,200|>>>:[company_registration_no] params error',//公司注册号
        'company_address'              => 'Required|StrLenGeLe:1,200|>>>:[company_address] params error',//公司地址
        'company_phone'                => 'Required|StrLenGeLe:1,200|>>>:[company_phone] params error',//公司电话
        'company_web_url'              => 'Required|StrLenGeLe:1,200|>>>:[company_web_url] params error',//公司网址
        'company_email'                => 'Required|StrLenGeLe:1,200|>>>:[company_email] params error',//公司邮箱
        'labor_name'                   => 'Required|StrLenGeLe:1,200|>>>:[labor_name] params error',//代表人姓名
        'labor_job_title'              => 'Required|StrLenGeLe:1,200|>>>:[labor_job_title] params error',//代表人职位
        'labor_sign_url'               => 'Required|ArrLenGe:1|>>>:[labor_sign_url] params error',//代表人签名图
    ];

    /**
     * 证明下载 接口  包括 工资条 和 在职证明 pdf
     * 对应pdf 模板 统一放到 模板下 salary/ 命名规则 类型名_download.phtml
     * @param int $staff_id 员工id
     * @param array $type_arr [type => int ,use_type =>  int]    type 证明类型  1- 工资条 2- 在职证明  3- 就业证明（离职证明） 4 薪资证明  use_type 证明用途 针对菲律宾 1 Loan Application, 2 Auto loan, 3 Housing loan, 4 Credit card Application, 5 Visa Application
     * @param array $date_array 对应证明的日期 工资条可能是多个到月份 在职 单个到日期天  就业证明 固定一份 日期为申请日期
     * @param int $mail_type 邮箱类型 by调用 在职证明和 工资证明 选择 邮箱类型 1 企业邮箱 2 个人邮箱 默认企业
     * @param int $is_send 是否需要发送  默认需要  0 不需要
     * @throws ValidationException
     * @throws \Mpdf\MpdfException
     */
    public function certificate_download(
        $staff_id,
        $type_arr,
        $date_array = [],
        $leave_job_type = 3,
        $mail_type = 1,
        $is_send = 1
    ) {
        if (empty($date_array) && in_array($type_arr['file_type'],
                [OssService::SALARY_PDF, OssService::SALARY_PDF_CN, OssService::SALARY_PDF_EN])) {
            throw new ValidationException('wrong parameter for salary');
        }

        if (empty($date_array)) {
            $date_array[] = date('Y-m', time());
        }

        $type = intval($type_arr['file_type']);

        //签字信息
        $proveDownloadSetting = (new SettingEnvService)->getSetVal('prove-download-setting');
        if (empty($proveDownloadSetting)) {
            $this->logger->error("证明下载 签字信息为空 !!! ");
            throw new ValidationException('管理员正在处理，请稍后。');
        }

        //新增 用途枚举
        $use_type = empty($type_arr['use_type']) ? 0 : $type_arr['use_type'];
        if (empty($type)) {
            throw new ValidationException('wrong parameter');
        }
        //获取用户信息 没有 返回
        $staff_model = new StaffService();
        $staff_info  = $staff_model->getHrStaffInfo($staff_id);

        if (empty($staff_info)) {
            throw new ValidationException('no such staff');
        }

        //获取职位名称
        if (!empty($staff_info['job_title'])) {
            $job_info                     = HrJobTitleModel::findFirst(intval($staff_info['job_title']));
            $staff_info['job_title_name'] = empty($job_info) ? '' : $job_info->job_name;
        }


        $type_name         = self::$certificate_type[$type];
        $res['staff_info'] = $staff_info;
        //根据type  获取对应pdf 模板 所需数据
        if (in_array($type, [OssService::SALARY_PDF, OssService::SALARY_PDF_CN, OssService::SALARY_PDF_EN])) {//工资条 信息
            //当时间达当月25日17点时，当月上半月显示可选；当时间达次月10日17点时，当月下半月显示可选
            // 这里面 工资条的月份 不是正常的 ymd  2021-062
            $current_time = date('Y-m-d H:i:s');
            $last_month   = date('Y-m', strtotime("-1 month"));
            foreach ($date_array as $k => $da) {
                if ($current_time < date("Y-m-25 17:00:00") && $da > date("Y-m")) {
                    unset($date_array[$k]);
                }

                if ($current_time < date("Y-m-10 17:00:00") && $da >= $last_month.'2') {
                    unset($date_array[$k]);
                }
            }

            if (empty($date_array)) {
                throw new ValidationException('no salary info');
            }

            $date_array = array_values($date_array);
            $date_str   = "'".implode("','", $date_array)."'";
            //换数据源了
            $paid_type = SalaryService::SALARY_STATUS_PAID;
            $sql       = "select staff_info_id as staff_id ,excel_month,paid_day ,staff_name from salary_gongzi
                    where  staff_info_id = {$staff_id} and excel_month in ({$date_str})
                    and status = {$paid_type}
                   ";
            $check = $this->db_rby->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
            if(empty($check)){
                throw new ValidationException('no salary info');
            }

            $date_array = array_column($check, 'excel_month');
        }

        $pdf_data                         = $this->format_pdf_data($type_arr, $staff_info);
        $pdf_data['proveDownloadSetting'] = json_decode($proveDownloadSetting, true);

        //生成pdf 更改为 获取 oss的文件地址
        $oss_bll  = new OssService();
        $url_list = [];
        foreach ($date_array as $d) {
            //换新逻辑 查询 file_oss_url 文件类型 $type 1 工资条pdf 2在职证明 3 离职证明（就业证明）4 薪资证明（不同于工资条）

            //on job 在职证明 payroll 薪资证明 是ymd
            if (in_array($type, [OssService::ON_JOB_PDF, OssService::PAYROLL_PDF])) {
                $oss_insert['date_at'] = $d;
            } else {
                $oss_insert['month'] = $d;
                $oss_insert['lang']  = FileOssUrlModel::LANG_EN;//菲律宾默认 英文 fbi 默认就存了1
            }
            $oss_insert['use_type'] = $use_type;
            $exist                  = $oss_bll->get_info($staff_id, $type, $oss_insert);
            $url_name               = "{$type_name}_{$staff_id}_{$d}";

            if (empty($exist)) {//没有已经上传的oss文件 需生成pdf 上传 入库
                if (in_array($type,
                    [OssService::SALARY_PDF, OssService::SALARY_PDF_CN, OssService::SALARY_PDF_EN])) {//工资条 信息
                    $ext_bll      = new SalaryService();
                    $url_list[$d] = $ext_bll->format_pdf($staff_id, $d, 0, $oss_insert['lang']);
                } else {
                    //模板地址
                    $temp_arr['path'] = APP_PATH.'/modules/Ph/views';
                    $temp_arr['dir']  = 'salary';
                    $temp_arr['name'] = "{$type_name}_pdf";
                    //模板渲染数据
                    $code                  = $this->get_file_code($staff_id, $type, $d);
                    $view_data['pdf_data'] = $pdf_data;
                    $view_data['code']     = $code;
                    //入库数据
                    $oss_insert['staff_info_id'] = $staff_id;
                    $oss_insert['file_type']     = $type;

                    $url_list[$url_name] = $this->createPdf($staff_id, $view_data, $oss_insert);
                }
            } else {
                $url_list[$url_name] = $exist;
            }
        }

        if (!$is_send) {
            return $url_list;
        }

        //发送邮件 和附件
        $param['date_array'] = $date_array;
        $param['staff_info'] = $staff_info;
        $param['type']       = $type;
        $param['url_list']   = (in_array($type, [
            OssService::SALARY_PDF,
            OssService::SALARY_PDF_CN,
            OssService::SALARY_PDF_EN,
        ])) ? $url_list : array_values($url_list);
        $mail                = $this->get_template($param);
        if ($type == OssService::LEAVE_JOB_PDF) {//离职就业证明不发公司邮箱
            $e_mail = (string)$staff_info['personal_email'];
        } else {
            //新增逻辑 by 可选邮箱发送类型
            if ($mail_type == 1)//企业邮箱
            {
                $e_mail = $staff_info['email'];
            } else {
                if ($mail_type == 2)//个人邮箱
                {
                    $e_mail = $staff_info['personal_email'];
                } else {
                    $e_mail = empty($staff_info['email']) ? $staff_info['personal_email'] : $staff_info['email'];
                }
            }
        }

        if (empty($e_mail)) {
            throw new ValidationException('no such email');
        }

        $this->logger->info("certificate_download_{$staff_id} {$type_name} 发送 {$e_mail} ");
        try {
            $flag = BiMail::send_salary($e_mail, $mail['title'], $mail['content']);
        } catch (\Exception $e) {
            if ($e->getCode() == 559) {
                throw new ValidationException('Please check whether email is available!');
            } else {
                $this->logger->notice("certificate_download: $e_mail 发送失败 {$staff_id} ".$e->getMessage());
                throw $e;
            }
        }

        $this->logger->info("certificate_download_{$staff_id} {$type_name} 发送结果：{$flag} {$e_mail} ");
        return true;
    }

    //获取文件编码  编码示例泰文版：HRFlashTH-00001-2563 英文版：HRFlashEN-00001-2020 中间部分 为自增
    protected function get_file_code($staff_id, $type, $date)
    {
        //看是否已经生成
        $sql  = " select code from certificate_code where staff_info_id = {$staff_id} and `type` = {$type} and date_at = '{$date}' order  by created_at";
        $info = $this->getDI()->get('db_rby')->fetchAll($sql);
        if (!empty($info)) {
            $r = end($info);
            return $r['code'];
        }

        //没有记录
        $sql = " select count(1) from certificate_code where  `type` = {$type} ";
        $num = $this->getDI()->get('db_rby')->fetchColumn($sql);
        //默认 5位数 如果 过万 位数加一 递增
        $length = 6;
        if ($num > '999999') {
            $length = strlen($num);
        }

        if (empty($num)) {
            $num = '1';
        } else {
            $num++;
        }

        //示例 HRFlashTH-00001
        $code = str_pad($num, $length, "0", STR_PAD_LEFT);

        $row   = [
            'staff_info_id' => $staff_id,
            'type'          => $type,
            'code'          => $code,
            'date_at'       => date("Y-m-d", strtotime($date)),
        ];
        $model = new CertificateCodeModel();
        $model->create($row);

        return $code;
    }

    /**动态返回 模板 工资条 和 在职证明 邮件标题内容模板
     * 新增 url_list参数 oss 文件地址链接
     * @param $param
     * @return mixed
     */
    protected function get_template($param)
    {
        $return['title']   = "";
        $return['content'] = '';

        if (in_array($param['type'], [OssService::SALARY_PDF, OssService::SALARY_PDF_CN, OssService::SALARY_PDF_EN])) {
            /**
             *   1. 邮件主题：#开始年月#to#结束年月#Payslip（特殊的，当开始年月=结束年月，仅显示一次）
             * 示例1：开始年月：2021 Sep 01-15， 结束年月：2021 Sep 16-30，邮件主题是：2021 Sep 01-15 to 2021 Sep 16-30 Payslip
             * 示例2：开始年月：2021 Sep 01-15， 结束年月：2021 Sep 01-15，邮件主题是：2021 Sep 01-15 Payslip
             */

            $server = new SalaryService();
            $start  = $server->format_month($param['date_array'][0]);
            $end    = end($param['date_array']);
            $end    = $server->format_month($end);
            //#开始年月#to#结束年月#Payslip（特殊的，当开始年月=结束年月，仅显示一次）
            $return['title'] = "{$start} to {$end} Payslip";
            if ($start == $end) {
                $return['title'] = "{$start} Payslip";
            }

            $return['content'] = 'Hello，<br/><br/>
                                Hello, please click the link below to view the Payslip<br/><br/>
                                ';
            if (!empty($param['url_list'])) {
                $return['content'] .= "</br></br>";
                foreach ($param['url_list'] as $d => $url) {
                    $show_name         = $server->format_month($d);
                    $return['content'] .= "<a href='{$url}'>{$show_name}</a></br>";
                }
            }
        }
        if ($param['type'] == 2) {
            /**
             * 1. 邮件主题：在职证明
             * 2. 邮件内容：
             * 您好，您的在职证明已附属在以下附件。
             * 3. 邮件附件：英文与泰文的在职证明
             */
            $return['title']   = "Certificate of employment";
            $return['content'] = "Hello,<br/><br/>
                                The certificate of employment  you applied for with our company has been successfully applied,<a href='{$param['url_list'][0]}'> please click here to download.</a>。<br/><br/>
                                Thanks<br/><br/>
                                ";
        }


        //-- todo 没出需求
        if ($param['type'] == 3) {
            $return['title']   = "หนังสือรับรองพนักงาน 就业证明";
            $return['content'] = "您好<br/><br/> 
                                 您在本公司申请的就业证明（已经离职）已申请成功，请<a href='{$param['url_list'][0]}'>请点击此处下载</a>。<br/><br/> 
                                 谢谢<br/><br/> 
                                 สวัสดีค่ะ<br/><br/> 
                                 ตามที่ท่านได้ส่งคำร้องขอหนังสือรับรองพนักงาน(พนักงานลาออก) ไว้กับทางบริษัทฯ ขณะนี้ทางบริษัทฯ ได้ดำเนิน<br/> 
                                 การจัดทำหนังสือรับรองฯ ตามที่ท่านขอเรียบร้อยแล้ว<br/> 
                                 ท่านสามารถ<a href='{$param['url_list'][0]}'>ดาวน์โหลดเอกสารดังกล่าวได้ที่นี่</a></br>
                                 ขอบคุณค่ะ<br/>";
        }

        if ($param['type'] == 4) {
            $return['title']   = "Certificate of employment(salary)";
            $return['content'] = "Hello,<br/><br/> 
                                 The certificate of employment(salary) you applied for with our company has been successfully applied,<a href='{$param['url_list'][0]}'>please click here to download.</a>。<br/><br/> 
                                 Thanks<br/><br/> 
                                 ";
        }

        return $return;
    }


    public function format_pdf_data($type_arr, $staff_info)
    {
        $pdf_data = [];
        $staff_id = $staff_info['staff_info_id'];
        $type     = intval($type_arr['file_type']);
        if ($type == OssService::ON_JOB_PDF || $type == OssService::PAYROLL_PDF) {//在职证明 个人信息 英文 日期 和入职日期 薪资构成

            $sql         = " select * from hr_staff_salary where staff_info_id = {$staff_id} ";
            $salary_info = $this->getDI()->get('db_rby')->query($sql)->fetch(\Phalcon\Db::FETCH_ASSOC);

            $pdf_data['base_salary'] = round($salary_info['base_salary'] / 100, 2);
            $pdf_data['name']        = preg_replace("/\(.*\)/", "", $staff_info['name']);
            $pdf_data['name_en']     = empty($staff_info['name_en']) ? $staff_info['name'] : $staff_info['name_en'];

            $pdf_data['job_title_name'] = $staff_info['job_title_name'];
//            $pdf_data['department_name'] = $staff_info['department_name'];

            $pdf_data['hire_date'] = date('Y-m-d', strtotime($staff_info['hire_date']));

            $current_month            = intval(date('m', time()));
            $pdf_data['current_date'] = date('d').' '.self::$month_en_list[$current_month].' '.date('Y');

            $pdf_data['en_money'] = $pdf_data['split_money'] = $pdf_data['th_money'] = '';
            if (!empty($pdf_data['base_salary'])) {
                //英文钱 字符串
                $pdf_data['en_money'] = num2Word($pdf_data['base_salary'], 'peso');
                //英泰文 数字钱  千分位
                $pdf_data['split_money'] = number_format($pdf_data['base_salary']);
            }
            $pdf_data['use_type_text'] = self::$use_type[$type_arr['use_type']];
            $pdf_data['staff_info_id'] = $staff_id;
        }

        if ($type == OssService::LEAVE_JOB_PDF) { // 就业证明

        }
        return $pdf_data;
    }

    /**
     * 获取员工及pdf 页眉页脚 信息
     * @param $staff_info_id
     * @param bool $base64
     * @return mixed
     */
    public function getStaffCompanyInfo($staff_info_id, $base64 = true , $options = [])
    {
        //员工信息
        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($staff_info_id);

        //公司信息
        $companyId         = $this->getCompanyId($staffInfo['node_department_id'], $staffInfo['nationality']);
        $companyId         = $this->staffSpecialCompany($staff_info_id, $companyId);
        $companyConfigInfo = $this->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);

        //文件抬头 公司名称（页眉）
        $data['header_company_name']  = $companyConfigInfo['company_name'] ?? '';
        $data['content_company_name'] = $companyConfigInfo['company_name'] ?? '';

        //公司logo
        $data['header_logo_url'] = $companyConfigInfo['company_logo_url'] ?? '';
        $data['labor_sign_url']  = $companyConfigInfo['labor_sign_url'] ?? '';
        $data['labor_name']      = $companyConfigInfo['labor_name'] ?? '';
        $data['labor_job_title'] = $companyConfigInfo['labor_job_title'] ?? '';

        //拼接页脚信息
        $company_address = $companyConfigInfo['company_address'] ?? '';//公司地址
        $company_phone   = $companyConfigInfo['company_phone'] ?? '';  //公司电话
        $company_web_url = $companyConfigInfo['company_email'] ?? '';  //公司邮箱

        $company_phone_web = [];
        if (!empty($company_phone)) {
            $company_phone_web[] = $company_phone;
        }

        if (!empty($company_web_url)) {
            $company_phone_web[] = $company_web_url;
        }

        $company_phone_web_string = empty($company_phone_web) ? '' : "</br>".implode(' ', $company_phone_web);

        //获取页脚内容
        $footer                 = $company_address.$company_phone_web_string;
        $data['footer_content'] = $footer;
        $data['company_phone']  = $company_phone;

        [$header, $footer] = $this->getHeaderFooter($data['header_company_name'], $companyConfigInfo['company_logo_url_base64'], $footer , $options);
        if ($base64) {
            $data['header_template'] = $header;
            $data['footer_template'] = $footer;
        }

        return $data;
    }

    /**
     * 获取员工 所属公司
     * @param $node_department_id
     * @param $nationality
     * @return int
     */
    public function getCompanyId($node_department_id, $nationality)
    {
        $department = (new SysDepartmentService())->getDepartmentDetail($node_department_id);
        if (empty($department)) {
            return CertificateEnums::COMPANY_FLASH_EXPRESS;
        }
        $departmentArray = explode('/', $department['ancestry_v3']);
        if (empty($departmentArray[2])) {
            return CertificateEnums::COMPANY_FLASH_EXPRESS;
        }

        //非ph 国籍 走 SOFTWARE 公司
        if($nationality != HrStaffItemsModel::NATIONALITY_4) {
            return CertificateEnums::COMPANY_SOFTWARE;
        }

        //ph 国籍
        if (in_array($departmentArray[2], [CertificateEnums::COMPANY_FFL, CertificateEnums::COMPANY_FCM]) && $nationality == HrStaffItemsModel::NATIONALITY_4) {
            return $departmentArray[2];
        }

        return CertificateEnums::COMPANY_FLASH_EXPRESS;
    }

    public function staffSpecialCompany($staff_id, $company_id)
    {
        $settingCodeValMap      = (new SettingEnvModel())->getMultiEnvByCode([
            'special_flashexpress_staff',
            'special_fulfillment_staff',
            'special_fcommerce_staff',
            'special_software_staff',
        ]);

        if (!empty($settingCodeValMap['special_flashexpress_staff'])
            && in_array($staff_id, explode(',', $settingCodeValMap['special_flashexpress_staff']))
        ) {
            $company_id = CertificateEnums::COMPANY_FLASH_EXPRESS;
        }
        if (!empty($settingCodeValMap['special_fulfillment_staff'])
            && in_array($staff_id, explode(',', $settingCodeValMap['special_fulfillment_staff']))
        ) {
            $company_id = CertificateEnums::COMPANY_FFL;
        }
        if (!empty($settingCodeValMap['special_fcommerce_staff'])
            && in_array($staff_id, explode(',', $settingCodeValMap['special_fcommerce_staff']))
        ) {
            $company_id = CertificateEnums::COMPANY_FCM;
        }
        if (!empty($settingCodeValMap['special_software_staff'])
            && in_array($staff_id, explode(',', $settingCodeValMap['special_software_staff']))
        ) {
            $company_id = CertificateEnums::COMPANY_SOFTWARE;
        }

        return $company_id;
    }

    /**
     * 生成pdf
     * @param $staff_id
     * @param $pdf_data
     * @param $oss_insert
     * @return mixed|string
     * @throws \Exception
     */
    public function createPdf($staff_id,$pdf_data, $oss_insert)
    {
        $type_name = self::$certificate_type[$oss_insert['file_type']];
        $pdf_data['split_money'] = empty($pdf_data['split_money']) ? 0 : $pdf_data['split_money'];
        //获取员工 所属 公司 信息
        $pdf_company_data = $this->getStaffCompanyInfo($staff_id, $pdf_data);
        $pdf_data['company_data'] = $pdf_company_data;

        $pdf_data['pdf_data']['proveDownloadSetting']['url'] = $pdf_company_data['labor_sign_url'] ?? '';
        $pdf_data['pdf_data']['proveDownloadSetting']['name'] = $pdf_company_data['labor_name'] ?? '';
        $pdf_data['pdf_data']['proveDownloadSetting']['job_title'] = $pdf_company_data['labor_job_title'] ?? '';

        //模板地址
        $tmpPath = APP_PATH . '/modules/Ph/views/salary/' . $type_name . '_pdf.ftl';
        $pdf_temp_url = $this->getPdfTemp($tmpPath);

        $file_name = time().rand(1,10000)."_{$staff_id}";
        $pdf_img_data[] = ['name' => 'header_logo_url', 'url' => $pdf_company_data['header_logo_url']];
        $pdf_img_data[] = ['name' => 'prove_download_setting_url', 'url' => $pdf_data['pdf_data']['proveDownloadSetting']['url']];

        $singPdfSetting = [
            'format'              => 'a4',
            'displayHeaderFooter' => true,
            'headerTemplate'      => $pdf_company_data['header_template'],
            'footerTemplate'      => $pdf_company_data['footer_template'],
        ];

        $res = (new FormPdfServer())->getInstance()->generatePdf($pdf_temp_url, $pdf_data, $pdf_img_data, $singPdfSetting,
            $file_name, '');//attchment

        $url = $res['object_url'] ?? '';

        if ($url) {
            //保存 file_oss_url
            $insert['staff_info_id'] = $staff_id;
            $insert['month']         = $oss_insert['month'] ?? '';
            $insert['file_type']     = $oss_insert['file_type'];
            $insert['lang']          = $oss_insert['lang'] ?? '';
            $insert['path']          = urldecode($res['object_key']);
            $insert['bucket']        = $res['bucket_name'];
            $model                   = new FileOssUrlModel();
            $model->create($insert);
        }
        return $url;

    }

    /**
     * 是否需要 页脚
     * @param $node_department_id
     * @param $nationality
     * @return bool
     */
    public function getDepartmentFooter($node_department_id, $nationality)
    {
        $department = (new SysDepartmentService())->getDepartmentDetail($node_department_id);

        //FLASH EXPRESS 页脚
        if (empty($department)) {
            return CertificateEnums::FOOTER_FE;
        }
        //FLASH EXPRESS 页脚
        $departmentArray = explode('/', $department['ancestry_v3']);
        if (empty($departmentArray[2])) {
            return CertificateEnums::FOOTER_FE;
        }

        //  //FLASH FULFILLMENT 页脚
        if($departmentArray[2] == CertificateEnums::COMPANY_FFL && $nationality == HrStaffItemsModel::NATIONALITY_4) {
            return CertificateEnums::FOOTER_FFL;
        }

        //  F-COMMERCE(PH)CO.,LTD.INC 页脚
        if($departmentArray[2] == CertificateEnums::COMPANY_FCM && $nationality == HrStaffItemsModel::NATIONALITY_4) {
            return CertificateEnums::FOOTER_FCM;
        }

        //FLASH EXPRESS 页脚
        return CertificateEnums::FOOTER_FE;
    }

    /**
     * 获取公司logo页眉页脚
     * @param $companyName
     * @param $companyLogoBase64
     * @param $footer
     * @return array
     */
    public function getHeaderFooter($companyName, $companyLogoBase64, $footer , $options = [])
    {
        $header = '
    <div style="width: 100%;margin: 0 12mm;box-sizing: border-box;">
        <table
        style="border-bottom: 2px solid #333;line-height: 6mm;width: 100%;font-weight: 700;font-size: 4mm;font-family: sans-serif; background-size: 38mm;">
        <tr>
            <td>
            <div>' . $companyName . '</div>
            </td>
            <td style="height: 7mm;text-align: right;">
            <img style="height: 7mm; width: auto;object-fit: cover;"
                src="' . $companyLogoBase64. '" />
            </td>
        </tr>
        </table>
    </div>';

        if (!empty($options['is_footer_sign'])) {
            //页尾巴签字图
            $footerSignImg = '';
            if (!empty($options['footer_sign_url'])) {
                $options['footer_sign_url'] = img_base64_encode($options['footer_sign_url']);
                $footerSignImg              = '<img style="width: 35mm; height: 15mm;" src="'.$options['footer_sign_url'].'">';
            }

            $t         = $this->getTranslation($options['lang'] ?? self::$language);
            $signTitle = $t->_('staff_sign_title');

            $footer = '<div style=" width: 297mm; box-sizing: border-box;">
    <div style="width: 100%; text-align: right; font-size: 3mm; display: flex; justify-content: flex-end; align-items: flex-end;">
      <span style="margin-right: 4mm;">'.$signTitle.':</span>
      <div style="width: 35mm; height: 17mm; padding-bottom: 2mm; box-sizing: border-box; border-bottom: 1px dashed black; margin-right: 6mm;">
      '.$footerSignImg.'
      </div>
    </div>
    <div style="width: 100%;text-align: center; font-size: 2mm; ">
      <p>'.$footer.'</p>
    </div>
  </div>';
        } else {
            $footer = '<div style=" width: 297mm; box-sizing: border-box; "><div style="width: 100%;text-align: center; font-size: 2mm;  border-top: 2px solid #000; style="word-wrap: break-word""><p>' . $footer . '</p></div></div>';
        }

        return [$header, $footer];
    }

    public function getCompanyConfigInfo($params)
    {
        $staffInfo = HrStaffInfoRepository::getHrStaffInfo($params['staff_info_id']);

        $departmentId = !empty($params['department_id']) ? $params['department_id'] : $staffInfo['node_department_id'];

        $companyId = $this->getCompanyId($departmentId, !empty($params['nationality']) ? $params['nationality'] : $staffInfo['nationality']);

        $companyId = $this->staffSpecialCompany($params['staff_info_id'], $companyId);

        return $this->getPdfCompanyInfoByCompanyId(['company_id' => $companyId]);
    }

}