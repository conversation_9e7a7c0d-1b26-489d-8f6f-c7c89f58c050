<?php


namespace App\Modules\Ph\Services;

use App\Services\SettingEnvService;
use App\Services\SysService as BaseService;

class SysService extends BaseService{

    public function saveCarAllowancePiece($piece_ids){

       return (new SettingEnvService())->saveSettingByCode([
            'code'=> 'car_allowance_piece_ids',
            'set_val'=> trim($piece_ids),
            'remark' => '发放车补系数片区',
        ]);
    }
    /**
     * 一线员工薪资配置-薪资规则配置 薪资项目 枚举
     * @return array[]
     */
    public function frontLineStaffSalaryConfigSalaryItem(): array
    {
        return [
            ['value' => 1, 'label' => static::$t->_('fsc_salary_item_type_ph_1')],
            ['value' => 2, 'label' => static::$t->_('fsc_salary_item_type_ph_2')],
            ['value' => 3, 'label' => static::$t->_('fsc_salary_item_type_ph_3')],
        ];
    }

    /**
     * 展示辅导员的职位
     * @return array[]
     */
    public function counselorShowJobTitle(): array
    {
        return [
            ['value' => 13, 'label' => 'Bike Courier'],
            ['value' => 37, 'label' => 'DC officer'],
            ['value' => 110, 'label' => 'Van Courier'],
            ['value' => 1000, 'label' => 'Tricycle Courier'],
            ['value' => 1194, 'label' => 'Truck Driver'],
            ['value' => 1675, 'label' => 'Truck Driver(night)'],
        ];
    }

    /**
     * @return array[]
     */
    public function workdaySettingStatus()
    {
        return [
            ['value' => '1', 'label' => static::$t->_('workday_setting_status_1')],
            ['value' => '2', 'label' => static::$t->_('workday_setting_status_0')],
        ];
    }

}
