<?php
/*
 * 除了计算逻辑,上传逻辑其他的业务逻辑,相当于gongzi_other_bll 菲律宾独有的写在这里
 */

namespace App\Modules\Ph\Services;

use App\Library\Enums;
use App\Library\Enums\SalaryEnums;
use App\Models\backyard\AttendanceDataMonthModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffTransferModel;
use App\Models\backyard\SysDepartmentModel;
use App\Services\GongziBaseService;
use App\Services\SettingEnvService;


class GongziService extends GongziBaseService
{
    const MONTH_TYPE_A = 1;
    const MONTH_TYPE_B = 2;
    const MONTH_TYPE_ALL = [self::MONTH_TYPE_A, self::MONTH_TYPE_B];
    /**
     * 真实公司ID
     */
    const REAL_COMPANY_ID_FULLFILLMENT = 25;
    const REAL_COMPANY_ID_COMMERCE = 5021;
    const REAL_COMPANY_ID_SOFTWARE = 5128;

    const REAL_COMPANY_ID_MONEY = 5111;
    const REAL_COMPANY_ID_PAY = 35;
    /**
     * 真实公司对应的通用公司枚举
     */
    const REAL_COMPANY_ID_MAP = [
        self::REAL_COMPANY_ID_FULLFILLMENT => SalaryEnums::COMPANY_ID_FULLFILLMENT,
        self::REAL_COMPANY_ID_COMMERCE     => SalaryEnums::COMPANY_ID_COMMERCE,
    ];
    const REAL_COMPANY_ID_MAP_202521 = [
        self::REAL_COMPANY_ID_FULLFILLMENT => SalaryEnums::COMPANY_ID_FULLFILLMENT,
        self::REAL_COMPANY_ID_COMMERCE     => SalaryEnums::COMPANY_ID_COMMERCE,
        self::REAL_COMPANY_ID_MONEY        => SalaryEnums::COMPANY_ID_MONEY,
        self::REAL_COMPANY_ID_PAY          => SalaryEnums::COMPANY_ID_PAY,
    ];
    public static $bank_type_name = [
        '0' => 'Not Known',
        '2' => 'SCB',
        '21' => 'UB',
    ];

    public static $specialCompanyStaffCodes = ['special_flashexpress_staff','special_fulfillment_staff','special_fcommerce_staff','special_software_staff','special_flash_money_staff','special_flash_pay_staff'];
    //导出excel 和 db字段的对应关系
    //默认是 db字段, 还可能是空, 还可能是常数
    //如果db字段是字符串 如name,不是数字,一定要在下面string类型里建好
    //注意 staff_info_id 必须是在第二列,不能变, 后面发工资条有用到
    public $excelShowMap = [
        //员工
//        'No' => '',
        'Emp.' => 'staff_info_id',
        'Name' => 'staff_name',
        'Corporate Name' => 'company_real_id', //真实
        'Payroll Company name' => 'company_id', //发薪公司
//        'Store Id'=>'sys_store_id',
//        'branch type'=>'sys_store_id',//sys_store_id通过这个获取网点类型
        'Branch' => 'branch_name',
        'Department' => 'department_name',
        'Position' => 'job_title_name',
//        'Disablity Status'=>'disability_status',
        'Social Status' => 'cal_mode',
        'Tax Status' => 'tax_mode',
        'job type' => 'week_day',

//       'store id'=>'sys_store_id'
        'Start' => 'start',
        'Last working day' => 'last_working_day',
        'Days' => 'total_month_days',//不输出 和 days一样?

        'LW' => 'lw',
        'AB' => 'ab',
        'OFF' => 'off',
        'SH' => 'sh',
        'RH(unpaid)' => 'unrh',
//        'pl' => 'PL Trial(-)',//2020年不用了
//        'Other UL(-)'=>0,//new 本期不做,直接为0
        'Days not in employment' => 'not_working_days',
        'Total working days' => 'total_working_days',
        'OFF not in employment' => 'off_not_in_employment', //入职日期以前和最后工作日以后的休息日
        'Transfer day' => 'transfer_day',
        'After transfer days' => 'after_transfer_days',

        'after transfer job type' => 'week_day_adj',
        //固定部分
        'Base Salary' => 'salary_base',//check
        'Base Salary Adj.' => 'salary_adj',
        'Salary Difference' => 'salary_difference',
        'Half-month salary' => 'salary',

        'Performance Allowance Base' => 'performance_base',
        'Performance Allowance Base Adj.' => 'performance_adj',
        'Performance Allowance Difference' => 'performance_difference',
        'Performance Allowance' => 'performance',

        'Other Taxable Allowance Base' => 'taxsubsidy_base',
        'Other Taxable Allowance Base Adj.' => 'taxsubsidy_adj',
        'Other Taxable Allowance Difference' => 'taxsubsidy_difference',
        'Other Taxable Allowance' => 'taxsubsidy',

        'Other Non-taxable Allowance Base' => 'subsidy_base',
        'Other Non-taxable Allowance Base Adj.' => 'subsidy_adj',
        'Other Non-taxable Allowance Difference' => 'subsidy_difference',
        'Other Non-taxable Allowance' => 'subsidy',

        'Deminimis benefits Base' => 'deminimis_base',
        'Deminimis benefits Base Adj.' => 'deminimis_adj',
        'Deminimis benefits Difference' => 'deminimis_difference',
        'Deminimis benefits Allowance' => 'deminimis',


        'Car Allowance per time before' => 'car_base',
        'Car On transfer before' => 'car_transfer_before_on',
        'Car Allowance per time after' => 'car_adj',
        'Car On transfer after' => 'car_transfer_after_on',
        'Car Allowance' => 'car',
        'Car Allowance (paid)' => 'car_paid',

        'Food Allowance per time before' => 'food_base',
        'Food On transfer before' => 'food_transfer_before_on',
        'Food Allowance per time after' => 'food_adj',
        'Food On transfer after' => 'food_transfer_after_on',
        'Food Allowance' => 'food',
        'Food Allowance (paid)' => 'food_paid',

        'Risk Allowance per time before' => 'risk_base',
        'Risk On transfer before' => 'risk_transfer_before_on',
        'Risk Allowance per time after' => 'risk_adj',
        'Risk On transfer after' => 'risk_transfer_after_on',
        'Risk Allowance' => 'risk',

        'Rate per Hour' => 'salary_hour',

        'Night Differential Hrs' => 'night_shift_len',
        'Night Amount' => 'night_shift',

        'Weekend 1 time' => 'ot_1_len',
        'OT 1 time' => 'ot_1',
        'Weekend 1.25 time' => 'ot_125_len',
        'OT 1.25 time' => 'ot_125',
        'Weekend 1.3 time' => 'ot_13_len',
        'OT 1.3 time' => 'ot_13',
        'Weekend 1.5 time' => 'ot_15_len',
        'OT 1.5 time' => 'ot_15',
        'Weekend 1.6 time' => 'ot_16_len',
        'OT 1.6 time' => 'ot_16',
        'Weekend 1.69 time' => 'ot_169_len',
        'OT 1.69 time' => 'ot_169',
        'Weekend 1.95 time' => 'ot_195_len',
        'OT 1.95 time' => 'ot_195',
        'Weekend 2.6 time' => 'ot_26_len',
        'OT 2.6 time' => 'ot_26',
        'Weekend 3.38 time' => 'ot_338_len',
        'OT 3.38 time' => 'ot_338',
        'OT' => 'ot',

        'Notebook Rental' => 'notebook',
        'KPI/competency incentive' => 'compensation_incentive',
        'House rental' => 'house',
        'Firestarter Award' => 'firestarer_award',
        'Christmas Bonus' => 'christmas_bonus',
//        'Postage and Telephone' => 'postage_tel',
        'Communication Allowance' => 'phone',
        'License Fee' => 'license_fee',
        'Gasoline Allowance' => 'gasoline',
        'Transportation' => 'transportation',
        'Incentive' => 'incentive',
        'Other Bonus' => 'other_bonus',
        'Recommendation' => 'recommended',
        'Refer&Earn Program' => 'refer_earn_program',//推荐活动奖励
        'Converted Paid Leaves' => 'converted_paid',
        'Income Tax Differences' => 'income_tax_diff',
        'other income1' => 'other_income1',//fixed/social new
        'other income2' => 'other_income2',//fixed/social new
        'other income3' => 'other_income3',//fixed new

        'Cumulative attendance day' => 'cumulative_attend_day',
        '13th month pay' => 'month_13_pay',
        'total income' => 'total_income',
        'No of Hrs Late' => 'late_len',
        'Late Amount' => 'late',

//        'PD days transfer before' => 'pd_transfer_before_on',
//        'PD days transfer after' => 'pd_transfer_after_on',
//        'Performance Deduct' => 'pd',
        'Other deduct before Tax1'=>'other_deduct_before_tax1',
        'Other deduct before Tax2'=>'other_deduct_before_tax2',
        'hire purchase' => 'hire_purchase',
        'Equipment' => 'equipment',
        'Uniform' => 'uniform',
        'Fleet Card' => 'fleet_card',
        'other deduct1' => 'other_deduct1',
        'other deduct2' => 'other_deduct2',
        'other deduct3' => 'other_deduct3',

//        'First half Social base' => 'first_social_base',
//        'Second half Social base' => 'second_social_base',
        'Social base' => 'social_base',


        'sss ER Company' => 'sss_company',
        'sss EE Staff' => 'sss_staff',
        'sss total' => 'sss',
        'phic ER company' => 'phic_company',
        'phic EE staff' => 'phic_staff',
        'phic total' => 'phic',
        'hdmf ER company' => 'hdmf_company',
        'hdmf EE staff' => 'hdmf_staff',
        'hdmf total' => 'hdmf',

        'Taxable Pay' => 'tax_base',
        'Tax' => 'tax',
        'Incentive Tax' => 'incentive_tax',
        'Salary Tax' => 'salary_tax',

        'Personal Loan' => 'personal_loan',
        'SSS Loan' => 'sss_loan',
        'SSS Calamity Loan' => 'sss_calamity_loan',
        'Pag ibig Loan' => 'pag_ibig_loan',
        'Deduct tax Differences' => 'deduct_tax_diff',
        'Incentive(paid)' => 'incentive_paid',
        'Negative salary last month' => 'negative_data_last',
        'Tax Unpaid' => 'tax_unpaid',//补税
        'Total Deduct' => 'total_deduct',
        'After tax income1'=>'after_tax_income1',
//        'After tax income2'=>'after_tax_income2',
        'Incentive Tax Refund' => 'incentive_tax_rebund',
        'Tax Refund' => 'tax_refund',//退税
        'Net Amount' => 'net_income',
        'negative data' => 'negative_data',
        'Remark' => 'remark',
        'Remark1' => 'remark1',//new
        'Remark2' => 'remark2',//new
        'Team' => 'team',
        'Bank name' => 'bank_type',
        'Bank account no.' => 'bank_no',
        'status' => 'status',
        'update time' => 'updated_at',
    ];

    //$excelShowMap每加一个字段,需要在 $str_type_column(导出字符串类型), $int_type_column(导出整形类型),$map_type_column (需要枚举类型关联)里添加进去,如果不加,默认是导出float类型, 写的是salary_gongzi数据库里的字段名; 没有的不需要添加,只是为了导出
    public static $str_type_column = ['start', 'last_working_day', 'transfer_day', 'remark','remark1','remark2','team','staff_name','job_title_name','branch_name','department_name','bank_no','updated_at'];

    public static $int_type_column = ['staff_info_id','job_title_id','department_id',
        'salary_base','salary_adj','performance_base','performance_adj','taxsubsidy_base','taxsubsidy_adj','subsidy_base','subsidy_adj','deminimis_base','deminimis_adj','food_base','food_adj','car_base','car_adj','risk_base','risk_adj'];

    public static $map_type_column = ['bank_type', 'cal_mode','tax_mode', 'status','company_real_id','company_id','hire_type'];

    /**
     * 导出工资展示
     * 应对自然年内小版本差异
     */
    public function getExcelShowMap (): array
    {
        return $this->excelShowMap;
    }
    /**
     * 当计算下半月工资时使用上半月 total_income - month_13_pay (不直接使用字段 直接加逻辑字段计算)
     * @var array
     */
    public $firstHalfMonthSSSBase1 = [];
    private function mkSelectSql(){

        $mkCaseSql = function($field,$config){
            $sql = " case {$field} ";
            foreach ($config as $value=>$show_value){
                $sql .= " when '{$value}' then  '{$show_value}' ";
            }
            $sql .= " else '' end as {$field} ";
            return $sql;
        };

        $select_sql = '';
        $fields = $this->getExcelShowMap();
        foreach ($fields as $field){
            if (in_array($field, self::$map_type_column)) {
                switch ($field) {
                    case 'bank_type':
                        $config = self::$bank_type_name;
                        break;
                    case 'cal_mode':
                        $config = parent::CAL_MODE_NAME_ARR;
                        break;
                    case 'tax_mode':
                        $config = parent::TAX_MODE_NAME_ARR;
                        break;
                    case  'status':
                        $config = parent::STATUS_NAME_MAP;
                        break;
                    case  'hire_type':
                        $config = $this->getSalaryHireTypeMap();
                        break;
                    case 'company_id':
                    case 'company_real_id':
                        $config = SalaryEnums::COMPANY_TYPE_NAME_MAP['ph'];
                        break;
                }
                $field_sql = $mkCaseSql($field,$config);
            }else{
                $field_sql = " {$field} ";
            }
            $select_sql .= $field_sql.',';
        }
        return rtrim($select_sql,',');
    }


    /*
     * $staff_type: 1总部, 2 网点 ,
     * $sql_filter : 工资表查询where条件。  第一个and 不写, 多个条件的话,中间的and 要写
     *
     */
    public function getGongziDataForExport($gongzi_month, $staff_type, $op_type = '', $export_type, $staff_id_arr = [], $sql_filter = '', $company_id = null)
    {
        try {
            $table_map = SalaryEnums::GONGZI_TABLE_TYPE_MAP;
            if (!isset($table_map[$export_type])) {
                return [];
            } else {
                $table_base_name = $table_map[$export_type];
            }

            $table_name = $this->get_table_name($table_base_name, $op_type);

            $sql_in = '';
            if (!empty($staff_id_arr)) {
                $staff_id_str = implode(',', $staff_id_arr);
                $sql_in = ' and staff_info_id in (' . $staff_id_str . ') ';
            }

            if (!empty($company_id)) {
                $sql_in .= ' and company_id = ' . $company_id;
            }
            $this->logger->info(['not_manage_staffs'=>$this->not_manage_staffs,'only_manage_staffs'=>$this->only_manage_staffs]);

            $sql_in .= ' and company_id in ('.implode(',',$this->manage_company_ids).') ';

            if ($this->not_manage_staffs) {
                $sql_in .= ' and staff_info_id NOT IN (' . implode(',', $this->not_manage_staffs) . ') ';
            }

            if ($this->only_manage_staffs) {
                $sql_in .= ' and staff_info_id IN (' . implode(',', $this->only_manage_staffs) . ') ';
            }

            if ($staff_type == 1) {
                $sql_where = ' and sys_store_id = "-1" ' . $sql_in;
            } elseif ($staff_type == 2) {
                $sql_where = ' and sys_store_id != "-1" ' . $sql_in;
            } elseif ($staff_type == 0) {
                $sql_where = $sql_in;
            } else {
                return [];
            }

            $sql_condition = '';
            if (!empty($sql_filter)) {
                $sql_condition = ' and ' . $sql_filter . ' ';
            }

            $select_sql = $this->mkSelectSql();
            $sql = "select $select_sql from $table_name where excel_month='" . $gongzi_month . "' " . $sql_where . $sql_condition;
            $this->logger->info("download_sql:".$sql);
            $data =   $this->{$this->write_db}->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
            $new_data = [];
            foreach ($data as $item){
                $fmt_item = [];
                foreach ($item as $key=>$value){
                    if(in_array($key,self::$str_type_column) || in_array($key,self::$map_type_column)){
                        $fmt_value = $value;
                    }elseif(in_array($key,self::$int_type_column)) {
                        $fmt_value = intval($value);
                    }else{
                        $fmt_value = floatval($value);
                    }
                    $fmt_item[] = $fmt_value;
                }
                $new_data[] = $fmt_item;
            }
            return $new_data;
        } catch (\Exception $e) {
            $this->getDi()->get('logger')->write_log('getGongziDataForExport have exception' . $e->getMessage() . $e->getTraceAsString(), 'error');
            return [];
        }

    }


    //员工的工资查询，对应excel,用于发工资条
    //$paid_date_range array [$min_day, $max_day]
    public function getExcelDataFinal($month, array $staff_id_arr = [], $export_type = 'normal', $op_type = '')
    {
        try {
            $header = array_keys($this->excelShowMap);
            $sql_filter = "status=" . self::SALARY_STATUS_PAID;
            $all_data = $this->getGongziDataForExport($month, 0, $op_type, $export_type, $staff_id_arr, $sql_filter);
            $_data = [];
            if (!empty($all_data)) {
                foreach ($all_data as $data) {
                    $t = [];
                    array_walk($data, function ($v, $k) use ($header, &$t) {
                        $t[$header[$k]] = $v;
                    });
                    $_data[] = $t;
                }
            }
            return $_data;
        } catch (\Exception $e) {
            $this->getDi()->get('logger')->write_log('getExcelDataFinal have exception' . $e->getMessage() . $e->getTraceAsString(), 'error');
            return [];
        }
    }
    /*
     * 根据当前日期,计算上一个计算周期
     */
    public function get_last_month()
    {
        $today = gmdate('Y-m-d', time() + ($this->timeOffset) * 3600);
        $this_month = substr($today, 0, 7);
        $last_month = date('Y-m', strtotime($this_month . '-01' . '-1 month'));

        if (date('d', strtotime($today)) > '15') {
            return $this_month . GongziService::MONTH_TYPE_A;
        } else {
            return $last_month . GongziService::MONTH_TYPE_B;
        }
    }

    /**
     * 获取员工最后一天的网点&总部 部门 职位
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getEndDataStaffTransfer($start_date,$end_date): array
    {
        $last_store = [];
        $staffFind =  HrStaffTransferModel::find([
            'columns' => 'max(id) as id',
            'conditions' => 'stat_date>=:start_date: and stat_date<=:end_date:',
            'bind' => ['start_date'=>$start_date,'end_date' => $end_date],
            'group' => 'staff_info_id',
        ])->toArray();
        $staffFindChunk =  array_chunk(array_column($staffFind,'id'),400);
        foreach ($staffFindChunk as $chunkStaff) {
          $find =  HrStaffTransferModel::find([
                'columns' => 'staff_info_id,store_id',
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => $chunkStaff],
            ])->toArray();
            $store = array_column($find,'store_id','staff_info_id');
            $last_store = $last_store + $store;
        }
        return $last_store;
    }

    /**
     * todo 逻辑变更时 需要同步修改 GongzicalService get_company_id 方法
     * 获取员工对应发薪的公司
     * @param $month
     * @param $month_type
     * @return array
     */
    public function get_staff_id_payroll_company_info($month, $month_type): array
    {
        $id_company_map = [];
        $salaryMonth    = $month . $month_type;
        if ($salaryMonth >= '2025-021') {
            //Flash Pay
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hsi.staff_info_id');
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->innerJoin(AttendanceDataMonthModel::class, 'hsi.staff_info_id = adm.staff_info_id', 'adm');
            $builder->innerJoin(SysDepartmentModel::class, 'hsi.node_department_id = ssd.id', 'ssd');
            $builder->where('ssd.company_id = :company_id:', ['company_id' => self::REAL_COMPANY_ID_PAY]);
            $builder->andWhere('adm.stat_period = :query_month: and adm.sub_period= :sub_period:',
                ['query_month' => $month, 'sub_period' => $month_type]);
            $attendance_data = $builder->getQuery()->execute()->toArray();
            $payStaff        = array_column($attendance_data, 'staff_info_id');
            $this->set_company_info($payStaff, SalaryEnums::COMPANY_ID_PAY, $id_company_map);
            //Flash Money
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('hsi.staff_info_id');
            $builder->from(['hsi' => HrStaffInfoModel::class]);
            $builder->innerJoin(AttendanceDataMonthModel::class, 'hsi.staff_info_id = adm.staff_info_id', 'adm');
            $builder->innerJoin(SysDepartmentModel::class, 'hsi.node_department_id = ssd.id', 'ssd');
            $builder->where('ssd.company_id = :company_id:', ['company_id' => self::REAL_COMPANY_ID_MONEY]);
            $builder->andWhere('adm.stat_period = :query_month: and adm.sub_period= :sub_period:',
                ['query_month' => $month, 'sub_period' => $month_type]);
            $attendance_data = $builder->getQuery()->execute()->toArray();
            $payStaff        = array_column($attendance_data, 'staff_info_id');
            $this->set_company_info($payStaff, SalaryEnums::COMPANY_ID_MONEY, $id_company_map);
        }
        //FulFillment
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->innerJoin(AttendanceDataMonthModel::class, 'hsi.staff_info_id = adm.staff_info_id', 'adm');
        $builder->innerJoin(SysDepartmentModel::class, 'hsi.node_department_id = ssd.id', 'ssd');
        $builder->where('ssd.company_id = :company_id:', ['company_id' => self::REAL_COMPANY_ID_FULLFILLMENT]);
        $builder->andWhere('adm.stat_period = :query_month: and adm.sub_period= :sub_period:',
            ['query_month' => $month, 'sub_period' => $month_type]);
        $attendance_data = $builder->getQuery()->execute()->toArray();
        $ffmStaff        = array_column($attendance_data, 'staff_info_id');
        $this->set_company_info($ffmStaff, SalaryEnums::COMPANY_ID_FULLFILLMENT, $id_company_map);


        //F Commerce
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->innerJoin(AttendanceDataMonthModel::class, 'hsi.staff_info_id = adm.staff_info_id', 'adm');
        $builder->innerJoin(SysDepartmentModel::class, 'hsi.node_department_id = ssd.id', 'ssd');
        $builder->where('ssd.company_id = :company_id:', ['company_id' => self::REAL_COMPANY_ID_COMMERCE]);
        $builder->andWhere('adm.stat_period = :query_month: and adm.sub_period= :sub_period:',
            ['query_month' => $month, 'sub_period' => $month_type]);
        $attendance_data = $builder->getQuery()->execute()->toArray();
        $commerceStaff   = array_column($attendance_data, 'staff_info_id');
        $this->set_company_info($commerceStaff, SalaryEnums::COMPANY_ID_COMMERCE, $id_company_map);


        //最高优先级，所以放在下面去覆盖  Software
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->innerJoin(AttendanceDataMonthModel::class, 'hsi.staff_info_id = adm.staff_info_id', 'adm');
        if ($salaryMonth >= '2025-021') {
            $builder->where('hsi.nationality != :nationality:', ['nationality' => Enums::HRIS_NATIONALITY_4]);
        } else {
            $builder->where('hsi.nationality != :nationality: and hsi.working_country = :working_country: ',
                ['nationality' => Enums::HRIS_NATIONALITY_4, 'working_country' => Enums::HRIS_NATIONALITY_4]);
        }
        $attendance_data = $builder->getQuery()->execute()->toArray();
        $softwareStaff   = array_column($attendance_data, 'staff_info_id');

        $this->set_company_info($softwareStaff, SalaryEnums::COMPANY_ID_SOFTWARE, $id_company_map);


        $settingEnv = (new SettingEnvService());

        $commerce_staffs = $settingEnv->getSetVal('special_fcommerce_staff');
        if ($commerce_staffs) {
            $this->set_company_info(explode(',', $commerce_staffs), SalaryEnums::COMPANY_ID_COMMERCE, $id_company_map);
        }
        $flash_staffs = $settingEnv->getSetVal('special_flashexpress_staff');
        if ($flash_staffs) {
            $this->set_company_info(explode(',', $flash_staffs), SalaryEnums::COMPANY_ID_EXPRESS, $id_company_map);
        }
        $fulfillment_staffs = $settingEnv->getSetVal('special_fulfillment_staff');
        if ($fulfillment_staffs) {
            $this->set_company_info(explode(',', $fulfillment_staffs), SalaryEnums::COMPANY_ID_FULLFILLMENT,
                $id_company_map);
        }
        $software_staffs = $settingEnv->getSetVal('special_software_staff');
        if ($software_staffs) {
            $this->set_company_info(explode(',', $software_staffs), SalaryEnums::COMPANY_ID_SOFTWARE, $id_company_map);
        }
        $money_staffs = $settingEnv->getSetVal('special_flash_money_staff');
        if ($money_staffs) {
            $this->set_company_info(explode(',', $money_staffs), SalaryEnums::COMPANY_ID_MONEY, $id_company_map);
        }
        $pay_staffs = $settingEnv->getSetVal('special_flash_pay_staff');
        if ($pay_staffs) {
            $this->set_company_info(explode(',', $pay_staffs), SalaryEnums::COMPANY_ID_PAY, $id_company_map);
        }
        return $id_company_map;
    }

    /**
     * 菲律宾使用 员工日薪还是月薪
     * 获取software员工
     * @param $gongzi_month
     * @param $op_type
     * @return array
     */
    public function get_staff_cal_rule($gongzi_month, $op_type): array
    {
        $table_name = $this->get_table_name('salary_special_staff', $op_type);
        $sql = "select staff_info_id,type  from {$table_name}  where type in(12,13) and excel_month=:excel_month";
        $result = $this->{$this->read_db}->query($sql,['excel_month' => $gongzi_month])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        return array_column($result, 'type','staff_info_id');
    }

    /**
     * 获取下半月工资时使用上半月 total_income - month_13_pay
     * @param $gongzi_month
     * @param $op_type
     */
    public function calFirstHalfMonthSSSBase1($gongzi_month, $op_type){
        $table_name = $this->get_table_name('salary_gongzi', $op_type);
        $firstHalfMonth = substr($gongzi_month,0,7).self::MONTH_TYPE_A;
        $result = $this->{$this->read_db}->query("select staff_info_id,(total_income-month_13_pay) as sss_base1 from {$table_name} where excel_month=:excel_month",['excel_month' => $firstHalfMonth])->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $this->firstHalfMonthSSSBase1 = array_column($result,'sss_base1','staff_info_id');
    }

    public function cal_total_deduct(&$realRowInfo, $fields)
    {
        $realRowInfo['total_deduct'] =
            $realRowInfo['late']  + $realRowInfo['penalty1'] + $realRowInfo['equipment']
            + $realRowInfo['uniform'] + $realRowInfo['fleet_card'] + $realRowInfo['other_deduct1'] + $realRowInfo['other_deduct2']
            + $realRowInfo['other_deduct3'] + $realRowInfo['sss_staff'] + $realRowInfo['phic_staff']
            + $realRowInfo['hdmf_staff'] + $realRowInfo['salary_tax'] + $realRowInfo['personal_loan'] + $realRowInfo['sss_loan']
            + $realRowInfo['sss_calamity_loan'] + $realRowInfo['pag_ibig_loan'] + $realRowInfo['deduct_tax_diff'] + $realRowInfo['incentive_paid']
            + $realRowInfo['negative_data_last'] + $realRowInfo['other_deduct_before_tax1']+ $realRowInfo['other_deduct_before_tax2']+$realRowInfo['incentive_tax']
            + $realRowInfo['tax_unpaid'];
        $realRowInfo['total_deduct'] = round($realRowInfo['total_deduct'], 2);
        return true;
    }


    //total_income - total_deduct 的实际值
    public function cal_negative_data(&$realRowInfo, $fields)
    {
        $realRowInfo['negative_data'] = $realRowInfo['salary'] + $realRowInfo['performance'] + $realRowInfo['taxsubsidy'] + $realRowInfo['subsidy'] + $realRowInfo['deminimis'] + $realRowInfo['car'] + $realRowInfo['food'] + $realRowInfo['risk'] + $realRowInfo['night_shift'] +
            $realRowInfo['ot'] + $realRowInfo['notebook'] + $realRowInfo['compensation_incentive'] + $realRowInfo['house'] +
            $realRowInfo['christmas_bonus'] + $realRowInfo['phone'] + $realRowInfo['license_fee'] + $realRowInfo['gasoline'] +
            $realRowInfo['transportation'] + $realRowInfo['incentive'] + $realRowInfo['other_bonus'] + $realRowInfo['recommended'] +
            $realRowInfo['converted_paid'] + $realRowInfo['income_tax_diff'] + $realRowInfo['other_income1'] + $realRowInfo['other_income2'] +
            $realRowInfo['other_income3'] + $realRowInfo['month_13_pay'] + $realRowInfo['after_tax_income1'] + $realRowInfo['incentive_tax_rebund'] +
            $realRowInfo['firestarer_award']+$realRowInfo['refer_earn_program'] - $realRowInfo['incentive_tax']
            - ($realRowInfo['late'] + $realRowInfo['penalty1'] + $realRowInfo['equipment']
                + $realRowInfo['uniform'] + $realRowInfo['fleet_card'] + $realRowInfo['other_deduct1'] + $realRowInfo['other_deduct2']
                + $realRowInfo['other_deduct3'] + $realRowInfo['sss_staff'] + $realRowInfo['phic_staff']
                + $realRowInfo['hdmf_staff'] + $realRowInfo['salary_tax'] + $realRowInfo['personal_loan'] + $realRowInfo['sss_loan']
                + $realRowInfo['sss_calamity_loan'] + $realRowInfo['pag_ibig_loan'] + $realRowInfo['deduct_tax_diff'] + $realRowInfo['incentive_paid']
                + $realRowInfo['negative_data_last'] +$realRowInfo['other_deduct_before_tax1']+ $realRowInfo['other_deduct_before_tax2'])
            + $realRowInfo['tax_refund'] - $realRowInfo['tax_unpaid'];

        $realRowInfo['negative_data'] = round($realRowInfo['negative_data'], 2);
    }

    //Net Amount
    public function cal_net_income(&$realRowInfo, $fields)
    {
        $realRowInfo['net_income'] = $realRowInfo['total_income'] + $realRowInfo['after_tax_income1'] + $realRowInfo['incentive_tax_rebund'] - $realRowInfo['total_deduct'] + $realRowInfo['tax_refund'];
        $realRowInfo['net_income'] = $realRowInfo['net_income'] <= 0 ? 0 : round($realRowInfo['net_income'], 2);
        return true;
    }


    public function cal_total_income(&$realRowInfo, $fields)
    {
        $realRowInfo['total_income'] =
            $realRowInfo['salary'] + $realRowInfo['performance'] + $realRowInfo['taxsubsidy'] + $realRowInfo['subsidy'] + $realRowInfo['deminimis'] +
            $realRowInfo['car'] + $realRowInfo['food'] + $realRowInfo['risk'] + $realRowInfo['night_shift'] +
            $realRowInfo['ot'] + $realRowInfo['notebook'] + $realRowInfo['compensation_incentive'] + $realRowInfo['house'] +
            $realRowInfo['christmas_bonus'] + $realRowInfo['phone'] + $realRowInfo['license_fee'] + $realRowInfo['gasoline'] +
            $realRowInfo['transportation'] + $realRowInfo['incentive'] + $realRowInfo['other_bonus'] + $realRowInfo['recommended'] +
            $realRowInfo['converted_paid'] + $realRowInfo['income_tax_diff'] + $realRowInfo['other_income1'] + $realRowInfo['other_income2'] +
            $realRowInfo['other_income3'] + $realRowInfo['month_13_pay'] + $realRowInfo['firestarer_award']+$realRowInfo['refer_earn_program'];
        $realRowInfo['total_income'] = round($realRowInfo['total_income'], 2);
        return true;
    }


    //医保
    public function cal_phic(&$realRowInfo){

        $monthType = substr($realRowInfo['excel_month'], -1, 1);

        $realRowInfo['phic_company'] = 0;
        $realRowInfo['phic_staff']   = 0;
        $realRowInfo['phic']         = 0;
        if ($realRowInfo['cal_mode'] == GongziBaseService::CAL_MODE_NO_SHEBAO) {
            return true;
        }
        if ($monthType == GongziService::MONTH_TYPE_B) {
            $social_base = $realRowInfo['social_base'];
            if ($realRowInfo['excel_month'] >= '2022-06') {
                if ($social_base <= 10000) {
                    $realRowInfo['phic_company'] = 200;
                    $realRowInfo['phic_staff']   = 200;
                } elseif ($social_base >= 80000) {
                    $realRowInfo['phic_company'] = 1600;
                    $realRowInfo['phic_staff']   = 1600;
                } else {
                    $realRowInfo['phic_company'] = round($social_base * 0.02, 2);
                    $realRowInfo['phic_staff']   = bcmul($social_base * 0.04, 0.5, 2);
                }
            } else {
                if ($social_base <= 10000) {
                    $realRowInfo['phic_company'] = 150;
                    $realRowInfo['phic_staff']   = 150;
                } elseif ($social_base >= 60000) {
                    $realRowInfo['phic_company'] = 900;
                    $realRowInfo['phic_staff']   = 900;
                } else {
                    //政府部门的系统有问题,正常:14007 * 0.015 = 210.105==>210.11
                    //公司的四舍五入,个人的都舍去
                    $realRowInfo['phic_company'] = round($social_base * 0.015, 2);
                    $realRowInfo['phic_staff']   = bcmul($social_base * 0.03, 0.5, 2);
                }
            }
        }
        $realRowInfo['phic'] = $realRowInfo['phic_company'] + $realRowInfo['phic_staff'];
        $this->get_real_cover_data($realRowInfo, 'phic_company');
        $this->get_real_cover_data($realRowInfo, 'phic_staff');
        $this->get_real_cover_data($realRowInfo, 'phic');
        return true;
    }

    public function getAttendanceDataField()
    {
        return [
            'lw'                      => 'LW',
            'lw_after'                => 'LW_transfer_after',
            'ab'                      => 'AB',
            'ab_after'                => 'AB_transfer_after',
            'off'                     => 'OFF',
            'off_after'               => 'OFF_transfer_after',
            'sh'                      => 'SH',
            'sh_after'                => 'SH_transfer_after',
            'unrh'                    => 'RH_UNPAID',
            'unrh_after'              => 'RH_UNPAID_transfer_after',
            'on'                      => 'On_t',//hcm里没有进行转化到On
            'food_on'                 => 'Food_ON',
            'food_transfer_before_on' => 'Food_ON_transfer_before',
            'food_transfer_after_on'  => 'Food_ON_transfer_after',
            'car_on'                  => 'Car_ON',
            'car_transfer_before_on'  => 'Car_ON_transfer_before',
            'car_transfer_after_on'   => 'Car_ON_transfer_after',
            'risk_on'                 => 'Car_ON',
            'risk_transfer_before_on' => 'Car_ON_transfer_before',
            'risk_transfer_after_on'  => 'Car_ON_transfer_after',
            'night_shift_len'         => 'night_shift_minutes',
            'late_len'                => 'late_times',
            'ot_1_len'                => 'RH_1_times',
            'ot_125_len'              => 'weekday_1_2_5_times',
            'ot_13_len'               => '1_3_times_all',
            'ot_15_len'               => 'SH_OFF_1_5_times',
            'ot_16_len'               => 'RH_OFF_1_6_times',
            'ot_169_len'              => '1_6_9_times_all',
            'ot_195_len'              => 'over_SH_OFF_1_9_5_times',
            'ot_26_len'               => 'over_RH_2_6_times',
            'ot_338_len'              => 'over_RH_OFF_3_3_8_times',
            'off_not_in_employment'   => 'off_not_in_employment',
        ];
    }


    public function getCalOtField()
    {
        return [
            'ot_1'   => 1,
            'ot_125' => 1.25,
            'ot_13'  => 1.3,
            'ot_15'  => 1.5,
            'ot_16'  => 1.6,
            'ot_169' => 1.69,
            'ot_195' => 1.95,
            'ot_26'  => 2.6,
            'ot_338' => 3.38,
        ];
    }

    //计算Cumulative salary 公式
    public function getCumulativeSalaryFormula()
    {
        return 'sum(salary) as sum_salary,sum(late) as sum_late';
    }

    /**
     * 通讯补贴
     * @param $realRowInfo
     * @param $fields
     * @return void
     */
    public function  cal_communication_allowance(&$realRowInfo, $fields)
    {

    }
}
