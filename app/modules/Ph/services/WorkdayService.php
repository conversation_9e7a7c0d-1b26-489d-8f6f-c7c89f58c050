<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/8/4
 * Time: 9:13 PM
 */

namespace App\Modules\Ph\Services;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffShiftOperateLogModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Modules\Ph\library\Enums\enums;
use App\Services\DefaultRestDayService;
use App\Services\HrShiftService;
use App\Services\LeaveBllService;
use App\Services\StaffPublicHolidayService;
use App\Services\WorkdaySettingService;
use App\Services\WorkShiftService;
use DateTime;
use Exception;
use App\Library\FlashOss;
use App\Library\DateHelper;
use App\Library\Enums\GlobalEnums;
use App\Services\StaffOffDayService;
use App\Library\Exception\BusinessException;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\ThailandHolidayModel;
use App\Services\WorkdayService as BaseWorkdayService;
use App\Library\Enums as GlobalEnum;
use phpDocumentor\Reflection\TypeResolver;


class WorkdayService extends BaseWorkdayService
{

    const WORKDAY_OSS_PATH = 'WORK_DAY';

    //排班建议用
    public $workDayOff;
    public $shiftData;

    //小方块展示 请假缩写 其他的不用 和薪酬那边不一样
    public $salaryLeaveType = ['AL', 'SL', 'CT', 'PL', 'LW', 'USL'];


    public function formatList($param, $data)
    {
        $month = $param['month'];
        //获取公休日 6天班的
        $holiday_data = $this->get_holidays(['date' => $month . '-01']);
        $rh_holiday   = $holiday_data[ThailandHolidayModel::HOLIDAY_TYPE_RH];
        $sh_holiday   = $holiday_data[ThailandHolidayModel::HOLIDAY_TYPE_SH];

        $staff_ids           = array_column($data, 'staff_info_id');
        $shift               = $this->get_workdays_by_ids($staff_ids,$month);
        $defaultRestStaffMap = (new DefaultRestDayService())->getAll($staff_ids);

        //班次 班次server  过去日期 history表 当天 hr shift 未来 临时中间表和 preset
        $shiftServer     = new HrShiftService();
        $dateList        = getDateByMonth($month);
        //如果 查询开始日期小于当天把上个月的也取出来
        $this->setLiveJob();
        $shiftStaffIds = $this->getShiftStaffIds($data);
        if(date('Y-m-d') < $dateList[0]){
            $lastMonth = date('Y-m',strtotime("{$dateList[0]} -1 month"));
            $dates = array_merge(getDateByMonth($lastMonth),$dateList);
            $this->shiftData = $shiftServer->getShiftInfos($shiftStaffIds, $dates);
        }else{
            $this->shiftData = $shiftServer->getShiftInfos($shiftStaffIds, $dateList);
        }

        //获取 请假
        $leaveServer     = new LeaveBllService();
        $leaveData       = $leaveServer->getLeaveByDate($staff_ids, $dateList);
        $this->leaveData = empty($leaveData) ? [] : array_column($leaveData, null, 'u_key');

        //每个格子的请假班次信息 把请假和班次 拼装到一个list
        $cubeInfo = $this->workdayCube($staff_ids, $dateList);

        foreach ($data as &$v) {
            if ($v['state'] == HrStaffInfoModel::STATE_ON_JOB && $v['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES) {//待离职
                $v['state'] = 999;
            }
            $v['state_name']       = !empty(\App\Library\Enums::$hris_working_state[$v['state']]) ? self::$t->_(\App\Library\Enums::$hris_working_state[$v['state']]) : '';

            //主播职位 不能点击班次
            $v['is_live_master'] = in_array($v['job_title'], $this->liveJobId);
            $v['shift'] = empty($shift[$v['staff_info_id']][$month]) ? [] : $shift[$v['staff_info_id']][$month];
            //拼接总部名称
            if (empty($v['store_name']) && $v['id'] == '-1') {
                $v['store_name'] = GlobalEnums::HEAD_OFFICE;
            }
            $v['hire_type_text']        = self::$t->_('hire_type_'.$v['hire_type']);
            $v['holiday']['rh']         = $rh_holiday;//还有 rh  sh 等
            $v['holiday']['sh']         = $sh_holiday;//还有 rh  sh 等
            $v['default_rest_day_date'] = $defaultRestStaffMap[$v['staff_info_id']] ?? [];

            //新增内容 方块里面新增的信息
            $v['cube_info'] = $cubeInfo[$v['staff_info_id']] ?? [];
        }
        return $data;
    }

    public function leaveTypeCode(){
        return enums::$leave_type2stat_type;
    }

    /**
     * 初始化 休息日和假日
     * @return $this
     */
    protected function initStaffHolidayAndOffDay(): \App\Services\WorkdayService
    {
        //获取公共的休息日
        $holidays = $this->get_holidays([
            'date'      => date('Y-m-d', strtotime($this->date) - 30 * 86400),
        ]);
        if (!empty($holidays)) {
            $this->commonPublicHoliday = array_merge($holidays[ThailandHolidayModel::HOLIDAY_TYPE_RH],$holidays[ThailandHolidayModel::HOLIDAY_TYPE_SH]);
            sort($this->commonPublicHoliday);
        }
        $this->allPublicHoliday = $this->commonPublicHoliday;
        return $this;
    }
    /**
     * 保存轮休数据
     * @throws Exception
     */
    protected function addStaffWorkdays(): bool
    {
        //验证设置日期
        if (!$this->isWorkDayRootId) {
            if ($this->canModifyTodayRest && $this->date < date('Y-m-d')) {
                throw new BusinessException(self::$t->_('cannot_be_set_on_less_than_today'));
            }
            if (!$this->canModifyTodayRest && $this->date <= date('Y-m-d')) {
                throw new BusinessException(self::$t->_('cannot_be_set_on_today_or_less_than_today'));
            }
        }

        //新增规则配置校验 能否修改自己的休息日
        if(!$this->canEditSelf){
            throw new BusinessException(self::$t->_('can_not_edit_self_off'));
        }
        $staffOffDayService = new StaffOffDayService();
        $setOffDays         = $staffOffDayService->getStaffDataByDates($this->staff_info_id,
            DateHelper::getWeekAllDaysByDate($this->date));

        $set_limit = 7 - ($this->staffInfo['week_working_day'] ?: 6);
        //已经设置过了
        if (!$this->isWorkDayRootId && in_array($this->date, $setOffDays)) {
            throw new BusinessException(self::$t->_('workdays_already_set',
                ['date' => $this->date, 'staff_info_id' => $this->staff_info_id, 'name' => $this->staffInfo['name']]));
        }
        //6天班的可以设置 1天休息日，5天班的可以设置 2天休息日
        if (!$this->isWorkDayRootId && $this->staffInfo['week_working_day'] != HrStaffInfoModel::WEEK_WORKING_DAY_FREE && !in_array($this->date, $this->allPublicHoliday) && count(array_diff($setOffDays,
                $this->allPublicHoliday)) >= $set_limit) {
            throw new BusinessException(self::$t->_('workdays_set_limit',
                ['num' => $set_limit, 'staff_info_id' => $this->staff_info_id, 'name' => $this->staffInfo['name']]));
        }
        //校验与上一个自然周和下一个自然周设置的休息日间隔是否<=6个自然日
        if (!$this->isWorkDayRootId && $this->staffInfo['week_working_day'] != HrStaffInfoModel::WEEK_WORKING_DAY_FREE && !$this->is_confirm && !$this->check_week_off_day($this->staff_info_id, $this->date)) {
            throw new BusinessException(self::$t->_('off_day_less_than_6_days'), 10010);
        }
        $this->checkWorkDaySuggest([$this->staff_info_id],[$this->date],$this->operator);

        //自由轮休 验证轮休配置
        if ($this->staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
            (new WorkdaySettingService())->checkOffDayAdd($this->staffInfo, $this->date,$this->operator);
        }

        //可以设置休息日
        $insert['staff_info_id'] = $this->staff_info_id;
        $insert['month']         = date('Y-m', strtotime($this->date));
        $insert['date_at']       = $this->date;
        $insert['operator']      = $this->operator;
        $insert['remark']        = 'api add';
        if (!$staffOffDayService->saveData($insert)) {
            throw  new \Exception('eeeeee1');
        }
        //操作日志
        $insert['operate_id'] = $this->operator;
        $logServer = new WorkShiftService();
        $logServer->addShiftLog(HrStaffShiftOperateLogModel::EDIT_TYPE_ADD_OFF,$insert);
        return true;
    }

    /**
     * 校验与上一个自然周和下一个自然周设置的休息日间隔是否<=6个自然日
     * 如果上周设置了休息日，当周最早的休息日与上周最晚的休息间隔是否<=6个自然日 & 如果下周设置了休息日，当周最晚的休息日与下周最早的休息日间隔是否<=6个自然日
     * 当周最早的休息日&当周最晚的休息日 包含要设置的日期
     * @param $staff_info_id
     * @param $day
     * @return bool
     * @throws BusinessException|Exception
     */
    public function check_week_off_day($staff_info_id, $date): bool
    {
        $w         = date("w", strtotime($date));                          //获取当前周的第几天 周日是 0 周一 到周六是 1 -6
        $d         = $w ? $w - 1 : 6;                                      //如果是周日 -6天
        $now_start = date("Y-m-d", strtotime("$date -".$d." days"));       //本周开始时间
        $now_end   = date("Y-m-d", strtotime("$now_start +6 days"));       //本周结束时间

        //上周一
        $before_monday = date('Y-m-d', strtotime("$now_start - 7 days"));  //上周开始时间
        //上周日
        $before_sunday = date('Y-m-d', strtotime("$now_start - 1 days"));  //上周结束时间
        //获取当周
        $currentFind = HrStaffWorkDaysModel::find([
            'conditions' => ' staff_info_id = :staff_info_id: and date_at >= :before_monday: and date_at <= :before_sunday: ',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'before_monday' => $now_start,
                'before_sunday' => $now_end,
            ],
            'columns'    => 'date_at',
            'order'      => 'date_at desc',
        ])->toArray();
        $currentDate = array_column($currentFind,'date_at');
        $currentDate[] = $date;
        sort($currentDate);
        $currentFirstDate = current($currentDate);
        $currentLastDate  = end($currentDate);
        //获取上周最近的一个休息日
        $near_before_off_day = HrStaffWorkDaysModel::findFirst([
            'conditions' => ' staff_info_id = :staff_info_id: and date_at >= :before_monday: and date_at <= :before_sunday: ',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'before_monday' => $before_monday,
                'before_sunday' => $before_sunday,
            ],
            'columns'    => 'date_at',
            'order'      => 'date_at desc',
        ]);
        if ($near_before_off_day && $near_before_off_day = $near_before_off_day->date_at) {
            $current_date_obj    = new DateTime($currentFirstDate);
            $before_date_max_obj = new DateTime($near_before_off_day);
            $days                = $current_date_obj->diff($before_date_max_obj)->days;
            if ($days > 7) {
                return false;
            }
        }
        //下周一
        $after_monday = date('Y-m-d', strtotime("$now_end + 1 days"));     //下周开始时间
        //下周日
        $after_sunday = date('Y-m-d', strtotime("$now_end + 7 days"));     //下周结束时间

        //获取下周最近的一个休息日
        $near_after_off_day = HrStaffWorkDaysModel::findFirst([
            'conditions' => ' staff_info_id = :staff_info_id:  and date_at >= :after_monday: and date_at <= :after_sunday:',
            'bind'       => [
                'staff_info_id' => $staff_info_id,
                'after_monday'  => $after_monday,
                'after_sunday'  => $after_sunday,
            ],
            'columns'    => 'date_at',
            'order'      => 'date_at',
        ]);

        if ($near_after_off_day && $near_after_off_day = $near_after_off_day->date_at) {
            $current_date_obj   = new DateTime($currentLastDate);
            $after_date_max_obj = new DateTime($near_after_off_day);
            $days               = $current_date_obj->diff($after_date_max_obj)->days;
            if ($days > 7) {
                return false;
            }
        }

        return true;
    }


    public function get_week_days($date, $is_array = 0, $holidays)
    {
        $date_num = date('w', strtotime($date));
        if ($date_num == 0) {
            $date_num = 7;
        }
        $week[] = $date;
        $num    = 0;
        for ($i = $date_num; $i > 1; $i--) {
            $num++;
            $week[] = date('Y-m-d', strtotime("{$date} -{$num} day"));
        }

        $num = 0;
        for ($i = $date_num; $i < 7; $i++) {
            $num++;
            $week[] = date('Y-m-d', strtotime("{$date} +{$num} day"));
        }
        sort($week);

        foreach ($week as $k => $d) {
            if (in_array($d, $holidays)) {
                unset($week[$k]);
            }
        }

        if ($is_array) {
            return $week;
        } else {
            $str_where = "'".implode("','", $week)."'";
            return $str_where;
        }
    }





    //获取对应 公休日
    // holiday_type 1 - rh
    // holiday_type 2 - sh
    public function get_holidays($param)
    {
        $holidayService = new HolidayService();
        return [
            ThailandHolidayModel::HOLIDAY_TYPE_RH => $holidayService->getHoliday([
                'date' => $param['date'],
                'type' => ThailandHolidayModel::HOLIDAY_TYPE_RH,
            ]),
            ThailandHolidayModel::HOLIDAY_TYPE_SH => $holidayService->getHoliday([
                'date' => $param['date'],
                'type' => ThailandHolidayModel::HOLIDAY_TYPE_SH,
            ]),
        ];
    }


    //验证能修改 TruckUpdate 职位的权限 只有菲律宾
    public function checkTruckUpdatePermission($staffIds, $operateId){
        //查配置项 TruckUpdate  看当前操作人是否再配置项 如果在 不限制
        $settingStaff = (new SettingEnvService)->getSetVal('TruckUpdate');
        $settingStaff = str_replace('，', ',', $settingStaff);
        $settingStaff = empty($settingStaff) ? [] : explode(',', $settingStaff);
        if(!empty($settingStaff) && in_array($operateId, $settingStaff)){
            return true;
        }
        //查询操作员工 是否有 truck 职位
        $data = HrStaffInfoModel::find([
            'columns' => 'job_title',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind' => ['ids' => $staffIds],
        ])->toArray();

        if(empty($data)){
            return true;
        }
        $data = array_column($data, 'job_title');
        if(in_array(GlobalEnum::$job_title['truck_driver_night'], $data)){
            return false;
        }
        return true;
    }



    /**
     * @param $staff_id
     * @param $date
     * @param $operateId
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function beforeCancelValidation($staff_id, $date, $operateId): array
    {
        $result = [];

        $operator = $operateId;
        if (empty($staff_id) || empty($date)) {
            $result['code']    = ErrCode::VALIDATE_ERROR;
            $result['message'] = 'wrong param';
            $result['data']    = [];
            return $result;
        }

        $this->checkSelfEdit($operator, $staff_id);
        $flag = $this->checkTruckUpdatePermission([$staff_id], $operator);
        if (!$flag) {
            $result['code']    = ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR;
            $result['message'] = self::$t->_('workday_truck_permission');
            $result['data']    = [];
            return $result;
        }
        //验证员工
        $staffInfo = (new StaffService())->getHrStaffInfo($staff_id);
        if (empty($staffInfo)) {
            $result['code']    = ErrCode::WORK_DAY_CHECK_LEAVE_ERROR;
            $result['message'] = self::$t->_('staff_id_not_found');
            $result['data']    = [];
            return $result;
        }
        //只能取消当天之后的
        $today              = date('Y-m-d');
        $sysService         = rebuildCountryInstance(new SysService());
        $isWorkDayRootId    = $sysService->setCurrentStaffId($operateId)->isWorkDayRootId();
        $canModifyTodayRest = $sysService->setCurrentStaffId($operateId)->canModifyTodayRest()
            && $staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_SIX && $staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_1;
        if (!$isWorkDayRootId) {
            if ($canModifyTodayRest && $date < $today || !$canModifyTodayRest && $date <= $today) {
                $result['code']    = ErrCode::VALIDATE_ERROR;
                $result['message'] = 'can not cancel,wrong date chosen';
                $result['data']    = [];
                return $result;
            }
        }

        //当天有审批通过或者待审批的加班申请，请撤销加班申请后再调整休息日
        $check_overtime = $this->check_overtime($staff_id, $date);
        if (!empty($check_overtime)) {
            $result['code']    = ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR;
            $result['message'] = self::$t->_('workdays_check_overtime_alert_v2',
                ['staff_info_id' => $staff_id, 'name' => $staffInfo['name']]);
            $result['data']    = [];
            return $result;
        }

        //当天有请假申请，限制不能取消休息日
        $check_leave_all = $this->check_leave_all($staff_id, $date);
        if (!empty($check_leave_all)) {
            $result['code']    = ErrCode::WORK_DAY_CHECK_LEAVE_ERROR;
            $result['message'] = self::$t->_('workdays_check_leave_alert_v2',
                ['staff_info_id' => $staff_id, 'name' => $staffInfo['name']]);
            $result['data']    = [];
            return $result;
        }

        //自由轮休 验证取消配置
        if (!$this->isReplace && $staffInfo['week_working_day'] == HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
            [$message, $dateListOption] = (new WorkdaySettingService())->checkOffDayCancel($staffInfo, $date,
                $operator);
            if ($dateListOption) {
                $result['code']            = ErrCode::SUCCESS;
                $result['message']         = 'success';
                $result['data']['code']    = ErrCode::WORK_DAY_CHECK_SETTING_ERROR;
                $result['data']['message'] = $message;
                $result['data']['data']    = $dateListOption;
                return $result;
            }
        }
        return $result;
    }



    /**
     * 替换休息日
     * @param $staff_info_id
     * @param $original_date_at
     * @param $new_date_at
     * @param $operator_id
     * @return bool
     * @throws ValidationException
     */
    public function replace($staff_info_id, $original_date_at, $new_date_at, $operator_id)
    {
        //不在同一周 异常数据
        if (date('oW', strtotime($original_date_at)) != date('oW', strtotime($new_date_at))) {
            throw new ValidationException(self::$t->_('data_error'));
        }

        $db = HrStaffWorkDaysModel::beginTransaction($this);
        try {
            $model = HrStaffWorkDaysModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and date_at = :original_date_at:',
                'bind'       => [
                    'staff_info_id'    => $staff_info_id,
                    'original_date_at' => $original_date_at,
                ],
                'for_update' => true,
            ]);

            if (empty($model)) {
                throw new ValidationException(self::$t->_('target_data_does_no_exist'));
            }
            $this->isReplace = true;
            $res = $this->cancel($staff_info_id, $original_date_at, $operator_id);
            if (!empty($res)) {
                $db->rollback();
                return $res;
            }
            $workdayService = new WorkdayService();
            $workdayService->setSuggestConfirm(0);
            $workdayService->handle([$staff_info_id], [$new_date_at], $operator_id, 0);

            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        $this->staff_set_work_day_send_message($staff_info_id, $original_date_at, 1);//轮休发送消息

        return true;
    }



}