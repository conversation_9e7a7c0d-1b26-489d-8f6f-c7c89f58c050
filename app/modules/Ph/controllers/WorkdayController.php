<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/8/4
 * Time: 2:43 PM
 */

namespace App\Modules\Ph\Controllers;

use App\Library\ErrCode;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Services\DefaultRestDayService;
use App\Services\StaffService;
use App\Models\backyard\SysStoreModel;
use App\Services\SysDepartmentService;
use App\Library\Validation\Validation;
use App\Modules\Ph\Services\WorkdayService;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Services\SysService;
use App\Controllers\WorkdayController as BaseController;
use App\Services\WorkdaySettingService;

class WorkdayController extends BaseController
{


    public $paramIn;
    public $userinfo;
    public $staff_service;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn       = $this->request->get();
        $this->paramIn       = filter_param($this->paramIn);
        $this->staff_service = new StaffService();
        $this->userinfo      = $this->staff_service->get_fbi_user_info($this->user['id']);

        //判断权限写死 参数
        $this->paramIn['sub'] = 1;//默认取直属下级
        $user_position        = $this->userinfo['position_category'] = empty($this->userinfo['position_category']) ? [] : explode(',',
            $this->userinfo['position_category']);
        if (!empty($this->userinfo['position_category'])) {
            //超管或人事  新增 系统管理员和人事专员角色
            if ($this->get_permission($user_position)) {
                $this->paramIn['is_sub'] = 0;
            } else {
                if (in_array(21, $user_position)) {//区域经理
                    $this->paramIn['is_manager'] = 'manager';
                } else {
                    if (in_array(3, $user_position) || in_array(18, $user_position)) {//网点
                        $this->paramIn['pre_store_id'] = $this->userinfo['organization_id'];
                    }
                }
            }
        }
    }


    /**
     * @Token
     * @param $user_position
     * @return bool
     */
    protected function get_permission($user_position)
    {
        //超管 运营经理 人事经理 人事专员  系统管理员  有全部权限
        if (in_array(17, $user_position)
            || in_array(99, $user_position)
//            || in_array(8,$user_position)//运营经理 去掉全部权限 根据所在部门 获取部门以及子部门的员工
            || in_array(14, $user_position)
            || in_array(16, $user_position)
            || in_array(41, $user_position)
        ) {
            return true;
        }
        return false;
    }



    /**
     * @Token
     * @Permission(action='work_day_view')
     */
    public function staff_workdays_listAction()
    {
        $month                  = empty($this->paramIn['month']) ? date('Y-m', time()) : $this->paramIn['month'];
        $param['store_id']      = empty($this->paramIn['store_id']) ? [] : $this->paramIn['store_id'];
        $param['state']      = empty($this->paramIn['state']) ? [] : $this->paramIn['state'];
        $param['area']          = empty($this->paramIn['area']) ? '' : $this->paramIn['area'];
        $param['job_title']     = empty($this->paramIn['job_title']) ? '' : $this->paramIn['job_title'];
        $param['staff_info_id'] = empty($this->paramIn['staff_info_id']) ? '' : $this->paramIn['staff_info_id'];
        //部门页面搜索
        $param['search_department'] = empty($this->paramIn['search_department']) ? '' : $this->paramIn['search_department'];

        $work_type                 = empty($this->paramIn['work_type']) ? '0_0' : $this->paramIn['work_type'];
        $param['week_working_day'] = explode('_', $work_type)[0];
        $param['rest_type']        = explode('_', $work_type)[1];
        $param['hire_type']        = $this->paramIn['hire_type']??[];

        //分页
        $param['start']  = empty($this->paramIn['start']) ? 0 : $this->paramIn['start'];
        $param['length'] = empty($this->paramIn['length']) ? 30 : $this->paramIn['length'];

        //员工信息
        $staff_bll         = new WorkdayService();
        $staff_id          = $this->userinfo['id'];
        $param['operator'] = $staff_id;

        //获符合筛选条件和登录用户可视范围数据权限的员工
        $result = $staff_bll->search_staff_workdays($param, $this->userinfo['id']);
        if (!empty($result['data'])) {
            $param['month'] = $month;
            $result['data'] = $staff_bll->formatList($param, $result['data']);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * @Token
     */
    public function area_listAction()
    {
        $parcel_model = SysStoreModel::$sorting_no_map;
        $return       = [];
        foreach ($parcel_model as $k => $v) {
            $row['code']  = $k;
            $row['value'] = $v;
            $return[]     = $row;
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $return);
    }


    /**
     * @Token
     * 单个人 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_workdaysAction()
    {
        $params['staff_info_id'] = $this->paramIn['staff_info_id'];
        $params['date']          = $this->paramIn['date'];
        $validation              = [
            'staff_info_id' => 'Required|int|>>>:staff_info_id error',
            'date'          => 'Required|Date|>>>:date error',
        ];
        $is_confirm              = $this->paramIn['is_confirm'] ?? 0;
        Validation::validate($params, $validation);

        try {
            $workdayService = new WorkdayService();
            $suggestConfirm = $this->paramIn['is_submit'] ?? 0;//轮休配置验证建议班次 二次确认参数
            $workdayService->setSuggestConfirm($suggestConfirm);
            $workdayService->handle([$params['staff_info_id']], [$params['date']], $this->user['id'], $is_confirm);
        } catch (BusinessException $b) {
            if ($b->getCode() == 10010) {
                return $this->returnJson(1, $b->getMessage(), ['off_day_less_than_6_days' => true, 'param' => 'is_confirm']);//小于6天
            }
            if ($b->getCode() == 10086) {
                return $this->returnJson(1, $b->getMessage(), ['code' => -1,'message' => $b->getMessage(),'data' => ['param' => 'is_submit']]);//小于6天
            }
            throw $b;
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }

    /**
     * @Token
     * 批量 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_batch_workdaysAction()
    {
        $params['staff_info_id'] = $this->paramIn['staff_info_id'];
        $params['date']          = $this->paramIn['date'];
        $validation              = [
            'staff_info_id' => 'Required|Arr|>>>:staff_info_id error',
            'date'          => 'Required|Arr|>>>:date error',
        ];
        Validation::validate($params, $validation);
        $is_confirm = $this->paramIn['is_confirm'] ?? 0;

        $workdayService = new WorkdayService();
        try {
            $suggestConfirm = $this->paramIn['is_submit'] ?? 0;//轮休配置验证建议班次 二次确认参数
            $workdayService->setSuggestConfirm($suggestConfirm);
            $workdayService->setSrc(2);
            $workdayService->handle($params['staff_info_id'], $params['date'], $this->user['id'], $is_confirm);
        } catch (BusinessException $b) {
            if ($b->getCode() == 10010) {
                return $this->returnJson(1, $b->getMessage(), ['off_day_less_than_6_days' => true, 'param' => 'is_confirm']);//小于6天
            }
            if ($b->getCode() == 10086) {
                return $this->returnJson(1, $b->getMessage(), ['code' => -1,'message' => $b->getMessage(),'data' => ['param' => 'is_submit']]);//小于6天
            }
            throw $b;
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }


    /**
     * @Token
     * @Permission(action='work_day_setting')
     * @throws \Exception
     */
    public function cancel_shiftAction()
    {
        $staff_id = $this->paramIn['staff_info_id'];
        $date     = $this->paramIn['date'];
        $operator = $this->paramIn['operator'];
        //能不能修改自己的轮休
        $workday_bll = new WorkdayService();
        $res =  $workday_bll->cancel($staff_id, $date, $operator);

        if (is_array($res) && !empty($res)) {
            return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
        }
        //发送消息 取消
        $workday_bll->staff_set_work_day_send_message($staff_id, $date, 1);//轮休发送消息
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }


    //获取 是否是 部门负责人
    protected function is_manager($staff_id)
    {
        $model = new SysDepartmentService();
        $res   = $model->get_dep_manager($staff_id);
        if (empty($res)) {
            return false;
        }

        $dep_ids = array_column($res, 'id');//负责的所有部门
        return $dep_ids;
    }

    /**
     * 五天班&六天班轮休 设置默认休息日
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @Permission(action='work_day_setting')
     */
    public function editDefaultRestDayDateAction(){
        $staffId = $this->paramIn['staff_info_id'];
        $defaultRestDayDate = $this->paramIn['default_rest_day_date'];
        if (!is_array($defaultRestDayDate)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        (new DefaultRestDayService())->editDefaultRestDayDate($staffId,$defaultRestDayDate, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

    /**
     * @Permission(action='work_day_setting')
     *
     */
    public function replaceAction()
    {
        $params['staff_info_id']    = $this->paramIn['staff_info_id'];
        $params['original_date_at'] = $this->paramIn['original_date_at'];
        $params['new_date_at']      = $this->paramIn['new_date_at'];
        $validation                 = [
            'staff_info_id'    => 'Required|Int|>>>:staff_info_id error',
            'original_date_at' => 'Required|Date|>>>:original_date_at error',
            'new_date_at'      => 'Required|Date|>>>:new_date_at error',
        ];
        Validation::validate($params, $validation);

        $res = (new WorkdayService())->replace($params['staff_info_id'],$params['original_date_at'],$params['new_date_at'],$this->user['id']);

        if (is_array($res) && !empty($res)) {
            return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'success');

    }



}