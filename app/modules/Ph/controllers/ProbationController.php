<?php
/**
 * Author: Bruce
 * Date  : 2022-04-08 15:39
 * Description:
 */

namespace App\Modules\Ph\Controllers;

use App\Controllers\ProbationController as BaseProbationController;
use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\ExcelService;
use App\Services\HrProbationContractService;
use App\Services\HrProbationContractThreeService;
use App\Services\ProbationService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ProbationController extends BaseProbationController
{
    /**
     * 月薪制合同工列表
     * @Token
     * @Permission(action='probation.contract_list')
     * @return Response|ResponseInterface
     * @throws ValidationException|\App\Library\Validation\ValidationException
     * @throws \Exception
     */
    public function contractListAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);
        if (!isset($params['is_sub_department'])) {
            $params['is_sub_department'] = 0;
        }
        if (is_numeric($this->request->get('first_status'))) {
            $params['first_status'] = $this->request->get('first_status');
        }

        Validation::validate($params, ProbationService::$probation_list_validate);

        $params['staff_info_id'] = $this->user['id'];
        $probationService        = new HrProbationContractService();
        $list                    = $probationService->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $list);
    }

    /**
     * 激活
     * @Token
     * @Permission(action='probation.contract_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function activeAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
        ]);
        $params['operator_id'] = $this->user['id'];
        $probationService      = new HrProbationContractService();
        $result                = $probationService->active($params);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $result);
    }
    /**
     * 查看详情
     * @Token
     * @Permission(action='probation.contract_detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function contractDetailAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
        ]);
        $probationService = new ProbationService();
        $data = $probationService->getDetail($params['staff_info_id']);
        return $this->returnJson(ErrCode::SUCCESS, 'ok',$data);
    }
    /**
     * 编辑详情
     * @Token
     * @Permission(action='probation.contract_edit-detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function contractEditDetailAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
        ]);
        $probationService = new ProbationService();
        $data             = $probationService->editDetail($params['staff_info_id']);
        if (is_string($data)) {
            $this->returnJson(ErrCode::VALIDATE_ERROR, $data, []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 保存得分记录 (编辑)
     * @Token
     * @Permission(action='probation.contract_edit-detail')
     * @return Response|ResponseInterface
     * @throws ValidationException|\App\Library\Exception\BusinessException
     */
    public function contractStoreAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);
        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
            'score'         => 'Required',
        ]);
        $probationId            = (new HrProbationContractService())->getExpiredAndProbationaryPeriod($params['staff_info_id']);
        $params['operate_id']   = $this->user['id'];
        $params['remark']       = $params['remark'] ?? '';
        $params['pic']          = $params['pic'] ?? '';
        $params['job_content']  = $params['job_content'] ?? '';
        $params['probation_id'] = $probationId;
        $rpc                    = (new ApiClient('by', '', 'storeProbationAuditByHCM', $this->locale));
        $rpc->withParam($params);
        $data = $rpc->execute();
        if (isset($data['code']) && $data['code'] == 1) {
            return $this->returnJson(ErrCode::SUCCESS, $data['msg'] ?? 'ok', 'update success');
        }
        return $this->returnJson(ErrCode::VALIDATE_ERROR, $data['msg'] ?? 'error', $data['data'] ?? '');
    }

    /**
     * 导出月薪制合同工Excel
     * @Token
     * @Permission(action='probation.contract_export')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function contractExportAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        // 当前端传递0 时 会被array_filter 过滤掉
        if (!isset($params['is_sub_department'])) {
            $params['is_sub_department'] = 0;
        }
        if (is_numeric($this->request->get('first_status'))) {
            $params['first_status'] = $this->request->get('first_status');
        }

        unset(ProbationService::$probation_list_validate['page'], ProbationService::$probation_list_validate['pagesize']);
        Validation::validate($params, ProbationService::$probation_list_validate);
        $params['staff_info_id'] = $this->user['id'];

        $params['file_name'] = uniqid('probation-' . date('YmdHis')) . '.xlsx';
        $params['lang']      = $this->locale;

        $action_name = 'probation' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'exportContractProbation';
        $result      = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, $params['file_name']);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 月薪制合同工下载评分
     * @Token
     * @Permission(action='probation.contract_download')
     * @return void
     * @throws ValidationException
     * @throws \Exception
     */
    public function contractDownloadAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);
        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
        ]);
        $probationService = new \App\Modules\Ph\Services\ProbationService();
        $probationService->download($params['staff_info_id']);
        exit;
    }


    /**
     * 合同工v3-列表
     * @Token
     * @Permission(action='probation.contract_v3_list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \Exception
     */
    public function contractV3ListAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);
        if (is_numeric($this->request->get('first_status'))) {
            $params['first_status'] = $this->request->get('first_status');
        }
        if (is_numeric($this->request->get('second_status'))) {
            $params['second_status'] = $this->request->get('second_status');
        }
        // 当前端传递0 时 会被array_filter 过滤掉
        if (!isset($params['is_sub_department'])) {
            $params['is_sub_department'] = 0;
        }

        Validation::validate($params, ProbationService::$probation_list_validate);

        $params['staff_info_id'] = $this->user['id'];
        $probationService        = new HrProbationContractThreeService();
        $list                    = $probationService->getList($params);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $list);
    }

    /**
     * 合同工v3-查看详情
     * @Token
     * @Permission(action='probation.contract_v3_detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function contractV3DetailAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
        ]);
        $probationService = new \App\Modules\Ph\Services\ProbationService();
        $data             = $probationService->getDetail($params['staff_info_id']);
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 合同工v3-下载试用期考核PDF文件
     * @Token
     * @Permission(action='probation.contract_v3_download')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface|void
     * @throws ValidationException
     * @throws \Exception
     */
    public function contractV3DownloadAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);
        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
        ]);
        (new HrProbationContractThreeService())->download($params['staff_info_id']);
        exit;
    }

    /**
     * 合同工v3-编辑详情
     * @Token
     * @Permission(action='probation.contract_v3_edit-detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws \Exception
     */
    public function contractV3EditDetailAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);

        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
        ]);
        $probationService = new ProbationService();
        $data             = $probationService->editDetail($params['staff_info_id']);
        if (is_string($data)) {
            $this->returnJson(ErrCode::VALIDATE_ERROR, $data, []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data);
    }

    /**
     * 合同工v3-保存得分记录
     * @Token
     * @Permission(action='probation.contract_v3_edit-detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function contractNewStoreAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);
        Validation::validate($params, [
            'staff_info_id' => 'Required|IntGe:0',
            'score'         => 'Required',
        ]);


        $probationService = new HrProbationContractThreeService();
        $data             = $probationService->auditBi($params['staff_info_id'], $params['score'], $params['pic'] ?? "",
            $params['remark'] ?? "", $this->user['id'], $params['job_content'] ?? "");
        if (!isset($data['code']) || $data['code'] != ErrCode::SUCCESS) {
            $this->returnJson(ErrCode::VALIDATE_ERROR, $data['message'] ?? '', []);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'ok', $data['data'] ?? '');
    }

    /**
     * 导出Excel
     * @Token
     * @Permission(action='probation.contract_v3_export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function contractV3exportAction()
    {
        $params = $this->convertParams($this->request->get());
        $params = array_filter($params);
        if (is_numeric($this->request->get('first_status'))) {
            $params['first_status'] = $this->request->get('first_status');
        }
        if (is_numeric($this->request->get('second_status'))) {
            $params['second_status'] = $this->request->get('second_status');
        }
        // 当前端传递0 时 会被array_filter 过滤掉
        if (!isset($params['is_sub_department'])) {
            $params['is_sub_department'] = 0;
        }

        // 数据导出无需翻页参数
        unset(ProbationService::$probation_list_validate['page'], ProbationService::$probation_list_validate['pagesize']);
        Validation::validate($params, ProbationService::$probation_list_validate);
        $params['staff_info_id'] = $this->user['id'];

        $params['lang'] = $this->locale;
        $action_name    = 'probation' . HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR . 'exportContractV3Probation';
        $result         = (new ExcelService())->insertTask($this->user['id'], $action_name, $params,
            HcmExcelTackModel::TYPE_DEFAULT, uniqid('probation-' . date('Ymd')) . '.xlsx');
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }
}