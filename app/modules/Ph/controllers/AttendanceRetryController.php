<?php

namespace App\Modules\Ph\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HcmExcelTackModel;
use App\Services\AttendanceRetryService;
use App\Services\ExcelService;
use App\Controllers\AttendanceRetryController as BaseAttendanceRetryController;

class AttendanceRetryController extends BaseAttendanceRetryController
{
    public $data_type_all = [
        0  => ['key' => 0, 'title' => "Salary Attendance Strict"],
        1  => ['key' => 1, 'title' => "Incentive Attendance"],
        2  => ['key' => 2, 'title' => "PD Horizontal"],
        3  => ['key' => 3, 'title' => "PD Detail"],
        4  => ['key' => 4, 'title' => "Hub Full Attendance"],
        5  => ['key' => 5, 'title' => "Salary Attendance Kind"],
        6  => ['key' => 6, 'title' => "Leave Type Sum"],
        7  => ['key' => 7, 'title' => "Incentive Hold List"],
        8  => ['key' => 8, 'title' => "salary Hold List"],
        9  => ['key' => 9, 'title' => "Fleet Driver Bonus"],
        10 => ['key' => 10, 'title' => "Hub OCW Attendance"],
    ];


    /**
	 * @description:获取相关静态资源列表
	 *
	 * @param null
	 * @Token
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/13 20:41
	 */
	public function getStaticListAction()
	{
        $data = [
            'month'                      => date('Y-m'),//月份
            'month_type'                 => [['key'=>1,'title'=>'First'],['key'=>2,'title'=>'Second']],
            'data_type_calculate_attend' => [
                 ['key'=>0,'title'=>'Salary Attendance Strict'],
                 ['key'=>1,'title'=>'Salary Attendance Kind'],
                 ['key'=>2,'title'=>'Incentive Attendance'],
                 ['key'=>3,'title'=>'Salary Intermediate Table Data'],
            ],
            'data_type_all'              => [
                 ['key'=>0,'title'=>"Salary Attendance Strict"],
                 ['key'=>1,'title'=>"Incentive Attendance"],
                 //['key'=>2,'title'=>"PD Horizontal"],
                 ['key'=>3,'title'=>"PD Detail"],
                 ['key'=>4,'title'=>"Hub Full Attendance"],
                 ['key'=>5,'title'=>"Salary Attendance Kind"],
                 ['key'=>6,'title'=>"Leave Type Sum"],
                 ['key'=>7,'title'=>"Incentive Hold List"],
                 ['key'=>8,'title'=>"salary Hold List"],
                 ['key'=>9,'title'=>"Fleet Driver Bonus"],
                 ['key'=>10,'title'=>"Hub OCW Attendance"],
                 ['key'=>11,'title'=>"FFM OCW Attendance"],
            ],
            'date_type_special'          => [
                 ['key'=>0,'title'=>"Salary Attendance Strict"],
                 ['key'=>1,'title'=>"Incentive Attendance"],
                 //['key'=>2,'title'=>"PD Horizontal"],
                 ['key'=>3,'title'=>"PD Detail"],
                 ['key'=>4,'title'=>"Hub Full Attendance"],
                 ['key'=>5,'title'=>"Salary Attendance Kind"],
            ],

        ];
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
	}
	
	
	/**
	 * @description:考勤重算 Re Calculate Attend
	 *
	 * @param string month   月份
	 * @param string re_cal_type  类型
	 * @param string staff_id    工号 ,号分割
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/14 14:13
	 * @Token
	 * @Permission(action='admin.attendance_retry.re_cal_attend_ajax')
     * @throws ValidationException
	 */
	
	//考勤重算
	public function re_cal_attend_ajaxAction()
    {
        $params['month'] = $this->request->get('month', 'trim', date('Y-m'));
        $params['re_cal_type'] = $this->request->get('re_cal_type', 'int');
        $params['staff_id'] = $this->request->get('staff_id', 'trim');
        $params['month_type'] = $this->request->get('month_type', 'trim', 1);
        $params['edit_staff_info_id'] = $this->user['id'];
        $params['staff_id'] = implode(',',clearSpecialChar($params['staff_id']));
        $validation = [
            'month' => 'Required',
            're_cal_type' => 'Required|IntIn:0,3|>>>: Data type error!',
            'staff_id' => 'Required|>>>:Staff Info Data Cannot be empty!',
        ];

        Validation::validate((array)$params, $validation);
        //本地化调整
        if ($params['month_type'] == 1) {
            $params['salary_start'] = date('Y-m-01', strtotime($params['month']));
            $params['salary_end'] = date('Y-m-15', strtotime($params['month']));
        } else {
            $params['salary_start'] = date('Y-m-16', strtotime($params['month']));
            $params['salary_end'] = date('Y-m-d', strtotime(date('Y-m-01', strtotime($params['month'])).' +1 month -1 day'));
        }
        $result = (new AttendanceRetryService())->re_cal_attend_ajax($params);
        return $this->returnJson($result['code'], $result['message'], []);
	}

    /**
     * @description:All Data 导出  和 Special Data
     *
     * @param null
     *
     * @Token
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2021/10/18 10:21
     * @Permission(action='admin.attendance_retry.attendance_export_all')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function attendance_export_allAction()
    {
        $params              = $this->request->get();
        $params['month']     = $params['month'] ?? date('Y-m');
        $params['data_type'] = $params['data_type'] ?? 0;
        $params['staff_id']  = $params['staff_id'] ?? '';
        $params['month_type'] = $params['month_type'] ?? 1;
        $params['lang']       = $this->locale;
        $params['staff_id'] = implode(',',clearSpecialChar($params['staff_id']));
        //获取当前登录人
        $data_type_all = $this->data_type_all;
        $file_name     = 'attendance' . '_' . $this->locale . $data_type_all[$params['data_type']]['title'] . date('Ymd-His') . '.xlsx';
        $file_name = str_replace(" ","_",$file_name);
        $action_name   = 'backyardattendance'.HcmExcelTackModel::ACTION_NAME_SPACE_SEPARATOR.'dow_excel';
        //入 task 表 走队列导出
        $result = (new ExcelService())->insertTask($this->user['id'], $action_name, $params, 2, $file_name,false);
        if ($result['code'] != ErrCode::SUCCESS) {
            return $this->returnJson($result['code'], $result['message'], []);
        }
        return $this->returnJson($result['code'], $result['message'], []);
    }
}
