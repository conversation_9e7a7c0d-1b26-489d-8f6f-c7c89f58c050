<?php

namespace App\Modules\My\Services;

use App\Library\Enums;
use App\Library\Enums\StaffEnums;
use App\Library\ErrCode;
use App\Library\FlashOss;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AssetsInventoryStoresModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrProbationAuditModel;
use App\Models\backyard\HrProbationMessageModel;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffInfoReadModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\StaffPayrollCompanyInfoModel;
use App\Models\BackYard\StaffResignModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\fle\StaffInfoPositionModel;
use App\Modules\My\library\Enums\enums as myEnums;
use App\Modules\My\library\Enums\SalaryEnums;
use App\Services\DictionaryService;
use App\Services\ProbationService as BaseProbationService;
use App\Services\SysStoreService;
use Mpdf\Mpdf;
use Phalcon\Mvc\Model\Query\Builder;

class ProbationService extends BaseProbationService
{
	//职级分界线
	public $job_grade = 17;    //职级分界线  <= 14
	
    public $probation_first_deadline_start = 30; //试用期第一次评估期限开始
    public $probation_first_deadline_end = 40;   //试用期第一次评估期限结束
    public $probation_second_deadline_start = 75;  //试用期第二次评估期限开始
    public $probation_second_deadline_end = 85;    //试用期第二次评估期限结束
    public $probation_second_delay_deadline_start = 91;  //试用期转正时间
    public $probation_second_delay_deadline_end = 100;    //延期时间(默认往后延期 10 天)
	public $formal_days = 91; //转正日期
	//这里是职级大于 17 的参数
	public $probation_first_deadline_start_two = 75; //试用期第一次评估期限开始
	public $probation_first_deadline_end_two = 85;   //试用期第一次评估期限结束
	public $probation_second_deadline_start_two = 165;  //试用期第二次评估期限开始
	public $probation_second_deadline_end_two= 175;    //试用期第二次评估期限结束
	public $probation_second_delay_deadline_start_two = 181;  //试用期转正时间
	public $probation_second_delay_deadline_end_two = 190;    //延期时间(默认往后延期 10 天)
	public $formal_days_two = 181; //转正日期
	
	public $cur_level_2 = 2; //阶段2
	
	
	/**
	 * @throws \Exception
	 */
	public function getList($params)
	{
		//hr_staff_info bi 从库
		//todo::迁移参考：BI：app/BLL/StaffSearchBLL.php method:staffList
		$data = [];
		$builder = new Builder();
        $builder->from(['hsi' => HrStaffInfoReadModel::class])
            ->columns("count(1) as total")
            ->leftJoin(HrProbationModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hp');
        if (!empty($params['is_non_front_line_probation']) && $params['is_non_front_line_probation'] == 1){
            $this->setnonFrontLineFlag(true);
        }else{
            $this->setnonFrontLineFlag(false);
        }
        $this->makeNonFrontLineBuilder($builder, $params);
        $builder->andWhere("hsi.formal = 1 and hsi.hire_type = 1 and hsi.is_sub_staff = 0");
		//直接上级
		if (!empty($params['manage_staff_id'])) {
			$builder->andWhere('hsi.manger = :manage_staff_id:', ['manage_staff_id' => intval($params['manage_staff_id'])]);
		}
		
		//试用期状态
		if (!empty($params['status'])) {
			$tmp = $params['status'];
			//没有进入的时候<40的时候，也是试用期
            if (in_array(StaffEnums::STATUS_PROBATION, $tmp)) {
                $builder->andWhere('hp.status IN ({status:array}) OR hp.status IS NULL', ['status' => $params['status']]);
				//只有试用期的时候有用，是否超时，1是,2否的时候只能搜出来待评估的
				if (!empty($params['is_timeout'])) {
					//超过第二阶段结束时间
					//小于等于 14 级的
					$second_end = $this->getDateByDays(gmdate("Y-m-d", time() + ($this->timeOffset) * 3600), $this->probation_second_deadline_end);
					//大于 14 级的
					$second_end_two = $this->getDateByDays(gmdate("Y-m-d", time() + ($this->timeOffset) * 3600), $this->probation_second_deadline_end_two);
					//超过第二阶段结束时间
					if ($params['is_timeout'] == 1) {
						$builder->andWhere('((hsi.hire_date< :second_end: and hsi.job_title_grade_v2 <= :job_grade:) or (hsi.hire_date< :second_end_two: and hsi.job_title_grade_v2 > :job_grade: ))', ['second_end' => $second_end,'job_grade'=>$this->job_grade,'second_end_two'=>$second_end_two]);
					} else {
						//未超过过第二阶段结束时间
						$builder->andWhere('((hsi.hire_date>= :second_end: and hsi.job_title_grade_v2 <= :job_grade:) or (hsi.hire_date>= :second_end_two: and hsi.job_title_grade_v2 > :job_grade: ))', ['second_end' => $second_end,'job_grade'=>$this->job_grade,'second_end_two'=>$second_end_two]);
					}
				}
			} else {
                $builder->inWhere('hp.status', $params['status']);
			}
		}
		
		//二级部门id
		if (!empty($params['node_department_id'])) {
			$builder->andWhere('hsi.node_department_id = :node_department_id:', ['node_department_id' => intval($params['node_department_id'])]);
		}
		
		
		//第一次评估期限
		if (!empty($params['first_date_begin']) && !empty($params['first_date_end'])) {
			$first_begin = $this->getDateByDays($params['first_date_begin'], $this->probation_first_deadline_start).' 00:00:00';
			$first_end = $this->getDateByDays($params['first_date_end'], $this->probation_first_deadline_start).' 23:59:59';
			//大于 14 级的评估期限
			$first_begin_two = $this->getDateByDays($params['first_date_begin'], $this->probation_first_deadline_start_two).' 00:00:00';
			$first_end_two = $this->getDateByDays($params['first_date_end'], $this->probation_first_deadline_start_two).' 23:59:59';
			$builder->andWhere(' ((hsi.hire_date >= :first_date_begin: and hsi.hire_date <= :first_date_end: and hsi.job_title_grade_v2 <= :job_grade: ) or (hsi.hire_date >= :first_date_begin_two: and hsi.hire_date <= :first_date_end_two: and hsi.job_title_grade_v2 > :job_grade:))', ['first_date_begin' => $first_begin,'first_date_end'=>$first_end,'job_grade','job_grade'=>$this->job_grade,'first_date_begin_two'=>$first_begin_two,'first_date_end_two'=>$first_end_two]);
			
		}
		
		//第二次评估期限
		if (!empty($params['second_date_begin']) && !empty($params['second_date_end'])) {
			$second_begin = $this->getDateByDays($params['second_date_begin'], $this->probation_second_deadline_start).' 00:00:00';
			$second_end = $this->getDateByDays($params['second_date_end'], $this->probation_second_deadline_start).' 23:59:59';
			//大于 14 级的评估期限
			$second_begin_two = $this->getDateByDays($params['second_date_begin'], $this->probation_second_deadline_start_two).' 00:00:00';
			$second_end_two = $this->getDateByDays($params['second_date_end'], $this->probation_second_deadline_start_two).' 23:59:59';
			
			$builder->andWhere(' ((hsi.hire_date >= :first_date_begin: and hsi.hire_date <= :first_date_end: and hsi.job_title_grade_v2 <= :job_grade: ) or (hsi.hire_date >= :first_date_begin_two: and hsi.hire_date <= :first_date_end_two: and hsi.job_title_grade_v2 > :job_grade:))', ['first_date_begin' => $second_begin,'first_date_end'=>$second_end,'job_grade','job_grade'=>$this->job_grade,'first_date_begin_two'=>$second_begin_two,'first_date_end_two'=>$second_end_two]);
		}
		
		//职位
		if (!empty($params['job_title'])) {
			//$builder->andWhere('hsi.job_title= :job_title:', ['job_title' => intval($params['job_title'])]);
            $builder->inWhere('hsi.job_title', $params['job_title']);
		}
		
		if (isset($params['name']) && $params['name']) {
			$builder->andWhere('hsi.name like :name: or hsi.staff_info_id like :name: or hsi.emp_id like :name: or hsi.name_en like :name:', ['name' => $params['name']]);
		}
        if (isset($params['staff_id']) && $params['staff_id']) {
            $builder->andWhere('hsi.staff_info_id = :staff_id:', ['staff_id' => $params['staff_id']]);
        }
        if (isset($params['name_en']) && $params['name_en']) {
            $builder->andWhere('hsi.name_en like :name_en:', ['name_en' => '%'.$params['name_en'].'%']);
        }
        if (isset($params['staff_name']) && $params['staff_name']) {
            $builder->andWhere('hsi.name like :staff_name:', ['staff_name' => '%'.$params['staff_name'].'%']);
        }
		if (isset($params['hire_date_begin']) && $params['hire_date_begin'] != '0000-00-00 00:00:00') {
			$builder->andWhere('hsi.hire_date >= :hire_date_begin:', ['hire_date_begin' => $params['hire_date_begin']]);
		}
		if (isset($params['hire_date_end']) && $params['hire_date_end'] != '0000-00-00 00:00:00') {
			$builder->andWhere('hsi.hire_date <= :hire_date_end:', ['hire_date_end' => $params['hire_date_end']]);
		}
		
		if (!empty($params['leave_source'])) {
			$builder->andWhere('hsi.leave_source= :leave_source:', ['leave_source' => $params['leave_source']]);
		}
		
		if (isset($params['leave_date_begin']) && $params['leave_date_begin'] != '0000-00-00 00:00:00'
		    &&
		    isset($params['leave_date_end']) && $params['leave_date_end'] != '0000-00-00 00:00:00'
		) {
			$results = StaffResignModel::query()->columns(['distinct submitter_id'])
			                           ->where('leave_date >= :leave_date_begin:', ['leave_date_begin' => $params['leave_date_begin']])
			                           ->andWhere('leave_date <= :leave_date_end:', ['leave_date_end' => $params['leave_date_end']])
			                           ->execute()
			                           ->toArray();
			$staffInfoIds = array_filter(array_column($results, 'submitter_id'));
			
			if ($staffInfoIds) {
				$staffInfoIdsIm = implode(",", $staffInfoIds);
				$builder->andWhere('hsi.staff_info_id in (:ids:) or hsi.leave_date >= :leave_date_begin: and hsi.leave_date <= :leave_date_end:',
				                   ['ids' => $staffInfoIdsIm, 'leave_date_begin' => $params['leave_date_begin'], 'leave_date_end' => $params['leave_date_end']]);
			}
		}

		if (isset($params['state']) && $params['state']) {
            $tmp_sql = [];
            if(in_array(4, $params['state'])){
                $tmp_sql[] = '( hsi.state = 1 and hsi.wait_leave_state  = 1 )';
            }
            if(in_array(Enums::HRIS_WORKING_STATE_1,$params['state'])){
                $tmp_sql[] = '( hsi.state = '.Enums::HRIS_WORKING_STATE_1 .' and hsi.wait_leave_state  = 0) ';
            }
            if(in_array(Enums::HRIS_WORKING_STATE_2,$params['state'])){
                $tmp_sql[] = 'hsi.state = '.Enums::HRIS_WORKING_STATE_2;
            }
            if(in_array(Enums::HRIS_WORKING_STATE_3,$params['state'])){
                $tmp_sql[] = 'hsi.state = '.Enums::HRIS_WORKING_STATE_3;
            }
            $_sql = implode(' or ',$tmp_sql);
            $builder->andWhere($_sql);
		}
		if (isset($params['store']) && $params['store']) {
			//$builder->andWhere('hsi.sys_store_id = :store:', ['store' => $params['store']]);
            $builder->inWhere('hsi.sys_store_id',$params['store']);
		}
		if (isset($params['stores']) && $params['stores']) {
			$stores = array_filter($params['stores'], function ($item) {
				return strval($item);
			});
			$builder->inWhere('hsi.sys_store_id', $stores);
		}
		
		if (isset($params['department']) && $params['department']) {
			//试用期管理 查询当前部门及自部门
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere('hsi.node_department_id', $deptIds);
            }
		}

        // 所属区域
		if (isset($params['store_area_id']) && $params['store_area_id']) {
			//$storeIds = $this->getStoresByArea($params['store_area_id']);
            $storeIds = call_user_func_array([$this,'getStoresByArea'], $params['store_area_id']);
			if ($storeIds) {
				$builder->inWhere('hsi.sys_store_id', $storeIds);
			}
		}
        //工作所在国家
        if(isset($params['working_country']) && $params['working_country']){
            $builder->inWhere('hsi.working_country', array_values($params['working_country']));
        }
        //合同公司
        if (!empty($params['contract_company_ids']) && is_array($params['contract_company_ids'])) {
            $builder->andWhere('hsi.contract_company_id in ({contract_company_ids:array})',['contract_company_ids' => $params['contract_company_ids']]);
        }
        if (!empty($params['first_audit_status']) && is_array($params['first_audit_status'])) {
            $condition = 'hp.first_audit_status in ({first_audit_status:array})';
            if (in_array(HrProbationModel::FIRST_AUDIT_STATUS_WAIT,$params['first_audit_status'])) {
                $condition .= '  or hp.staff_info_id is null';
            }
            $builder->andWHere($condition,['first_audit_status' => $params['first_audit_status']]);
        }
        if (!empty($params['second_audit_status']) && is_array($params['second_audit_status'])) {
            $condition = 'hp.second_audit_status in ({second_audit_status:array})';
            if (in_array(HrProbationModel::SECOND_AUDIT_STATUS_WAIT,$params['second_audit_status'])) {
                $condition .= '  or hp.staff_info_id is null';
            }
            $builder->andWHere($condition,['second_audit_status' => $params['second_audit_status']]);
        }
        if (isset($params['first_status']) && in_array($params['first_status'],HrProbationModel::$passMap)) {
            $builder->andWhere('hp.first_status=:first_status:',['first_status' => $params['first_status']]);
        }
        if (isset($params['second_status']) && in_array($params['second_status'],HrProbationModel::$passMap)) {
            $builder->andWhere('hp.second_status=:second_status:',['second_status' => $params['second_status']]);
        }
        $builder = $this->checkStaffPermission($builder, $params);
        if (false === $builder) {
            return ['page_count' => 0, 'rows' => []];
        }

        //查总数
        $pageCount = 0;
        if (!isset($params['is_export'])) {
//            print_r($builder->getQuery()->getSql());die;
            $totalInfo = $builder->getQuery()->getSingleResult();
            $pageCount = intval($totalInfo->total);
            if ($pageCount === 0) {
                return ['page_count' => 0, 'rows' => []];
            }
        }

        $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.name_en',
            'hsi.job_title',
            'hsi.sys_department_id',
            'hsi.node_department_id',
            'hsi.sys_store_id',
            'hsi.hire_date',
            'hsi.state',
            'hsi.wait_leave_state',
            'hsi.leave_date',
            'hsi.job_title_level',
            'hsi.identity',
            'hsi.mobile_company',
            'hsi.working_country',
            'hsi.manger as manage_staff_id',
            'hsi.contract_company_id',
            'hp.status as probation_status',
            'hp.first_score',
            'hp.second_score',
            'hp.formal_at',
            'hp.remark',
            'hp.cur_level',
            'hp.is_delay',
            'hp.mark',
            'hsi.job_title_grade_v2',
            'hp.first_audit_status',
            'hp.first_status',
            'hp.second_audit_status',
            'hp.second_status',
            'hp.result_notification',
            'hp.first_evaluate_start',
            'hp.first_evaluate_end',
            'hp.second_evaluate_start',
            'hp.second_evaluate_end',
            'hp.probation_channel_type',
        ]);
		$builder->orderBy('hsi.hire_date');
		
        $params['page'] = $params['page'] ?? 1;
        $params['pagesize'] = $params['pagesize'] ?? 50;
        $offset = ($params['page'] - 1) * $params['pagesize'];
        $builder->limit($params['pagesize'], $offset);
        
		$data = $builder->getQuery()->execute()->toArray();
		if ($data) {
			$jobIds = array_unique(array_filter(array_column($data, 'job_title')));
			$jobIds = array_map(function ($item) {
				return intval($item);
			}, $jobIds);
			$jobTitle = $jobIds ? HrJobTitleModel::query()->inWhere('id', $jobIds)->execute()->toArray() : [];
			$jobTitle = array_column($jobTitle, null, 'id');
			//$sysDepartments = $this->sysDepartmentList();
            // 拿到所有部门信息
            $nodeDepartmentId = array_column($data,'node_department_id');
            $sysDepartmentId = array_column($data,'sys_department_id');
            $sysDepartments = (new \App\Services\SysDepartmentService())->getAllDepartmentByIds($nodeDepartmentId, $sysDepartmentId);
			$staffId = array_unique(array_filter(array_column($data, 'staff_info_id')));
			$staffApproveStateArr = $this->getApproveState($staffId);//员工对应审核状态，[staff_id]=state
		}
		
			$manage_staffs = [];
			$manage_high_staff_map = [];
			$manage_ids = array_values(array_unique(array_filter(array_column($data, 'manage_staff_id'))));
			//总有特殊字符，会有问题。
			foreach ($manage_ids as $k => $v) {
				$manage_ids[$k] = intval($v);
			}
			
			//如果导出的话+上上级电话
			if ($manage_ids && isset($params['is_export'])) {
				$t_arr = HrStaffItemsModel::query()->columns(['value', 'staff_info_id'])->inWhere('staff_info_id', $manage_ids)->where('item=\'MANGER\'')->execute()->toArray();
				$t_ids = array_column($t_arr, "value");
				
				//[manager_id] =>上级id
				$manage_high_staff_map = array_column($t_arr, "value", "staff_info_id");
				
				$manage_ids = array_merge($manage_ids, $t_ids);
				$manage_ids = array_values(array_unique(array_filter($manage_ids)));
			}
			if ($manage_ids) {
				$manage_staffs = $manage_ids ? HrStaffInfoReadModel::query()->columns(['staff_info_id', 'name', 'mobile'])->inWhere('staff_info_id', $manage_ids)->execute()->toArray() : [];
				$manage_staffs = array_column($manage_staffs, null, "staff_info_id");
			}
			
			
			$store_ids = array_values(array_unique(array_filter(array_column($data, "sys_store_id"))));
			
			$pieceArr = [];
			if ($store_ids) {
				$storeArr = (new SysStoreService())->getStoreListByIds($store_ids);
				$pieceArr = array_column($storeArr, "piece_name", "id");
			}
		
		$return = ['page_count' => intval($pageCount), 'rows' => []];
		$isInventories = [];
		if (isset($params['userinfo']) && $params['userinfo']) {
			// 查询是否是特殊的角色
			$positionsModel = StaffInfoPositionModel::findFirst([
				                                                    'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ',
				                                                    'bind' => ['staff_id' => $params['userinfo']['id'], 'positions' => [
					                                                    Enums::ROLES_SYSTEM_MANAGER_ID, // 系统管理员
					                                                    Enums::ROLES_SUPER_MANAGER_ID, // 超级管理员
					                                                    Enums::ROLES_OUTLET_SUPERVISOR_ID, // 网点主管
					                                                    Enums::ROLES_REGIONAL_MANAGER_ID, // 区域经理
				                                                    ]]
			                                                    ]);
			
			// 查询网点是否打开了
			$sysStoreIds = array_values(array_unique((array_column($data, 'sys_store_id'))));
			$wmsInventories = $sysStoreIds ? AssetsInventoryStoresModel::find([
				                                                                  'conditions' => ' sys_store_id in ({store_ids:array})',
				                                                                  'bind' => ['store_ids' => $sysStoreIds]
			                                                                  ])->toArray() : [];
			foreach ($wmsInventories as $inventory) {
				$isInventories[$inventory['sys_store_id']] = $inventory;
			}
		}

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        $probation_score_enumerate = (new SysService())->probation_score_enumerate();
        $probation_score_enumerate = array_column($probation_score_enumerate, 'label', 'value');

		//翻转 key value;
        $sorting_no = array_flip(myEnums::$store_sorting_no_select);
        $companyFind = StaffPayrollCompanyInfoModel::find([
            'columns' => 'company_id,company_short_name',
        ])->toArray();
        $companyMap = array_column($companyFind, 'company_short_name', 'company_id');
		foreach ($data as $item) {

            $job_title_name = isset($jobTitle[$item['job_title']]) ? $jobTitle[$item['job_title']]['job_name'] : '';
            if (!empty($job_title_name) && HrJobTitleModel::STATUS_2 == $jobTitle[$item['job_title']]['status']) {
                $job_title_name .= self::$t->_('deleted');
            }

            $department_name = $sysDepartments[$item['node_department_id']]['name'] ?? "";
            if (!empty($department_name) && SysDepartmentModel::DELETE_1 == $sysDepartments[$item['node_department_id']]['deleted']) {
                $department_name .= self::$t->_('deleted');
            }

            if (empty($department_name)) {
                $department_name = $sysDepartments[$item['sys_department_id']]['name'] ?? '';
                if (!empty($department_name) &&  SysDepartmentModel::DELETE_1 == $sysDepartments[$item['sys_department_id']]['deleted']) {
                    $department_name .= self::$t->_('deleted');
                }
            }

            $sys_department_name = $sysDepartments[$item['sys_department_id']]['name'] ?? '';
            if (!empty($sys_department_name) && SysDepartmentModel::DELETE_1 == $sysDepartments[$item['sys_department_id']]['deleted']) {
                $sys_department_name .= self::$t->_('deleted');
            }

			$row = [
				'staff_info_id' => $item['staff_info_id'],
				'name' => $item['name'],
				'name_en' => (string)$item['name_en'],
				'mobile' => !empty($item['mobile']) ? (string)$item['mobile'] : '',
				'state' => $item['state'],
				'leave_date' => $item['leave_date'] ? date("Y-m-d", strtotime($item['leave_date'])) : '',
				'sys_store_name' => isset($this->temp()[$item['sys_store_id']]) ? $this->temp()[$item['sys_store_id']]['name'] : '',
				'job_title_name' => $job_title_name,
				'sys_department_name' => $sys_department_name,
				'department_name' => $department_name,
				'store_area_id' => $this->temp()[$item['sys_store_id']] ? $sorting_no[$this->temp()[$item['sys_store_id']]['sorting_no']] ?? 0 : 0,
				'store_area_text' => $this->temp()[$item['sys_store_id']]['sorting_no'] ? $sorting_no[$this->temp()[$item['sys_store_id']]['sorting_no']] ? $this->temp()[$item['sys_store_id']]['sorting_no'] : '' : '',
				'state_name' => $this->getStateName($item['state'], $item['wait_leave_state']),
				'approve_state_text' => $this->getApproveStateName($staffApproveStateArr[$item['staff_info_id']] ?? 1),
				//                'is_inventory' => 1
				// 满足四个角色 (系统管理员 超级管理员 网点主管 区域经理) 的用户并且网点也被打开了 查看编辑
				'is_inventory' => (isset($positionsModel) && $positionsModel
				                   && isset($isInventories[$item['sys_store_id']]) && $isInventories[$item['sys_store_id']]['is_switch'] == 1)
					? 1 : 0,
				'superior_operate_time' => $item['lm_superior_operate_time'] ?? '',
                // 工作所在国家
                'working_country'     => $workingCountryList[$item['working_country']] ?? '',
                'first_audit_status' => $item['first_audit_status'],
                'first_status'=> $item['first_status'],
                'second_audit_status'=> $item['second_audit_status'],
                'second_status'=> $item['second_status'],
                'first_audit_status_name' => static::$t->_('hr_probation_audit_status_'.(empty($item['first_audit_status']) ? HrProbationModel::FIRST_AUDIT_STATUS_WAIT :$item['first_audit_status'])),
                'first_status_name'        => $item['first_audit_status'] == HrProbationModel::FIRST_AUDIT_STATUS_DONE ? (static::$t->_('hr_probation_status_' . (empty($item['first_status']) ? HrProbationModel::FIRST_STATUS_NOT_PASS : $item['first_status']))) : '',
                'second_audit_status_name'=> static::$t->_('hr_probation_audit_status_'.(empty($item['second_audit_status']) ? HrProbationModel::SECOND_AUDIT_STATUS_WAIT: $item['second_audit_status'])),
                'second_status_name'       => $item['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE ? (static::$t->_('hr_probation_status_' . (empty($item['second_status']) ? HrProbationModel::SECOND_STATUS_NOT_PASS : $item['second_status']))) : '',

            ];
			
				$now_time = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);
				
				//转正评估不需要leave_source
				$item['leave_source'] = 5;
				
				$row['hire_date'] = $item['hire_date'] ? date("Y-m-d", strtotime($item['hire_date'])) : '';
				$row['job_title_level'] = !empty($item['job_title_level']) ? $item['job_title_level'] : 1;
				$row['manage_staff_id'] = $item['manage_staff_id'] ?? '';
				$row['manage_staff_name'] = '';
				$row['manage_staff_mobile'] = '';
				
				$row['manage_higher_staff_id'] = "";
				$row['manage_higher_staff_name'] = "";
				$row['manage_higher_staff_mobile'] = "";
				
				
				if (!empty($row['manage_staff_id']) && !empty($manage_staffs[$row['manage_staff_id']])) {
					$row['manage_staff_name'] = $manage_staffs[$row['manage_staff_id']]['name'] ?? '';
					$row['manage_staff_mobile'] = $manage_staffs[$row['manage_staff_id']]['mobile'] ?? '';
					
					$row['manage_higher_staff_id'] = $manage_high_staff_map[$row['manage_staff_id']] ?? '';
				}
				
				if (isset($params['is_export']) && !empty($row['manage_higher_staff_id'])) {
					$row['manage_higher_staff_name'] = $manage_staffs[$row['manage_higher_staff_id']]['name'] ?? '';
					$row['manage_higher_staff_mobile'] = $manage_staffs[$row['manage_higher_staff_id']]['mobile'] ?? '';
				}
				
				$row['first_deadline_date'] = "";
				$row['second_deadline_date'] = "";
				
				$row['first_deadline_date_start'] = "";
				$row['first_deadline_date_end'] = "";
				
				$row['second_deadline_date_start'] = "";
				$row['second_deadline_date_end'] = "";
				
				$row['days_90'] = "";
				$row['is_delay'] = $item['is_delay'];
                
				if (!empty($row['hire_date'])) {
                    $row['first_deadline_date_start']  = $item['first_evaluate_start'] ?? '';
                    $row['first_deadline_date_end']    = $item['first_evaluate_end'] ?? '';
                    $row['second_deadline_date_start'] = $item['second_evaluate_start'] ?? '';
                    $row['second_deadline_date_end']   = $item['second_evaluate_end'] ?? '';
                    $row['formal_at']                  = $item['formal_at'] ?? '';
                    $row['first_deadline_date']        = $item['first_evaluate_start'] . '——' . $item['first_evaluate_end'];
                    $row['second_deadline_date']       = $item['second_evaluate_start'] . '——' . $item['second_evaluate_end'];
				}
				
				$row['status'] = $item['probation_status'] ?? '1';
				$row['status_text'] = (static::$t)->_("probation_status_" . $row['status']);
				$row['first_score'] = $item['first_score'] ? bcdiv($item['first_score'], 1000, 2) : '0';
				$row['second_score'] = $item['second_score'] ? bcdiv($item['second_score'], 1000, 2) : '0';
                
				if (empty($item['hire_date'])) {
					$row['days'] = 0;
				} else {
					$datetime_start = new \DateTime($item['hire_date']);
					$datetime_end = new \DateTime($now_time);
					$row['days'] = $datetime_start->diff($datetime_end)->days;
				}
				
				$row['identity'] = $item['identity'] ?? "";
				$row['mobile_company'] = $item['mobile_company'] ?? "";
				$row['node_department_name'] = $this->showDepartmentName($item['node_department_id']);
				
				//试用期状态按钮是否变红
				$row['is_not_pass'] = '0';
				//编辑按钮是否展示
				$row['is_edit_show'] = '0';
				
				//试用期状态为未通过 或者 已过第二次评估期限但状态为试用期中
				if ($row['status'] == StaffEnums::STATUS_NOT_PASS || ($now_time > $row['second_deadline_date_end'] && $row['status'] == StaffEnums::STATUS_PROBATION)) {
					$row['is_not_pass'] = '1';
				}
				$row['cur_level'] = $item['cur_level'];
				
				
				//审批意见
				$row['remark'] = $item['remark'] ?? '';
				
				//试用期转正时候的备注
				$row['mark'] = $item['mark'] ?? '';
				
				$row['piece_name'] = $pieceArr[$item['sys_store_id']] ?? "";
			
			$row['is_edit_show'] = $this->isCanEdit($row);//BI 系统 app/BLL/ProbationBLL.php
			//新需求 来源非“BY申请离职”以及职位一下职位（Bike courier 13 /van courier 110/Branch Supervisor 16/shop officer 98/ Shop Supervisor 101）不显示某些字段 并且关系
			$row['is_show'] = 0;
			$forbidden = array(Enums::$job_title['bike_courier'], Enums::$job_title['van_courier'], Enums::$job_title['branch_supervisor'], Enums::$job_title['shop_officer'], Enums::$job_title['shop_supervisor']);
			//来源 '0=空，-1其他。6批量导入。其他同hold来源,1.新增,2.hirs,3旷工3天及以上，4未缴纳公款，5backyard提交申请，'
            if ($item['leave_source'] == 5 && in_array($item['job_title'], $forbidden)) {
                $row['is_show'] = 1;
            }
            $row['contract_company_id'] = $item['contract_company_id'];
            $row['contract_company_name'] = $companyMap[$row['contract_company_id']] ?? '';
            $row['is_show_download'] = true; // 下载按钮除权限外的展示逻辑
            if (!empty($params['is_non_front_line_probation']) && $params['is_non_front_line_probation'] == 1) {
                $row['is_result_notification_show'] = $row['status'] == HrProbationModel::STATUS_NOT_PASS && $row['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_DONE ? '1' : '0';
                $row['result_notification_text']    = empty($item['result_notification']) ? static::$t->_('probation_result_notification_no') : static::$t->_('probation_result_notification_yes');

                $row['first_deadline_date_end_timeout_day']  = 0;
                $row['second_deadline_date_end_timeout_day'] = 0;
                if ($row['first_audit_status'] == HrProbationModel::FIRST_AUDIT_STATUS_TIMEOUT && !empty($row['first_deadline_date_end']) && $now_time >= $row['first_deadline_date_end']) {
                    $row['first_deadline_date_end_timeout_day'] = (new \DateTime($row['first_deadline_date_end']))->diff(new \DateTime($now_time))->days;
                }
                if ($row['second_audit_status'] == HrProbationModel::SECOND_AUDIT_STATUS_TIMEOUT && !empty($row['second_deadline_date_end']) && $now_time >= $row['second_deadline_date_end']) {
                    $row['second_deadline_date_end_timeout_day'] = (new \DateTime($row['second_deadline_date_end']))->diff(new \DateTime($now_time))->days;
                }

                $row['first_score'] = $item['first_score'] ? $probation_score_enumerate[$item['first_score']] : '';
                $row['second_score'] = $item['second_score'] ? $probation_score_enumerate[$item['second_score']] : '';
                $row['leave_date'] = '';
                if ($item['state'] == HrStaffInfoModel::STATE_RESIGN || ($item['state'] == HrStaffInfoModel::STATE_ON_JOB && $item['wait_leave_state'] == HrStaffInfoModel::STAFF_WAIT_LEAVE_STATE_YES)){
                    $row['leave_date'] = $item['leave_date'] ? date("Y-m-d", strtotime($item['leave_date'])) : '';
                }
                $row['is_show_download'] = $row['first_audit_status'] == 3 || $row['second_audit_status'] == 3;
            }
			$return['rows'][] = $row;
		}
		return $return;
	}

    /**
     * 根据区域获取网点列表
     * @param mixed ...$str
     * @return array
     */
    public function getStoresByArea(...$str): array
    {
        $ths = [];
        foreach ($str as $name) {
            if (isset(myEnums::$store_sorting_no_select[$name])) {
                array_push($ths, myEnums::$store_sorting_no_select[$name]);
            }
        }

        $stores = [];
        $storeList = $this->temp();
        foreach ($storeList as $storeId => $store) {
            if (in_array($store['sorting_no'], $ths)) {
                $stores[] = $storeId;
            }
        }
        return $stores;
    }

    /**
     * bi直接编辑分数
     * @param $staff_info_id
     * @param $score
     * @param $pic
     * @param $remark
     * @param $userId
     * @param string $job_content
     * @return array
     */
    public function auditBi($staff_info_id, $score, $pic, $remark, $userId, $job_content = '')
    {
        $db = $this->getDI()->get("db_backyard");
        $db->begin();
        try {
            $single = (new Builder)->columns([
                'hp.status',
                'hp.formal_at',
                'hp.id',
                'hp.staff_info_id',
                'hsi.job_title_level',
                'hsi.hire_date',
                'hsi.job_title_grade_v2',
            ])
                ->from(['hp' => HrProbationModel::class])
                ->leftJoin(HrStaffInfoReadModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hsi')
                ->where(' hp.staff_info_id = :id:', ['id' => intval($staff_info_id)])->getQuery()->getSingleResult();

            if (!$single) {
                //没有的话直接插入一条
                //查询入职日期
                $HrStaffInfo = HrStaffInfoReadModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: ',
                    'bind'       => [
                        'staff_info_id' => $staff_info_id,
                    ],
                ]);
                if (empty($HrStaffInfo)) {
                    throw new \Exception('没有找到该用户数据'.$staff_info_id);
                }
                $formal_days = (int)$HrStaffInfo->job_title_grade_v2 <= $this->job_grade ? $this->formal_days : $this->formal_days_two;
                $formal_at   = $this->getDateByDays($HrStaffInfo->hire_date, $formal_days - 1, 1);
                $arr         = [
                    'staff_info_id' => $staff_info_id,
                    'status'        => 1,
                    'is_system'     => 1,
                    'formal_at'     => $formal_at,
                    'created_at'    => gmdate("Y-m-d H:i:s", time() + $this->config->application->add_hour * 3600),
                ];
                $db->insertAsDict("hr_probation", $arr);
                $arr['id']                 = $db->lastInsertId();
                $arr['job_title_grade_v2'] = $HrStaffInfo->job_title_grade_v2;
            } else {
                $arr = $single->toArray();
            }

            $lang = (static::$t);

            $item = []; //hr_probation表

            //如果info与库里不同,证明已经修改
            foreach ($score['list'] as $k => $v) {
                foreach ($v['list'] as $kk => $vv) {
                    if ($lang->_($vv['info_key']) != $vv['info']) {
                        $score['list'][$k]['list'][$kk]['is_update'] = 1;
                    }
                }
            }

            $arr['tpl_id'] = $this->getTplIdByJobTitleGradeV2($arr['job_title_grade_v2']);

            //自己根据规则算一遍
            $score = $this->getScoreFromTpl($score, 2);

            $item['updated_at']  = gmdate("Y-m-d H:i:s", time() + ($this->timeOffset) * 3600);

            //第二次
            $score_num            = $this->getScore($score['second_score'], 1);
            $item['second_score'] = $score_num;
            $item['is_system']    = 1;
            if ($score_num >= 6000) {
                $item['status'] = StaffEnums::STATUS_PASS;    //已通过
                $item['second_status']     = HrProbationModel::SECOND_STATUS_PASS;
                //如果当前时间，大于转正时间，且分数大于等于6分，直接已转正
                if (strtotime($item['updated_at']) >= strtotime($arr['formal_at'])) {
                    $item['status']          = StaffEnums::STATUS_FORMAL;
                    $item['formal_staff_id'] = $userId;
                }
            } else {
                $item['status'] = StaffEnums::STATUS_NOT_PASS;    //未通过
                $item['second_status']     = HrProbationModel::SECOND_STATUS_NOT_PASS;
            }
            if (!empty($remark)) {
                $item['remark'] = $remark;
            }

            // 如果状态为已经通过，但是还未到转正日期 则后续转正脚本需要跑，插入转正评估数据等不许在在执行
            if ($item['status'] == StaffEnums::STATUS_PASS){
                $item['is_system']    = 2;
            }

            $db->updateAsDict("hr_probation", $item, ["conditions" => 'id='.intval($arr['id'])]);
            //下一级评审，内容大部分相同
            $tmp                  = [];
            $tmp['probation_id']  = $arr['id'];
            $tmp['staff_info_id'] = $arr['staff_info_id'];
            $tmp['tpl_id']        = $arr['tpl_id'];
            $tmp['audit_id']      = $userId;
            $tmp['audit_level']   = 2;
            $tmp['cur_level']     = self::CUR_LEVEL_SECOND;
            $tmp['audit_status']  = self::AUDIT_STATUS_DEAL;
            $tmp['status']        = $item['status'];
            $tmp['score']         = json_encode($score, JSON_UNESCAPED_UNICODE);
            $tmp['created_at']    = $item['updated_at'];
            $tmp['updated_at']    = $item['updated_at'];
            $tmp['remark']        = $remark;
            $tmp['pic']           = $pic;
            $tmp['deadline_at']   = gmdate("Y-m-d", time() + ($this->timeOffset) * 3600);
            $tmp['job_content']   = $job_content;
            $db->insertAsDict("hr_probation_audit", $tmp);

            //转正记录抓正日志
            if ($item['status'] == StaffEnums::STATUS_FORMAL) {
                $this->putFormalLog($userId, $staff_info_id, $arr['status'], StaffEnums::STATUS_FORMAL);
            }
        } catch (\Exception $e) {
            $db->rollback();
            $this->getDi()->get('logger')->error('hcm 编辑编辑分数：'.json_encode(
                    [
                        'Err_Msg'  => $e->getMessage(),
                        'Err_Line' => $e->getLine(),
                        'Err_File' => $e->getFile(),
                        'Err_Code' => $e->getCode(),
                    ], JSON_UNESCAPED_UNICODE));
            return ['code' => ErrCode::VALIDATE_ERROR, 'message' => 'Please try again later', 'data' => []];
        }
        $db->commit();
        return ['code' => ErrCode::SUCCESS, 'message' => 'ok', 'data' => []];
    }
	
	
	/**
	 * 根据新的职级来获得模板
	 * @param $grade
	 * @return int
	 */
	public function getTplIdByJobTitleGradeV2($grade)
	{
		$grade = intval($grade);
		//小于等于16都是模板1
		if ($grade <= 16) {
			return 4;
		}
		//17及以上，都是3
		if ($grade >= 17) {
			return 3;
		}
		//其他情况2
		return 2;
	}
	
  
   
    /**
     * 下载
     */
    public function download($staff_info_id, $type = 2)
    {
        $data = [];
        $builder = new  Builder();
        $execute = $builder->columns([
            'hsi.staff_info_id',
            'hsi.name',
            'hsi.job_title_level',
            'hsi.job_title_grade_v2',
            'hsi.hire_date',
            'job.job_name',
            'd.name   as department_name',
            'n.name as node_department_name',
            'hp.id as probation_id',
            'hp.formal_at',
            'hp.is_delay',
            'hp.first_audit_status',
            'hp.second_audit_status',
        ])->from(['hsi' => HrStaffInfoReadModel::class])
            ->leftJoin(HrProbationModel::class, 'hp.staff_info_id = hsi.staff_info_id', 'hp')
            ->leftJoin(HrJobTitleModel::class, 'job.id = hsi.job_title', 'job')
            ->leftJoin(SysDepartmentModel::class, 'd.id = hsi.sys_department_id', 'd')
            ->leftJoin(SysDepartmentModel::class, 'n.id = hsi.node_department_id', 'n')
            ->where('hsi.staff_info_id =:id:', ['id' => intval($staff_info_id)])->getQuery()->getSingleResult();
        if ($execute) {
            $data = $execute->toArray();
        }


        $data['first_deadline_date'] = "";
        $data['second_deadline_date'] = "";
        if (!empty($data['hire_date'])) {
            $data['hire_date'] = $this->getDateByDays($data['hire_date'], 0, 1);
            $data['first_deadline_date'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays($data['hire_date'], $this->probation_first_deadline_end, 1) : $this->getDateByDays(
                $data['hire_date'],
                $this->probation_first_deadline_end_two,
                1
            );
            if ($data['is_delay']) {
                $data['second_deadline_date'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays(
                    $data['hire_date'],
                    $this->probation_second_delay_deadline_end,
                    1
                ) : $this->getDateByDays($data['hire_date'], $this->probation_second_delay_deadline_end_two, 1);
            } else {
                $data['second_deadline_date'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays(
                    $data['hire_date'],
                    $this->probation_second_deadline_end,
                    1
                ) : $this->getDateByDays($data['hire_date'], $this->probation_second_deadline_end_two, 1);
            }


            if (empty($data['formal_at'])) {
                $data['formal_at'] = $data['job_title_grade_v2'] <= $this->job_grade ? $this->getDateByDays($data['hire_date'], $this->formal_days, 1) : $this->getDateByDays(
                    $data['hire_date'],
                    $this->formal_days_two,
                    1
                );
            }
        }

        $data['comment'] = "";

        $data['probation_id'] = $data['probation_id'] ?? 0;
        $data['job_title_level'] = !empty($data['job_title_level']) ? $data['job_title_level'] : 1;
        $data['job_title_level_text'] = StaffEnums::JOB_TITLE_LEVEL_DESC[$data['job_title_level']];

        $data['job_title_grade_v2'] = $data['job_title_grade_v2'] ?? 0;
        $data['job_title_grade_v2_text'] = 'F' . $data['job_title_grade_v2'];


        if (!empty($data['probation_id'])) {
            $data['comment'] = $this->getProbationLogs($data['probation_id'])['comment'];
        }
        $data['rules'] = [];
        for ($i = 1; $i <= 6; $i++) {
            $data['rules'][] = (static::$t)->_("hr_probation_rule_" . $i);
        }

        $item = HrProbationAuditModel::findFirst([
            'conditions' => 'probation_id = ?1',
            'order'      => 'id desc',
            'bind'       => [
                1 => $data['probation_id'],
            ]
        ]);
        if ($item) {
            $item = $item->toArray();
            $tpl_id = $item['tpl_id'];
            $score = $item['score'];
        } else {
            $tpl_id = $this->getTplIdByJobTitleGradeV2($data['job_title_grade_v2']);
            $score = null;
        }
        $tpl = $this->getTplItem($tpl_id, $score);
        if (is_string($tpl)) {
            throw new \Exception($tpl);
        }
        $data['score'] = $tpl['score'];
        $data['question_num'] = $tpl['question_num'];
        $data['score_rule_text'] = $tpl['score_rule_text'];

        $data['score']['score_text'] = $this->getScoreGrade($data['score']['score']);
        $data['score']['second_score_text'] = $this->getScoreGrade($data['score']['second_score']);

        $data['field'] = [];

        $fieldArr = [
            "name",
            "staff_info_id",
            "job_title",
            "job_title_level",
            "department",
            "hire_date",
            "first_deadline_date",
            "second_deadline_date",
            "rule",
            "info",
            "weight",
            "first_score",
            "second_score",
            "comment",
            "attendance",
            'attendance_info',
            "first_check",
            "second_check",
            "late",
            "sick",
            "casual",
            "lack",
            "alert",
            "first_alert",
            "second_alert",
            "from",
            "all_info",
            "company",
            "grade",
            "sign_title_1",
            "sign_title_2",
            "sign_time",
            'company_my'
        ];

        foreach ($fieldArr as $field) {
            $data['field'][$field] = (static::$t)->_("hr_probation_field_" . $field);
        }
        //获取 签字图片和日期
        if (isset($item['id'])) {
            //获取当前阶段的签字图片
            $HrProbationMessage = HrProbationMessageModel::findFirst([
                'conditions' => 'probation_audit_id = :probation_audit_id: and sign_status = 2',
                'bind'       => ['probation_audit_id' => $item['id']],
            ]);

            if ($HrProbationMessage) {
                $HrProbationMessage = $HrProbationMessage->toArray();
                $data['message_sign'][$item['cur_level']] = [
                    'sign_url'  => $HrProbationMessage['sign_url'],
                    'sign_time' => $HrProbationMessage['sign_time'],
                ];
            }
            //如果是第二阶段
            if ($item['cur_level'] == $this->cur_level_2) {
                //获取第一阶段的签字
                //查询第一阶段的评估表
                $HrProbationAudit = HrProbationAuditModel::findFirst([
                    'conditions' => 'staff_info_id = ?1 and cur_level =1 ',
                    'order'      => 'id desc',
                    'columns'    => 'id,cur_level',
                    'bind'       => [
                        1 => $data['staff_info_id'],
                    ]
                ]);
                if ($HrProbationAudit) {
                    $HrProbationMessage = HrProbationMessageModel::findFirst([
                        'conditions' => 'probation_audit_id = :probation_audit_id: and sign_status = 2',
                        'bind'       => ['probation_audit_id' => $HrProbationAudit->id],
                    ]);

                    if ($HrProbationMessage) {
                        $HrProbationMessage = $HrProbationMessage->toArray();
                        $data['message_sign'][$HrProbationAudit->cur_level] = [
                            'sign_url'  => $HrProbationMessage['sign_url'],
                            'sign_time' => $HrProbationMessage['sign_time'],
                        ];
                    }
                }
            }
        }


        $data['field']['tpl_name'] = (static::$t)->_("hr_probation_field_title_" . $tpl_id);
        $data['attendance'] = $this->getAttendance($staff_info_id);
        $data['alert'] = $this->getAlert($staff_info_id, $data['hire_date'], $data['formal_at']);

        if ($tpl_id == 4) {
            $tpl_name = "staff-my";
        } else {
            $tpl_name = "staff-cn";
        }

        $this->outPdf($staff_info_id, $tpl_name, $data, $type);
    }

    /**
     * 导出
     * @param $params
     * @throws \Exception
     */
    public function export($params)
    {
        $params['is_export'] = 1;
        $params['page']     = 1;
        $params['pagesize'] = 1000;
        $data['rows'] = [];
        while (true) {
            $_data = $this->getList($params);
            if (empty($_data['rows'])) {
                break;
            }
            $data['rows'] = array_values(array_merge($data['rows'],$_data['rows']));
            $params['page']++;
        }

        $fieldArr = [
            "staff_info_id",
            "name",
            "name_en",
            "identity",
            "mobile_company",
            "state_name",
            "manage_staff_name",
            "manage_staff_mobile",
            "manage_staff_id",
            "manage_higher_staff_name",
            "manage_higher_staff_mobile",
            "manage_higher_staff_id",
            'status_text',
            'remark',
            "hire_date",
            "formal_at",
            "days",
            "job_title_name",
            "contract_company_name",
            "sys_department_name",
            "node_department_name",
            "sys_store_name",
//            "store_area_text",
//            "piece_name",
            "first_deadline_date",
            "first_audit_status_name",
            "first_status_name",
            "second_deadline_date",
            "second_audit_status_name",
            "second_status_name",
            "first_score",
            "second_score",
        ];

        $langArr = [
            "staff_info_id",
            "name",
            "name_en",
            "identity",
            "mobile_company",
            "state",
            "manage_staff_name",
            "manage_staff_mobile",
            "manage_staff_id",
            "manage_higher_staff_name",
            "manage_higher_staff_mobile",
            "manage_higher_staff_id",
            'status_text',
            'remark',
            "hire_date",
            "formal_at",
            "days",
            "job_title",
            "contract_company_name",
            "sys_department_name",
            "node_department_name",
            "sys_store_name",
//            "store_area_text",
//            "piece_name",
            "excel_first_deadline_date",
            "first_status",//第一次评估状态
            "first_name",//第一次评估结果
            "excel_second_deadline_date",
            "second_status", //第二次评估状态
            "second_name",//第二次评估结果
            "excel_first_score",
            "excel_second_score",
        ];
        if (!empty($params['is_non_front_line_probation']) && $params['is_non_front_line_probation'] == 1) {
            $langArr[]  = 'leave_date';
            $langArr[]  = 'first_deadline_date_end_timeout_day';
            $langArr[]  = 'second_deadline_date_end_timeout_day';
            $fieldArr[] = 'leave_date';
            $fieldArr[] = 'first_deadline_date_end_timeout_day';
            $fieldArr[] = 'second_deadline_date_end_timeout_day';
            $langArr = array_diff($langArr, ['remark']);
            $fieldArr = array_diff($fieldArr, ['remark']);
        }

        $header = [];
        foreach ($langArr as $k => $v) {
            $header[] = (static::$t)["hr_probation_field_".$v];
        }

        $data_list = [];
        foreach ($data['rows'] as $k => $v) {
            foreach ($fieldArr as $kk => $vv) {
                $data_list[$k][$vv] = $v[$vv];
            }
            $data_list[$k] = array_values($data_list[$k]);
        }

        //3.生成Excel URL
        $file_name = $params['file_name'];
        $file_data = $this->exportExcel($header, $data_list ?? [], $file_name);
        $flashOss  = new FlashOss();
        $ossObject = env('country_code').'/probation/'.$file_name;
        $flashOss->uploadFile($ossObject, $file_data['data']);
        return $ossObject;
    }


    /**
     * 修改转正
     * @param $params
     * @return string|boolean
     * @throws ValidationException
     */

    public function formal($params)
    {
        $single           = HrProbationModel::query()->where('staff_info_id = :id:',
            ['id' => $params['staff_info_id']])->execute()->getFirst();
        $item             = $single ? $single->toArray() : [];
        $status           = 1;
        $formal_at        = '';
        $is_change_status = false;
        if (!empty($item)) {
            $status    = $item['status'];
            $formal_at = $item['formal_at'];
            if ($item['status'] != $params['status']) {
                $is_change_status = true;
            }
        }

        //1->4  试用期-已转正
        //2->4  已通过-已转正
        //3->4  未通过-已转正
        if (($status == 1 && $params['status'] == 4) || ($status == 2 && $params['status'] == 4) || ($status == 3 && $params['status'] == 4)) {
            $data['formal_staff_id'] = $params['user_id'];
        }

        $data               = [];
        $data['status']     = $params['status'];
        $data['formal_at']  = date("Y-m-d", strtotime($params['formal_at']));
        $data['updated_at'] = gmdate("Y-m-d H:i:s", time() + ($this->timeOffset) * 3600);
        $data['mark']       = $params['mark'] ?? '';
        //如果没有则添加
        if (empty($item)) {
            $data['created_at']    = $data['updated_at'];
            $data['staff_info_id'] = $params['staff_info_id'];
            $data['is_system']     = 1;
            return $this->getDI()->get("db_backyard")->insertAsDict("hr_probation", $data);
        }
        //记录转正记录
        $this->putFormalLog($params['user_id'], $params['staff_info_id'], $status, $data['status'], $formal_at,
            $data['formal_at']);

        // 如果 后台更改了试用期状态 则发送转正评估、发送通知、转正脚本等都不在运行
        if ($is_change_status) {
            $data['is_system'] = 1;
        }

        return $this->getDI()->get("db_backyard")->updateAsDict("hr_probation", $data,
            ["conditions" => 'id='.intval($item['id'])]);
    }
	
	
	
	/**
	 * @param $staff_info_id
	 * @return array|string
	 * @throws \Exception
	 */
	public function editDetail($staff_info_id,$probationChannelType = HrProbationModel::PROBATION_CHANNEL_TYPE_FORMAL)
	{
		

		$builder = new Builder();
		$single = $builder->columns(['hsi.staff_info_id','hsi.name','hsi.name_en','hsi.job_title_level','hsi.job_title_grade_v2','hsi.hire_date','hp.status','hp.cur_level'])
		                  ->from(['hsi' => HrStaffInfoReadModel::class])
		                  ->leftJoin(HrProbationModel::class,'hp.staff_info_id = hsi.staff_info_id','hp')
		                  ->where('hsi.staff_info_id =:id:',['id' => intval($staff_info_id)])->getQuery()->getSingleResult();
		
		
		if(! $single){
			return 'not found user';
		}
		$item = $single->toArray();
		

		
		//找最新的一次，一定是第二次评估
		$auditDataFind = HrProbationAuditModel::findFirst([
			                                                  'conditions' => 'staff_info_id = ?1',
			                                                  'order' => 'id desc',
			                                                  'bind'       => [
				                                                  1 =>  $staff_info_id,
			                                                  ]
		                                                  ]);
		$auditData =   $auditDataFind ? $auditDataFind->toArray() : [];
		if(empty($auditData)){
			$tpl = $this->getTplItem($this->getTplIdByJobTitleGradeV2($item['job_title_grade_v2']),[]);
		}else{
			$tpl = $this->getTplItem($auditData['tpl_id'], $auditData['score']);
		}
		//不考虑该表job_title_level的情况
		//$tpl_id = $this->getTplId($item['job_title_level']);
		//$tpl = $this->getTplItem($auditData['tpl_id'], $auditData['score']);
		if (is_string($tpl)) {
			throw new \Exception($tpl);
		}
		
		$item['score'] = $tpl['score'];
		$item['job_content'] = $auditData['job_content'] ?? '';
		$item['score_rule_text'] = (static::$t)->_('hr_probation_field_all_info') . ($tpl['score_rule_text'] ?? '');
		//
		return $item;
	}

    /**
     * 获取所属区域
     * @return array
     */
    public function getAreaInfo()
    {
        $area = [];
        foreach (myEnums::$store_sorting_no_select as $k => $item) {
            $area[] = ['id' => $k,'name' => $item];
        }
        return $area;
    }
  
    
    
   





    

    
}