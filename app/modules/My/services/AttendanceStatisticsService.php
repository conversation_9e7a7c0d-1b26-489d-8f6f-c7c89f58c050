<?php

namespace App\Modules\My\Services;

use App\Library\ArrayToObject;
use App\Library\AttendanceEnums;
use App\Library\DateHelper;
use App\Library\Enums\ConditionsRulesEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrShiftV2ExtendModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffWorkDaysModel;
use App\Models\backyard\StaffAuditReissueForBusinessModel;
use App\Models\backyard\StaffWorkAttendanceModel;
use App\Modules\My\library\AttendanceStatisticsBox;
use App\Modules\My\library\AttendanceUtil;
use App\Modules\My\library\Enums\enums;
use App\Services\AttendanceStatisticsService as BaseAttendanceStatisticsService;
use App\Services\ConditionsRulesService;
use App\Services\HrStaffService;
use App\Services\StaffPublicHolidayService;
use App\Services\SysStoreService;
use App\Services\WorkHomeService;

class AttendanceStatisticsService extends BaseAttendanceStatisticsService
{

    /**
     * 查询attendance_data_v2的字段
     * @var string[]
     */
    protected $solidify_attendance_data_v2_query_field = [
        'leave_type',
        'attendance_started_at',
        'attendance_end_at',
        'stat_date',
        'staff_info_id',
        'shift_start',
        'shift_end',
        'late_times_after',//迟到
        'leave_early_times',//早退
        'sys_store_id',
        'OFF',
        'REST',
        'BT',
        'AB',
        'PH',
        'job_title',


    ];

    public function getAttendanceDataV2(array $params): array
    {
        $list = [];
        if (empty($params['staff_info_ids'])) {
            return $list;
        }
        $staff_ids = array_chunk($params['staff_info_ids'], 400);
        $attendance_data = [];
        $field = [
            'attendance_date',
            'staff_info_id',
            'started_state',
            'end_state',
            "CONVERT_TZ( started_at, '+00:00', '".$this->timeZone."' ) AS started_at",
            "CONVERT_TZ( end_at, '+00:00', '".$this->timeZone."' ) AS end_at",
            "shift_type"
        ];
        foreach ($staff_ids as $staff_id) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns($field);
            $builder->from(['a' => StaffWorkAttendanceModel::class]);

            $builder->inWhere('a.staff_info_id', $staff_id);

            if (!empty($params['start_date'])) {
                $builder->andWhere('a.attendance_date >= :start_date: ', ['start_date' => $params['start_date']]);
            }
            if (!empty($params['end_date'])) {
                $builder->andWhere('a.attendance_date <= :end_date: ', ['end_date' => $params['end_date']]);
            }
            $res = $builder->getQuery()->execute()->toArray();
            if (!empty($res)) {
                $attendance_data = array_merge($attendance_data, $res);
            }
        }

        foreach ($attendance_data as $value) {
            $list[$value['staff_info_id'].'-'.$value['attendance_date']][$value['shift_type']] = $value;
        }
        unset($attendance_data);
        return $list;
    }

    public function getStaffAuditReissueInfo(array $params)
    {
        $returnData = [];

        if (empty($params['staff_info_ids'])) {
            return $returnData;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns(
            [
                'start_time',
                'end_time',
                'start_time_zone',
                'end_time_zone',
                "CONVERT_TZ( start_time, '+00:00', '".$this->timeZone."' ) AS started_at",
                "CONVERT_TZ( end_time, '+00:00', '".$this->timeZone."' ) AS end_at",
                'staff_info_id',
                'attendance_date',
                'status',
                'shift_type',
            ]
        );

        $builder->from(['s' => StaffAuditReissueForBusinessModel::class]);

        $builder->where("s.status in (1,2)"); //查询申请中和已通过的

        if (!empty($params['start_date'])) { //开始时间
            $builder->andWhere('s.attendance_date >= :start_time:', ['start_time' => $params['start_date']]);
        }
        if (!empty($params['end_date'])) { //截止时间
            $builder->andWhere('s.attendance_date <= :end_time:', ['end_time' => $params['end_date']]);
        }
        if (!empty($params['staff_info_ids'])) {
            $builder->inWhere('s.staff_info_id', $params['staff_info_ids']);
        }

        $list=  $builder->getQuery()->execute()->toArray();

        foreach ($list as $item) {
            $start_time_zone = is_null($item['start_time_zone']) ? $this->timeOffset : $item['start_time_zone'];
            $end_time_zone = is_null($item['end_time_zone']) ? $this->timeOffset : $item['end_time_zone'];
            $item['started_at']=  empty($item['start_time']) ? $item['start_time'] :  date(
                'Y-m-d H:i:s',
                strtotime($item['start_time']) + $start_time_zone * 3600
            );                               //上班
            $item['end_at']= empty($item['end_time']) ? $item['end_time'] :  date(
                'Y-m-d H:i:s',
                strtotime($item['end_time']) + $end_time_zone * 3600
            );                               //下班

            $returnData[$item['staff_info_id'].'-'.$item['attendance_date']][$item['shift_type']] = $item;
        }
        unset($list);

        return $returnData;
    }



    /**
     * 获取员工考勤数据
     * @param $params
     */
    public function getStaffsAttendanceInfo($params)
    {
        if (empty($params['start_date'])) {
            throw new ValidationException('start_date empty');
        }

        if (empty($params['end_date'])) {
            throw new ValidationException('end_date empty');
        }

        $returnData = [
            'total'=>0,
            'list'=>[],
        ];
        //获取员工信息
        if ($this->query_type == 2) {
            $staffs = $this->getHarderOfficeStaffInfo($params);
        } else {
            $staffs = $this->getStoreStaffInfo($params);
        }

        if (!$staffs['list']) {
            return $returnData;
        }


        [$returnData['total'], $list] = [$staffs['total'],$staffs['list']];
        //获取考勤信息
        $params['staff_info']     = $list;
        $params['staff_info_ids'] = array_column($list, 'id');

        //获取考勤打卡信息
        $attendance_data = $this->getAttendanceDataV2($params);

        //获取请假信息
        $apply_info     = $this->getStaffAuditInfo($params);
        $apply_info     = !empty($apply_info) ? $this->handleAuditDataUseToSelf($apply_info) : [];

        //获取出差打卡数据
        $reissue_info  =  $this->getStaffAuditReissueInfo($params);
    
        //出差
        $backyard_attendance_bll = new BackyardAttendanceService();
        $trip_where = " and apply_user in ({apply_user:array}) ";
        $trip_where_params['apply_user'] = $params['staff_info_ids'] ?? [0];
    
        $trip_data = $backyard_attendance_bll->getTripData($params['start_date'] ?? '', $params['end_date'] ?? '', $trip_where, $trip_where_params);
        
        
        //用固化表的数据是最好的
        $staticStaffInfoData = $this->getStaffWeekWorkingDay($params);

        $staff_week_working_data = array_column($list, 'week_working_day', 'id');

        //获取固化的员工班次信息
        $staff_shift_history_data = $this->getShiftByDate($params);

        //构建考情数据
        $attendance_data =  $this->handleAttendanceDataToViewV2(
            $params,
            $attendance_data,
            $staff_week_working_data,
            $apply_info,
            $reissue_info,
            $trip_data,
            $staff_shift_history_data,
            $staticStaffInfoData
        ) ;

        $returnData['list'] = $this->handelAttendanceOtherInfo($list, $attendance_data, $params);
        return  $returnData;
    }

    /**
     * 整理员工信息
     * @param $staff_list
     * @param $attendance_data
     * @param $params
     * @return mixed
     */
    protected function handelAttendanceOtherInfo($staff_list, $attendance_data, $params)
    {
        $sysService = new SysService();
        $region_info = array_column($sysService->getRegionListFromCache(), 'name', 'id');
        $piece_info = array_column($sysService->getPieceListFromCache(), 'name', 'id');
        $store_info = array_column($sysService->getStoreListFromCache(), 'name', 'id');
        //网点区域
        $sorting_no_map =  array_column( $sysService->storeGeographyCodeFromCache(),'label','value');
        //网点类型
        $store_category_map =  SysStoreService::$category;
        $sorting_no_map = array_flip($sorting_no_map);

        foreach ($staff_list as &$item) {
            $item['attend_data'] = $attendance_data[$item['id']] ?? $this->getEmptyAttendance($params);
            $item['manage_region'] = $item['manage_region'] ? $region_info[$item['manage_region']] : '';
            $item['manage_piece'] = $item['manage_piece'] ? $piece_info[$item['manage_piece']] : '';

            $item['state'] = $item['wait_leave_state'] == 1 && in_array($item['state'], [1, 2]) ? self::$t->_(
                'wait_leave_state_1'
            ) : self::$t->_('staff_state_' . $item['state']);
            $item['store_name'] = $this->query_type == 1 ? ($store_info[$item['organization_id']] ?? '') : '';
            //统计旷工
            $item['absenteeism_num'] = array_sum(array_column($item['attend_data'], 'absenteeism_num'));
            //迟到统计
            $item['late_num'] = array_sum(array_column($item['attend_data'], 'late_num'));
            //迟到分钟数统计
            $item['late_time'] = array_sum(array_column($item['attend_data'], 'late_time'));
            //早退统计
            $item['leave_early_num'] = array_sum(array_column($item['attend_data'], 'leave_early_num'));
            //早退分钟数
            $item['leave_early_time'] = array_sum(array_column($item['attend_data'], 'leave_early_time'));
            //网点区域
            $item['manage_geography_code'] = isset($item['sorting_no']) ? ($sorting_no_map[$item['sorting_no']] ?? '') : '';
            $item['manage_geography_code_name'] = isset($item['sorting_no']) ? (isset($sorting_no_map[$item['sorting_no']]) ? $item['sorting_no'] : '') : '';
            //网点类型             s.category as store_category,
            $item['store_category_name'] = isset($item['store_category']) ? ($store_category_map[$item['store_category']] ?? '') : '';
            unset($item['sorting_no']);
        }
        return $staff_list;
    }


    /**
     * 与与泰国有差异化
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getStoreStaffRegion($builder, $params)
    {
        if (!empty($params['geography_code'])) { // 区域
            $sorting_no = [];
            foreach ($params['geography_code'] as $oneCode) {
                $sorting_no[] = enums::$store_sorting_no_select[$oneCode];
            }
            $builder->andWhere(
                's.sorting_no in ({sorting_no:array})',
                ['sorting_no' => $sorting_no]
            );
        }
        return $builder;
    }

    protected function handleAuditDataUseToSelf(array $auditData)
    {
        $returnData = [];
        if (empty($auditData)) {
            return $returnData;
        }
        $list = array_chunk($auditData,300);
        foreach($list as $d_list){
            foreach ($d_list as $key => $value) {
                $unique_key = $value['staff_info_id'].'-'.$value['date_at'];
                $returnData[$unique_key][$value['type']]['type'] = $value['type'];//请假日期类型 0-一天 1-上午 2-下午'
                $returnData[$unique_key][$value['type']]['leave_type'] = $value['leave_type'];
                $returnData[$unique_key][$value['type']]['reason'] = $value['audit_reason'];
                if(empty($returnData[$unique_key][$value['type']]['image_path'])){
                    $returnData[$unique_key][$value['type']]['image_path'] = $value['image_path'];
                }else{
                    $returnData[$unique_key][$value['type']]['image_path'] .= ',' . $value['image_path'];
                }
                $returnData[$unique_key][$value['type']]['status'] = $value['status']; //请假审批状态
            }
        }

        return $returnData;
    }


    /**
     * 构建考勤数据
     * @param array  $params 日期等参数
     * @param array $attendance_dataAll 考勤信息
     * @param array $staff_week_working_data 员工工作制信息
     * @param array $apply_info 请假信息
     * @param array $reissue_infos 出差打卡数据
     * @param array $trip_data 出差数据
     * @param array $staff_shift_history_data 历史班次信息
     * @return array
     */
    protected function handleAttendanceDataToViewV2(
        array $params,
        array $attendance_data_all,
        array $staff_week_working_data,
        array $apply_info,
        array $reissue_infos=[],
        array $trip_data=[],
        array $staff_shift_history_data = [],
        array $staticStaffInfoData = []
    ) {
        $returnData = [];

        $staffInfo = $params['staff_info'];
        $backyardAttendanceService = new BackyardAttendanceService();
        //网点所在州的数据
        $store_province_code = $backyardAttendanceService->getStoreProvinceCode();

        //公共假日
        $holidayService   = new HolidayService();
        $public_holiday_6 = $holidayService->getHoliday(['type'=>HrStaffInfoModel::WEEK_WORKING_DAY_SIX,'date'=>$params['start_date']]);
        $public_holiday_5 = $holidayService->getHoliday(['type'=>HrStaffInfoModel::WEEK_WORKING_DAY_FIVE,'date'=>$params['start_date']]);
        $public_holiday_free = $holidayService->getHoliday(['type'=>HrStaffInfoModel::WEEK_WORKING_DAY_FREE,'date'=>$params['start_date']]);

        //员工个人的ph
        $staffAddPublicHoliday = (new StaffPublicHolidayService())->getMultiStaffData($params['staff_info_ids']);


        $date_range = DateHelper::DateRange(strtotime($params['start_date']), strtotime($params['end_date']));

        //未来日期 居家办公 取配置
        $workHomeData = $this->getHomeDates($params['start_date'], $params['end_date'], $staffInfo);

        //班次信息
        $shift_info = (new StaffShiftService())->getDailyShiftInfo($params['staff_info_ids'],$params['start_date'], $params['end_date']);

        //出差
        $trap_data2date = AttendanceUtil::btSection2Date($date_range, $trip_data);

        //员工休息日
        $offAndRestDay= $this->getStaffOffDayData($params['start_date'], $params['end_date'], $params['staff_info_ids']);
        
        //获取员工工作地所在州
        $staff_province_code = $backyardAttendanceService->getStaffsProvinceCode($params['staff_info_ids']);
        $staff_province_code = $staff_province_code? array_column($staff_province_code, 'value', 'staff_info_id'):[];
        //请假枚举

        //弹性班次
        $free_shift_config = $this->getFreeShiftConfig();
        $this->setFreeShiftInfo($free_shift_config);

        //员工角色
        $staffInfoPositions = (new HrStaffService())->getStaffInfoPositions($params['staff_info_ids']);

        //转正员工数据
        $positive_staffs  = $backyardAttendanceService->getPositiveStaffs(implode(',', $params['staff_info_ids']));

        //根据新配置 获取所属大区
        $storeServer = new StoreService();
        //规则条件
        $conditions = ConditionsRulesService::getInstance()->setRuleKey('leave_special_branch_belongs');

        //支援信息
        $supportServer = new StaffSupportStoreServer();
        $supportData = $supportServer->getSupportDataBetween($params['staff_info_ids'], $params['start_date'], $params['end_date']);
        $supportData = $supportServer->formatSupportData($supportData, $params['start_date'], $params['end_date']);
        $supportText = self::$t->_("on_support");

        foreach ($staffInfo as $value) {
            foreach ($date_range as $day) {
                $unique_key = $value['staff_info_id'].'-'.$day;
                //优先使用员工配置的工作地所在州，没有的话在用网点来查找
                //isset($staticStaffInfoData[$unique_key]) &&  !empty($staticStaffInfoData[$unique_key]['province_code']) ? $staticStaffInfoData[$unique_key]['province_code'] :  $staff_province_code[$value['staff_info_id']] ?? ($store_province_code[$value['sys_store_id']] ?: null);
                $code = $staticStaffInfoData[$unique_key]['province_code'] ?? null;

                if (isset($staticStaffInfoData[$unique_key])) {
                    $value['job_title']          = $staticStaffInfoData[$unique_key]['job_title'];
                }

                if(empty($code)){//固化数据没有 网点员工
                    $params = [
                        'w_f_condition_staff_id'           => $value['staff_info_id'],
                        'w_f_condition_store_id'           => $staticStaffInfoData[$unique_key]['store_id'] ?? $value['sys_store_id'],
                        'w_f_condition_job_title'          => $value['job_title'],
                        'w_f_condition_sex'                => $value['sex'],
                        'w_f_condition_state'              => $value['state'],
                        'w_f_condition_job_title_grade_v2' => $value['job_title_grade_v2'],
                        'w_f_condition_node_department_id' => $staticStaffInfoData[$unique_key]['node_department_id'] ?? $value['node_department_id'],
                        'w_f_condition_hire_type'          => $staticStaffInfoData[$unique_key]['hire_type'] ?? $value['hire_type'],
                        'w_f_condition_category'           => $value['store_category'],
                        'w_f_condition_probation'          => isset($positive_staffs[$value['staff_info_id']]) ? GlobalEnums::OPTION_YES: GlobalEnums::OPTION_NO,
                        'w_f_condition_position_category'  => $staffInfoPositions[$value['staff_info_id']] ?? [],
                        'w_f_condition_nationality'        => $value['nationality'],
                    ];
                    $conditionsResult = $conditions->loadParameters($params)->getConfig();
                    $setting = '';
                    if(!empty($conditionsResult['response_type']) && $conditionsResult['response_type'] == ConditionsRulesEnums::RESPONSE_TYPE_VALUE){
                        //处理数据
                        $setting = $conditionsResult['response_data'];
                    }
                    $code = !empty($setting) ? strtoupper($setting): '';
                }
                $current_staff_store_province_code =  !empty($code) ? $code : $staff_province_code[$value['staff_info_id']] ?? ($store_province_code[$value['sys_store_id']] ?: null);

                //优先使用固化表的工作制
                $week_working_day  = isset($staticStaffInfoData[$unique_key])?  $staticStaffInfoData[$unique_key]['week_working_day'] : $staff_week_working_data[$value['staff_info_id']];

                $staff_ph = [];
                if ($week_working_day == HrStaffInfoModel::WEEK_WORKING_DAY_5) {
                    $staff_ph = array_merge($public_holiday_5[$current_staff_store_province_code]??[], $staffAddPublicHoliday[$value['staff_info_id']]??[]);
                }
                if ($week_working_day == HrStaffInfoModel::WEEK_WORKING_DAY_6) {
                    $staff_ph = array_merge($public_holiday_6[$current_staff_store_province_code]??[], $staffAddPublicHoliday[$value['staff_info_id']]??[]);
                }
                if ($week_working_day == HrStaffInfoModel::WEEK_WORKING_DAY_FREE) {
                    $staff_ph = array_merge($public_holiday_free[$current_staff_store_province_code]??[], $staffAddPublicHoliday[$value['staff_info_id']]??[]);
                }
                $value['week_working_day'] = $week_working_day;

                $box  = new AttendanceStatisticsBox();
                $box->initFreeShiftInfo($free_shift_config);
                $box->setAttendanceData($attendance_data_all[$unique_key]??[]);
                $box->setBusinessTrip($trap_data2date[$unique_key]??[]);
                $box->setBTAttendanceData($reissue_infos[$unique_key]??[]);
                $box->setIsDayOff(isset($offAndRestDay[$unique_key]) && $offAndRestDay[$unique_key]['type'] == HrStaffWorkDaysModel::TYPE_1);
                $box->setIsDayRest(isset($offAndRestDay[$unique_key]) && $offAndRestDay[$unique_key]['type'] == HrStaffWorkDaysModel::TYPE_2);
                $box->setIsPH(in_array($day,$staff_ph));
                $box->setFutureWorkHome($workHomeData[$value['staff_info_id']]??[]);
                $box->setShiftData($shift_info[$unique_key]??[]);
                $box->setLeaveData($apply_info[$unique_key]??[]);
                $box->setStaffInfo($value);
                $box->setCurrentDate($day);
                $result = $box->handle();

                //$background  1 灰色 2 黄色  3 红色
                $is_show_error_attendance_time_1 = $result['is_show_error_attendance_time_1'];
                $is_show_error_attendance_time_2 = $result['is_show_error_attendance_time_2'];
                $is_show_error_attendance_time_3 = $result['is_show_error_attendance_time_3'];
                $is_show_error_attendance_time_4 = $result['is_show_error_attendance_time_4'];
                $background                    = $result['background'];
                $reason                        = $result['reason'];
                $image_path                    = $result['image_path'];
                $late_num                      = $result['late_num'];
                $late_time                     = $result['late_time'];
                $leave_early_num               = $result['leave_early_num'];
                $leave_early_time              = $result['leave_early_time'];
                $holiday_str                   = $result['PH'] ? 'PH' : '';
                $home_str = $result['work_home'] == true ? 'WH' : '';
                $absenteeism_num               = $result['absenteeism_num'];
                $start_at =  self::$t->_("lack_of_work_card");//默认缺卡
                $end_at = self::$t->_("lack_of_work_card");//默认缺卡
                $punch_info = '';
                if($result['attendance_started_at']){
                    $punch_info .= "in:".date('H:i',strtotime($result['attendance_started_at']));
                } elseif ($result['today_not_show_ab']) {
                    $punch_info .= "in:--";
                } else {
                    $is_show_error_attendance_time_1 && $punch_info .= "in:" . $start_at;
                }

                if($result['attendance_end_at']){
                    $punch_info .= " out:".date('H:i',strtotime($result['attendance_end_at']));
                } elseif ($result['today_not_show_ab']) {
                    $punch_info .= " out:--";
                }else{
                    $is_show_error_attendance_time_2  &&  $punch_info .= " out:".$end_at;
                }

                if ($result['shift_type'] == 2) {
                    if ($result['second_attendance_started_at']) {
                        $punch_info .= " in:" . date('H:i', strtotime($result['second_attendance_started_at']));
                    } elseif ($result['today_not_show_ab']) {
                        $punch_info .= " in:--";
                    } else {
                        $is_show_error_attendance_time_3 && $punch_info .= " in:" . $start_at;
                    }
                    if ($result['second_attendance_end_at']) {
                        $punch_info .= " out:" . date('H:i', strtotime($result['second_attendance_end_at']));
                    } elseif ($result['today_not_show_ab']) {
                        $punch_info .= " out:--";
                    } else {
                        $is_show_error_attendance_time_4 && $punch_info .= " out:" . $end_at;
                    }
                }

                $type = '';
                foreach ($result['show_info'] as $tp) {
                    $type .= ' '. self::$t->_($tp);
                }

                if( ($day > date('Y-m-d'))
                    ||
                    ( strtotime($day) < strtotime($value['hire_date']))
                    ||
                    $value['state'] == 2 && strtotime($day) > strtotime($value['leave_date'])
                ){

                    $punch_info       = '';
                    $background       = 0;
                    $type             = '';
                    $absenteeism_num  = 0;
                    $leave_early_num  = 0;
                    $late_num         = 0;
                    $late_time        = 0;
                    $leave_early_time = 0;
                }

                //新增 支援显示文案
                $isSupport = empty($supportData[$unique_key]) ? '' : $supportText;

                $returnData[$value['staff_info_id']][] = [
                    'stat_date'        => $day,
                    'value'            => trim($punch_info ? $punch_info.' '.trim($type) . ' ' . $isSupport : $type . ' ' . $isSupport),
                    'background'       => $background,
                    'reason'           => $reason,
                    'image_path'       => $image_path,
                    'shift_time'       => implode(' ', $result['shift_info']),
                    'shift_type'       => self::$t->_('shift'),
                    'holiday'          => $holiday_str,  //假期
                    'work_from_home'   => $home_str,//居家办公
                    'absenteeism_num'  => $absenteeism_num,
                    'late_num'         => $late_num,
                    'late_time'        => $late_time,
                    'leave_early_num'  => $leave_early_num,
                    'leave_early_time' => $leave_early_time,
                ];
            }
        }
        
        return  $this->handelVacancyData($returnData, $date_range);
    }
    //获取居家办公 未来日期 配置信息
    public function getHomeDates($start, $end, $staffInfos){
        //未来日期 居家办公 取配置
        $today = date('Y-m-d');
        $dateList = [];
        //一半日期 是未来的
        if($end >= $today && $start <= $today){
            $dateList = DateHelper::DateRange(strtotime($today), strtotime($end));
        }
        //所有日期都是未来的
        if($start >= $today){
            $dateList = DateHelper::DateRange(strtotime($start), strtotime($end));
        }
        //都是过去时间 不用取配置 取打卡表数据
        if($end < $today){
            $dateList = [];
        }
        $server = new WorkHomeService();
        $homeData = $server->getWhDays($dateList, $staffInfos);
        return $homeData;
    }



}




