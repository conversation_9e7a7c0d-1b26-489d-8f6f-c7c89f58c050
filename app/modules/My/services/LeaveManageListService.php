<?php

namespace App\Modules\My\Services;


use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Models\backyard\AssetsInventoryStoresModel;
use App\Models\backyard\BackyardBaseModel;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\LeaveManagerModel;
use App\Models\backyard\MsgAssetModel;
use App\Models\backyard\StaffLeaveReasonModel;
use App\Models\backyard\StaffResignModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\fle\StaffInfoPositionModel;
use App\Modules\My\library\CalShortNotice;
use app\repository\HireTypeImportListRepository;
use App\Services\AssetsService;
use App\Services\DictionaryService;
use App\Services\HrJobTitleService;
use App\Services\LeaveManageListService as BaseLeaveManageListService;
use App\Services\SysDepartmentService;
use App\Services\SysStoreService;
use Exception;
use Phalcon\Db;
use App\Services\ExternalDataService;

use App\Modules\My\library\Enums\enums as myEnums;


class LeaveManageListService extends BaseLeaveManageListService
{
    public static $validate_leave_list_params = [
        'name'                   => 'StrLenGeLe:1,50|>>>:name error',
        'working_country'        => 'Arr|ArrLenGe:1|>>>:working_country error', //工作所在国家
        'working_country[*]'     => 'Int|>>>:working_country error',
        'hire_date_begin'        => 'Date|>>>:hire_date_begin error', //入职日期 开始
        'hire_date_end'          => 'Date|>>>:hire_date_end error', //入职日期 结束
        'leave_source'           => 'Int|>>>:leave_source error', //离职来源
        'store_area_id'          => 'Int|>>>:store_area_id error', //所属区域id
        'leave_date_begin'       => 'Date|>>>:leave_date_begin error', //离职日期 开始
        'leave_date_end'         => 'Date|>>>:leave_date_end error', //离职日期 结束
        'state'                  => 'Int|>>>:state error', //在职状态
        'job_title_id'           => 'Arr|ArrLenGe:1|>>>:job_title_id error', //职位
        'job_title_id[*]'        => 'Int|>>>:job_title_id error',
        'store'                  => 'Arr|ArrLenGe:1|>>>:store error', //所属网点
        'store[*]'               => 'StrLenGeLe:1,50|>>>:store error',
        'department'             => 'Int|>>>:department error', //部门
        'remand_state'           => 'Int|>>>:remand_state error', //处理进度
        'assets_remand_state'    => 'Int|>>>:assets_remand_state error', //资产处理状态
        'money_remand_state'     => 'Int|>>>:money_remand_state error', //钱款处理状态
        'approval_status'        => 'Arr|ArrLenGe:1|>>>:approval_status error', //离职申请审批状态
        'approval_status[*]'     => 'Int|>>>:approval_status error',
    ];

    //离职管理员工列表 build sql
    public function buildV2Sql($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(LeaveManagerModel::class, 'hsi.staff_info_id = lm.staff_info_id', 'lm');
        //正式员工和实习生 非子账号  待离职或离职状态
        $builder->where("hsi.formal in (1, 4) and hsi.is_sub_staff = 0  and ((hsi.state = 1 and hsi.wait_leave_state = 1) or hsi.state = 2)");

        $builder = $this->getStaffDataPermission($builder, ['staff_info_id' => $params['user']['id']]);
        if (false === $builder) {
            return false;
        }

        // 雇佣类型
        if (!empty($params['hire_type'])) {
            $builder->inWhere("hsi.hire_type",
                is_array($params['hire_type']) ? $params['hire_type'] : [$params['hire_type']]);
        }

        //姓名 工号搜索
        if (isset($params['name']) && $params['name']) {
            $builder->andWhere("hsi.name like :name: or hsi.staff_info_id like :name: or hsi.name_en like :name:",
                ['name' => '%' . $params['name'] . '%']);
        }

        // 工作所在国家 多选
        if (isset($params['working_country']) && $params['working_country']) {
            $builder->inWhere("hsi.working_country", $params['working_country']);
        }

        //入职日期 开始 区间
        if (isset($params['hire_date_begin']) && $params['hire_date_begin']) {
            $builder->andWhere("hsi.hire_date >= :hire_date_begin:", ['hire_date_begin' => $params['hire_date_begin']]);
        }

        //入职日期 结束 区间
        if (isset($params['hire_date_end']) && $params['hire_date_end']) {
            $builder->andWhere("hsi.hire_date <= :hire_date_end:", ['hire_date_end' => $params['hire_date_end']]);
        }

        //离职日期 开始 区间
        if (isset($params['leave_date_begin']) && $params['leave_date_begin']) {
            $builder->andWhere("hsi.leave_date >= :leave_date_begin: ",
                ['leave_date_begin' => $params['leave_date_begin']]);
        }

        //离职日期 结束 区间
        if (isset($params['leave_date_end']) && $params['leave_date_end']) {
            $builder->andWhere("hsi.leave_date <= :leave_date_end:", ['leave_date_end' => $params['leave_date_end']]);
        }

        //离职来源 单选
        if (!empty($params['leave_source'])) {
            $builder->andWhere("hsi.leave_source = :leave_source:", ['leave_source' => $params['leave_source']]);
        }

        //所属区域
        if (isset($params['store_area_id']) && $params['store_area_id']) {
            $storeIds = $this->getStoresByArea($params['store_area_id']);
            if ($storeIds) {
                $builder->inWhere("hsi.sys_store_id", $storeIds);
            }
        }

        //在职状态
        if (isset($params['state']) && $params['state']) {
            if ($params['state'] == Enums::HRIS_WORKING_STATE_4) { //4 待离职
                $builder->andWhere("hsi.state = 1 and hsi.wait_leave_state = 1");
            } else {
                $builder->andWhere("hsi.state = :state:", ['state' => $params['state']]);
            }
        }

        //职位 多选
        if (isset($params['job_title_id']) && $params['job_title_id']) {
            $builder->inWhere('hsi.job_title', $params['job_title_id']);
        }

        //所属网点 多选
        if (isset($params['store']) && $params['store']) {
            $builder->inWhere("hsi.sys_store_id", $params['store']);
        }

        //所属部门 查询当前部门及自部门 单选
        if (isset($params['department']) && $params['department']) {
            $deptIds = SysService::getDepartmentConditionByParams($params);
            if (!empty($deptIds)) {
                // 如果选择了GROUP CEO时 表示查询全部，则不再拼接部门的查询
                $builder->inWhere("hsi.node_department_id", $deptIds);
            }
        }

        //离职管理处理状态 单选
        if (isset($params['remand_state']) && $params['remand_state']) {
            switch ($params['remand_state']) {
                case self::ASSETS_STATE_UNPROCESSED: //未处理
                    $builder->andWhere("assets_remand_state = 1 and money_remand_state = 1");
                    break;
                case self::ASSETS_STATE_PROCESSING: //处理中
                    $builder->andWhere("(lm.assets_remand_state = 2 or lm.money_remand_state = 2 or (lm.assets_remand_state = 1 and lm.money_remand_state != 1) or (lm.assets_remand_state != 1 and lm.money_remand_state = 1))");
                    break;
                case self::ASSETS_STATE_PROCESSED: //已处理
                    $builder->andWhere("lm.assets_remand_state = 3 and lm.money_remand_state = 3");
                    break;
            }
        }

        //资产处理状态 单选
        if (isset($params['assets_remand_state']) && $params['assets_remand_state']) {
            $builder->andWhere("lm.assets_remand_state = :assets_remand_state:", ['assets_remand_state' => $params['assets_remand_state']]);
        }

        //钱款处理状态 单选
        if (isset($params['money_remand_state']) && $params['money_remand_state']) {
            $builder->andWhere("lm.money_remand_state = :money_remand_state:", ['money_remand_state' => $params['money_remand_state']]);
        }

        //离职申请审批状态 多选
        if (isset($params['approval_status']) && !empty($params['approval_status'])) {
            $tArr = array_map('intval', $params['approval_status']);
            $builder->inWhere("lm.approval_status", $tArr);
            $builder->andWhere("hsi.leave_source in ({approval_leave_source:array})",
                ['approval_leave_source' => LeaveManagerService::$approval_leave_source]);
        }

        return $builder;
    }



    //离职管理列表
    public function staffListV2($params)
    {
        if (!isset($params['page'])) {
            $params['page'] = 1;
        }
        if (!isset($params['size'])) {
            $params['size'] = 50;
        }
        $offset = ($params['page'] - 1) * $params['size'];

        $list_builder = $this->buildV2Sql($params);
        if(false === $list_builder) {
            return ['page_count' => 0, 'rows' => []];
        }

        $list_builder->columns("count(1) as total");
        $totalInfo = $list_builder->getQuery()->getSingleResult();
        $pageCount = intval($totalInfo->total);

        if (empty($pageCount)) {
            return ['page_count' => 0, 'rows' => []];
        }
        $return = ['page_count' => $pageCount, 'rows' => []];

        $list_builder->columns($this->leaveListColumns());
        $list_builder->orderBy("lm.id desc");
        $list_builder->limit($params['size'], $offset);
        $data = $list_builder->getQuery()->execute()->toArray();

        $staffId = array_column($data,'staff_info_id');
        //员工对应审核状态，[staff_id]=state
        $staffApproveStateArr = $this->getApproveState($staffId);
        $msgAssets            = [];
        if ($staffId) {
            $msgAssets = MsgAssetModel::find([
                'conditions' => ' staff_id in ({staff_ids:array}) ',
                'bind'       => [
                    'staff_ids' => $staffId,
                ],
            ])->toArray();
            $msgAssets = array_column($msgAssets, null, 'staff_id');
        }


        $isInventories = [];
        if (isset($params['userinfo']) && $params['userinfo']) {
            // 查询是否是特殊的角色
            $positionsModel = StaffInfoPositionModel::findFirst([
                'conditions' => ' staff_info_id = :staff_id: and position_category in ({positions:array}) ',
                'bind'       => [
                    'staff_id'  => $params['userinfo']['id'],
                    'positions' => [
                        14, // 系统管理员
                        99, // 超级管理员
                        18, // 网点主管
                        21, // 区域经理
                    ],
                ],
            ]);

            // 查询网点是否打开了
            $sysStoreIds    = array_values(array_unique((array_column($data, 'sys_store_id'))));
            $wmsInventories = $sysStoreIds ? AssetsInventoryStoresModel::find([
                'conditions' => ' sys_store_id in ({store_ids:array})',
                'bind'       => ['store_ids' => $sysStoreIds],
            ])->toArray() : [];
            foreach ($wmsInventories as $inventory) {
                $isInventories[$inventory['sys_store_id']] = $inventory;
            }
        }

        $resignData    = $this->getStaffResignMaxList($staffId);

        [$allPrice, $deductAmount] = $this->allPrices($staffId, GlobalEnums::DATA_EXTEND);
        $goodsInfo = !empty($staffId) ? $this->assetsExport($staffId) : [];

        $leaveManagerService = new LeaveManagerService();

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        //默认币种
        $currency = $this->getDefaultCurrency();
        $leave_reason_version_map = $this->getLeaveReasonVersionMap();
        $old_new_leave_reason_map = $this->getOldNewLeaveReasonMap();
        foreach ($data as $item) {
            $areaInfo        = $this->getAreaInfoToList($item['sys_store_id']);
            $sys_store_name  = $this->showStoreName($item['sys_store_id']);
            $job_title_name  = $this->showJobTitleName($item['job_title'], true);
            $department_name = $this->showDepartmentName($item['node_department_id'], true);

            $row = [
                'staff_info_id'       => $item['staff_info_id'],
                'name'                => $item['name'],
                'name_en'             => (string)$item['name_en'],
                'state'               => $item['state'],
                'hire_date'           => formatHrDate($item['hire_date']),
                'sys_store_name'      => $sys_store_name,
                'job_title_name'      => $job_title_name,
                'department_name'     => $department_name,
                'store_area_id'       => $areaInfo['store_area_id'],
                'store_area_text'     => $areaInfo['store_area_text'],
                'working_country'     => $workingCountryList[$item['working_country']] ?? '',
                'state_name'          => $this->getStateName($item['state'], $item['wait_leave_state']),
                'approve_state_text'  => $this->getApproveStateName($staffApproveStateArr[$item['staff_info_id']] ?? 1),
                // 满足四个角色 (系统管理员 超级管理员 网点主管 区域经理) 的用户并且网点也被打开了 查看编辑
                'is_inventory'        => (isset($positionsModel) && $positionsModel && isset($isInventories[$item['sys_store_id']]) && $isInventories[$item['sys_store_id']]['is_switch'] == 1) ? 1 : 0,
                'leave_type'          => $item['leave_type'],
                'leave_type_text'     => static::$t->_('hris_leave_type_'.$item['leave_type']),
                'leave_scenario_text' => $this->getLeaveScenarioByCode($item['leave_scenario']),
                'contract_expiry_date' => $item['contract_expiry_date'],
                'hire_type'           => $item['hire_type'],
                'hire_type_text'      => static::$t->_('hire_type_'.$item['hire_type']),
            ];
            $row['leave_reason'] = $item['leave_reason'];
            $row['leave_reason_text'] = '';
            if (!empty($row['leave_reason'])) {
                if (isset($leave_reason_version_map[$row['leave_reason']]) && $leave_reason_version_map[$row['leave_reason']] != StaffLeaveReasonModel::LEAVER_REASON_VERSION_NEW) {
                    $leave_reason_key = $this->getReasonKeyByCode($old_new_leave_reason_map[$row['leave_reason']]);
                } else {
                    $leave_reason_key = $this->getReasonKeyByCode($row['leave_reason']);
                }
                $row['leave_reason_text'] = static::$t->_($leave_reason_key);
            }

            $row['leave_date'] =  formatHrDate($item['leave_date']);
            $row['resignation_notice'] = $item['resignation_notice'];
            $row['last_work_date']     = date('Y-m-d', strtotime($item['leave_date']." -1 days "));
            $row['short_notice']       = $item['short_notice'];

            if ($row['resignation_notice']) {
                $tmp                       = explode(" ", $row['resignation_notice']);
                $row['resignation_notice'] = $tmp[0].self::$t->_($tmp[1]);
            }

            $row['wait_leave_state'] = $item['wait_leave_state'];
            //未归还钱款总额
            $row['all_price'] = $allPrice[$item['staff_info_id']] ? bcdiv($allPrice[$item['staff_info_id']], 100, 2) : 0;
            if ($item['is_new_assets_remand_state'] == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES) {
                $row['deduct_amount'] = $deductAmount[$item['staff_info_id']] ? bcdiv($deductAmount[$item['staff_info_id']], 100, 2) : 0;
            } else {
                $row['deduct_amount'] = $goodsInfo[$item['staff_info_id']] ? $goodsInfo[$item['staff_info_id']]['all_price'] : 0;
            }
            $row['deduct_amount'] = sprintf('%s %s', $row['deduct_amount'], $currency);

            //资产处理进度
            $row['assets_remand_state'] = (int)$item['assets_remand_state'] ? (int)$item['assets_remand_state'] : self::ASSETS_STATE_UNPROCESSED;
            //钱款处理进度
            $row['money_remand_state'] = (int)$item['money_remand_state'] ? (int)$item['money_remand_state'] : self::ASSETS_STATE_UNPROCESSED;
            //处理进度
            $row['remand_state'] = $this->leaveStateMaps((int)$item['assets_remand_state'], (int)$item['money_remand_state']);
            //离职日期
            $row['leave_approval_date'] = $item['lm_leave_date'] && $item['lm_leave_date'] != "0000-00-00 00:00:00" ? date("Y-m-d", strtotime($item['lm_leave_date'])) : "";
            //离职申请审批状态
            if(in_array($item['leave_source'],[self::LEAVE_SOURCE_BACKYARD,self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT])) {
                $row['approval_status']      = (int)$item['approval_status'] ? (int)$item['approval_status'] : 0;
                $row['approval_status_text'] = $item['approval_status'] ? self::$t->_("by_state_".$item['approval_status']) : "";
            } else {
                $row['approval_status']      = 0;
                $row['approval_status_text'] = "";
            }
            //主管是否操作过0未操作 1操作过
            $row['is_has_superior'] = (int)$item['is_has_superior'] ? (int)$item['is_has_superior'] : 0;
            if (($row['state'] == 2 && $row['is_has_superior'] == 0) || !in_array($item['job_title'], [
                    AssetsService::$job_title['bike_courier'],
                    AssetsService::$job_title['van_courier'],
                    AssetsService::$job_title['shop_officer'],
                    AssetsService::$job_title['branch_supervisor'],
                    AssetsService::$job_title['shop_supervisor'],
                ]) || !isset($msgAssets[$item['staff_info_id']])) {
                // 已离职并且主管处理情况为未处理
                // 包含了 历史数据 与 手动改离职
                $row['is_has_superior_text'] = "";
            } else {
                $row['is_has_superior_text'] = (int)$item['is_has_superior'] ? self::$t->_("assets_state_".self::ASSETS_STATE_PROCESSED) : self::$t->_("assets_state_".self::ASSETS_STATE_UNPROCESSED);
            }

            $row['assets_remand_state_text'] = self::$t->_('assets_state_'.(int)$item['assets_remand_state']);
            $row['money_remand_state_text']  = self::$t->_('assets_state_'.(int)$item['money_remand_state']);
            $row['remand_state_text']        = self::$t->_('assets_state_'.$this->leaveStateMaps((int)$item['assets_remand_state'],
                    (int)$item['money_remand_state']));
            $row['is_has_employ_operater']   = !in_array($item['sys_department_id'], [
                self::$has_employ_operater_department['fulfillment'],
                self::$has_employ_operater_department['flash_money'],
                self::$has_employ_operater_department['flash_home'],
                self::$has_employ_operater_department['flash_logistic'],
            ]) && $item['state'] == 2 ? 1 : 0;

            //离职来源
            $row['leave_source']      = $item['leave_source'];
            $row['leave_source_text'] = "";
            if (!empty($item['leave_source'])) {
                $leaveSourceKey = $leaveManagerService->getLeaveSource($item['leave_source']);
                $row['leave_source_text'] = self::$t->_($leaveSourceKey);
            }

            //新需求 来源非“BY申请离职”以及职位一下职位（Bike courier 13 /van courier 110/Branch Supervisor 16/shop officer 98/ Shop Supervisor 101）不显示某些字段 并且关系
            $row['is_show'] = 0;
            $forbidden      = [13, 110, 16, 98, 101];
            //来源 '0=空，-1其他。6批量导入。其他同hold来源,1.新增,2.hirs,3旷工3天及以上，4未缴纳公款，5backyard提交申请，'
            if ($item['leave_source'] == 5 && in_array($item['job_title'], $forbidden)) {
                $row['is_show'] = 1;
            }

            $row['is_show_cancel_staff_resign'] = 0; // 默认不显示
            $row['staff_resign_leave_date'] = !empty($resignData[$item['staff_info_id']]['leave_date']) ? $resignData[$item['staff_info_id']]['leave_date'] : '';
            $today = date('Y-m-d');
            if ($item['state'] == Enums::HRIS_WORKING_STATE_2 && $item['leave_source'] != self::LEAVE_SOURCE_BACKYARD && $item['approval_status'] == 2 && $resignData[$item['staff_info_id']]['leave_date'] > $today){
                // 非BY申请离职 审批状态已同意 申请的离职日期大于当前日期 显示同时撤销by离职申请 和不需要撤销 按钮
                $row['is_show_cancel_staff_resign'] = 1;
            }

            $return['rows'][] = $row;
        }

        return $return;
    }


    /**
     * 导出
     * @param $params
     * @return array
     * @throws Exception
     */
    public function export($params): array
    {
        $data_s       = [];
        $list_builder = $this->buildV2Sql($params);
        if (false === $list_builder) {
            return [];
        }

        //工作所在国家
        $workingCountryList = (new DictionaryService())->getTotalDictionaryLabelByDictCode('working_country');
        $workingCountryList = array_column($workingCountryList, 'label', 'value');

        $i               = 1;
        $builder_columns = $this->exportLeaveListColumns();
        $list_builder->columns($builder_columns);
        $list_builder->orderBy("lm.id desc");
        $page_size = 500;
        if (get_runtime() == 'dev') {
            $page_size = 50;
        }
        $leave_reason_version_map = $this->getLeaveReasonVersionMap();
        $old_new_leave_reason_map = $this->getOldNewLeaveReasonMap();
        $leave_scenario_map       = $this->getLeaveScenarioMap();
        while (true) {
            $offset = ($i - 1) * $page_size;
            $list_builder->limit($page_size, $offset);
            $data = $list_builder->getQuery()->execute()->toArray();
            if (empty($data)) {
                break;
            }
            $i++;

            $staffIds        = array_column($data, 'staff_info_id');
            $staff_store_ids = array_column($data, 'sys_store_id');

            // 调用oa 获取 借款未归还金额 备用金未归还金额
            $notReturnAmount = (new ExternalDataService())->batchGetUserOutstandingAmount($staffIds);
            // 快递员未回款
            $receivableAmounts = $this->receivableAmounts($staffIds);
            // 其他 出纳应汇款
            $monies = $this->getMoneys($staffIds);
            // 出纳应汇款 cashier_money
            [$cashier, $cashier2] = $this->getAmounts($staffIds);
            //所属区域
            $storeAreas = $this->storeAreas($staff_store_ids);
            // 资产 钱款用户ID
            $goods     = $this->assetsExport($staffIds);
            $goods_new = $this->getNewAssetsExport($staffIds);
            // 最后离职时间
            $resignsTime = $this->lastResign($staffIds);
            //公司解约个人代理
            $companyTerminationContractInfo = $this->companyTerminationContractInfo($staffIds);

            foreach ($data as &$datum) {
                if ($datum['resignation_notice']) {
                    $tmp                         = explode(" ", $datum['resignation_notice']);
                    $datum['resignation_notice'] = $tmp[0].self::$t->_($tmp[1]);
                }

                $storeAreasBelong = $this->getAreaInfoToList($datum['sys_store_id'])['store_area_text'];
                // 快递员未回款
                $_receiveable_amount = isset($receivableAmounts[$datum['staff_info_id']]) && $receivableAmounts[$datum['staff_info_id']] ? bcdiv((int)($receivableAmounts[$datum['staff_info_id']]['receiveable_amount']),
                    100, 2) : 0;
                // 出纳应回款
                if (isset($monies[$datum['staff_info_id']]) && $monies[$datum['staff_info_id']]['cashier_money'] != '-1') {
                    $_parcel_amount_cod_amount = bcdiv((int)$monies[$datum['staff_info_id']]['cashier_money'], 100, 2);
                } else {
                    $cashier_parcel_amount     = isset($cashier[$datum['staff_info_id']]) ? (int)$cashier[$datum['staff_info_id']]['parcel_amount'] : 0;
                    $cashier2_cod_amount       = isset( $cashier2[$datum['staff_info_id']]) ? (int)$cashier2[$datum['staff_info_id']]['cod_amount'] : 0;
                    $_parcel_amount_cod_amount = bcdiv($cashier_parcel_amount + $cashier2_cod_amount, 100, 2);
                }

                // 借款未归还
                $_loanNotReturns = $notReturnAmount['loan_amount'][$datum['staff_info_id']] ?? 0;
                // 网点备用金
                $_revserveFunds = $notReturnAmount['reserve_fund_amount'][$datum['staff_info_id']] ?? 0;
                // 其他
                $_other_money = !empty($monies[$datum['staff_info_id']]) && $monies[$datum['staff_info_id']]['money'] ? bcdiv($monies[$datum['staff_info_id']]['money'], 100, 2) : 0;

                $leave_source = "";
                if (!empty($datum['leave_source'])) {
                    $leaveSourceKey = $this->getLeaveSource($datum['leave_source']);
                    $leave_source = self::$t->_($leaveSourceKey);
                }
                $leave_reason_key_old = '';
                $leave_reason_key = '';
                if (!empty($datum['leave_reason'])) {
                    if (isset($leave_reason_version_map[$datum['leave_reason']]) && $leave_reason_version_map[$datum['leave_reason']] != StaffLeaveReasonModel::LEAVER_REASON_VERSION_NEW) {
                        $leave_reason_key_old = $this->getReasonKeyByCode($datum['leave_reason']);
                        $leave_reason_key     = $this->getReasonKeyByCode($old_new_leave_reason_map[$datum['leave_reason']]);
                    } else {
                        $leave_reason_key = $this->getReasonKeyByCode($datum['leave_reason']);
                    }
                    //个人代理的离职原因 新旧一样
                    if (!isset($leave_reason_version_map[$datum['leave_reason']])) {
                        $leave_reason_key_old = $leave_reason_key;
                    }
                }
                $leave_scenario_text = '';
                if (!empty($datum['leave_scenario'])) {
                    $leave_scenario_text = $leave_scenario_map[$datum['leave_scenario']] ?? '';
                }


                $assets_operate_remark = '';
                $asset_name            = '';
                $all_price             = 0;
                if ($datum['is_new_assets_remand_state'] == LeaveManagerModel::IS_NEW_ASSETS_REMAND_STATE_YES) {
                    if (isset($goods_new[$datum['staff_info_id']])) {
                        $asset_name            = $goods_new[$datum['staff_info_id']]['asset_name'];
                        $all_price             = $goods_new[$datum['staff_info_id']]['all_deduct_amount'];
                        $assets_operate_remark = $goods_new[$datum['staff_info_id']]['asset_department_remark'];
                    }
                } else {
                    if (isset($goods[$datum['staff_info_id']])) {
                        $assets_operate_remark = $datum['assets_operate_remark'];
                        $asset_name            = $goods[$datum['staff_info_id']]['goods'];
                        $all_price             = $goods[$datum['staff_info_id']]['all_price'];
                    }
                }

                if(mb_strlen($asset_name) > 200) {
                    $asset_name = mb_substr($asset_name, 0, 200) . '......';
                }
                $tmpCompanyTerminationContractInfo = $companyTerminationContractInfo[$datum['staff_info_id']] ?? [];
                //“解约类型”；“解约原因”
                if (!empty($tmpCompanyTerminationContractInfo)) {
                    $datum['leave_reason_remark'] =  self::$t->_('termination_type_' . $tmpCompanyTerminationContractInfo['termination_type']);
                    if ($tmpCompanyTerminationContractInfo['reason']) {
                        $datum['leave_reason_remark'] .= ';' . self::$t->_('termination_reason_' . $tmpCompanyTerminationContractInfo['reason']);
                    } else {
                        $datum['leave_reason_remark'] .= ';' . $tmpCompanyTerminationContractInfo['remark'];
                    }
                    $datum['leave_reason_remark'] = trim($datum['leave_reason_remark'],';');
                }

                $item = [];
                // 工号
                $item[] = $datum['staff_info_id'];
                // 姓名
                $item[] = $datum['name'];
                // 英文名称
                $item[] = $datum['name_en'];
                // 职位
                $item[] = $this->showJobTitleName($datum['job_title'],true);
                // 部门
                $item[] = $this->showDepartmentName($datum['node_department_id'],true);
                // 工作所在国家
                $item[] = $workingCountryList[$datum['working_country']] ?? '';
                // 所属网点
                $item[] = $this->showStoreName($datum['sys_store_id']);
                // 所属区域
                $item[] = $storeAreasBelong;
                // 在职状态
                $item[] = $this->getStateName($datum['state'], $datum['wait_leave_state']);
                //雇佣类型
                $item[] = self::$t->_('hire_type_' . $datum['hire_type']);
                // 入职日期
                $item[] = date('Y-m-d', strtotime($datum['hire_date']));;
                // 离职/待离职日期
                $item[] = date('Y-m-d', strtotime($datum['leave_date']));

                // 未还资产
                $item[] = $asset_name;
                // 总价值(资产总扣费金额)
                $item[] = $all_price;

                // 资产处理状态
                $item[] = static::$t->_('assets_state_'.$datum['assets_remand_state']);;
                // 未归还钱款(未归还钱款总额)
                $item[] = $_other_money + $_revserveFunds + $_loanNotReturns + $_parcel_amount_cod_amount + $_receiveable_amount;
                // 快递员未回款
                $item[] = $_receiveable_amount;
                // 出纳应汇款
                $item[] = $_parcel_amount_cod_amount;
                // 借款未归还金额
                $item[] = $_loanNotReturns;
                // 网点备用金未归还金额q
                $item[] = $_revserveFunds;
                // 其他
                $item[] = $_other_money;
                // 钱款处理状态
                $item[] = self::$t->_('assets_state_'.(int)$datum['money_remand_state']);;
                // 来源
                $item[] = $leave_source;
                // 离职申请日期
                $item[] = $datum['leave_source'] == self::LEAVE_SOURCE_BACKYARD ? ($resignsTime[$datum['staff_info_id']] ?? '') :'';
                // 离职类型
                $item[] = empty($datum['leave_type']) ? '' : static::$t->_('resign_type_'.$datum['leave_type']);;
                // 离职原因(旧)
                $item[] = $leave_reason_key_old ? static::$t->_($leave_reason_key_old):'';
                // 离职原因
                $item[] = $leave_reason_key ? static::$t->_($leave_reason_key):'';
                $item[] = $leave_scenario_text ? : '';
                // 离职原因备注
                $item[] = $datum['leave_reason_remark'];
                //离职通知期
                $item[]  = $datum['resignation_notice'];
                //最后工作日
                $item[]  = date('Y-m-d', strtotime($datum['leave_date']." -1 days "));
                //short_notice
                $item[]  = $datum['short_notice'];
                // 备注
                $item[] = $assets_operate_remark;
                // 处理进度
                $item[] = self::$t->_('assets_state_'.$this->leaveStateMaps((int)$datum['assets_remand_state'], (int)$datum['money_remand_state']));

                $data_s[] = $item;
            }
        }
        return $data_s;
    }

    /**
     * 资产申请书 管理书pdf
     * @param integer $staffId 员工工号
     * @param integer $type 1资产申请单 2资产管理同意书
     * @return array|bool|mixed|null
     */
    public function deduction_notice($staffId, $type)
    {
        $client = new ApiClient('oa_rpc', '', 'asset_transfer_get_asset_pdf', 'en');
        $params = [
            'user_id' => $staffId,
            'type' => $type,
            'platform' => 2,
        ];
        $client->setParamss($params);
        return $client->execute();
    }


    /**
     * 获取short规则
     * @param $datum
     * @param $resignation
     * @param $resignData
     * @param $probationData
     * @return int
     */
    public function getShortNoticeRule($datum , $resignation ,$resignData, $probationData)
    {
        $type = 0;

        if ($resignation && $datum) {
            $probationInfo = $probationData[$datum['staff_info_id']] ?? [];
            $resignInfo = $resignData[$datum['staff_info_id']] ?? [];

            //by数据和离职数据同时存在
            if (
                !empty($probationInfo) && !empty($resignInfo) && ($resignInfo['source'] == StaffResignModel::SOURCE_BY)
                && in_array($resignInfo['status'],[Enums::audit_status_2, Enums::audit_status_5])
            ) {
                if (strtotime($probationInfo['created_at']) > strtotime($resignInfo['created_at'])) {
                    //以未通过试用期为准
                    $type = 2;
                    $leaveDate = $probationInfo['leave_date'];
                } else {
                    //以BY为准
                    $type = 1;
                    $leaveDate = $resignInfo['leave_date'];
                }

                //小于离职日期走旷工逻辑
                if (strtotime($leaveDate) < strtotime($datum['leave_date'])) {
                    $type = 0;
                }
            } elseif(
                !empty($resignInfo)
                && ($resignInfo['source'] == StaffResignModel::SOURCE_BY)
                && in_array($resignInfo['status'],[Enums::audit_status_2, Enums::audit_status_5])
                && (strtotime($resignInfo['leave_date']) >= strtotime($datum['leave_date']))
            ) {
                $type = 1;
            } elseif(!empty($probationInfo) && (strtotime($probationInfo['leave_date']) >= strtotime($datum['leave_date']))) {
                $type = 2;
            }
        }

        return $type;
    }

    /**
     * 根据网点获取对应 所属区域
     * @param $sysStoreId
     * @return mixed
     */
    public function getAreaInfoToList($sysStoreId)
    {
        $result['store_area_id'] = 0;
        $result['store_area_text'] = '';
        if($sysStoreId == GlobalEnums::HEAD_OFFICE_ID){
            return $result;
        }
        $sorting_no = array_flip(myEnums::$store_sorting_no_select);

        $data = $this->temp()[$sysStoreId];

        $result['store_area_id']   =  $sorting_no[$data['sorting_no']];
        $result['store_area_text'] = $data['sorting_no'];
        return $result;
    }


    /**
     * 计算 short notice
     * @param $staff_info_id
     * @return bool
     * @throws Exception
     */
    public function calShortNotice($staff_info_id): bool
    {
        //开事务
        $db = BackyardBaseModel::beginTransaction($this);
        try {
            $leaveManagerModel       = LeaveManagerModel::findFirst(
                [
                    "conditions" => "staff_info_id = :staff_info_id:",
                    "bind"       => [
                        "staff_info_id" => $staff_info_id,
                    ],
                ]
            );
            if (empty($leaveManagerModel)) {
                $this->logger->notice($staff_info_id . ' leave_manager表未查询到');
                return true;
            }

            $result                                = $this->getStaffShortNoticeData($staff_info_id);
            $this->logger->info(['result'=>$result,'staff_info_id'=>$staff_info_id]);
            $leaveManagerModel->resignation_notice = $result['resignation_notice'];
            $leaveManagerModel->last_work_date     = $result['last_work_date'];
            $leaveManagerModel->short_notice       = $result['short_notice'];
            $leaveManagerModel->save();
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 计算 short notice
     * @param $staff_info_id
     * @return bool
     * @throws Exception
     */
    public function calShortNoticeV2($staff_info_id): bool
    {
        //开事务
        $db = BackyardBaseModel::beginTransaction($this);
        try {
            $leaveManagerModel       = LeaveManagerModel::findFirst(
                [
                    "conditions" => "staff_info_id = :staff_info_id:",
                    "bind"       => [
                        "staff_info_id" => $staff_info_id,
                    ],
                ]
            );
            if (empty($leaveManagerModel)) {
                $this->logger->notice($staff_info_id . ' leave_manager表未查询到');
                return true;
            }

            $result                                = (new CalShortNotice())->fire($staff_info_id);
            $this->logger->info(['result'=>$result,'staff_info_id'=>$staff_info_id]);
            $leaveManagerModel->resignation_notice = $result['resignation_notice'];
            $leaveManagerModel->last_work_date     = $result['last_work_date'];
            $leaveManagerModel->short_notice       = $result['short_notice'];
            $leaveManagerModel->save();
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * @deprecated
     *
     * 获取员工的shot notice
     * @param $staff_info_id
     * @return array
     */
    protected function getStaffShortNoticeData($staff_info_id): array
    {
        $result['resignation_notice'] = null;//离职通知期

        $result['short_notice']       = 0;   // short notice

        $staffInfo     = (new StaffInfoService())->getStaffInfoByIdv4($staff_info_id);
        $resignData    = $this->getStaffResignMaxList([$staff_info_id]);
        $probationData = $this->getStaffProbationMaxList([$staff_info_id]);

        if ($staffInfo['leave_source'] == self::LEAVE_SOURCE_BACKYARD) {
            if (!empty($resignData[$staffInfo['staff_info_id']])
                && in_array($resignData[$staffInfo['staff_info_id']]['status'],
                    [self::APPROVAL_STATUS_WAIT, self::APPROVAL_STATUS_APPROVED, self::APPROVAL_STATUS_TIME_OUT,])
                && (($staffInfo['state'] == 1 && $staffInfo['wait_leave_state'] == 1) || $staffInfo['state'] == 2)
            ) {
                $result['resignation_notice'] = $resignData[$staffInfo['staff_info_id']]['resignation_notice'];// 离职通知期
                // last work date
                // 离职申请来源  最后工作日
                // 批量导入来源 导入离职的日期-1
                // 三天未出勤停职，未缴纳公款 犯罪记录， 其他 同上
                $result['last_work_date'] = $resignData[$staffInfo['staff_info_id']]['last_work_date'];        // 最后工作日期

                // short_notice
                // 离职申请来源 离职通知期离职时间 - 员工的离职时间
                // 三天未出勤离职来源 并且 有离职申请 离职通知期-实际离职日期
                // 三天未出勤停职 未回公款 犯罪记录 离职通知期天数
                $result['short_notice'] = 0;
                if (!empty($resignData[$staffInfo['staff_info_id']]['resignation_leave_day']) && $resignData[$staffInfo['staff_info_id']]['resignation_leave_day'] > $resignData[$staffInfo['staff_info_id']]['leave_date']) {
                    $diffDays               =
                        round((strtotime($resignData[$staffInfo['staff_info_id']]['resignation_leave_day']) - strtotime($resignData[$staffInfo['staff_info_id']]['leave_date'])) / (86400));
                    $result['short_notice'] = $diffDays; //  离职通知期天数
                }

                //  1. 对于离职原因是“要去做个人代理”的正式员工，Short notice先按照正常员工的计算规则在离职员工管理展示数据，但是如果此员工在离职日期当天以及后2天内被到岗确认，那么更新Short notice=0。
                if (!empty($resignData[$staffInfo['staff_info_id']]['reduction_short_notice'])) {
                    $result['short_notice'] = 0; //  离职通知期天数
                }
            }
        } else {
            $resignation = $this->resignationNotice($staffInfo, $staffInfo['staff_info_id']);

            if (($staffInfo['state'] == 1 && $staffInfo['wait_leave_state'] == 1 || $staffInfo['state'] == 2) && $staffInfo['leave_date']) {
                $result['resignation_notice'] = $resignation ? $resignation['resignation_notice'] : null;
                $result['last_work_date']     = date('Y-m-d', strtotime($staffInfo['leave_date'] . " -1 days "));
                $result['short_notice']       = 0;
                if ($resignation && $resignation['resignation_notice']) {
                    $result['short_notice'] = StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? 0;
                }
            }

            if ($staffInfo['leave_source'] == self::LEAVE_SOURCE_OFF3DAYS && $staffInfo['leave_date']) {
                //最新一条审批通过的BY申请离职的离职日期大于等于旷工导致的离职日期，按照离职来源是BY申请离职处理
                //小于按照离职来源是3天旷工离职计算

                $ruleType = $this->getShortNoticeRule($staffInfo, $resignation, $resignData, $probationData);
                //echo max(round(((strtotime('2024-03-08') - strtotime('2024-03-10')) / (86400)) + 45),0);
                if ($ruleType == 1) {
                    // 三天未出勤 离职 并且申请过离职申请
                    //$datum['short_notice'] = max(round(((strtotime(explode(' ', $resignData[$datum['staff_info_id']]['created_at'])[0]) - strtotime($datum['leave_date'])) / (86400)) + $datum['short_notice']),0);
                    $result['short_notice'] = max(round(((strtotime(show_time_zone($resignData[$staffInfo['staff_info_id']]['created_at'],
                                    'Y-m-d')) - strtotime($staffInfo['leave_date'])) / (86400)) + $result['short_notice']),
                        0);
                    //如果计算出来的short_notice > 离职通知期天数 则 short_notice = 离职通知期天数
                    if ($result['short_notice'] > (StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? 0)) {
                        $result['short_notice'] = StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? 0;
                    }
                } elseif ($ruleType == 2) {
                    $result['short_notice'] = max(round(((strtotime($probationData[$staffInfo['staff_info_id']]['leave_date']) - strtotime($staffInfo['leave_date'])) / (86400))),
                        0);
                    //如果计算出来的short_notice > 离职通知期天数 则 short_notice = 离职通知期天数
                    if ($result['short_notice'] > (StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? 0)) {
                        $result['short_notice'] = StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? 0;
                    }
                } else {
                    //小于按照离职来源是3天旷工离职计算
                    //  1. 雇佣类型=月薪制特殊合同工/日薪制特殊合同工/时薪制特殊合同工
                    //contract_expiry_date
                    $result['short_notice'] = StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? 0;
                    if (!empty($staffInfo['contract_expiry_date']) && in_array($staffInfo['hire_type'], [
                            HrStaffInfoModel::HIRE_TYPE_2,
                            HrStaffInfoModel::HIRE_TYPE_3,
                            HrStaffInfoModel::HIRE_TYPE_4,
                            HrStaffInfoModel::HIRE_TYPE_UN_PAID,
                        ])) {
                        $short_notice = round((strtotime($staffInfo['contract_expiry_date']) - strtotime($staffInfo['leave_date'])) / 86400) + 1;
                        if ($short_notice <= $result['short_notice']) {
                            $result['short_notice'] = max($short_notice,0);
                        }
                    }
                }
            }

            //批量导入离职/全局离职/员工管理修改
            if (in_array($staffInfo['leave_source'],
                [self::LEAVE_SOURCE_BATCH, self::LEAVE_SOURCE_GLOBAL, self::LEAVE_SOURCE_HRIS_EDIT])) {
                //离职原因 贪污腐败/连续旷工三天以上
                if (in_array($staffInfo['leave_reason'], [20, 21])) {
                    $result['short_notice'] = StaffResignModel::$short_notice[$resignation['resignation_notice']] ?? 0;
                } else {
                    $result['short_notice'] = 0;
                }
            }

            //未通过试用期
            if ($staffInfo['leave_source'] == self::LEAVE_SOURCE_PROBATION) {
                $result['short_notice'] = 0; //离职通知期天数
            }


            //公司解约个人代理
            if ($staffInfo['leave_source'] == self::LEAVE_SOURCE_COMPANY_TERMINATION_CONTRACT) {
                $result['short_notice'] = 0; //离职通知期天数
            }
            //人脸黑名单
            if ($staffInfo['leave_source'] == 14) {
                $result['short_notice'] = 0; 
            }
        }

        // 离职来源=合同到期并且离职类型=合同终止 short_notice强制给0
        if ($staffInfo['leave_source'] == self::LEAVE_SOURCE_CONTRACT_EXPIRE and $staffInfo['leave_type'] == self::LEAVE_TYPE_EXPIRE) {
            $result['short_notice'] = 0;
        }

        if ($staffInfo['hire_type'] == HrStaffInfoModel::HIRE_TYPE_PART_TIME_AGENT) {
            $result['resignation_notice'] = '0 day';
            $result['short_notice']       = 0;
        }
        $result['short_notice'] = max($result['short_notice'],0);

        return $result;
    }



}