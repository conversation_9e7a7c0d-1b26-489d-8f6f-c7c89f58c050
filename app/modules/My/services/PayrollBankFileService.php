<?php


namespace App\Modules\My\Services;


use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Exception\BusinessException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\StaffPayrollCompanyInfoModel;
use App\Models\backyard\StaffPayrollModel;
use App\Models\backyard\StaffSalaryEpfSocsoModel;
use App\Models\backyard\StaffSalaryItemsModel;
use App\Models\backyard\StaffSalaryTaxModel;
use App\Modules\My\library\Enums\SalaryEnums;
use App\Repository\DictionaryRepository;
use App\Services\PayrollManageService;

class PayrollBankFileService extends BaseService
{
    public $companyInfo     = [];
    public $staffInfo       = [];
    public $staffPayroll    = [];
    public $month           = '';
    public $bankType        = null;
    public $fileType        = null;
    public $companyMess     = '';
    public $tapeId          = '';
    public $remark          = '';
    public $appointStaffIds = null;
    public $appointPayDate  = '';

    /**
     * 银行文件下载列表
     * @param $param
     * @return array
     * @throws BusinessException
     */
    public function list($param): array
    {
        $list = [];
        $companyIds = (new PayrollManageService())->getStaffManageCompanyIds($param['operate_id'] ?? 0);
        if (empty($companyIds)) {
            throw new BusinessException(static::$t->_('no_data_permission'));
        }
        if (!empty($param['company_id']) && !in_array($param['company_id'],$companyIds)) {
            throw new BusinessException(static::$t->_('no_data_permission'));
        }
        $map  = SalaryEnums::$payrollBankFIleMap;
        if ($param['period_type'] != StaffPayrollModel::RUN_TYPE_1) {
            $map = [SalaryEnums::PAYROLL_BANK_FILE_SALARY => 'Salary'];
        }
        foreach ($map as $bankFileType => $bankFileName) {
            $list[] = [
                'file_type'        => $bankFileType,
                'file_name'        => $bankFileName,
                'month'            => $param['month'],
                'period_type'      => $param['period_type'],
                'period_type_name' => static::$t->_(StaffPayrollModel::$runTypeMap[$param['period_type']] ?? ''),
                'company_id'       => $param['company_id'],
                'company_name'     => SalaryEnums::$companyMap[$param['company_id']],
            ];
        }
        return $list;
    }

    public function downloadFileTxt($params): array
    {
        $periodType        = $params['period_type'];
        $this->month       = $params['month'];
        $companyId         = $params['company_id'];
        $this->bankType    = $params['bank_type'];
        $this->fileType    = $params['file_type'];
        $this->companyMess = $params['company_mess'];
        $this->tapeId      = $params['tape_id'];
        $this->remark      = $params['remark'];
        if (in_array($this->fileType, [
                SalaryEnums::PAYROLL_BANK_FILE_SALARY,
                SalaryEnums::PAYROLL_BANK_FILE_EPF,
            ]) && $this->bankType == SalaryEnums::PAYROLL_BANK_CIMB
            || ($this->bankType == SalaryEnums::PAYROLL_BANK_OCBC && $this->fileType == SalaryEnums::PAYROLL_BANK_FILE_SALARY)
        ) {
            $this->appointStaffIds = $params['staff_ids'];
            $this->appointPayDate  = $params['pay_date'];
        }
        $this->companyInfo  = $this->getCompanyInfo($companyId);
        $this->staffPayroll = $this->getStaffPayroll($periodType, $this->month, $companyId);
        $staffInfoIds       = array_column($this->staffPayroll, 'staff_info_id');
        $fileName           = SalaryEnums::$payrollBankFIleMap[$this->fileType] . '_' . SalaryEnums::$payrollBankMap[$this->bankType] . date('YmdHis') . '.txt';
        if (empty($staffInfoIds)) {
            return ['list' => [], 'file_name' => $fileName];
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_SALARY) {
            $this->staffInfo = $this->getStaffInfoForSalary($staffInfoIds);
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_EIS) {
            $this->staffInfo = $this->getStaffInfoForEIS($staffInfoIds);
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_SOCSO) {
            $this->staffInfo = $this->getStaffInfoForSOCSO($staffInfoIds);
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_EPF) {
            $this->staffInfo = $this->getStaffInfoForEPF($staffInfoIds);
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_TAX) {
            $this->staffInfo = $this->getStaffInfoForTax($staffInfoIds);
        }
        $fileTypeName         = strtolower(SalaryEnums::$payrollBankFIleMap[$this->fileType]);
        $bankName             = strtoupper(SalaryEnums::$payrollBankMap[$this->bankType]);
        $fileTypeBankDataName = $fileTypeName . $bankName . 'Data';
        /**
         * @var $fileTypeBankDataName self::salaryCIMBData|self::salaryOCBCData|self::salaryHSBCData|self::eisCIMBData|self::eisOCBCData|self::eisHSBCData|self::socsoCIMBData
         * @var $fileTypeBankDataName self::socsoOCBCData|self::socsoHSBCData|self::epfCIMBData|self::epfOCBCData|self::epfHSBCData|self::taxCIMBData|self::taxOCBCData|self::taxHSBCData
         */
        if (method_exists($this, $fileTypeBankDataName)) {
            $data = $this->$fileTypeBankDataName();
        }
        return ['list' => $data ?? [], 'file_name' => $fileName];
    }

    /**
     * CIMB银行 salary
     */
    public function salaryCIMBData(): array
    {
        $cimbNo      = $this->companyInfo['id'] == SalaryEnums::LNT ? '36127' : '21760';
        $companyMess = ($this->companyInfo['company_name'] ?? '') . ' (' . $cimbNo . ')';
        $companyMess = strFillIn($companyMess, ' ', 40, 'r');
        $payDate     = $this->appointPayDate;
        $payDate     = date('dmY', strtotime($payDate));
        $line[]      = '01' . $cimbNo . $companyMess . $payDate . '****************  ';
        $peopleNumber = 0;
        $netPayTotal  = 0;
        $blankSpace24 = str_repeat(' ', 24);
        $blankSpace8  = str_repeat(' ', 8);
        foreach ($this->staffInfo as $item) {
            if (empty($this->staffPayroll[$item['staff_info_id']]['paid_date'])) {
                continue;
            }
            $bankTypeCode = (SalaryEnums::$bankTypeCodeForCIMB[$item['bank_type']] ?? '00');
            $bankTypeCode = strFillIn($bankTypeCode, '0', 7, 'r');
            $bankNo       = strFillIn($item['bank_no'], ' ', 16, 'r');
            $staffName    = strFillIn(strtoupper($item['bank_no_name']), ' ', 40, 'r');
            $netPay       = strFillIn($this->staffPayroll[$item['staff_info_id']]['net_pay'] * 100, '0', 11);
            $staffId      = strFillIn($item['staff_info_id'], ' ', 6, 'r');
            $identity     = strFillIn($item['identity'], ' ', 12, 'r');
            $line[]       = '02' . $bankTypeCode . $bankNo . $staffName . $netPay . $staffId . $blankSpace24 . $identity . $blankSpace8 . '2';
            ++$peopleNumber;
            $netPayTotal += $this->staffPayroll[$item['staff_info_id']]['net_pay'];
        }
        $peopleNumberStr = strFillIn($peopleNumber, '0', 6);
        $netPayTotalStr  = strFillIn($netPayTotal * 100, '0', 13);
        $line[]          = '03' . $peopleNumberStr . $netPayTotalStr;
        return $line;
    }

    /**
     * OCBC银行 salary
     * @return array
     */
    public function salaryOCBCData(): array
    {
        $companyCIFNumber = 'A999999' . str_repeat(' ', 13);
        $companyName      = ($this->companyInfo['company_name'] ?? '');
        $companyName      = strFillIn($companyName, ' ', 30, 'r');
        $payDate          = date('dmY', strtotime($this->appointPayDate));
        $blankSpace40     = str_repeat(' ', 40);
        $companyMess      = strFillIn($this->companyMess, ' ', 16, 'r');
        $blankSpace334    = str_repeat(' ', 334);
        $line[]           = '01' . $this->tapeId . '00701' . $companyCIFNumber . $companyName . '00000000007011582518' . 'DN' . $payDate . $blankSpace40 . $companyMess . $blankSpace334;
        $peopleNumber     = $netPayTotal = 0;
        $blankSpace15     = str_repeat(' ', 15);
        $blankSpace65     = str_repeat(' ', 65);
        $blankSpace75     = str_repeat(' ', 75);
        $blankSpace20     = str_repeat(' ', 20);
        $blankSpace233    = str_repeat(' ', 233);
        $blankSpace453    = str_repeat(' ', 453);
        foreach ($this->staffInfo as $item) {
            if (empty($this->staffPayroll[$item['staff_info_id']]['paid_date'])) {
                continue;
            }
            $bankNo         = strFillIn($item['bank_no'], ' ', 20, 'r');
            $netPay         = strFillIn($this->staffPayroll[$item['staff_info_id']]['net_pay'] * 100, '0', 17);
            $identity       = HrStaffItemsModel::NATIONALITY_3 == $item['nationality'] ? strFillIn($item['identity'],
                ' ', 15, 'r') : $blankSpace15;
            $bankCode       = SalaryEnums::$bankTypeCodeV2[$item['bank_type']] ?? str_repeat('0', 9);
            $bankNoName     = strFillIn(strtoupper($item['bank_no_name']), ' ', 22, 'r');
            $passportNumber = HrStaffItemsModel::NATIONALITY_3 == $item['nationality'] ? $blankSpace20 : strFillIn($item['identity'],
                ' ', 20, 'r');
            $line[]         = '02' . $bankNo . $netPay . 'C' . $identity . $blankSpace65 . $bankCode . $bankNoName . $passportNumber . $blankSpace75 . 'Y' . $blankSpace233;
            ++$peopleNumber;
            $netPayTotal += $this->staffPayroll[$item['staff_info_id']]['net_pay'];
        }
        $line[] = '03' . strFillIn($peopleNumber, '0', 6) . strFillIn($netPayTotal * 100, '0', 19) . $blankSpace453;
        return $line;
    }

    /**
     * HSBC银行 salary
     * @return array
     */
    public function salaryHSBCData(): array
    {
        $payDate      = date('Ymd,', strtotime(current($this->staffPayroll)['pay_date']));
        $companyMess  = strFillIn($this->companyMess, ' ', 8, 'r') . ',';
        $remark       = strFillIn($this->remark, ' ', 24, 'r');
        $createTime   = date('Y/m/d,H:i:s,');
        $numberSum    = count($this->staffInfo);
        $line[0]      = 'IFH,IFILE,CSV,' . 'ABCB2610001,' . 'SGHBAPGSG041758889,' . $payDate . $createTime . 'P,0.1,' . ($numberSum + 2);
        $lineNumber   = strFillIn($numberSum, ',', 7, 'r') . ',';//  3. 居左显示，未填满使用英文逗号填充，最后1位固定英文逗号
        $line[1]      = 'BATHDR,' . 'ACH-CR,' . $lineNumber . '@1ST@,' . $companyMess . '************,' . 'MYR,' . '%net_pay%' . 'MY,' . 'HBMB,MYR,,FLASH FULFILLMENT (M) SDN. BHD.,,,,,C11,' . $remark;
        $peopleNumber = $netPayTotal = 0;
        $payDate      = date('Ymd', strtotime(current($this->staffPayroll)['pay_date']));
        $payDate      = strFillIn($payDate, ',', 14, 'r');
        foreach ($this->staffInfo as $item) {
            if (empty($this->staffPayroll[$item['staff_info_id']]['paid_date'])) {
                continue;
            }
            $bankNo       = fixedEndFillingChar($item['bank_no'], ',', 13);
            $bankNoName   = fixedEndFillingChar($item['bank_no_name'], ',', 21);
            $staffInfoId  = strFillIn($item['staff_info_id'], ',', 7, 'r');
            $bankTypeCode = SalaryEnums::$bankTypeCode[$item['bank_type']] ?? str_repeat(',', 5);
            $bankTypeCode = strFillIn($bankTypeCode, ',', 5, 'r');
            $netPay       = bcadd($this->staffPayroll[$item['staff_info_id']]['net_pay'], 0, 2) . ',';
            $line[]       = 'SECPTY,' . $bankNo . $bankNoName . $staffInfoId . $bankTypeCode . $netPay . $payDate . 'N,N';
            ++$peopleNumber;
            $netPayTotal += $this->staffPayroll[$item['staff_info_id']]['net_pay'];
        }
        $netPayTotal = bcadd($netPayTotal, 0, 2);
        $netPay      = strFillIn($netPayTotal, ',', 10, 'r') . ',';
        $line[1]     = str_replace('%net_pay%', $netPay, $line[1]);
        return $line;
    }

    /**
     * CIMB银行 就业保险
     * @return array
     */
    public function eisCIMBData(): array
    {
        $socsoRegistrationNumber = strFillIn($this->companyInfo['socso_registration_number'], ' ', 12, 'r');
        $SSMNumber               = strFillIn(str_replace('-', '', $this->companyInfo['business_registration_number']),
            ' ', 20, 'r');
        $line                    = [];
        foreach ($this->staffInfo as $item) {
            $identity = strFillIn($item['identity'], ' ', 12, 'r');
            if ($item['nationality'] != HrStaffItemsModel::NATIONALITY_3) {
                $identity = strFillIn($item['socso_no'], ' ', 12, 'r');
            }
            $bankNoName       = strFillIn($item['bank_no_name'], ' ', 150, 'r');
            $month            = date('mY', strtotime($this->month));
            $eisTotal         = strFillIn($this->staffPayroll[$item['staff_info_id']]['eis_total'] * 100, '0', 14);
            $hireOrLeaveDate  = empty($item['hire_or_last_work_day']) ? str_repeat(' ', 8) : date('dmY',
                strtotime($item['hire_or_last_work_day']));
            $hireOrLeaveState = empty($item['hire_or_last_work_day_state']) ? ' ' : $item['hire_or_last_work_day_state'];
            $line[]           = $socsoRegistrationNumber . $SSMNumber . $identity . $bankNoName . $month . $eisTotal . $hireOrLeaveDate . $hireOrLeaveState;
        }
        return $line;
    }

    /**
     * OCBC银行 就业保险
     * @return array
     */
    public function eisOCBCData(): array
    {
        return $this->eisCIMBData();
    }

    /**
     * HSBC银行 就业保险
     * @return array
     */
    public function eisHSBCData(): array
    {
        return $this->eisCIMBData();
    }

    /**
     * CIMB银行 社保
     * @return array
     */
    public function socsoCIMBData(): array
    {
        $socsoNUmber = strFillIn($this->companyInfo['socso_registration_number'], ' ', 12, 'r');
        $SSMNumber   = strFillIn(str_replace('-', '', $this->companyInfo['business_registration_number']), ' ', 20,
            'r');
        $line        = [];
        foreach ($this->staffInfo as $item) {
            $identity = strFillIn($item['identity'], ' ', 12, 'r');
            if ($item['nationality'] != HrStaffItemsModel::NATIONALITY_3) {
                $identity = strFillIn($item['socso_no'], ' ', 12, 'r');
            }
            $bankNoName       = strFillIn($item['bank_no_name'], ' ', 150, 'r');
            $month            = date('mY', strtotime($this->month));
            $socsoTotal       = strFillIn($this->staffPayroll[$item['staff_info_id']]['socso_total'] * 100, '0', 14);
            $hireOrLeaveDate  = empty($item['hire_or_last_work_day']) ? str_repeat(' ', 8) : date('dmY',
                strtotime($item['hire_or_last_work_day']));
            $hireOrLeaveState = empty($item['hire_or_last_work_day_state']) ? ' ' : $item['hire_or_last_work_day_state'];
            $line[]           = $socsoNUmber . $SSMNumber . $identity . $bankNoName . $month . $socsoTotal . $hireOrLeaveDate . $hireOrLeaveState;
        }
        return $line;
    }

    /*
     * OCBC银行 社保
     */
    public function socsoOCBCData(): array
    {
        $socsoNUmber = strFillIn($this->companyInfo['socso_registration_number'], ' ', 12, 'r');
        $SSMNumber   = strFillIn(str_replace('-', '', $this->companyInfo['business_registration_number']), ' ', 20,
            'r');
        $line        = [];
        foreach ($this->staffInfo as $item) {
            $identity = strFillIn($item['identity'], ' ', 12, 'r');
            if ($item['nationality'] != HrStaffItemsModel::NATIONALITY_3) {
                $identity = strFillIn($item['socso_no'], ' ', 12, 'r');
            }
            $bankNoName       = strFillIn($item['bank_no_name'], ' ', 150, 'r');
            $month            = date('mY', strtotime($this->month));
            $socsoTotal       = strFillIn($this->staffPayroll[$item['staff_info_id']]['socso_total'] * 100, '0', 14);
            $hireOrLeaveDate  = empty($item['hire_or_last_work_day']) ? str_repeat(' ', 8) : date('dmY',
                strtotime($item['hire_or_last_work_day']));
            $hireOrLeaveState = empty($item['hire_or_last_work_day_state']) ? ' ' : $item['hire_or_last_work_day_state'];
            $line[]           = $socsoNUmber . $SSMNumber . $identity . $bankNoName . $month . $socsoTotal . $hireOrLeaveDate . $hireOrLeaveState;
        }
        return $line;
    }

    /**
     * HSBC银行 社保
     * @return array
     */
    public function socsoHSBCData(): array
    {
        $socsoNUmber = strFillIn($this->companyInfo['socso_registration_number'], ' ', 12, 'r');
        $SSMNumber   = strFillIn(str_replace('-', '', $this->companyInfo['business_registration_number']), ' ', 20,
            'r');
        $line        = [];
        foreach ($this->staffInfo as $item) {
            $identity = strFillIn($item['identity'], ' ', 12, 'r');
            if ($item['nationality'] != HrStaffItemsModel::NATIONALITY_3) {
                $identity = strFillIn($item['socso_no'], ' ', 12, 'r');
            }
            $bankNoName = strFillIn($item['bank_no_name'], ' ', 150, 'r');
            $month      = date('mY', strtotime($this->month));
            $socsoTotal = strFillIn($this->staffPayroll[$item['staff_info_id']]['socso_total'] * 100, '0', 14);
            $line[]     = $socsoNUmber . $SSMNumber . $identity . $bankNoName . $month . $socsoTotal;
        }
        return $line;
    }

    /**
     * CIMB银行 公积金
     * @return array
     */
    public function epfCIMBData(): array
    {
        $epfNUmber   = strFillIn($this->companyInfo['epf_registration_number'], '0', 19);
        $fixedEpf    = strFillIn('EPF MONTHLY FORM A', ' ', 20, 'r');
        $nextMonth   = date('mY', strtotime($this->month . ' next month'));
        $laborName   = strFillIn($this->companyInfo['labor_name'] ?? '', ' ', 40, 'r');
        $companyPhone = strFillIn($this->companyInfo['company_phone'] ?? '', ' ', 20, 'r');
        $payDate     = $this->appointPayDate;
        $payDate     = date('Ymd', strtotime($payDate));
        $payDate     .= date('His') . '00';
        $fixed16     = str_repeat('0', 16);
        $line[]      = '01' . $fixedEpf . $epfNUmber . $nextMonth . 'ITB' . '*********' . '010' . $laborName . $companyPhone . 'N' . '00' . $payDate . $fixed16 . 'N';
        $epfErTotal  = $epfEeTotal = $epfNoTotal = 0;
        foreach ($this->staffInfo as $item) {
            $epfNo       = strFillIn($item['epf_no'], '0', 19);
            $identity    = strFillIn($item['identity'], ' ', 15, 'r');
            $bankNoName  = strFillIn($item['bank_no_name'], ' ', 80, 'r');
            $staffInfoId = strFillIn($item['staff_info_id'], ' ', 20, 'r');
            $epfER       = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_paid_by_er'] * 100, '0', 15);
            $epfEE       = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_paid_by_ee'] * 100, '0', 15);
            $epfWages    = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_wages'] * 100, '0', 15);
            $line[]      = '02' . $epfNo . $identity . $bankNoName . $staffInfoId . $epfER . $epfEE . $epfWages;
            $epfErTotal  += $this->staffPayroll[$item['staff_info_id']]['epf_paid_by_er'];
            $epfEeTotal  += $this->staffPayroll[$item['staff_info_id']]['epf_paid_by_ee'];
            $epfNoTotal  += (int)$epfNo;
        }
        $number     = strFillIn(count($this->staffInfo), '0', 7);
        $epfErTotal = strFillIn($epfErTotal * 100, '0', 15);
        $epfEeTotal = strFillIn($epfEeTotal * 100, '0', 15);
        $epfNoTotal = strFillIn($epfNoTotal, '0', 21);
        $line[]     = '99' . $number . $epfErTotal . $epfEeTotal . $epfNoTotal;
        return $line;
    }

    /**
     * OCBC银行 公积金
     * @return array
     */
    public function epfOCBCData(): array
    {
        $epfNUmber   = strFillIn($this->companyInfo['epf_registration_number'], '0', 21);
        $epfNUmber19 = strFillIn($this->companyInfo['epf_registration_number'], '0', 19);
        $fixedEpf    = 'EPF MONTHLY FORM A';
        $date        = date('Ymd');
        $number      = '00001';
        $fixed45     = str_repeat(' ', 45);
        $fixed86     = str_repeat(' ', 86);
        $line[]      = '00' . $fixedEpf . $date . $number . '$epfErTotal' . '$epfEeTotal' . $epfNUmber . $fixed45;
        $nextMonth   = date('mY', strtotime($this->month . ' next month'));
        $line[]      = '01' . $epfNUmber19 . $nextMonth . 'DSK' . '00001' . '0000' . '0000' . $fixed86;
        $epfErTotal  = $epfEeTotal = $epfNoTotal = 0;
        foreach ($this->staffInfo as $item) {
            $epfNo       = strFillIn($item['epf_no'], '0', 19);
            $identity    = strFillIn($item['identity'], ' ', 15, 'r');
            $bankNoName  = strFillIn($item['bank_no_name'], ' ', 40, 'r');
            $staffInfoId = strFillIn($item['staff_info_id'], ' ', 20, 'r');
            $epfER       = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_paid_by_er'] * 100, '0', 9);
            $epfEE       = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_paid_by_ee'] * 100, '0', 9);
            $epfWages    = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_wages'] * 100, '0', 15);
            $line[]      = '02' . $epfNo . $identity . $bankNoName . $staffInfoId . $epfER . $epfEE . $epfWages;
            $epfErTotal  += $this->staffPayroll[$item['staff_info_id']]['epf_paid_by_er'];
            $epfEeTotal  += $this->staffPayroll[$item['staff_info_id']]['epf_paid_by_ee'];
            $epfNoTotal  += (int)$epfNo;
        }
        $epfErTotal = strFillIn($epfErTotal * 100, '0', 15);
        $epfEeTotal = strFillIn($epfEeTotal * 100, '0', 15);
        $line[0]    = str_replace(['$epfErTotal', '$epfEeTotal'], [$epfErTotal, $epfEeTotal], $line[0]);
        $epfNoTotal = strFillIn($epfNoTotal, '0', 21);
        $number7    = strFillIn(count($this->staffInfo), '0', 7);
        $fixed69    = str_repeat(' ', 69);
        $line[]     = '99' . $number7 . $epfErTotal . $epfEeTotal . $epfNoTotal . $fixed69;
        return $line;
    }

    /**
     * HSBC银行 公积金
     * @return array
     */
    public function epfHSBCData(): array
    {
        $epfNUmber   = strFillIn($this->companyInfo['epf_registration_number'], '0', 21);
        $epfNUmber19 = strFillIn($this->companyInfo['epf_registration_number'], '0', 19);
        $fixedEpf    = 'EPF MONTHLY FORM A';
        $date        = date('Ymd');
        $number      = strFillIn(count($this->staffInfo), '0', 5);
        $fixed45     = str_repeat(' ', 45);
        $fixed7      = str_repeat(' ', 7);
        $fixed79     = str_repeat(' ', 79);
        $line[]      = '00' . $fixedEpf . $date . $number . '$epfErTotal' . '$epfEeTotal' . $epfNUmber . $fixed45;
        $nextMonth   = date('mY', strtotime($this->month . ' next month'));
        $line[]      = '01' . $epfNUmber19 . $nextMonth . 'DSK' . '00001' . '0000' . '0000' . $fixed7 . $fixed79;
        $epfErTotal  = $epfEeTotal = $epfNoTotal = 0;
        foreach ($this->staffInfo as $item) {
            $epfNo       = strFillIn($item['epf_no'], '0', 19);
            $identity    = strFillIn($item['identity'], ' ', 15, 'r');
            $bankNoName  = strFillIn($item['bank_no_name'], ' ', 40, 'r');
            $staffInfoId = strFillIn($item['staff_info_id'], ' ', 20, 'r');
            $epfER       = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_paid_by_er'] * 100, '0', 9);
            $epfEE       = strFillIn($this->staffPayroll[$item['staff_info_id']]['epf_paid_by_ee'] * 100, '0', 9);
            $netPay      = strFillIn($this->staffPayroll[$item['staff_info_id']]['net_pay'] * 100, '0', 15);
            $line[]      = '02' . $epfNo . $identity . $bankNoName . $staffInfoId . $epfER . $epfEE . $netPay;
            $epfErTotal  += $this->staffPayroll[$item['staff_info_id']]['epf_paid_by_er'];
            $epfEeTotal  += $this->staffPayroll[$item['staff_info_id']]['epf_paid_by_ee'];
            $epfNoTotal  += (int)$epfNo;
        }

        $epfErTotal = strFillIn($epfErTotal * 100, '0', 15);
        $epfEeTotal = strFillIn($epfEeTotal * 100, '0', 15);
        $line[0]    = str_replace(['$epfErTotal', '$epfEeTotal'], [$epfErTotal, $epfEeTotal], $line[0]);
        $epfNoTotal = strFillIn($epfNoTotal, '0', 21);
        $number7    = strFillIn(count($this->staffInfo), '0', 7);
        $fixed69    = str_repeat(' ', 69);
        $line[]     = '99' . $number7 . $epfErTotal . $epfEeTotal . $epfNoTotal . $fixed69;
        return $line;
    }

    /**
     * CIMB银行 税
     * @return array
     */
    public function taxCIMBData(): array
    {
        $taxNUmber   = strFillIn(strOnlyNumber($this->companyInfo['company_registration_number']), '0', 10);
        $laborName   = strFillIn($this->companyInfo['labor_name'] ?? '', ' ', 25, 'r');
        $companyPhone = strFillIn($this->companyInfo['company_phone'] ?? '', ' ', 15, 'r');
        $fixed10     = str_repeat(' ', 10);
        $month       = date('Ym', strtotime($this->month));
        $email       = strFillIn('<EMAIL>', ' ', 40, 'r');
        $line[]      = 'H' . $fixed10 . $taxNUmber . $month . '$pcbTotal' . '$pcbNumber' . '$cp38Total' . '$cp38Number' . $email . $companyPhone . $laborName;
        $pcbTotal    = $pcbNumber = $cp38Total = $cp38Number = 0;
        $fixed3      = str_repeat(' ', 3);
        $fixed9      = str_repeat(' ', 9);
        foreach ($this->staffInfo as $item) {
            $taxNo          = strFillIn(strOnlyNumber($item['tax_no']), '0', 10);
            $bankNoName     = strFillIn($item['bank_no_name'], ' ', 60, 'r');
            $identity       = strFillIn($item['identity'], ' ', 12, 'r');
            $passportNumber = strFillIn($item['passport_number'], ' ', 12, 'r'); //护照号码
            $country        = strFillIn($item['country'], ' ', 2, 'r');
            $pcbAmount      = strFillIn($this->staffPayroll[$item['staff_info_id']]['pcb_amount'] * 100, '0', 8);
            $cp38           = strFillIn($this->staffPayroll[$item['staff_info_id']]['cp38'] * 100, '0', 8);
            $staffInfoId    = strFillIn($item['staff_info_id'], ' ', 10, 'r');
            $line[]         = 'D' . $taxNo . '0' . $bankNoName . $fixed9 . $fixed3 . $identity . $passportNumber . $country . $pcbAmount . $cp38 . $staffInfoId;
            $pcbTotal       += $this->staffPayroll[$item['staff_info_id']]['pcb_amount'];
            $cp38Total      += $this->staffPayroll[$item['staff_info_id']]['cp38'];
            if ($this->staffPayroll[$item['staff_info_id']]['pcb_amount'] > 0) {
                ++$pcbNumber;
            }
            if ($this->staffPayroll[$item['staff_info_id']]['cp38'] > 0) {
                ++$cp38Number;
            }
        }
        $pcbTotal   = strFillIn($pcbTotal * 100, '0', 10);
        $pcbNumber  = strFillIn($pcbNumber, '0', 5);
        $cp38Total  = strFillIn($cp38Total * 100, '0', 10);
        $cp38Number = strFillIn($cp38Number, '0', 5);
        $line[0]    = str_replace(['$pcbTotal', '$pcbNumber', '$cp38Total', '$cp38Number'],
            [$pcbTotal, $pcbNumber, $cp38Total, $cp38Number], $line[0]);
        return $line;
    }

    /**
     * OCBC银行 税
     * @return array
     */
    public function taxOCBCData(): array
    {
        $taxNUmber = strFillIn(strOnlyNumber($this->companyInfo['company_registration_number']), '0', 10);
        $month     = date('Ym', strtotime($this->month));
        $line[]    = 'H' . $taxNUmber . $taxNUmber . $month . '$pcbTotal' . '$pcbNumber' . '$cp38Total' . '$cp38Number';
        $fixed3    = str_repeat(' ', 3);
        $fixed9    = str_repeat(' ', 9);
        $pcbTotal  = $pcbNumber = $cp38Total = $cp38Number = 0;
        foreach ($this->staffInfo as $item) {
            $taxNo          = strFillIn(strOnlyNumber($item['tax_no']), '0', 11);
            $identity       = strFillIn($item['identity'], ' ', 12, 'r');
            $passportNumber = strFillIn($item['passport_number'], ' ', 14, 'r'); //护照号码
            $bankNoName     = strFillIn($item['bank_no_name'], ' ', 60, 'r');
            $pcbAmount      = strFillIn($this->staffPayroll[$item['staff_info_id']]['pcb_amount'] * 100, '0', 8);
            $cp38           = strFillIn($this->staffPayroll[$item['staff_info_id']]['cp38'] * 100, '0', 8);
            $staffInfoId    = strFillIn($item['staff_info_id'], ' ', 10, 'r');
            $line[]         = 'D' . $taxNo . $bankNoName . $fixed9 . $fixed3 . $identity . $passportNumber . $pcbAmount . $cp38 . $staffInfoId;
            $pcbTotal       += $this->staffPayroll[$item['staff_info_id']]['pcb_amount'];
            $cp38Total      += $this->staffPayroll[$item['staff_info_id']]['cp38'];
            if ($this->staffPayroll[$item['staff_info_id']]['pcb_amount'] > 0) {
                ++$pcbNumber;
            }
            if ($this->staffPayroll[$item['staff_info_id']]['cp38'] > 0) {
                ++$cp38Number;
            }
        }
        $pcbTotal   = strFillIn($pcbTotal * 100, '0', 10);
        $pcbNumber  = strFillIn($pcbNumber, '0', 5);
        $cp38Total  = strFillIn($cp38Total * 100, '0', 10);
        $cp38Number = strFillIn($cp38Number, '0', 5);
        $line[0]    = str_replace(['$pcbTotal', '$pcbNumber', '$cp38Total', '$cp38Number'],
            [$pcbTotal, $pcbNumber, $cp38Total, $cp38Number], $line[0]);
        return $line;
    }

    /**
     * HSBC银行 税
     * @return array
     */
    public function taxHSBCData(): array
    {
        $taxNUmber = strFillIn(strOnlyNumber($this->companyInfo['company_registration_number']), '0', 10);
        $month     = date('Ym', strtotime($this->month));
        $fixed15   = str_repeat('0', 15);
        $line[]    = 'H' . $taxNUmber . $taxNUmber . $month . '$pcbTotal' . '$pcbNumber' . $fixed15;
        $fixed8    = str_repeat('0', 8);
        $pcbTotal  = $pcbNumber = 0;
        foreach ($this->staffInfo as $item) {
            if ($this->staffPayroll[$item['staff_info_id']]['pcb_amount'] < 0.01) {
                continue;
            }
            $taxNo       = strFillIn(strOnlyNumber($item['tax_no']), '0', 11);
            $bankNoName  = strFillIn($item['bank_no_name'], ' ', 72, 'r');
            $identity    = strFillIn($item['identity_hsbc'], ' ', 26, 'r');
            $pcbAmount   = strFillIn($this->staffPayroll[$item['staff_info_id']]['pcb_amount'] * 100, '0', 8);
            $staffInfoId = strFillIn($item['staff_info_id'], ' ', 6, 'r');
            $line[]      = 'D' . $taxNo . $bankNoName . $identity . $pcbAmount . $fixed8 . $staffInfoId;
            $pcbTotal    += $this->staffPayroll[$item['staff_info_id']]['pcb_amount'];
            ++$pcbNumber;
        }
        $pcbTotal  = strFillIn($pcbTotal * 100, '0', 10);
        $pcbNumber = strFillIn($pcbNumber, '0', 5);
        $line[0]   = str_replace(['$pcbTotal', '$pcbNumber'], [$pcbTotal, $pcbNumber], $line[0]);
        return $line;
    }

    public function getStaffPayroll($periodType, $month, $companyId): array
    {
        $find       = [];
        $bind       = ['month' => $month, 'run_type' => $periodType, 'company_id' => $companyId];
        $conditions = 'month=:month:   and run_type=:run_type:  and company_id=:company_id: and is_deleted=0 ';
        if ($this->appointStaffIds) {
            $bind['ids'] = $this->appointStaffIds;
            $conditions  .= ' and staff_info_id in ({ids:array})';
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_SALARY) {
            $find = StaffPayrollModel::find([
                'columns'    => 'staff_info_id,net_pay,pay_date,paid_date',
                'conditions' => $conditions . ' and net_pay>0',
                'bind'       => $bind,
            ])->toArray();
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_EIS) {
            $find = StaffPayrollModel::find([
                'columns'    => 'staff_info_id,(eis_paid_by_ee+eis_paid_by_er) as eis_total,pay_date',
                'conditions' => $conditions . '  and is_deleted=0 and (eis_paid_by_ee>0 or eis_paid_by_er>0)',
                'bind'       => $bind,
            ])->toArray();
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_SOCSO) {
            $find = StaffPayrollModel::find([
                'columns'    => 'staff_info_id,(socso_paid_by_ee+socso_paid_by_er) as socso_total,pay_date',
                'conditions' => $conditions . ' and (socso_paid_by_ee>0 or socso_paid_by_er>0)',
                'bind'       => $bind,
            ])->toArray();
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_EPF) {
            $find = StaffPayrollModel::find([
                'columns'    => 'staff_info_id,epf_paid_by_ee,epf_paid_by_er,net_pay,pay_date,epf_wages',
                'conditions' => $conditions . ' and (epf_paid_by_ee>0 or epf_paid_by_er>0)',
                'bind'       => $bind,
            ])->toArray();
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_TAX) {
            $find = StaffPayrollModel::find([
                'columns'    => 'staff_info_id,(mtd_for_current+mtd_for_nr+mtd_for_irda+mtd_for_rep+tax_by_er) as pcb_amount,cp38',
                'conditions' => $conditions . ' and ((mtd_for_current+mtd_for_nr+mtd_for_irda+mtd_for_rep+tax_by_er)>0 or cp38>0)',
                'bind'       => $bind,
            ])->toArray();
        }
        return array_column($find, null, 'staff_info_id');
    }

    public function getCompanyInfo($companyId): array
    {
        $find = StaffPayrollCompanyInfoModel::findFirst($companyId);
        if (empty($find)) {
            return [];
        }
        $find      = $find->toArray();
        $staffFind = HrStaffInfoModel::findFirst([
            'columns'    => 'staff_info_id,name,identity,job_title,mobile_company,email',
            'conditions' => 'staff_info_id=:id:',
            'bind'       => ['id' => $find['labor_no']],
        ]);
        if ($staffFind) {
            $find['labor_name']   = strtoupper($staffFind->name);
            $find['labor_mobile'] = str_replace('-','', $staffFind->mobile_company);
            $find['company_phone'] = str_replace('-','', $find['company_phone']);
        }
        return $find;
    }

    public function getStaffInfoForSalary($staffInfoIds): array
    {
        $find          = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,lower(name) as name,identity,is_sub_staff,sex,job_title,bank_no,sys_department_id,node_department_id,nationality,bank_type',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $bankNoName    = HrStaffItemsModel::find([
            'columns'    => 'staff_info_id,value',
            'conditions' => 'staff_info_id  in ({ids:array}) and item=:item:',
            'bind'       => ['ids' => $staffInfoIds, 'item' => 'BANK_NO_NAME'],
        ])->toArray();
        $bankNoNameMap = array_column($bankNoName, 'value', 'staff_info_id');
        foreach ($find as $key => $item) {
            $find[$key]['bank_no_name'] = $bankNoNameMap[$item['staff_info_id']] ? strtoupper($bankNoNameMap[$item['staff_info_id']]) : '';
        }
        if ($this->fileType == SalaryEnums::PAYROLL_BANK_FILE_SALARY && $this->bankType == SalaryEnums::PAYROLL_BANK_CIMB) {
            array_multisort(array_column($find, 'bank_no_name'), SORT_ASC, $find);
        } else {
            array_multisort(array_column($find, 'name'), SORT_ASC, $find);
        }
        return array_column($find, null, 'staff_info_id');
    }

    public function getStaffInfoForEIS($staffInfoIds): array
    {
        $cycleStart = date('Y-m-24', strtotime($this->month . ' last month'));
        $cycleEnd   = date('Y-m-23', strtotime($this->month));
        $find       = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,lower(name) as name,identity,bank_no,nationality,bank_type,state,date(hire_date) as hire_date,date(leave_date) as leave_date',
            'conditions' => 'staff_info_id in ({ids:array}) and formal=1',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $bankNoName = HrStaffItemsModel::find([
            'columns'    => 'staff_info_id,value',
            'conditions' => 'staff_info_id  in ({ids:array}) and item=:item:',
            'bind'       => ['ids' => $staffInfoIds, 'item' => 'BANK_NO_NAME'],
        ])->toArray();

        $bankNoNameMap = array_column($bankNoName, 'value', 'staff_info_id');
        $socsoNoFind   = StaffSalaryEpfSocsoModel::find([
            'columns'    => 'staff_info_id,socso_no',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $socsoNoMap    = array_column($socsoNoFind, 'socso_no', 'staff_info_id');
        foreach ($find as $key => $item) {
            $find[$key]['bank_no_name'] = $bankNoNameMap[$item['staff_info_id']] ? strtoupper($bankNoNameMap[$item['staff_info_id']]) : '';
            $find[$key]['socso_no']     = $socsoNoMap[$item['staff_info_id']] ?? '';
            $lastWorkDay                = $item['leave_date'] ? date('Y-m-d',
                strtotime($item['leave_date'] . ' last day')) : null;
            if ($lastWorkDay && $lastWorkDay >= $cycleStart && $lastWorkDay <= $cycleEnd) {
                $find[$key]['hire_or_last_work_day']       = $lastWorkDay;
                $find[$key]['hire_or_last_work_day_state'] = 'H';
            } elseif ($item['hire_date'] >= $cycleStart && $item['hire_date'] <= $cycleEnd) {
                $find[$key]['hire_or_last_work_day']       = $item['hire_date'];
                $find[$key]['hire_or_last_work_day_state'] = 'B';
            } else {
                $find[$key]['hire_or_last_work_day']       = '';
                $find[$key]['hire_or_last_work_day_state'] = '';
            }
        }
        array_multisort(array_column($find, 'bank_no_name'), SORT_ASC, $find);
        return array_column($find, null, 'staff_info_id');
    }

    public function getStaffInfoForSOCSO($staffInfoIds): array
    {
        $cycleStart = date('Y-m-24', strtotime($this->month . ' last month'));
        $cycleEnd   = date('Y-m-23', strtotime($this->month));
        $find       = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,identity,nationality,date(hire_date) as hire_date,date(leave_date) as leave_date',
            'conditions' => 'staff_info_id in ({ids:array}) and formal=1',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $bankNoName = HrStaffItemsModel::find([
            'columns'    => 'staff_info_id,value',
            'conditions' => 'staff_info_id  in ({ids:array}) and item=:item:',
            'bind'       => ['ids' => $staffInfoIds, 'item' => 'BANK_NO_NAME'],
        ])->toArray();

        $bankNoNameMap = array_column($bankNoName, 'value', 'staff_info_id');
        $socsoNoFind   = StaffSalaryEpfSocsoModel::find([
            'columns'    => 'staff_info_id,socso_no',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $socsoNoMap    = array_column($socsoNoFind, 'socso_no', 'staff_info_id');
        foreach ($find as $key => $item) {
            $find[$key]['bank_no_name'] = $bankNoNameMap[$item['staff_info_id']] ? strtoupper($bankNoNameMap[$item['staff_info_id']]) : '';
            $find[$key]['socso_no']     = $socsoNoMap[$item['staff_info_id']] ?? '';
            $lastWorkDay                = $item['leave_date'] ? date('Y-m-d',
                strtotime($item['leave_date'] . ' last day')) : null;
            if ($lastWorkDay && $lastWorkDay >= $cycleStart && $lastWorkDay <= $cycleEnd) {
                $find[$key]['hire_or_last_work_day']       = $lastWorkDay;
                $find[$key]['hire_or_last_work_day_state'] = 'H';
            } elseif ($item['hire_date'] >= $cycleStart && $item['hire_date'] <= $cycleEnd) {
                $find[$key]['hire_or_last_work_day']       = $item['hire_date'];
                $find[$key]['hire_or_last_work_day_state'] = 'B';
            } else {
                $find[$key]['hire_or_last_work_day']       = '';
                $find[$key]['hire_or_last_work_day_state'] = '';
            }
        }
        array_multisort(array_column($find, 'bank_no_name'), SORT_ASC, $find);
        return array_column($find, null, 'staff_info_id');
    }

    public function getStaffInfoForEPF($staffInfoIds): array
    {
        $find       = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,identity,nationality',
            'conditions' => 'staff_info_id in ({ids:array}) and formal=1',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $bankNoName = HrStaffItemsModel::find([
            'columns'    => 'staff_info_id,value',
            'conditions' => 'staff_info_id  in ({ids:array}) and item=:item:',
            'bind'       => ['ids' => $staffInfoIds, 'item' => 'BANK_NO_NAME'],
        ])->toArray();

        $bankNoNameMap = array_column($bankNoName, 'value', 'staff_info_id');
        $socsoNoFind   = StaffSalaryEpfSocsoModel::find([
            'columns'    => 'staff_info_id,socso_no,epf_no',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $epfNoMap      = array_column($socsoNoFind, 'epf_no', 'staff_info_id');
        foreach ($find as $key => $item) {
            $find[$key]['bank_no_name'] = $bankNoNameMap[$item['staff_info_id']] ? strtoupper($bankNoNameMap[$item['staff_info_id']]) : '';
            $find[$key]['epf_no']       = $epfNoMap[$item['staff_info_id']] ?? '';
        }
        array_multisort(array_column($find, 'bank_no_name'), SORT_ASC, $find);
        return array_column($find, null, 'staff_info_id');
    }

    public function getStaffInfoForTax($staffInfoIds): array
    {
        $find       = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,identity,nationality',
            'conditions' => 'staff_info_id in ({ids:array}) and formal=1',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $bankNoName = HrStaffItemsModel::find([
            'columns'    => 'staff_info_id,value',
            'conditions' => 'staff_info_id  in ({ids:array}) and item=:item:',
            'bind'       => ['ids' => $staffInfoIds, 'item' => 'BANK_NO_NAME'],
        ])->toArray();

        $bankNoNameMap = array_column($bankNoName, 'value', 'staff_info_id');
        $taxNoFind     = StaffSalaryTaxModel::find([
            'columns'    => 'staff_info_id,tax_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffInfoIds],
        ])->toArray();
        $taxNoMap      = array_column($taxNoFind, 'tax_id', 'staff_info_id');
        $nationalityCode = (new DictionaryRepository())->getCodeTwoCodeMap();
        $nationalityCode = array_column($nationalityCode,'v_item_value','k_item_value');
        foreach ($find as $key => $item) {
            $find[$key]['passport_number'] = $item['identity'];
            $find[$key]['identity']        = '';
            if ($item['nationality'] == HrStaffItemsModel::NATIONALITY_3) {
                $find[$key]['identity']        = $item['identity'];
                $find[$key]['passport_number'] = '';
            }
            $find[$key]['identity_hsbc'] = $item['identity'];
            $find[$key]['bank_no_name']  = $bankNoNameMap[$item['staff_info_id']] ? strtoupper($bankNoNameMap[$item['staff_info_id']]) : '';
            $find[$key]['tax_no']        = $taxNoMap[$item['staff_info_id']] ?? '';
            $find[$key]['country']       = $nationalityCode[$item['nationality']] ?? '';
        }
        array_multisort(array_column($find, 'bank_no_name'), SORT_ASC, $find);
        return array_column($find, null, 'staff_info_id');
    }
}