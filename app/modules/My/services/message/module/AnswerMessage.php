<?php
/**
 * Author: Bruce
 * Date  : 2021-12-27 15:43
 * Description:
 */

namespace App\Modules\My\Services\message\module;


use App\Library\Validation\ValidationException;
use App\Modules\My\Services\message\BaseMessageService;
use App\Services\QuestionnaireLibService;

class AnswerMessage extends BaseMessageService
{
    const MESSAGE_TYPE_CODE = 34;

    public function verifyOrganizationMessage(array $formData)
    {
        try {

            // 问卷/答题消息: 问卷库必关联
            if(empty($formData['questionnaire_lib_id'])){
                throw new ValidationException(self::$t->_('msg_qn_057'));
            }
            $lib_info = (new QuestionnaireLibService())->read_lib(['lib_id' => $formData['questionnaire_lib_id']]);
            if ($lib_info['qn_lib_type'] != 3 || $lib_info['is_del']) {
                throw new ValidationException(self::$t->_('msg_qn_057'));
            }
            (new QuestionnaireLibService())->validateQesUUid($lib_info);

            $this->insertData = $formData;
            return $this;
        } catch (\Exception $e) {
            throw $e;
        }
    }
}