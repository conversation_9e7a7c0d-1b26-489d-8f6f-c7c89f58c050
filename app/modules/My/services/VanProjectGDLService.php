<?php

namespace App\Modules\My\Services;

use App\Library\BaseService;
use App\Library\FlashOss;
use App\Models\backyard\GdlExpirationModel;
use App\Models\backyard\HrEntryModel;
use App\Models\backyard\HrInterviewOfferModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\VehicleInfoModel;
use App\Repository\JobTitleCarTypeChangeRecordRepository;

class VanProjectGDLService extends BaseService
{
    public $jobTitleIds = [110];//Van Courier

    /**
     * $vehicleTypeCategoryMY
     * Van Project-Van/Van Project-Lorry
     * @var int[]
     */
    public $carTypes = [4, 9];


    public $header = [
        'Staff id',
        'Name',
        'Position',
        'Start day',
        'Resign day',
        'GDL start day',
        'GDL expires day',
        'Period effectiveness',
    ];

    public function download($month, $fileName): string
    {
        $lastMonth      = date('Y-m', strtotime($month . ' last month'));
        $monthStartTime = strtotime($lastMonth . '-24');
        $monthEndTime   = strtotime($month . '-23');
        $case1Staff     = $this->handleCase1($month);
        $case2Staff     = $this->handleCase2($month);

        $endStaffIds = array_merge(array_keys($case1Staff), array_keys($case2Staff));
        $returnData  = [];
        if ($endStaffIds) {
            $staffFind = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,date(hire_date) as hire_date,name,job_title,date(leave_date) as leave_date,state',
                'conditions' => 'staff_info_id in ({ids:array}) and is_sub_staff=0 and  formal=1 and hire_type not in (13,14)',
                'bind'       => ['ids' => $endStaffIds],
            ])->toArray();

            foreach ($staffFind as $item) {
                $gdLDay            = isset($case1Staff[$item['staff_info_id']]) ? $case1Staff[$item['staff_info_id']]['gdl_start_day'] : $item['hire_date'];
                $gdlExpiresDayTime = strtotime($gdLDay . ' +90 days');
                $gdlExpiresDay     = date('Y-m-d', $gdlExpiresDayTime);
                $leaveDate         = '';
                if ($item['state'] == HrStaffInfoModel::STATE_RESIGN && strtotime($item['leave_date']) <= strtotime($month . '-24')) {
                    $leaveDate = $item['leave_date'];
                }
                $PeriodEffectiveness = ($gdlExpiresDayTime >= $monthStartTime && $gdlExpiresDayTime <= $monthEndTime
                    && (empty($leaveDate) || strtotime($leaveDate) > $gdlExpiresDayTime)) ? 'Y' : 'N';
                $base                = [
                    $item['staff_info_id'],
                    $item['name'],
                    $this->showJobTitleName($item['job_title']),
                    $item['hire_date'],
                    $leaveDate,
                    $gdLDay,
                    $gdlExpiresDay,
                    $PeriodEffectiveness,
                ];
                $this->logger->info(json_encode($base));
                $returnData[] = $base;
            }
        }
        $filePathRes = $this->exportExcel($this->header, $returnData, $fileName);
        if (!isset($filePathRes['data'])) {
            throw new \Exception('exportExcel error');
        }
        $flashOss = new FlashOss();
        $object   = 'allowance/gdl/' . date('Ymd') . '/' . $fileName;
        $flashOss->uploadFile($object, $filePathRes['data']);
        return $object;
    }

    /**
     * 获取 offer gdl 津贴
     * @param $staffIds
     * @return array
     */
    protected  function getOfferSalary($staffIds): array
    {
        if (empty($staffIds)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('entry.staff_id,offer.gdl_allowance/100 as gdl_allowance');
        $builder->from(['entry' => HrEntryModel::class]);
        $builder->join(HrInterviewOfferModel::class, 'entry.interview_offer_id=offer.id', 'offer');
        $builder->where('entry.staff_id in ({ids:array})', ['ids' => $staffIds]);
        $offerSalary = $builder->getQuery()->execute()->toArray();
        return array_column($offerSalary,'gdl_allowance','staff_id');
    }
    public function handleCase2($month): array
    {
        //处理情况 2
        $staffFind      = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id,date(hire_date) as hire_date',
            'conditions' => 'date(hire_date) >=:start_time: and is_sub_staff=0 and formal=1 and hire_type not in (13,14)',
            'bind'       => ['start_time' => '2025-04-01'],
        ])->toArray();
        $staffFindMap   = array_column($staffFind, 'hire_date', 'staff_info_id');
        $lastMonth      = date('Y-m', strtotime($month . ' last month'));
        $monthStartTime = strtotime($lastMonth . '-24');
        $monthEndTime   = strtotime($month . '-23');
        //2-1. 员工入职日期≥2025-04-01，且从「入职日期」到「入职日期+90天」期间职位一直都是Van Courier且车类型是Van Project-Van/Van Project-Lorry」，「入职日期+90天」在所选月份考勤周期内
        $case2_1Staff  = array_filter($staffFindMap, function ($hireDate) use ($monthStartTime, $monthEndTime) {
            $addDaysTime = strtotime($hireDate . ' + 90 days');
            if ($addDaysTime >= $monthStartTime && $addDaysTime <= $monthEndTime) {
                return true;
            }
            return false;
        });
        $staffIds     = array_keys($case2_1Staff);
        //仅仅保留offer gdl 大于 0 的
        $offerGdl = $this->getOfferSalary($staffIds);
        $case2_1Staff = array_filter($case2_1Staff,function ($staffId) use ($offerGdl) {
            if (isset($offerGdl[$staffId]) && $offerGdl[$staffId] > 0) {
                return true;
            }
            return false;
        },ARRAY_FILTER_USE_KEY);
        $solidData     = JobTitleCarTypeChangeRecordRepository::staffMap(array_keys($case2_1Staff));
        $case2_1Return = $this->case2_1($case2_1Staff, $solidData, $monthStartTime, $monthEndTime);
        echo 'case2_1 .' . json_encode($case2_1Return) . PHP_EOL;

        //2-2 员工入职日期≥2025-04-01，且「入职日期+90天」>所选月份考勤周期最后一天，且 从「入职日期」到「截止日期」期间「职位一直都是Van Courier且车类型是Van Project-Van/Van Project-Lorry」
        $case2_2Staff = array_filter($staffFindMap, function ($hireDate) use ($monthEndTime) {
            $addDaysTime = strtotime($hireDate . ' + 90 days');
            if ($addDaysTime > $monthEndTime) {
                return true;
            }
            return false;
        });
        $leaveStaffs  = [];
        $staffIds     = array_keys($case2_2Staff);
        //仅仅保留offer gdl 大于 0 的
        $offerGdl = $this->getOfferSalary($staffIds);
        $case2_2Staff = array_filter($case2_2Staff,function ($staffId) use ($offerGdl) {
            if (isset($offerGdl[$staffId]) && $offerGdl[$staffId] > 0) {
                return true;
            }
            return false;
        },ARRAY_FILTER_USE_KEY);
        $staffIds     = array_keys($case2_2Staff);
        $solidData    = JobTitleCarTypeChangeRecordRepository::staffMap($staffIds);
        if ($staffIds) {
            $staffFind   = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,date(leave_date) as leave_date',
                'conditions' => 'staff_info_id in ({ids:array}) and state=:state:',
                'bind'       => ['ids' => $staffIds, 'state' => HrStaffInfoModel::STATE_RESIGN],
            ])->toArray();
            $leaveStaffs = array_column($staffFind, 'leave_date', 'staff_info_id');
        }
        $case2_2Return = $this->case2_2($case2_2Staff, $solidData, $monthStartTime, $monthEndTime, $leaveStaffs);
        echo 'case2_2 .' . json_encode($case2_2Return) . PHP_EOL;
        return $case2_1Return + $case2_2Return;
    }

    public function handleCase1($month): array
    {
        $find = GdlExpirationModel::find([
            'columns' => 'staff_info_id,gdl_start_day',
        ])->toArray();
        //处理情况 1
        $findGdlExpirationMap  = array_column($find, 'gdl_start_day', 'staff_info_id');
        $gdlExpirationStaffIds = array_column($find, 'staff_info_id');

        $solidData = JobTitleCarTypeChangeRecordRepository::staffMap($gdlExpirationStaffIds);
        //1-1 在HCM-工资计算-薪资数据-GDL Expiration完成「上传线下数据」的工号，
        //从「GDL start day」到「GDL start day+90天」期间「职位一直都是Van Courier且车类型是Van Project-Van/Van Project-Lorry」，
        //「GDL start day+90天」在所选月份考勤周期内
        $case1_1Return = $this->case1_1($month, $solidData, $findGdlExpirationMap);
        echo 'case1_1 .' . json_encode($case1_1Return) . PHP_EOL;
        //1- 2. 在HCM-工资计算-薪资数据-GDL Expiration完成「上传线下数据」的工号，从「GDL start day+90天」>所选月份考勤周期最后一天，
        //且从「GDL start day」到「所选月份考勤周期最后一天」期间「职位一直都是Van Courier且车类型是Van Project-Van/Van Project-Lorry」
        $monthEndDate = $month . '-23';
        $case1_2Staff = array_filter($findGdlExpirationMap, function ($GdDate) use ($monthEndDate) {
            $addDaysTime = strtotime($GdDate . ' + 90 days');
            if ($addDaysTime > strtotime($monthEndDate)) {
                return true;
            }
            return false;
        });
        $staffIds     = array_keys($case1_2Staff);
        $leaveStaffs  = [];
        if ($staffIds) {
            $staffFind   = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id,date(leave_date) as leave_date',
                'conditions' => 'staff_info_id in ({ids:array}) and state=:state:',
                'bind'       => ['ids' => $staffIds, 'state' => HrStaffInfoModel::STATE_RESIGN],
            ])->toArray();
            $leaveStaffs = array_column($staffFind, 'leave_date', 'staff_info_id');
        }
        $solidData     = JobTitleCarTypeChangeRecordRepository::staffMap($staffIds);
        $case1_2Return = $this->case1_2($month, $solidData, $case1_2Staff, $leaveStaffs);
        echo 'case1_2 .' . json_encode($case1_2Return) . PHP_EOL;
        return $case1_1Return + $case1_2Return;
    }

    public function case2_2($case2_2Staff, $solidData, $monthStartTime, $monthEndTime, $leaveStaffs): array
    {
        $returnData = [];
        foreach ($solidData as $staffId => $itemList) {
            if (!isset($case2_2Staff[$staffId])) {
                continue;
            }
            $isHireDateStart = false;
            $startTime       = null;
            $endTime         = null;
            foreach ($itemList as $keyNum => $item) {
                if (strtotime($case2_2Staff[$staffId]) >= strtotime($item['start_time'])
                    && strtotime($case2_2Staff[$staffId]) <= strtotime($item['end_time'])
                    && in_array($item['job_title'], $this->jobTitleIds) && in_array($item['car_type'],
                        $this->carTypes)) {
                    $isHireDateStart = true;
                    $startTime       = $item['start_time'];
                    $endTime         = $item['end_time'];
                }
                if ($isHireDateStart && isset($itemList[$keyNum + 1])
                    && in_array($itemList[$keyNum + 1]['job_title'],
                        $this->jobTitleIds) && in_array($itemList[$keyNum + 1]['car_type'], $this->carTypes)) {
                    $endTime = $itemList[$keyNum + 1]['end_time'];
                } else {
                    $isHireDateStart = false;
                }
            }
            if ($endTime) {
                $returnData[$staffId] = ['start' => $startTime, 'end' => $endTime];
            }
        }

        foreach ($returnData as $staffId => $item) {
            $leaveDate    = $leaveStaffs[$staffId] ?? null;
            $monthEndTime = $this->getJudgeEndTime($monthStartTime, $monthEndTime, $leaveDate);
            $isReturn     = strtotime($case2_2Staff[$staffId]) >= strtotime($item['start']) && strtotime($case2_2Staff[$staffId]) <= strtotime($item['end'])
                && $monthEndTime >= strtotime($item['start']) && $monthEndTime <= strtotime($item['end']);
            if (!$isReturn) {
                unset($returnData[$staffId]);
                continue;
            }
            $returnData[$staffId]['gdl_start_day'] = $case2_2Staff[$staffId];
            $returnData[$staffId]['leave_date']    = $leaveDate;
        }
        return $returnData;
    }

    public function case2_1($case2_1Staff, $solidData, $monthStartTime, $monthEndTime): array
    {
        $returnData = [];
        foreach ($solidData as $staffId => $itemList) {
            $isHireDateStart = false;
            $startTime       = null;
            $endTime         = null;
            foreach ($itemList as $keyNum => $item) {
                if (strtotime($case2_1Staff[$staffId]) >= strtotime($item['start_time'])
                    && strtotime($case2_1Staff[$staffId]) <= strtotime($item['end_time'])
                    && in_array($item['job_title'], $this->jobTitleIds) && in_array($item['car_type'],
                        $this->carTypes)) {
                    $isHireDateStart = true;
                    $startTime       = $item['start_time'];
                    $endTime         = $item['end_time'];
                }
                if ($isHireDateStart && isset($itemList[$keyNum + 1])
                    && in_array($itemList[$keyNum + 1]['job_title'],
                        $this->jobTitleIds) && in_array($itemList[$keyNum + 1]['car_type'], $this->carTypes)) {
                    $endTime = $itemList[$keyNum + 1]['end_time'];
                } else {
                    $isHireDateStart = false;
                }
            }
            if ($endTime) {
                $returnData[$staffId] = ['start' => $startTime, 'end' => $endTime];
            }
        }

        foreach ($returnData as $staffId => $item) {
            $addDaysTime = strtotime($case2_1Staff[$staffId] . '+90 days');
            $isReturn    = strtotime($case2_1Staff[$staffId]) >= strtotime($item['start']) && strtotime($case2_1Staff[$staffId]) <= strtotime($item['end'])
                && $addDaysTime >= strtotime($item['start']) && $addDaysTime <= strtotime($item['end'])
                && $addDaysTime >= $monthStartTime && $addDaysTime <= $monthEndTime;
            if (!$isReturn) {
                unset($returnData[$staffId]);
                continue;
            }
            $returnData[$staffId]['gdl_start_day'] = $case2_1Staff[$staffId];
        }
        return $returnData;
    }

    /**
     * 情况 1-2
     * @param $month
     * @param $solidData
     * @param $findGdlExpirationMap
     * @param $leaveStaffs
     * @return array
     */
    public function case1_2($month, $solidData, $findGdlExpirationMap, $leaveStaffs): array
    {
        $lastMonth      = date('Y-m', strtotime($month . ' last month'));
        $monthStartTime = strtotime($lastMonth . '-24');
        $monthEndTime   = strtotime($month . '-23');
        $returnData     = [];
        foreach ($solidData as $staffId => $itemList) {
            if (!isset($findGdlExpirationMap[$staffId])) {
                continue;
            }
            $isGDLStartDay = false;
            $startTime     = null;
            $endTime       = null;
            foreach ($itemList as $keyNum => $item) {
                if (strtotime($findGdlExpirationMap[$staffId]) >= strtotime($item['start_time'])
                    && strtotime($findGdlExpirationMap[$staffId]) <= strtotime($item['end_time'])
                    && in_array($item['job_title'], $this->jobTitleIds) && in_array($item['car_type'],
                        $this->carTypes)) {
                    $isGDLStartDay = true;
                    $endTime       = $item['end_time'];
                    $startTime     = $item['start_time'];
                }
                if ($isGDLStartDay && isset($itemList[$keyNum + 1])
                    && in_array($itemList[$keyNum + 1]['job_title'],
                        $this->jobTitleIds) && in_array($itemList[$keyNum + 1]['car_type'], $this->carTypes)) {
                    $endTime = $itemList[$keyNum + 1]['end_time'];
                } else {
                    $isGDLStartDay = false;
                }
            }
            if ($endTime) {
                $returnData[$staffId] = ['start' => $startTime, 'end' => $endTime];
            }
        }
        $this->logger->info(json_encode($returnData));
        foreach ($returnData as $staffId => $item) {
            $leaveDate    = $leaveStaffs[$staffId] ?? null;
            $monthEndTime = $this->getJudgeEndTime($monthStartTime, $monthEndTime, $leaveDate);
            $isReturn     = strtotime($findGdlExpirationMap[$staffId]) >= strtotime($item['start']) && strtotime($findGdlExpirationMap[$staffId]) <= strtotime($item['end'])
                && $monthEndTime >= strtotime($item['start']) && $monthEndTime <= strtotime($item['end']);
            if (!$isReturn) {
                unset($returnData[$staffId]);
                continue;
            }
            $returnData[$staffId]['gdl_start_day'] = $findGdlExpirationMap[$staffId];
            $returnData[$staffId]['leave_date']    = $leaveDate;
        }
        return $returnData;
    }

    /**
     * 获取判断的截止日期
     * @param $monthStartTime
     * @param $monthEndTime
     * @param $leaveDate
     * @return false|int
     */
    protected function getJudgeEndTime($monthStartTime, $monthEndTime, $leaveDate)
    {
        if ($leaveDate) {
            $lastDay = strtotime($leaveDate . ' -1 day');
            if ($lastDay >= $monthStartTime && $lastDay <= $monthEndTime) {
                return $lastDay;
            }
        }
        return $monthEndTime;
    }

    /**
     * 情况 1-1
     * @param $month
     * @param $solidData
     * @param $findGdlExpirationMap
     * @return array
     */
    public function case1_1($month, $solidData, $findGdlExpirationMap): array
    {
        $lastMonth      = date('Y-m', strtotime($month . ' last month'));
        $monthStartTime = strtotime($lastMonth . '-24');
        $monthEndTime   = strtotime($month . '-23');
        $returnData     = [];
        foreach ($solidData as $staffId => $itemList) {
            $isGDLStartDay = false;
            $startTime     = null;
            $endTime       = null;
            foreach ($itemList as $keyNum => $item) {
                if (strtotime($findGdlExpirationMap[$staffId]) >= strtotime($item['start_time'])
                    && strtotime($findGdlExpirationMap[$staffId]) <= strtotime($item['end_time'])
                    && in_array($item['job_title'], $this->jobTitleIds) && in_array($item['car_type'],
                        $this->carTypes)) {
                    $isGDLStartDay = true;
                    $startTime     = $item['start_time'];
                    $endTime       = $item['end_time'];
                }
                if ($isGDLStartDay && isset($itemList[$keyNum + 1])
                    && in_array($itemList[$keyNum + 1]['job_title'],
                        $this->jobTitleIds) && in_array($itemList[$keyNum + 1]['car_type'], $this->carTypes)) {
                    $endTime = $itemList[$keyNum + 1]['end_time'];
                } else {
                    $isGDLStartDay = false;
                }
            }
            if ($endTime) {
                $returnData[$staffId] = ['start' => $startTime, 'end' => $endTime];
            }
        }
        foreach ($returnData as $staffId => $item) {
            $addDaysTime = strtotime($findGdlExpirationMap[$staffId] . '+90 days');
            $isReturn    = strtotime($findGdlExpirationMap[$staffId]) >= strtotime($item['start']) && strtotime($findGdlExpirationMap[$staffId]) <= strtotime($item['end'])
                && $addDaysTime >= strtotime($item['start']) && $addDaysTime <= strtotime($item['end'])
                && $addDaysTime >= $monthStartTime && $addDaysTime <= $monthEndTime;
            if (!$isReturn) {
                unset($returnData[$staffId]);
                continue;
            }
            $returnData[$staffId]['gdl_start_day'] = $findGdlExpirationMap[$staffId];
        }
        return $returnData;
    }

    /**
     * 上传
     * @param $month
     * @param $tmpFilePath
     * @param $userId
     * @return array
     */
    public function upload($month, $tmpFilePath, $userId): array
    {
        // 读取Excel文件
        $excel = new \Vtiful\Kernel\Excel(['path' => dirname($tmpFilePath)]);
        $excel->openFile(basename($tmpFilePath))->openSheet();
        $sheetData = $excel->getSheetData();
        $this->logger->info($sheetData);
        //移除文件备注
        array_shift($sheetData);
        // 移除表头
        $header = array_shift($sheetData);
        $header = array_merge($header, ['Result', 'Remark']);

        // 提取工号
        $staffIds = array_column($sheetData, 0);
        $staffIds = array_values(array_filter($staffIds));

        // 检查文件中工号是否重复
        $duplicateStaffIds = [];
        $staffIdCount      = array_count_values($staffIds);
        foreach ($staffIdCount as $staffId => $count) {
            if ($count > 1) {
                $duplicateStaffIds[] = $staffId;
            }
        }

        $staffIds     = array_values(array_unique($staffIds));
        $staffFindIds = [];
        if ($staffIds) {
            $staffFind    = HrStaffInfoModel::find([
                    'columns'    => 'staff_info_id',
                    'conditions' => "formal = 1 and is_sub_staff = 0  and hire_type not in (13,14)
                 and staff_info_id  in ({ids:array})",
                    'bind'       => ['ids' => $staffIds],
                ]
            )->toArray();
            $staffFindIds = array_flip(array_column($staffFind, 'staff_info_id'));
        }
        // 删除临时文件
        unlink($tmpFilePath);
        $fileName = uniqid(date('Ym', strtotime($month)) . '_GDL_Expiration upload result') . date('YmdHis') . '.xlsx';
        // 查询已存在的记录
        $existingRecords = [];
        if ($staffIds) {
            $existingRecords = GdlExpirationModel::find([
                'columns'    => 'staff_info_id,gdl_start_day',
                'conditions' => 'staff_info_id IN ({ids:array})',
                'bind'       => ['ids' => $staffIds],
            ])->toArray();
        }
        $existingRecordsMap = array_column($existingRecords, 'gdl_start_day', 'staff_info_id');
        $existingStaffIds   = array_column($existingRecords, 'staff_info_id');

        // 处理数据
        $returnData   = $insertData = [];
        $successCount = $errorCount = 0;

        foreach ($sheetData as $item) {
            if (empty(array_filter($item))) {
                continue;
            }

            $errorMessage = [];
            [$staffId, $gdlStartDay] = $item;

            // 验证工号
            if (empty($staffId) || !is_numeric($staffId) || !isset($staffFindIds[$staffId])) {
                $errorMessage[] = 'Staff id error';
            }
            //工号重复校验
            if (empty($errorMessage) && in_array($staffId, $duplicateStaffIds)) {
                $errorMessage[] = 'Staff id is repeat';
            }
            if (is_numeric($gdlStartDay)) {
                $gdlStartDay = \PHPExcel_Shared_Date::ExcelToPHP($gdlStartDay);
                $gdlStartDay = date('Y-m-d', $gdlStartDay);
            }
            // 验证日期格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $gdlStartDay)) {
                $errorMessage[] = 'GDL start day error';
            }

            // 检查是否已存在记录
            if (in_array($staffId, $existingStaffIds)) {
                $errorMessage[] = 'Staff id has date for ' . $existingRecordsMap[$staffId];
            }

            if ($errorMessage) {
                $returnData[] = [
                    $staffId,
                    $gdlStartDay,
                    'fail',
                    implode(',', $errorMessage),
                ];
                ++$errorCount;
                continue;
            }

            // 保存数据
            $insertData[] = [
                'staff_info_id' => $staffId,
                'gdl_start_day' => $gdlStartDay,
                'operate_id'    => $userId,
            ];
            $returnData[] = [
                $staffId,
                $gdlStartDay,
                'success',
                '',
            ];
        }
        if ($insertData) {
            try {
                $gdlExpiration = new GdlExpirationModel();
                $gdlExpiration->batch_insert($insertData);
                $successCount = count($insertData);
            } catch (\Exception $e) {
                $this->logger->error($e->getMessage());
            }
        }

        return [
            'success_count' => $successCount,
            'fail_count'    => $errorCount,
            'error_url'     => $this->makeGdlExcel($header, $returnData, $fileName),
        ];
    }

    /**
     * 生成Excel文件
     * @param $header
     * @param $data
     * @param $fileName
     * @return string
     */
    private function makeGdlExcel($header, $data, $fileName): string
    {
        try {
            $exportRes = $this->exportExcel($header, $data, $fileName);
            $url       = '';
            if (isset($exportRes['code']) && $exportRes['code'] == 1) {
                $filePath = $exportRes['data'];
                $flashOss = new FlashOss();
                $object   = 'backyard/payroll/' . date('Ymd') . '/' . $fileName;
                $flashOss->uploadFile($object, $filePath);
                $url = $flashOss->signUrl($object, 20 * 86400);
            }
            return $url;
        } catch (\Exception $e) {
            $this->logger->error($e);
            return '';
        }
    }
}