<?php

namespace App\Modules\My\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Services\AttendanceRetryService;

class AttendanceRetryController extends BaseController
{
	
	
	/**
	 * @description:获取相关静态资源列表
	 *
	 * @param null
	 * @Token
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/13 20:41
	 */
	public function getStaticListAction()
	{
        $add_hour = $this->config->application->add_hour;
        $data = [
            'month'                      => gmdate("d", time() + $add_hour * 3600) > 15 ?  gmdate("Y-m", time() + $add_hour * 3600) : gmdate('Y-m', strtotime('last month') + $add_hour * 3600),//月份
            'data_type_calculate_attend' => [
                 ['key'=>0,'title'=>'Salary Attendance Strict'],
                 ['key'=>1,'title'=>'Salary Attendance Kind'],
                 ['key'=>2,'title'=>'Incentive Attendance'],
                 ['key'=>3,'title'=>'Salary Intermediate Table Data'],
            ],
            'data_type_all'              => [
                 ['key'=>0,'title'=>"Salary Attendance Strict"],
                 ['key'=>1,'title'=>"Incentive Attendance"],
                 ['key'=>2,'title'=>"PD Horizontal"],
                 ['key'=>3,'title'=>"PD Detail"],
                 ['key'=>4,'title'=>"Hub Full Attendance"],
                 ['key'=>5,'title'=>"Salary Attendance Kind"],
                 ['key'=>6,'title'=>"Leave Type Sum"],
                 ['key'=>7,'title'=>"Incentive Hold List"],
                 ['key'=>8,'title'=>"salary Hold List"],
            ],
            'date_type_special'          => [
                 ['key'=>0,'title'=>"Salary Attendance Strict"],
                 ['key'=>1,'title'=>"Incentive Attendance"],
                 ['key'=>2,'title'=>"PD Horizontal"],
                 ['key'=>3,'title'=>"PD Detail"],
                 ['key'=>4,'title'=>"Hub Full Attendance"],
                 ['key'=>5,'title'=>"Salary Attendance Kind"],
            ],

        ];
        return $this->returnJson(ErrCode::SUCCESS, '', $data);
	}
	
	
	/**
	 * @description:考勤重算 Re Calculate Attend
	 *
	 * @param string month   月份
	 * @param string re_cal_type  类型
	 * @param string staff_id    工号 ,号分割
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/10/14 14:13
	 * @Token
	 * @Permission(action='admin.attendance_retry.re_cal_attend_ajax')
     * @throws ValidationException
	 */
	
	//考勤重算
	public function re_cal_attend_ajaxAction()
    {
        $params['month'] = $this->request->get('month', 'trim', date('Y-m'));
        $params['re_cal_type'] = $this->request->get('re_cal_type', 'int');
        $params['staff_id'] = $this->request->get('staff_id', 'trim');
        $params['edit_staff_info_id'] = $this->user['id'];
        $params['staff_id'] = implode(',',clearSpecialChar($params['staff_id']));
        $validation = [
            'month' => 'Required',
            're_cal_type' => 'Required|IntIn:0,3|>>>: Data type error!',
            'staff_id' => 'Required|>>>:Staff Info Data Cannot be empty!',
        ];

        Validation::validate((array)$params, $validation);
        //本地化调整
        $salary_start_suffix = '24';
        $salary_end_suffix = '23';
        $salary_month = $params['month'] . '-'.$salary_start_suffix;
        $params['salary_start'] = date('Y-m-'.$salary_start_suffix, strtotime(date('Y-m-01',strtotime($salary_month)) ." -1 months")) ;
        $params['salary_end'] = date('Y-m-'.$salary_end_suffix, strtotime($salary_month));


        $result = (new AttendanceRetryService())->re_cal_attend_ajax($params);
        return $this->returnJson($result['code'], $result['message'], []);
	}
	
	
}
