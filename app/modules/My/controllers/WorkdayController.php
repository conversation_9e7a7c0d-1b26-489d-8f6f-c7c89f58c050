<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/8/4
 * Time: 2:43 PM
 */

namespace App\Modules\My\Controllers;

use App\Library\ErrCode;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffWorkDaysChangeModel;
use App\Services\StaffService;
use App\Library\Validation\Validation;
use App\Services\SysDepartmentService;
use App\Modules\My\library\Enums\enums;
use App\Library\Validation\ValidationException;
use App\Modules\My\Services\WorkdayService;
use App\Library\Exception\BusinessException;
use App\Services\DefaultRestDayService;
use App\Services\SysService;
use App\Controllers\WorkdayController as BaseController;

class WorkdayController extends BaseController
{


    public $paramIn;
    public $userinfo;
    public $staff_service;

    public function initialize()
    {
        parent::initialize();
        $this->paramIn       = $this->request->get();
        $this->paramIn       = filter_param($this->paramIn);
        $this->staff_service = new StaffService();
        $this->userinfo      = $this->staff_service->get_fbi_user_info($this->user['id']);

        //判断权限写死 参数
        $this->paramIn['sub'] = 1;//默认取直属下级
        $user_position        = $this->userinfo['position_category'] = empty($this->userinfo['position_category']) ? [] : explode(',',
            $this->userinfo['position_category']);
        if (!empty($this->userinfo['position_category'])) {
            //超管或人事  新增 系统管理员和人事专员角色
            if ($this->get_permission($user_position)) {
                $this->paramIn['is_sub'] = 0;
            } else {
                if (in_array(21, $user_position)) {//区域经理
                    $this->paramIn['is_manager'] = 'manager';
                } else {
                    if (in_array(3, $user_position) || in_array(18, $user_position)) {//网点
                        $this->paramIn['pre_store_id'] = $this->userinfo['organization_id'];
                    }
                }
            }
        }
    }


    /**
     * @Token
     * 单个人 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_workdaysAction()
    {
        $params['staff_info_id'] = $this->paramIn['staff_info_id'];
        $params['date']          = $this->paramIn['date'];
        $params['is_not_tips']   = $this->paramIn['is_not_tips'] ?? 0;
        $params['operator']      = $this->user['id'];
        $validation              = [
            'staff_info_id' => 'Required|int|>>>:staff_info_id error',
            'date'          => 'Required|Date|>>>:date error',
        ];
        Validation::validate($params, $validation);

        $data   = [
            'staff_info_ids' => [$params['staff_info_id']],
            'dates'          => [$params['date']],
            'is_not_tips'    => $params['is_not_tips'],
            'operator'       => $params['operator'],
            'src'            => 1,
        ];

        try {
            $workdayService = new WorkdayService();
            $suggestConfirm = $this->paramIn['is_submit'] ?? 0;//轮休配置验证建议班次 二次确认参数
            $workdayService->setSuggestConfirm($suggestConfirm);
            $result = $workdayService->fire($data);
            return $this->returnJson($result['code'] ?? ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data'] ?? [0]);
        } catch (BusinessException $b) {
            //排班建议 二次确认弹窗
            if ($b->getCode() == 10086) {
                return $this->returnJson(1, '', ['code' => -1,'message' => $b->getMessage(),'data' => null]);//小于6天
            }
            throw $b;
        }


    }


    /**
     * @Token
     * 批量 设置轮休
     * @throws ValidationException
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function add_batch_workdaysAction()
    {
        $params['staff_info_id'] = $this->paramIn['staff_info_id'];
        $params['date']          = $this->paramIn['date'];
        $params['is_not_tips']   = $this->paramIn['is_not_tips'] ?? 0;
        $params['operator']      = $this->user['id'];
        $validation              = [
            'staff_info_id' => 'Required|Arr|>>>:staff_info_id error',
            'date'          => 'Required|Arr|>>>:date error',
        ];
        Validation::validate($params, $validation);
        $data   = [
            'staff_info_ids' => $params['staff_info_id'],
            'dates'          => $params['date'],
            'is_not_tips'    => $params['is_not_tips'],
            'operator'       => $params['operator'],
            'src'            => 2,
        ];

        try{
            $workdayService = new WorkdayService();
            $suggestConfirm = $this->paramIn['is_submit'] ?? 0;//轮休配置验证建议班次 二次确认参数
            $workdayService->setSuggestConfirm($suggestConfirm);
            $result = $workdayService->fire($data);
            return $this->returnJson($result['code'] ?? ErrCode::SUCCESS, $result['message'] ?? 'success', $result['data'] ?? [0]);
        }catch (BusinessException $b) {
            //排班建议 二次确认弹窗
            if ($b->getCode() == 10086) {
                return $this->returnJson(1, '', ['code' => -1,'message' => $b->getMessage(),'data' => null]);//小于6天
            }
            throw $b;
        }

    }


    /**
     * @Token
     * @param $user_position
     * @return bool
     */
    protected function get_permission($user_position)
    {
        //超管 运营经理 人事经理 人事专员  系统管理员  有全部权限
        if (in_array(17, $user_position)
            || in_array(99, $user_position)
//            || in_array(8,$user_position)//运营经理 去掉全部权限 根据所在部门 获取部门以及子部门的员工
            || in_array(14, $user_position)
            || in_array(16, $user_position)
            || in_array(41, $user_position)
        ) {
            return true;
        }
        return false;
    }


    /**
     * @Token
     * @Permission(action='work_day_view')
     */
    public function staff_workdays_listAction()
    {
        $param['store_id']      = empty($this->paramIn['store_id']) ? [] : $this->paramIn['store_id'];
        $param['state']      = empty($this->paramIn['state']) ? [] : $this->paramIn['state'];
        $param['area']          = empty($this->paramIn['area']) ? '' : $this->paramIn['area'];
        $param['job_title']     = empty($this->paramIn['job_title']) ? '' : $this->paramIn['job_title'];
        $param['staff_info_id'] = empty($this->paramIn['staff_info_id']) ? '' : $this->paramIn['staff_info_id'];
        //部门页面搜索
        $param['search_department'] = empty($this->paramIn['search_department']) ? '' : $this->paramIn['search_department'];
//            $param['sub'] = $this->paramIn['sub'];
        //分页
        $param['start']  = empty($this->paramIn['start']) ? 0 : $this->paramIn['start'];
        $param['length'] = empty($this->paramIn['length']) ? 30 : $this->paramIn['length'];

        $work_type                 = empty($this->paramIn['work_type']) ? '0_0' : $this->paramIn['work_type'];
        $param['week_working_day'] = explode('_', $work_type)[0];
        $param['rest_type']        = explode('_', $work_type)[1];
        $param['hire_type']        = $this->paramIn['hire_type']??[];

        //员工信息
        $staff_bll         = new WorkdayService();
        $staff_id          = $this->userinfo['id'];
        $param['operator'] = $staff_id;

        //获符合筛选条件和登录用户可视范围数据权限的员工
        $result = $staff_bll->search_staff_workdays($param, $this->userinfo['id']);
        if (!empty($result['data'])) {
            $param['month'] = $this->paramIn['month'] ?? date('Y-m');
            $result['data'] = $staff_bll->formatList($param, $result['data']);
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $result);
    }

    /**
     * @Token
     */
    public function area_listAction()
    {
        $parcel_model = enums::$store_sorting_no;
        $return       = [];
        foreach ($parcel_model as $k => $v) {
            $row['code']  = $k;
            $row['value'] = $v;
            $return[]     = $row;
        }
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', $return);
    }

    /**
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     * @Permission(action='work_day_setting')
     */
    public function cancel_shiftAction()
    {
        $staff_id = $this->paramIn['staff_info_id'];
        $date     = $this->paramIn['date'];
        $operator = $this->paramIn['operator'];

        if (empty($staff_id) || empty($date)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'wrong param', []);
        }

        //能不能修改自己的轮休
        $workday_bll = new WorkdayService();
        $workday_bll->checkSelfEdit($operator, $staff_id);
        $flag = $workday_bll->checkTruckUpdatePermission([$staff_id], $operator);
        if(!$flag){
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR,$this->t->_('workday_truck_permission'), []);
        }
        //验证员工
        $staffInfo = (new StaffService())->getHrStaffInfo($staff_id);
        if (empty($staffInfo)) {
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_LEAVE_ERROR, $this->t->_('staff_id_not_found'), []);
        }
        //只能取消当天之后的
        $today = date('Y-m-d');
        $sysService = new SysService;
        $isWorkDayRootId =$sysService->setCurrentStaffId($this->user['id'])->isWorkDayRootId();
        $canModifyTodayRest = $sysService->setCurrentStaffId($this->user['id'])->canModifyTodayRest()
            && in_array($staffInfo['week_working_day'],HrStaffInfoModel::$weekWorkingDayTogether) &&  $staffInfo['rest_type'] == HrStaffInfoModel::REST_TYPE_1;
        if (!$isWorkDayRootId) {
            if ($canModifyTodayRest && $date < $today || !$canModifyTodayRest && $date <= $today) {
                return $this->returnJson(ErrCode::VALIDATE_ERROR, 'can not cancel,wrong date chosen', []);
            }
        }
        //当天有审批通过或者待审批的加班申请，请撤销加班申请后再调整休息日
        $check_overtime = $workday_bll->check_overtime($staff_id, $date);
        if (!empty($check_overtime)) {
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_OVERTIME_ERROR,
                $this->t->_('workdays_check_overtime_alert_v2',
                    ['staff_info_id' => $staff_id, 'name' => $staffInfo['name']]), []);
        }
        //当天有请假申请，限制不能取消休息日
        $check_leave_all = $workday_bll->check_leave_all($staff_id, $date);
        if (!empty($check_leave_all)) {
            return $this->returnJson(ErrCode::WORK_DAY_CHECK_LEAVE_ERROR, $this->t->_('workdays_check_leave_alert_v2',
                ['staff_info_id' => $staff_id, 'name' => $staffInfo['name']]), []);
        }

        $workday_bll->cancel($staff_id, $date, $operator);
        //日志
        $this->logger->info("staff_workdays_cancel {$staff_id} {$date} {$operator}");
        //发送消息 取消
        $workday_bll->staff_set_work_day_send_message($staff_id, $date, 1);//轮休发送消息
        return $this->returnJson(ErrCode::SUCCESS, 'SUCCESS', [0]);
    }



    //获取 是否是 部门负责人
    protected function is_manager($staff_id)
    {
        $model = new SysDepartmentService();
        $res   = $model->get_dep_manager($staff_id);
        if (empty($res)) {
            return false;
        }

        $dep_ids = array_column($res, 'id');//负责的所有部门
        return $dep_ids;
    }



    //一级部门列表 下拉菜单接口
    public function get_department_listAction()
    {
        $p_id = empty($this->paramIn['p_id']) ? 0 : $this->paramIn['p_id'];
        $p_id = intval($p_id);
        $bll  = new SysDepartmentService();
        $list = $bll->department_list($p_id);
        return $this->returnJson(ErrCode::SUCCESS, 'success', $list);
    }

    /**
     * 五天班&六天班轮休 设置默认休息日
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @Permission(action='work_day_setting')
     */
    public function editDefaultRestDayDateAction(){
        $staffId = $this->paramIn['staff_info_id'];
        $defaultRestDayDate = $this->paramIn['default_rest_day_date'];
        if (!is_array($defaultRestDayDate)) {
            return $this->returnJson(ErrCode::VALIDATE_ERROR, 'param error');
        }
        (new DefaultRestDayService())->editDefaultRestDayDate($staffId,$defaultRestDayDate, $this->user['id']);
        return $this->returnJson(ErrCode::SUCCESS, 'success');
    }

}