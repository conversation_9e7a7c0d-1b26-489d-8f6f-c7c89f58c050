<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Payroll</title>
    <style>
      body{
        margin: 0;
      }
      .box {
        /* width: 210mm;
        padding: 0mm 5mm;
        box-sizing: border-box; */
        font-size: 3mm;
        /* margin: 0 auto; */
        font-family: system-ui;
        /* background: #000; */
      }
      .page {
        width: 210mm;
        padding: 0mm 5mm;
        margin: 0 auto;
                height: 279.4mm;
                overflow: hidden;
              box-sizing: border-box;
              /* border: 1px solid #000; */
            }
      p {
        margin: 0;
      }
      .title {
        border: 1px solid #000;
        display: flex;
        flex-direction: row;
      }
      .select-box {
        width: 40mm;
        display: inline;
        margin: 5mm;
      }
      .select-box:last-child {
        width: 100mm;
        /* margin-left: 20mm; */
      }
      .span-no {
        border-bottom: 1px solid #000;
        padding: 0 2mm;
        min-width: 20mm;
        display: inline-block;
      }
      .select-box input[type="checkbox"] {
        margin-right: 5px;
        cursor: pointer;
        font-size: 14px;
        width: 20px;
        height: 12px;
        position: relative;
      }

      .select-box input[type="checkbox"]:after {
        position: absolute;
        width: 15px;
        height: 20px;
        text-align: center;
        top: 0;
        content: " ";
        background-color: #fff;
        color: #000;
        display: inline-block;
        visibility: visible;
        padding: 0px 2px;
        border: 1px solid #000;
      }

      .select-box input[type="checkbox"]:checked:after {
        content: "x";
        font-size: 18px;
      }
      .title-img,
      .title-page {
        width: 40mm;
        height: 100%;
        box-sizing: border-box;
        padding: 2mm;
      }
      .title-page p {
        text-align: center;
      }
      .title-img .img {
        height: 40mm;
        width: 100%;
        margin: 2mm 0;
        text-align: center;
      }
      .img img {
        height: auto;
        width: 25mm;
      }
      .title-img p {
        text-align: right;
      }
      .title-main-table-max {
        font-size: 4.5mm;
        font-weight: 900;
        text-align: center;
      }
      .title-main-table-min {
        text-align: center;
        margin-bottom: 5mm;
      }
      .title-main-table table {
        width: 100%;
        border-collapse: collapse;
        font-size: 3mm;
      }
      .title-main-table table td,
      .title-main-table table th {
        border: 1px solid #000;
        /* display: inline-block; */
        box-sizing: border-box;
      }
      .page-1 {
        font-weight: 900;
        font-size: 6mm;
        margin-top: 7mm;
      }
      .page-2 {
        font-weight: 900;
        font-size: 10mm;
        margin: 2mm 0;
      }
      .page-3 {
        border: 1px solid #000;
        height: 12mm;
        width: 12mm;
        border-radius: 50%;
        margin: 5mm auto;
      }
      .info {
        width: 100%;
        margin: 2mm 0;
      }
      .info table {
        width: 100%;
        border-collapse: collapse;
        font-size: 3mm;
        table-layout: fixed;
      }
      .info table td,
      .info table th {
        border: 1px solid #000;
        /* display: inline-block; */
        box-sizing: border-box;
        height: 5mm;
      }
      .tr-1 td {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .sign {
        width: 100%;
        display: flex;
        flex-direction: row;
      }
      .sign-1 {
        width: 80mm;
      }
      .sign-1 table {
        width: 100%;
        font-size: 3mm;
      }
      .td-b {
        border-bottom: 1px solid #000;
      }
      .sign-2 {
        width: 40mm;
        height: auto;
        margin: 0 2mm;
        box-sizing: border-box;
        padding: 1mm;
        border: #000 1px solid;
        display: flex;
        align-items: flex-end;
        justify-content: center;
      }
      .sign-3 {
        width: 90mm;
      }
      ol {
        margin: 0;
        padding-left: 4mm;
      }
      .fu{
position: absolute;
width: 110mm !important;
left: -25mm;
      }
      .fu tr,
      .fu td {
        border: 0 !important;
      }
    </style>
  </head>


  <#list list as list_item><body>
    <div class="box">
    <div class='page'>
      <div class="title">
        <div class="title-img">
          <div class="img">
            <img
              src="data:image/jpeg;base64,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"
              alt=""
            />
          </div>

        </div>
        <div class="title-main-table">
          <div class="title-main-table-max">KUMPULAN WANG SIMPANAN PEKERJA</div>
          <div class="title-main-table-min">
            PERATURAN-PERATURAN DAN KAEDAH-KAEDAH KWSP 1991 KAEDAH 11(1)
          </div>
          <table border="0" style="margin: 0 auto">
            <tr valign="top">
              <th width="40%" align="center">No Rujukan Majikan</th>
              <th width="20%" align="center">Bulan Caruman</th>
              <th width="20%" align="center">Amaun Caruman (RM)</th>
              <th width="20%" align="center">No Rujukan Borang A</th>
            </tr>
            <tr valign="top">
              <td width="40%" align="center">${epf_num}</td><!-- {{雇主注冊號}} -->
              <td width="20%" align="center">${month}</td>
              <td width="20%" align="center">${total_epf}</td><!--{{貢獻數額}} -->
              <td width="20%" align="center">${table_number}</td> <!-- {{表格號}} -->
            </tr>
            <tr valign="top" height="80px">
              <td colspan="4">
                <p style="margin: 1mm">
                  Jumlah caruman untuk bulan di atas hendaklah dibayar kepada
                  KWSP/Agen Kutipan KWSP sebelum/pada 15hb setiap bulan
                </p>
                <div class="select-box">
                  <!-- checked 控制打钩 -->
                  <input
                    type="checkbox"
                    name="method"
                    value="plus"
                  />Wang Tunai
                </div>
                <div class="select-box">
                  <input type="checkbox" name="method" value="plus" checked />
                  <div style="display: inline-table">
                    <p>Cek/Kiriman Wang/Wang Pos</p>
                    <p>
                      /Draf Bank*No /EFT /TT.:<span class="span-no"
                        ></span
                      ><! -- {{⽀票 / 匯票 / 郵政錢}} -->
                    </p>
                  </div>
                </div>
              </td>
            </tr>
            <tr valign="top" height="130px" >
              <td colspan="4" align="right" style="padding: 2mm;position: relative;">
                <div>
                  <table class="fu">
                    <tr valign="top">
                      <td width="25%" >Nama Majikan:</td><td width="75%">${employer_name} </td><!--{{雇主名字}} -->
                    
                    </tr>
                    <tr valign="top">
                      <td>Alamat :</td>
                      <td >${employer_addr}</td><!-- {{地址}} -->
                    </tr>
                  </table>
                </div>
                <div style="width: 50mm; text-align: left">

                  <p>Tarikh DiCetak :${print_date} </p> <!-- {{打印⽇期}} -->
                  <p>
                    Bil Pekerja &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;:
                    <!--{{員⼯⼈數}} -->${total_epf_num}
                  </p>
                </div>
              </td>
            </tr>
          </table>
        </div>
        <div class="title-page">
          <p style="text-align: right"><strong>KWSP 6</strong></p>
          <p class="page-1">Borang</p>
          <p class="page-2">A</p>
          <p style="margin-top: 5mm">Mukasurat:</p><!--{{⾴數}} -->
          <div class="page-3"></div>
          <p>Cop Agen Kutipan</p>
        </div>
      </div>
      <div class="info">
        <table border="0" style="margin: 0 auto">
          <tr valign="center">
            <th width="4%" rowspan="2" align="center">BIL</th>
            <th width="12%" rowspan="2" align="center">NO AHLI</th>
            <th width="2%" rowspan="2" align="center">N K</th>
            <th width="14%" rowspan="2" align="center">NO KAD PENGENALAN</th>
            <th width="38%" rowspan="2" align="center">
              <p>NAMA PEKERJA / AHLI</p>
              <p style="font-weight: 400">
                (Seperti yang terdapat di dalam Kad Pengenalan)
              </p>
            </th>
            <th width="14%" rowspan="2" align="center">UPAH (RM)</th>
            <th width="24%" align="center" colspan="2">CARUMAN (RM)</th>
          </tr>

          <tr valign="center">
            <th width="12%" align="center">MAJIKAN</th>
            <th width="12%" align="center">PEKERJA</th>
          </tr>
          <tr valign="center">
            <th width="80%" align="right" colspan="6">
              Jumlah yang dibawa dari mukasurat terdahulu (jika ada)
            </th>
            <th width="12%" align="center">${list_item.pre_er_total}</th>
            <th width="12%" align="center">${list_item.pre_ee_total}</th>
          </tr>
          <#list list_item.arr as arr_item>
            <tr valign="center" class="tr-1">
                        <td width="4%" align="center">${arr_item.b}</td>
                        <td width="12%" align="left">${arr_item.e_n}</td>
                        <td width="2%" align="center">${arr_item.n_k}</td>
                        <td width="14%" align="left">${arr_item.i}</td>
                        <td width="28%" align="left">${arr_item.n}</td>
                        <td width="14%" align="right">${arr_item.t}</td>
                        <td width="12%" align="right">${arr_item.er}</td>
                        <td width="12%" align="right">${arr_item.ee}</td>
                      </tr>
          </#list>
          <tr valign="center">
            <th width="80%" align="right" colspan="6">
              Jumlah yang dibawa ke mukasurat seterusnya (jika ada)
            </th>
            <th width="12%" align="right">${list_item.current_er_total}</th>
            <th width="12%" align="right">${list_item.current_ee_total}</th>
          </tr>
          <tr>
            <th width="62%" align="right" colspan="5" style="border: 0"></th>
            <th width="14%" align="center">JUMLAH (RM)</th>
            <th width="24%" align="center" colspan="2">${total_epf}</th>
          </tr>
        </table>
      </div>
      <div class="sign">
        <div class="sign-1">
          <table border="0" style="margin: 0 auto">
            <tr valign="center">
              <th width="30%" align="left">Tandatangan Wakil Majikan</th>
              <td width="70%" align="left" class="td-b"><#if er_sign??>
                                                                            <img src="${er_sign}" alt="" style="height: 12mm;" />
                                                                        </#if></td><!-- {{雇主代表簽名}} -->
            </tr>
            <tr valign="center">
              <th width="30%" align="left">Nama</th>
              <td width="70%" align="left" class="td-b">${er_name}</td><!-- {{名字}} -->
            </tr>
            <tr valign="center">
              <th width="30%" align="left">No. Kad Pengenalan</th>
              <td width="70%" align="left" class="td-b">${er_identity}</td><!-- {{⾝份證號碼}}-->
            </tr>
            <tr valign="center">
              <th width="30%" align="left">Jawatan</th>
              <td width="70%" align="left" class="td-b">${er_job_title}</td><!--{{職位}} -->
            </tr>
            <tr valign="center">
              <th width="30%" align="left">No. Tel / Bimbit</th>
              <td width="70%" align="left" class="td-b">${er_mobile}</td><!-- {{聯絡號碼}} -->
            </tr>
            <tr valign="center">
              <th width="30%" align="left">E-Mel</th>
              <td width="70%" align="left" class="td-b">${er_email}</td><!-- {{郵件}} -->
            </tr>
            <tr valign="center">
              <th width="30%" align="left">Tarikh</th>
              <td width="70%" align="left" class="td-b">${print_date}</td><!--{{⽇期}} -->
            </tr>
          </table>
        </div>
        <div class="sign-2">
          <p>Cop Rasmi Majikan</p>
        </div>
        <div class="sign-3">
          <p><strong>CATATAN</strong></p>
          <ol>
            <li>Nombor Majikan mesti ditulis di belakang cek.</li>
            <li>Jumlah bayaran mesti sama dengan jumlah di Borang A.</li>
            <li>Potong maklumat ahli yang telah berhenti kerja.</li>
            <li>
              Jika ada butir-butir pekerja yang tidak disenaraikan, sila
              catatkan semua butirnya dan masukkan pekerja baru dalam ruangan
              kosong (jika ada).
            </li>
            <li>Ruang ketiga (NK) hanya diisi oleh KWSP sahaja.</li>
            <li>Bulan caruman bersamaan Bulan Upah + 1</li>
            <li>
              Upah termasuklah gaji pokok, komisyen, bonus, elaun dan bayaran
              yang dikenakan caruman KWSP.
            </li>
            <li>
              Sila rujuk panduan mengisi Borang A di buku Panduan Majikan.
            </li>
          </ol>
        </div>
      </div>
      <p style="margin: 5mm auto">
        <strong>PERINGATAN:</strong> Berdasarkan Akta KWSP 1991, kesilapan
        membekalkan maklumat ahli boleh menyebabkan tuan dikenakan caj atau
        tindakan undang-undang.
      </p>
    </div>
</div>    </#list></body>

</html>
