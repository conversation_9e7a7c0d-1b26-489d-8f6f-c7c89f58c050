<?php

namespace App\Modules\My\Tasks;

use App\Library\DateHelper;
use App\Models\backyard\HrStaffApplySupportStoreModel;
use App\Models\backyard\HrImportStaffSupportExcelModel;
use App\Models\backyard\HrImportStaffSupportDetailModel;
use App\Models\backyard\HrStaffApplySupportStoreSplitModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Modules\My\Services\StaffSupportStoreServer;
use App\Services\StaffContractService;
use App\Services\StaffNoticeService;
use StaffSupportStoreTask as BaseStaffSupportStoreTask;
use Exception;

class StaffSupportStoreTask extends BaseStaffSupportStoreTask
{
    /**
     * 定时任务 每天00:10 创建支援子账号
     */
    public function createSubStaffAction($params)
    {
        echo "begin:" . date("Y-m-d h:i:s") . PHP_EOL;
        $logger = $this->getDi()->get('logger');

        $today = date("Y-m-d");
        if (isset($params[0])) {
            $today = $params[0];
        }
        $list = HrStaffApplySupportStoreModel::find([
            'conditions' => 'status = 2 and sub_staff_info_id = 0 and employment_begin_date = :employment_begin_date: and support_status in (1,2)',
            'bind'       => [
                'employment_begin_date' => $today,
            ],
        ])->toArray();
        echo '当日申请支援总数量:' . count($list) . PHP_EOL;

        $create_sub_staff_result = [];
        if (empty($list)) {
            $this->logger->notice(['params' => $today, 'createSubStaff' => 'empty']);
            return true;
        }

        $ids = array_column($list, 'id');
        echo '当日申请支援id:' . json_encode($ids) . PHP_EOL;

        $server         = new StaffSupportStoreServer();
        $staff_info_ids = array_column($list, 'staff_info_id');
        $staff_list     = $server->getStaffInfoByIds($staff_info_ids);

        $staff_items    = HrStaffItemsModel::find([
            'conditions' => "staff_info_id in ({staff_ids:array}) and item = 'MANGER'",
            'bind'       => [
                'staff_ids' => $staff_info_ids,
            ],
        ])->toArray();
        $staff_manager  = !empty($staff_items) ? array_column($staff_items, 'value', 'staff_info_id') : [];
        $db             = $this->getDI()->get("db_backyard");
        $sync_ms_params = [];
        $job_title_config = (new StaffSupportStoreServer())->getSupportStaffJobTitleConfig();//setting_env 配置
        $support_job_title_role_config = $job_title_config['support_job_title_role_config'];//支援职位对应角色id

        foreach ($list as $key => $value) {
            //如果是 揽派分离 不生成子账号
            $staff_info_id         = $value['staff_info_id'];
            $staff_info            = $staff_list[$staff_info_id];
            $staff_info['manager'] = $staff_manager[$staff_info_id] ?? '';
            $sub_staff_info_id = 0;
            if(empty($value['is_separate'])){
                $sub_staff_info_id     = $server->createSubStaff($staff_info,
                    [
                        'job_title'             => $value['job_title_id'],
                        'store_id'              => $value['store_id'],
                        'employment_begin_date' => $value['employment_begin_date'],
                        'position_category'     => $support_job_title_role_config[$value['job_title_id']] ?? [],
                    ]);

                if (empty($sub_staff_info_id)) {
                    $this->logger->error(['params' => $value, 'createSubStaff' => 'error']);
                    continue;
                }
                $sync_ms_params = [
                    [
                        'id'       => $value['id'],
                        'staff_id' => $sub_staff_info_id,
                        'master_staff' => $staff_info_id,
                        'begin_at' => strtotime($value['employment_begin_date']),
                        'end_at'   => strtotime($value['employment_end_date']) + 86399,
                    ],
                ];
                //同步ms工号信息
                $sync_ms = $server->syncMsSupportApply($sync_ms_params, 'addStaffSupportInfo');
                if (!$sync_ms) {
                    $this->logger->error(['params' => $sync_ms_params, 'syncMsSupportApply' => 'error']);
                    continue;
                }
                $result = $db->updateAsDict(
                    'hr_staff_apply_support_store',
                    [
                        'sub_staff_info_id' => $sub_staff_info_id,
                    ],
                    'id = ' . $value['id']
                );
            }
            //子账号发送合同消息
            $staffContractService = new StaffContractService();
            //方法里面 如果子账号是0 不发送
            $staffContractService->sendContractMsgToSubStaff($staff_info_id, $sub_staff_info_id);
            //给子账号 发送简历完善通知 消息
            $sendResult = (new StaffNoticeService())->sendResumeMessage($staff_info_id, $sub_staff_info_id, $today);
            $this->getDI()->get('logger')->write_log([
                'function' => 'StaffSupportStoreServer-createSubStaffAction-sendResumeMessage',
                'params' => [$staff_info_id, $sub_staff_info_id, $today],
                'result' => $sendResult
            ], 'info');

            $create_sub_staff_result[] = [
                'staff_info_id'     => $value['staff_info_id'],
                'sub_staff_info_id' => $sub_staff_info_id,
                'result'            => $result,
            ];
           // echo '主账号:' . $value['staff_info_id'] . '-----子账号:' . $sub_staff_info_id . '-----结果:' . $result . PHP_EOL;

            //生成班次信息
            $createShiftInfo = [
                'staff_info_id'     => $staff_info_id,
                'sub_staff_info_id' => $sub_staff_info_id,
                'shift_start_date'  => $value['employment_begin_date'],
                'shift_end_date'    => $value['employment_end_date'],
                'shift_id'          => $value['shift_id'],
                'shift_extend_id'   => $value['shift_extend_id'],
            ];
            $server->createSupportStaffShift($createShiftInfo);
        }

        $logger->notice([
            'function'                => 'createSubStaffTask',
            '主账号'                  => $staff_info_ids,
            '支援工单id'              => $ids,
            'create_sub_staff_result' => $create_sub_staff_result,
        ]);

        //发送支援员工信息 原网点主管和仓管职位员工发送支援信息/支援网点网点主管和仓管员工发送支援信息
        $this->sendStaffSupportMessageAction();

        return true;
    }

    /**
     * 导入支援定时任务 每10秒执行一次，每次只取一条
     */
    public function importStaffSupportAction()
    {
        echo "begin:" . date("Y-m-d h:i:s") . PHP_EOL;
        $logger = $this->getDi()->get('logger');
        $limit = 1;
        $list  = HrImportStaffSupportExcelModel::find([
            'conditions' => 'type = :type: AND is_del = 0 AND status = 1 ',
            'limit'      => $limit,
            'order'      => 'id asc',
            'bind'       => [
                'type' => HrImportStaffSupportExcelModel::TYPE_SUPPORT_STAFF,
            ],
        ])->toArray();
        echo '需要执行的任务:' . json_encode($list) . PHP_EOL;
        if (count($list) > 0) {
            $server = new StaffSupportStoreServer();
            foreach ($list as $key => $value) {
                $data_list = HrImportStaffSupportDetailModel::find([
                    'conditions' => 'import_excel_id = :excel_id: and is_del = 0',
                    'bind'       => [
                        'excel_id' => $value['id'],
                    ],
                ])->toArray();

                $excel_data = [];
                foreach ($data_list as $k => $v) {
                    $excel_data[] = json_decode($v['row_content'], true);;
                }

                $params = [
                    'excel_data'      => $excel_data,
                    'import_excel_id' => $value['id'],
                    'operator_id'     => $value['operator_id'],
                ];
                $result = $server->ImportStaffSupport($params);
                echo '执行结果:' . json_encode($result) . PHP_EOL;
            }
        } else {
            echo "没有要执行的任务" . PHP_EOL;
        }
        echo "end:" . date("Y-m-d h:i:s") . PHP_EOL;
    }

    //支援拆分表刷数据 跑一次
    public function initSupportSplitAction()
    {
        $today = date('Y-m-d');
        //当前 有效期内的 生效中的 支援
        $data = HrStaffApplySupportStoreModel::find([
            'columns'    => 'id,staff_info_id,sub_staff_info_id, employment_begin_date,employment_end_date,is_separate',
            'conditions' => 'support_status = 2 and support_status = 2 and employment_end_date >= :today:',
            'bind' => ['today' => $today],
        ])->toArray();

        if (empty($data)) {
            echo '没数据';
            return;
        }
        $insert = [];
        foreach ($data as $da) {
            $startDate = $da['employment_begin_date'];
            $endDate   = $da['employment_end_date'];
            $dateList  = DateHelper::DateRange(strtotime($startDate), strtotime($endDate));
            foreach ($dateList as $date) {
                $row['staff_info_id']   = $da['staff_info_id'];
                $row['origin_id']       = $da['id'];
                $row['employment_date'] = $date;
                $row['is_separate']     = $da['is_separate'];
                $insert[]               = $row;
            }
        }
        $model = new HrStaffApplySupportStoreSplitModel();
        $model->batch_insert($insert);

        echo '跑完了';
    }


}